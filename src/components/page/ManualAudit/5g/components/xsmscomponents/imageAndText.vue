<template>
  <div>
    <el-form ref="form" :model="ssoContentFrom" disabled label-width="100px">
      <div>
        <div class="drawer_img">
          <span style="margin-left: 10px">图片设置</span>
        </div>
        <div class="drawer_img_content">
          <el-form-item label="图片" prop="imgcontents.sourceId">
            <div
              v-if="ssoContentFrom.imgcontents.sourceId == ''"
              class="drawe_item_img"
            >
              <img
                src="../../../../../../assets/images/ssobg.png"
                alt=""
                class="image_d"
              />
              <div class="add_img">
                <el-icon><el-icon-plus /></el-icon>
              </div>
            </div>
            <div v-else class="drawe_item_img">
              <img
                v-if="ssoContentFrom.imgcontents.srcType == '1'"
                :src="ssoContentFrom.imgcontents.url"
                class="image_d"
              />
            </div>
            <div style="color: #999">图片比例16:9，建议1248x702px</div>
          </el-form-item>
          <el-form-item label="事件类型" prop="imgcontents.actionType">
            <!-- <div v-if="ssoContentFrom.imgcontents.actionType =='OPEN_UR'">打开链接</div>
                <div v-else-if="ssoContentFrom.imgcontents.actionType =='DIAL_PHONE'">拨打电话</div>
                <div v-else-if="ssoContentFrom.imgcontents.actionType =='OPEN_BROWSER'">打开浏览器</div> -->
            <el-select
              style="width: 250px"
              v-model="ssoContentFrom.imgcontents.actionType"
              placeholder="请选择"
            >
              <el-option label="打开链接" value="OPEN_URL"> </el-option>
              <el-option label="拨打电话" value="DIAL_PHONE"> </el-option>
              <el-option label="打开浏览器" value="OPEN_BROWSER"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="ssoContentFrom.imgcontents.actionType == 'OPEN_URL'"
            label="打开链接"
            prop="imgcontents.action.target"
          >
            <a
              :href="ssoContentFrom.imgcontents.action.target"
              target="_blank"
              rel="noopener noreferrer"
              >{{ ssoContentFrom.imgcontents.action.target }}</a
            >
            <!-- <el-input
                  class="textarea_text"
                  type="textarea"
                  placeholder="链接格式为https://开头"
                  v-model="ssoContentFrom.imgcontents.action.target"
                ></el-input> -->
          </el-form-item>
          <el-form-item
            v-if="ssoContentFrom.imgcontents.actionType == 'OPEN_BROWSER'"
            label="打开浏览器"
            prop="imgcontents.action.target"
          >
            <a
              :href="ssoContentFrom.imgcontents.action.target"
              target="_blank"
              rel="noopener noreferrer"
              >{{ ssoContentFrom.imgcontents.action.target }}</a
            >
            <!-- <el-input
                  class="textarea_text"
                  type="textarea"
                  placeholder="链接格式为https://开头"
                  v-model="ssoContentFrom.imgcontents.action.target"
                ></el-input> -->
          </el-form-item>
          <el-form-item
            v-if="
              ssoContentFrom.imgcontents.actionType == 'OPEN_URL' ||
              ssoContentFrom.imgcontents.actionType == 'OPEN_BROWSER'
            "
            label="商户名称"
            prop="imgcontents.action.merchantName"
          >
            <div>{{ ssoContentFrom.imgcontents.action.merchantName }}</div>
            <!-- <el-input
                  style="width: 250px"
                  v-model="ssoContentFrom.imgcontents.action.merchantName"
                ></el-input> -->
          </el-form-item>
          <el-form-item
            v-if="ssoContentFrom.imgcontents.actionType == 'DIAL_PHONE'"
            label="拨打电话"
            prop="imgcontents.action.target"
          >
            <div>{{ ssoContentFrom.imgcontents.action.target }}</div>
            <!-- <el-input
                  class="textarea_text"
                  type="textarea"
                  v-model="ssoContentFrom.imgcontents.action.target"
                ></el-input> -->
          </el-form-item>
        </div>
      </div>

      <div>
        <div class="drawer_img">
          <span style="margin-left: 10px">内容设置</span>
        </div>
        <div class="drawer_img_content">
          <el-form-item label="标题名称" prop="titlecontents.content">
            <div>{{ ssoContentFrom.titlecontents.content }}</div>
            <!-- <el-input
                  style="width: 250px"
                  maxlength="17"
                  show-word-limit
                  v-model="ssoContentFrom.titlecontents.content"
                ></el-input> -->
          </el-form-item>
          <el-form-item label="摘要内容:" prop="textcontents.content">
            <!-- <div>{{ssoContentFrom.textcontents.content}}</div> -->
            <el-input
              class="textarea_text"
              type="textarea"
              maxlength="69"
              show-word-limit
              v-model="ssoContentFrom.textcontents.content"
            ></el-input>
            <span style="margin-left: -50px; font-size: 12px"
              >共{{ ssoContentFrom.textcontents.content.length }}个字</span
            >
          </el-form-item>
        </div>
      </div>
      <div>
        <div class="drawer_img">
          <span style="margin-left: 10px">按钮设置</span>
        </div>
        <div class="drawer_img_content">
          <el-form-item label="按钮名称" prop="btncontents.content">
            <div>{{ ssoContentFrom.btncontents.content }}</div>
            <!-- <el-input
                  style="width: 250px"
                  maxlength="17"
                  show-word-limit
                  v-model="ssoContentFrom.btncontents.content"
                ></el-input> -->
          </el-form-item>
          <el-form-item label="事件类型" prop="btncontents.actionType">
            <el-select
              style="width: 250px"
              v-model="ssoContentFrom.btncontents.actionType"
              placeholder="请选择"
            >
              <el-option label="打开链接" value="OPEN_URL"> </el-option>
              <el-option label="拨打电话" value="DIAL_PHONE"> </el-option>
              <el-option label="打开浏览器" value="OPEN_BROWSER"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="ssoContentFrom.btncontents.actionType == 'OPEN_URL'"
            label="打开链接"
            prop="btncontents.action.target"
          >
            <a
              :href="ssoContentFrom.btncontents.action.target"
              target="_blank"
              rel="noopener noreferrer"
              >{{ ssoContentFrom.btncontents.action.target }}</a
            >
            <!-- <el-input
                  class="textarea_text"
                  type="textarea"
                  placeholder="链接格式为https://开头"
                  v-model="ssoContentFrom.btncontents.action.target"
                ></el-input> -->
          </el-form-item>
          <el-form-item
            v-if="ssoContentFrom.btncontents.actionType == 'OPEN_BROWSER'"
            label="打开浏览器"
            placeholder="链接格式为https://开头"
            prop="btncontents.action.target"
          >
            <a
              :href="ssoContentFrom.btncontents.action.target"
              target="_blank"
              rel="noopener noreferrer"
              >{{ ssoContentFrom.btncontents.action.target }}</a
            >
            <!-- <el-input
                  class="textarea_text"
                  type="textarea"
                  v-model="ssoContentFrom.btncontents.action.target"
                ></el-input> -->
          </el-form-item>
          <el-form-item
            v-if="
              ssoContentFrom.btncontents.actionType == 'OPEN_URL' ||
              ssoContentFrom.btncontents.actionType == 'OPEN_BROWSER'
            "
            label="商户名称"
            prop="btncontents.action.merchantName"
          >
            <div>{{ ssoContentFrom.btncontents.action.merchantName }}</div>
            <!-- <el-input
                  style="width: 250px"
                  v-model="ssoContentFrom.btncontents.action.merchantName"
                ></el-input> -->
          </el-form-item>
          <el-form-item
            v-if="ssoContentFrom.btncontents.actionType == 'DIAL_PHONE'"
            label="拨打电话"
            prop="btncontents.action.target"
          >
            <div>{{ ssoContentFrom.btncontents.action.target }}</div>
            <!-- <el-input
                  class="textarea_text"
                  type="textarea"
                  v-model="ssoContentFrom.btncontents.action.target"
                ></el-input> -->
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  props: ['list'],
  data() {
    // const getAddRow = (val)=>{
    //     let row = 0
    //     val.split("\n").forEach(item=>{
    //       if (item.length === 0) {
    //           row += 1
    //       } else {
    //           row += Math.ceil(item.replace(/[\u0391-\uFFE5]/g,"aa").length / 46)
    //       }
    //     })
    //     console.log(row,'row');
    //   return row
    //   }
    // const textcontents = (rule, value, callback) => {
    //   if (value != '') {
    //     if(getAddRow(value) < 4){
    //       callback()

    //     }else{
    //       callback(new Error('摘要内容不能超过4行'))
    //     }
    //   } else {
    //     callback(new Error('摘要内容不可为空'))
    //   }

    // }
    return {
      ssoContentFrom: {
        imgcontents: {
          type: 'image',
          srcType: '',
          sourceId: '',
          url: '',
          thumbnailUrl: '',
          actionType: '',
          action: {
            target: '',
            merchantName: '',
          },
          positionNumber: 1,
        },
        titlecontents: {
          type: 'text',
          content: '',
          textTitle: true,
          positionNumber: 2,
        },
        textcontents: {
          type: 'text',
          content: '',
          textTitle: false,
          positionNumber: 3,
        },
        btncontents: {
          type: 'button',
          content: '',
          actionType: '',
          action: {
            target: '',
            merchantName: '',
          },
          positionNumber: 4,
        },
      },
      // rules: {
      //   imgcontents: {
      //     sourceId: [
      //       {
      //         required: true,
      //         message: "请选择素材",
      //         trigger: "change",
      //       },
      //     ],
      //     actionType: [
      //       {
      //         required: true,
      //         message: "请选择事件",
      //         trigger: "change",
      //       },
      //     ],
      //     action: {
      //       target: [
      //         {
      //           required: true,
      //           message: "事件内容不能为空",
      //           trigger: "change",
      //         },
      //       ],
      //       merchantName:[
      //          {
      //           required: true,
      //           message: "商户名称不能为空",
      //           trigger: "change",
      //         },
      //       ]
      //     },
      //   },
      //   titlecontents: {
      //     content: [
      //       {
      //         required: true,
      //         message: "标题不可为空",
      //         trigger: "change",
      //       },
      //     ],
      //   },
      //   textcontents: {
      //     content: [
      //       {
      //         required: true,
      //         // validator: textcontents,
      //         message: "摘要内容不可为空",
      //         trigger: "change",
      //       },
      //     ],
      //   },
      //   btncontents: {
      //     content: [
      //       {
      //         required: true,
      //         message: "按钮名称不可为空",
      //         trigger: "change",
      //       },
      //     ],
      //     actionType: [
      //       {
      //         required: true,
      //         message: "请选择事件",
      //         trigger: "change",
      //       },
      //     ],
      //     action: {
      //       target: [
      //         {
      //           required: true,
      //           message: "事件内容不能为空",
      //           trigger: "change",
      //         },
      //       ],
      //       merchantName:[
      //          {
      //           required: true,
      //           message: "商户名称不能为空",
      //           trigger: "change",
      //         },
      //       ]
      //     },
      //   },
      // },
    }
  },
  methods: {},
  watch: {
    list: {
      handler(val) {
        this.ssoContentFrom.imgcontents = val.ssoContentFrom.imgcontents
        this.ssoContentFrom.titlecontents = val.ssoContentFrom.titlecontents
        this.ssoContentFrom.textcontents = val.ssoContentFrom.textcontents
        this.ssoContentFrom.btncontents = val.ssoContentFrom.btncontents
        // if (val.chaobotId) {
        //   this.form.chatbotId = val.chaobotId;
        // }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
}
</script>

<style scoped>
.drawer_img {
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: #eee;
}
.drawer_img_content {
  margin-top: 10px;
}
.drawe_item_img {
  width: 240px;
  height: 135px;
  position: relative;
}
.image_d {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
.add_img {
  position: absolute;
  font-size: 30px;
  font-weight: 800;
  top: 0;
  margin-left: 110px;
  margin-top: 50px;
  color: #fff;
}
.file_item {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
}
.item_medias {
  width: 194px;
  height: 215px;
  background: #fff;
  border-radius: 8px;
  position: relative;
  margin: 10px;
  border: 1px solid #eee;
  z-index: 9;
}
.item_medias_active {
  width: 194px;
  height: 215px;
  background: #fff;
  border-radius: 8px;
  position: relative;
  margin: 10px;
  border: 2px solid #409eff;
  z-index: 9;
}
.media_h {
  width: 100%;
  height: 124px;
}
.temp_media_h {
  width: 100%;
  height: 100%;
}
.media_t {
  width: 100%;
  height: 62px;
  font-size: 12px;
  line-height: 20px;
}
.image {
  width: 100%;
  height: 100%;
  border-radius: 8px 8px 0px 0px;
}
.madia_footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.media_name {
  display: inline-block;
  margin-left: 10px;
  margin-top: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 170px;
}
.media_icon {
  width: 24px;
  height: 22px;
  position: absolute;
  left: 2.08%;
  right: 2.08%;
  top: 6.25%;
  bottom: 5.9%;
}
.replace {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  color: #fff;
  right: 0;
  top: 0;
  margin: 10px;
  z-index: 9;
}
.textarea_text {
  width: 315px;
  height: 90px;
  transform: scale(0.795);
  transform-origin: 0px 0px;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
  font-size: 12px;
}
.el-textarea .el-input__count {
  bottom: 0px;
  right: 30px;
}
</style>
