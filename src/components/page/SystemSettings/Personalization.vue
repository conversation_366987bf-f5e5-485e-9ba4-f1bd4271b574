<template>
  <div style="background: #fff; padding: 15px">
    <!-- <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon>
          个性化设置</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div> -->
    <div class="Templat-matter">
      <p style="font-weight: bolder">温馨提醒：</p>
      <p>
        1、个性化设置支持所有类型账号。优先看本账号设置，其次看管理商账号设置。
      </p>
      <p>2、短信签名：登录/修改密码验证码时生效。</p>
    </div>
    <div class="OuterFrame fillet" style="height: 100%">
      <div>
        <el-form
          ref="formInline"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建人" prop="createName">
            <el-input
              v-model="formInline.createName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="time1">
            <el-date-picker
              class="input-time"
              v-model="formInline.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              @change="getTimeOperating"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="ListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reset('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table" style="padding-bottom: 40px">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="LogoSetting"
              >添加个性化设置</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="Personalization"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名" title="用户名" width="120">
            <template #default="scope">
              <div>{{ scope.row.userName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="域名" title="域名">
            <template #default="scope">
              <div>{{ scope.row.domainName }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="电话号码" title="电话号码">
                            <template #default="scope" >{{ scope.row.mobile}} </template>
                        </vxe-column> -->
          <vxe-column field="短信签名" title="短信签名">
            <template #default="scope">
              <div>{{ scope.row.signature }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="登录提示语" title="登录提示语">
                            <template #default="scope" >{{ scope.row.reminder}} </template>
                        </vxe-column>
                        <vxe-column field="登录title" title="登录title">
                            <template #default="scope" >{{ scope.row.title}} </template>
                        </vxe-column>
                        <vxe-column field="备案信息" title="备案信息">
                            <template #default="scope" >{{ scope.row.recordInformation}} </template>
                        </vxe-column>
                        <vxe-column field="企业icon" title="企业icon" width='75'>
                            <template #default="scope" ><div style="text-align: center;"><img :src="scope.row.icon" alt=""></div></template>
                        </vxe-column> -->
          <vxe-column field="企业logo" title="企业logo" width="75">
            <template #default="scope"
              ><div
                @click="Showimage(scope.row.logo)"
                style="color: #16a085; cursor: pointer"
                >点击查看</div
              ></template
            >
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template #default="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="160">
            <template #default="scope">
              <div>{{ scope.row.createTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="130">
            <template #default="scope">
              <el-button link type="primary" @click="dealWith(scope.row)"
                ><el-icon
                  ><el-icon><Edit /></el-icon></el-icon
                >&nbsp;编辑</el-button
              >
              <el-button
                link
                @click="logoStatus(scope.row)"
                style="color: #f56c6c"
                v-if="scope.row.status == '1'"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;停用</el-button
              >
              <el-button
                link
                type="primary"
                @click="logoStatus(scope.row)"
                v-else-if="scope.row.status == '2'"
                ><el-icon><SuccessFilled /></el-icon>&nbsp;启用</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <div style="float: right; margin-top: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInlines.currentPage"
            :page-size="formInlines.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.total"
          >
          </el-pagination>
        </div>
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <!-- 添加个性化设置 -->
    <el-dialog
      title="个性化设置"
      v-model="Personalization"
      :close-on-click-modal="false"
      width="500px"
    >
      <el-form
        :inline="true"
        :model="formLogo"
        :rules="formLogoRules"
        ref="formLogos"
        class="demo-form-inline IPForm"
      >
        <el-form-item label="用户名" label-width="100px" prop="userName">
          <el-input
            v-if="IPaddDialog == '编辑个性化设置'"
            type="text"
            style="background: #00000014"
            disabled
            v-model="formLogo.userName"
            placeholder=" 请输入用户名"
            class="input-w"
          ></el-input>
          <el-input
            v-else
            type="text"
            v-model="formLogo.userName"
            placeholder=" 请输入用户名"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="域名" label-width="100px" prop="domainName">
          <el-input
            type="text"
            style="background: #00000014"
            disabled
            v-model="formLogo.domainName"
            placeholder=" 请输入域名"
            class="input-w"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="电话号码" label-width="100px" prop="mobile">
                        <el-input type="text" v-model="formLogo.mobile" placeholder=" 请输入电话号码" class="input-w"></el-input>
                    </el-form-item> -->
        <el-form-item label="短信签名" label-width="100px" prop="signature">
          <el-input
            type="text"
            v-model="formLogo.signature"
            placeholder=" 请输入接收短信登录验证码的签名"
            class="input-w"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="登录提示语" label-width="100px" prop="reminder">
                        <el-input type="text" v-model="formLogo.reminder" placeholder=" 请输入登录页面提示语" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="登录title" label-width="100px" prop="title">
                        <el-input type="text" v-model="formLogo.title" placeholder=" 请输入登录后title" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="备案信息" label-width="100px" prop="recordInformation">
                        <el-input type="text" v-model="formLogo.recordInformation" placeholder=" 请输入备案信息" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="企业icon" label-width="100px" prop="">
                        <el-upload
                            class="upload-demo"
                            :action="window.path.cpus + 'consumerclientinfo/uploadImage'"
                            :headers="token"
                             :limit="1"
                            :on-preview="handlePreview"
                            :on-remove="handleRemove"
                            :file-list="fileList"
                            :on-success="handleAvatarSuccess1"
                            :before-upload="beforeAvatarUpload1"
                            list-type="picture">
                            <el-button size="default" type="primary">点击上传</el-button>
                            <div slot="tip" class="el-upload__tip">只能上传.ico格式</div>
                        </el-upload>
                    </el-form-item> -->
        <el-form-item label="企业logo" label-width="100px" prop="">
          <el-upload
            class="upload-demo"
            :action="url"
            :headers="token"
            :limit="1"
            :on-preview="handlePreview1"
            :on-remove="handleRemove1"
            :file-list="fileList1"
            :on-success="handleAvatarSuccess2"
            :before-upload="beforeAvatarUpload2"
            list-type="picture"
          >
            <el-button size="default" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">png格式照片，大小不超过1M</div>
              <div class="el-upload__tip">照片尺寸，150*45</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="Personalization = false">取 消</el-button>
          <el-button type="primary" @click="personalise('formLogos')"
            >保 存</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 图片展示 -->
    <el-dialog
      title="企业logo"
      v-model="imgShow"
      :close-on-click-modal="false"
      width="300px"
    >
      <img :src="imgSrc" alt="" width="150px" height="45px" />
    </el-dialog>
  </div>
</template>

<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem.vue";
export default {
  components: {
    DatePlugin,
    TableTem,
  },
  name: "Personalization",
  data() {
    // 验证IP规则
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      // 获取id
      personId: "",
      IPaddDialog: "",
      url:window.path.cpus + 'consumerclientinfo/uploadImage',
      formLogoRules: {
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        domainName: [
          { required: true, message: "请输入域名", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        reminder: [
          { required: true, message: "请输入提示语", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        // mobile:[
        //     { required: false, message: '请输入电话号码', trigger: 'blur' },
        // ],
        signature: [
          { required: false, message: "请输入签名", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        title: [
          { required: true, message: "请输入登录title", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        recordInformation: [
          { required: true, message: "请输入备案信息", trigger: "blur" },
          {
            min: 1,
            max: 100,
            message: "长度在 1 到 100 个字符",
            trigger: "blur",
          },
        ],
      },
      fileList: [],
      fileList1: [],
      actionHttp1: "",
      token: {
        Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
      },
      // 搜索数据
      formInline: {
        createName: "",
        userName: "",
        begTime: "",
        endTime: "",
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        createName: "",
        userName: "",
        begTime: "",
        endTime: "",
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 新增logo
      formLogo: {
        userName: "",
        domainName: "partner.zthysms.com",
        reminder: "",
        mobile: "",
        signature: "",
        title: "",
        recordInformation: "",
        icon: "",
        logo: "",
      },
      // 添加角色弹出窗
      Personalization: false,
      imgShow: false,
      imgSrc: "",
      datePluginValueList: {
        type: "date",
        start: "",
        end: "",
        range: "-",
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    };
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.cpus + "personalizationSettings/page",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.tablecurrent.total = res.total;
        }
      );
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      this.formInline.begTime = "";
      this.formInline.endTime = "";
      Object.assign(this.formInlines, this.formInline);
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.begTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.begTime = "";
        this.formInline.endTime = "";
      }
    },
    // 图片上传
    handleRemove(file, fileList) {
      this.formLogo.icon = "";
    },
    handlePreview(file) {
      // console.log(file);
    },
    handleRemove1(file, fileList) {
      this.formLogo.logo = "";
    },
    handlePreview1(file) {
      // console.log(file);
    },
    handleAvatarSuccess1(res, file) {
      this.formLogo.icon = res.data;
    },
    handleAvatarSuccess2(res, file) {
      this.formLogo.logo = res.data;
    },
    beforeAvatarUpload1(file) {
      console.log(file);
      const isico = file.type === "image/x-icon";
      // const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isico) {
        this.$message.error("上传头像图片只能是 ico 格式!");
        return false;
      }
      // if (!isLt2M) {
      //     this.$message.error('上传头像图片大小不能超过 2MB!');
      // }

      // return isJPG && isLt2M;
    },
    beforeAvatarUpload2(file) {
      console.log(file);
      const isJPG1 = file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 1;
      if (!isJPG1) {
        this.$message.error("上传头像图片只能是 png 格式!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 1MB!");
      }

      // return isJPG && isLt2M;
    },
    // 图片展示
    Showimage(val) {
      this.imgSrc = val;
      this.imgShow = true;
    },
    // 添加个性话按钮
    LogoSetting() {
      this.IPaddDialog = "个性化设置";
      this.personId = null;
      this.Personalization = true;
    },
    // 编辑
    dealWith(val) {
      this.personId = val.id;
      this.IPaddDialog = "编辑个性化设置";
      this.Personalization = true;
      this.$nextTick(() => {
        this.$refs.formLogos.resetFields();
        Object.assign(this.formLogo, val);
        this.fileList = [{ name: "", url: val.icon }];
        this.fileList1 = [{ name: "", url: val.logo }];
      });
    },
    // 停用启用
    logoStatus(val) {
      this.$confirms.confirmation(
        "post",
        val.status == 1 ? "确认停用" : "确认启用",
        window.path.cpus + "personalizationSettings/updateStatus",
        {
          id: val.id,
          status: val.status == 1 ? "2" : "1",
          userName: val.userName,
        },
        (res) => {
          this.InquireList();
        }
      );
    },

    // 新增个性化
    personalise() {
      this.$refs["formLogos"].validate((valid) => {
        if (valid) {
          let fprmPId;
          if (this.personId) {
            fprmPId = {
              userName: this.formLogo.userName,
              id: this.personId,
            };
          } else {
            fprmPId = {
              userName: this.formLogo.userName,
            };
          }
          window.api.post(
            window.path.cpus + "personalizationSettings/check",
            fprmPId,
            (res) => {
              if (res.code != 400) {
                if (this.IPaddDialog == "个性化设置") {
                  this.$confirms.confirmation(
                    "post",
                    "确认新增",
                    window.path.cpus + "personalizationSettings/insertSettings",
                    this.formLogo,
                    (res) => {
                      this.InquireList();
                      this.Personalization = false;
                    }
                  );
                } else {
                  this.$confirms.confirmation(
                    "post",
                    "确认编辑",
                    window.path.cpus + "personalizationSettings/insertSettings",
                    this.formLogo,
                    (res) => {
                      this.InquireList();
                      this.Personalization = false;
                    }
                  );
                }
              } else {
                this.$message({
                  type: "error",
                  duration: "2000",
                  message: res.msg,
                });
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    // 关闭弹出层
    handleClose(done) {
      this.addCharacterPopUps = false;
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
    },
  },
  activated() {
    this.InquireList();
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听弹出层关闭
    Personalization: function (val) {
      if (val == false) {
        this.$refs.formLogos.resetFields(); //清空表单
        (this.formLogo = {
          userName: "",
          domainName: "",
          reminder: "",
          mobile: "",
          signature: "",
          title: "",
          recordInformation: "",
          icon: "",
          logo: "",
        }),
          (this.formLogo.domainName = "partner.zthysms.com");
        this.fileList = [];
        this.fileList1 = [];
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList();
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
}
.Templat-matter > p {
  padding: 5px 0;
}
</style>
