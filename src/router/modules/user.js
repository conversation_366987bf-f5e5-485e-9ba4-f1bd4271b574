// 用户管理相关路由
export default [
  {
    path: '/ManagerSub',
    meta: { title: ' 子用户列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/UserMag/components/ManagerSub.vue'),
  },
  {
    path: '/UserMag',
    component: () => import('@/components/page/UserMag/UserMag.vue'),
    meta: { title: '用户管理', keepAlive: true, isBack: false },
  },
  {
    path: '/subAccountRealInfo',
    component: () => import('@/components/page/UserMag/subAccountRealInfo.vue'),
    meta: { title: '子用户实名信息管理', keepAlive: true, isBack: false },
  },
  {
    path: '/scompanyrisk',
    component: () => import('@/components/page/UserMag/scompanyrisk.vue'),
    meta: { title: '风险账号管理', keepAlive: true, isBack: false },
  },
  {
    path: '/userModification',
    component: () => import('@/components/page/UserMag/userModification.vue'),
    meta: { title: '新增编辑用户管理', keepAlive: true, isBack: false },
  },
  {
    path: '/UserDetail',
    component: () => import('@/components/page/UserMag/UserDetail.vue'),
    meta: { title: '用户详情', keepAlive: true, isBack: false },
  },
  {
    path: '/SMSconfiguration',
    meta: { title: '短信配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/SMSconfiguration.vue'),
  },
  {
    path: '/SMS5Gconfiguration',
    meta: { title: '5G短信配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/SMS5gConfiguration.vue'),
  },
  {
    path: '/yuexinconfiguration',
    meta: { title: '阅信配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/yuexinConfiguration.vue'),
  },
  {
    path: '/MMSconfiguration',
    meta: { title: '彩信配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/MMSconfiguration.vue'),
  },
  {
    path: '/RMSconfiguration',
    meta: { title: '视频短信配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/RMSconfiguration.vue'),
  },
  {
    path: '/ImsInternational',
    meta: { title: '国际短信配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/ImsInternational.vue'),
  },
  {
    path: '/FlashTest',
    meta: { title: '闪验配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/FlashTest.vue'),
  },
  {
    path: '/voiceCode',
    meta: { title: '语音验证码配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/voiceCode.vue'),
  },
  {
    path: '/voiceNotice',
    meta: { title: '语音通知配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/voiceNotice.vue'),
  },
  {
    path: '/productSetUp',
    meta: { title: '产品配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/productSetUp.vue'),
  },
  {
    path: '/batchaaAction',
    meta: { title: '批量操作', keepAlive: false, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/batchaaAction.vue'),
  },
  {
    path: '/batchUserShop',
    meta: { title: '用户关停', keepAlive: false, isBack: false },
    component: () => import('@/components/page/UserMag/batchUserShop.vue'),
  },
  {
    path: '/batchUserLists',
    meta: { title: '批量添加黑/白名单', keepAlive: false, isBack: false },
    component: () => import('@/components/page/UserMag/batchUserLists.vue'),
  },
  {
    path: '/batchUserOperators',
    meta: { title: '批量操作运营商下发 ', keepAlive: false, isBack: false },
    component: () => import('@/components/page/UserMag/batchUserOperators.vue'),
  },
  {
    path: '/batchShortlinkReset',
    meta: { title: '批量短链续期 ', keepAlive: false, isBack: false },
    component: () => import('@/components/page/UserMag/batchShortlinkReset.vue'),
  },
  {
    path: '/batchDynamicIPReset',
    meta: { title: '批量动态IP续期 ', keepAlive: false, isBack: false },
    component: () => import('@/components/page/UserMag/batchDynamicIPReset.vue'),
  },
  {
    path: '/batchUserList',
    meta: { title: '任务详情', keepAlive: false, isBack: false },
    component: () => import('@/components/page/UserMag/batchUserList.vue'),
  },
  {
    path: '/ShortchainSetting',
    meta: { title: '短链用户配置', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/UserMag/ShortchainSetting.vue'),
  },
] 