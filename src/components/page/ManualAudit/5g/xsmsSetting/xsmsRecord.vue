<template>
  <div class="container_left">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon
          >阅信费用返还</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="padding: 10px">
      <el-form
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="返还状态" prop="refundStatus">
          <el-select
            class="input-w"
            v-model="ruleForm.refundStatus"
            clearable
            placeholder="请选择"
          >
            <el-option label="全部 " value=""> </el-option>
            <el-option label="待返回 " value="0"> </el-option>
            <el-option label="已返还" value="1"> </el-option>
            <el-option label="返回失败" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短链" prop="url">
          <el-input
            class="input-w"
            v-model="ruleForm.url"
            placeholder="阅信链接/AIM链接"
          ></el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="短链创建时间" prop="value1">
          <el-date-picker
            :default-time="['00:00:00', '23:59:59']"
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handelDate"
          >
          </el-date-picker>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px"></div>
    <div style="padding: 10px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
        :header-cell-style="getRowClass"
      >
        <el-table-column label="短链创建时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time" v-if="scope.row.createTime">{{
                                    moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="短链生成批次" width="120">
          <template v-slot="scope">{{ scope.row.batchNo }}</template>
        </el-table-column>
        <el-table-column label="chatbotId" width="120">
          <template v-slot="scope">{{ scope.row.chatbotId }}</template>
        </el-table-column>
        <el-table-column label="Chatbot名称" width="120">
          <template v-slot="scope">{{ scope.row.chatbotName }}</template>
        </el-table-column>
        <el-table-column label="模板名称" width="120">
          <template v-slot="scope">{{ scope.row.xsmsTemplateName }}</template>
        </el-table-column>
        <el-table-column label="模板ID" width="120">
          <template v-slot="scope">{{ scope.row.xsmsTemplateId }}</template>
        </el-table-column>
        <el-table-column label="阅信链接" width="120">
          <template v-slot="scope">
            <a
              v-if="scope.row.xsmsUrl"
              :href="'https://' + scope.row.xsmsUrl"
              target="_blank"
              rel="noopener noreferrer"
              >{{ scope.row.xsmsUrl }}</a
            >
          </template>
        </el-table-column>
        <el-table-column label="阅信解析次数上限">
          <template v-slot="scope">{{ scope.row.xsmsShowTimes }}</template>
        </el-table-column>
        <el-table-column label="阅信已解析次数">
          <template v-slot="scope">{{ scope.row.xsmsUsedShowTimes }}</template>
        </el-table-column>
        <el-table-column label="阅信返还解析次数">
          <template v-slot="scope">{{
            scope.row.xsmsRefundShowTimes
          }}</template>
        </el-table-column>
        <el-table-column label="阅信预扣金额(元)">
          <template v-slot="scope">{{ scope.row.xsmsTotalAmount }}</template>
        </el-table-column>
        <el-table-column label="阅信已用金额(元)">
          <template v-slot="scope">{{ scope.row.xsmsUsedAmount }}</template>
        </el-table-column>
        <el-table-column label="阅信返还金额(元)">
          <template v-slot="scope">{{ scope.row.xsmsRefundAmount }}</template>
        </el-table-column>
        <el-table-column label="AIM链接" width="120">
          <template v-slot="scope">
            <a
              v-if="scope.row.aimUrl"
              :href="'https://' + scope.row.aimUrl"
              target="_blank"
              rel="noopener noreferrer"
              >{{ scope.row.aimUrl }}</a
            >
          </template>
        </el-table-column>
        <el-table-column label="AIM解析次数上限">
          <template v-slot="scope">{{ scope.row.aimShowTimes }}</template>
        </el-table-column>
        <el-table-column label="AIM已解析次数">
          <template v-slot="scope">{{ scope.row.aimUsedShowTimes }}</template>
        </el-table-column>
        <el-table-column label="AIM返还解析次数">
          <template v-slot="scope">{{ scope.row.aimRefundShowTimes }}</template>
        </el-table-column>
        <el-table-column label="AIM预扣金额(元)">
          <template v-slot="scope">{{ scope.row.aimTotalAmount }}</template>
        </el-table-column>
        <el-table-column label="AIM已用金额(元)">
          <template v-slot="scope">{{ scope.row.aimUsedAmount }}</template>
        </el-table-column>
        <el-table-column label="AIM返还金额(元)">
          <template v-slot="scope">{{ scope.row.aimRefundAmount }}</template>
        </el-table-column>
        <el-table-column label="单价(元)" width="120">
          <template v-slot="scope">
            <span v-if="scope.row.price == null">-</span>
            <span v-else-if="scope.row.price == 0 || scope.row.price">{{
              scope.row.price
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="预扣金额(元)" width="120">
          <template v-slot="scope">
            <span v-if="scope.row.totalAmount == null">-</span>
            <span
              v-else-if="scope.row.totalAmount == 0 || scope.row.totalAmount"
              >{{ scope.row.totalAmount }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="已用金额(元)" width="120">
          <template v-slot="scope">
            <span v-if="scope.row.usedAmount == null">-</span>
            <div v-else-if="scope.row.usedAmount == 0 || scope.row.usedAmount">
              <span>{{ scope.row.usedAmount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="剩余金额(元)" width="120">
          <template v-slot="scope">
            <span v-if="scope.row.refundAmount == null">-</span>
            <div
              v-else-if="scope.row.refundAmount == 0 || scope.row.refundAmount"
            >
              <span>{{ scope.row.refundAmount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作人" width="120">
          <template v-slot="scope">{{ scope.row.refundUser }}</template>
        </el-table-column>
        <el-table-column label="操作时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.refundTime">
              {{ $parseTime(scope.row.refundTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.refundTime">
              {{ $parseTime(scope.row.refundTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time" v-if="scope.row.refundTime">{{
                                    moment(scope.row.refundTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="返还状态" fixed="right" width="120">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.refundStatus == 0"
            >
              待返还
            </div>
            <div
              class="item_status"
              style="
                background: #dae9ff;
                color: #4e95ff;
                border: 1px solid #c2d6f3;
              "
              v-if="scope.row.refundStatus == 1"
            >
              已返还
            </div>
            <div
              class="item_status"
              style="
                background: #ffe7e7;
                color: #ff6666;
                border: 1px solid #fac2c2;
              "
              v-if="scope.row.refundStatus == 2"
            >
              返还失败
            </div>
          </template>
        </el-table-column>
        <el-table-column label="返还原因" fixed="right" width="120">
          <template v-slot="scope">
            <div v-if="scope.row.refundReason == 1">失败返还</div>
            <div v-if="scope.row.refundReason == 2">到期返还</div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.refundStatus == 0 || scope.row.refundStatus == 2"
              type="text"
              style="color: #409eff; margin-left: 0px"
              @click="regenerate(scope.row)"
              >返还金额</el-button
            >
            <!-- <el-button v-if="scope.row.linkStatus == 2" type="text"
                                style="color: #F56C6C; margin-left: 10px" @click="identificationFfailure(scope.row)"> 确认失败
                            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <div>
        <div></div>
        <div style="margin: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      title="返还金额"
      v-model="dialogEditNum"
      width="30%"
      :before-close="handleClose"
    >
      <el-descriptions title="阅信信息" size="medium" :column="2">
        <el-descriptions-item label="短链生成批次">{{
          updateList.batchNo
        }}</el-descriptions-item>
        <el-descriptions-item label="模板名称">{{
          updateList.xsmsTemplateName
        }}</el-descriptions-item>
        <!-- 阅信 -->
        <el-descriptions-item label="阅信链接">{{
          updateList.xsmsUrl
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信解析数上限">
          <span v-if="updateList.xsmsShowTimes == null">-</span>
          <span
            v-else-if="
              updateList.xsmsShowTimes == 0 || updateList.xsmsShowTimes
            "
            >{{ updateList.xsmsShowTimes }}</span
          >
        </el-descriptions-item>
        <el-descriptions-item label="阅信已解析数">{{
          updateList.xsmsUsedShowTimes
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信返还解析次数">{{
          updateList.xsmsRefundShowTimes
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信预扣金额（元）">{{
          updateList.xsmsTotalAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信已用金额（元）">{{
          updateList.xsmsUsedAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信返还金额（元）">{{
          updateList.xsmsRefundAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信更新时间">
          <span v-if="updateList.xsmsExpireTime">{{
            moment(updateList.xsmsExpireTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </el-descriptions-item>
        <!-- AIM -->
      </el-descriptions>
      <el-descriptions title="AIM信息" size="medium" :column="2">
        <el-descriptions-item label="短链生成批次">{{
          updateList.batchNo
        }}</el-descriptions-item>
        <el-descriptions-item label="模板名称">{{
          updateList.xsmsTemplateName
        }}</el-descriptions-item>
        <!-- AIM -->
        <el-descriptions-item label="AIM链接">{{
          updateList.aimUrl
        }}</el-descriptions-item>
        <el-descriptions-item label="AIM解析数上限">
          <span v-if="updateList.aimShowTimes == null">-</span>
          <span
            v-else-if="updateList.aimShowTimes == 0 || updateList.aimShowTimes"
            >{{ updateList.aimShowTimes }}</span
          >
        </el-descriptions-item>
        <el-descriptions-item label="AIM已解析数">{{
          updateList.aimUsedShowTimes
        }}</el-descriptions-item>
        <el-descriptions-item label="AIM返还解析次数">{{
          updateList.aimRefundShowTimes
        }}</el-descriptions-item>
        <el-descriptions-item label="AIM预扣金额（元）">{{
          updateList.aimTotalAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="AIM已用金额（元）">{{
          updateList.aimUsedAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="AIM返还金额（元）">{{
          updateList.aimRefundAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="AIM更新时间">
          <span v-if="updateList.aimUpdateTime">{{
            moment(updateList.aimUpdateTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="总信息" size="medium" :column="2">
        <el-descriptions-item label="总解析数">{{
          updateList.totalShowTimes
        }}</el-descriptions-item>
        <!-- <el-descriptions-item label="总已用解析数">{{updateList.totalShowTimes}}</el-descriptions-item> -->
        <el-descriptions-item label="单价（元）">{{
          updateList.price
        }}</el-descriptions-item>
        <el-descriptions-item label="总预扣金额（元）">{{
          updateList.totalAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="总已用金额（元）">{{
          updateList.usedAmount
        }}</el-descriptions-item>
        <el-descriptions-item label="总返还金额（元）">
          <span v-if="updateList.refundAmount == null">-</span>
          <span
            v-else-if="updateList.refundAmount == 0 || updateList.refundAmount"
            >{{ updateList.refundAmount }}</span
          >
        </el-descriptions-item>
        <!-- <el-descriptions-item label="总剩余金额（元）">{{updateList.usedAmount}}</el-descriptions-item> -->
      </el-descriptions>
      <el-form :model="formInline" class="demo-form-inline">
        <!-- <el-form-item label="阅信已解析次数">
                        <el-input style="width:200px"  v-model="formInline.usedTimes" placeholder="请输入解析次数"></el-input>
                    </el-form-item> -->
        <el-form-item label="">
          <el-checkbox v-model="formInline.checked"
            >确认以上信息无误</el-checkbox
          >
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogEditNum = false">取 消</el-button>
          <el-button
            type="primary"
            :disabled="!formInline.checked"
            @click="handelquite"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  components: {
    ElIconLxEmoji,
  },
  data() {

return {
status: '',
title: '',
reason: '',
usedTimes: '',
xsmsUrl: '',
usedTimesIndex: 0,
dialogVisible: false,
dialogEditNum: false,
codeFlag: true,
value1: [],
formInline: {
checked: false,
id: '',
},
updateList: {},
ruleForm: {
// value1: [],
refundStatus: '',
startTime: '',
endTime: '',
// status: '',
url: '',
currentPage: 1,
pageSize: 10,
},
reasonFrom: {
reason: '',
},
rules: {
reason: [
{ required: true, message: '审核原因不能为空', trigger: 'blur' },
],
},
tabelAlllist: {
// value1: [],
refundStatus: '',
startTime: '',
endTime: '',
url: '',
currentPage: 1,
pageSize: 10,
},
detail: {},
tableDataObj: {
loading2: false,
tableData: [],
total: 0,
},
isFirstEnter: false,
}
},
name: 'xsmsRecord',
created() {
this.isFirstEnter = true
this.$nextTick(() => {
this.getchatList()
})
},
activated() {
if (this.$route.meta.isBack || !this.isFirstEnter) {
this.$nextTick(() => {
this.getchatList()
})
} else {
this.$route.meta.isBack = false
this.isFirstEnter = false
}
},
methods: {
getchatList() {
this.tableDataObj.loading2 = true
// this.tabelAlllist.status = this.tabelAlllist.shortStatus.join(',')
window.api.post(
window.path.fiveWeb + '/operator/refund/page',
this.tabelAlllist,
(res) => {
  this.tableDataObj.tableData = res.data.records
  this.tableDataObj.total = res.data.total
  this.tableDataObj.loading2 = false
  // console.log(res, "res");
}
)
},
getRowClass({ rowIndex, columnIndex }) {
if (
columnIndex == 13 ||
columnIndex == 14 ||
columnIndex == 15 ||
columnIndex == 16 ||
columnIndex == 17 ||
columnIndex == 18 ||
columnIndex == 19
) {
return 'background:#EEFBEE;'
} else if (
columnIndex == 6 ||
columnIndex == 7 ||
columnIndex == 8 ||
columnIndex == 9 ||
columnIndex == 10 ||
columnIndex == 11 ||
columnIndex == 12
) {
return 'background:#EDF4FF;'
}
},
regenerate(row) {
this.updateList = row
this.dialogEditNum = true
this.formInline.id = row.id
},
handelquite() {
this.$confirms.confirmation(
'put',
'是否确认返还！',
window.path.fiveWeb + '/operator/refund/refund',
{
  id: this.formInline.id,
},
(res) => {
  if (res.code == 200) {
    this.getchatList()
    this.dialogEditNum = false
  }
}
)
},
handleClose() {
// this.dialogVisible = false;
this.dialogEditNum = false
},
handelDate(val) {
if (val) {
this.ruleForm.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
this.ruleForm.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
} else {
this.ruleForm.startTime = ''
this.ruleForm.endTime = ''
}
},
handelUpdate(row, index) {
this.usedTimesIndex = index
this.xsmsUrl = row.xsmsUrl
},
handelInput(val) {
window.api.put(
window.path.fiveWeb + '/operator/xsmsamiurl/updateAvailableTimes',
{
  usedTimes: val,
  xsmsUrl: this.xsmsUrl,
},
(res) => {
  if (res.code == 200) {
    this.getchatList()
    this.usedTimes = ''
    this.usedTimesIndex = 0
    this.$message({
      message: res.msg,
      type: 'success',
    })
  } else {
    this.$message({
      message: res.msg,
      type: 'error',
    })
  }
  // console.log(res, "res");
}
)
},
handleSizeChange(size) {
this.tabelAlllist.pageSize = size
this.getchatList()
},
handleCurrentChange(currentPage) {
this.tabelAlllist.currentPage = currentPage
this.getchatList()
},
submitForm() {
Object.assign(this.tabelAlllist, this.ruleForm)
this.getchatList()
},
resetForm() {
this.$refs.ruleForm.resetFields()
this.ruleForm.startTime = ''
this.ruleForm.endTime = ''
Object.assign(this.tabelAlllist, this.ruleForm)
this.value1 = []
this.getchatList()
},
checked(row) {
this.dialogVisible = true
this.registerFrom = row
this.registerFrom.renewed = row.renewed + ''
this.registerFrom.industryTypeCode = row.industryTypeCode * 1
this.status = row.customerStatus
//   console.log(this.registerFrom );
if (row.code) {
this.codeFlag = true
} else {
this.codeFlag = false
}
},
audit(row, val, tit, from) {
this.$refs[from].validate((valid) => {
if (valid) {
  this.$confirms.confirmation(
    'post',
    `确认审核${tit}模板吗？`,
    window.path.fiveWeb + '/operator/xsmstemplate/audit',
    {
      audit: val,
      reason: this.reasonFrom.reason,
      templateId: row.id,
    },
    (res) => {
      if (res.code == 200) {
        this.dialogVisible = false
        this.getchatList()
      }
    }
  )
} else {
  console.log('error submit!!')
  return false
}
})
},
},
watch: {
dialogVisible(val) {
if (!val) {
this.reason = ''
this.reasonFrom.reason = ''
// this.$refs['reasonFrom'].resetFields();
// this.detail = {};
if (this.detail.status == 0 || this.detail.status == 5) {
  this.$refs['reasonFrom'].resetFields()
}
}
},
dialogEditNum(val) {
if (!val) {
this.formInline.checked = false
this.formInline.id = ''
}
},
}
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>

<style>
/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>
