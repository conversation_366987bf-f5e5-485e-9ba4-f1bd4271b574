<template>
  <div class="tags" v-if="showTags">
    <ul>
      <li
        class="tags-li"
        v-for="(item, index) in tagsList"
        :class="{ active: isActive(item.title) }"
        :key="index"
        @click="handelClick(item.path)"
      >
        <router-link :to="item.path" class="tags-li-title">
          {{ item.title }}
        </router-link>
        <span class="tags-li-icon" @click="closeTags(index)"><el-icon style="margin-top: 4px;"><Close /></el-icon></span>
      </li>
    </ul>
    <div class="tags-close-box">
      <el-dropdown @command="handleTags">
        <el-button size="default" type="primary">
          标签选项<el-icon><ArrowDown /></el-icon>
        </el-button>
        <template v-slot:dropdown>
          <el-dropdown-menu size="default">
            <el-dropdown-item command="other">关闭其他</el-dropdown-item>
            <el-dropdown-item command="all">关闭所有</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import bus from './bus'
export default {
  data() {
    return {
      tagsList: [],
    }
  },
  methods: {
    isActive(path) {
      // 如果当前路由不需要展示标签页，直接返回 false
      if (this.$route.meta.hideTag) {
        return false
      }
      
      localStorage.setItem('tagPathName', path)
      let { meta, fullPath } = { ...this.$route }
      let tagRouter = {
        meta,
        fullPath,
        matched: [{}, { components: { default: { name: meta.title } } }],
      }
      localStorage.setItem('tagRouter', JSON.stringify(tagRouter))
      return path === this.$route.meta.title
    },
    // 关闭单个标签
    closeTags(index) {
      const delItem = this.tagsList.splice(index, 1)[0]
      const item = this.tagsList[index]
        ? this.tagsList[index]
        : this.tagsList[index - 1]
      if (item) {
        delItem.path === this.$route.fullPath && this.$router.push(item.path)
        // console.log(delItem.path,'delItem.path');
      } else {
        this.$router.push('/')
      }
    },
    // 关闭全部标签
    closeAll() {
      this.tagsList = []
      this.$router.push('/')
    },
    // 关闭其他标签
    closeOther() {
      const curItem = this.tagsList.filter((item) => {
        return item.path === this.$route.fullPath
      })
      this.tagsList = curItem
    },
    handelClick(url) {
      sessionStorage.setItem('path', url)
    },
    // 设置标签
    setTags(route) {
      const isExist = this.tagsList.some((item) => {
        return item.title === route.meta.title
      })
      // console.log(isExist,'ggg');
      if (!isExist) {
        if (this.tagsList.length >= 10) {
          this.tagsList.shift()
        }
        // console.log(this.tagsList);
        // let flag = this.tagsList.some((item) => {
        //     return item.title == item.title;
        // });
        this.tagsList.push({
          title: route.meta.title,
          path: route.fullPath,
          name: route.matched[1].components.default.name,
        })
        // console.log( this.tagsList,'ll');
      } else {
        // console.log( this.tagsList,'222');
        this.tagsList.forEach((item) => {
          if (item.title === route.meta.title) {
            item.path = route.fullPath
          }
        })
        $emit(bus, 'path', route.fullPath)
      }
      $emit(bus, 'tags', this.tagsList)
    },
    handleTags(command) {
      command === 'other' ? this.closeOther() : this.closeAll()
    },
  },
  computed: {
    showTags() {
      return this.tagsList.length > 0
    },
  },
  watch: {
    $route(newValue, oldValue) {
      if (!newValue.meta.hideTag) {
        this.setTags(newValue)
      }
    },
  },
  created() {
    if (this.$route.meta.hideTag) {
      this.setTags(JSON.parse(localStorage.getItem('tagRouter')))
    } else {
      this.setTags(this.$route)
    }
  },
  emits: ['path', 'tags'],
}
</script>

<style>
.tags {
  position: relative;
  height: 30px;
  overflow: hidden;
  background: #fff;
  padding-right: 120px;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);
}
.tags ul {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.tags-li {
  float: left;
  margin: 3px 5px 2px 3px;
  border-radius: 3px;
  font-size: 12px;
  overflow: hidden;
  cursor: pointer;
  height: 23px;
  line-height: 23px;
  border: 1px solid #e9eaec;
  background: #fff;
  padding: 0 5px 0 12px;
  vertical-align: middle;
  color: #666;
  -webkit-transition: all 0.3s ease-in;
  -moz-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}
.tags-li:not(.active):hover {
  background: #f8f8f8;
}
.tags-li.active {
  color: #fff;
}
.tags-li-title {
  float: left;
  max-width: 80px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 5px;
  color: #666;
}
.tags-li.active .tags-li-title {
  color: #fff;
}
.tags-close-box {
  position: absolute;
  right: 0;
  top: 0;
  box-sizing: border-box;
  padding-top: 1px;
  text-align: center;
  width: 110px;
  height: 30px;
  background: #fff;
  box-shadow: -3px 0 15px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}
</style>
