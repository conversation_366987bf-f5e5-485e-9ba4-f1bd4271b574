<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="sensiTag"
        class="demo-form-inline"
        ref="sensiTag"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="sensiTag.userName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="queryForm()">查询</el-button>
          <el-button type="primary" plain @click="resetForm('sensiTag')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div class="sensitive-table">
        <!-- <el-table
          :data="tableTreeDdata"
          border
          size="default"
          style="width: 100%"
          v-loading="loading"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="handleAdd">新增账号</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="PhoneWhiteList"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="loading"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableTreeDdata">

          <!-- <table-tree-column prop="labelName"  treeKey="id"  label="标签名称"></table-tree-column> -->
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>{{ scope.row.userName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column
            field="备注"
            title="备注"
            width="150"
          >
            <template v-slot="scope">
              <div>{{ scope.row.remark }}</div>
            </template>
          </vxe-column>
          <vxe-column
            fixed="right"
            header-align="center"
            align="center"
            width="120"
            field="操作"
            title="操作"
          >
            <template v-slot="scope">
              <!-- <el-button  @click="handleEdit(scope.row)"  type="text"><i class="el-icon-edit"></i> 编辑 </el-button> -->
              <el-button
                link
                @click="handleDelete(scope.row)"
                style="color: #f56c6c"
                ><el-icon style="margin-right: 5px;"><CircleClose /></el-icon>
                删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensiTagObj.currentPage"
            :page-size="sensiTagObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableTotal"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增修改界面 -->
      <el-dialog
        :title="title"
        width="520px"
        v-model="dialogVisible"
        :close-on-click-modal="false"
      >
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          @keyup.enter="submitForm()"
          label-width="100px"
          style="padding: 0 20px"
          class="addMenu"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="dataForm.userName"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item  label="id地址" prop="ipAddress">
                            <el-input v-model="dataForm.ipAddress" placeholder="请输入ip地址"></el-input>
                        </el-form-item> -->
          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="dataForm.remark"
              placeholder="请输入备注"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm()">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import TableTreeColumn from '@/components/page/SystemSettings/TableTreeColumn.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import moment from 'moment'
export default {
  components: {
    TableTreeColumn,
    TableTem
  },
  name: 'SensitiveWordsTag',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      title: '新增账号',
      sensiTag: {
        //查询框的值
        userName: '',
        currentPage: 1,
        pageSize: 10,
      },
      sensiTagObj: {
        //储存查询框的值
        userName: '',
        currentPage: 1,
        pageSize: 10,
      },
      dialogVisible: false,
      dataForm: {
        userName: '',
        ipAddress: '',
        remark: '',
      },
      dataRule: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        ipAddress: [
          { required: true, message: 'ip地址不能为空', trigger: 'blur' },
        ],
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }],
      },
      props: {
        value: 'id',
        label: 'labelName',
        children: 'children',
      },
      addMenu: [
        {
          id: 0,
          labelName: '敏感词标签',
        },
      ],
      tableTreeDdata: [],
      tableTotal: 0,
      loading: false,
    }
  },
  methods: {
    //-------------   列表信息   --------------
    // 获取列表数据
    findTreeDatas() {
      Object.assign(this.sensiTag, this.sensiTagObj)
      window.api.post(
        window.path.upms + 'sysuserwhitelist/page',
        this.sensiTagObj,
        (res) => {
          this.tableTreeDdata = res.data.records
          this.tableTotal = res.data.total
        }
      )
    },
    handleAdd() {
      this.dialogVisible = true
    },
    // 表单提交
    submitForm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.upms + 'sysuserwhitelist',
            this.dataForm,
            (res) => {
              // this.tableTreeDdata =res.data.records;
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.dialogVisible = false
                this.findTreeDatas()
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        }
      })
    },

    // 删除
    handleDelete(val) {
      console.log(val)
      this.$confirms.confirmation(
        'delete',
        '确认删除',
        window.path.upms + 'sysuserwhitelist/' + val.id,
        {},
        (res) => {
          this.findTreeDatas()
          this.dialogVisible = false
        }
      )
    },
    // //弹框父级菜单下拉框数据
    // findTreeData() {
    //     window.api.post(window.path.upms + 'menu/allTree',{
    //         platform:this.activeName2,
    //         flag:1
    //     },res=>{
    //         this.addMenu =[]; //置空
    //         let parent = {
    //             id: -1,
    //             name: "顶级菜单"
    //         };
    //         let datas = res;

    //         for(let i = 0; i<datas.length;i++){
    //             if(datas[i].children.length == 0){
    //                 delete datas[i].children
    //             }else{
    //                 for(let j = 0; j<datas[i].children.length;j++){
    //                     if(datas[i].children[j].children.length == 0){
    //                         delete datas[i].children[j].children
    //                     }else{
    //                         for(let k = 0; k<datas[i].children[j].children.length;k++){
    //                             if(datas[i].children[j].children[k].children.length == 0){
    //                                 delete datas[i].children[j].children[k].children
    //                             }else{
    //                                 for(let Q = 0; Q<datas[i].children[j].children[k].children.length;Q++){
    //                                     if(datas[i].children[j].children[k].children[Q].children.length == 0){
    //                                          delete datas[i].children[j].children[k].children[Q].children
    //                                     }else{
    //                                         for(let y = 0; y<datas[i].children[j].children[k].children[Q].children.length;y++){
    //                                             if(datas[i].children[j].children[k].children[Q].children[y].children.length == 0){
    //                                                 delete datas[i].children[j].children[k].children[Q].children[y].children
    //                                             }
    //                                         }
    //                                     }
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //         parent.children = datas;
    //         if(parent.children.length == 0){
    //             delete parent.children
    //         }
    //         this.addMenu =[parent];
    //     })
    // },
    //获取列表数据
    // getTableDtate(){
    //     this.tableDataObj.loading2 = true;
    //     window.api.post(window.path.omcs+'operatingblackwordslabel/page',this.sensiTagObj,res=>{
    //         this.tableDataObj.tableData = res.records;
    //         this.tableDataObj.tablecurrent.totalRow = res.total;
    //         this.tableDataObj.loading2 = false;
    //     })
    // },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.sensiTagObj.pageSize = size
      this.findTreeDatas()
    },
    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.sensiTagObj.currentPage = currentPage
      this.findTreeDatas()
    },
    //查询
    queryForm() {
      Object.assign(this.sensiTagObj, this.sensiTag)
      //    this.getTableDtate();
      this.findTreeDatas()
    },
    //重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.sensiTag.beginTime = ''
      this.sensiTag.endTime = ''
      Object.assign(this.sensiTagObj, this.sensiTag)
      this.findTreeDatas()
    },
    //获取查询时间的开始时间和结束时间
    hande: function (val) {
      if (val) {
        this.sensiTag.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensiTag.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensiTag.beginTime = ''
        this.sensiTag.endTime = ''
      }
    },
    //-------------   列表信息   --------------
    //列表复选框的值
    // handelSelection(val){
    //     let selectId = [];
    //     let wordsCount = [];
    //     for(let i=0;i<val.length;i++){
    //         selectId.push(val[i].wordsLabelId);
    //         wordsCount.push(val[i].wordsCount);
    //     }
    //     this.selectId = selectId.join(','); //批量操作选中id
    //     this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    // },
    // //操作按钮
    // handelOptionButton: function(val){
    //     this.tagLibrary.formData.wordsLabelId = val.row.wordsLabelId;  //获得所选行的id
    //     if(val.methods=='delet'){  //删除
    //         this.$confirms.confirmation('delete','此操作将永久删除该条数据, 是否继续?',window.path.omcs+'operatingblackwordslabel/'+val.row.wordsLabelId,{},res =>{
    //             // this.getTableDtate();
    //         })
    //     }else if (val.methods == 'modify'){ //编辑
    //         this.tagLibrary.title="编辑敏感词标签";
    //         this.setsLabelDialog = true;//弹框显示
    //         this.$nextTick(()=>{
    //             this.$refs.tagLibrary.resetFields();//清空表单
    //             Object.assign(this.tagLibrary.formData,val.row); //编辑赋值
    //         })
    //         this.tagLibrary.labelName = val.row.labelName;
    //     }
    // },
    // //新增
    // addTags(){
    //     this.tagLibrary.title = "设置敏感词标签";
    //     this.setsLabelDialog = true;
    // },
    //  //-------------   编辑和新增 标签功能开始   --------------
    // //弹框提交按钮
    // tag_ok_add(formName){
    //     //表单数据验证
    //     this.$refs[formName].validate((valid) => {
    //         if (valid) { //新增
    //             let tagLibraryDate =this.tagLibrary.formData;
    //             if(this.tagLibrary.title == "设置敏感词标签"){
    //                 tagLibraryDate.wordsLabelId='';
    //                 window.api.get(window.path.omcs+'operatingblackwordslabel/exists/'+this.tagLibrary.formData.labelName,{},res=>{
    //                     if(res.code == 200 && res.data == '0'){
    //                         this.$confirms.confirmation('post','确定新增?',window.path.omcs+'operatingblackwordslabel',tagLibraryDate,res =>{
    //                             // this.getTableDtate();
    //                             this.setsLabelDialog=false;//隐藏弹窗
    //                         })
    //                     }else{
    //                         this.$message({
    //                             message: '敏感词标签已存在，切勿重复添加！',
    //                             type: 'warning'
    //                         });
    //                     }

    //                 })
    //             }else{ //编辑
    //                 if( this.tagLibrary.formData.labelName ==  this.tagLibrary.labelName){
    //                     this.$confirms.confirmation('put','确定修改?',window.path.omcs+'operatingblackwordslabel',tagLibraryDate,res =>{
    //                         // this.getTableDtate();
    //                         this.setsLabelDialog=false;//隐藏弹窗
    //                     })
    //                 }else{
    //                     window.api.get(window.path.omcs+'operatingblackwordslabel/exists/'+this.tagLibrary.formData.labelName,{},res=>{
    //                         if(res.code == 200 && res.data == '0'){
    //                             this.$confirms.confirmation('put','确定修改?',window.path.omcs+'operatingblackwordslabel',this.tagLibrary.formData,res =>{
    //                                 // this.getTableDtate();
    //                                 this.setsLabelDialog=false;//隐藏弹窗
    //                             })
    //                         }else{
    //                             this.$message({
    //                                 message: '敏感词标签已存在，切勿重复！',
    //                                 type: 'warning'
    //                             });
    //                         }

    //                     })
    //                 }
    //             }
    //         } else {
    //             console.log('error submit!!');
    //             return false;
    //         }
    //     });
    // },
    //-------------  编辑和新增 标签功能结束  --------------
    //导出
    // exportSensit(){
    //     let aa = Object.assign({},this.sensiTagObj);
    //     aa.isDown = 1;
    //     if(this.tableDataObj.tableData.length == 0){
    //         this.$message({
    //             message: '列表无数据，不可导出！',
    //             type: 'warning'
    //         });
    //     }else{
    //         this.$File.export(window.path.omcs+'operatingblackwordslabel/page',aa,'敏感词标签报表.xlsx');
    //     }
    // },
    //批量删除功能
    // tagBatchDelet(){
    //     let flag = true;
    //     for(let i = 0;i<this.wordsCount.length;i++){
    //         if( this.wordsCount[i] != '0'){
    //             flag = false;
    //             break;
    //         }
    //     }
    //     if(flag){
    //         this.$confirms.confirmation('delete','此操作将永久删除该数据, 是否继续?',window.path.omcs+'operatingblackwordslabel/deleteBatchIds/'+this.selectId,{},res =>{
    //             // this.getTableDtate();
    //         })
    //     }else{
    //         this.$message({
    //             message: '选项中有不能删除项，需重新选择！',
    //             type: 'warning'
    //         });
    //     }
    // }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.findTreeDatas()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.findTreeDatas()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听查询框对象的变化
    // sensiTagObj:{
    //     handler(){
    //         this.findTreeDatas();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听弹框是否关闭
    dialogVisible(val) {
      if (val == false) {
        this.dataForm.remark = ''
        this.dataForm.userName = ''
        this.dataForm.ipAddress = ''
      }
    },
  },
  emits: ['update:value'],
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>