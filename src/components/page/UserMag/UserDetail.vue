<template>
  <div class="container_left">
    <div class="Top_title" style="display: flex;">
      <div
        style="
          display: flex;
          padding-right: 10px;
          cursor: pointer;
          align-items: center;
          color: #16a589;
        "
        @click="goBack()"
      >
        <el-icon><ArrowLeft /></el-icon>返回</div
      >|
      <span style="margin-left: 10px;">用户详情</span>
    </div>
    <div class="OuterFrame fillet"  style="padding-bottom: 60px;">
      <el-tabs
        v-model="activeName"
        type="card"
        class="demo-tabs"
        @tab-click="handleClick"
      >
        <el-tab-pane label="账户信息" name="accountInfo">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'accountInfo'"
              v-bind:is="accountInfo"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="登录手机号管理" name="loginPhone">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'loginPhone'"
              v-bind:is="loginPhone"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="接口IP管理" name="ipMang">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'ipMang'"
              v-bind:is="ipMang"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="动态IP管理" name="commonIpMang">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'commonIpMang'"
              v-bind:is="commonIpMang"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="其他设置" name="settings">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'settings'"
              v-bind:is="settings"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="敏感词设置" name="SensitiveSettings">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'SensitiveSettings'"
              v-bind:is="SensitiveSettings"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="产品设置" name="ProductSettings">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'ProductSettings'"
              v-bind:is="ProductSettings"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="标签管理" name="tagMang">
          <keep-alive>
            <component
              v-if="currentTabComponent === 'tagMang'"
              v-bind:is="tagMang"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <!-- <el-tab-pane label="Config" name="second">Config</el-tab-pane>
        <el-tab-pane label="Role" name="third">Role</el-tab-pane>
        <el-tab-pane label="Task" name="fourth">Task</el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
// import TableTem from "@/components/publicComponents/TableTem";
// 引入时间戳转换
// import {formatDate} from '@/assets/js/date.js'
// import accountInfo from "./components/accountInfo.vue";
// // import material from './components/material.vue'
// import settings from "./components/settings.vue";
// import SensitiveSettings from "./components/SensitiveSettings.vue";
// import ProductSettings from "./components/ProductSettings.vue";
// import Similarity from "./components/Similarity.vue";
// import tagMang from "./components/tagManagement.vue";
// import whiteList from "./components/whiteList.vue";
// import loginPhone from "./settingPhone.vue";
import { ref } from "vue";
import { useRouter } from 'vue-router';
const $router = useRouter();
const accountInfo = defineAsyncComponent(() => import("./components/accountInfo.vue"));
const settings = defineAsyncComponent(() => import("./components/settings.vue"));
const SensitiveSettings = defineAsyncComponent(() => import("./components/SensitiveSettings.vue"));
const loginPhone = defineAsyncComponent(() => import("./settingPhone.vue"));
const ProductSettings = defineAsyncComponent(() => import("./components/ProductSettings.vue"));
const tagMang = defineAsyncComponent(() => import("./components/tagManagement.vue"));
const ipMang = defineAsyncComponent(() => import("./components/ipManagement.vue"));
const commonIpMang = defineAsyncComponent(() => import("./components/commonIpManagement.vue"));
const activeName = ref("accountInfo");
const currentTabComponent = ref("accountInfo");
const goBack = () => {
  $router.push('/UserMag'); 
};
const handleClick = (tab, event) => {
  currentTabComponent.value = tab.props.name;
};
</script>

<style scoped>
.OuterFrame {
    padding: 20px;
}
</style>