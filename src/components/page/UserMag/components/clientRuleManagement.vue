<template>
  <div style="background: #fff">
    <div class="OuterFrame fillet">
      <el-tabs v-model="formInline.type" type="card" @tab-click="handleClick">
        <el-tab-pane label="全局配置" name="0"> </el-tab-pane>
        <el-tab-pane label="区域配置" name="1"> </el-tab-pane>
      </el-tabs>
      <!-- <div>
          <el-form
            :inline="true"
            :model="formInline"
            class="demo-form-inline"
            ref="formInline"
          >
            <el-form-item label="用户名称" prop="username">
                <el-input v-model="formInline.username" placeholder></el-input>
              </el-form-item>
            <el-form-item label="产品类型" prop="productId">
              <el-select v-model="formInline.productId" placeholder="请选择">
                <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <div>
              <el-button type="primary" plain style @click="Query"
                >查询</el-button
              >
              <el-button type="primary" plain style @click="Reload('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div> -->
      <div class="Signature-search-fun" style="margin: 10px 0">
        <el-button type="primary" @click="addRule">添加配置</el-button>
        <el-button :disabled="!ids.length" type="danger" @click="deleteAll">批量删除</el-button>
      </div>
      <div class="Mail-table">
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <!-- <el-table-column type="expand">
            <template #default="scope">
              <div v-if="scope.row.rules" style="padding: 10px">
                <el-table
                  :data="scope.row.rules"
                  border
                  :stripe="true"
                  style="width: 100%"
                >
                  <el-table-column prop="ruleName" label="规则名称">
                  </el-table-column>
                  <el-table-column label="标签">
                    <template #default="scope">
                      <div v-if="scope.row.labelName">
                        <el-tag
:disable-transitions="true"
                          style="margin-left: 5px"
                          v-for="(label, i) in scope.row.labelName.split(',')"
                          :key="i + 'b'"
                          >{{ label }}</el-tag
                        >
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间">
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="账号名称">
            <template #default="scope">
              <span>{{ scope.row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="短信类型">
            <template #default="scope">
              <span v-if="scope.row.smsType == '1'">验证码</span>
              <span v-if="scope.row.smsType == '2'">通知</span>
              <span v-if="scope.row.smsType == '3'">营销推广</span>
            </template>
          </el-table-column>
          <el-table-column label="运营商">
            <template #default="scope">
              <div
                v-if="scope.row.operator == 1"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yidong"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div
                v-else-if="scope.row.operator == 2"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-liantong"
                  style="font-size: 16px; color: #f56c6c"
                ></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div
                v-else-if="scope.row.operator == 3"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-dianxin"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">电信</span>
              </div>
              <div
                v-else-if="scope.row.operator == 4"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yunyingshang"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">全网</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="省份">
            <template #default="scope">
              <span>{{ scope.row.province || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="规则名称">
            <template #default="scope">
              <span>{{ scope.row.ruleName || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template #default="scope">
              <el-tag
:disable-transitions="true"
                style="cursor: pointer"
                v-if="scope.row.status == '0'"
                effect="dark"
                type="primary"
                @click="changeStatus(scope.row, 1)"
              >
                启用
              </el-tag>
              <el-tag
:disable-transitions="true"
                style="cursor: pointer"
                v-if="scope.row.status == '1'"
                effect="dark"
                type="danger"
                @click="changeStatus(scope.row, 0)"
              >
                停用
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建人">
            <template #default="scope">
              <span>{{ scope.row.createName || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180">
            <template #default="scope">
              <span v-if="scope.row.createTime">
                {{ scope.row.createTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                link
                style="color: #f56c6c"
                @click="deleteRule(scope.$index, scope.row)"
                ><el-icon><Delete /></el-icon>删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div style="float: right; margin-top: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

        <!-- 表格和分页结束 -->
        <el-dialog
          title="添加配置"
          v-model="dialogFormVisible"
          width="500px"
          :close-on-click-modal="false"
        >
          <el-form
            ref="formChannel"
            label-width="110px"
            :model="settingForm"
            :rules="rules"
          >
            <el-form-item label="配置类型" prop="type">
              <el-radio-group v-remove-hidden v-model="settingForm.type">
                <el-radio label="全局配置" value="1"></el-radio>
                <el-radio label="区域配置" value="2"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="短信类型" prop="smsType">
              <el-radio-group v-remove-hidden v-model="settingForm.smsType">
                <el-radio label="验证码" value="1"></el-radio>
                <el-radio label="通知" value="2"></el-radio>
                <el-radio label="营销推广" value="3"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="settingForm.type == '2'"
              label="运营商"
              prop="operator"
            >
              <el-radio-group v-remove-hidden v-model="settingForm.operator">
                <el-radio label="移动" value="1"></el-radio>
                <el-radio label="联通" value="2"></el-radio>
                <el-radio label="电信" value="3"></el-radio>
                <el-radio label="全网" value="4"></el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="settingForm.type == '2'"
              label="省份"
              prop="provinces"
            >
              <el-select
                v-model="settingForm.provinces"
                filterable
                multiple
                clearable
                placeholder="请选择"
                class="input-w"
              >
                <el-option
                  v-for="(item, index) in ProvincesCities"
                  :key="index"
                  :label="item.provincial"
                  :value="item.provincial"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="规则" prop="rules">
              <el-select
                v-model="settingForm.rules"
                placeholder="请选择"
                filterable
                multiple
                clearable
                class="input-w"
              >
                <el-option
                  v-for="(item, index) in blackRuleList"
                  :label="item.ruleName"
                  :value="item.id"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <template #footer>
            <el-button type="primary" @click="submitForm(formChannel)"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </template>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { reactive, ref } from "vue";

const props = defineProps({
  dataObj: {
    type: Object,
    default: () => ({
      username: "",
      productId: "",
    }),
  },
});
const dialogFormVisible = ref(false);
const formChannel = ref(null);
const activeName = ref("0");
const formInline = reactive({
  username: "",
  type: "0",
  productId: "",
  currentPage: 1,
  pageSize: 10,
});
const tableDataObj = reactive({
  total: 0,
  loading2: false,
  tableData: [],
});
const settingForm = reactive({
  type: "1",
  operator: "1",
  smsType: "1",
  rules: [],
  provinces: [],
});
const rules = reactive({
  type: [{ required: true, message: "请选择配置类型", trigger: "change" }],
  operator: [{ required: true, message: "请选择运营商", trigger: "change" }],
  smsType: [{ required: true, message: "请选择短信类型", trigger: "change" }],
  rules: [{ required: true, message: "请选择规则", trigger: "change" }],
  provinces: [{ required: true, message: "请选择省份", trigger: "change" }],
});
const blackRuleList = ref([]);
const ProvincesCities = ref([]);
const ids = ref([]);
const gettableLIst = () => {
  tableDataObj.loading2 = true;
  formInline.username = props.dataObj.username;
  formInline.productId = props.dataObj.productId;
  window.api.post(
    window.path.omcs + "operatingaccountrule/page",
    formInline,
    (res) => {
      if (res.code == 200) {
        tableDataObj.total = res.data.total;
        tableDataObj.tableData = res.data.records;
        tableDataObj.loading2 = false;
      } else {
        ElMessage({
          message: res.msg,
          type: "error",
        });
      }
    }
  );
};
const handleSizeChange = (size) => {
  formInline.pageSize = size;
  gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  formInline.currentPage = currentPage;
  gettableLIst();
};
const Query = () => {
  gettableLIst();
};
const Reload = (val) => {
  formInline.username = "";
  formInline.productId = "";
  formInline.currentPage = 1;
  formInline.pageSize = 10;
  gettableLIst();
};
const handleClick = (tab, event) => {
  formInline.type = tab.index;
  gettableLIst();
};
const getProvincial = () => {
  window.api.get(
    window.path.omcs + "operatingchannelprovincial/list",
    {},
    (res) => {
      if (res.code == 200) {
        ProvincesCities.value = res.data;
      }
    }
  );
};
const getBlackRuleList = () => {
  window.api.get(window.path.omcs + "operatingaccountrule/rules", {}, (res) => {
    if (res.code == 200) {
      blackRuleList.value = res.data;
    } else {
      ElMessage({
        message: res.msg,
        type: "error",
      });
    }
  });
};
const addRule = () => {
  dialogFormVisible.value = true;
  getBlackRuleList();
};
const submitForm = async (formName) => {
  if (!formName) return;
  await formName.validate((valid, fields) => {
    if (valid) {
      let data = {
        productId: props.dataObj.productId,
        username: props.dataObj.username,
        operators: settingForm.type == "2" ? [settingForm.operator] : ["4"],
        smsType: settingForm.smsType,
        rules: settingForm.rules,
        provinces: settingForm.type == "2" ? settingForm.provinces : ["其他"],
      };
      ElMessageBox.confirm("此操作将添加此配置, 是否继续?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          window.api.post(
            window.path.omcs + "operatingaccountrule/add",
            data,
            (res) => {
              if (res.code == 200) {
                ElMessage({
                  type: "success",
                  message: "操作成功",
                });
                dialogFormVisible.value = false;
                gettableLIst();
              } else {
                ElMessage({
                  message: res.msg,
                  type: "error",
                });
              }
            }
          );
        })
        .catch(() => {
          ElMessage({
            type: "info",
            message: "操作取消",
          });
        });
      // this.$confirms.confirmation(
      //   "post",
      //   "此操作将添加此配置, 是否继续?",
      //   window.path.omcs + "operatingaccountrule/add",
      //   data,
      //   (res) => {
      //     if (res.code == 200) {
      //       dialogFormVisible.value = false;
      //       gettableLIst();
      //     }
      //   }
      // );
    } else {
      // console.log("error submit!!");
      // console.info('error submit!!')
      console.warn("error submit!!", fields);
      // console.error("error submit!!");
      return false;
    }
  });
};
const handleSelectionChange = (val) => {
  console.log(val,'val');
  ids.value = val.map((item) => item.id);
}
const deleteAll = () =>{
  ElMessageBox.confirm("此操作将删除所有选中的配置, 是否继续?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      window.api.post(
        window.path.omcs + "operatingaccountrule/delete",
        {
          username: props.dataObj.username,
          ids: ids.value.join(","),
        },
        (res) => {
          if (res.code == 200) {
            ElMessage({ type: "success", message: "操作成功" });          
            gettableLIst();
          } else {
            ElMessage({
              message: res.msg,
              type: "error",
            });
          }
        }
      );
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作取消",
      });
    });
}
const deleteRule = (index, row) => {
  ElMessageBox.confirm("此操作将删除该配置, 是否继续?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      window.api.post(
        window.path.omcs + "operatingaccountrule/delete",
        {
          username: props.dataObj.username,
          ids: row.id,
        },
        (res) => {
          if (res.code == 200) {
            ElMessage({
              type: "success",
              message: "操作成功",
            });
            dialogFormVisible.value = false;
            gettableLIst();
          } else {
            ElMessage({
              message: res.msg,
              type: "error",
            });
          }
        }
      );
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作取消",
      });
    });
  // this.$confirms.confirmation(
  //   "post",
  //   "此操作将删除该配置, 是否继续?",
  //   window.path.omcs + "operatingaccountrule/delete",
  //   {
  //     username: props.dataObj.username,
  //     ids: row.id,
  //   },
  //   (res) => {
  //     if (res.code == 200) {
  //       dialogFormVisible.value = false;
  //       gettableLIst();
  //     } else {
  //       ElMessage({
  //         message: res.msg,
  //         type: "error",
  //       });
  //     }
  //   }
  // );
};

const changeStatus = (row, status) => {
  this.$confirms.confirmation(
    "post",
    "此操作将" + (status == 1 ? "停用" : "启用") + "该配置, 是否继续?",
    window.path.omcs + "operatingaccountrule/update",
    {
      username: props.dataObj.username,
      id: row.id,
      status,
    },
    (res) => {
      if (res.code == 200) {
        gettableLIst();
      }
    }
  );
};

onMounted(() => {
  gettableLIst();
  getProvincial();
});

watch(
  () => dialogFormVisible.value,
  (newV) => {
    if (!newV) {
      formChannel.value.resetFields();
    }
  }
);
</script>
<style scoped>
.OuterFrame {
  /* padding: 20px; */
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  /* padding-bottom: 40px; */
}

.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.input-w {
  width: 300px;
}
.rule-box {
  width: 200px;
  /* height: 200px; */
  border: 1px solid #eee;
  background: #fff;
  padding: 10px;
  font-size: 14px;
  margin-right: 10px;
  text-align: center;
  box-shadow: -3px 0 15px 3px rgba(0, 0, 0, 0.1);
}
.label-box {
  max-height: 150px;
  overflow: auto;
}
</style>
    
    