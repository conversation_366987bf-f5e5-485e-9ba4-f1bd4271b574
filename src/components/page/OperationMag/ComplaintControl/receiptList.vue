<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" label-width="82px" prop="signature">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="82px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="boderbottom">
            <el-button type="primary" plain style @click="Query">查询</el-button>
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
            <el-button type="primary" plain style @click="download">导出数据</el-button>
          </div> -->
      <div class="sensitive">
        <!-- <span class="Signature-list-header">签名扩展号列表</span> -->
        <!-- <el-button
              type="primary"
              style="margin-left: 15px"
              @click="SignafileDialog = true"
              >签名扩展导入</el-button
            > -->
        <!-- <el-button
              type="primary"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delAll"
              >批量删除</el-button
            > -->
        <!-- <el-button
              type="primary"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delStatus(0)"
              >批量启用</el-button
            >
            <el-button
              type="warning"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delStatus(1)"
              >批量停用</el-button
            >
            <el-button
              type="primary"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="Batchtest()"
              >批量测试</el-button
            > -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->

        <!-- <table-tem
              :tableDataObj="tableDataObj"
              @handelOptionButton="handelOptionButton"
              @handelSelection="handelSelection"
              @save="save"
            ></table-tem> -->
        <!--分页-->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">创建</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="receiptList"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称">
            <template v-slot="scope">
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="百分比" title="百分比">
            <template v-slot="scope">
              <div>{{ scope.row.percent + '%' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <div v-if="scope.row.status == 0" style="color: #409eff"
                >启用</div
              >
              <div v-else style="color: red">停用</div>
            </template>
          </vxe-column>
          <vxe-column field="最晚回执时间（小时）" title="最晚回执时间（小时）">
            <template v-slot="scope">
              <div>{{ scope.row.endHour }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="200" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="color: orange; margin-left: 0px"
                v-if="scope.row.status == 0"
                @click="delState(scope.$index, scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;停用</el-button
              >
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                v-else
                @click="delStateq(scope.$index, scope.row)"
                ><el-icon><SuccessFilled /></el-icon>&nbsp;启用</el-button
              >
              <el-button
                link
                style="margin-left: 5px; color: #409eff;"
                @click="detailsRow(scope.$index, scope.row)"
                ><el-icon><EditPen /></el-icon>&nbsp;编辑</el-button
              >
              <el-button
                link
                style="margin-left: 5px; color: red"
                @click="delRow(scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 签名扩展导入 -->
      <el-dialog
        title="签名扩展导入"
        v-model="SignafileDialog"
        :close-on-click-modal="false"
        width="520px"
      >
        <file-upload
          style="display: inline-block"
          :action="actionUrl"
          :limit="1"
          :showfileList="true"
          :fileStyle="fileStyle"
          :del="del1"
          :istip="false"
          :tip="tip"
          @fileup="fileup"
          @fileupres="fileupres"
          @onProgress="onProgress"
          >选择上传文件</file-upload
        >
        <div v-if="Progressflag" style="text-align: center; font-size: 35px">
          <el-icon><Loading /></el-icon>
        </div>
        <!-- <div style="margin-top: 10px" v-if="failNum != 0">
              <span style="font-size: 16px"> 失败数：</span
              ><span>{{ failNum }}</span>
            </div>
            <div
              style="margin-top: 10px; word-wrap: break-word"
              v-if="failNum != 0"
            >
              <span style="font-size: 16px">失败行数：</span
              ><span>{{ failRow }}</span>
            </div> -->
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button @click="SignafileDialog = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增签名 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
          label-width="120px" 
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              class="input-w"
              v-model="formop.username"
              placeholder="用户名"
              :disabled="flagPut"
            ></el-input>
          </el-form-item>
          <el-form-item label="百分比" prop="percent">
            <el-input-number
              class="input-w"
              v-model="formop.percent"
              :min="0"
              :max="100"
              label="百分比"
            ></el-input-number>
            <!-- <el-input
                  v-model="formop.percent"
                  @change="changeNum"
                  placeholder="百分比"
                  style="width: 100%"
                ></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="回执开始小时" prop="beginHour">
            <el-input-number
              class="input-w"
              v-model="formop.beginHour"
              :min="2"
              :max="72"
              label=""
            ></el-input-number>
          </el-form-item> -->
          <el-form-item label="最晚回执时间" prop="endHour">
            <el-input-number
              class="input-w"
              v-model="formop.endHour"
              :min="6"
              :max="72"
              label=""
            ></el-input-number>
            <span>（小时）</span>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增标签商结束 -->

      <!-- 批量测试 -->
      <el-dialog
        title="批量测试"
        v-model="dialogBatchTest"
        :close-on-click-modal="false"
        style="padding: 0 28px 0 20px"
        width="520px"
      >
        <el-form
          :model="formoTest"
          :rules="rulesTest"
          ref="formoTest"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="手机号" label-width="80px" prop="mobile">
            <el-input v-model="formoTest.mobile" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="通道号" label-width="80px" prop="channelId">
            <el-input
              v-model="formoTest.channelId"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitBatchtest()"
              >提 交</el-button
            >
            <el-button @click="dialogBatchTest = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 批量测试 -->
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import TableTem from '../../../publicComponents/TableTem.vue'
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import moment from 'moment'
export default {
  components: {
    TableTem,
    FileUpload,
    ChannelView
  },
  name: 'SignatureNumMag',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      actionUrl: window.path.omcs + 'operatingsignature/upload',
      isFirstEnter: false,
      ChannelData: '', //传递通道值
      titleMap: {
        add: '添加',
        edit: '编辑',
      },
      time1: '',
      signatureNum: '',
      status: 0,
      etxNum: '',
      flagPut: '', //用户名禁止状态 签名禁止状态
      dialogStatus: '', //新增编辑标题
      singName: [], //接受标签列表
      dialogFormVisible: false, //新增弹出框显示隐藏
      SignafileDialog: false,
      dialogBatchTest: false, //批量测试
      //查询表单
      formInline: {
        username: '',
        percent: 0,
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        percent: 0,
        currentPage: 1,
        pageSize: 10,
      },
      //批量测试
      formoTest: {
        channelId: '',
        mobile: '',
      },
      rulesTest: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确在手机号',
          },
        ],
        channelId: [
          { required: true, message: '请输入通道号', trigger: 'change' },
          {
            pattern: /^([0-9]*[1-9][0-9]*)(,([0-9]*[1-9][0-9]*))*/,
            message: '含有非法字符（只能输入数字或逗号！）',
          },
        ],
      },
      //添加列表
      formop: {
        username: '',
        percent: 0,
        endHour:undefined
      },
      id: '', //列表行id
      selectId: '', //批量操作选中id
      tableRow: '', //当前行列表数据
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'change' },
        ],
        percent: [
          { required: true, message: '请输入百分比', trigger: 'change' },
        ],
        channel: [
          { required: false, message: '请输入通道号', trigger: 'change' },
          {
            pattern: /^([0-9]*[1-9][0-9]*)(,([0-9]*[1-9][0-9]*))*/,
            message: '含有非法字符（只能输入数字或逗号！）',
          },
        ],
        ext: [
          { required: true, message: '请输入扩展号', trigger: 'change' },
          { min: 1, max: 12, message: '长度在 1 到 12位', trigger: 'change' },
          { pattern: /^[0-9]+$/, message: '含有非法字符（只能输入数字！）' },
        ],
      },
      formpEdit: {},
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],

        // tableLabel: [
        //   {
        //     prop: "signature",
        //     showName: "签名",
        //     fixed: false,

        //   },
        //   {
        //     prop: "ext",
        //     showName: "扩展号",
        //     fixed: false
        //   },
        //   {
        //     prop: "channel",
        //     showName: "报备通道",
        //     Channel:true,
        //     fixed: false
        //   },
        //   {
        //     prop: "status",
        //     showName: "状态",
        //     fixed: false,
        //     width:"50px",
        //     formatData: function(val) {
        //       return val == "0" ? "启用" : "停用";
        //     },
        //     showCondition: {
        //       condition: "1"
        //     }
        //   },
        //   // {
        //   //   prop: "conCompName",
        //   //   showName: "公司名称",
        //   //   fixed: false
        //   // },
        //   {
        //     prop: "createName",
        //     showName: "创建人",
        //     fixed: false
        //   },
        //   {
        //     prop: "createTime",
        //     showName: "创建时间",
        //     fixed: false,
        //     width:"140px"
        //   }
        // ],
        // tableStyle: {
        //   isSelection: true, //是否复选框
        //   // height:250,//是否固定表头
        //   isExpand: false, //是否是折叠的
        //   style: {
        //     //表格样式,表格宽度
        //     width: "100%"
        //   },
        //   optionWidth: "140", //操作栏宽度
        //   border: true, //是否边框
        //   stripe: false //是否有条纹
        // },
        // conditionOption:[
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'1',//关联的表格属性-值
        //         optionName:'停用',//按钮的显示文字
        //         optionMethod:'stop',//按钮的方法
        //         icon:'el-icon-circle-close-outline',//按钮图标
        //         optionButtonColor:'orange',//按钮颜色
        //         otherOptionName:'启用',//其他条件的按钮显示文字
        //         otherOptionMethod:'start',//其他条件的按钮方法
        //         otherIcon:'el-icon-circle-check-outline',//其他条件按钮的图标
        //         optionOtherButtonColor:'#16A589'//其他条件按钮的颜色
        //     },{
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'1',//关联的表格属性-值
        //         optionName:'编辑',//按钮的显示文字
        //         optionMethod:'details',//按钮的方法
        //         icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'#16A589',//按钮颜色
        //     },{
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'2',//关联的表格属性-值
        //         optionName:'删除',//按钮的显示文字
        //         optionMethod:'dellog',//按钮的方法
        //         icon:'el-icon-delete',//按钮图标
        //         optionButtonColor:'#f56c6c',//按钮颜色
        //     }
        // ],
        // tableOptions: [ //操作栏不加状态

        // ]
      },
      Progressflag: false,
      failNum: '',
      failRow: '',
      fileStyle: {
        size: 245678943234,
        style: ['xlsx', 'xls'],
      },
      tip: '仅支持.xlsx .xls 等格式',
      del1: true, //关闭弹框时清空图片
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
    // console.log(moment,'lll');
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingclientdelivrdsetting/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },

    hande: function (val) {
      if (val) {
        //获取查询时间框的值
        this.formInline.createTime = moment(val[0]).format('YYYY-MM-DD ')
        // this.formInline.createEndTime = this.moment(val[1]).format("YYYY-MM-DD ")+"23:59:59";
      } else {
        this.formInline.createTime = ''
        // this.formInline.createEndTime = '';
      }
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    signaTure() {
      window.api.get(
        window.path.omcs + 'operatingsignature/sign/' + this.formop.signature,
        {},
        (res) => {
          this.signatureNum = res.data
        }
      )
    },
    exTNum() {
      window.api.get(
        window.path.omcs + 'operatingsignature/ext/' + this.formop.ext,
        {},
        (res) => {
          this.etxNum = res.data
        }
      )
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(row) {
      if (row.records.length > 0) {
        let arr = []
        row.records.forEach(item => {
          arr.push(item.id)
        })
        this.selectId = arr.join(",");
      } else {
        this.selectId = ""
      }
    },

    // 导出数据
    download() {
      let aa = Object.assign({}, this.tabelAlllist)
      aa.isDown = 1
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingSignature/download',
          aa,
          '签名拓展号报表.xlsx'
        )
      }
    },
    Reload() {
      //重置
      this.$refs.formInline.resetFields()
      //   this.formInline.createStartTime = "";
      //   this.formInline.createEndTime = "";
      //   this.time1 = "";
      this.formInline.username = ''
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    changeNum() {
      if (this.formop.percent > 100) {
        this.$message({
          message: '输入百分比不能>100',
          type: 'error',
        })
        this.formop.percent = 100
      }
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //添加
            window.api.post(
              window.path.omcs + 'operatingclientdelivrdsetting',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.gettableLIst()
                  this.dialogFormVisible = false
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error',
                  })
                }
              }
            )
          } else {
            //编辑
            window.api.put(
              window.path.omcs + 'operatingclientdelivrdsetting',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.gettableLIst()
                  this.dialogFormVisible = false
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error',
                  })
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
      this.flagPut = false
    },
    //移除文件
    fileup(val) {
      this.Progressflag = false
      this.failNum = ''
      this.failRow = ''
    },
    onProgress(val, val1, val2, val3) {
      this.Progressflag = true
    },
    //文件上传成功
    fileupres(val, val2) {
      console.log(val, 'aa')
      this.Progressflag = false
      if (val.code != 200) {
        this.$message({
          message: '签名导入失败！',
          type: 'warning',
        })
        // this.failNum = val.data.failNum;
        // this.failRow = val.data.failRow;
      } else {
        this.$message({
          message: '签名导入成功！',
          type: 'success',
        })
      }
    },
    delAll(val) {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingSignature',
        {
          ids: this.selectId,
        },
        () => {
          this.gettableLIst()
        }
      )
    },

    delStatus(val) {
      //批量启用停用
      if (val == 0) {
        //启用
        this.$confirms.confirmation(
          'post',
          '确认执行启用或停用操作吗？',
          window.path.omcs + 'operatingsignature/status',
          {
            ids: this.selectId,
            status: val,
          },
          () => {
            this.gettableLIst()
          }
        )
      } else if (val == 1) {
        this.$confirms.confirmation(
          'post',
          '确认执行启用或停用操作吗？',
          window.path.omcs + 'operatingsignature/status',
          {
            ids: this.selectId,
            status: val,
          },
          () => {
            this.gettableLIst()
          }
        )
      }
    },
    detailsRow(index, val) {
      console.log(val)
      //编辑
      this.dialogFormVisible = true
      this.flagPut = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.tableRow = val
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        // Object.assign(this.formop, val); //编辑框赋值
        this.formop.username = val.username
        this.formop.percent = val.percent
        this.formop.id = val.id
        this.formop.endHour = val.endHour
        // this.formpEdit.percent=this.formop.percent
        // this.formpEdit.id=val.id
      })
    },
    delRow(row) {
      console.log(row, 'row')
      this.$confirms.confirmation(
        'delete',
        '此操作不可逆？',
        window.path.omcs + 'operatingclientdelivrdsetting/' + row.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      //   console.log(val, index);

      this.$confirms.confirmation(
        'post',
        '确认执行启用吗？',
        window.path.omcs + 'operatingclientdelivrdsetting/disable/' + val.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    delStateq(index, val) {
      this.$confirms.confirmation(
        'post',
        '确认执行停用吗？',
        window.path.omcs + 'operatingclientdelivrdsetting/enable/' + val.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    // 批量测试
    Batchtest() {
      this.dialogBatchTest = true
    },
    submitBatchtest() {
      this.formoTest.ids = this.selectId
      this.$confirms.confirmation(
        'post',
        '请确认操作！',
        window.path.omcs + 'operatingsignature/test',
        this.formoTest,
        () => {
          this.gettableLIst()
          this.dialogBatchTest = false
        }
      )
    },
    // handelOptionButton: function (val) {
    //   console.log(val);
    //   if (val.methods == "details") {
    //     this.detailsRow(val);
    //   }
    //   if (val.methods == "start") {
    //     this.delState(val, 1);
    //   }
    //   if (val.methods == "stop") {
    //     this.delState(val, 2);
    //   }
    //   if (val.methods == "dellog") {
    //     this.delRow(val);
    //   }
    // },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep: true, //深度监听
    //   immediate: true,
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.formop.username = ''
        this.formpEdit.percent = 0
        this.formpEdit.id = ''
        this.$refs.formop.resetFields()
      }
    },
    'formop.userName'(val) {
      if (val == '') {
        this.formop.userName = ''
      }
    },
    SignafileDialog(val) {
      this.del1 = true
      if (val == false) {
        this.del1 = false
        this.failNum = ''
        this.failRow = ''
      }
    },
    dialogBatchTest(val) {
      if (val == false) {
        this.$refs.formoTest.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.sensitive {
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>