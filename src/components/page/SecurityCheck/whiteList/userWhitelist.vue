<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">新增</el-button>
            <el-button
              type="danger"
              @click="bathdel()"
              :disabled="selectId.length == 0"
              >批量删除</el-button
            >
            <el-button type="primary" style="margin-left: 15px" @click="updatefail"
              >白名单导入</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="userWhitelist"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称">
            <template v-slot="scope">
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号">
            <template #default="{ row, rowIndex}">
              <div v-if="pIndex == rowIndex">{{
                row.mobile || "-"
              }}</div>
              <div
                v-else
                style="cursor: pointer; color: #16a589"
                @click="phoneClickTable(rowIndex, row)"
                >{{ row.maskMobile || "-" }}</div
              >
              <!-- {{ scope.row.mobile }} -->
            </template>
          </vxe-column>
          <vxe-column field="等级" title="等级">
            <template v-slot="scope">
              <div v-if="scope.row.level == '1'">一级白名单</div>
              <div v-if="scope.row.level == '2'">二级白名单</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注">
            <template v-slot="scope">
              <div>{{ scope.row.remark }}</div>
            </template>
          </vxe-column>
          <vxe-column field="有效天数" title="有效天数">
            <template v-slot="scope">
              <div v-if="scope.row.days">{{ scope.row.days }}天</div>
            </template>
          </vxe-column>
          <vxe-column field="到期时间" title="到期时间" width="160px">
            <template v-slot="scope">
              <div v-if="scope.row.expireDate">{{
                moment(scope.row.expireDate).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="160px">
            <template v-slot="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div></template
            >
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180">
            <template v-slot="scope">
              <el-button link style="color: #409eff;" @click="edit(scope.row)">编 辑</el-button>
              <el-button
                link
                style="color: red"
                @click="cancel(scope.row)"
                >删 除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="80px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="formop.username"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formop.mobile"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="等级" prop="level">
            <!-- <el-input v-model="formop.mobile" autocomplete="off" class="input-w"></el-input> -->
            <el-radio-group v-model="formop.level">
              <el-radio value="1">一级白名单</el-radio>
              <el-radio value="2" style="margin-left: 10px"
                >二级白名单</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="有效期" prop="days">
            <el-input
              style="width: 150px"
              v-model="formop.days"
              placeholder="请输入有效期天数"
              class="input-w"
            ></el-input
            ><span style="margin-left: 10px">天</span>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formop.remark"
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 编辑 -->
      <el-dialog
        :title="titleMap"
        v-model="dialogEditVisible"
        :close-on-click-modal="false"
        width="500px"
      >
        <el-form
          :model="formopEdit"
          :rules="rulessEdit"
          label-width="80px"
          ref="formopEdit"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              :disabled="true"
              v-model="formopEdit.username"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              :disabled="true"
              v-model="formopEdit.mobile"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="等级" prop="level">
            <!-- <el-input v-model="formopEdit.mobile" autocomplete="off" class="input-w"></el-input> -->
            <el-radio-group v-model="formopEdit.level">
              <el-radio value="1">一级白名单</el-radio>
              <el-radio value="2" style="margin-left: 10px"
                >二级白名单</el-radio
              >
            </el-radio-group>
          </el-form-item>

          <el-form-item label="有效时长">
            <!-- <el-input v-model="formopEdit.days" placeholder='请输入有效期天数' class="input-w"></el-input> -->
            <el-date-picker
              disabled
              v-model="formopEdit.expireDate"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="有效期" prop="days">
            <el-input
              style="width: 150px"
              v-model="formopEdit.days"
              placeholder="请输入有效期天数"
              class="input-w"
            ></el-input
            ><span style="margin-left: 10px">天</span>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formopEdit.remark"
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formopEdit')"
              >提 交</el-button
            >
            <el-button @click="dialogEditVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 导入 -->
      <el-dialog
        title="白名单导入"
        v-model="dialogFormUpdate"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formupcode"
          :rules="rules"
          ref="formupcode"
          label-width="120px"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="formupcode.username"
              placeholder="请输入用户名称"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="有效期" prop="days">
            <el-input
              style="width: 150px"
              v-model="formupcode.days"
              placeholder="请输入有效期天数"
              class="input-w"
            ></el-input
            ><span style="margin-left: 10px">天</span>
          </el-form-item>
          <el-form-item label="上传文件">
            <el-upload
              v-bind:data="{
                days: formupcode.days,
                username: formupcode.username,
              }"
              class="upload-demo"
              ref="uploading"
              :action="actionUrl"
              :on-remove="handleRemove"
              multiple
              :limit="1"
              :headers="myHeader"
              :auto-upload="false"
              :on-success="handleAvatarSuccess"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <a
                style="color: #409eff; margin-left: 10px"
                href="https://doc.zthysms.com/Public/Uploads/2021-08-23/6123727007ad8.xlsx"
                >模板下载</a
              >
            </el-upload>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForms('formupcode')"
              >提 交</el-button
            >
            <el-button @click="dialogFormUpdate = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
import moment from 'moment'
export default {
  components: {
    TableTem
  },
  name: 'ShuntSetting',
  data() {
    var percent = (rule, value, callback) => {
      if (value != '') {
        let reg = /^[0-9]*[1-9][0-9]*$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入正确天数'))
        }
      } else {
        callback(new Error('有效期不能为空'))
      }
    }
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      actionUrl: window.path.omcs + 'operatingclientwhitelist/upload',
      titleMap: '新增',
      isFirstEnter: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      dialogEditVisible: false,
      dialogFormUpdate: false,
      pIndex: -1,
      ids: '',
      selectId: [],
      expireTime: '',
      formop: {
        username: '',
        mobile: '',
        level: '',
        remark: '',
        days: '',
      },
      formopEdit: {
        id: '',
        username: '',
        mobile: '',
        level: '',
        remark: '',
        days: '',
        expireDate: '',
      },
      rulessEdit: {
        username: [
          { required: true, message: '请输入用户名称', trigger: 'change' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'change',
          },
        ],
        level: [
          {
            required: true,
            message: '请选择等级',
            trigger: 'change',
          },
        ],
        remark: [
          {
            required: true,
            message: '备注不能为空',
            trigger: 'change',
          },
        ],
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名称', trigger: 'change' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'change',
          },
        ],
        level: [
          {
            required: true,
            message: '请选择等级',
            trigger: 'change',
          },
        ],
        days: [
          {
            required: true,
            validator: percent,
            trigger: 'change',
          },
        ],
        remark: [
          {
            required: true,
            message: '备注不能为空',
            trigger: 'change',
          },
        ],
      },
      formupcode: {
        username: '',
        days: '',
      },
      fileList: [],
      myHeader: '', //请求头
      formInline: {
        username: '',
        mobile: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        mobile: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    }
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.pIndex = -1;
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingclientwhitelist/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
        }
      )
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      window.api.post(
        window.path.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    // 新增 编辑
    addopt() {
      this.titleMap = '新增'
      this.dialogFormVisible = true
      this.fileList = []
    },
    edit(val) {
      this.titleMap = '编辑'
      this.dialogEditVisible = true
      this.formopEdit.username = val.username
      this.formopEdit.mobile = val.mobile
      this.formopEdit.remark = val.remark
      this.formopEdit.days = val.days
      this.formopEdit.level = val.level + ''
      this.formopEdit.id = val.id
      this.formopEdit.expireDate = val.expireDate

      //清除编辑默认值
      // this.$nextTick(() => {
      //   this.$refs.formop.resetFields();
      //   Object.assign(this.formop, val);
      // })
    },
    //   handleTime(e){
    //     this.formop.expireTime = moment(e).format('YYYY-MM-DD HH:mm:ss')
    //   },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            this.titleMap == '新增' ? 'post' : 'put',
            this.titleMap == '新增' ? '确定新增操作？' : '确定编辑操作？',
            window.path.omcs + 'operatingclientwhitelist',
            this.titleMap == '新增' ? this.formop : this.formopEdit,
            (res) => {
              if (res.code == 200) {
                if (this.titleMap == '新增') {
                  this.dialogFormVisible = false
                } else {
                  this.dialogEditVisible = false
                }

                this.gettableLIst()
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除
    cancel(val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs + 'operatingclientwhitelist/' + val.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    //批量
    handleSelectionChange(val) {
      this.selectId = val.records.map((item, index) => {
        return item.id
      })
      this.ids = this.selectId.join(',')
    },
    bathdel() {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs + 'operatingclientwhitelist/' + this.ids,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    updatefail() {
      this.dialogFormUpdate = true
    },
    handleRemove(file, fileList) {
      this.fileList = []
    },
    //改变上传文件
    handleChange(file, fileList) {
      let endingCode = file.name //结尾字符
      let endingName = endingCode.slice(
        endingCode.lastIndexOf('.') + 1,
        endingCode.length
      )
      let isStyle = false //文件格式
      let fileStyle = ['xls', 'xlsx']
      for (let i = 0; i < fileStyle.length; i++) {
        if (endingName === fileStyle[i]) {
          isStyle = true
          break
        }
      }
      if (isStyle == true) {
        this.fileList = fileList
      } else {
        this.fileList = []
        this.$message({
          message: '只能上传.xls .xlsx 结尾的文件！',
          type: 'warning',
        })
      }
    },
    //上传文件过多提示
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      )
    },
    //上传成功
    handleAvatarSuccess(res, file) {
      console.log(res)
      
      if (res.code == 200) {
        this.dialogFormUpdate = false
         this.gettableLIst()
        this.$refs.uploading.clearFiles();  // 清空组件内的文件
        this.fileList = [];  // 手动清空 data 中的 fileList 数组
        this.$message({
          message: '文件上传成功！此次共上传' + res.data + '条数据！',
          type: 'success',
        })
      } else {
        this.$message.error('文件上传失败！')
      }
    },
    submitForms(formupcode) {
      //上传失败代码
      this.$refs[formupcode].validate((valid) => {
        if (valid) {
          if (this.fileList.length <= 0) {
            this.$message.error('请至少上传一个文件！')
            return false
          } else {
            this.$confirm('上传文件, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                //上传成功
                this.$refs.uploading.submit()
                
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消上传',
                })
              })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
      this.myHeader = {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      }
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
        this.myHeader = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
        this.formop.username = ''
        this.formop.mobile = ''
        this.formop.remark = ''
        this.formop.level = ''
        this.formop.days = ''
      }
    },
    dialogEditVisible(val) {
      if (!val) {
        this.$refs.formopEdit.resetFields()
        this.formopEdit.username = ''
        this.formopEdit.mobile = ''
        this.formopEdit.remark = ''
        this.formopEdit.level = ''
        this.formopEdit.days = ''
        this.formopEdit.id = ''
      }
    },
    dialogFormUpdate(val) {
      if (!val) {
        this.$refs.formupcode.resetFields()
        this.fileList = []
        this.formupcode.username = ''
        this.formupcode.days = ''
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
