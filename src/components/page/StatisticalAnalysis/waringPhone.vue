<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon>
          反欺诈检测报表</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名称" label-width="80px" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="批次号" label-width="80px" prop="batchNo">
              <el-input
                v-model="formInline.batchNo"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="提交时间" label-width="80px" prop="time">
              <el-date-picker
                class="input-w"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="false"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <!-- <el-form-item label="检测状态" label-width="80px" prop="submitStatus">
                                <el-select v-model="formInline.submitStatus" placeholder="请选择" class="input-w">
                                    <el-option label="请选择" value=""></el-option>
                                    <el-option label="提交成功" value="1"></el-option>
                                    <el-option label="检测中" value="2"></el-option>
                                    <el-option label="检测完成" value="3"></el-option>
                                </el-select>
                            </el-form-item> -->
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>
        <div class="sensitive-fun" style="margin: 10px 0">
          <!-- <span class="sensitive-list-header">反欺诈检测记录</span> -->
        </div>

        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- 表格和分页开始 -->
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          >
            <el-table-column label="用户名称" width="220">
              <template v-slot="scope">{{ scope.row.userName }}</template>
            </el-table-column>
            <el-table-column label="批次号" width="220">
              <template v-slot="scope">{{ scope.row.batchNo }}</template>
            </el-table-column>
            <el-table-column label="检测状态" width="140">
              <template v-slot="scope">
                <span v-if="scope.row.submitStatus == '1'">提交成功</span>
                <span v-else-if="scope.row.submitStatus == '2'">检测中</span>
                <span v-else-if="scope.row.submitStatus == '3'">检测成功</span>
                <span v-else-if="scope.row.submitStatus == '4'"
                  >解析文件错误</span
                >
              </template>
            </el-table-column>
            <el-table-column label="报表状态" width="90">
              <template v-slot="scope">
                <span v-if="scope.row.reportStatus == '1'">未生成</span>
                <span v-else-if="scope.row.reportStatus == '2'">生成中</span>
                <span v-else-if="scope.row.reportStatus == '3'">完成</span>
              </template>
            </el-table-column>
            <el-table-column label="数据处理描述" width="140">
              <template v-slot="scope">{{ scope.row.reason }}</template>
            </el-table-column>

            <el-table-column label="提交时间" width="140">
              <template v-slot="scope">{{ scope.row.submitDate }}</template>
            </el-table-column>
            <el-table-column label="提交号码数">
              <template v-slot="scope">{{ scope.row.phoneSize }}</template>
            </el-table-column>
            <el-table-column label="正常号码">
              <template v-slot="scope">{{ scope.row.succCount }}</template>
            </el-table-column>
            <el-table-column label="异常号码">
              <template v-slot="scope">{{ scope.row.abnorCount }}</template>
            </el-table-column>
            <el-table-column label="疑似异常号">
              <template v-slot="scope">{{
                scope.row.suspectAbnorCount
              }}</template>
            </el-table-column>
            <el-table-column label="未知号码">
              <template v-slot="scope">{{ scope.row.unknownCount }}</template>
            </el-table-column>

            <el-table-column label="操作" width="160">
              <template v-slot="scope">
                <!-- <el-button type="text" style="color:#F56C6C"  @click="delTem(scope.$index, scope.row)"><i class="el-icon-error"></i>&nbsp;删除</el-button> -->
                <el-button
                  type="text"
                  @click="Download(scope.$index, scope.row)"
                  ><el-icon><el-icon-download /></el-icon>&nbsp;导出</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0"
            >
              <el-pagination
                style="float: right"
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              >
              </el-pagination>
            </el-col>
          </template>
          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
export default {
  components: {
    DatePlugin,
    TableTem,
    ElIconLxEmoji,
    ElIconDownload,
  },
  name: 'waringPhone',
  data() {
    return {
      // 搜索数据
      formInline: {
        batchNo: '',
        userName: '',
        submitStatus: '',
        begTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        batchNo: '',
        userName: '',
        submitStatus: '',
        begTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.wmcs + 'v3/riskphoneList',
        this.formInlines,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.loading2 = false
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.wmcs + 'v3/riskphoneList',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    Download(index, val) {
      //导出
      if (val.fileGroup != '' && val.reportStatus == 3) {
        // 时间过滤
        Date.prototype.format = function (format) {
          var args = {
            'M+': this.getMonth() + 1,
            'd+': this.getDate(),
            'h+': this.getHours(),
            'm+': this.getMinutes(),
            's+': this.getSeconds(),
            'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
            S: this.getMilliseconds(),
          }
          if (/(y+)/.test(format))
            format = format.replace(
              RegExp.$1,
              (this.getFullYear() + '').substr(4 - RegExp.$1.length)
            )
          for (var i in args) {
            var n = args[i]
            if (new RegExp('(' + i + ')').test(format))
              format = format.replace(
                RegExp.$1,
                RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
              )
          }
          return format
        }
        var that = this
        filedownload()
        function filedownload() {
          fetch(that.API.wmcs + 'v3/riskphonedownload/' + val.batchNo, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
            // body: JSON.stringify({
            //     batchno: val.batchNo,
            // })
          })
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data)
              download(blobUrl)
            })
        }
        function download(blobUrl) {
          var a = document.createElement('a')
          a.style.display = 'none'
          a.download =
            '(' +
            new Date().format('YYYY-MM-DD HH:mm:ss') +
            ') ' +
            '反欺诈检测.zip'
          a.href = blobUrl
          a.click()
        }

        // this.$File.export(window.path.cpus +'mobileCheck/download',{batchNo:val.row.batchNo},`空号检测报表.zip`);
      } else if (val.fileGroup == '' && val.reportStatus == 3) {
        this.$message.warning('接口提交的数据不可导出！')
      } else if (val.fileGroup != '' && val.reportStatus != 3) {
        this.$message.warning('报表状态未完成不可导出！')
      } else {
        this.$message.warning('此数据不可导出！')
      }
    },

    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.begTime = ''
      this.formInline.endTime = ''
      Object.assign(this.formInlines, this.formInline)
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.begTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.begTime = ''
        this.formInline.endTime = ''
      }
    },

    handleSizeChange(size) {
      //分页每一页的有几条
      this.formInlines.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.formInlines.currentPage = currentPage
      this.getTableData()
    },
    // // 分页
    // handleSizeChange(size) {
    //     this.formInlines.pageSize = size;
    // },
    // handleCurrentChange: function(currentPage){
    //     this.formInlines.currentPage = currentPage;
    // },
  },
  created() {},
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>
