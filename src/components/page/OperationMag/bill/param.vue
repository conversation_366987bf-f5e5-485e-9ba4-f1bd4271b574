<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="formInline.username" placeholder></el-input>
          </el-form-item>
          <!-- <el-form-item label="产品类型" prop="productId">
                            <el-select v-model="formInline.productId" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
          <el-form-item label="统计类型" prop="billType">
            <el-select v-model="formInline.billType" class="input-w" placeholder="请选择">
              <el-option label="签名统计" value="SIGN"> </el-option>
              <el-option label="参数统计" value="EXTEND"> </el-option>
            </el-select>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      
      <div class="Mail-table">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="billParam"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <!-- <vxe-column type="checkbox" width="46"></vxe-column> -->
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="统计参数" title="统计参数">
            <template v-slot="scope">
              <div>{{ scope.row.billParam }}</div>
            </template>
          </vxe-column>
          <vxe-column field="统计类型" title="统计类型">
            <template v-slot="scope">
              <div v-if="scope.row.billType == 'SIGN'">签名统计</div>
              <div v-if="scope.row.billType == 'EXTEND'">参数统计</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>{{
                moment(scope.row.createTime).format('YYYY-MM-DD hh:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="操作" title="操作" width="150" fixed="right">
                            <template #default="scope">
                                <el-button type="text" style="margin-left: 0px"
                                    @click="detailsRow(scope.$index, scope.row)">编辑</el-button>
                                <el-button type="text" style="margin-left: 10px;color:red"
                                    @click="delState(scope.$index, scope.row)">删除</el-button>
                            </template>
                        </vxe-column> -->
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import common from '../../../../assets/js/common'
export default {
  name: 'billParam',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      menuName: '账单参数',
      //查询表单
      formInline: {
        username: '',
        productId: '',
        billType: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        productId: '',
        billType: '',
        currentPage: 1,
        pageSize: 10,
      },
      options: [],
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerClientBillParam/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
        this.menuName = common.getMenuList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
      this.menuName = common.getMenuList()
    })
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听弹框是否关闭
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.input-w {
  width: 300px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>