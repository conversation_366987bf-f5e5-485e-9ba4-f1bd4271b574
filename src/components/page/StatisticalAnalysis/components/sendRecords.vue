<template>
  <div>
    <div class="passageWay-title">
      <div style="display: flex; align-items: center; width: 630px;">
        <span
          style="
            display: inline-block;
            width: 68px;
            text-align: right;
            padding-right: 10px;
          "
          >时间</span
        >
        <el-radio-group
          v-model="flag"
          size="default"
          text-color="#fff"
          fill="#16a589"
          @change="handleChangeTimeOptions"
        >
          <el-radio-button value="1">今天</el-radio-button>
          <el-radio-button value="2">近4天</el-radio-button>
          <!-- <el-radio-button label="8">上月</el-radio-button> -->
        </el-radio-group>
        <!-- <date-plugin
          class="search-date"
          :datePluginValueList="datePluginValueList"
          @handledatepluginVal="handledatepluginVal"
        ></date-plugin> -->
        <el-date-picker
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="datePluginValue"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          range-separator="至"
          @change="timeClick"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <!-- 查询框开始 -->
      <el-form
        label-width="80px"
        :inline="true"
        :model="form1"
        class="demo-form-inline"
        ref="sendjl1"
      >
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form1.clientName" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form1.mobile" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input v-model="form1.signature" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="回复内容" prop="content">
          <el-input v-model="form1.content" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="msgId" prop="msgid">
          <el-input v-model="form1.msgid" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="通道号" prop="channelId">
          <el-input v-model="form1.channelId" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="省份" prop="provincial">
          <el-input v-model="form1.provincial" class="input-w"></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click="Query2">查询</el-button>
          <el-button type="primary" plain @click="reset1('sendjl1')"
            >重置</el-button
          >
          <el-button type="primary" plain @click="export2()">导出</el-button>
          <el-button
            type="primary"
            style="margin-left: 15px"
            v-if="selectId.length > 0"
            @click="delAll"
            >批量加入黑名单</el-button
          >
        </div>
      </el-form>
      <!-- 查询框结束 -->
      <div class="passage-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj1" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj1.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj1.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="statisticalAnalysisSendRecords"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj1.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px;"
          :data="tableDataObj1.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <!-- <vxe-column field="" title="签名ID" width="80">
                            <template #default="scope">{{ scope.row.signatureId }}</template>
                        </vxe-column> -->
          <vxe-column field="用户名称" title="用户名称" width="120">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.userName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.userName}}
                                    </span>
                                </el-popover> -->
              <!-- <span>{{ scope.row.userName }}</span> -->
              <div
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
              >
                {{ scope.row.userName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号码" title="手机号码" width="130">
            <template v-slot="scope">
              <!-- <div class="spanColor" @click="trueFlogShow(scope.row.content)" v-html="scope.row.content"></div> -->
              <div class="spanColor" v-html="scope.row.mobile"></div>
            </template>
          </vxe-column>
          <vxe-column field="通道号" title="通道号" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.channelId }}</div>
            </template>
          </vxe-column>
          <vxe-column field="省份" title="省份" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.provincial }}</div>
            </template>
          </vxe-column>
          <vxe-column field="城市" title="城市" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.city }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回复内容" title="回复内容" width="150">
            <template v-slot="scope">
              <!-- <Tooltip v-if="scope.row.content" :content="scope.row.content" className="multiline-text"
                                    widthpx="400px" effect="light">
                                </Tooltip> -->
              <div>{{ scope.row.content }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回复时间" title="回复时间" width="180">
            <template v-slot="scope">
              <div>{{ scope.row.createTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="发送内容" title="发送内容" width="450">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.smsContent"
                :content="scope.row.smsContent"
                className="wrapper-text"
                effect="dark"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="msgId" title="msgId" width="180">
            <template v-slot="scope">
              <el-tooltip class="item" effect="dark" placement="top">
                <template v-slot:content>
                  <div class="tooltip">{{ scope.row.msgid }}</div>
                </template>
                <div class="span">{{ scope.row.msgid }}</div>
              </el-tooltip>
            </template>
          </vxe-column>
          <vxe-column field="扩展码" title="扩展码" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.ext }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button
                v-if="scope.row.isBlacknumber == 1"
                link
                style="margin-left: 0px; color: #409eff;"
                @click="detailsRow(scope.$index, scope.row)"
                ><i></i>&nbsp;</el-button
              >
              <el-button
                v-else
                link
                style="margin-left: 0px; color: #409eff;"
                @click="detailsRow(scope.$index, scope.row)"
                ><el-icon><SuccessFilled /></el-icon>&nbsp;加入黑名单</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange1"
            @current-change="handleCurrentChange1"
            :current-page="formData1.currentPage"
            :page-size="formData1.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj1.totalRow"
          >
          </el-pagination>
        </div>
        
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="100px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formop.remark"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增结束 -->
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
export default {
  components: {
    DatePlugin,
    TableTem,
    UserLists,
    Tooltip
  },
  name: 'PassagewaySend',
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      userFlag: false,
      nameover: '',
      titleMap: {
        add: '批量加入黑名单',
        edit: '加入黑名单',
      },
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      idStr: '',
      rules: {
        //验证规则
        remark: [
          { required: false, message: '请输入备注', trigger: 'blur' },
          {
            min: 1,
            max: 70,
            message: '长度在 1 到 70 个字符',
            trigger: ['blur', 'change'],
          },
        ],
      },
      dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: '',
      },
      name: 'SendRecord',
      flag: '1', //选择那一天
      Times: {
        beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      datePluginValueList: {
        //日期选择器
        type: 'daterange',
        start: '',
        end: '',
        range: '-',
        clearable: false,
        defaultTime: '', //默认起始时刻
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.pickerMinDate = minDate.getTime()
            if (maxDate) {
              this.pickerMinDate = ''
            }
          },
          disabledDate: (time) => {
            if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
              return false
            }
            if (this.pickerMinDate !== '') {
              const day30 = 30 * 24 * 3600 * 1000
              let maxTime = this.pickerMinDate + day30
              if (maxTime > Date.now() - 345600000) {
                maxTime = Date.now() - 345600000
              }
              const minTime = this.pickerMinDate - day30
              return time.getTime() < minTime || time.getTime() > maxTime
            }
            return time.getTime() > Date.now() - 345600000
          },
        },
        datePluginValue: '',
      },
      datePluginValue: [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      ],
      //回复查询的值
      form1: {
        mobile: '',
        signature: '',
        clientName: '',
        content: '',
        msgid: '',
        channelId: '',
        provincial: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //复制回复查询的值
      formData1: {
        mobile: '',
        signature: '',
        clientName: '',
        content: '',
        msgid: '',
        channelId: '',
        provincial: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      tableDataObj1: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
        tableLabel: [
          //列表表头
          {
            prop: 'userName',
            showName: '用户名称',
            width: '120',
            fixed: false,
          },
          { prop: 'mobile', showName: '手机号码', width: '130', fixed: false },
          { prop: 'content', showName: '回复内容', width: '150', fixed: false },
          {
            prop: 'createTime',
            showName: '回复时间',
            width: '180',
            fixed: false,
          },
          { prop: 'smsContent', showName: '发送内容', fixed: false },
          { prop: 'msgid', showName: 'msgid', fixed: false },
          { prop: 'ext', showName: '扩展码', width: '120', fixed: false },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '140', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'isBlacknumber', //关联的表格属性
            contactData: '2', //关联的表格属性-值
            optionName: '加入黑名单', //按钮的显示文字
            optionMethod: 'details', //按钮的方法
            icon: 'SuccessFilled', //按钮图标
            optionButtonColor: '#16A589', //按钮颜色
          },
        ],
        tableOptions: [],
      },
    }
  },
  methods: {
    timeClick(val) {
      if (val) {
        this.Times.beginTime = this.moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = this.moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
        this.flag = '3'
      } else {
        this.Times.beginTime = ""
        this.Times.endTime = ""
        this.flag=''
      }
    },
    handelSelection(val) {
      //列表复选框的值
      if (val.records.length > 0) {
        let selectId = []
        val.records.forEach((item) => {
          selectId.push(item.smsReplyId)
        })
        this.selectId = selectId.join(',')
      } else {
        this.selectId = ""
      }
    },
    detailsRow(index, val) {
      console.log(val)
      //加入黑名单
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.idStr = val.smsReplyId
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        // Object.assign(this.formop.remark,val.row.remark);
      })
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    //列表操作
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //批量//加入黑名单
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingStatisticalAnalysis/blacklistBatch',
              {
                idStr: this.selectId,
                remark: this.formop.remark,
              },
              () => {
                this.dialogFormVisible = false
                this.getChallDate1()
              }
            )
          } else {
            //加入黑名单
            this.formop.idStr = this.idStr
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingStatisticalAnalysis/blacklistBatch',
              this.formop,
              () => {
                this.dialogFormVisible = false
                this.getChallDate1()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    delAll() {
      //批量加入黑名单
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    //获取 回复记录数据
    getChallDate1() {
      let bb = {}
      Object.assign(bb, this.formData1)
      bb.flag = this.flag
      bb.beginTime = this.Times.beginTime
      bb.endTime = this.Times.endTime
      this.tableDataObj1.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingStatisticalAnalysis/smsReply/page',
        bb,
        (res) => {
          this.tableDataObj1.loading2 = false
          this.tableDataObj1.tableData = res.records
          this.tableDataObj1.totalRow = res.total
        }
      )
    },
    //导出 （回复记录）
    export2() {
      let bb = {}
      Object.assign(bb, this.formData1)
      bb.flag = this.flag
      bb.beginTime = this.Times.beginTime
      bb.endTime = this.Times.endTime
      bb.isDownload = 1
      if (this.tableDataObj1.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingStatisticalAnalysis/smsReply/page',
          bb,
          '回复记录报表.xlsx'
        )
      }
    },
    handledatepluginVal: function (val1, val2) {
      if (val1) {
        this.Times.beginTime = val1
        this.Times.endTime = val2
        this.flag = '3'
      } else {
        this.flag = '1'
        this.Times.beginTime = ''
        this.Times.endTime = ''
      }
    },
    handleChangeTimeOptions: function () {
      // this.datePluginValueList.datePluginValue = ''
    },
    //回复查询
    Query2() {
      Object.assign(this.formData1, this.form1)
      this.getChallDate1()
    },
    //回复重置
    reset1() {
      if (this.flag == '1') {
        // console.log(11);
        this.$refs.sendjl1.resetFields()
        this.Times.beginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
        Object.assign(this.formData, this.form)
        this.getChallDate1()
      } else {
        this.flag = '1'
        this.$refs.sendjl1.resetFields()
        Object.assign(this.formData, this.form)
      }
    },
    handleSizeChange1(size) {
      this.formData1.pageSize = size
    },
    handleCurrentChange1: function (currentPage) {
      this.formData1.currentPage = currentPage
    },
  },
  mounted() {
    // this.getChallDate1();
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate1()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate1()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听时间范围
    flag: function (val) {
      // console.log(val,'val');
      // if(val != '5'){
      //     this.Times.beginTime = '';
      //     this.Times.endTime = '';
      //     this.datePluginValue='';
      // }
      if (val == '1') {
        this.Times.beginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
      } else if (val == '2') {
        this.Times.beginTime = moment()
          .subtract(4, 'days')
          .format('YYYY-MM-DD 00:00:00')
        this.Times.endTime = moment().format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().subtract(4, 'days').format('YYYY-MM-DD 00:00:00'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ]
      }
      // this.getChallDate1()
    },
    //监听时间是否改变
    Times: {
      handler() {
        this.getChallDate1()
      },
      deep: true,
    },

    activeName2: function (val) {
      this.getChallDate1()
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
    formData1: {
      handler(val) {
        this.getChallDate1()
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
.span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tooltip {
  width: 300px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>