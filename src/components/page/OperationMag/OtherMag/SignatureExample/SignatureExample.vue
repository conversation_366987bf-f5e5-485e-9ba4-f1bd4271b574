<template>
    <div class="container_left">
        <div class="OuterFrame fillet">
            <div>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInlineRef"
                    label-width="80px">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model.trim="formInline.username" placeholder class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="签名" prop="signature">
                        <el-input v-model.trim="formInline.signature" placeholder class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="类型" prop="smsType">
                        <el-select class="input-w" clearable v-model="formInline.smsType" placeholder="请选择">
                            <!-- <el-option label="全部" value=""></el-option> -->
                            <!-- <el-option label="验证码" value="1"></el-option> -->
                            <el-option label="行业" value="2"></el-option>
                            <el-option label="营销" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain @click="Query">查询</el-button>
                        <el-button type="primary" plain @click="Reload(formInlineRef)">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div>
                <vxe-toolbar ref="toolbarRef" custom>
                    <template #buttons>
                        <el-button type="primary" plain @click="openImportDialog">导入示例</el-button>
                    </template>
                </vxe-toolbar>
                <vxe-table ref="tableRef" id="blackWordTable" border v-loading="tableDataObj.loading2"
                    element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px"
                    :virtual-y-config="{ enabled: false }" :custom-config="{ storage: true }"
                    :column-config="{ resizable: true }" :row-config="{ isHover: true }" :data="tableDataObj.tableData"
                    @checkbox-all="handleSelectionChange" @checkbox-change="handleSelectionChange">
                    <vxe-column field="用户名" title="用户名">
                        <template v-slot="scope">
                            <div>
                                {{ scope.row.username }}
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="签名" title="签名" show-overflow>
                        <template v-slot="scope">
                            <span>{{ scope.row.signature }}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="类型" title="类型">
                        <template v-slot="scope">
                            <!-- <div v-if="scope.row.smsType == 1">
                                <span>验证码</span>
                            </div> -->
                            <div v-if="scope.row.smsType == 2">
                                <el-tag type="success">行业</el-tag>
                            </div>
                            <div v-if="scope.row.smsType == 3">
                                <el-tag type="primary">营销</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="内容" title="内容" show-overflow>
                        <template v-slot="scope">
                            <span>
                                {{ scope.row.content }}
                            </span>
                        </template>
                    </vxe-column>
                    <vxe-column field="创建时间" title="创建时间" width="180">
                        <template v-slot="scope">
                            <div v-if="scope.row.createTime">
                                {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
                            </div>
                        </template>
                    </vxe-column>
                </vxe-table>
                <div class="paginationBox">
                    <el-pagination class="page_bottom" @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" :current-page="formInline.currentPage"
                        :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
                </div>
            </div>
        </div>

        <!-- 导入示例对话框 -->
        <el-dialog
            title="导入示例"
            v-model="importDialogVisible"
            :close-on-click-modal="false"
            width="520px"
        >
            <el-upload
                class="upload-demo"
                :action="actionUrl"
                :headers="headers"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :on-progress="handleProgress"
                :on-remove="handleRemove"
                :before-upload="beforeUpload"
                :limit="1"
                :file-list="fileList"
                accept=".xlsx,.xls"
            >
                <el-button size="default" type="primary">
                    <el-icon><upload-filled /></el-icon>选择文件上传
                </el-button>
                <template #tip>
                    <div class="el-upload__tip">只能上传 xlsx、xls 格式的Excel文件，且不超过10MB</div>
                </template>
            </el-upload>
            <div v-if="Progressflag" style="text-align: center; font-size: 35px">
                <el-icon class="is-loading"><loading /></el-icon>
            </div>
            <template #footer>
                <el-button @click="importDialogVisible = false">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import { ref } from "vue";
import { reactive } from "vue";
import { onMounted } from "vue";
import { UploadFilled, Loading } from '@element-plus/icons-vue';

const formInlineRef = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const formInline = reactive({
    username: "",
    signature: "",
    smsType: "",
    currentPage: 1,
    pageSize: 10,
});
const tableDataObj = reactive({
    loading2: false,
    tableData: [],
    total: 0,
});
const handleSelectionChange = () => { };
const handleSizeChange = (size) => {
    formInline.pageSize = size;
    gettableLIst();
};
const handleCurrentChange = (currentPage) => {
    formInline.currentPage = currentPage;
    gettableLIst();
};
const gettableLIst = () => {
    //获取列表
    tableDataObj.loading2 = true;
    window.api.post(
        window.path.omcs + "operator/signatureExample/page",
        formInline,
        (res) => {
            if (res.code == 200) {
                tableDataObj.loading2 = false;
                tableDataObj.total = res.data.total;
                tableDataObj.tableData = res.data.records;
            } else {
                ElMessage.error(res.msg);
            }
        }
    );
};
const Query = () => {
    gettableLIst();
};
const Reload = () => {
    formInlineRef.value.resetFields();
    gettableLIst();
};

// 导入功能相关变量和方法
const importDialogVisible = ref(false);
const actionUrl = window.path.omcs + 'operator/signatureExample/upload'; // 导入接口
const fileList = ref([]);
const Progressflag = ref(false);
const headers = {
    Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN')
};

// 打开导入对话框
const openImportDialog = () => {
    importDialogVisible.value = true;
    fileList.value = [];
};

// 文件上传前的钩子
const beforeUpload = (file) => {
    const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                    file.type === 'application/vnd.ms-excel';
    const isLt10M = file.size / 1024 / 1024 < 10;

    if (!isExcel) {
        ElMessage.error('只能上传Excel文件!');
        return false;
    }
    if (!isLt10M) {
        ElMessage.error('文件大小不能超过10MB!');
        return false;
    }
    return true;
};

// 文件移除回调
const handleRemove = (file, fileList) => {
    console.log('文件移除', file, fileList);
};

// 文件上传成功回调
const handleUploadSuccess = (res, file) => {
    console.log('上传结果', res);
    Progressflag.value = false;
    if (res.code === 200) {
        ElMessage.success('导入成功');
        importDialogVisible.value = false;
        gettableLIst(); // 重新加载数据
    } else {
        ElMessage.error(res.msg || '导入失败');
    }
};

// 文件上传失败回调
const handleUploadError = (error) => {
    Progressflag.value = false;
    ElMessage.error('上传失败：' + error);
};

// 文件上传进度回调
const handleProgress = () => {
    Progressflag.value = true;
};

onMounted(() => {
    const $table = tableRef.value;
    const $toolbar = toolbarRef.value;
    if ($table && $toolbar) {
        $table.connect($toolbar);
    }
    gettableLIst();
});
</script>

<style lang="less" scoped>
.OuterFrame {
    padding: 20px;
}

.Templat-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
}

.Templat-matter>p {
    padding: 5px 0;
}
</style>