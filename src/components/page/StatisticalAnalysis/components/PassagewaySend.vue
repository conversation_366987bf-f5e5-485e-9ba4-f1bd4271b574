<template>
  <div>
    <div class="passageWay-title">
      <el-form
        :inline="true"
        :model="passageformInline"
        label-width="80px"
        class="demo-form-inline"
        ref="passgeForm"
      >
        <el-form-item
          label="通道编号"
          prop="channelId"
          style="margin-left: 8px"
        >
          <el-input
            v-model="passageformInline.channelId"
            class="input-w"
            oninput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="时间">
          <date-plugin
            class="input-w"
            :datePluginValueList="datePluginValueList"
            @handledatepluginVal="handledatepluginVal"
          ></date-plugin>
        </el-form-item>
        <el-form-item label="运营商" prop="operator">
          <el-select
            v-model="passageformInline.operator"
            placeholder="请选择运营商"
            class="input-w"
          >
            <el-option label="全部" value=""></el-option>
            <el-option label="移动" value="1"></el-option>
            <el-option label="联通" value="2"></el-option>
            <el-option label="电信" value="3"></el-option>
            <el-option label="三网" value="4"></el-option>
          </el-select>
        </el-form-item>
        <br />
        <el-form-item>
          <el-button type="primary" plain @click="Query">查询</el-button>
          <el-button type="primary" plain @click="reset()">重置</el-button>
          <el-button type="primary" plain @click="exportFile()">导出</el-button>
        </el-form-item>
      </el-form>
      <!-- 查询框结束 -->
      <div class="passage-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem :tableDataObj="tableDataObj"></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="passageformInline1.currentPage"
              :page-size="passageformInline1.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin' //时间
import TableTem from '@/components/publicComponents/TableTem' //列表
export default {
  name: 'PassagewaySend',
  components: { DatePlugin, TableTem },
  data() {
    return {
      isFirstEnter: false,
      name: 'PassagewaySend',
      datePluginValueList: {
        //日期选择器
        type: 'daterange',
        start: '',
        end: '',
        range: '-',
        clearable: false,
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.pickerMinDate = minDate.getTime()
            if (maxDate) {
              this.pickerMinDate = ''
            }
          },
          disabledDate: (time) => {
            if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
              return false
            }
            if (this.pickerMinDate !== '') {
              const day30 = 30 * 24 * 3600 * 1000
              let maxTime = this.pickerMinDate + day30
              if (maxTime > Date.now()) {
                maxTime = Date.now()
              }
              const minTime = this.pickerMinDate - day30
              return time.getTime() < minTime || time.getTime() > maxTime
            }
            return time.getTime() > Date.now()
          },
        },
        datePluginValue: [
          new Date().toLocaleDateString().split('/').join('-'),
          new Date().toLocaleDateString().split('/').join('-'),
        ],
      },
      flag: '',
      passageformInline: {
        channelId: '',
        operator: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
        beginTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00',
        endTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59',
      },
      passageformInline1: {
        channelId: '',
        operator: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
        beginTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00',
        endTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59',
      },
      tableDataObj: {
        //列表数据
        total: 0,
        tableData: [],
        loading2: false,
        tableLabel: [
          { prop: 'channelId', showName: '通道编号', fixed: false },
          {
            prop: 'createTime',
            showName: '发送时间',
            width: '90',
            fixed: false,
          },
          // {prop:"channelNature",showName:'通道属性',fixed:false,width:'80',formatData: function(val) { return val == '1' ? '直连' : '第三方' } },
          // {prop:"operator",showName:'运营商',width:'60',
          //     formatData: function(val) { if(val == '1'){return val='移动'
          //     }else if(val == '2'){
          //         return val='联通'
          //     }else if(val == '3'){
          //         return val='电信'
          //     }else if(val == '4'){
          //         return val='三网'
          //     }
          //     } ,fixed:false},
          // {prop:"channelPrice",showName:'通道单价',fixed:false},
          { prop: 'mobileNumber', showName: '提交号码数', fixed: false },
          { prop: 'sendAmount', showName: '提交计费数', fixed: false },
          { prop: 'successAmount', showName: '成功计费数', fixed: false },
          { prop: 'failNumber', showName: '失败计费数', fixed: false },
          { prop: 'waitNumber', showName: '待返回计费数', fixed: false },
          {
            prop: 'successRate',
            showName: '成功率',
            fixed: false,
            formatData: function (val) {
              if (val != 0 && val != null) {
                return val + '%'
              } else {
                return 0
              }
            },
          },
          {
            prop: 'failureRate',
            showName: '失败率',
            fixed: false,
            formatData: function (val) {
              if (val != 0 && val != null) {
                return val + '%'
              } else {
                return 0
              }
            },
          },
          {
            prop: 'waitRate',
            showName: '待返回率',
            fixed: false,
            formatData: function (val) {
              if (val != 0 && val != null) {
                return val + '%'
              } else {
                return 0
              }
            },
          },
          // {prop:"money",showName:'消费金额（元）',fixed:false,formatData: function(val) {if(val == null){return 0}else{return val} }}
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    }
  },
  methods: {
    //获取列表数据
    getChallDate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingStatisticalAnalysis/channelSend',
        this.passageformInline1,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.total = res.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //选择时间范围
    handledatepluginVal: function (val1, val2) {
      if (val1) {
        this.passageformInline.beginTime = val1 + ' 00:00:00'
        this.passageformInline.endTime = val2 + ' 23:59:59'
      } else {
        this.passageformInline.beginTime = ''
        this.passageformInline.endTime = ''
      }
    },
    //查询
    Query() {
      Object.assign(this.passageformInline1, this.passageformInline)
      this.getChallDate()
    },
    //导出
    exportFile() {
      let aa = Object.assign({}, this.passageformInline1)
      aa.isDownload = 1
      this.exportSend(aa)
    },
    //导出发送请求
    exportSend(aa) {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingStatisticalAnalysis/channelSend',
          aa,
          '通道发送列表.xlsx'
        )
      }
    },
    //重置
    reset() {
      this.$refs.passgeForm.resetFields()
      this.datePluginValueList.datePluginValue = [
        new Date().toLocaleDateString().split('/').join('-'),
        new Date().toLocaleDateString().split('/').join('-'),
      ]
      this.passageformInline.beginTime =
        new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00'
      this.passageformInline.endTime =
        new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59'
      Object.assign(this.passageformInline1, this.passageformInline)
    },
    handleSizeChange(size) {
      this.passageformInline1.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.passageformInline1.currentPage = currentPage
    },
  },
  mounted() {
    // this.getChallDate();
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate()
    })
  },
  watch: {
    // //监听时间是否改变
    // Times:{
    //     handler(){
    //         this.getChallDate();
    //     },
    //     deep:true
    // },
    //监听查询条件的改变
    passageformInline1: {
      handler() {
        this.getChallDate()
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
</style>
