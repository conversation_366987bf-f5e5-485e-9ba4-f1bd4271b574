# 📋 通道重启计划 - 运营商通道选择功能

## 🎯 功能概述

为通道重启计划的新增功能增加了根据运营商查找通道的功能，支持通道多选和批量创建计划。

## ✨ 新增功能特性

### 📊 主要功能
- **运营商选择**: 支持选择联通、移动、电信三大运营商
- **通道查询**: 根据选择的运营商动态获取可用通道列表
- **多选通道**: 支持同时选择多个通道
- **全选功能**: 支持一键全选/取消全选所有通道
- **选择状态**: 实时显示已选择通道数量和总数
- **批量创建**: 为选中的多个通道批量创建重启计划
- **智能表单**: 新增和编辑使用不同的表单结构

## 🔧 技术实现

### 1. API接口

#### **通道列表查询**
- **接口**: `GET /v3/operatingchannelgroup/channelList`
- **参数**: 
  - `operatorType`: 运营商类型（1-联通，2-移动，3-电信）
  - `productId`: 产品ID（当前使用运营商类型作为产品ID）

```javascript
// 示例请求
GET /v3/operatingchannelgroup/channelList?operatorType=1&productId=1
```

#### **批量创建计划**
- **接口**: `POST /operatingchannelrestarttask`
- **说明**: 将多个通道ID以逗号分隔的形式提交到单个接口

### 2. 数据结构更新

#### **表单数据结构**
```javascript
const form = reactive({
    id: 0,
    channelId: '',          // 编辑时使用的单个通道ID
    channelIds: [],         // 新增时使用的多选通道ID数组
    operatorType: '',       // 运营商类型
    beginTime: '',
    endTime: '',
    nextTime: '',
    connectNum: 0,
    maxNum: 0,
    status: 0
});
```

#### **通道列表数据**
```javascript
const channelList = ref([]);      // 通道列表
const channelLoading = ref(false); // 通道加载状态
```

### 3. 表单验证规则

#### **动态验证规则**
```javascript
const rules = computed(() => {
    const baseRules = {
        beginTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
        endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
        nextTime: [{ required: true, message: '请选择下次执行时间', trigger: 'change' }],
        connectNum: [{ required: true, message: '请输入连接数', trigger: 'blur' }],
        maxNum: [{ required: true, message: '请输入最大连接数', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
    };

    // 新增时验证运营商和通道选择
    if (!isEdit.value) {
        baseRules.operatorType = [{ required: true, message: '请选择运营商', trigger: 'change' }];
        baseRules.channelIds = [
            { required: true, message: '请选择通道', trigger: 'change' },
            { type: 'array', min: 1, message: '请至少选择一个通道', trigger: 'change' }
        ];
    } else {
        // 编辑时验证通道ID
        baseRules.channelId = [{ required: true, message: '请输入通道ID', trigger: 'blur' }];
    }

    return baseRules;
});
```

## 🎨 UI设计

### 新增表单布局
```vue
<!-- 新增时显示运营商选择和通道多选 -->
<template v-if="!isEdit">
    <el-form-item label="运营商" prop="operatorType">
        <el-select v-model="form.operatorType" placeholder="请选择运营商" @change="handleOperatorChange">
            <el-option label="联通" :value="1"></el-option>
            <el-option label="移动" :value="2"></el-option>
            <el-option label="电信" :value="3"></el-option>
        </el-select>
    </el-form-item>
    <el-form-item label="通道选择" prop="channelIds">
        <el-select 
            v-model="form.channelIds" 
            multiple 
            placeholder="请先选择运营商，然后选择通道" 
            :loading="channelLoading"
            :disabled="!form.operatorType">
            <el-option 
                v-for="channel in channelList" 
                :key="channel.id" 
                :label="`${channel.channelName}(${channel.channelId})`" 
                :value="channel.channelId">
            </el-option>
        </el-select>
    </el-form-item>
</template>

<!-- 编辑时显示单个通道ID -->
<el-form-item v-else label="通道ID" prop="channelId">
    <el-input v-model="form.channelId" placeholder="请输入通道ID" disabled></el-input>
</el-form-item>
```

### 表单特性
- **运营商选择**: 下拉框选择运营商类型
- **通道多选**: 支持多选的下拉框，显示通道名称和ID
- **加载状态**: 获取通道列表时显示加载状态
- **禁用状态**: 未选择运营商时禁用通道选择
- **编辑模式**: 编辑时通道ID为只读状态

## 🔄 核心方法

### 1. 获取通道列表
```javascript
const getChannelList = async (operatorType) => {
    if (!operatorType) return;
    
    channelLoading.value = true;
    try {
        const productId = operatorType; // 使用运营商类型作为产品ID
        
        window.api.get(
            window.path.omcs + `v3/operatingchannelgroup/channelList?operatorType=${operatorType}&productId=${productId}`, 
            {}, 
            (res) => {
                if (res.code == 200) {
                    channelList.value = res.data || [];
                } else {
                    ElMessage.error(res.msg || '获取通道列表失败');
                }
                channelLoading.value = false;
            }, 
            (error) => {
                console.error('获取通道列表失败:', error);
                ElMessage.error('获取通道列表失败，请稍后重试');
                channelLoading.value = false;
            }
        );
    } catch (error) {
        console.error('获取通道列表失败:', error);
        ElMessage.error('获取通道列表失败，请稍后重试');
        channelLoading.value = false;
    }
};
```

### 2. 运营商变更处理
```javascript
const handleOperatorChange = (operatorType) => {
    // 清空之前选择的通道
    form.channelIds = [];
    channelList.value = [];
    
    if (operatorType) {
        getChannelList(operatorType);
    }
};
```

### 3. 批量创建计划
```javascript
const submitForm = async () => {
    // ... 表单验证

    if (!isEdit.value) {
        // 新增操作：将多个通道ID以逗号分隔的形式提交
        const submitData = {
            channelId: form.channelIds.join(','), // 将数组转换为逗号分隔的字符串
            beginTime: form.beginTime,
            endTime: form.endTime,
            nextTime: form.nextTime,
            connectNum: form.connectNum,
            maxNum: form.maxNum,
            status: form.status
        };

        window.api.post(window.path.omcs + 'operatingchannelrestarttask', submitData, (res) => {
            if (res.code == 200) {
                getRestartPlanList();
                ElMessage.success('成功创建通道重启计划');
                dialogVisible.value = false;
            } else {
                ElMessage.error(res.msg);
            }
        }, (error) => {
            console.error('添加失败:', error);
            ElMessage.error('添加失败，请稍后重试');
        });
    }
    // ... 编辑逻辑
};
```

## 📊 数据流程

### 1. 新增计划流程
```
点击添加 → 打开弹窗 → 选择运营商 → 获取通道列表 → 选择通道 → 填写其他信息 → 批量创建计划
```

### 2. 运营商选择流程
```
选择运营商 → 清空通道选择 → 调用通道列表API → 更新通道下拉选项
```

### 3. 批量创建流程
```
提交表单 → 验证通过 → 将通道ID数组转换为逗号分隔字符串 → 调用创建API → 显示结果
```

## 🎯 功能特点

### 1. 智能表单
- 新增和编辑使用不同的表单结构
- 动态验证规则适应不同场景
- 运营商选择驱动通道列表更新

### 2. 用户体验
- 加载状态提示用户等待
- 禁用状态防止无效操作
- 批量操作提升效率

### 3. 错误处理
- 完善的错误提示机制
- 部分失败时的友好处理
- 网络异常的重试提示

## 🚀 使用说明

### 1. 新增通道重启计划
1. 点击"添加"按钮打开弹窗
2. 选择运营商（联通/移动/电信）
3. 等待通道列表加载完成
4. 选择一个或多个通道
5. 填写计划的时间和连接数信息
6. 选择计划状态
7. 点击"确定"批量创建计划

### 2. 编辑现有计划
1. 点击列表中的"编辑"按钮
2. 通道ID为只读状态，不可修改
3. 修改其他计划信息
4. 点击"确定"保存修改

## 📋 注意事项

1. **运营商类型**: 1-联通，2-移动，3-电信
2. **产品ID**: 当前使用运营商类型作为产品ID，可能需要根据实际情况调整
3. **批量创建**: 多个通道ID以逗号分隔的形式提交到单个API接口
4. **通道格式**: 通道选项显示格式为"通道名称(通道ID)"
5. **表单验证**: 新增时必须选择运营商和至少一个通道
6. **数据格式**: channelId字段接收逗号分隔的字符串，如"CH001,CH002,CH003"

## 🔧 扩展建议

1. **产品ID映射**: 建立运营商类型到产品ID的映射关系
2. **通道筛选**: 在通道列表中添加搜索筛选功能
3. **模板功能**: 支持保存常用的计划模板
4. **批量编辑**: 支持批量修改多个计划的状态
5. **导入导出**: 支持Excel导入导出通道重启计划

---

**更新完成**: 通道重启计划现在支持根据运营商查找通道并批量创建计划，大大提升了操作效率。
