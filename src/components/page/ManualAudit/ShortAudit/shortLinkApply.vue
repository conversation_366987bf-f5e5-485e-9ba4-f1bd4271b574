<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon> 短链申请</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet shortChain-box">
      <div class="short-box">
        <div>
          <el-form
            :inline="true"
            :model="formInline"
            label-width="80px"
            class="demo-form-inline"
            ref="queryForm"
          >
            <el-form-item label="用户名" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="公司名" prop="compName">
              <el-input
                v-model="formInline.compName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="formInline.auditStatus" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="待审核" value="1"></el-option>
                <el-option label="审核通过" value="2"></el-option>
                <el-option label="审核失败" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="time">
              <el-date-picker
                type="daterange"
                v-model="formInline.time"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-time"
                @change="handleTimeChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain style="" @click="Query"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reload('queryForm')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="short-btn">
          <!-- <el-button @click="openShort()" v-if="shortStatus != 2" :disabled="shortStatus== 1 ? true : false" type="primary">开通短链</el-button>
                        <el-button v-if="shortStatus == 2" type="primary">添加白名单</el-button> -->
        </div>
        <div class="Mail-table Mail-table1">
          <!-- 表格和分页开始 -->
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column label="用户名">
              <template v-slot="scope">
                <span sty>{{ scope.row.userName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="公司名">
              <template v-slot="scope">
                <span sty>{{ scope.row.compName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态">
              <template v-slot="scope">
                <el-tag
:disable-transitions="true"
                  type="info"
                  effect="dark"
                  v-if="scope.row.auditStatus == '1'"
                  >待审核</el-tag
                >
                <el-tag
:disable-transitions="true"
                  type="success"
                  effect="dark"
                  v-else-if="scope.row.auditStatus == '2'"
                  >审核通过</el-tag
                >
                <el-tag
:disable-transitions="true"
                  type="danger"
                  effect="dark"
                  v-else-if="scope.row.auditStatus == '3'"
                  >审核未通过</el-tag
                >
                <el-tooltip
                  v-else-if="scope.row.auditStatus == '3'"
                  class="item"
                  effect="dark"
                  :content="scope.row.auditReason"
                  placement="top"
                >
                  <el-icon style="color: #f56c6c"><el-icon-info /></el-icon>
                </el-tooltip>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="auditName" label="审核人"> </el-table-column>
            <el-table-column label="审核时间 ">
              <template v-slot="scope">
                <span>{{
                  scope.row.auditTime
                    ? moment(scope.row.auditTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
              <template v-slot="scope">
                <el-button
                  v-if="scope.row.auditStatus == '1'"
                  type="text"
                  style="color: #67c23a; margin-left: 0px"
                  @click="handlePass(2, scope.row)"
                  ><el-icon><el-icon-check /></el-icon>&nbsp;通过</el-button
                >
                <el-button
                  v-if="scope.row.auditStatus == '1'"
                  type="text"
                  style="color: #f56c6c; margin-left: 10px"
                  @click="handleReject(3, scope.row)"
                  ><el-icon><el-icon-error /></el-icon>&nbsp;不通过</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <div
            style="
              display: flex;
              justify-content: space-between;
              margin-top: 10px;
            "
          >
            <div></div>
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.currentPage"
              :page-size="formInline.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            >
            </el-pagination>
          </div>
          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
    <el-dialog
      title="审核不通过"
      v-model="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="原因" prop="auditReason">
          <el-input
            type="textarea"
            v-model="ruleForm.auditReason"
            placeholder="请输入审核不通过原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit('ruleForm')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  components: {
    ElIconLxEmoji,
    ElIconInfo,
    ElIconCheck,
    ElIconError,
  },
  name: 'shortApply',
  data() {
    return {
      shortStatus: 0, //短链状态
      dialogVisible: false, //审核不通过弹窗
      isFirstEnter: false, //是否第一次进入页面
      formInline: {
        userName: '',
        compName: '',
        auditStatus: '',
        beginTime: '',
        endTime: '',
        time: [],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        total: 0, //总条数
      },
      ruleForm: {
        auditReason: '',
      },
      rules: {
        auditReason: [
          { required: true, message: '请输入审核不通过原因', trigger: 'blur' },
        ],
      },
      auditData: {},
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getShortList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getShortList()
    })
  },
  methods: {
    //获取短链列表
    getShortList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.slms + 'shortLink/apply/page',
        this.formInline,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.total = res.data.total
            this.tableDataObj.loading2 = false
          }
        }
      )
    },
    //查询
    Query() {
      this.getShortList()
    },
    //重置
    Reload(formName) {
      this.$refs[formName].resetFields() //清空查询表单
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.getShortList()
    },
    //-----复制功能
    // handleCopy(name, event) {
    //     clip(name, event)
    // },
    //-----翻页操作
    handleSizeChange(size) {
      this.formInline.pageSize = size
      this.getShortList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInline.currentPage = currentPage
      this.getShortList()
    },
    handleTimeChange(val) {
      if (val) {
        this.formInline.beginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formInline.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    //全选
    handleSelectionChange() {},
    //审核通过
    handlePass(status, row) {
      let data = {
        userName: row.userName,
        auditStatus: status,
        id: row.id,
      }
      this.$confirms.confirmation(
        'put',
        '短链申请审核确认通过！',
        window.path.cpus + 'consumerclientshortlink/audit',
        data,
        (res) => {
          this.getShortList()
        }
      )
    },
    //审核不通过弹窗关闭
    handleClose() {
      this.dialogVisible = false
    },
    //审核不通过
    handleReject(status, row) {
      this.auditData = {
        userName: row.userName,
        auditStatus: status,
        id: row.id,
      }

      this.dialogVisible = true
    },
    //审核不通过
    handleSubmit(formName) {
      this.auditData.auditReason = this.ruleForm.auditReason
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'put',
            '短链申请审核不通过！',
            window.path.cpus + 'consumerclientshortlink/audit',
            this.auditData,
            (res) => {
              this.getShortList()
              this.dialogVisible = false
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs.ruleForm.resetFields() //清空表单
        this.auditData = {}
      }
    },
  },
}
</script>

<style lang="less" scoped>
.shortChain-box {
  padding: 20px;
}
.shortChain-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  font-size: 12px;
}
.shortChain-matter > p {
  padding: 5px 0px;
}
.short-box {
  margin: 10px 0;
}
.short-btn {
  margin-bottom: 10px;
}
:deep(.el-table__row) {
  height: 48px;
}
</style>
