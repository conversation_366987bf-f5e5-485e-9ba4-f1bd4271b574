<template>
  <el-date-picker
    :default-time="datePluginValueList.defaultTime"
    :shortcuts="
      datePluginValueList.pickerOptions &&
      datePluginValueList.pickerOptions.shortcuts
    "
    :disabled-date="
      datePluginValueList.pickerOptions &&
      datePluginValueList.pickerOptions.disabledDate
    "
    :cell-class-name="
      datePluginValueList.pickerOptions &&
      datePluginValueList.pickerOptions.cellClassName
    "
    v-model="datePluginValueList.datePluginValue"
    :type="datePluginValueList.type"
    :range-separator="datePluginValueList.range"
    :start-placeholder="datePluginValueList.start"
    :end-placeholder="datePluginValueList.end"
    :placeholder="datePluginValueList.placeholder"
    :clearable="datePluginValueList.clearable"
    @change="hande"
    @blur="getdateval"
  >
  </el-date-picker>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
export default {
  data() {
    return {
      value: '',
    }
  },
  name: 'DatePlugin',
  props: ['datePluginValueList'],
  methods: {
    hande: function (val) {
      if (val) {
        if (val.length == 2) {
          let startTime = this.moment(val[0]).format('YYYY-MM-DD')
          let endTime = this.moment(val[1]).format('YYYY-MM-DD')
          $emit(this, 'handledatepluginVal', startTime, endTime)
        } else {
          let staTime = this.moment(val).format('YYYY-MM-DD HH:mm:ss')
          $emit(this, 'handledatepluginVal', staTime)
        }
      }
      this.value = val
    },
    getdateval: function () {
      //日期input框是否有值
      $emit(this, 'IsThereValue', this.value)
    },
  },
  emits: ['handledatepluginVal', 'IsThereValue'],
}
</script>
