#!/usr/bin/env node

/**
 * 路由模块化验证脚本
 * 用于验证路由拆分后的完整性和正确性
 */

const fs = require('fs')
const path = require('path')

// 路由模块目录
const MODULES_DIR = path.join(__dirname, '../src/router/modules')
const ROUTER_FILE = path.join(__dirname, '../src/router/router.js')

/**
 * 读取所有模块文件
 */
function getAllModules() {
  try {
    const files = fs.readdirSync(MODULES_DIR)
    return files
      .filter(file => file.endsWith('.js'))
      .map(file => file.replace('.js', ''))
  } catch (error) {
    console.error('Error reading modules directory:', error.message)
    return []
  }
}

/**
 * 验证模块文件格式
 */
function validateModuleFormat(moduleName) {
  const modulePath = path.join(MODULES_DIR, `${moduleName}.js`)
  
  try {
    const content = fs.readFileSync(modulePath, 'utf8')
    
    // 基本格式检查
    if (!content.includes('export default')) {
      return { valid: false, error: 'Missing export default' }
    }
    
    if (!content.includes('import(')) {
      return { valid: false, error: 'No lazy loading found' }
    }
    
    return { valid: true }
  } catch (error) {
    return { valid: false, error: error.message }
  }
}

/**
 * 检查路由路径重复
 */
function checkDuplicatePaths() {
  const modules = getAllModules()
  const allPaths = new Set()
  const duplicates = []
  
  modules.forEach(moduleName => {
    const modulePath = path.join(MODULES_DIR, `${moduleName}.js`)
    
    try {
      const content = fs.readFileSync(modulePath, 'utf8')
      const pathMatches = content.match(/path:\s*['"`]([^'"`]+)['"`]/g)
      
      if (pathMatches) {
        pathMatches.forEach(match => {
          const path = match.match(/['"`]([^'"`]+)['"`]/)[1]
          if (allPaths.has(path)) {
            duplicates.push({ path, module: moduleName })
          } else {
            allPaths.add(path)
          }
        })
      }
    } catch (error) {
      console.error(`Error reading ${moduleName}:`, error.message)
    }
  })
  
  return { totalPaths: allPaths.size, duplicates }
}

/**
 * 统计路由数量
 */
function getRouteStats() {
  const modules = getAllModules()
  const stats = {
    totalModules: modules.length,
    moduleStats: {},
    totalRoutes: 0
  }
  
  modules.forEach(moduleName => {
    const modulePath = path.join(MODULES_DIR, `${moduleName}.js`)
    
    try {
      const content = fs.readFileSync(modulePath, 'utf8')
      const pathMatches = content.match(/path:\s*['"`][^'"`]+['"`]/g)
      const routeCount = pathMatches ? pathMatches.length : 0
      
      stats.moduleStats[moduleName] = routeCount
      stats.totalRoutes += routeCount
    } catch (error) {
      stats.moduleStats[moduleName] = 0
      console.error(`Error reading ${moduleName}:`, error.message)
    }
  })
  
  return stats
}

/**
 * 主验证函数
 */
function validateRoutes() {
  console.log('🔍 开始验证路由模块化...\n')
  
  // 1. 检查模块目录
  const modules = getAllModules()
  console.log(`📁 发现 ${modules.length} 个路由模块:`)
  modules.forEach(module => console.log(`   - ${module}.js`))
  console.log()
  
  // 2. 验证模块格式
  console.log('🔧 验证模块格式:')
  let validModules = 0
  modules.forEach(moduleName => {
    const result = validateModuleFormat(moduleName)
    if (result.valid) {
      console.log(`   ✅ ${moduleName}.js`)
      validModules++
    } else {
      console.log(`   ❌ ${moduleName}.js - ${result.error}`)
    }
  })
  console.log()
  
  // 3. 检查路径重复
  console.log('🔍 检查路径重复:')
  const { totalPaths, duplicates } = checkDuplicatePaths()
  console.log(`   📊 总路径数: ${totalPaths}`)
  if (duplicates.length > 0) {
    console.log('   ❌ 发现重复路径:')
    duplicates.forEach(dup => {
      console.log(`      - ${dup.path} (在 ${dup.module}.js 中)`)
    })
  } else {
    console.log('   ✅ 未发现重复路径')
  }
  console.log()
  
  // 4. 统计信息
  console.log('📊 路由统计:')
  const stats = getRouteStats()
  console.log(`   总模块数: ${stats.totalModules}`)
  console.log(`   总路由数: ${stats.totalRoutes}`)
  console.log('   各模块路由数:')
  Object.entries(stats.moduleStats)
    .sort(([,a], [,b]) => b - a)
    .forEach(([module, count]) => {
      console.log(`      ${module}: ${count} 个路由`)
    })
  console.log()
  
  // 5. 总结
  console.log('📋 验证总结:')
  console.log(`   ✅ 有效模块: ${validModules}/${modules.length}`)
  console.log(`   ❌ 重复路径: ${duplicates.length}`)
  
  if (validModules === modules.length && duplicates.length === 0) {
    console.log('\n🎉 路由模块化验证通过！')
    process.exit(0)
  } else {
    console.log('\n⚠️  路由模块化存在问题，请检查上述错误')
    process.exit(1)
  }
}

// 运行验证
if (require.main === module) {
  validateRoutes()
}

module.exports = {
  getAllModules,
  validateModuleFormat,
  checkDuplicatePaths,
  getRouteStats,
  validateRoutes
} 