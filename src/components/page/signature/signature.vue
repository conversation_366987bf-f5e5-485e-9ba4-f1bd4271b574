<template>
  <div class="signature-container">
    <!-- 查询表单 -->
    <el-form :inline="true" :model="queryForm" :rules="rules" ref="queryFormRef" class="query-form">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="queryForm.username" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="签名" prop="signature">
        <el-input v-model="queryForm.signature" placeholder="请输入签名" clearable></el-input>
      </el-form-item>
      <el-form-item class="form-buttons">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
        <!-- <el-button type="success" @click="showMockData">查看Mock数据</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 签名详情展示 -->
    <div class="signature-details" v-loading="loading">
      <!-- 无数据提示 -->
      <el-empty v-if="!signatureData" description="暂无数据，请进行查询"></el-empty>
      
      <!-- 签名详情 -->
      <el-card v-else class="signature-card">
        <template #header>
          <div class="card-header">
            <span>签名详情</span>
            <div v-if="queryTime" class="query-time">
              <el-icon><Clock /></el-icon>
              <span>查询时间：{{ queryTime }}</span>
            </div>
          </div>
        </template>
        
        <!-- 基础信息展示 -->
        <div class="signature-info-wrapper">
          <div class="basic-info-card">
            <div class="user-info">
              <div class="avatar-section">
                <el-icon size="32" color="#409eff"><User /></el-icon>
              </div>
              <div class="user-details">
                <div class="username">{{ signatureData.username }}</div>
                <div class="signature-display">
                  <span class="signature-label">签名：</span>
                  <span class="signature-content">{{ signatureData.signature }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 运营商状态卡片 -->
          <div class="operators-grid">
            <div class="operator-card" :class="{ 'success': signatureData.yd==1, 'danger': signatureData.yd==0 }">
              <div class="operator-header">
                <i class="iconfont icon-yidong operator-card-icon mobile-card-icon"></i>
                <span class="operator-name">中国移动</span>
              </div>
              <div class="operator-status-content">
                <div v-if="signatureData.yd==1 && signatureData.ydAvalibleType" class="available-types">
                  <div class="type-tags">
                    <el-tag 
                      v-for="type in signatureData.ydAvalibleType.split(',')" 
                      :key="type" 
                      type="success" 
                      size="default" 
                      effect="light"
                      class="type-tag"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
                <el-tag v-else :type="signatureData.yd==1 ? 'success' : 'danger'" size="default" effect="light">
                  {{ signatureData.yd==1 ? '已实名' : '未实名' }}
                </el-tag>
                <div v-if="signatureData.ydReason" class="status-reason">
                  <el-icon size="14"><Warning /></el-icon>
                  {{ signatureData.ydReason }}
                </div>
              </div>
            </div>
            
            <div class="operator-card" :class="{ 'success': signatureData.lt==1, 'danger': signatureData.lt==0 }">
              <div class="operator-header">
                <i class="iconfont icon-liantong operator-card-icon unicom-card-icon"></i>
                <span class="operator-name">中国联通</span>
              </div>
              <div class="operator-status-content">
                <div v-if="signatureData.lt==1 && signatureData.ltAvalibleType" class="available-types">
                  <div class="type-tags">
                    <el-tag 
                      v-for="type in signatureData.ltAvalibleType.split(',')" 
                      :key="type" 
                      type="success" 
                      size="default" 
                      effect="light"
                      class="type-tag"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
                <el-tag v-else :type="signatureData.lt==1 ? 'success' : 'danger'" size="default" effect="light">
                  {{ signatureData.lt==1 ? '已实名' : '未实名' }}
                </el-tag>
                <div v-if="signatureData.ltReason" class="status-reason">
                  <el-icon size="14"><Warning /></el-icon>
                  {{ signatureData.ltReason }}
                </div>
              </div>
            </div>
            
            <div class="operator-card" :class="{ 'success': signatureData.dx==1, 'danger': signatureData.dx==0 }">
              <div class="operator-header">
                <i class="iconfont icon-dianxin operator-card-icon telecom-card-icon"></i>
                <span class="operator-name">中国电信</span>
              </div>
              <div class="operator-status-content">
                <div v-if="signatureData.dx==1 && signatureData.dxAvalibleType" class="available-types">
                  <div class="type-tags">
                    <el-tag 
                      v-for="type in signatureData.dxAvalibleType.split(',')" 
                      :key="type" 
                      type="success" 
                      size="default" 
                      effect="light"
                      class="type-tag"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
                <el-tag v-else :type="signatureData.dx==1 ? 'success' : 'danger'" size="default" effect="light">
                  {{ signatureData.dx==1 ? '已实名' : '未实名' }}
                </el-tag>
                <div v-if="signatureData.dxReason" class="status-reason">
                  <el-icon size="14"><Warning /></el-icon>
                  {{ signatureData.dxReason }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import getNoce from '../../../plugins/getNoce'

// 创建独立的 axios 实例
const instance = axios.create({
  baseURL: window.path.omcs,
  timeout: 10000
})

// 请求拦截器
instance.interceptors.request.use(
  async config => {
    const nonce = await getNoce.useNonce();
    config.headers['Once'] = nonce
    return config
  },
  error => {
    // console.log(error, 'error');
    
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response) {
      if (error.response.status == 403) {
        ElMessage.error(error.response.data.msg || error.response.data.message)
        localStorage.removeItem("nonce_list");
      }else{
        ElMessage.error(error.response.data.msg || error.response.data.message)
      }
    }
    // ElMessage.error('请求失败，请稍后重试')
    return Promise.reject(error)
  }
)

// 表单引用
const queryFormRef = ref(null)

// 查询表单
const queryForm = reactive({
  username: '',
  signature: ''
})

// 表单验证规则
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  signature: [
    { required: true, message: '请输入签名', trigger: 'blur' }
  ]
})

// 加载状态
const loading = ref(false)

// 签名详情数据
const signatureData = ref(null)

// 当前展开的运营商手风琴
const activeOperatorAccordions = ref(['mobile'])

// 查询时间
const queryTime = ref('')

// 组件挂载后执行
onMounted(() => {
  // 页面加载时不自动查询
})

// 查询按钮
const handleQuery = () => {
  queryFormRef.value.validate((valid) => {
    if (valid) {
      getSignatureDetail()
    } else {
      ElMessage.warning('请完善必填项')
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  queryFormRef.value.resetFields()
  signatureData.value = null
}

// 显示Mock数据（用于演示）
const showMockData = () => {
  // 记录查询时间
  const now = new Date()
  queryTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  
  // 自动填充表单
  queryForm.username = 'demo_user'
  queryForm.signature = '【测试签名】'
  
  // 直接显示mock数据
  signatureData.value = {
    username: queryForm.username || 'demo_user',
    signature: queryForm.signature || '【测试签名】',
    yd: 1,
    lt: 0, 
    dx: 1,
    ydReason: '',
    ltReason: '签名格式不符合规范要求',
    dxReason: '',
    ydAvalibleType: '验证码,行业,通知',
    ltAvalibleType: '验证码,行业,通知',
    dxAvalibleType: null,
    drainageInfo: getMockDrainageData()
  }
  
  ElMessage.success('已加载Mock数据用于演示')
}

// 获取签名详情数据
const getSignatureDetail = async () => {
  try {
    loading.value = true
    
    // 记录查询时间
    const now = new Date()
    queryTime.value = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    
    // 真实接口调用
    const params = {
      username: queryForm.username,
      signatures: queryForm.signature ? [queryForm.signature] : []
    }
    const res = await instance.post('openApi/querySignatureRealNameStatus', params)
    
    if (res?.code === 200) {
      if (res.data?.length > 0) {
        // 只展示第一条记录作为详情
        signatureData.value = res.data[0]
        
        // 如果接口返回的username为null，使用查询表单中的用户名
        if (!signatureData.value.username) {
          signatureData.value.username = queryForm.username
        }
        
        // 模拟添加引流信息数据（实际环境中应该从接口获取）
        signatureData.value.drainageInfo = getMockDrainageData()
        
        ElMessage.success('查询成功')
      } else {
        ElMessage.info('未找到相关签名信息')
        signatureData.value = null
      }
    } else {
      ElMessage.error(res?.msg || '获取签名详情失败')
      signatureData.value = null
    }
  } catch (error) {
    console.error('获取签名详情出错:', error)
    ElMessage.error('查询失败，请稍后重试')
    signatureData.value = null
  } finally {
    loading.value = false
  }
}

// 模拟引流信息数据（实际开发中应该从接口获取）
const getMockDrainageData = () => {
  return {
    // 按运营商分类的域名号码绑定对列表
    mobile: [
      {
        id: 1,
        submitTime: '2024-01-15 09:30:00',
        updateTime: '2024-01-18 14:20:00',
        overallStatus: 'approved', // pending, processing, approved, rejected, partial
        domain: {
          name: 'www.mobile-example.com',
          status: 'approved',
          reason: ''
        },
        phone: {
          number: '400-1001-001',
          status: 'approved',
          reason: ''
        }
      },
      {
        id: 2,
        submitTime: '2024-01-16 10:15:00',
        updateTime: '',
        overallStatus: 'processing',
        domain: {
          name: 'api.mobile-test.cn',
          status: 'processing',
          reason: ''
        },
        phone: {
          number: '400-1001-002',
          status: 'approved',
          reason: ''
        }
      },
      {
        id: 3,
        submitTime: '2024-01-14 16:45:00',
        updateTime: '2024-01-17 11:30:00',
        overallStatus: 'partial',
        domain: {
          name: 'mobile.mybusiness.org',
          status: 'rejected',
          reason: '域名与企业营业执照名称不符，请提供域名所有权证明文件'
        },
        phone: {
          number: '95001',
          status: 'approved',
          reason: ''
        }
      }
    ],
    unicom: [
      {
        id: 4,
        submitTime: '2024-01-17 13:20:00',
        updateTime: '',
        overallStatus: 'pending',
        domain: {
          name: 'shop.unicom-company.com',
          status: 'pending',
          reason: ''
        },
        phone: {
          number: '400-2002-001',
          status: 'pending',
          reason: ''
        }
      },
      {
        id: 5,
        submitTime: '2024-01-18 08:30:00',
        updateTime: '2024-01-19 16:15:00',
        overallStatus: 'approved',
        domain: {
          name: 'unicom.service.net',
          status: 'approved',
          reason: ''
        },
        phone: {
          number: '400-2002-002',
          status: 'approved',
          reason: ''
        }
      }
    ],
    telecom: [
      {
        id: 6,
        submitTime: '2024-01-19 14:30:00',
        updateTime: '2024-01-20 10:15:00',
        overallStatus: 'rejected',
        domain: {
          name: 'telecom.business.com',
          status: 'approved',
          reason: ''
        },
        phone: {
          number: '400-3003-001',
          status: 'rejected',
          reason: '该号码已被其他用户绑定使用，请选择其他号码'
        }
      },
      {
        id: 7,
        submitTime: '2024-01-20 11:00:00',
        updateTime: '',
        overallStatus: 'processing',
        domain: {
          name: 'app.telecom-demo.cn',
          status: 'processing',
          reason: ''
        },
        phone: {
          number: '400-3003-002',
          status: 'processing',
          reason: ''
        }
      },
      {
        id: 8,
        submitTime: '2024-01-21 09:45:00',
        updateTime: '2024-01-22 15:30:00',
        overallStatus: 'approved',
        domain: {
          name: 'portal.telecom-service.org',
          status: 'approved',
          reason: ''
        },
        phone: {
          number: '95533',
          status: 'approved',
          reason: ''
        }
      }
    ]
  }
}

// 获取审核状态显示文本
const getAuditStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '审核通过',
    3: '审核不通过'
  }
  return statusMap[status] || '未知状态'
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

// // 获取运营商状态显示文本
// const getOperatorStatusText = (status) => {
//   const statusMap = {
//     0: '未实名',
//     1: '已实名'
//   }
//   return statusMap[status] || '未知状态'
// }

// // 获取运营商状态类型
// const getOperatorStatusType = (status) => {
//   const typeMap = {
//     0: 'danger',
//     1: 'success'
//   }
//   return typeMap[status] || 'info'
// }

// 获取签名来源显示文本
const getSignatureTypeText = (type) => {
  const typeMap = {
    1: '企业名称',
    2: '事业单位',
    3: '商标',
    4: 'APP',
    5: '小程序',
    // 6: '公众号',
    7: '网站'
  }
  return typeMap[type] || ''
}

// 获取签名来源标签类型
const getSignatureTypeTag = (type) => {
  const typeMap = {
    1: 'primary',    // 企业名称
    2: 'info',       // 事业单位
    3: 'warning',    // 商标
    4: 'success',    // APP
    5: 'success',    // 小程序
    // 6: 'success',    // 公众号
    7: 'success'     // 网站
  }
  return typeMap[type] || 'info'
}

// 域名状态相关函数
const getDomainStatusText = (status) => {
  const statusMap = {
    'pending': '待审核',
    'processing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return statusMap[status] || '未知状态'
}

const getDomainStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getDomainCardClass = (status) => {
  return {
    'status-pending': status === 'pending',
    'status-processing': status === 'processing',
    'status-approved': status === 'approved',
    'status-rejected': status === 'rejected'
  }
}

// 号码状态相关函数
const getPhoneStatusText = (status) => {
  const statusMap = {
    'pending': '待审核',
    'processing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回'
  }
  return statusMap[status] || '未知状态'
}

const getPhoneStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getPhoneCardClass = (status) => {
  return {
    'status-pending': status === 'pending',
    'status-processing': status === 'processing',
    'status-approved': status === 'approved',
    'status-rejected': status === 'rejected'
  }
}

// 绑定状态相关函数
const getBindingStatusText = (status) => {
  const statusMap = {
    'pending': '待审核',
    'processing': '审核中',
    'approved': '全部通过',
    'rejected': '全部驳回',
    'partial': '部分通过'
  }
  return statusMap[status] || '未知状态'
}

const getBindingStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'approved': 'success',
    'rejected': 'danger',
    'partial': 'warning'
  }
  return typeMap[status] || 'info'
}

const getBindingCardClass = (bindingPair) => {
  return {
    'binding-pending': bindingPair.overallStatus === 'pending',
    'binding-processing': bindingPair.overallStatus === 'processing',
    'binding-approved': bindingPair.overallStatus === 'approved',
    'binding-rejected': bindingPair.overallStatus === 'rejected',
    'binding-partial': bindingPair.overallStatus === 'partial'
  }
}

// 获取指定运营商的绑定记录
const getOperatorBindings = (operator) => {
  if (!signatureData.value || !signatureData.value.drainageInfo) {
    return []
  }
  return signatureData.value.drainageInfo[operator] || []
}

// 获取指定运营商的绑定记录数量
const getOperatorBindingCount = (operator) => {
  const bindings = getOperatorBindings(operator)
  return bindings.length
}

// 获取运营商徽章类型
const getOperatorBadgeType = (operator) => {
  const bindings = getOperatorBindings(operator)
  if (bindings.length === 0) return 'info'
  
  // 统计各种状态的数量
  const statusCounts = bindings.reduce((acc, binding) => {
    acc[binding.overallStatus] = (acc[binding.overallStatus] || 0) + 1
    return acc
  }, {})
  
  // 如果有任何审核通过的，显示成功
  if (statusCounts.approved > 0) return 'success'
  // 如果有处理中的，显示警告
  if (statusCounts.processing > 0) return 'warning'
  // 如果有驳回的，显示危险
  if (statusCounts.rejected > 0) return 'danger'
  // 其他情况显示信息
  return 'info'
}

// 获取运营商状态概要文本
const getOperatorSummaryText = (operator) => {
  const bindings = getOperatorBindings(operator)
  if (bindings.length === 0) return '暂无报备记录'
  
  return `共${bindings.length}条记录`
}
</script>

<style scoped>
.signature-container {
  padding: 10px;
  max-width: 100%;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
  overflow-y: auto;
  height: 100vh;
}

.query-form {
  margin-bottom: 12px;
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.query-form:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.query-form :deep(.el-form-item) {
  margin-bottom: 12px;
}

.query-form :deep(.el-form-item__label) {
  width: 80px;
  text-align: right;
  font-weight: 500;
  color: #606266;
}

.query-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.query-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.query-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.form-buttons :deep(.el-button) {
  padding: 8px 18px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.form-buttons :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
}

.form-buttons :deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.signature-details {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
  min-height: 150px;
  transition: all 0.3s ease;
}

.signature-details:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.signature-card {
  margin-top: 10px;
}

.signature-card :deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.query-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(144, 147, 153, 0.2);
}

.query-time .el-icon {
  font-size: 12px;
}

/* 签名详情新样式 */
.signature-info-wrapper {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  padding: 20px;
}

.basic-info-card {
  flex: 1;
  min-width: 280px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #dee2e6;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-section {
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  border: 3px solid #fff;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.signature-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signature-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.signature-content {
  background: #fff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  border: 2px solid #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.operators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
  flex: 2;
  min-width: 320px;
}

.operator-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.operator-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: #ebeef5;
  transition: all 0.3s ease;
}

.operator-card.success::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.operator-card.danger::before {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.operator-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.operator-card.success:hover {
  border-color: #67c23a;
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.2);
}

.operator-card.danger:hover {
  border-color: #f56c6c;
  box-shadow: 0 8px 25px rgba(245, 108, 108, 0.2);
}

.operator-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.operator-card-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
}

.mobile-card-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.3);
}

.unicom-card-icon {
  background: linear-gradient(135deg, #f56c6c, #e85656);
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.3);
}

.telecom-card-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.3);
}

.operator-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.operator-status-content {
  text-align: center;
}

.available-types {
  text-align: center;
}

.type-header {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;
}

.type-tag {
  margin: 0;
  border-radius: 12px;
  font-weight: 500;
  background: #f0f9eb !important;
  border-color: #c2e7b0 !important;
  color: #67c23a !important;
}

.status-reason {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
  margin-top: 8px;
  padding: 6px 10px;
  background: #fef0f0;
  border-radius: 6px;
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.4;
}

:deep(.el-tag) {
  margin: 0 5px;
  padding: 4px 12px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-empty__description) {
  color: #909399;
}

/* 引流信息卡片样式 */
.drainage-card {
  margin-top: 15px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.drainage-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.drainage-card :deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 运营商手风琴样式 */
.operator-accordion {
  margin-top: 20px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.operator-accordion :deep(.el-collapse-item) {
  border-bottom: 1px solid #e4e7ed;
}

.operator-accordion :deep(.el-collapse-item:last-child) {
  border-bottom: none;
}

.operator-accordion :deep(.el-collapse-item__header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  height: auto;
  line-height: 1.5;
}

.operator-accordion :deep(.el-collapse-item__header:hover) {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.operator-accordion :deep(.el-collapse-item__header.is-active) {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-bottom-color: #2196f3;
}

.operator-accordion :deep(.el-collapse-item__wrap) {
  border-bottom: none;
}

.operator-accordion :deep(.el-collapse-item__content) {
  padding: 0;
}

.accordion-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

.operator-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.operator-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  flex-shrink: 0;
}

.mobile-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  color: #fff;
}

.unicom-icon {
  background: linear-gradient(135deg, #f56c6c, #e85656);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
  color: #fff;
}

.telecom-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  color: #fff;
}

.operator-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.operator-badge {
  margin-left: 8px;
}

.operator-summary {
  text-align: right;
}

.summary-text {
  font-size: 14px;
  color: #606266;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.operator-content {
  padding: 20px;
  background: #fafbfc;
}

/* 固定宽度的绑定网格 */
.binding-grid-fixed {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 20px;
  align-items: start;
}

/* 固定宽度的绑定卡片 */
.binding-card-fixed {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.binding-card-fixed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  transition: all 0.3s ease;
}

/* 绑定状态样式 */
.binding-card-fixed.binding-pending::before {
  background: linear-gradient(90deg, #909399, #a6a9ad);
}

.binding-card-fixed.binding-processing::before {
  background: linear-gradient(90deg, #409eff, #66b1ff);
}

.binding-card-fixed.binding-approved::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.binding-card-fixed.binding-rejected::before {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.binding-card-fixed.binding-partial::before {
  background: linear-gradient(90deg, #e6a23c, #f0a932);
}

.binding-card-fixed:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.drainage-content {
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding: 12px 0;
  border-bottom: 2px solid #f0f2f5;
}

.section-title .el-icon {
  color: #409eff;
}

.binding-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

/* 绑定卡片样式 */
.binding-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.binding-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  transition: all 0.3s ease;
}

/* 绑定状态样式 */
.binding-card.binding-pending::before {
  background: linear-gradient(90deg, #909399, #a6a9ad);
}

.binding-card.binding-processing::before {
  background: linear-gradient(90deg, #409eff, #66b1ff);
}

.binding-card.binding-approved::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.binding-card.binding-rejected::before {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.binding-card.binding-partial::before {
  background: linear-gradient(90deg, #e6a23c, #f0a932);
}

.binding-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.binding-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.binding-time {
  font-size: 12px;
  color: #909399;
}

.pair-item {
  background: #fafbfc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 8px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.pair-item:hover {
  background: #f5f7fa;
  border-color: #dcdfe6;
}

.pair-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.pair-label {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.pair-content {
  margin-left: 28px;
}

.pair-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  word-break: break-all;
}

.pair-reason {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 6px 10px;
  border-radius: 6px;
  line-height: 1.4;
}

.binding-connector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 12px 0;
  position: relative;
}

.connector-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #e6a23c, #f0a932);
  border-radius: 1px;
}

.connector-icon {
  position: absolute;
  background: #fff;
  border: 2px solid #e6a23c;
  border-radius: 50%;
  padding: 4px;
  color: #e6a23c;
}

.binding-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin: 16px 0 8px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 8px;
  color: #409eff;
  font-size: 13px;
  font-weight: 500;
}

.binding-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
  text-align: center;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}


/* 时间线图标样式调整 */
:deep(.el-timeline-item__icon) {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-timeline-item__icon .el-icon) {
  font-size: 12px;
}

/* 标签样式优化 */
.step-details .el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 4px 8px;
  font-weight: 500;
}

/* 响应式设计 */
@media screen and (max-width: 767px) {
  .drainage-content {
    padding: 15px;
  }
  
  .section-title {
    font-size: 15px;
  }

  .binding-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .binding-card {
    padding: 16px;
  }

  .pair-item {
    padding: 12px;
  }

  .pair-name {
    font-size: 14px;
  }

  .pair-content {
    margin-left: 24px;
  }

  .binding-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .binding-time {
    order: -1;
  }

  /* 移动端签名详情优化 */
  .signature-info-wrapper {
    flex-direction: column;
    padding: 15px;
    gap: 15px;
  }

  .basic-info-card {
    min-width: unset;
    padding: 15px;
  }

  .user-info {
    gap: 12px;
  }

  .avatar-section {
    width: 50px;
    height: 50px;
  }

  .username {
    font-size: 16px;
  }

  .signature-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .signature-content {
    font-size: 13px;
    padding: 5px 10px;
  }

  .operators-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    min-width: unset;
  }

  .operator-card {
    padding: 12px;
  }

  .operator-name {
    font-size: 14px;
  }

  .status-reason {
    font-size: 11px;
    padding: 5px 8px;
  }

  /* 移动端手风琴优化 */
  .operator-accordion :deep(.el-collapse-item__header) {
    padding: 12px 15px;
    font-size: 15px;
  }

  .accordion-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding-right: 0;
  }

  .operator-info {
    width: 100%;
  }

  .operator-summary {
    width: 100%;
    text-align: left;
  }

  .summary-text {
    font-size: 12px;
    padding: 4px 8px;
    display: inline-block;
  }

  .operator-content {
    padding: 15px;
  }

  .binding-grid-fixed {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .binding-card-fixed {
    padding: 15px;
    min-height: 280px;
  }

  .operator-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }

  .operator-name {
    font-size: 16px;
  }

  /* 移动端查询时间优化 */
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .query-time {
    font-size: 11px;
    padding: 3px 6px;
  }
}

@media screen and (max-width: 991px) and (min-width: 768px) {
  /* 平板端优化 */
  .binding-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
  
  .binding-card {
    padding: 18px;
  }
}

@media screen and (min-width: 768px) {
  .signature-container {
    padding: 30px;
  }

  .query-form {
    padding: 25px;
  }

  .signature-details {
    padding: 25px;
    min-height: 300px;
  }

  :deep(.el-descriptions) {
    padding: 20px;
  }

  :deep(.el-descriptions__content) {
    padding: 15px 20px;
  }

  .card-header {
    font-size: 18px;
  }

  .drainage-card {
    margin-top: 25px;
  }

  .drainage-section {
    margin-bottom: 35px;
  }

  .section-title {
    font-size: 17px;
    margin-bottom: 20px;
    padding: 12px 0;
  }

  .step-title {
    font-size: 16px;
  }
}
</style>
