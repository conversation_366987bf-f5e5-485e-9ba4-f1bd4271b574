<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="系统审核" :name="5">
          <template v-if="sumCoun.auditCount > 0" #label>
            <el-badge :value="sumCoun.auditCount" :max="99999999" :show-zero="false" class="item">
              <span>系统审核</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="动态IP管控" :name="1">
          <template v-if="sumCoun.ipCount > 0" #label>
            <el-badge :value="sumCoun.ipCount" :max="99999999" :show-zero="false" class="item">
              <span>动态IP管控</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="日提交数量管控" :name="4">
          <template v-if="sumCoun.submitCount > 0" #label>
            <el-badge :value="sumCoun.submitCount" :max="99999999" :show-zero="false" class="item">
              <span>日提交数量管控</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="发送时间管控" :name="2">
          <template v-if="sumCoun.timeCount > 0" #label>
            <el-badge :value="sumCoun.timeCount" :max="99999999" :show-zero="false" class="item">
              <span>发送时间管控</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="发送数量管控" :name="3">
          <template v-if="sumCoun.numCount > 0" #label>
            <el-badge :value="sumCoun.numCount" :max="99999999" :show-zero="false" class="item">
              <span>发送数量管控</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="黑模版管控" :name="6">
          <template v-if="sumCoun.bcCount > 0" #label>
            <el-badge :value="sumCoun.bcCount" :max="99999999" :show-zero="false" class="item">
              <span>黑模版管控</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="内容号码管控" :name="7">
          <template v-if="sumCoun.contentNumberCount > 0" #label>
            <el-badge :value="sumCoun.contentNumberCount" :max="99999999" :show-zero="false" class="item">
              <span>内容号码管控</span>
            </el-badge>
          </template>
        </el-tab-pane>
        <el-tab-pane label="已驳回信息" name="已驳回信息"></el-tab-pane>
      </el-tabs>
      <div>
        <el-form :inline="true" :model="sensitiveCondition" class="demo-form-inline" ref="sensitiveCondition"
          label-width="70px">
          <el-form-item label="用户名称" prop="username">
            <el-input v-model="sensitiveCondition.username" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input v-model="sensitiveCondition.content" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="排除用户" prop="excludeUsername">
            <el-input v-model="sensitiveCondition.excludeUsername" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="提交IP" prop="ip">
            <el-input v-model="sensitiveCondition.ip" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="通道组" prop="channelGroup">
            <el-select v-model="sensitiveCondition.channelGroup" filterable clearable placeholder="请选择" class="input-w">
              <el-option v-for="(item, index) in channelGroupName" :key="index" :label="item.channelGroupName"
                :value="item.channelGroupId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="内容标识" prop="contentFeature">
            <el-select v-model="sensitiveCondition.contentFeature" placeholder="请选择" clearable class="input-w">
              <el-option label="不包含号码+链接" value="-1"></el-option>
              <!-- <el-option  label="包含号码" value="1"></el-option>
              <el-option  label="包含链接" value="2"></el-option> -->
              <el-option label="包含号码+链接" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态" prop="auditStatus">
            <el-select v-model="sensitiveCondition.auditStatus" placeholder="请选择" clearable class="input-w">
              <el-option v-if="activeName != '已驳回信息'" label="待审核" value="1"></el-option>
              <el-option v-if="activeName != '已驳回信息'" label="审核通过" value="2"></el-option>
              <el-option v-if="activeName == '已驳回信息'" label="审核不通过" value="3"></el-option>
              <el-option v-if="activeName != '已驳回信息'" label="自动审核通过" value="5"></el-option>
              <el-option v-if="activeName == '已驳回信息'" label="自动审核失败" value="6"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户类型" prop="content">
            <el-select v-model="sensitiveCondition.roleId" clearable placeholder="不限" class="input-w">
              <el-option label="不限" value=""></el-option>
              <el-option label="管理商" value="12"></el-option>
              <el-option label="子用户" value="13"></el-option>
              <el-option label="终端" value="14"></el-option>
              <el-option label="线上终端" value="22"></el-option>
            </el-select>
            <!-- <el-input v-model="sensitiveCondition.roleId" placeholder="" class="input-w"></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="提交时间"  prop="time1">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time1"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande1"
                            :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain @click="sensitiveQuery()">查询</el-button>
            <el-button type="primary" plain @click="sensitiveReload('sensitiveCondition')">重置</el-button>
            <el-button type="primary" @click="ConditionalAudit()">条件审核</el-button>
          </div>
        </el-form>
      </div>
      <div class="sensitive-table" style="margin-top: 10px">
        <div style="
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 12px;
          ">
          <div></div>
          <div style="display: flex; align-items: center">
            <div style="margin-right: 10px;color: #606266;font-size: 14px"><span>当前页</span><span
                style="margin: 0 5px;">{{ tableDataObj.tableData.length }}</span><span>条；</span></div>
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="sensitiveConObj.currentPage" :page-size="sensitiveConObj.pageSize"
              :page-sizes="[100, 300, 500]" layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total">
            </el-pagination>
          </div>
        </div>

        <!-- 表格和分页开始 -->
        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <div class="sensitive">
              <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">黑词审核列表</span> -->
              <!-- <el-button type="primary"  @click="establishSig()">创建签名</el-button> -->
              <el-button type="primary" @click="handleAdopt1()"
                v-if="remarkObj.formData.idArr.length != 0">批量通过</el-button>
              <el-button type="danger" @click="sensitiveBatchSet()"
                v-if="remarkObj.formData.idArr.length != 0">批量不通过</el-button>
              <el-button type="primary" @click="batchChannelGroup()"
                v-if="remarkObj.formData.idArr.length != 0">批量切换通道</el-button>
              <el-button type="primary" v-if="remarkObj.formData.idArr.length != 0"
                @click="handeExtract()">提取模板</el-button>
            </div>
          </template>
          <template #tools>
            <vxe-button style="margin-right: 10px;" icon="vxe-icon-refresh" @click="scrub()"></vxe-button>
          </template>
        </vxe-toolbar>
        <vxe-table ref="tableRef" id="smsTestContentTable" border height="600" v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px"
          :custom-config="{ storage: true }" :column-config="{ resizable: true }" :row-config="{ isHover: true }"
          :virtual-y-config="{
            enabled: false,
          }" :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">
          <vxe-column type="checkbox" width="45"></vxe-column>
          <vxe-column field="用户名" title="用户名" width="170">
            <template #default="{ row }">
              <div class="spanColor">
                <el-tooltip effect="dark" placement="bottom">
                  <template #content>
                    <a style="
                        color: #fff;
                        margin-right: 5px;
                        text-decoration: underline;
                      " :href="'https://www.tianyancha.com/search?key=' + row.compName
                        " target="_blank" rel="noopener noreferrer">
                      {{ row.compName }}
                    </a>
                  </template>
                  <div style="display: flex; align-items: center">
                    <i v-if="row.roleId == '14'" class="iconfont icon-zhong"
                      style="font-size: 20px; color: #23227e; cursor: pointer"></i>
                    <i v-if="row.roleId == '12'" class="iconfont icon-guan"
                      style="font-size: 20px; color: #812c7a; cursor: pointer"></i>
                    <i v-if="row.roleId == '13'" class="iconfont icon-zimaliu"
                      style="font-size: 20px; color: #812c7a; cursor: pointer"></i>
                    <i v-if="row.roleId == '22'" class="iconfont icon-xianshang"
                      style="font-size: 20px; color: #3a6b1e; cursor: pointer"></i>
                    <span :style="row.mobCount > 99
                        ? 'background:#EDF4FC;color: red;cursor: pointer;'
                        : ''
                      ">
                      {{ row.username }}
                    </span>
                  </div>
                </el-tooltip>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="发送内容" title="发送内容" min-width="400">
            <template #default="{ row }">
              <div class="spanColor">
                <el-tooltip effect="dark" :content="'字数：' + row.contentSize" placement="top-end">
                  <span v-html="row.content"></span>
                </el-tooltip>
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="字数" title="字数" width="70">
            <template #default="{ row }">
              <div class="spanColor">
                <span>{{ row.contentSize }}</span>
              </div>
            </template>
          </vxe-column> -->
          <vxe-column field="号码数" title="号码数" width="90">
            <!-- <template slot="header">
              <span>号码数</span>
              <el-tooltip effect="dark" placement="top">
                <div slot="content">三天有效</div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </template> -->
            <template #header="{ column, rowIndex }">
              <div style="display: flex; align-items: center">
                <span>号码数</span>
                <el-tooltip effect="dark" content="三天有效" placement="top">
                  <el-icon style="color: #409eff; margin-left: 2px; font-size: 16px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template #default="{ row }">
              <span :style="row.mobCount > 99 ? 'color:#f56c6c' : ''">{{
                row.mobCount
                }}</span>
            </template>
          </vxe-column>
          <!-- <vxe-column field="智能检测" title="智能检测" width="90">
            <template #default="{ row }">
              <div>
                <i
                  class="iconfont icon-anquan"
                  v-if="row.aiCheck == 'safe'"
                  style="color: #67c23a"
                ></i>
                <el-tooltip
                  v-else-if="row.aiCheck == 'unsafe'"
                  class="item"
                  effect="light"
                  content="智能检测"
                  placement="bottom"
                >
                  <template #content>
                    <div v-if="row.ai">
                      <div
                        v-for="(item, index) in row.ai.split(',')"
                        :key="index"
                      >
                        <span style="color: red">
                          {{ item }}
                          <i
                            class="iconfont icon-buanquanshijian"
                            style="font-size: 20px"
                          ></i>
                        </span>
                      </div>
                    </div>
                  </template>
                  <i
                    class="iconfont icon-buanquanshijian"
                    style="color: #f56c6c; cursor: pointer"
                  ></i>
                </el-tooltip>
                <el-tooltip
                  v-else-if="row.aiCheck == 'unknown'"
                  class="item"
                  effect="dark"
                  content="未知"
                  placement="bottom"
                >
                  <i
                    class="iconfont icon-weixian1"
                    style="color: #e6a23c"
                    @click="handelAicheck(row)"
                  ></i>
                </el-tooltip>
              </div>
            </template>
          </vxe-column> -->
          <vxe-column field="提示" title="提示" width="90">
            <template #default="{ row }">
              <div class="spanColor">{{ row.prompt }}</div>
            </template>
          </vxe-column>
          <vxe-column field="敏感词标签" title="敏感词标签" width="145">
            <template #default="{ row }">
              <el-tag :disable-transitions="true" size="default" style="color: #fff" :color="bgColor[index]"
                v-for="(item, index) in row.labelName" :key="index">{{ item }}</el-tag>
            </template>
          </vxe-column>
          <vxe-column field="原始通道组" title="原始通道组" width="130">
            <template #default="{ row }">
              <div class="spanColor" v-for="(item, index) in channelGroupName" :key="index">
                <span v-if="item.channelGroupId == row.channelGroup">
                  {{ item.channelGroupName }}
                </span>
              </div>
            </template>
          </vxe-column>
          <vxe-column v-if="KZNum != '1'" field="快照预览" title="快照预览" width="70">
            <template #default="{ row }">
              <div class="demo-image__preview" style="cursor: pointer" @click="KZCK(row.auditPic)">
                <el-image style="width: 50px; height: 50px" :z-index="9999" :src="row.auditPic ? imgPath + row.auditPic.split(',')[0] : ''
                  " :preview-src-list="KFck.srcList" :append-to-body="true" :preview-teleported="true">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="提交IP" title="提交IP" width="140">
            <template #default="{ row }">
              <div style="display: flex; align-items: center">
                <div>{{ row.ip }}</div>
                <div @click="handleIP(row)">
                  <i class="iconfont icon-xitongshezhi1" style="color: #409eff;margin-left: 5px;cursor: pointer"></i>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="提交时间" title="提交时间" width="100">
            <template #default="{ row }">
              <p v-if="row.time">
                {{ $parseTime(row.time, "{y}-{m}-{d}") }}
              </p>
              <p v-if="row.time">
                {{ $parseTime(row.time, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>
          <vxe-column v-if="sensitiveConObj.auditStatus != 1" field="审核人" title="审核人" width="90">
            <template #default="{ row }">
              <div class="spanColor">
                {{ row.auditUserName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column v-if="sensitiveConObj.auditStatus != 1" field="审核时间" title="审核时间" width="90">
            <template #default="{ row }">
              <div class="spanColor">
                {{ row.auditTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180">
            <template #default="{ row, rowIndex }">
              <el-tooltip v-if="row.auditStatus == '1'" class="item" effect="dark" content="审核通过" placement="bottom">
                <span style="color: #409eff; font-size: 22px; cursor: pointer" @click="handleAdopt3(rowIndex, row)">
                  <el-icon>
                    <SuccessFilled />
                  </el-icon>
                </span>
              </el-tooltip>
              <el-tooltip v-if="row.auditStatus == '3' && row.today == '1'" class="item" effect="dark" content="驳回审核"
                placement="bottom">
                <span style="
                    color: #f56c6c;
                    font-size: 22px;
                    cursor: pointer;
                    margin-left: 18px;
                  " @click="handleAdopt2(rowIndex, row)">
                  <el-icon>
                    <CircleCloseFilled />
                  </el-icon>
                </span>
              </el-tooltip>
              <el-tooltip v-if="row.auditStatus == '2' && row.today == '1'" class="item" effect="dark" content="驳回审核"
                placement="bottom">
                <span style="
                    color: #f56c6c;
                    font-size: 22px;
                    cursor: pointer;
                    margin-left: 18px;
                  " @click="turnDown(rowIndex, row)">
                  <el-icon>
                    <CircleCloseFilled />
                  </el-icon>
                </span>
              </el-tooltip>
              <el-tooltip v-if="row.auditStatus == '1'" class="item" effect="dark" content="审核不通过" placement="bottom">
                <span style="
                    color: #f56c6c;
                    font-size: 22px;
                    margin-left: 18px;
                    cursor: pointer;
                  " @click="handleReject(row)">
                  <el-icon>
                    <CircleCloseFilled />
                  </el-icon>
                </span>
              </el-tooltip>
              <el-tooltip v-if="row.auditStatus == '1'" class="item" effect="dark" content="切换通道" placement="bottom">
                <span style="
                    color: #409eff;
                    font-size: 22px;
                    cursor: pointer;
                    margin-left: 18px;
                  " @click="batchChannelGroups(row)">
                  <el-icon>
                    <Sort />
                  </el-icon>
                </span>
              </el-tooltip>
              <el-tooltip v-if="activeName == '7'" class="item" effect="dark" content="设置白名单" placement="bottom">
                <span style="
                    color: #67c23a;
                    font-size: 22px;
                    cursor: pointer;
                    margin-left: 18px;
                  " @click="setMobileWhitelist(row)">
                  <el-icon>
                    <UserFilled />
                  </el-icon>
                </span>
              </el-tooltip>
            </template>
          </vxe-column>
        </vxe-table>

        <!--分页-->
        <!-- <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              slot="pagination"
              style="background: #fff; padding: 10px 0; text-align: right"
            >
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="sensitiveConObj.currentPage"
                :page-size="sensitiveConObj.pageSize"
                :page-sizes="[100, 500, 1000]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.total"
              >
              </el-pagination>
            </el-col> -->
      </div>
      <!-- 弹窗（查看图片） -->
      <el-dialog :title="titleS" v-model="imagesshow" width="600px" :close-on-click-modal="false">
        <template>
          <el-carousel :interval="5000" arrow="always" :autoplay="false" style="height: 560px">
            <el-carousel-item style="height: 560px" v-for="(item, index) in imgUrl" :key="index">
              <img style="width: 100%; max-height: 580px" :src="item" alt="" />
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-dialog>
      <!-- 弹框 （审核 通过） -->
      <el-dialog :title="title" v-model="remarkshow" width="600px" :close-on-click-modal="false" :show-close="false">
        <el-form :model="remarkObj.formData" :rules="remarkObj.formRule" ref="remarkObjs" label-width="100px"
          style="padding: 0 28px">
          <el-form-item label="不通过原因:" prop="labelCheckList">
            <!-- <el-checkbox-group v-model="remarkObj.formData.labelCheckList">
              <el-checkbox
                :value="item.id"
                v-for="(item, index) in labelCheckListArry"
                :key="index"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group> -->
            <el-checkbox-group v-model="remarkObj.formData.labelCheckList">
              <el-checkbox v-for="(item, index) in labelCheckListArry" :key="index" :label="item.name"
                :value="item.id" />
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input type="textarea" :disabled="disabledinput" rows="4" resize="none"
              v-model="remarkObj.formData.auditReason"></el-input>
          </el-form-item>
          <el-upload class="upload-demo" :action="actionUrl" :headers="token" :file-list="fileList1"
            :on-remove="handleRemove1" :on-success="handleAvatarSuccess1" :before-upload="beforeAvatarUpload1"
            list-type="picture">
            <el-button size="default" type="primary">点击上传</el-button>
            <template v-slot:tip>
              <div class="el-upload__tip">请上传图片</div>
            </template>
          </el-upload>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button class="footer-center-button" v-if="operationFlag == true" @click="remarkshow = false">取
              消</el-button>
            <el-button class="footer-center-button" v-if="operationFlag == true" type="primary"
              @click="handelAlertdd('remarkObjs')">确 认</el-button>
            <el-button type="primary" v-if="operationFlag == false" :loading="true">操作进行中，请稍等</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 切换通道 -->
      <el-dialog title="切换通道组" v-model="SwitchChannelGroup" width="530px" class="TemDialog"
        :close-on-click-modal="false">
        <el-form :model="SwitchChannelForm" :rules="SwitchChannelRules" label-width="100px" style="padding: 10px 30px"
          ref="refForm">
          <!-- <el-form-item label="扩展号" prop="ext">
                <el-input
                  v-model="SwitchChannelForm.ext"
                  class="input-w"
                ></el-input>
                <span style="color: #f56c6c; font-size: 12px">不可随意填写</span>
              </el-form-item> -->
          <el-form-item label="选择通道" prop="aisle">
            <el-select v-model="SwitchChannelForm.aisle" placeholder="请选择" clearable class="input-w">
              <el-option label="人工通道组" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道组名称" prop="channelGroupId">
            <el-select v-model="SwitchChannelForm.channelGroupId" placeholder="请选择" filterable clearable
              class="input-w">
              <el-option v-for="(item, index) in channelGroupName" :key="index" :label="item.channelGroupName"
                :value="item.channelGroupId"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="移动" prop="channelYd">
                             <el-select v-model="SwitchChannelForm.channelYd" placeholder="请选择" filterable clearable class="input-w">
                                <el-option v-for="(item,index) in channelYd" :key='index' :label="'移动通道:'+item[1]+' 单价:'+item[2]" :value="item[0]"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="联通" prop="channelLt">
                             <el-select v-model="SwitchChannelForm.channelLt" placeholder="请选择" filterable clearable class="input-w">
                                <el-option v-for="(item,index) in channelLt" :key='index' :label="'联通通道:'+item[1]+' 单价:'+item[2]" :value="item[0]"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="电信" prop="channelDx">
                             <el-select v-model="SwitchChannelForm.channelDx" placeholder="请选择" filterable clearable class="input-w">
                                <el-option v-for="(item,index) in channelDx" :key='index' :label="'电信通道:'+item[1]+' 单价:'+item[2]" :value="item[0]"></el-option>
                            </el-select>
                        </el-form-item> -->
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="confirmSwitchChannel()">确 定</el-button>
            <el-button @click="SwitchChannelGroup = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 上传图片 -->
      <el-dialog title="测试短信发送内容审核" v-model="uploadImage" width="35%" class="TemDialog" :close-on-click-modal="false">
        <div style="display: flex">
          <span>上传图片：</span>
          <el-upload class="upload-demo" :action="actionUrl" :headers="token" :file-list="fileList1"
            :on-remove="handleRemove1" :on-success="handleAvatarSuccess1" :before-upload="beforeAvatarUpload1"
            list-type="picture">
            <el-button size="default" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">请上传图片</div> -->
          </el-upload>
        </div>

        <div style="margin: 10px 0">请确认以下注意事项：</div>
        <!-- <span style="color:red;font-size:18px;margin-right:5px">*</span> -->
        <el-checkbox-button style="border: 1px solid #eee; margin-right: 5px" v-model="linkFlag" label="链接"
          border></el-checkbox-button>
        <!-- <span style="color:red;font-size:18px;margin-right:5px">*</span> -->
        <el-checkbox-button style="border: 1px solid #eee; margin-right: 5px" v-model="sceneFlag" label="场景"
          border></el-checkbox-button>
        <!-- <span style="color:red;font-size:18px;margin-right:5px">*</span> -->
        <el-checkbox-button style="border: 1px solid #eee; margin-right: 5px" v-model="violateFlag" label="违禁词"
          border></el-checkbox-button>
        <template #footer>
          <div class="dialog-footer">
            <el-button :disabled="linkFlag && sceneFlag && violateFlag ? false : true" type="primary"
              @click="uploadReview">确 定</el-button>
            <el-button @click="uploadImage = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 条件审核 -->
      <el-dialog title="条件审核" v-model="ConditionalReplacement" width="600px" :close-on-click-modal="false"
        :before-close="handleClose1">
        <div>
          <span style="padding: 10px 0; font-weight: 600">审核条件</span>
          <span>共:（</span><span style="color: red; font-size: 13px">{{
            tableDataObj.total
            }}</span>）条
        </div>
        <div>
          <div>
            <div>
              <el-input readonly v-model="sensitiveConObj.username">
                <template v-slot:prepend>用户名:</template>
              </el-input>
            </div>
            <div>
              <el-input readonly v-model="sensitiveConObj.content">
                <template v-slot:prepend>内&#12288;容:</template>
              </el-input>
            </div>
          </div>
          <div>
            <el-form :model="ConditionForm" :rules="ConditionFormRulef" ref="ConditionFormRulef" label-width="124px"
              :inline="true">
              <div>
                <el-form-item label="审核操作" prop="auditStatus">
                  <el-radio-group v-model="ConditionForm.auditStatus">
                    <el-radio label="2">通过</el-radio>
                    <el-radio label="3">不通过</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="通道组" prop="channelGroupId">
                  <el-select v-model="ConditionForm.channelGroupId" placeholder="请选择" class="input-w">
                    <el-option v-for="(item, index) in channelGroupName" :key="index" :label="item.channelGroupName"
                      :value="item.channelGroupId"></el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="divWidth" v-if="ConditionForm.auditStatus == 3" style="width: 100%">
                <el-form-item label="审核不通过原因" prop="auditReason">
                  <el-input type="textarea" v-model="ConditionForm.auditReason" resize="none" class="input-w-3"
                    rows="4"></el-input>
                </el-form-item>
              </div>
              <div class="divWidth" style="width: 100%">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" v-model="ConditionForm.remark" resize="none" class="input-w-3"
                    rows="4"></el-input>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="ConditionalReplacement = false">取 消</el-button>
            <el-button type="primary" @click="determine('ConditionFormRulef')">确 定</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 条件审核 -->
      <el-dialog title="内容查看" v-model="trueFlog" width="530px" class="TemDialog" :close-on-click-modal="false">
        <div class="spanColor" v-html="trueSize"></div>
      </el-dialog>
      <!-- 相似度 -->
      <SimilarTemlpate :dialogVisibles="dialogVisible" :tableDatas="extractObj.tableData"
        :similarTemplateLists="extractObj.similarTemplateList" @handleClose="handleCloses">
      </SimilarTemlpate>
      <!-- 设置白名单弹窗 -->
      <el-dialog title="设置号码白名单" v-model="whitelistDialog" width="600px" :close-on-click-modal="false">
        <el-form :model="whitelistForm" :rules="whitelistRules" ref="whitelistFormRef" label-width="100px"
          style="padding: 0 28px">
          <el-form-item label="有效天数:" prop="days">
            <el-input-number v-model="whitelistForm.days" :min="1" :max="999" placeholder="请输入有效天数"
              style="width: 100%"></el-input-number>
          </el-form-item>
          <el-form-item label="手机号码:" prop="mobile">
            <el-input type="textarea" v-model="whitelistForm.mobile" :rows="6" resize="none"
              placeholder="请输入手机号码，多个号码用英文逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input type="textarea" v-model="whitelistForm.remark" :rows="4" resize="none"
              placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button @click="whitelistDialog = false">取 消</el-button>
            <el-button type="primary" @click="confirmSetWhitelist('whitelistFormRef')">确 认</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- <commonIp v-if="commonIpFlag" :showVisible="commonIpFlag" :userId="userId"  @handleClose="handleClosesIp"></commonIp> -->
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import FileUpload from "@/components/publicComponents/FileUpload.vue"; //文件上传
import TableTem from "@/components/publicComponents/TableTem.vue";
import UserLists from "@/components/publicComponents/userList.vue";
import SimilarTemlpate from "@/components/publicComponents/similarTemlpate.vue"; //相似度
import commonIp from "@/components/publicComponents/commonIp.vue";
import { addIp } from '../../../utils/commonIp'
import funCommon from "@/assets/js/common.js"; //公共方法
// 引入时间戳转换
import { formatDate } from "@/assets/js/date.js";

export default {
  components: {
    TableTem,
    FileUpload,
    UserLists,
    SimilarTemlpate,
    commonIp,
  },
  name: "BlackWordReview",
  data() {
    var ext = (rule, value, callback) => {
      if (value == "" || value == null) {
        callback();
      } else {
        if (!/^\d{2,15}$/.test(value)) {
          return callback(new Error("请输入2-15位扩展号"));
        } else {
          callback();
        }
      }
    };
    return {
      activeName: 5,
      actionUrl: window.path.cpus + "v3/file/upload",
      commonIpFlag: false, //是否是动态IP管控
      userId: "", //用户id
      variableFlag: false,
      linkFlag: false,
      sceneFlag: false,
      violateFlag: false,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime();
          if (maxDate) {
            this.pickerMinDate = "";
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
            return false;
          }
          if (this.pickerMinDate !== "") {
            const day30 = 2 * 24 * 3600 * 1000;
            let maxTime = this.pickerMinDate;
            if (maxTime > Date.now()) {
              maxTime = Date.now();
            }
            const minTime = this.pickerMinDate - day30;
            return time.getTime() < minTime || time.getTime() > maxTime;
          }
          return time.getTime() > Date.now();
        },
      },
      flge: false,
      checked: true,
      height: 550,
      rowHeight: 50,
      id: "",
      adoptFlag: '',
      turnflag: '',
      //条件审核
      ConditionForm: {
        auditStatus: "2",
        auditReason: "",
        channelGroupId: "",
        productId: "1",
        remark: "",
      },
      ConditionFormRulef: {
        auditReason: [
          {
            required: true,
            message: "请填写不通过原因",
            trigger: ["change", "blur"],
          },
        ],
      },
      KZNum: "1",
      KFck: {
        url: "",
        srcList: [window.path.imgU],
      },
      trueSize: "",
      trueFlog: false,
      auditTime: "",
      turnStatus: false, //是否驳回操作
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      ConditionalReplacement: false, //条件审核弹出层
      remarkshow: false,
      labelCheckListArry: [],
      channelGroupName: [],
      // 切换通道组
      SwitchChannelGroup: false,
      // 切换通道组数据
      SwitchChannelForm: {
        auditStatus: 2,
        // ext: "",
        aisle: "1",
        channelGroupId: "",
        // channelYd:'',
        // channelLt:'',
        // channelDx:'',
      },
      // 移动
      channelYd: [],
      // 联通
      channelLt: [],
      // 电信
      channelDx: [],
      // 切换通道组验证
      SwitchChannelRules: {
        ext: [{ required: false, validator: ext, trigger: "blur" }],
        aisle: [{ required: true, message: "请选择通道", trigger: "change" }],
        channelGroupId: [
          { required: true, message: "请选择通道组名称", trigger: "change" },
        ],
        channelYd: [
          { required: true, message: "请选择移动通道", trigger: "change" },
        ],
        channelLt: [
          { required: true, message: "请选择联通通道", trigger: "change" },
        ],
        channelDx: [
          { required: true, message: "请选择电信通道", trigger: "change" },
        ],
      },
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: "",
          idArr: [],
          labelCheckList: [],
        },
        formRule: {
          labelCheckList: [
            {
              type: "array",
              required: true,
              message: "请选择拒绝原因",
              trigger: "change",
            },
          ],
        },
      },
      nameover: "",
      userFlag: false,
      idArrs: [], // 驳回的id
      uploadImage: false,
      selectionflag: true, //赋值批量检测号码数
      token: {
        Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
      },
      fileList1: [],
      fileImgs: [],
      rowId: "",
      fileauditpic: [],
      title: "",
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        username: "",
        roleId: "",
        content: "",
        // prompt: "系统审核",
        auditType: 5,
        ip: "",
        excludeUsername: "",
        channelGroup: "",
        time1: [],
        auditStatus: "1",
        contentFeature: "",
        beginTime: "",
        endTime: "",
        currentPage: 1,
        pageSize: 300,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        username: "",
        content: "",
        // prompt: "系统审核",
        auditType: 5,
        ip: "",
        excludeUsername: "",
        channelGroup: "",
        time1: [],
        auditStatus: "1",
        contentFeature: "",
        beginTime: "",
        endTime: "",
        currentPage: 1,
        pageSize: 300,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: "", //签名的类型
      isFirstEnter: false,
      //相似度
      contentFlag: false,
      dialogVisible: false,
      contentList: [],
      extractObj: {
        loading2: false,
        tableData: [],
        similarTemplateList: [],
      },
      // 标签颜色
      bgColor: [
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
      ],
      timer: null,
      sumCoun: {
        ipCount: 0,
        submitCount: 0,
        timeCount: 0,
        numCount: 0,
        auditCount: 0,
        bcCount: 0,
        contentNumberCount: 0,
      },
      prevSumCoun: {}, // 上次的数据
      notifiedTypes: {}, // 已提示的类型
      // 白名单相关数据
      whitelistDialog: false,
      whitelistForm: {
        days: 60,
        mobile: "",
        remark: ""
      },
      whitelistRules: {
        days: [
          { required: true, message: "请输入有效天数", trigger: "blur" }
        ],
        mobile: [
          { required: true, message: "请输入手机号码", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.getTableDtate();
      this.startPolling();
    });
    // this.getTableDtate();
    // 查询拒绝原因标签
    window.api.get(window.path.omcs + "servicerefusereason/tag", {}, (res) => {
      this.labelCheckListArry = res.data;
    });
    // 查询通道组
    window.api.get(
      window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup",
      {},
      (res) => {
        this.channelGroupName = res.data;
      }
    );
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.sensitiveConObj.blackWordLevel = -1;
      this.remarkObj.formData.idArr = [];
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "v3/serviceaduitsms/cooperate/page",
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          // this.$refs.plTable.reloadData(res.data.records);
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    getCount() {
      let data = {};
      Object.assign(data, this.sensitiveCondition);
      data.cooperateStatus = "TEST";
      delete data.currentPage;
      delete data.pageSize;
      delete data.auditType;

      window.api.post(
        window.path.omcs + "v3/serviceaduitsms/statisfy",
        data,
        (res) => {
          if (res.code == 200) {
            // 先重置所有计数为0
            this.sumCoun = {
              ipCount: 0,
              submitCount: 0,
              timeCount: 0,
              numCount: 0,
              auditCount: 0,
              bcCount: 0,
              contentNumberCount: 0,
            };

            if (res.data && res.data.length > 0) {
              res.data.forEach((item) => {
                let newValue = parseInt(item.total) || 0; // 确保是数字类型
                let typeKey = this.mapAuditType(item.auditType);

                if (typeKey) {
                  // 检查数量增加并进行提示
                  this.checkAndNotify(typeKey, newValue);
                }
              });

              // 更新上次数据
              this.prevSumCoun = { ...this.sumCoun };
            }
          }
        }
      );
    },
    checkAndNotify(typeKey, newValue) {
      this.sumCoun[typeKey] = newValue;
      // 仅在新值大于旧值时提示
      if (
        this.prevSumCoun[typeKey] !== undefined &&
        newValue > this.prevSumCoun[typeKey]
      ) {
        this.sumCoun[typeKey] = newValue;

        // 如果该类型尚未提示，则弹窗提示
        if (!this.notifiedTypes[typeKey]) {
          // this.notifyUser(typeKey);
          this.notifiedTypes[typeKey] = true; // 标记该类型已提示
        }
      }
    },

    mapAuditType(auditType) {
      // 映射类型
      const typeMap = {
        1: "ipCount",
        2: "timeCount",
        3: "numCount",
        4: "submitCount",
        5: "auditCount",
        6: "bcCount",
        7: "contentNumberCount",
      };
      return typeMap[auditType] || "";
    },
    notifyUser(type) {
      const messageMap = {
        ipCount: "IP审核有新的消息，请及时查看",
        submitCount: "日提交数量管控有新的消息，请及时查看",
        timeCount: "发送时间数管控有新的消息，请及时查看",
        numCount: "发送量管控有新的消息，请及时查看",
        auditCount: "系统审核有新的消息，请及时查看",
        bcCount: "黑模版管控有新的消息，请及时查看",
        contentNumberCount: "内容号码管控有新的消息，请及时查看",
      };
      // alert(messageMap[type]);
      ElNotification({
        title: "Info",
        message: messageMap[type],
        type: "info",
      });
    },
    startPolling() {
      this.getCount();
      this.timer = setInterval(() => {
        this.getCount();
      }, 10000);
    },
    stopPolling() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    resetNotifications() {
      // 重新允许通知
      this.notifiedTypes = {};
    },
    scrub() {
      this.getTableDtate()
      this.getCount();
    },
    //查询
    sensitiveQuery() {
      if (this.tableDataObj.loading2) return; //防止重复点击
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.KZNum = this.sensitiveConObj.auditStatus;
      this.getTableDtate();
      this.getCount();
    },
    handleClick(val) {
      if (val.props.name == "已驳回信息") {
        this.sensitiveCondition.auditStatus = "3";
        this.sensitiveCondition.auditType = "";
      } else if (val.props.name == 1) {
        this.sensitiveCondition.auditStatus = "1";
        this.sensitiveCondition.auditType = 1;
      } else {
        this.sensitiveCondition.auditStatus = "1";
        this.sensitiveCondition.auditType = val.props.name;
      }
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
      this.getCount();
    },
    handleIP(row) {
      let data = {
        userId: row.userId,
        ip: row.ip,
        effectTime: this.moment(Date.now() + 30 * 24 * 60 * 60 * 1000).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        remark: "",
      }
      addIp(data)
      // this.userId = userId;
      // this.commonIpFlag = true;
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields();
      this.sensitiveCondition.beginTime = "";
      this.sensitiveCondition.endTime = "";
      this.sensitiveCondition.auditStartTime = "";
      this.sensitiveCondition.auditEndTime = "";
      this.sensitiveCondition.roleId = "";
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.KZNum = "1";
      this.getTableDtate();
      this.getCount();
    },
    trueFlogShow(val) {
      this.trueSize = val;
      this.trueFlog = true;
    },
    //快照查看
    KZCK(val) {
      if (val) {
        this.KFck.srcList = [];
        let a = val.split(",");
        console.log(val.split(","));
        for (var i = 0; i < a.length; i++) {
          this.KFck.srcList.push(window.path.imgU + a[i]);
        }
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.blackWordLevel == 1) {
        return "warning-rows";
      }
      return "";
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          this.moment(val[0]).format("YYYY-MM-DD") + " 00:00:00";
        this.sensitiveCondition.endTime =
          this.moment(val[1]).format("YYYY-MM-DD") + " 23:59:59";
      } else {
        this.sensitiveCondition.beginTime = "";
        this.sensitiveCondition.endTime = "";
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          this.moment(val[0]).format("YYYY-MM-DD") + " 00:00:00";
        this.sensitiveCondition.auditEndTime =
          this.moment(val[1]).format("YYYY-MM-DD") + " 23:59:59";
      } else {
        this.sensitiveCondition.auditStartTime = "";
        this.sensitiveCondition.auditEndTime = "";
      }
    },
    userList(row, index) {
      this.userFlag = true;
      this.nameover = index;
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size;
      this.getTableDtate();
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage;
      this.getTableDtate();
    },
    //删除
    handleEdit(index, row) {
      this.$confirms.confirmation(
        "get",
        "此操作将永久删除该条数据, 是否继续?",
        window.path.omcs + "signatureaudit/delete/" + row.signatureId,
        {},
        (res) => {
          this.getTableDtate();
        }
      );
    },
    //条件审核
    ConditionalAudit() {
      if (this.sensitiveConObj.username && this.sensitiveConObj.content) {
        this.ConditionalReplacement = true;
      } else {
        this.$message({
          message: "请先填写名称与内容条件并查询列表",
          type: "warning",
        });
      }
    },
    // 确认条件审核
    determine(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          this.ConditionForm.auditContent = this.sensitiveConObj.content;
          this.ConditionForm.auditUsername = this.sensitiveConObj.username;
          this.$confirms.confirmation(
            "post",
            "确定以当前条件批量审核？",
            window.path.omcs + "servicebatchaudit",
            this.ConditionForm,
            (res) => {
              this.getTableDtate();
              this.getCount();
              this.ConditionalReplacement = false; //条件审核弹出层
              this.ConditionForm.auditReason = "";
              this.ConditionForm.remark = "";
            }
          );
        }
      });
    },
    handleClose1() {
      this.ConditionalReplacement = false;
    },
    handleCloses(val) {
      this.dialogVisible = val;
    },
    handleClosesIp(val) {
      this.commonIpFlag = val;
    },
    //批量通过
    handleAdopt1() {
      this.adoptFlag = '2';
      if (!this.selectionflag) {
        this.$message({
          message:
            "选项中包含大于等于100个号码数，如批量通过，请先将其单个审核通过！",
          type: "warning",
        });
      } else {
        this.rowId = this.remarkObj.formData.idArr;
        this.uploadImage = true;
      }
    },
    //驳回审核
    handleAdopt2(index, row, status) {
      this.$confirms.confirmation(
        "post",
        "是否确认审核通过？",
        window.path.omcs + "v3/serviceaduitsms/audit",
        {
          overrule: true,
          auditStatus: 2,
          ids: row.id,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData = funCommon.deleteList(this.tableDataObj.tableData, row.id)
          }
        }
      );
    },
    //审核通过todey
    handleAdopt3(index, row, status) {
      this.rowId = row.id;
      this.adoptFlag = '1';
      this.uploadImage = true;
    },
    // 审核驳回通过
    turnDown(index, row, status) {
      this.idArrs = "";
      this.remarkObj.formData.idArr = "";
      this.remarkshow = true;
      this.title = "驳回审核";
      this.idArrs = row.id; //赋值ID
      this.turnStatus = status || false;
    },
    //批量通过
    sensitiveBatchDel() {
      let status = true;
      //判断选项中是否有审核过的状态
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != "1") {
          status = false;
          break;
        }
      }
      if (status) {
        this.$confirms.confirmation(
          "post",
          "此操作不可逆，是否继续?",
          window.path.omcs + "signatureaudit/auditPass",
          {
            auditStatus: 2,
            ids: this.remarkObj.formData.idArr,
          },
          (res) => {
            this.getTableDtate();
            this.remarkObj.formData.idArr = [];
          }
        );
      } else {
        this.$message({
          message: "选项中包含已审核项，需重新选择待审核项！",
          type: "warning",
        });
      }
    },
    //驳回
    handleReject(row) {
      this.turnflag = 1;
      this.idArrs = "";
      this.remarkObj.formData.idArr = "";
      this.remarkshow = true;
      this.title = "审核不通过";
      this.idArrs = row.id; //赋值ID
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "此操作不可逆，是否继续?",
            window.path.omcs + "v3/serviceaduitsms/audit",
            {
              overrule: this.turnStatus || false,
              auditStatus: 3,
              ids:
                this.remarkObj.formData.idArr.length == 0
                  ? this.idArrs
                  : this.remarkObj.formData.idArr,
              remark: this.remarkObj.formData.auditReason,
              tag: this.remarkObj.formData.labelCheckList.join(","),
              auditPic: this.fileauditpic.join(","),
            },
            (res) => {
              if (res.code == 200) {
                if (this.turnflag == 1) {
                  this.tableDataObj.tableData = funCommon.deleteList(this.tableDataObj.tableData, this.idArrs)
                } else if (this.turnflag == 2) {
                  this.tableDataObj.tableData = funCommon.batcnDeleteList(this.tableDataObj.tableData, this.remarkObj.formData.idArr.split(','))
                }
                this.remarkshow = false;
                this.getCount();
                this.remarkObj.formData.auditReason = "";
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    //批量驳回
    sensitiveBatchSet() {
      this.title = "批量驳回备注";
      this.remarkshow = true;
      this.turnflag = 2;
    },
    //批量切换通道组
    batchChannelGroup() {
      if (!this.selectionflag) {
        this.$message({
          message:
            "选项中包含大于等于100个号码数，如批量通过，请先将其单个审核通过！",
          type: "warning",
        });
      } else {
        // this.title = '批量驳回备注'
        this.SwitchChannelGroup = true;
      }
    },
    batchChannelGroups(row) {
      // console.log(row);
      this.SwitchChannelGroup = true;
      // this.id
      this.id = row.id;
    },
    handelAicheck(row) {
      this.$confirms.confirmation(
        "get",
        "是否使用智能检测？",
        window.path.omcs + "v3/serviceaduitsms/smsDetect?auditId=" + row.id,
        {},
        (res) => {
          // this.getTableDtate();
          if (res.code == 200) {
            this.$nextTick(() => {
              if (res.data == "") {
                this.tableDataObj.tableData[index].aiCheck = "safe";
              } else {
                this.tableDataObj.tableData[index].aiCheck = "unsafe";
              }
            });
          }
        }
      );
    },
    uploadReview() {
      this.$confirms.confirmation(
        "post",
        "是否确认审核通过？",
        window.path.omcs + "v3/serviceaduitsms/audit",
        {
          overrule: false,
          auditStatus: 2,
          ids: this.rowId,
          auditPic: this.fileauditpic.join(","),
        },
        (res) => {
          if (res.code == 200) {
            this.uploadImage = false;
            if (this.adoptFlag == '1') {
              this.tableDataObj.tableData = funCommon.deleteList(this.tableDataObj.tableData, this.rowId)
            } else if (this.adoptFlag == '2') {
              let data = this.remarkObj.formData.idArr.split(',')
              this.tableDataObj.tableData = funCommon.batcnDeleteList(this.tableDataObj.tableData, data)
            }
            this.getCount();
          }
        }
      );
    },
    handleRemove1(file, fileList) {
      this.fileauditpic = [];
      for (let i = 0; i < fileList.length; i++) {
        this.fileauditpic.push(fileList[i].response.data.fullpath);
      }
    },
    //图片上传成功
    handleAvatarSuccess1(res, file) {
      this.fileauditpic.push(res.data.fullpath);
    },
    // 图片上传限制
    beforeAvatarUpload1(file) {
      const isJPG1 = file.type === "image/png";
      const isJPG2 = file.type === "image/jpeg";
      const isJPG3 = file.type === "image/tiff";
      const isJPG4 = file.type === "image/raw";
      const isJPG5 = file.type === "image/bmp";
      // const isLt2M = file.size / 1024 / 1024 < 1;
      if (!isJPG1 && !isJPG2 && !isJPG3 && !isJPG4 && !isJPG5) {
        this.$message.error("只允许上传图像");
        return false;
      }
      // if (!isLt2M) {
      //     this.$message.error('上传头像图片大小不能超过 1MB!');
      // }
      // return isJPG && isLt2M;
    },
    // 确认切换通道
    confirmSwitchChannel() {
      this.$refs.refForm.validate((valid) => {
        if (valid) {
          // 单条切换通道
          if (this.remarkObj.formData.idArr.length) {
            this.SwitchChannelForm.ids = this.remarkObj.formData.idArr;
          } else {
            this.SwitchChannelForm.ids = this.id + "";
          }
          // this.SwitchChannelForm.ids=this.remarkObj.formData.idArr
          this.$confirms.confirmation(
            "post",
            "确认切换通道组？",
            window.path.omcs + "v3/serviceaduitsms/audit",
            this.SwitchChannelForm,
            (res) => {
              this.SwitchChannelGroup = false; //关闭弹窗
              this.getTableDtate();
              this.getCount();
            }
          );
        }
      });
    },
    // 设置白名单
    setMobileWhitelist(row) {
      // 重置表单并回显当前行的mobiles数据
      let mobileList = row.mobiles.join(',');
      this.whitelistForm = {
        days: 60,
        mobile: mobileList || "",
        remark: ""
      };

      this.whitelistDialog = true;
    },

    // 确认设置白名单
    confirmSetWhitelist(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 构建接口所需的数据格式，添加默认的level字段
          const requestData = {
            days: this.whitelistForm.days,
            // level: 0, // 设置默认级别为0
            mobile: this.whitelistForm.mobile,
            remark: this.whitelistForm.remark
          };

          this.$confirms.confirmation(
            'post',
            '确认设置号码白名单？',
            window.path.omcs + 'operatingmobilewhitelist',
            requestData,
            (res) => {
              if (res.code == 200) {
                // this.$message.success('设置白名单成功');
                this.whitelistDialog = false;
                this.whitelistForm = {
                  days: 60,
                  mobile: "",
                  remark: ""
                };
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                  duration: 5000,
                });
              }
            },
            (err) => {
            },
            'default'
          );
        } else {
          return false;
        }
      });
    },
    isAllEqual(array) {
      if (array.length > 0) {
        return !array.some(function (value, index) {
          return value.username !== array[0].username;
        });
      } else {
        return true;
      }
    },
    handleSelectionChange(val) {
      // console.log(val);
      if (val.records.length === 0) {
        this.auditStatus = [];
        this.selectionflag = true;
        this.remarkObj.formData.idArr = []; //置空ID
      } else {
        this.selectionflag = true;
        let selectId = [];
        let auditStatus = [];
        for (let i = 0; i < val.records.length; i++) {
          if (val.records[i].mobCount >= 100) {
            this.selectionflag = false;
            // this.$message({
            //     message: '选项中包含大于等于100个号码数，如批量通过，请先将其单个审核通过！',
            //     type: 'warning'
            // });
          }
          selectId.push(val.records[i].id);
          //批量操作列表选中项的审核状态
          auditStatus.push(val.records[i].auditStatus);
        }
        this.auditStatus = auditStatus; //选中项的审核状态
        this.remarkObj.formData.idArr = selectId.join(","); //批量通过，不通过的id数组
        //判断相同用户名
        this.contentFlag = this.isAllEqual(val.records);
        if (this.contentFlag) {
          this.contentList = val.records.map((item) => {
            //   return item.content;
            return {
              username: item.username,
              content: item.content,
            };
          });
        }
      }
    },
    //提取相似度
    handeExtract() {
      if (this.contentFlag) {
        window.api.post(
          window.path.omcs + "operatingclientsmssimilar/content/percent",
          this.contentList,
          (res) => {
            if (res.code == 200) {
              this.dialogVisible = true;
              this.extractObj.tableData = res.data.similarList;
              this.extractObj.similarTemplateList =
                res.data.similarTemplateList;
              // this.similarFormop.content = res.data.similarList[0].content.replace(/<span class='prompt'>/g,'').replace(/<\/span>/g,'')
              // this.similarFormop.username =  res.data.similarList[0].username
              // this.similarFormop.snapshot = JSON.stringify(res.data.similarList)
            }
          }
        );
      } else {
        this.$message({
          message: "请选择同一用户名！",
          type: "warning",
        });
      }
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate();
        this.startPolling();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  deactivated() {
    this.stopPolling();
  },
  watch: {
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    uploadImage(val) {
      if (val == false) {
        this.fileList1 = [];
        this.fileauditpic = [];
        this.rowId = "";
        // this.variableFlag = false
        this.linkFlag = false;
        this.sceneFlag = false;
        this.violateFlag = false;
        this.remarkObj.formData.idArr = []; //置空ID
        // this.getTableDtate();
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.turnStatus = false;
          this.operationFlag = true; //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields();
          this.remarkObj.formData.idArr = []; //置空ID
          // this.getTableDtate();
        }
      }
    },
    // 监听切换通道组是否关闭
    SwitchChannelGroup(val) {
      if (this.$refs.refForm) {
        if (val == false) {
          this.$refs.refForm.resetFields();
          this.idArrs = [];
          this.SwitchChannelForm.idArr = [];
          this.getTableDtate();
        }
      }
    },
    ConditionalReplacement(val) {
      if (!val) {
        this.$refs["ConditionFormRulef"].resetFields();
      }
    },
    // 监听通道组改变
    // 'SwitchChannelForm.channelGroupId'(val){
    //     this.SwitchChannelForm.channelYd=''
    //     this.SwitchChannelForm.channelLt=''
    //     this.SwitchChannelForm.channelDx=''
    //     if(val){
    //         window.api.post(window.path.omcs+'v3/serviceaduitsms/getAllSmsChannelGroup/'+val,{},res=>{
    //             this.channelYd=res.listYD2
    //             this.channelLt=res.listLT2
    //             this.channelDx=res.listDX2
    //         })
    //     }else{
    //         this.channelYd=[]
    //         this.channelLt=[]
    //         this.channelDx=[]
    //     }
    // }
  },
};
</script>

<style lang="less" scoped>
.el-checkbox-group>.el-checkbox {
  margin-left: 30px;
}

.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.sensitive-table {}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.tips {
  margin-top: 30px;
}

.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}

.divWidth>div {
  width: 100%;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.role_active {
  display: inline-block;
  min-height: 20px;
  border-radius: 20px;
  line-height: 20px;
  text-align: center;
  padding: 0 10px;
}

.user_tip {
  display: inline-block;
  width: 13px;
  height: 13px;
  margin-top: 5px;
}

.title {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  margin: 10px 0;
}

.spanColor {
  white-space: normal;
  word-break: break-all;
}

:deep(.prompt) {
  display: inline-block;
  height: 20px;
  color: #fff;
  line-height: 20px;
  padding: 0 5px;
  background: #f56c6c;
  border-radius: 4px;
  font-size: 12px;
}

:deep(.prompt_url) {
  color: #409eff;
  text-decoration: underline;
}

:deep(.dangerous-moblie) {
  color: #FF4500;
  font-weight: 800;
  // cursor: pointer;
  // text-decoration: underline;
}

:deep(.el-tabs__nav-wrap) {
  overflow: visible !important;
}

:deep(.el-tabs__nav-scroll) {
  overflow: visible !important;
}

:deep(.el-badge__content.is-fixed) {
  top: -8px;
  right: -5px;
}
</style>

<style>
.divWidth>div>div {
  width: 60%;
}

/* .spanColor>.prompt {
  display: inline-block;
  height: 20px;
  color: #fff;
  line-height: 20px;
  padding: 0 5px;
  background: #f56c6c;
  border-radius: 4px;
  font-size: 12px;
}

.prompt_url {
  color: #409eff;
  text-decoration: underline;
} */

.el-icon-question {
  color: #409eff;
}

.el-table--small th {
  background: #f5f5f5;
}

.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}

.el-checkbox-button__inner {
  border: none;
}

.el-dialog__title {
  font-size: 20px;
}

.el-textarea__inner {
  height: 100%;
}
</style>
<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
