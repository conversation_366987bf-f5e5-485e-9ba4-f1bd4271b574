<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        ref="formBlackList"
      >
        <el-form-item label="手机号" prop="mobile">
          <el-input
            v-model="formBlackList.mobile"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="quenryBlack()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header" style="margin-right:10px" >营销黑号库列表</span> -->
        <el-button type="primary" @click="blackListDialog = true"
          >添加黑名单</el-button
        >
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        >
          <el-table-column prop="mobile" label="手机号码"></el-table-column>
          <el-table-column
            label="操作"
            width="80"
            fixed="right"
            v-if="roleId == '3' || roleId == '4' || roleId == '5'"
          >
            <template v-slot="scope">
              <el-button
                link
                v-if="roleId == '3' || roleId == '4' || roleId == '5'"
                style="color: #f56c6c"
                @click="delet(scope.row.mobile)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;删&nbsp;除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formBlackListObj.currentPage"
            :page-size="formBlackListObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 弹窗（新增） -->
      <el-dialog
        title="添加营销黑号库黑名单"
        v-model="blackListDialog"
        width="520px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="blackList.formData"
          :rules="blackList.formRule"
          ref="blackList"
          label-width="96px"
          style="padding: 0 28px"
        >
          <el-form-item
            label="手机号码"
            prop="mobile"
            :rules="
              filter_rules({
                required: true,
                type: 'fiveMobiles',
                message: '手机号码不能为空！',
              })
            "
          >
            <el-input v-model="blackList.formData.mobile"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer" style="margin-right: 30px;">
            <el-button
              class="footer-center-button"
              @click="blackListDialog = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              type="primary"
              @click="blacklist_ok_add('blackList')"
              >确 认</el-button
            >
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  name: 'MarketBlackLIst',
  data() {
    return {
      isFirstEnter: false,
      header: '',
      formBlackList: {
        //查询框的值
        mobile: '',
        currentPage: 1,
        pageSize: 10,
      },
      formBlackListObj: {
        //copy查询框的值
        mobile: '',
        currentPage: 1,
        pageSize: 10,
      },
      selectId: '', //批量操作选中id
      rowId: '', //操作行的id
      blackListDialog: false, //弹框显示隐藏
      blackList: {
        formData: {
          mobile: '',
        },
        formRule: {},
      },

      tableDataObj: {
        //列表数据
        loading2: false,
        tableData: [],
        title: '',
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //-------------   列表信息   --------------
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true //打开loading
      window.api.post(
        window.path.omcs + 'operatingMarketBlacklist/page',
        this.formBlackListObj,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.totalRow = res.total
          this.tableDataObj.loading2 = false //关闭loading
        }
      )
    },
    //查询
    quenryBlack() {
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.formBlackListObj.pageSize = size
      this.getTableDtate()
    },
    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.formBlackListObj.currentPage = currentPage
      this.getTableDtate()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.formBlackList.begTime = ''
      this.formBlackList.endTime = ''
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].blacklistId)
      }
      this.selectId = selectId.join(',')
    },
    //-------------   列表信息   --------------

    //-------------   新增功能开始   --------------
    blacklist_ok_add(formName) {
      //提交
      this.$refs[formName].validate((valid) => {
        //表单数据验证
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '确定新增?',
            window.path.omcs + 'operatingMarketBlacklist/add',
            this.blackList.formData,
            (res) => {
              this.getTableDtate()
              this.blackListDialog = false //隐藏弹窗
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    delet(val) {
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该条数据, 是否继续?',
        window.path.omcs + 'operatingMarketBlacklist/delete/' + val,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
  },
  mounted() {
    this.header = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  watch: {
    //监听查询框对象的变化
    // formBlackListObj:{
    //     handler(){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     // immediate:true
    // },
    blackListDialog(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.blackList.resetFields() //清空表单
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
