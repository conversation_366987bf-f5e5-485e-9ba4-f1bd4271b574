<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title" style="display: flex; align-items: center">
      <div
        @click="goBack()"
        style="
          display: flex;
          align-items: center;
          cursor: pointer;
          color: #16a589;
        "
      >
        <el-icon><ArrowLeftBold /></el-icon>
        <span style="display: inline-block; padding-right: 10px"> 返回</span>
      </div>
      |
      <span style="margin-left: 10px;">闪验用户配置</span>
    </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="参数设置" name="parameterRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="addUserStep2Form"
            label-width="140px"
          >
            <div class="float-div-1" style="padding-right: 20px">
              <div>
                <p
                  style="
                    padding: 0 0 18px 40px;
                    font-weight: bold;
                    font-size: 14px;
                  "
                >
                  参数设置
                </p>
                <el-form-item label="付费方式" prop="flashPayMethod">
                  <el-radio-group
                    v-model="addUserStep2Form.formData.flashPayMethod"
                  >
                    <el-radio disabled value="1">预付费</el-radio>
                    <el-radio disabled value="2" style="margin-left: 10px"
                      >后付费</el-radio
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
            </div>
            <div style="text-align: right" class="clear-div">
              <!-- <el-button type="primary" style="margin-right: 20px;" @click="submitForm('addUserStep2Form')">提 交</el-button> -->
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  name: 'FlashTest',
  data() {
    return {
      isFirstEnter: false,
      activeName: 'parameterRef',
      jsonData: {
        consumerName: '',
        compName: '',
      }, //用户资料
      addUserStep2Form: {
        //弹窗步骤2的数据和验证
        formData: {
          userId: '', //用户id
          flashPayMethod: '1',
        },
        formRule: {
          flashPayMethod: [
            { required: true, message: '请选择付费方式', trigger: 'change' },
          ],
        },
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    /**---------（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs +
          'operatinguser/' +
          this.addUserStep2Form.formData.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName
          this.jsonData.compName = res.data.compName
        }
      )
    },
    //返回
    goBack() {
      // this.$router.push({ path: 'UserDetail',query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({
        path: 'UserDetail',
        query: { id: this.$route.query.id },
      })
    },
    //编辑的赋值
    handleEdit() {
      window.api.get(
        window.path.omcs + 'operatinguser/userFlashInfo/' + this.$route.query.id,
        {},
        (res) => {
          this.addUserStep2Form.formData.flashPayMethod =
            res.data.flashPayMethod + ''
        }
      )
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          this.$confirms.confirmation(
            'put',
            '确认提交短信配置？',
            window.path.omcs + 'operatinguser/userFlashInfo',
            this.addUserStep2Form.formData,
            (res) => {
              this.goBack()
            }
          )
        } else {
          return false
        }
      })
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.addUserStep2Form.formData.userId = this.$route.query.id
      this.handleEdit()
      this.getBasicData()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.addUserStep2Form.formData.userId = this.$route.query.id
        this.handleEdit()
        this.getBasicData()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  deactivated() {
    this.activeName = "parameterRef";
  },
  watch: {},
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}
.input-w {
  width: 345px !important;
}
.el-radio-group .el-radio {
  margin-right: 0px;
}
.fillet {
  padding: 20px;
}
.input-w-2 {
  width: 320px !important;
}
.input-w-3 {
  width: 320px !important;
}
.input-w-4 {
  width: 190px !important;
}
.input-w-sm {
  width: 126px !important;
}
.input-w-f {
  width: 80px !important;
}
.float-div-1 {
  float: left;
}
.float-div-2 {
  float: left;
}
.float-div-3 {
  float: left;
}
.clear-div {
  clear: both;
}
.red {
  color: red;
}
@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>

<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>
