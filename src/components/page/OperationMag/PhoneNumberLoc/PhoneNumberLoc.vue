<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="手机号段" prop="mobileNumber">
            <el-input
              v-model="formInline.mobileNumber"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="所属地区" prop='optionsall'>
                           <el-cascader
                                ref="cascaderAddr"
                                class="input-w"
                                size="default"
                                :options="options"
                                v-model="formInline.optionsall"
                                @change="handleChange">
                            </el-cascader>
                        </el-form-item> -->
          <el-form-item label="省份" prop="mobileProvincial">
            <el-input
              v-model="formInline.mobileProvincial"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="城市" prop="mobileCity">
            <el-input
              v-model="formInline.mobileCity"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="运营商" prop="mobileOperators">
            <el-select
              v-model="formInline.mobileOperators"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建人" prop="buildName">
            <el-input
              v-model="formInline.buildName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="dataCreat">
            <el-date-picker
              class="input-time"
              v-model="formInline.dataCreat"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            >
            </el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button
              type="primary"
              plain
              style=""
              @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button type="primary" @click="addopt">新增</el-button>
            <el-button
              type="danger"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delAll"
              >批量删除</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增手机号码归属地 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="440px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
          label-width="120px"
        >
          <el-form-item label="手机号段(7位)" prop="mobNumber">
            <el-input
              v-model="formop.mobNumber"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="省份" prop="provincial">
            <el-input
              v-model="formop.provincial"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="城市" prop="city">
            <el-input
              v-model="formop.city"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>

          <!-- <el-form-item label="所属地区"  prop='optionscity'>
                           <el-cascader
                                class="input-w"
                                ref="cascaderAddrs"
                                size="default"
                                :options="options"
                                v-model="formop.optionscity"
                                @change="handleChanges">
                            </el-cascader>
                        </el-form-item> -->
          <el-form-item label="运营商" prop="operators">
            <el-select
              v-model="formop.operators"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增运营商结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
import {
  provinceAndCityData,
  provinceAndCityDataPlus, //带全部的
} from 'element-china-area-data'
export default {
  components: {
    TableTem
  },
  name: 'PhoneNumberLoc',
  data() {
    return {
      isFirstEnter: false,
      options: provinceAndCityData, //不带全部的新增地区
      titleMap: {
        add: '增加手机号码归属地',
        edit: '编辑手机号码归属地',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      formLabelWidth: '100px',
      selectId: '',
      tableRow: '', //当前行列表数据
      formop: {
        //新增数据
        mobNumber: '',
        operators: '',
        provincial: '',
        city: '',
        // provincialId:'',
        // cityId:'',
        // optionscity:[]
      },
      rules: {
        mobNumber: [
          { required: true, message: '请输入手机号段', trigger: 'blur' },
          { min: 7, max: 7, message: '长度在7个字符', trigger: 'blur' },
          { pattern: /^1[3|4|5|6|7|8|9]\d{5}$/, message: '请输入正确的号段' },
        ],
        optionscity: [
          { required: true, message: '请选择地区', trigger: 'change' },
        ],
        provincial: [
          { required: true, message: '请输入省份', trigger: 'change' },
        ],
        city: [{ required: true, message: '请输入城市', trigger: 'change' }],
        operators: [
          { required: true, message: '请选择运营商', trigger: 'change' },
        ],
      },

      formInline: {
        //查询列表
        mobileNumber: '',
        mobileOperators: '',
        buildName: '',
        dataCreat: [],
        beginTime: '',
        endTime: '',
        mobileProvincial: '',
        mobileCity: '',
        optionsall: [],
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        mobileNumber: '',
        mobileOperators: '',
        buildName: '',
        dataCreat: [],
        beginTime: '',
        endTime: '',
        optionsall: [],
        mobileProvincial: '',
        mobileCity: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        tableData: [],
        custom: true,
        id: 'PhoneNumberLoc',
        loading2: false,
        currentPage: 1,
        pageSize: 10,
        total: 0,
        tableLabel: [
          // {
          // prop:"phoneAttributionId",
          // showName:'序号',
          // fixed:false
          // },
          {
            prop: 'mobNumber',
            showName: '手机号段',
          },
          {
            prop: 'provincial',
            showName: '所属省份',
          },
          {
            prop: 'city',
            showName: '所属市级',
          },
          {
            prop: 'operators',
            showName: '运营商',
            width: '100',
            formatData: function (val) {
              if (val == 1) {
                return (val = '移动')
              }
              if (val == 2) {
                return (val = '联通')
              }
              if (val == 3) {
                return (val = '电信')
              }
              if (val == 4) {
                return (val = '未知')
              }
            },
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '180',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'EditPen',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'Delete',
            color: '#f56c6c',
          },
        ],
      },
      pldel: false,
    }
  },
  methods: {
    //-------------------------------------------列表数据------------------------------------------
    gettableLIst() {
      //获取手机号码归属地列表数据
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingphoneattribution/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //-------------------------------------------列表数据------------------------------------------
    //-------------------------------------------列表操作------------------------------------------
    handelSelection(val) {
      //列表复选框的值
      if (val.length > 0) {
        let arr = []
        val.forEach(item => {
          arr.push(item.phoneAttributionId)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime =
          this.moment(val[1]).format('YYYY-MM-DD ') + '23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    //新增选择地区
    // handleChanges (value) {
    //    let arr = this.$refs['cascaderAddrs'].getCheckedNodes()
    //    this.formop.provincial=arr[0].pathLabels[0];
    //    this.formop.city=arr[0].pathLabels[1];
    //    this.formop.provincialId=value[0];
    //    this.formop.cityId=value[1];
    // },
    //查询选择地区
    // handleChange (value) {
    //    let arr = this.$refs['cascaderAddr'].getCheckedNodes()
    //    this.formInline.mobileProvincial=arr[0].pathLabels[0];
    //    this.formInline.mobileCity=arr[0].pathLabels[1];
    // },
    Query() {
      //查询功能
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      //重置
      //清除地区和时间
      this.formInline.mobileProvincial = ''
      this.formInline.mobileCity = ''
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      //新增提交 //编辑提交
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'edit') {
            this.formop.phoneAttributionId = this.phoneAttributionId
            if (this.tableRow.mobNumber == this.formop.mobNumber) {
              this.$confirms.confirmation(
                'put',
                '确认执行此操作吗？',
                window.path.omcs + 'operatingphoneattribution',
                this.formop,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              window.api.get(
                window.path.omcs +
                  'operatingphoneattribution/checkMobNumberExist/' +
                  this.formop.mobNumber,
                {},
                (res) => {
                  if (res.code == 400) {
                    this.$message({
                      message: '手机号段重复，重新输入！',
                      type: 'warning',
                    })
                  } else {
                    this.$confirms.confirmation(
                      'put',
                      '确认执行此操作吗？',
                      window.path.omcs + 'operatingphoneattribution',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  }
                }
              )
            }
          } else {
            window.api.get(
              window.path.omcs +
                'operatingphoneattribution/checkMobNumberExist/' +
                this.formop.mobNumber,
              {},
              (res) => {
                if (res.code == 400) {
                  this.$message({
                    message: '手机号段重复，重新输入！',
                    type: 'warning',
                  })
                } else {
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs + 'operatingphoneattribution',
                    {
                      mobNumber: this.formop.mobNumber,
                      operators: this.formop.operators,
                      provincial: this.formop.provincial,
                      city: this.formop.city,
                      // provincialId:this.formop.provincialId,
                      // cityId:this.formop.cityId
                    },
                    () => {
                      this.dialogFormVisible = false
                      this.gettableLIst()
                    }
                  )
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //新增
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    delAll() {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs +
          'operatingphoneattribution/deleteBatch/' +
          this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    detailsRow(val) {
      //编辑id
      this.phoneAttributionId = val.row.phoneAttributionId
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.tableRow = val.row
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val.row)
        //省市区选中
        // this.formop.optionscity = [val.row.provincialId,val.row.cityId];
      })
    },
    delRow: function (val) {
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs +
          'operatingphoneattribution/' +
          val.row.phoneAttributionId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }

      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //     handler() {
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate: true
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
