<template>
  <div>
    <!-- <div class="crumbs">
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 账号白名单</el-breadcrumb-item>
                </el-breadcrumb>
            </div> -->
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <!-- <el-form-item label="用户名称" label-width="90px" prop="username">
                            <el-input v-model="formInline.username" placeholder class="input-w"></el-input>
                        </el-form-item> -->
          <el-form-item label="IP白名单" prop="ip">
            <el-input
              v-model="formInline.ip"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="" prop="">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="boderbottom">

                </div> -->
      <div class="Signature-search-fun">
        <el-button type="primary" @click="addopt">发送IP白名单</el-button>
        <!-- <el-button type="primary" @click="handelSynIP">同步IP</el-button> -->
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->
          <el-table-column label="用户名称">
            <template v-slot="scope">{{ scope.row.username }}</template>
          </el-table-column>
          <el-table-column label="IP白名单">
            <template v-slot="scope">{{ scope.row.ip }}</template>
          </el-table-column>
          <el-table-column label="备注">
            <template v-slot="scope">{{ scope.row.remark }}</template>
          </el-table-column>
          <el-table-column label="创建人">
            <template v-slot="scope">{{ scope.row.createName }}</template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">
              <span v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</span></template
            >
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template v-slot="scope">
              <!-- <el-button type="text" @click="edit(scope.row)">编 辑</el-button> -->
              <el-button
                type="text"
                style="color: red"
                @click="cancel(scope.row)"
                >删 除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
        
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="80px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              disabled
              v-model="formop.username"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="IP白名单" prop="ip">
            <el-input
              v-model="formop.ip"
              placeholder="请输入IP白名单;多个IP以,分割"
              type="textarea"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="formop.remark"
              placeholder="请输入备注"
              type="textarea"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import moment from 'moment'
export default {
  name: 'ShuntSetting',
  components: {
    TableTem,
  },
  data() {
    var percent = (rule, value, callback) => {
      if (value != '') {
        let reg = /^[0-9]*[1-9][0-9]*$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入正确天数'))
        }
      } else {
        callback(new Error('有效期不能为空'))
      }
    }
    return {
      titleMap: '新增',
      isFirstEnter: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      ids: '',
      selectId: [],
      editId: '',
      formop: {
        username: '',
        ip: '',
        remark: '',
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名称', trigger: 'change' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        ip: [
          {
            required: true,
            message: '请输入IP白名单',
            trigger: 'change',
          },
        ],
      },
      formInline: {
        username: '',
        ip: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        ip: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    }
  },
  methods: {
    getUserInfo() {
      window.api.get(
        window.path.omcs + 'operatinguser/userInfo/' + this.$route.query.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.formInline.username = res.data.consumerName
            this.tabelAlllist.username = res.data.consumerName
            this.formop.username = res.data.consumerName
            this.gettableLIst()
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        }
      )
    },
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatorClientIp/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    // 新增 编辑
    addopt() {
      this.titleMap = '新增'
      this.dialogFormVisible = true
    },
    edit(val) {
      this.titleMap = '编辑'
      this.dialogFormVisible = true
      this.formop.username = val.username
      this.formop.ip = val.ip
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.editId == '') {
            window.api.post(
              window.path.omcs + 'operatorClientIp',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.gettableLIst()
                  this.dialogFormVisible = false
                } else {
                  this.$message.error(res.msg)
                }
              }
            )
          } else {
            let data = {
              id: this.editId,
              ip: this.formop.ip,
            }
            window.api.put(window.path.omcs + 'operatorClientIp', data, (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.gettableLIst()
                this.dialogFormVisible = false
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除
    cancel(val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs + 'operatorClientIp/' + val.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    //同步
    handelSynIP() {
      this.$confirms.confirmation(
        'post',
        '确定同步IP？',
        window.path.omcs + 'operatorClientIp/synIp',
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    //批量
    // handleSelectionChange(val) {
    //     this.selectId = val.map((item, index) => {
    //         return item.id
    //     })
    //     this.ids = this.selectId.join(',')
    // },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getUserInfo()
    })
  },
  // activated() {
  //     if (this.$route.meta.isBack || !this.isFirstEnter) {
  //         this.$nextTick(() => {
  //             this.getUserInfo();
  //         });
  //     } else {
  //         this.$route.meta.isBack = false
  //         this.isFirstEnter = false;
  //     }
  // },
  watch: {
    // '$route.query.id': {
    //     handler(newVal, oldVal) {
    //         // console.log(newVal);
    //         //判断newVal有没有值监听路由变化
    //         if (newVal) {
    //             // this.clientId = newVal;
    //             this.getUserInfo();
    //         }

    //     },
    //     deep: true,
    //     immediate: true, // 初次监听即执行
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
        // this.formop.username = ''
        this.formop.ip = ''
        this.editId = ''
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>
