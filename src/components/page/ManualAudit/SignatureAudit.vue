<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form :inline="true" :model="sensitiveCondition" class="demo-form-inline" ref="sensitiveCondition"
          label-width="100px">

          <el-form-item label="用户名称" prop="consumerName">
            <el-input v-model="sensitiveCondition.consumerName" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="公司名称" prop="compId">
            <el-select ref="optionRef" class="input-w" v-model="sensitiveCondition.compId" clearable filterable remote
              :remote-method="remoteMethod" :loading="loadingcomp" placeholder="请选择公司名称">
              <el-option v-for="(item, index) in compNamelist" :key="index" :label="item.company" :value="item.id">
              </el-option>
            </el-select>
            <!-- <el-input class="input-w" v-model="addUserStep1Form.formData.compName"></el-input> -->
          </el-form-item>
          <el-form-item label="排除用户" prop="excludeUsername">
            <el-input v-model="sensitiveCondition.excludeUsername" placeholder="多个用户名用 , 号分割"
              class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="签名名称" prop="signature">
            <el-input v-model="sensitiveCondition.signature" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="审核状态" prop="auditStatus">
            <el-select v-model="sensitiveCondition.auditStatus" placeholder="请选择" clearable class="input-w">
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否新签名" prop="queryType">
            <el-select v-model="sensitiveCondition.queryType" placeholder="请选择" clearable class="input-w">
              <!-- <el-option label="" value=""></el-option> -->
              <el-option label="新签名" value="1"></el-option>
              <el-option label="历史签名" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户类型" prop="roleId">
            <el-select v-model="sensitiveCondition.roleId" clearable placeholder="不限" class="input-w">
              <el-option label="不限" value=""></el-option>
              <el-option label="管理商" value="12"></el-option>
              <el-option label="子用户" value="13"></el-option>
              <el-option label="终端" value="14"></el-option>
              <el-option label="线上终端" value="22"></el-option>
            </el-select>
            <!-- <el-input v-model="sensitiveCondition.roleId" placeholder="" class="input-w"></el-input> -->
          </el-form-item>
          <el-form-item label="申请时间" prop="time1">
            <el-date-picker class="input-time" v-model="sensitiveCondition.time1" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="hande1">
            </el-date-picker>
          </el-form-item>
          <el-form-item v-if="sensitiveConObj.auditStatus != 1" label="最近审核时间" prop="time2">
            <el-date-picker class="input-time" v-model="sensitiveCondition.time2" value-format="YYYY-MM-DD"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="hande2">
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain @click="sensitiveQuery()">查询</el-button>
            <el-button type="primary" plain @click="sensitiveReload('sensitiveCondition')">重置</el-button>
            <el-button type="success" plain @click="exportSignatureData()">失败导出</el-button>
          </div>
        </el-form>
      </div>

      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <!-- <el-button type="primary" @click="sensitiveBatchDel()" :disabled="!contentExampleflag"
              v-if="remarkObj.formData.idArr.length != 0">批量通过</el-button> -->
            <el-button type="danger" @click="sensitiveBatchSet()"
              v-if="remarkObj.formData.idArr.length != 0">批量驳回</el-button>

          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="ManualAuditSignatureAudit" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px" :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">
          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称" width="140">
            <template v-slot="scope">
              <div style="color: #16a589; cursor: pointer" @click="rouTz(scope.row)">
                {{ scope.row.consumerName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="签名内容" title="签名内容" width="180">
            <template v-slot="scope">
              <!-- <Tooltip v-if="scope.row.signature" :content="scope.row.signature" className="wrapper-text"
                                    effect="light">
                                </Tooltip> -->
              <el-tooltip class="item" effect="dark" :content="`字数：${scope.row.signature.replace(/【|】/g, '').length
                }`" placement="top">
                <span v-if="scope.row.signature.replace(/【|】/g, '').length > 15" style="color: #f56c6c">{{
                  scope.row.signature }}</span>
                <span v-else>{{ scope.row.signature }}</span>
              </el-tooltip>
              <span style="color: #f56c6c">{{ scope.row.prompt }}</span>
            </template>
          </vxe-column>

          <vxe-column field="短信示例" title="短信示例" width="240">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.contentExample" :content="scope.row.contentExample" className="wrapper-text"
                effect="light">
              </Tooltip>
              <!-- <span>{{ scope.row.signature }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="短信类型" title="短信类型" width="140">
            <template v-slot="scope">
              <div v-if="scope.row.serviceType == 1">验证码</div>
              <div v-else-if="scope.row.serviceType == 2">通知</div>
              <div v-else-if="scope.row.serviceType == 3">营销</div>
            </template>
          </vxe-column>
          <vxe-column field="营销类型" title="营销类型" width="140">
            <template v-slot="scope">
              <div v-for="item in subServiceTypeList">
                <span v-if="item.code == scope.row.subServiceType">{{
                  item.desc
                }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="截图" title="截图" width="80">
            <template v-slot="scope">
              <div v-if="scope.row.imgUrlList">
                <el-image style="width: 100%; max-height: 580px" :z-index="99999"
                  :src="API.imgU + scope.row.imgUrlList[0]" :preview-src-list="scope.row.previewList"
                  :append-to-body="true" :preview-teleported="true">
                </el-image>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="备注内容" title="备注内容" width="140">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.remark" :content="scope.row.remark" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="审核状态" title="审核状态" width="120">
            <template v-slot="scope">
              <el-tag :disable-transitions="true" v-if="scope.row.auditStatus == '1'" type="info" effect="dark">
                待审核
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.auditStatus == '2'" type="success" effect="dark">
                审核通过
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.auditStatus == '3'" type="danger" effect="dark">
                审核未通过
              </el-tag>
            </template>
          </vxe-column>
          <vxe-column field="实名状态" title="实名状态" width="120">
            <template v-slot="scope">
              <el-tag v-if="scope.row.signatureRealNameStatus == 0" type="info" effect="dark">未校验</el-tag>
              <el-tag v-else-if="scope.row.signatureRealNameStatus == 1" type="danger" effect="dark">校验失败</el-tag>
              <el-tag v-else-if="scope.row.signatureRealNameStatus == 2" type="success" effect="dark">校验成功</el-tag>
              <el-tag v-else-if="scope.row.signatureRealNameStatus == 3" type="warning" effect="dark">未知</el-tag>
            </template>
          </vxe-column>

          <vxe-column field="申请时间" title="申请时间" width="100">
            <template v-slot="scope">
              <!-- <span>{{ scope.row.createTime | fmtDate }}</span> -->
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="首次审核时间" title="首次审核时间" width="100">
            <template v-slot="scope">
              <!-- <span>{{ scope.row.createTime | fmtDate }}</span> -->
              <p v-if="scope.row.firstAuditTime">
                {{ $parseTime(scope.row.firstAuditTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.firstAuditTime">
                {{ $parseTime(scope.row.firstAuditTime, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>
          <vxe-column v-if="sensitiveConObj.auditStatus != 1" field="最近审核时间" title="最近审核时间" width="110">
            <template v-slot="scope">
              <p v-if="scope.row.auditTime">
                {{ $parseTime(scope.row.auditTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.auditTime">
                {{ $parseTime(scope.row.auditTime, "{h}:{i}:{s}") }}
              </p>
              <!-- <span>{{ scope.row.auditTime | fmtDate }}</span> -->
            </template>
          </vxe-column>
          <vxe-column v-if="sensitiveConObj.auditStatus != 1" field="审核人" title="审核人" width="150">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.auditUsername" :content="scope.row.auditUsername" className="wrapper-text"
                effect="dark">
              </Tooltip>
            </template>
          </vxe-column>

          <vxe-column field="驳回原因" title="驳回原因" width="140">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.auditReason" :content="scope.row.auditReason" className="wrapper-text"
                effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人" width="140">
            <template v-slot="scope">
              <span>{{ scope.row.createName }}</span>
            </template>
          </vxe-column>
          <vxe-column field="更新人" title="更新人" width="140">
            <template v-slot="scope">
              <span>{{ scope.row.updateName }}</span>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" min-width="120" fixed="right">
            <template v-slot="scope">
              <!-- <el-button type="text" style="color:#f56c6c;margin-left:0px;" @click="handleEdit(scope.$index, scope.row)"><i class="el-icon-delete"></i>&nbsp;删除</el-button> -->
              <el-button link style="margin-left: 0px; color: #409eff" v-if="scope.row.auditStatus == '1'"
                @click="handleAdopt(scope.$index, scope.row)"><el-icon>
                  <SuccessFilled />
                </el-icon>&nbsp;通过</el-button>
              <el-button link style="color: orange; margin-left: 0px" v-if="scope.row.auditStatus == '1'"
                @click="handleReject(scope.$index, scope.row)"><el-icon>
                  <CircleCloseFilled />
                </el-icon>&nbsp;驳回</el-button>
              <el-button link style="margin-left: 0px; color: #409eff"
                v-if="scope.row.auditStatus == '2' || scope.row.auditStatus == '3'"
                @click="handleCheckRealName(scope.$index, scope.row)"><el-icon>
                  <View />
                </el-icon>&nbsp;查看实名信息</el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage" :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total">
          </el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹窗（查看图片） -->
      <el-dialog :title="titleS" v-model="imagesshow" width="600px" :close-on-click-modal="false" draggable
        :append-to-body="true">
        <template>
          <el-carousel :interval="5000" arrow="always" :autoplay="false" style="height: 560px">
            <el-carousel-item style="height: 560px" v-for="(item, index) in imgUrl" :key="index">
              <el-image style="width: 100%; max-height: 580px" :z-index="9999" :src="item" :preview-src-list="srcList"
                :append-to-body="true" :preview-teleported="true">
              </el-image>
              <!-- <img style="width:100%;max-height:580px" :src='item' alt=""> -->
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-dialog>
      <!-- 弹框 （审核 通过） -->
      <el-dialog :title="title" v-model="remarkshow" width="900px" :close-on-click-modal="false" :show-close="false"
        draggable :append-to-body="true">
        <el-descriptions v-if="reportId" class="margin-top" title="实名信息" :column="3" size="default" border>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                企业名称
              </div>
            </template>
            {{ realNameInfo.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                社会统一信用代码
              </div>
            </template>
            {{ realNameInfo.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                企业法人
              </div>
            </template>
            {{ realNameInfo.legalPerson }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人姓名
              </div>
            </template>
            {{ realNameInfo.principalName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人证件号码
              </div>
            </template>
            {{ realNameInfo.principalIdCard }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人手机号
              </div>
            </template>
            {{ realNameInfo.principalMobile }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                校验状态
              </div>
            </template>
            <el-tag v-if="realNameInfo.signatureRealNameStatus == 0" type="info" effect="dark">未校验</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 1" type="danger" effect="dark">校验失败</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 2" type="success" effect="dark">校验成功</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 3" type="warning" effect="dark">未知</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                原因
              </div>
            </template>
            {{ realNameInfo.signatureRealNameReason }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-else>
          <el-empty description="暂无实名信息" />
        </div>
        <el-form :model="remarkObj.formData" :rules="remarkObj.formRule" ref="remarkObjs" label-width="80px"
          style="padding: 0 28px">
          <el-form-item label="拒绝原因" prop="auditReason">
            <el-input style="width: 350px;" type="textarea" :disabled="disabledinput" :rows="4" resize="none"
              v-model="remarkObj.formData.auditReason"></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button class="footer-center-button" v-if="operationFlag == true" @click="remarkshow = false">取
              消</el-button>
            <el-button class="footer-center-button" v-if="operationFlag == true" type="primary"
              @click="handelAlertdd('remarkObjs')">确 认</el-button>
            <el-button type="primary" v-if="operationFlag == false" :loading="true">操作进行中，请稍等</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 新增 弹框 -->
      <el-dialog title="创建短信签名" v-model="SigDialogVisible" width="780px" :close-on-click-modal="false" draggable
        :append-to-body="true">
        <el-form :model="signatureFrom.formData" :rules="signatureFrom.formRule" label-width="80px"
          style="padding-right: 14px" ref="signatureFrom">
          <el-form-item label="用户名称" prop="clientName" :rules="filter_rules({ required: true, message: '用户名称不能为空！' })
            ">
            <el-input v-model="signatureFrom.formData.clientName"></el-input>
          </el-form-item>
          <el-form-item label="签名内容" prop="signature" style="position: relative">
            <el-input v-model="signatureFrom.formData.signature"></el-input>
            <span style="position: absolute; top: 2px; left: 0px">【</span>
            <span style="position: absolute; top: 2px; right: 0px">】</span>
          </el-form-item>
          <el-form-item label="签名类型" class="sig-type" prop="signatureType">
            <el-radio-group v-model="signatureFrom.formData.signatureType">
              <el-radio label="1" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">公司名全称或简称：</span>须提供营业执照截图</el-radio>
              <el-radio label="2" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">APP名全称或简称：</span>须提供任一应用商店的下载链接与该应用商店的后台管理截图</el-radio>
              <el-radio label="3" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">工信部备案的网站名全称或简称：</span>提供域名备案服务商的后台备案截图</el-radio>
              <el-radio label="4" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">公众号或小程序名全称或简称：</span>须提供公众号（小程序）微信开放平台截图</el-radio>
              <el-radio label="5" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">商标全称或简称：</span>须提供商标注册证书截图</el-radio>
              <el-radio label="6" style="padding-bottom: 6px"><span class="sig-type-title-tips">其他</span></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传图片" v-if="signatureFrom.formData.signatureType != 6">
            <file-upload style="display: inline-block" :action="actionUrl" :limit="3" listType="picture"
              tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M" :fileStyle="fileStyle" :del="del1"
              :showfileList="true" @fileup="fileup" @fileupres="fileupres">选择上传文件</file-upload>
          </el-form-item>
          <el-form-item label="备注内容" prop="remark">
            <el-input type="textarea" v-model="signatureFrom.formData.remark" placeholder="请输入备注内容"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="signature_add('signatureFrom', 'signatureFrom.title')">确 定</el-button>
            <el-button @click="SigDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="签名审核通过" v-model="AdoptVisible" width="900px" draggable :append-to-body="true"
        :close-on-click-modal="false" :before-close="handleClose">
        <el-descriptions v-if="reportId" class="margin-top" title="实名信息" :column="3" size="default" border>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                企业名称
              </div>
            </template>
            {{ realNameInfo.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                社会统一信用代码
              </div>
            </template>
            {{ realNameInfo.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                企业法人
              </div>
            </template>
            {{ realNameInfo.legalPerson }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人姓名
              </div>
            </template>
            {{ realNameInfo.principalName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人证件号码
              </div>
            </template>
            {{ realNameInfo.principalIdCard }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人手机号
              </div>
            </template>
            {{ realNameInfo.principalMobile }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                校验状态
              </div>
            </template>
            <el-tag v-if="realNameInfo.signatureRealNameStatus == 0" type="info" effect="dark">未校验</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 1" type="danger" effect="dark">校验失败</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 2" type="success" effect="dark">校验成功</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 3" type="warning" effect="dark">未知</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                原因
              </div>
            </template>
            {{ realNameInfo.signatureRealNameReason }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-else>
          <el-empty description="暂无实名信息" />
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="90px" class="demo-ruleForm">
          <el-form-item v-if="actionType == '1'" label="短信类型" prop="serviceType">
            <el-select v-model="ruleForm.serviceType" placeholder="请选择短信类型" style="width: 350px;">
              <el-option label="验证码" value="1"></el-option>
              <el-option label="通知" value="2"></el-option>
              <el-option label="营销推广" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="ruleForm.serviceType == '3' && actionType == '1'" label="营销类型" prop="subServiceType">
            <el-select style="width: 350px;" v-model="ruleForm.subServiceType" placeholder="请选择营销类型">
              <el-option v-for="item in subServiceTypeList" :key="item.code" :label="item.desc"
                :value="item.code"></el-option>
              <!-- <el-option label="通知" value="2"></el-option>
                                <el-option label="营销推广" value="3"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item v-if="actionType == '1'" label="短信示例" prop="contentExample">
            <el-input style="height: 100%; width: 350px" v-model="ruleForm.contentExample" type="textarea"
              placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！" maxlength="800" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="签名来源" prop="signatureType">
            <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;"
              v-model="ruleForm.signatureType" @change="changeSignatureType">
              <el-radio :value="1">
                <span>企业名称</span>
              </el-radio>
              <el-radio :value="2">
                <span>事业单位：如机关，学校，科研单位，街道社区等</span>
              </el-radio>
              <el-radio :value="3">
                <span>商标</span>
                （须提供商标注册证书图片或在在中国商标网的商标查询截图）
              </el-radio>
              <el-radio :value="4">
                <span class="sig-type-title-tips">App </span>
                （须提供app在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio :value="5">
                <span class="sig-type-title-tips">小程序</span>
                （须提供小程序在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <!-- <el-radio :value="6">
                <span class="sig-type-title-tips">公众号</span>
                （须提供小程序与主体公司存在关联关系的证明材图片）
              </el-radio> -->
              <!-- <el-radio :value="7">
                <span class="sig-type-title-tips">网站</span>
                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
              </el-radio> -->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签名类型" prop="signatureSubType">
            <el-radio-group v-model="ruleForm.signatureSubType">
              <el-radio :value="0">
                <span>全称</span>
              </el-radio>
              <el-radio :value="1">
                <span>简称</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="ruleForm.signatureType == '4'" label="APP名称" prop="appName">
            <el-input style="width: 350px;" v-model="ruleForm.appName" placeholder="请输入APP名称"></el-input>
          </el-form-item>
          <el-form-item label="文件上传" prop="imgUrl">
            <div>
              <el-upload class="upload-demo" :headers="headers" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
                :file-list="fileList" list-type="picture-card" :on-preview="handlePictureCardPreview"
                :on-success="handleSuccess" :on-remove="handleRemove">
                <div>
                  <el-icon>
                    <Plus />
                  </el-icon>

                </div>
              </el-upload>
              <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
            </div>
          </el-form-item>
        </el-form>
        <!-- <div style="margin: 10px 10px">请确认以下注意事项：</div>
        <el-checkbox-button style="border: 1px solid #eee; margin-right: 5px; margin-left: 10px"
          v-model="certificatiFlag" label="资质" border @keyup.enter="addAdopt('ruleForm')"></el-checkbox-button>
        <el-checkbox-button style="border: 1px solid #eee" v-model="signatureFlag" label="签名" border
          @keyup.enter="addAdopt('ruleForm')"></el-checkbox-button> -->
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="AdoptVisible = false">取 消</el-button>
            <el-button type="primary" @click="addAdopt('ruleForm')">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 实名信息弹框 -->
      <el-dialog title="实名信息" v-model="checkRealNamevisible" width="900px" draggable :append-to-body="true"
        :close-on-click-modal="false" :before-close="handleClose">
        <el-descriptions v-if="reportId" class="margin-top" title="实名信息" :column="3" size="default" border>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                企业名称
              </div>
            </template>
            {{ realNameInfo.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                社会统一信用代码
              </div>
            </template>
            {{ realNameInfo.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                企业法人
              </div>
            </template>
            {{ realNameInfo.legalPerson }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人姓名
              </div>
            </template>
            {{ realNameInfo.principalName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人证件号码
              </div>
            </template>
            {{ realNameInfo.principalIdCard }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                责任人手机号
              </div>
            </template>
            {{ realNameInfo.principalMobile }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                校验状态
              </div>
            </template>
            <el-tag v-if="realNameInfo.signatureRealNameStatus == 0" type="info" effect="dark">未校验</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 1" type="danger" effect="dark">校验失败</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 2" type="success" effect="dark">校验成功</el-tag>
            <el-tag v-else-if="realNameInfo.signatureRealNameStatus == 3" type="warning" effect="dark">未知</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">
                原因
              </div>
            </template>
            {{ realNameInfo.signatureRealNameReason }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-else>
          <el-empty description="暂无实名信息" />
        </div>
        <el-form :disabled="true" :model="disabledRuleForm" :rules="rules" ref="disabledRuleForm" label-width="90px" class="demo-disabledRuleForm">
          <el-form-item v-if="actionType == '1'" label="短信类型" prop="serviceType">
            <el-select v-model="disabledRuleForm.serviceType" placeholder="请选择短信类型" style="width: 350px;">
              <el-option label="验证码" value="1"></el-option>
              <el-option label="通知" value="2"></el-option>
              <el-option label="营销推广" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="disabledRuleForm.serviceType == '3' && actionType == '1'" label="营销类型" prop="subServiceType">
            <el-select style="width: 350px;" v-model="disabledRuleForm.subServiceType" placeholder="请选择营销类型">
              <el-option v-for="item in subServiceTypeList" :key="item.code" :label="item.desc"
                :value="item.code"></el-option>
              <!-- <el-option label="通知" value="2"></el-option>
                                <el-option label="营销推广" value="3"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item v-if="actionType == '1'" label="短信示例" prop="contentExample">
            <el-input style="height: 100%; width: 350px" v-model="disabledRuleForm.contentExample" type="textarea"
              placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！" maxlength="800" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="签名来源" prop="signatureType">
            <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;"
              v-model="disabledRuleForm.signatureType" @change="changeSignatureType">
              <el-radio :value="1">
                <span>企业名称</span>
              </el-radio>
              <el-radio :value="2">
                <span>事业单位：如机关，学校，科研单位，街道社区等</span>
              </el-radio>
              <el-radio :value="3">
                <span>商标</span>
                （须提供商标注册证书图片或在在中国商标网的商标查询截图）
              </el-radio>
              <el-radio :value="4">
                <span class="sig-type-title-tips">App </span>
                （须提供app在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio :value="5">
                <span class="sig-type-title-tips">小程序</span>
                （须提供小程序在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <!-- <el-radio :value="6">
                <span class="sig-type-title-tips">公众号</span>
                （须提供小程序与主体公司存在关联关系的证明材图片）
              </el-radio> -->
              <!-- <el-radio :value="7">
                <span class="sig-type-title-tips">网站</span>
                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
              </el-radio> -->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签名类型" prop="signatureSubType">
            <el-radio-group v-model="disabledRuleForm.signatureSubType">
              <el-radio :value="0">
                <span>全称</span>
              </el-radio>
              <el-radio :value="1">
                <span>简称</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="disabledRuleForm.signatureType == '4'" label="APP名称" prop="appName">
            <el-input style="width: 350px;" v-model="disabledRuleForm.appName" placeholder="请输入APP名称"></el-input>
          </el-form-item>
          <el-form-item label="文件上传" prop="imgUrl">
            <div>
              <el-upload class="upload-demo" :headers="headers" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
                :file-list="disabledRuleForm.fileList" list-type="picture-card" :on-preview="handlePictureCardPreview"
                :on-success="handleSuccess" :on-remove="handleRemove">
                <div>
                  <el-icon>
                    <Plus />
                  </el-icon>

                </div>
              </el-upload>
              <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
            </div>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="checkRealNamevisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog v-model="dialogVisible">
        <el-image :src="dialogImageUrl" :preview-src-list="[dialogImageUrl]" :append-to-body="true"
          :preview-teleported="true">
        </el-image>
        <!-- <img w-full :src="dialogImageUrl" alt="Preview Image" /> -->
      </el-dialog>

    </div>
  </div>
</template>

<script>
import FileUpload from "@/components/publicComponents/FileUpload.vue"; //文件上传
import TableTem from "@/components/publicComponents/TableTem.vue";
import UserLists from "@/components/publicComponents/userList.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import axios from 'axios';
import getNoce from '../../../plugins/getNoce';

export default {
  components: {
    TableTem,
    FileUpload,
    UserLists,
    Tooltip,
  },
  name: "SignatureAudit",
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: "",
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      fileList: [],
      actionUrl: window.path.omcs + "signatureaudit/uploadFile",
      userFlag: false,
      AdoptVisible: false,
      // certificatiFlag: false,
      // signatureFlag: false,
      // yxFlag: false,
      nameover: "",
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      SigDialogVisible: false, //弹出框创建签名显示隐藏
      remarkshow: false,
      srcList: [],
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: "",
          idArr: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: "请输入拒绝原因", trigger: "blur" },
            {
              min: 1,
              max: 70,
              message: "长度在 1 到 70 个字符",
              trigger: "blur",
            },
          ],
        },
      },
      idArrs: [], // 驳回的id
      title: "",
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          clientName: "",
          signatureType: "1",
          remark: "",
          imgUrl: "",
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "长度在 1 到 20 个字符",
              trigger: "blur",
            },
          ],
          signatureType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            { required: true, message: "请选择上传图片", trigger: "change" },
          ],
        },
        signature: "", //签名
        signatureId: "", //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        consumerName: "",
        roleId: "",
        compId: "",
        excludeUsername: "",
        signature: "",
        time1: [],
        time2: [],
        auditStatus: "1",
        auditUsername: "",
        createStartTime: "",
        createEndTime: "",
        auditStartTime: "",
        auditEndTime: "",
        queryType: "",
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        consumerName: "",
        roleId: "",
        compId: "",
        excludeUsername: "",
        signature: "",
        time1: [],
        time2: [],
        auditStatus: "1",
        auditUsername: "",
        createStartTime: "",
        createEndTime: "",
        auditStartTime: "",
        auditEndTime: "",
        queryType: "",
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      signatureIds: "",
      isFirstEnter: false,
      titleS: "", //签名的类型
      ruleForm: {
        serviceType: "",
        subServiceType: "",
        contentExample: "",
        imgUrl: "",
        signatureType: "",
        signatureSubType: 0,
        appName: ""
      },
      disabledRuleForm: {
        serviceType: "",
        subServiceType: "",
        contentExample: "",
        imgUrl: "",
        signatureType: "",
        signatureSubType: 0,
        appName: "",
        fileList: [],
        copyUrlList: [],
        imgUrlList: [],
        previewList: []
      },
      rules: {
        serviceType: [
          { required: true, message: "请选择短信类型", trigger: "change" },
        ],
        subServiceType: [
          { required: true, message: "请选择营销类型", trigger: "change" },
        ],
        contentExample: [
          { required: true, message: "请输入短信示例", trigger: "change" },
        ],
      },
      // serviceTypeflag: false, //短信类型是否选择
      contentExampleflag: false, //短信示例是否输入
      subServiceTypeList: [], //营销类型列表
      bacthType: "", //批量操作类型
      actionType: "", //操作类型
      reportId: '',
      realNameInfo: {
        companyName: '',
        creditCode: '',
        legalPerson: '',
        principalName: '',
        principalIdCard: '',
        principalMobile: '',
        signatureRealNameStatus: '',
        signatureRealNameReason: ''
      },
      checkRealNamevisible: false, //查看实名信息弹框
      flieULR: [],
      headers: {
        Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
      },
      copyUrl: "",
      copyUrlList: [],
      compNamelist: [],
      loadingcomp: false,
    };
  },
  methods: {
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      // console.log(query, "query");
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.compNamelist = [];
        this.searchAccount();
      }
    },
    // bindChange(val) {
    //   if (val) {
    //     this.sensitiveCondition.compId = val.split("+")[0];
    //     if (val.split("+")[1] != "null") {
    //       this.sensitiveCondition.compId = val.split("+")[1];
    //     } else {
    //       this.sensitiveCondition.compId = "";
    //     }
    //   } else {
    //     this.searchAccount();
    //     this.sensitiveCondition.compId = "";
    //     this.sensitiveCondition.compId = "";
    //   }
    // },
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "signatureaudit/page",
        this.sensitiveConObj,
        (res) => {
          res.records.forEach((record) => {
            if (record.imgUrl) {
              record.imgUrlList = record.imgUrl.split(",").filter(item => item.trim() !== '');
              if (record.imgUrlList.length) {
                record.previewList = record.imgUrlList.map(
                  (url) => window.path.imgU + url
                );
              }
              // this.srcList.push(
              //   ...record.imgUrlList.map((url) => window.path.imgU + url)
              // )
            }
          });
          // res.records.forEach((item) => {
          //     if (item.imgUrl) {
          //         item.imgUrlList = item.imgUrl.split(',');
          //         // this.srcList.push(window.path.imgU + item.imgUrl.split(','))
          //     }
          // })
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.total = res.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    //查询
    sensitiveQuery() {
      if (this.tableDataObj.loading2) return; //防止重复请求
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
    },
    handleClose() {
      this.AdoptVisible = false;
      this.checkRealNamevisible = false;
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields();
      this.sensitiveCondition.createStartTime = "";
      this.sensitiveCondition.createEndTime = "";
      this.sensitiveCondition.auditStartTime = "";
      this.sensitiveCondition.auditEndTime = "";
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.createStartTime =
          this.moment(val[0]).format("YYYY-MM-DD") + " 00:00:00";
        this.sensitiveCondition.createEndTime =
          this.moment(val[1]).format("YYYY-MM-DD") + " 23:59:59";
      } else {
        this.sensitiveCondition.createStartTime = "";
        this.sensitiveCondition.createEndTime = "";
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          this.moment(val[0]).format("YYYY-MM-DD") + " 00:00:00";
        this.sensitiveCondition.auditEndTime =
          this.moment(val[1]).format("YYYY-MM-DD") + " 23:59:59";
      } else {
        this.sensitiveCondition.auditStartTime = "";
        this.sensitiveCondition.auditEndTime = "";
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size;
      this.getTableDtate();
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage;
      this.getTableDtate();
    },
    //查看图片
    lookUp(index, row) {
      this.imgUrl = [];
      if (row.signatureType == "1") {
        this.titleS = "签名类型：公司名称（营业执照截图）";
      } else if (row.signatureType == "2") {
        this.titleS = "签名类型：APP名称（下载链接截图及后台管理截图）";
      } else if (row.signatureType == "3") {
        this.titleS = "签名类型：网站名称（后台备案截图）";
      } else if (row.signatureType == "4") {
        this.titleS = "签名类型：公众号或小程序名称（微信开放平台截图）";
      } else if (row.signatureType == "5") {
        this.titleS = "签名类型：商标名称（商标名称注册证书截图）";
      }
      window.api.get(
        window.path.omcs + "signatureaudit/imgUrl/" + row.signatureId,
        {},
        (res) => {
          if (res != "") {
            let aa = res.split(",");
            let bb = [];
            console.log(aa);
            for (let i = 0; i < aa.length; i++) {
              if (aa[i] != "") {
                bb.push(window.path.imgU + aa[i]);
              }
            }
            console.log(bb, "bb");
            this.imgUrl = bb;
            bb.forEach((item) => {
              this.srcList.push(item);
            });

            this.imagesshow = true;
          } else {
            this.$message({
              message: "无图片",
              type: "warning",
            });
          }
        }
      );
    },
    // lookUpimg(item){
    //     console.log(111);
    //     this.srcList[0] =window.path.imgU + item
    // },
    //删除
    handleEdit(index, row) {
      this.$confirms.confirmation(
        "get",
        "此操作将永久删除该条数据, 是否继续?",
        window.path.omcs + "signatureaudit/delete/" + row.signatureId,
        {},
        (res) => {
          this.getTableDtate();
        }
      );
    },
    getSeverType() {
      window.api.post(
        window.path.omcs + "signatureaudit/allSubServiceType",
        {},
        (res) => {
          if (res.code == 200) {
            this.subServiceTypeList = res.data; //营销类型列表
          } else {
            this.$message({
              type: "error",
              duration: "2000",
              message: res.msg,
            });
          }
        }
      );
    },
    handleCheckRealName(index, row) {
      this.checkRealNamevisible = true;
      if (row.reportId) {
        this.reportId = row.reportId;
        this.realNameInfo.companyName = row.companyName;
        this.realNameInfo.creditCode = row.creditCode;
        this.realNameInfo.legalPerson = row.legalPerson;
        this.realNameInfo.principalName = row.principalName;
        this.realNameInfo.principalIdCard = row.principalIdCard;
        this.realNameInfo.principalMobile = row.principalMobile;
        this.realNameInfo.signatureRealNameStatus = row.signatureRealNameStatus;
        this.realNameInfo.signatureRealNameReason = row.signatureRealNameReason;
        this.disabledRuleForm.serviceType = row.serviceType;
        this.disabledRuleForm.subServiceType = row.subServiceType;
        this.disabledRuleForm.contentExample = row.contentExample;
        this.disabledRuleForm.signatureType = row.signatureType;
        this.disabledRuleForm.signatureSubType = row.signatureSubType;
        this.disabledRuleForm.appName = row.appName;
        this.disabledRuleForm.imgUrl = row.imgUrl;
        this.disabledRuleForm.imgUrlList = row.imgUrl.split(",").filter(item => item.trim() !== '');
        this.disabledRuleForm.previewList = this.disabledRuleForm.imgUrlList.map(
          (url) => window.path.imgU + url
        );
        this.disabledRuleForm.fileList = this.disabledRuleForm.imgUrlList.map((item) => {
          return {
            name: item,
            url: window.path.imgU + item,
          };
        });
        let imgUrlList = JSON.stringify(this.disabledRuleForm.fileList)
        this.disabledRuleForm.copyUrlList = JSON.parse(imgUrlList)
      }
    },
    changeSignatureType(val) {
      if (val == '2') {
        this.ruleForm.imgUrl = "";
        this.fileList = [];
      } else {
        this.fileList = this.copyUrlList;
        this.ruleForm.imgUrl = this.copyUrl;
      }
    },
    //通过
    handleAdopt(index, row) {
      this.AdoptVisible = true;
      this.signatureIds = row.signatureId;
      if (row.serviceType) {
        this.ruleForm.serviceType = row.serviceType + "";
      } else {
        this.ruleForm.serviceType;
      }
      if (row.contentExample) {
        this.ruleForm.contentExample = row.contentExample;
      } else {
        this.ruleForm.contentExample = "";
      }
      this.ruleForm.signatureType = row.signatureType ? row.signatureType : "";
      this.ruleForm.signatureSubType = row.signatureSubType !== undefined && row.signatureSubType !== null ? row.signatureSubType : "";
      this.ruleForm.appName = row.appName || "";
      this.ruleForm.imgUrl = row.imgUrl || "";
      if (row.imgUrl) {
        this.copyUrl = row.imgUrl
        this.flieULR = row.imgUrl.split(",").filter(item => item.trim() !== '');
        this.fileList = this.flieULR.map((item) => {
          return {
            name: item,
            url: window.path.imgU + item,
          };
        });
        let imgUrlList = JSON.stringify(this.fileList)
        this.copyUrlList = JSON.parse(imgUrlList)
      } else {
        this.fileList = [];
      }
      this.bacthType = "1";
      this.actionType = "1";
      this.getSeverType();
      if (row.reportId) {
        this.reportId = row.reportId;
        this.realNameInfo.companyName = row.companyName;
        this.realNameInfo.creditCode = row.creditCode;
        this.realNameInfo.legalPerson = row.legalPerson;
        this.realNameInfo.principalName = row.principalName;
        this.realNameInfo.principalIdCard = row.principalIdCard;
        this.realNameInfo.principalMobile = row.principalMobile;
        this.realNameInfo.signatureRealNameStatus = row.signatureRealNameStatus;
        this.realNameInfo.signatureRealNameReason = row.signatureRealNameReason;
      }
      // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs+'signatureaudit/auditPass',{idArr:[row.signatureId]},res =>{
      //     this.getTableDtate();
      // });
    },
    addAdopt(formRef) {
      this.$refs[formRef].validate((valid) => {
        if (valid) {
          // let data = {
          //   idArr:
          //     this.bacthType == "1"
          //       ? [this.signatureIds]
          //       : this.remarkObj.formData.idArr,
          //   serviceType: this.bacthType == "1" ? this.ruleForm.serviceType : "",
          //   contentExample:
          //     this.bacthType == "1" ? this.ruleForm.contentExample : "",
          //   subServiceType: this.bacthType == "1" ? this.ruleForm.subServiceType : "",
          // };
          // if (this.bacthType == "2" && this.yxFlag) {
          //   data.subServiceType = this.ruleForm.subServiceType;
          // } else if (this.bacthType == "1") {
          //   data.subServiceType = this.ruleForm.subServiceType;
          // }
          let data = {
            idArr: [this.signatureIds],
            serviceType: this.ruleForm.serviceType,
            contentExample: this.ruleForm.contentExample,
            subServiceType: this.ruleForm.subServiceType,
            signatureType: this.ruleForm.signatureType,
            signatureSubType: this.ruleForm.signatureSubType,
            imgUrl: this.ruleForm.imgUrl,
            appName: this.ruleForm.appName,
          }
          this.$confirms.confirmation(
            "post",
            "此操作不可逆，是否继续?",
            window.path.omcs + "signatureaudit/auditPass",
            data,
            (res) => {
              this.getTableDtate();
              this.AdoptVisible = false;
            }
          );
          // if (this.certificatiFlag && this.signatureFlag) {

          // } else {
          //   this.$message({
          //     message: "审核签名时请确认以下事项：资质和签名！",
          //     type: "warning",
          //   });
          // }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //批量通过
    sensitiveBatchDel() {
      this.actionType = "2";
      let status = true;
      //判断选项中是否有审核过的状态
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != "1") {
          status = false;
          break;
        }
      }
      if (status) {
        this.AdoptVisible = true;
        // this.bacthType = "2";
        this.getSeverType();
        // this.$confirms.confirmation('post', '此操作不可逆，是否继续?', window.path.omcs + 'signatureaudit/auditPass', { idArr: this.remarkObj.formData.idArr }, res => {
        //     this.getTableDtate();
        //     this.remarkObj.formData.idArr = [];
        // });
      } else {
        this.$message({
          message: "选项中包含已审核项，需重新选择待审核项！",
          type: "warning",
        });
      }
    },
    //驳回
    handleReject(index, row) {
      this.idArrs = [];
      this.remarkshow = true;
      this.title = "驳回备注";
      this.idArrs.push(row.signatureId); //赋值ID
      this.remarkObj.formData.auditReason = row.prompt;
      if (row.reportId) {
        this.reportId = row.reportId;
        this.realNameInfo.companyName = row.companyName;
        this.realNameInfo.creditCode = row.creditCode;
        this.realNameInfo.legalPerson = row.legalPerson;
        this.realNameInfo.principalName = row.principalName;
        this.realNameInfo.principalIdCard = row.principalIdCard;
        this.realNameInfo.principalMobile = row.principalMobile;
        this.realNameInfo.signatureRealNameStatus = row.signatureRealNameStatus;
        this.realNameInfo.signatureRealNameReason = row.signatureRealNameReason;
      }
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let aa = {};
          if (this.title == "驳回备注") {
            Object.assign(aa, this.remarkObj.formData);
            aa.idArr = this.idArrs;
          } else {
            this.operationFlag = false; //操作进行中（按钮的显示）
            Object.assign(aa, this.remarkObj.formData);
          }
          this.$confirm("此操作不可逆，是否继续?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            closeOnClickModal: false,
            type: "warning",
          })
            .then(() => {
              window.api.post(
                window.path.omcs + "signatureaudit/auditNotPass",
                aa,
                (res) => {
                  this.getTableDtate();
                  this.remarkshow = false;
                  this.remarkObj.formData.idArr = []; //置空ID
                  if (res.code == 200) {
                    this.$message({
                      type: "success",
                      duration: "2000",
                      message: "操作成功!",
                    });
                  } else {
                    this.$message({
                      type: "error",
                      duration: "2000",
                      message: res.msg,
                    });
                  }
                }
              );
            })
            .catch(() => {
              this.operationFlag = true;
              this.$message({
                type: "info",
                duration: "2000",
                message: "已取消操作!",
              });
            });
          // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs + 'signatureaudit/auditNotPass', aa,res =>{
          //         this.getTableDtate();
          //         this.remarkshow = false;
          //         this.remarkObj.formData.idArr=[]; //置空ID
          //     });
        } else {
          return false;
        }
      });
    },
    //批量驳回
    sensitiveBatchSet() {
      let status = true;
      //判断选项中是否有审核过的状态
      this.title = "批量驳回备注";
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != "1") {
          status = false;
          break;
        }
      }
      if (status) {
        this.remarkshow = true;
      } else {
        this.$message({
          message: "选项中包含已审核项，需重新选择待审核项！",
          type: "warning",
        });
      }
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = [];
      let auditStatus = [];
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].signatureId);
        //批量操作列表选中项的审核状态
        auditStatus.push(val.records[i].auditStatus);
      }
      this.auditStatus = auditStatus; //选中项的审核状态
      this.remarkObj.formData.idArr = selectId; //批量通过，不通过的id数组
      this.contentExampleflag = val.records.every((item) => {
        return item.contentExample !== null;
      });

      // console.log(this.serviceTypeflag, 'this.serviceTypeflag')
      if (!this.contentExampleflag) {
        this.$message({
          message: "短信示例为空，不支持批量操作！",
          type: "warning",
        });
        return false;
      }
      // this.yxFlag = val.records.every((item) => {
      //   return item.serviceType == 3;
      // });
    },
    //创建签名
    establishSig() {
      this.SigDialogVisible = true;
      this.del1 = true;
    },
    //提交新增签名的表单
    signature_add(formName, title) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {};
          Object.assign(formDates, this.signatureFrom.formData);
          if (this.signatureFrom.formData.signatureType != 6) {
            if (this.signatureFrom.formData.imgUrl != "") {
              this.sendRequest(
                "post",
                "确定新增签名",
                window.path.omcs + "signatureaudit/addResourceSignature",
                formDates
              );
            } else {
              this.$message({
                message: "请上传图片！",
                type: "warning",
              });
            }
          } else {
            this.sendRequest(
              "post",
              "确定新增签名",
              window.path.omcs + "signatureaudit/addResourceSignature",
              formDates
            );
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //发送新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      window.api.get(
        window.path.omcs +
        "signatureaudit/findModelBySignature/" +
        this.signatureFrom.formData.signature,
        {},
        (res) => {
          if (res.code == 200 && res.data == "0") {
            //发送新增的请求
            this.$confirms.confirmation(
              type,
              title,
              action,
              formDates,
              (res) => {
                this.getTableDtate(); //重载列表
                this.SigDialogVisible = false; //隐藏弹窗
              }
            );
          } else {
            this.$message({
              message: "签名已存在，切勿重复！",
              type: "warning",
            });
          }
        }
      );
    },
    //移除文件
    fileup(val) {
      let aa = this.signatureFrom.formData.imgUrl.split(",");
      aa.splice(aa.indexOf(val), 1);
      this.signatureFrom.formData.imgUrl = aa.join(",");
    },
    //文件上传成功
    fileupres(val) {
      this.signatureFrom.imgUrl.push(val);
      this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
    },
    rouTz(val) {
      // console.log(val,'val');
      this.$router.push({ path: "/UserDetail", query: { id: val.userId } });
    },
    userList(row, index) {
      this.userFlag = true;
      this.nameover = index;
    },
    handleSuccess(res) {
      if (res.code == 200) {
        this.flieULR.push(res.data.fullpath);
        this.ruleForm.imgUrl = this.flieULR.join(",");
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove(file, fileList) {
      if (file.response) {
        this.flieULR.splice(this.flieULR.indexOf(file.response.data.fullpath), 1);
        this.ruleForm.imgUrl = this.flieULR.join(",");
      } else {
        this.flieULR.splice(this.flieULR.indexOf(file.name), 1);
        this.ruleForm.imgUrl = this.flieULR.join(",");
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    exportSignatureData() {
      this.$confirm("确定要导出签名审核失败的数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        closeOnClickModal: false
      }).then(async () => {
        // 使用当前查询条件进行导出
        // let exportParams = { ...this.sensitiveConObj };
        // // 移除分页参数
        // delete exportParams.currentPage;
        // delete exportParams.pageSize;

        // 设置loading提示
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        try {
          // 获取nonce
          const nonce = await getNoce.useNonce();

          // 使用axios处理二进制流下载
          const response = await axios({
            method: 'post',
            url: window.path.omcs + "signatureaudit/export",
            data: this.sensitiveCondition,
            responseType: 'blob', // 指定响应类型为blob
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              'Authorization': 'Bearer ' + window.common.getCookie("ZTADMIN_TOKEN"),
              'Once': nonce // 添加Once头
            }
          });

          loading.close();

          // 从响应头获取文件名
          let filename = '签名审核数据.xlsx';
          const disposition = response.headers['content-disposition'];
          if (disposition && disposition.includes('filename=')) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches && matches[1]) {
              filename = matches[1].replace(/['"]/g, '');
            }
          }

          // 生成带时间戳的文件名
          const now = new Date();
          const timestamp = now.getFullYear() +
            ('0' + (now.getMonth() + 1)).slice(-2) +
            ('0' + now.getDate()).slice(-2) +
            ('0' + now.getHours()).slice(-2) +
            ('0' + now.getMinutes()).slice(-2) +
            ('0' + now.getSeconds()).slice(-2);

          // 处理原文件名，添加时间戳
          const fileNameParts = filename.split('.');
          const extension = fileNameParts.pop(); // 获取文件扩展名
          // const nameWithoutExt = fileNameParts.join('.'); // 获取不带扩展名的文件名
          filename = `${timestamp}.${extension}`;

          // 创建Blob对象
          const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

          // 创建下载链接
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = filename;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();

          // 清理
          window.URL.revokeObjectURL(link.href);
          document.body.removeChild(link);

          this.$message({
            type: 'success',
            message: '导出成功'
          });
        } catch (error) {
          loading.close();

          // 处理错误情况
          if (error.response) {
            // 服务器返回了错误状态码
            if (error.response.data instanceof Blob) {
              // 如果响应是Blob对象，可能是错误信息的JSON
              const reader = new FileReader();
              reader.onload = () => {
                try {
                  const errorJson = JSON.parse(reader.result);
                  this.$message({
                    type: 'error',
                    message: errorJson.msg || '导出失败，请稍后重试',
                    duration: 5000
                  });
                } catch (e) {
                  this.$message({
                    type: 'error',
                    message: '导出失败，HTTP状态码: ' + error.response.status,
                    duration: 5000
                  });
                }
              };
              reader.readAsText(error.response.data);
            } else {
              this.$message({
                type: 'error',
                message: '导出失败，HTTP状态码: ' + error.response.status,
                duration: 5000
              });
            }
          } else if (error.request) {
            // 请求已发送但没有收到响应
            this.$message({
              type: 'error',
              message: '网络错误，未收到服务器响应',
              duration: 5000
            });
          } else {
            // 请求设置时发生错误
            this.$message({
              type: 'error',
              message: '导出请求错误: ' + error.message,
              duration: 5000
            });
          }

          console.error('导出错误:', error);
        }
      }).catch(() => {
        this.$message({
          type: "info",
          message: "已取消导出操作"
        });
      });
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.getTableDtate();
      this.getSeverType();
    });
    // this.getTableDtate();
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate();
        this.getSeverType();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
    // this.getTableDtate();
  },
  watch: {
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听设置标签弹框是否关闭
    setLabelDialog(val) {
      if (this.$refs.setLabel) {
        if (val == false) {
          this.$refs.setLabel.resetFields();
        }
      }
    },
    AdoptVisible(val) {
      const $table = this.$refs.tableRef;
      if (!val) {
        // this.certificatiFlag = false
        // this.signatureFlag = false
        this.signatureIds = "";
        // this.yxFlag = false;
        this.$refs["ruleForm"].resetFields();
        this.ruleForm.signatureType = ''
        this.ruleForm.signatureSubType = ''
        this.ruleForm.appName = '';
        this.ruleForm.imgUrl = '';
        this.fileList = [];
        this.flieULR = [];
        this.copyUrlList = [];
        this.copyUrl = "";
        $table.clearCheckboxRow()
        this.reportId = "";
        this.realNameInfo = {
          companyName: "",
          creditCode: "",
          legalPerson: "",
          principalName: "",
          principalIdCard: "",
          signatureRealNameStatus: "",
          signatureRealNameReason: "",
        }
      }
    },
    //监听创建签名弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.signatureFrom.imgUrl = [];
        this.signatureFrom.formData.imgUrl = "";
        this.del1 = false;
        this.$refs.signatureFrom.resetFields(); //清空表单
      }
    },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true;
      } else {
        this.disabledinput = false;
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.operationFlag = true; //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields();
          this.remarkObj.formData.idArr = []; //置空ID
          this.getTableDtate();
          this.reportId = "";
          this.realNameInfo = {
            companyName: "",
            creditCode: "",
            legalPerson: "",
            principalName: "",
            principalIdCard: "",
            signatureRealNameStatus: "",
            signatureRealNameReason: "",
          }
        }
      }
    },
    checkRealNamevisible(val) {
      if (!val) {
        this.reportId = "";
        this.realNameInfo = {
          companyName: "",
          creditCode: "",
          legalPerson: "",
          principalName: "",
          principalIdCard: "",
          signatureRealNameStatus: "",
          signatureRealNameReason: "",
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.sensitive-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.tips {
  margin-top: 30px;
}

.margin-top {
  margin-bottom: 10px;
}

.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}

.sensitive {
  margin-bottom: 10px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.el-checkbox-button__inner {
  border: none;
}

:deep(.el-textarea__inner) {
  height: 80px;
}

:deep(.el-empty__image) {
  width: 45px;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}

.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}

/* 修复图片预览蒙层问题 */
.el-image-viewer__wrapper {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 99999 !important;
}

.el-image-viewer__mask {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  /* z-index: 99998 !important; */
}

.el-image-viewer__canvas {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}
</style>
