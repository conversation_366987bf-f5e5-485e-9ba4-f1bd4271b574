<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder="请输入用户名"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              class="input-w"
              v-model="formInline.status"
              placeholder="选择状态"
              clearable
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="待处理" value="0"></el-option>
              <el-option label="处理中" value="1"></el-option>
              <el-option label="处理完成 " value="2"></el-option>
              <el-option label="处理失败" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间 " prop="time">
            <el-date-picker
              v-model="formInline.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleTime1"
            >
            </el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div style="margin: 10px 0">
        <!-- <span class="Signature-list-header">路由列表</span> -->
        <!-- <el-button type="primary" @click="addopt">创建任务</el-button> -->
        <!-- <el-button type="primary" style="margin-left:15px;" @click="Synchronize()">同 步</el-button>
              <el-button type="primary" @click="handeStart()" v-if="selectId.length != 0">批量启用</el-button>
              <el-button type="warning" @click="handeStop()" v-if="selectId.length != 0">批量停用</el-button> -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="shortTask"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="46"></vxe-column>
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="参数" title="参数">
            <template v-slot="scope">
              <el-tooltip effect="light" placement="top">
                <template v-slot:content>
                  <div>
                    <el-table
                      :data="scope.row.paramsList"
                      style="width: 100%"
                      border
                    >
                      <el-table-column prop="province" label="省份" width="180">
                      </el-table-column>
                      <el-table-column
                        prop="count"
                        label="提取数量"
                        width="180"
                      >
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
                <el-icon style="color: #409eff; cursor: pointer; font-size: 19px"><Operation /></el-icon>
              </el-tooltip>
            </template>
          </vxe-column>
          <vxe-column field="开始时间" title="开始时间">
            <template v-slot="scope">
              <div v-if="scope.row.beginTime">{{
                moment(scope.row.beginTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="结束时间" title="结束时间">
            <template v-slot="scope">
              <div v-if="scope.row.endTime">{{
                moment(scope.row.endTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                待处理
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
              >
                处理中
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 2"
                type="success"
                effect="dark"
              >
                处理完成
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="danger"
                effect="dark"
              >
                处理失败
              </el-tag>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="100" fixed="right">
            <template v-slot="scope">
              <el-button link style="color: #409eff;" @click="hangleStatistics(scope.row)"
                >统 计</el-button
              >
              <!-- <el-button type="text" style="color: red;" @click="cancel(scope.row)">删 除</el-button> -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="800px"
      >
        <el-form
          :inline="true"
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <div>
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="formop.username"
                autocomplete="off"
                class="input-w"
                placeholder="用户名"
              ></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="时间段" prop="time">
              <el-date-picker
                v-model="formop.time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleTime"
              >
              </el-date-picker>
              <el-button
                style="margin-left: 10px"
                type="primary"
                link
                circle
                @click="handleSearch('formop')"
              ><el-icon><Search /></el-icon></el-button>
              <div style="color: #909399">
                tips: 请根据用户名和时间段，查询提取数量！
              </div>
            </el-form-item>
          </div>
          <template v-if="formop.provinceData.length > 0">
            <div style="color: #606266; margin-left: 60px; margin-bottom: 10px">
              下发短信总条数为：{{ smsTotal }}条
            </div>
            <div
              v-for="(item, index) in formop.provinceData"
              :key="index + 'a'"
            >
              <el-form-item
                label="短链信息"
                :prop="'provinceData.' + index + '.province'"
              >
                <div
                  style="
                    display: flex;
                    border-radius: 5px;
                    background-color: #f5f7fa;
                    border-color: #e4e7ed;
                    color: #c0c4cc;
                    border: 1px solid;
                    padding: 0 10px;
                  "
                >
                  <div>
                    省份
                    <span style="margin-left: 8px">{{ item.province }}</span>
                  </div>
                  <div style="margin-left: 18px">
                    短链总数
                    <span style="margin-left: 8px">{{ item.countSum }}个</span>
                  </div>
                </div>
                <!-- <el-input disabled v-model="item.province" autocomplete="off" class="input-w" placeholder="省份"></el-input> -->
              </el-form-item>
              <el-form-item
                label="提取数量"
                :prop="'provinceData.' + index + '.count'"
                :rules="{
                  required: true,
                  message: '提取数量不能为空',
                  trigger: 'blur',
                }"
              >
                <el-input-number
                  class="input-w"
                  v-model="item.count"
                  :min="1"
                  :max="item.countSum"
                  label="提取数量"
                ></el-input-number>
                <!-- <el-input v-model="item.count" autocomplete="off" class="input-w" placeholder="数量"></el-input> -->
              </el-form-item>
            </div>
          </template>
          <div v-else>
            <el-empty :image-size="100"></el-empty>
          </div>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
export default {
  data() {
    var numberRules = (rule, value, callback) => {
      if (!/^([0-9][0-9]{0,3}|100)$/.test(value)) {
        return callback(new Error('请输入0-100的数字'))
      } else {
        callback()
      }
    }
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      titleMap: '创建任务',
      isFirstEnter: false,
      //新增弹出框显示隐藏
      dialogFormVisible: false,
      ids: '',
      selectId: [],
      expireTime: '',
      formop: {
        username: '',
        startTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ],
        provinceData: [],
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'change' },
        ],
        time: [{ required: true, message: '请选择时间', trigger: 'change' }],
      },
      formInline: {
        username: '',
        status: '',
        time: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        status: '',
        time: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
      smsTotal: 0,
    }
  },
  components: {
    TableTem,
    Tooltip
  },
  name: 'shortTask',
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingshortlinkextractiontask/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      this.formInline.startTime = ''
      this.formInline.endTime = ''
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    // 新增 编辑
    addopt() {
      this.titleMap = '创建任务'
      this.dialogFormVisible = true
    },
    hangleStatistics(row) {
      this.$router.push({
        path: '/shortStatistics',
        query: {
          id: row.id,
        },
      })
    },
    // edit(val) {
    //   this.titleMap = '编辑'
    //   this.dialogFormVisible = true
    //   //清除编辑默认值
    //   this.$nextTick(() => {
    //     this.$refs.formop.resetFields();
    //     Object.assign(this.formop, val);
    //     this.expireTime = val.expireTime
    //     this.formop.hyOpen += ""
    //     // console.log(this.formop,'for');
    //     this.formop.productId += ''
    //   })
    // },
    handleTime1(val) {
      if (val) {
        this.formInline.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formInline.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
    },
    handleTime(val) {
      if (val) {
        this.formop.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formop.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formop.startTime = ''
        this.formop.endTime = ''
      }
    },
    handleSearch(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let data = {
            username: this.formop.username,
            startTime: this.formop.startTime,
            endTime: this.formop.endTime,
          }
          window.api.post(
            window.path.omcs + 'operatingdivert/message/statistics',
            data,
            (res) => {
              if (res.code == 200) {
                if (res.data.provinceData.length == 0) {
                  this.$message.warning('暂无数据!')
                }
                this.formop.provinceData = res.data.provinceData.map((item) => {
                  return {
                    province: item.province,
                    countSum: item.count,
                    count: '',
                  }
                })

                this.smsTotal = res.data.total
              } else {
                this.$message.error(res.msg)
              }
            }
          )
        } else {
          this.$message.error('请确认信息是否有遗漏！')
          return false
        }
      })
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let data = {
            username: this.formop.username,
            beginTime: this.formop.startTime,
            endTime: this.formop.endTime,
            // provinceNumList: this.formop.provinceData,
          }
          data.provinceNumList = this.formop.provinceData.map((item) => {
            return {
              province: item.province,
              count: item.count,
            }
          })
          if (this.formop.provinceData.length) {
            window.api.post(
              window.path.omcs + 'operatingshortlinkextractiontask',
              data,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                  this.$message({
                    type: 'success',
                    duration: '2000',
                    message: res.msg,
                  })
                } else {
                  this.$message.error(res.msg)
                }
              }
            )
          } else {
            this.$message.error('该用户暂无提取数量，请确认！')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 同步
    // Synchronize() {
    //   this.$confirms.confirmation('post', '确定同步缓存操作？', window.path.omcs + 'operatingdivertclientsetting/synDivert', {}, res => {
    //     this.gettableLIst()
    //   })
    // },
    // // 停用启用
    // Enable(val) {
    //   if (val.status == 1) {
    //     this.$confirms.confirmation('post', '确定停用操作？', window.path.omcs + 'operatingdivertclientsetting/disable/' + val.id, {}, res => {
    //       this.gettableLIst()
    //     })
    //   } else {
    //     this.$confirms.confirmation('post', '确定启用操作？', window.path.omcs + 'operatingdivertclientsetting/enable/' + val.id, {}, res => {
    //       this.gettableLIst()
    //     })
    //   }

    // },
    // 删除
    cancel(val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs + 'operatingdivertclientsetting/' + val.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    //批量
    handleSelectionChange(row) {
      if (row.records.length > 0) {
        let arr = []
        row.records.forEach(item => {
          arr.push(item.id)
        })
        this.selectId = arr.join(",");
      } else {
        this.selectId = ""
      }
    },
    //批量启用
    // handeStart() {
    //   this.$confirms.confirmation('post', '批量启用投诉控制分流！', window.path.omcs + 'operatingdivertclientsetting/batch/enable?ids=' + this.ids, {}, res => {
    //     this.gettableLIst()
    //     this.selectId = []
    //   })
    // },
    // //批量停用
    // handeStop() {
    //   this.$confirms.confirmation('post', '批量停用投诉控制分流！', window.path.omcs + 'operatingdivertclientsetting/batch/disable?ids=' + this.ids, {}, res => {
    //     this.gettableLIst()
    //     this.selectId = []
    //   })
    // }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
        this.formop.provinceData = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>