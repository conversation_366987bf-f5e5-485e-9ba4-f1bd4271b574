<template>
  <div class="header-tools">
    <div class="btn-fullscreen" @click="handleFullScreen">
      <el-tooltip
        effect="dark"
        :content="fullscreen ? `取消全屏` : `全屏`"
        placement="bottom"
      >
        <el-icon><FullScreen /></el-icon>
      </el-tooltip>
    </div>
    <!-- 短链检索 -->
    <div class="btn-bell" @click="$emit('show-shortlink')">
      <el-tooltip effect="dark" content="短链检索" placement="bottom">
        <i
          class="iconfont icon-jiansuo"
          style="color: #fff; font-size: 26px !important;"
        ></i>
      </el-tooltip>
    </div>
    <!-- 加密工具 -->
    <div class="btn-bell" @click="$emit('show-encipher')">
      <el-tooltip effect="dark" content="加密工具" placement="bottom">
        <i
          class="iconfont icon-mima1"
          style="color: #fff; font-size: 28px !important;"
        ></i>
      </el-tooltip>
    </div>
    <!-- 短信测试 -->
    <div class="btn-bell" @click="$emit('show-sms')">
      <el-tooltip effect="dark" content="短信测试" placement="bottom">
        <i
          class="iconfont icon-fasong"
          style="color: #fff; font-size: 26px !important;"
        ></i>
      </el-tooltip>
    </div>
    <!-- 视频压缩 -->
    <div class="btn-bell" @click="$emit('generate-video')">
      <el-tooltip effect="dark" content="视频压缩" placement="bottom">
        <i
          class="iconfont icon-shengchengshipin"
          style="color: #fff; font-size: 26px !important; margin-left: 10px"
        ></i>
      </el-tooltip>
    </div>
    <!-- 内容计算 -->
    <div class="btn-bell" @click="$emit('calculate')">
      <el-tooltip effect="dark" content="内容计算" placement="bottom">
        <i
          class="iconfont icon-jisuanqi"
          style="color: #fff; font-size: 26px !important; margin-left: 10px"
        ></i>
      </el-tooltip>
    </div>
    <!-- 签名查询 -->
    <div class="btn-bell" @click="$emit('show-signature')">
      <el-tooltip effect="dark" content="签名查询" placement="bottom">
        <i
          class="iconfont icon-qianming"
          style="color: #fff; font-size: 26px !important; margin-left: 10px"
        ></i>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { FullScreen } from '@element-plus/icons-vue'

export default {
  name: 'HeaderTools',
  components: {
    FullScreen
  },
  data() {
    return {
      fullscreen: false
    }
  },
  methods: {
    // 全屏事件
    handleFullScreen() {
      let element = document.documentElement
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen()
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen()
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen()
        }
      }
      this.fullscreen = !this.fullscreen
    }
  },
  emits: ['show-shortlink', 'show-encipher', 'show-sms', 'generate-video', 'calculate', 'show-signature']
}
</script>

<style scoped>
.header-tools {
  display: flex;
  align-items: center;
}

.btn-fullscreen {
  margin-right: 5px;
  font-size: 28px;
}

.btn-bell,
.btn-fullscreen {
  position: relative;
  width: 36px;
  height: 36px;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;
  margin: 10px 10px 0 16px;
}

.btn-bell-badge {
  position: absolute;
  right: 0;
  top: -2px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #f56c6c;
  color: #fff;
}
</style> 