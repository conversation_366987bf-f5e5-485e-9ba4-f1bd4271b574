<template>
  <div class="container_left">
    <div style="padding: 10px">
      <!-- <el-form :model="ruleForm" :inline="true" ref="ruleForm" class="demo-ruleForm" label-width="82px">
                    <el-form-item label="模板类型" prop="kindId">
                        <el-select class="input-w" v-model="ruleForm.kindId" placeholder="请选择">
                            <el-option label="全部类型" value=""></el-option>
                            <el-option v-for="item in kindsList" :label="item.name" :value="item.id" :key="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="华为" prop="hwSupport">
                        <el-select class="input-w" v-model="ruleForm.hwSupport" placeholder="请选择">
                            <el-option label="启用" :value=true></el-option>
                            <el-option label="禁用" :value=false></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="聚梦AIM" prop="jmSupport">
                        <el-select class="input-w" v-model="ruleForm.jmSupport" placeholder="请选择">
                            <el-option label="启用" :value=true></el-option>
                            <el-option label="禁用" :value=false></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="南通移动" prop="ntSupport">
                        <el-select class="input-w" v-model="ruleForm.ntSupport" placeholder="请选择">
                            <el-option label="全部类型" :value="null"></el-option>
                            <el-option label="启用" :value=true></el-option>
                            <el-option label="禁用" :value=false></el-option>
                        </el-select>
                    </el-form-item>
                    <div>
                        <el-button plain type="primary" @click="submitForm('ruleForm')">查询</el-button>
                        <el-button plain type="primary" @click="resetForm('ruleForm')">重置</el-button>
                    </div>
                </el-form> -->
    </div>
    <div style="padding: 0 10px"></div>
    <div style="padding: 10px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
      >
        <el-table-column label="渠道">
          <template v-slot="scope">
            <span>{{ scope.row.channelName }}</span>
            <!-- <div v-for="(item, index) in kindsList" :key="item.id">
                                <span v-if="item.id == scope.row.kindId">{{ item.name }}</span>
                            </div> -->
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template v-slot="scope">
            <!-- <el-switch v-model="scope.row.enable" active-color="#409EFF"
                                inactive-color="#909399" @change="handleSwitch(scope.row)">
                            </el-switch> -->
            <el-switch
              :model-value="scope.row.enable"
              active-color="#409EFF"
              inactive-color="#909399"
              @click="handleSwitch(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <!-- <el-table-column label="聚梦AIM">
                        <template #default="scope">
                            <el-switch :disabled="!scope.row.jmDisabled" v-model="scope.row.jmSupport" active-color="#13ce66"
                                inactive-color="#ff4949" @change="handleSwitch(scope.row, 'jmSupport')">
                            </el-switch>
                        </template>
                    </el-table-column>
                    <el-table-column label="南通移动">
                        <template #default="scope">
                            <el-switch :disabled="!scope.row.ntDisabled" v-model="scope.row.ntSupport" active-color="#13ce66"
                                inactive-color="#ff4949" @change="handleSwitch(scope.row, 'ntSupport')">
                            </el-switch>
                        </template>
                    </el-table-column> -->
        <el-table-column label="更新时间">
          <template v-slot="scope">
            <span v-if="scope.row.updateTime">{{
              moment(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新人">
          <template v-slot="scope">
            <span>{{ scope.row.updateUser }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <div></div>
        <div style="margin: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="ruleForm.currentPage"
            :page-size="ruleForm.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'yuexinTemplateChannel',
  data() {
    return {
      kindsList: [],
      ruleForm: {
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTempList()
      this.getchatList()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTempList()
        this.getchatList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getTempList() {
      window.api.post(
        window.path.yuexin + '/operator/template/kind/page',
        {
          currentPage: 1,
          pageSize: 100,
        },
        (res) => {
          if (res.code == 200) {
            this.kindsList = res.data.records
          }
        }
      )
    },
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.yuexin + '/operator/template/kind/channel/page',
        this.ruleForm,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
          // for (let i = 0; i < this.tableDataObj.tableData.length; i++) {
          //     for (let j = 0; j < this.tableDataObj.tableData[i].channelDTO.length; j++) {
          //         if (this.tableDataObj.tableData[i].channelDTO[j].channelOperator == "南通移动") {
          //             this.tableDataObj.tableData[i].ntDisabled = this.tableDataObj.tableData[i].channelDTO[j].isPublic;
          //             this.tableDataObj.tableData[i].ntChanelId = this.tableDataObj.tableData[i].channelDTO[j].channelId;
          //         } else if (this.tableDataObj.tableData[i].channelDTO[j].channelOperator == "华为Koomessage") {
          //             this.tableDataObj.tableData[i].hwDisabled = this.tableDataObj.tableData[i].channelDTO[j].isPublic;
          //             this.tableDataObj.tableData[i].hwChanelId = this.tableDataObj.tableData[i].channelDTO[j].channelId;
          //         } else if (this.tableDataObj.tableData[i].channelDTO[j].channelOperator == "聚梦AIM") {
          //             this.tableDataObj.tableData[i].jmDisabled = this.tableDataObj.tableData[i].channelDTO[j].isPublic;
          //             this.tableDataObj.tableData[i].jmChanelId = this.tableDataObj.tableData[i].channelDTO[j].channelId;
          //         }
          //     }
          // }
        }
      )
    },
    handleSizeChange(size) {
      this.ruleForm.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.ruleForm.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      // Object.assign(this.ruleForm, this.ruleForm);
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.getchatList()
    },
    handleSwitch(row) {
      let tips
      if (row.enable) {
        tips = '是否关闭' + row.channelName + '渠道'
      } else {
        tips = '是否开启' + row.channelName + '渠道'
      }

      let data = {
        id: row.id,
        enable: row.enable,
      }
      // if (row.hwSupport && row.jmSupport && row.ntSupport) {
      //     data.channelId = row.hwChanelId + ',' + row.jmChanelId + ',' + row.ntChanelId
      // } else if (row.hwSupport && row.jmSupport && !row.ntSupport) {
      //     data.channelId = row.hwChanelId + ',' + row.jmChanelId
      // } else if (row.hwSupport && !row.jmSupport && row.ntSupport) {
      //     data.channelId = row.hwChanelId + ',' + row.ntChanelId
      // } else if (!row.hwSupport && row.jmSupport && row.ntSupport) {
      //     data.channelId = row.jmChanelId + ',' + row.ntChanelId
      // } else if (row.hwSupport && !row.jmSupport && !row.ntSupport) {
      //     data.channelId = row.hwChanelId
      // } else if (!row.hwSupport && row.jmSupport && !row.ntSupport) {
      //     data.channelId = row.jmChanelId
      // } else if (!row.hwSupport && !row.jmSupport && row.ntSupport) {
      //     data.channelId = row.ntChanelId
      // } else {
      //     data.channelId = ''
      // }
      // if (key == 'hwSupport') {
      //     tips = '是否开启华为Koomessage模板推送？'
      //     data.hwSupport = row.hwSupport
      // } else if (key == 'jmSupport') {
      //     tips = '是否开启聚梦AIM模板推送？'
      //     data.jmSupport = row.jmSupport
      // } else if (key == 'ntSupport') {
      //     tips = '是否开启南通移动模板推送？'
      //     data.ntSupport = row.ntSupport
      // }

      this.$confirm(tips, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          data.enable = !data.enable
          window.api.post(
            window.path.yuexin + '//operator/template/kind/channel/update',
            data,
            (res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'info',
                  message: '操作成功',
                })
                this.getchatList()
              } else {
                this.$message({
                  type: 'error',
                  message: res.message,
                })
                // if (key == 'hwSupport') {
                //     row.hwSupport = !row.hwSupport
                // } else if (key == 'jmSupport') {
                //     row.jmSupport = !row.jmSupport
                // } else if (key == 'ntSupport') {
                //     row.ntSupport = !row.ntSupport
                // }
              }
            }
          )
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '操作已取消',
          })
          // if (key == 'hwSupport') {
          //     row.hwSupport = !row.hwSupport
          // } else if (key == 'jmSupport') {
          //     row.jmSupport = !row.jmSupport
          // } else if (key == 'ntSupport') {
          //     row.ntSupport = !row.ntSupport
          // }
          // this.tableDataObj.tableData.forEach(item => {
          //     if (item.id == row.id) {

          //     }
          // })
        })
    },
  },
  watch: {
    echartsDialogVisible(val) {
      if (!val) {
        this.treeData = {}
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
.statistic_bar {
  width: 100%;
  height: 460px;
  background: #fff;
  margin-top: 10px;
  position: relative;
}
.bar-xsms {
  width: 100%;
  height: 400px;
  position: absolute;
  top: 0%;
  margin-top: 20px;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
  display: flex;
}
.Templat-matter > p {
  padding: 5px 0;
}
</style>

<style>
/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>
