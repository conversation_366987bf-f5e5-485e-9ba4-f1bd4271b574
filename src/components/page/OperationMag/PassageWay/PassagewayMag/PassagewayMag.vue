<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="84px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="通道编号" prop="channelCode">
            <el-input
              v-model="formInline.channelCode"
              @keyup.enter="Query"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>

          <el-form-item label="通道名称" prop="channelName">
            <el-input
              v-model="formInline.channelName"
              @keyup.enter="Query"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>

          <el-form-item label="运营商类型" prop="operatorsType">
            <el-select
              v-model="formInline.operatorsType"
              clearable
              @keyup.enter="Query"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
              <el-option label="三网" value="4"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="通道类型" prop="productId">
            <el-select
              v-model="formInline.productId"
              clearable
              @keyup.enter="Query"
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="item in productList"
                :key="item.productId"
                :label="item.name"
                :value="item.productId"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="通道状态" prop="channelStatus">
            <el-select
              v-model="formInline.channelStatus"
              clearable
              @keyup.enter="Query"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网关类型" prop="isPrivate">
            <el-select
              v-model="formInline.pactType"
              clearable
              @keyup.enter="Query"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="新网关" value="2"></el-option>
              <el-option label="旧网关" value="1"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="是否专属" prop="isPrivate">
                <el-select v-model="formInline.isPrivate" placeholder="请选择" @keyup.enter.native='Query' class="input-w">
                  <el-option label="全部" value></el-option>
                  <el-option label="是" value="2"></el-option>
                  <el-option label="否" value="1"></el-option>
                </el-select>
              </el-form-item> -->
          <el-form-item label="区域" prop="channelFlag">
            <el-select
              v-model="formInline.channelFlag"
              clearable
              @keyup.enter="Query"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全网" value="0"></el-option>
              <el-option label="省网" value="1"></el-option>
              <el-option label="全网+省网" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="IP" prop="ip">
            <el-input
              v-model="formInline.ip"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item>
           
              </el-form-item> -->
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
        <el-button type="primary" plain style @click="addtongdao"
          >添加通道</el-button
        >
      </div>
      <div class="sensitive-fun">
        <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">通道列表</span> -->
      </div>
      <div class="Mail-table tableTem">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          :data="tableDataObj.tableData"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons> </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="PassagewayMag"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
        >
          <vxe-column
            type="expand"
            field="展开项"
            title="展开项"
            align="center"
            width="70"
          >
            <template #content="{ row }">
              <div style="padding: 0 20px">
                <el-form label-position="left" inline class="demo-table-expand">
                  <el-form-item label="通道单价:" class="expand2Line">
                    <div>{{ row.channelUnitPrice }}</div>
                  </el-form-item>
                  <el-form-item label="下行流速:" class="expand2Line">
                    <div>{{ row.descendingVelocity }}</div>
                  </el-form-item>
                  <el-form-item label="运营商类型:" class="expand2Line">
                    <div>{{ row.operatorsType }}</div>
                  </el-form-item>
                  <el-form-item label="运营商名称:" class="expand2Line">
                    <div>{{ row.operatorName }}</div>
                  </el-form-item>
                  <el-form-item label="通道号码:" class="expand2Line">
                    <div>{{ row.channelNumber }}</div>
                  </el-form-item>
                  <el-form-item label="接口方式:" class="expand2Line">
                    <div>{{ row.interfaceMode }}</div>
                  </el-form-item>
                  <el-form-item label="是否需要提前报备:" class="expand2Line">
                    <div>{{ row.madeReported }}</div>
                  </el-form-item>
                  <el-form-item label="区域:" class="expand2Line">
                    <div>{{ row.isProvincialStr }}</div>
                  </el-form-item>
                  <el-form-item label="批次容量:" class="expand2Line">
                    <div>{{ row.batch }}</div>
                  </el-form-item>
                </el-form>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="通道编号" title="通道编号">
            <template v-slot="scope">
              <div
                @click="save(scope.row.channelCode)"
                style="color: rgb(22, 165, 137); cursor: pointer"
                >{{ scope.row.channelCode }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="通道名称" title="通道名称" width="150">
            <template v-slot="scope">
              <div>{{ scope.row.channelName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="验证码队列" title="验证码队列" width="100">
            <template v-slot="scope">
              <div style="display: flex">
                <div>
                  <span v-if="scope.row.yzmNum == 0">{{
                    scope.row.yzmNum
                  }}</span>
                  <span
                    v-else
                    @click="channeClear(scope.row, 'YZM')"
                    style="color: red; cursor: pointer"
                    >{{ scope.row.yzmNum }}</span
                  >
                </div>
                <!-- <span>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
                    <div>
                      <span v-if="scope.row.yzmNumV2 == 0">{{ scope.row.yzmNumV2 }}</span>
                      <span v-else @click="channeClearQueue(scope.row, 'YZM')" style="color: red; cursor: pointer">{{
                        scope.row.yzmNumV2 }}</span>
                    </div> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="行业队列" title="行业队列">
            <template v-slot="scope">
              <div style="display: flex">
                <div>
                  <span v-if="scope.row.hyNum == 0">{{ scope.row.hyNum }}</span>
                  <span
                    v-else
                    @click="channeClear(scope.row, 'HY')"
                    style="color: red; cursor: pointer"
                    >{{ scope.row.hyNum }}</span
                  >
                </div>
                <!-- <span>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
                    <div>
                      <span v-if="scope.row.hyNumV2 == 0">{{ scope.row.hyNumV2 }}</span>
                      <span v-else @click="channeClearQueue(scope.row, 'YZM')" style="color: red; cursor: pointer">{{
                        scope.row.hyNumV2 }}</span>
                    </div> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="营销队列" title="营销队列">
            <template v-slot="scope">
              <div style="display: flex">
                <div>
                  <span v-if="scope.row.yxNum == 0">{{ scope.row.yxNum }}</span>
                  <span
                    v-else
                    @click="channeClear(scope.row, 'YX')"
                    style="color: red; cursor: pointer"
                    >{{ scope.row.yxNum }}</span
                  >
                </div>
                <!-- <span>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
                    <div>
                      <span v-if="scope.row.yxNumV2 == 0">{{ scope.row.yxNumV2 }}</span>
                      <span v-else @click="channeClearQueue(scope.row, 'YZM')" style="color: red; cursor: pointer">{{
                        scope.row.yxNumV2 }}</span>
                    </div> -->
              </div>
            </template>
          </vxe-column>

          <vxe-column field="备注" title="备注">
            <template v-slot="scope">
              <div>{{ scope.row.remark }}</div>
            </template>
          </vxe-column>
          <vxe-column field="最近启用时间" title="最近启用时间" width="160">
            <template v-slot="scope">
              <!-- <p v-if="scope.row.lastStartTime">{{ $parseTime(scope.row.lastStartTime, '{y}-{m}-{d}') }}</p>
                  <p v-if="scope.row.lastStartTime">{{ $parseTime(scope.row.lastStartTime, '{h}:{i}:{s}') }}</p> -->
              <div v-if="scope.row.lastStartTime">{{
                scope.row.lastStartTime
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="网关类型" title="网关类型">
            <template v-slot="scope">
              <div v-if="scope.row.pactType == '1'">旧网关</div>
              <div v-else>新网关</div>
            </template>
          </vxe-column>
          <vxe-column field="通道类型" title="通道类型">
            <template v-slot="scope">
              <div v-for="item in productList" :key="item.productId">
                <div v-if="item.productId == scope.row.productId">{{
                  item.name
                }}</div>
              </div>
              <!-- <span v-if="scope.row.productId == 1">短信通道</span>
                  <span v-else-if="scope.row.productId == 2">彩信通道</span>
                  <span v-else-if="scope.row.productId == 3">视频通道</span>
                  <span v-else-if="scope.row.productId == 4">国际通道</span>
                  <span v-else-if="scope.row.productId == 6">语音验证码</span>
                  <span v-else-if="scope.row.productId == 7">语音通知</span>
                  <span v-else-if="scope.row.productId == 9">5G消息</span> -->
            </template>
          </vxe-column>
          <vxe-column field="IP" title="IP" width="100">
            <template v-slot="scope">
              <div>{{ scope.row.ip }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <div v-if="scope.row.channelStatus == 1" style="color: #5fbeaa"
                >启用</div
              >
              <div v-else-if="scope.row.channelStatus == 2" style="color: red"
                >停用</div
              >
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="200" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="color: #409eff; margin-left: 0px"
                @click="detailsRow(scope)"
                ><el-icon><EditPen /></el-icon>&nbsp;编辑</el-button
              >
              <el-button
                link
                style="color: #409eff; margin-left: 5px"
                @click="addSetting(scope)"
                ><el-icon><Setting /></el-icon>&nbsp;配置通道</el-button
              >
              <el-button
                link
                style="color: orange; margin-left: 5px"
                v-if="scope.row.channelStatus == 2"
                @click="delRow(scope)"
                ><el-icon><Delete /></el-icon>&nbsp;删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 标签修改 -->
      <el-dialog
        title="设置标签"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="setLabel.formData"
          :rules="setLabel.formRule"
          ref="setLabel"
          label-width="110px"
          style="padding: 0 28px"
        >
          <el-form-item label="类型" prop="style">
            <el-select
              class="input-w"
              v-model="setLabel.formData.style"
              @change="changeLocationValue"
              multiple
              filterable
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in labelpass1"
                :key="index"
                :label="item.labelContent"
                :value="item.labelId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="行业" prop="hangye">
            <el-select
              class="input-w"
              v-model="setLabel.formData.hangye"
              multiple
              filterable
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in labelpass2"
                :key="index"
                :label="item.labelContent"
                :value="item.labelId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="退订格式" prop="tuidinggeshi">
            <el-select
              class="input-w"
              v-model="setLabel.formData.tuidinggeshi"
              filterable
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option label="请选择" value></el-option>
              <el-option
                v-for="(item, index) in labelpass3"
                :key="index"
                :label="item.labelContent"
                :value="item.labelId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="签名报备情况" prop="signqingkuang">
            <el-select
              class="input-w"
              v-model="setLabel.formData.signqingkuang"
              filterable
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option label="请选择" value></el-option>
              <el-option
                v-for="(item, index) in labelpass4"
                :key="index"
                :label="item.labelContent"
                :value="item.labelId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="次数限制" prop="cishu">
            <el-select
              class="input-w"
              v-model="setLabel.formData.cishu"
              filterable
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option label="请选择" value></el-option>
              <el-option
                v-for="(item, index) in labelpass5"
                :key="index"
                :label="item.labelContent"
                :value="item.labelId"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('setLabel')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 标签修改结束 -->
      <!-- 新增通道配置 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="addUserDialog"
        :close-on-click-modal="false"
        width="880px"
        class="addUserDialog"
      >
        <el-form
          :model="addUserStep1Form.formData"
          :rules="addUserStep1Form.formRules"
          ref="addUserStep1Form"
          label-width="140px"
        >
          <div style="width: 50%; float: left">
            <el-form-item label="通道编号" prop="channelCode">
              <el-input
                class="input-w"
                :disabled="channelIsfalse"
                v-model="addUserStep1Form.formData.channelCode"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道名称" prop="channelName">
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.channelName"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道号码" prop="channelNumber">
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.channelNumber"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道区域" prop="channelFlag">
              <el-select
                v-model="addUserStep1Form.formData.channelFlag"
                class="input-w"
                placeholder="请选择"
              >
                <el-option label="全网" value="0"></el-option>
                <el-option label="省网" value="1"></el-option>
                <el-option label="全网+省网" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="接口方式" prop="interfaceMode">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.interfaceMode"
                placeholder="请选择"
              >
                <el-option label="http接口" value="1"></el-option>
                <el-option label="直连协议" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="运营商名称" prop="operatorId">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.operatorId"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in operatorNames"
                  :label="item.operatorName"
                  :value="item.operatorId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="是否需要提前报备" prop="madeReported">
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Form.formData.madeReported"
                    placeholder="请选择"
                  >
                    <el-option label="不报可发" value="1"></el-option>
                    <el-option label="签名报备可发" value="2"></el-option>
                    <el-option label="签名扩展报备可发" value="3"></el-option>
                  </el-select>
                </el-form-item> -->
            <!-- <el-form-item label="协议推送端口" prop="port">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Form.formData.port"
                  ></el-input>
                </el-form-item> -->
            <!-- <el-form-item label="报备通道：" prop="removeSign">
                  <el-select class="input-w" v-model="addUserStep1Form.formData.removeSign" placeholder="请选择">
                    <el-option label="是（抹除签名）" value="true"></el-option>
                    <el-option label="否（不抹除）" value="false"></el-option>
                  </el-select>
                </el-form-item> -->
            <!-- <el-form-item label="选择新旧网关" prop="pactType">
                  <el-radio-group v-model="addUserStep1Form.formData.pactType">
                    <el-radio label="1">旧网关</el-radio>
                    <el-radio label="2">新网关</el-radio>
                  </el-radio-group>
                </el-form-item> -->
            <el-form-item label="支持携号转网" prop="supportNumberPortability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.supportNumberPortability"
                placeholder="请选择"
              >
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item
                  label="验证码流速占比"
                  prop="velocityRateYzm"
                  v-if="addUserStep1Form.formData.productId == '1'"
                >
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Form.formData.velocityRateYzm"
                    placeholder="请选择"
                  >
                    <el-option label="0" value="0"></el-option>
                    <el-option label="0.1" value="0.1"></el-option>
                    <el-option label="0.2" value="0.2"></el-option>
                    <el-option label="0.3" value="0.3"></el-option>
                    <el-option label="0.4" value="0.4"></el-option>
                    <el-option label="0.5" value="0.5"></el-option>
                    <el-option label="0.6" value="0.6"></el-option>
                    <el-option label="0.7" value="0.7"></el-option>
                    <el-option label="0.8" value="0.8"></el-option>
                    <el-option label="0.9" value="0.9"></el-option>
                    <el-option label="1" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
            <!-- <el-form-item
                  label="营销流速占比"
                  prop="velocityRateYx"
                  v-if="addUserStep1Form.formData.productId == '1'"
                >
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Form.formData.velocityRateYx"
                    placeholder="请选择"
                  >
                    <el-option label="0" value="0"></el-option>
                    <el-option label="0.1" value="0.1"></el-option>
                    <el-option label="0.2" value="0.2"></el-option>
                    <el-option label="0.3" value="0.3"></el-option>
                    <el-option label="0.4" value="0.4"></el-option>
                    <el-option label="0.5" value="0.5"></el-option>
                    <el-option label="0.6" value="0.6"></el-option>
                    <el-option label="0.7" value="0.7"></el-option>
                    <el-option label="0.8" value="0.8"></el-option>
                    <el-option label="0.9" value="0.9"></el-option>
                    <el-option label="1" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
          </div>
          <div style="width: 50%; float: left">
            <el-form-item label="通道产品类型" prop="productId">
              <el-select
                v-model="addUserStep1Form.formData.productId"
                :disabled="channelIsfalse"
                placeholder="请选择"
                class="input-w"
              >
                <el-option
                  v-for="item in productList"
                  :key="item.productId"
                  :label="item.name"
                  :value="item.productId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="批次容量" prop="batch">
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.batch"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道属性" prop="channelNature">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.channelNature"
                placeholder="请选择通道属性"
              >
                <el-option label="直连" value="1"></el-option>
                <el-option label="第三方" value="2"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="下行流速" prop="descendingVelocity">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Form.formData.descendingVelocity"
                  ></el-input>
                </el-form-item> -->
            <el-form-item
              label="省份选择"
              prop="provincialId"
              v-if="
                addUserStep1Form.formData.channelFlag == '1' ||
                addUserStep1Form.formData.channelFlag == '2'
              "
            >
              <el-select
                v-model="addUserStep1Form.formData.provincialId"
                placeholder="请选择"
                class="input-w"
                @change="changeCity"
              >
                <el-option
                  v-for="(item, index) in ProvincesCities"
                  :key="index"
                  :label="item.provincial"
                  :value="item.provincialId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="城市"
              prop="cityId"
              v-if="
                addUserStep1Form.formData.channelFlag == '1' ||
                addUserStep1Form.formData.channelFlag == '2'
              "
            >
              <el-select
                v-model="addUserStep1Form.formData.cityId"
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="(item, index) in cityList"
                  :key="item.id"
                  :label="item.cityName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="是否专属" prop="isPrivate">
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Form.formData.isPrivate"
                    placeholder="请选择"
                  >
                    <el-option label="是" value="2"></el-option>
                    <el-option label="否" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
            <!-- <el-form-item
                  label="专属客户名称"
                  prop="privateName"
                  v-if="addUserStep1Form.formData.isPrivate == '2'"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Form.formData.privateName"
                  ></el-input>
                </el-form-item> -->
            <el-form-item label="运营商类型" prop="operatorsType">
              <el-select
                v-model="addUserStep1Form.formData.operatorsType"
                :disabled="channelIsfalse"
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="移动" value="1"></el-option>
                <el-option label="联通" value="2"></el-option>
                <el-option label="电信" value="3"></el-option>
                <el-option label="三网" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="通道状态" prop="channelStatus">
              <el-select
                class="input-w"
                :disabled="channelIsfalse"
                v-model="addUserStep1Form.formData.channelStatus"
                placeholder="请选择"
              >
                <el-option label="启用" value="1"></el-option>
                <el-option label="停用" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="协议推送ip" prop="ip">
              <el-input
                type="textarea"
                class="input-w"
                v-model="addUserStep1Form.formData.ip"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item
                  label="行业流速占比"
                  prop="velocityRateHy"
                  v-if="addUserStep1Form.formData.productId == '1'"
                >
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Form.formData.velocityRateHy"
                    placeholder="请选择"
                  >
                    <el-option label="0" value="0"></el-option>
                    <el-option label="0.1" value="0.1"></el-option>
                    <el-option label="0.2" value="0.2"></el-option>
                    <el-option label="0.3" value="0.3"></el-option>
                    <el-option label="0.4" value="0.4"></el-option>
                    <el-option label="0.5" value="0.5"></el-option>
                    <el-option label="0.6" value="0.6"></el-option>
                    <el-option label="0.7" value="0.7"></el-option>
                    <el-option label="0.8" value="0.8"></el-option>
                    <el-option label="0.9" value="0.9"></el-option>
                    <el-option label="1" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
            <el-form-item label="备注" prop="remark">
              <el-input
                :autosize="{ minRows: 4, maxRows: 5 }"
                class="input-w"
                type="textarea"
                v-model="addUserStep1Form.formData.remark"
              ></el-input>
            </el-form-item>
          </div>
          <div style="text-align: center; display: inline-block; width: 100%">
            <el-button
              @click="addUserDialog = false"
              style="width: 100px; padding: 9px 0"
              >取消</el-button
            >
            <el-button
              type="primary"
              :disabled="this.passDis"
              @click="submitForms('addUserStep1Form')"
              style="width: 100px; padding: 9px 0"
              >提交</el-button
            >
          </div>
        </el-form>
      </el-dialog>

      <!-- 新增通道配置 -->
      <el-dialog
        title="通道配置"
        v-model="addUserDialogs"
        :close-on-click-modal="false"
        width="1160px"
        class="addUserDialogs"
      >
        <div class="clearfix">
          <el-form
            :model="addUserStep1Forms.formData"
            :rules="addUserStep1Forms.formRules"
            ref="addUserStep1Forms"
            label-width="210px"
          >
            <div style="width: 100%; margin-bottom: 20px; padding-left: 90px">
              &nbsp;&nbsp;&nbsp;通道号：
              <span style="padding-left: 10px">{{ this.TDH }}</span>
              &nbsp;&nbsp;&nbsp;-&nbsp;&nbsp;&nbsp;通道状态：
              <span style="padding-left: 10px; color: red">{{
                this.settimes
              }}</span>
            </div>
            <div v-if="productIdShow != '9'">
              <div style="width: 50%; float: left">
                <el-form-item
                  label="配置通道运营商"
                  label-width="180px"
                  prop="gwType"
                >
                  <el-select
                    @change="
                      productIdShowChange(addUserStep1Forms.formData.gwType)
                    "
                    v-model="addUserStep1Forms.formData.gwType"
                    placeholder="请选择"
                    style="width: 100%"
                    class="input-w"
                  >
                    <el-option
                      v-if="productIdShow == '1'"
                      label="移动"
                      value="YD"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '1'"
                      label="移动5G"
                      value="YD_5G"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '1'"
                      label="联通"
                      value="LT"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '1'"
                      label="电信"
                      value="DX"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '2'"
                      label="HTTP"
                      value="MMS_HTTP"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '3'"
                      label="HTTP"
                      value="VIDEO_HTTP"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '4'"
                      label="HTTP"
                      value="IMS_HTTP"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '6'"
                      label="HTTP"
                      value="VOICE_YZM"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '7'"
                      label="HTTP"
                      value="VOICE_HY"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '9'"
                      label="5G"
                      value="MSG_5G"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '6' || productIdShow == '7'"
                      label="SIP"
                      value="VOICE_SIP"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <div v-if="productIdShow != '9'">
                  <el-form-item
                    v-if="
                      (productIdShow == '1' ||
                        productIdShow == '6' ||
                        productIdShow == '7') &&
                      addUserStep1Forms.formData.gwType != 'VOICE_YZM'
                    "
                    label="网关IP"
                    prop="bindIp"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.bindIp"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="
                      (productIdShow == '1' ||
                        productIdShow == '6' ||
                        productIdShow == '7') &&
                      addUserStep1Forms.formData.gwType != 'VOICE_YZM'
                    "
                    label="网关端口"
                    prop="bindPort"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.bindPort"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="productIdShow == '1'"
                    label="SP企业代码"
                    prop="corpId"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.corpId"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="productIdShow == '1'"
                    label="SP码号"
                    prop="spPort"
                    :rules="[
                      {
                        required: true,
                        message: 'sp号码不能为空',
                        trigger: 'change',
                      },
                    ]"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.spPort"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="
                      productIdShow != '1' &&
                      productIdShow != '6' &&
                      productIdShow != '7'
                    "
                    label="SP码号"
                    prop="spPort"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.spPort"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    v-if="productIdShow != '6' && productIdShow != '7'"
                    label="SP码号扩展"
                    prop="spPortExtend"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.spPortExtend"
                    ></el-input>
                  </el-form-item>
                </div>
                <el-form-item label="绑定IP1" prop="masterIp">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.masterIp"
                  ></el-input>
                </el-form-item>
                <el-form-item label="绑定IP2" prop="slaveIp">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.slaveIp"
                  ></el-input>
                </el-form-item>
                <el-form-item label="本地ip" prop="localIp">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.localIp"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="addUserStep1Forms.formData.gwType != 'VOICE_SIP'"
                  label="网关协议版本"
                  prop="version"
                >
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Forms.formData.version"
                    placeholder="请选择"
                  >
                    <el-option
                      v-if="
                        addUserStep1Forms.formData.gwType == 'YD' ||
                        addUserStep1Forms.formData.gwType == 'YD_5G'
                      "
                      label="CMPP20-CMPP 2.0 协议"
                      value="CMPP20"
                    ></el-option>
                    <el-option
                      v-if="
                        addUserStep1Forms.formData.gwType == 'YD' ||
                        addUserStep1Forms.formData.gwType == 'YD_5G'
                      "
                      label="CMPP30-CMPP 3.0 协议"
                      value="CMPP30"
                    ></el-option>
                    <el-option
                      v-else-if="addUserStep1Forms.formData.gwType == 'LT'"
                      label="SGIP12 协议"
                      value="SGIP12"
                    ></el-option>
                    <el-option
                      v-else-if="addUserStep1Forms.formData.gwType == 'DX'"
                      label="SMGP30 协议"
                      value="SMGP30"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'MMS_HTTP'" label="福建移动"
                          value="MMS_FJYD"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'MMS_HTTP'"
                      label="彩信转视频"
                      value="RMS_FORWARD"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'MMS_HTTP'" label="众览无限"
                          value="MMS_ZHONGLAN"></el-option>
                        <el-option v-if="addUserStep1Forms.formData.gwType == 'MMS_HTTP'" label="专信"
                          value="MMS_ZHUANXIN"></el-option>
                        <el-option v-if="addUserStep1Forms.formData.gwType == 'MMS_HTTP'" label="大汉三通"
                          value="MMS_DAHAN"></el-option>
                        <el-option v-if="addUserStep1Forms.formData.gwType == 'MMS_HTTP'" label="胜盛"
                          value="MMS_SHENGSHENG"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="视频转5G"
                      value="VIDEO_TO_5G"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="河滔视频短信"
                      value="VIDEO_HETAO"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="上海义晓"
                      value="VIDEO_YIXIAO"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="张家港咪咕"
                      value="VIDEO_ZJGYD"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'" label="福建移动"
                          value="VIDEO_FJYD"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="江苏移动"
                      value="VIDEO_JSYD"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'" label="山东济南咪咕"
                          value="VIDEO_SDYD"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="山东-联通（新）"
                      value="VIDEO_SDLT_NEW"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="山东联通"
                      value="VIDEO_SDLT"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="山东电信"
                      value="VIDEO_SDDX"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="千寻"
                      value="VIDEO_QIANXUN"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="网信"
                      value="VIDEO_WANGXIN"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="宽信"
                      value="VIDEO_KUANXIN"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'" label="浙江隐尚移动"
                          value="VIDEO_ZJYD"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="人民信产"
                      value="VIDEO_RMXC"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="新国都"
                      value="VIDEO_GUODU"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="领道易"
                      value="VIDEO_LINGDAOYI"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="上海田南"
                      value="VIDEO_TIANNAN"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="上海田南（新）"
                      value="VIDEO_TIANNAN_NEW"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="上海文嘻"
                      value="VIDEO_WENXI"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="赛邮"
                      value="VIDEO_SUBMAIL"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="北京空间畅享"
                      value="VIDEO_KONGJIAN"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="北京阳光普惠"
                      value="VIDEO_YANGGUANG"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="苏州胜盛"
                      value="VIDEO_SHENGSHENG"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="上海分治"
                      value="VIDEO_FENZHI"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="联麓-三方"
                      value="VIDEO_LIANLU"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VIDEO_HTTP'"
                      label="江苏-移动-卓望"
                      value="VIDEO_JSYD_NEW"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'VOICE_YZM'" label="移动时空语音验证码"
                          value="VOICE_YDSK"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_YZM'"
                      label="阿里云语音验证码"
                      value="VOICE_ALIYUN"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_YZM'"
                      label="赛邮语音验证码"
                      value="VOICE_SUBMAIL"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_YZM'"
                      label="飞鸽语音验证码"
                      value="VOICE_FEIGE"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_YZM'"
                      label="联麓语音验证码"
                      value="VOICE_LIANLU"
                    ></el-option>
                    <!-- <el-option v-if="addUserStep1Forms.formData.gwType == 'VOICE_HY'" label="泽讯语音通知"
                          value="VOICE_ZEXUN"></el-option> -->
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_HY'"
                      label="南京中太智和"
                      value="VOICE_ZHONG_TAI"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_HY'"
                      label="江苏天移"
                      value="VOICE_TIAN_YI"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_HY'"
                      label="赛邮语音通知"
                      value="VOICE_SUBMAIL"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_HY'"
                      label="飞鸽语音通知"
                      value="VOICE_FEIGE"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'VOICE_HY'"
                      label="联麓语音通知"
                      value="VOICE_LIANLU"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'MSG_5G'"
                      label="江苏移动5G"
                      value="MSG_5G_JSYD"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'MSG_5G'"
                      label="联通5G"
                      value="MSG_5G_LT"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'IMS_HTTP'"
                      label="阿尔发"
                      value="IMS_AF"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'IMS_HTTP'"
                      label="CM 国际"
                      value="IMS_CM"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'IMS_HTTP'"
                      label="君隆国际"
                      value="IMS_JL"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'IMS_HTTP'"
                      label="飞鸽传书"
                      value="IMS_FG"
                    ></el-option>
                    <el-option
                      v-if="
                        addUserStep1Forms.formData.gwType != 'YD' &&
                        addUserStep1Forms.formData.gwType != 'YD_5G' &&
                        addUserStep1Forms.formData.gwType != 'LT' &&
                        addUserStep1Forms.formData.gwType != 'DX'
                      "
                      label="模拟成功"
                      value="MOCK_SUCCESS"
                    ></el-option>
                    <el-option
                      v-if="
                        addUserStep1Forms.formData.gwType != 'YD' &&
                        addUserStep1Forms.formData.gwType != 'YD_5G' &&
                        addUserStep1Forms.formData.gwType != 'LT' &&
                        addUserStep1Forms.formData.gwType != 'DX'
                      "
                      label="模拟失败"
                      value="MOCK_FAIL"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <div v-if="productIdShow != '9'">
                  <!-- <el-form-item v-if="productIdShow != '6' && productIdShow != '7'" label="检测ip：" prop="checkIp">
                        <el-input class="input-w" v-model="addUserStep1Forms.formData.checkIp"></el-input>
                      </el-form-item> -->
                  <el-form-item
                    v-if="productIdShow != '6' && productIdShow != '7'"
                    label="信息格式"
                    prop="ucs"
                  >
                    <el-select
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ucs"
                      placeholder="请选择"
                    >
                      <el-option label="UCS2编码" value="8"></el-option>
                      <el-option label="含GB汉字" value="15"></el-option>
                      <el-option label="通道固定签名" value="25"></el-option>
                    </el-select>
                  </el-form-item>
                  <!-- <el-form-item v-if="productIdShow != '6' && productIdShow != '7'" label="计费用户类型字段：" prop="feeType">
                        <el-select class="input-w" v-model="addUserStep1Forms.formData.feeType" placeholder="请选择">
                          <el-option label="无" value="1"></el-option>
                          <el-option label="对目的终端MSISDN计费" value="DES_MSISDN"></el-option>
                          <el-option label="对源终端MSISDN计费" value="ORIGIN_MSISDN"></el-option>
                          <el-option label="对SP计费" value="SP"></el-option>
                        </el-select>
                      </el-form-item> -->
                  <el-form-item
                    v-if="productIdShow != '6' && productIdShow != '7'"
                    label="可信任通道"
                    prop="friendChannelId"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.friendChannelId"
                      @input="updateView($event)"
                    ></el-input>
                  </el-form-item>

                  <el-form-item
                    v-if="productIdShow != '6' && productIdShow != '7'"
                    label="噪音上行通道"
                    prop="moChannelId"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.moChannelId"
                      @input="updateView($event)"
                    ></el-input>
                  </el-form-item>
                  <el-form-item v-if="productIdShow == '1'" label="回落文本5G通道" prop="fallbackTextChannelId">
                    <el-input class="input-w" v-model="addUserStep1Forms.formData.fallbackTextChannelId"
                      @input="updateView($event)"></el-input>
                  </el-form-item>
                  <el-form-item
                    label="节点编码："
                    prop="nodeId"
                    v-if="addUserStep1Forms.formData.gwType == 'LT'"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.nodeId"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
              <div style="width: 50%; float: left">
                <!-- <el-form-item label="通道版本" prop="channelVersion">
                      <el-select class="input-w" v-model="addUserStep1Forms.formData.channelVersion" placeholder="请选择">
                        <el-option label="v1" value="v1"></el-option>
                        <el-option label="v2" value="v2"></el-option>
                      </el-select>
                    </el-form-item> -->

                <!-- <el-form-item label="返回状态确认报告：" prop="deliverType">
                      <el-radio-group
                        v-model="addUserStep1Forms.formData.deliverType"
                      >
                        <el-radio label="NONE">不需要</el-radio>
                        <el-radio label="REPORT">需要</el-radio>
                      </el-radio-group>
                    </el-form-item> -->
                <el-form-item
                  v-if="productIdShow != '6' && productIdShow != '7'"
                  label="上行匹配规则"
                  prop="moType"
                >
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Forms.formData.moType"
                    placeholder="请选择"
                  >
                    <el-option label="全匹配" value="BOTH"></el-option>
                    <el-option label="扩展+号码匹配" value="EXT"></el-option>
                    <el-option label="扩展匹配" value="ONLY_EXT"></el-option>
                    <el-option label="号码匹配" value="MOBILE"></el-option>
                    <el-option label="无" value="NO"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow != '6' && productIdShow != '7'"
                  label="抹除客户扩展"
                  prop="removeUserExt"
                >
                  <el-radio-group
                    v-model="addUserStep1Forms.formData.removeUserExt"
                  >
                    <el-radio value="true">是</el-radio>
                    <el-radio value="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '1'"
                  label="抹签名"
                  prop="removeSign"
                >
                  <el-radio-group
                    v-model="addUserStep1Forms.formData.removeSign"
                  >
                    <el-radio value="true">是</el-radio>
                    <el-radio value="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '1'"
                  label="用户名"
                  prop="clientId"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.clientId"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item v-else label="用户名" prop="clientId1">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.clientId"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '1'"
                  label="密码"
                  prop="clientPwd"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.clientPwd"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item v-else label="密码" prop="clientPwd1">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.clientPwd"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="token" prop="token">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.token"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '4'"
                  label="发送URL"
                  prop="sendUrl"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.sendUrl"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="通道部署状态" prop="deployStatus">
                  <el-radio-group
                    v-model="addUserStep1Forms.formData.deployStatus"
                  >
                    <el-radio value="START">启用</el-radio>
                    <el-radio value="STOP">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="链接数" prop="connectNum">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.connectNum"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="最大流速" prop="maxNum">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.maxNum"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow != '6' && productIdShow != '7'"
                  label="最大滑窗值"
                  prop="flow"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.flow"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="
                    productIdShow != '3' &&
                    productIdShow != '6' &&
                    productIdShow != '7'
                  "
                  label="业务代码"
                  prop="serviceId"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.serviceId"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '1'"
                  label="回执处理方式"
                  prop="toRabbitMq"
                >
                  <el-radio-group
                    v-model="addUserStep1Forms.formData.toRabbitMq"
                  >
                    <el-radio value="false">redis</el-radio>
                    <el-radio value="true">rabbitMQ</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '1'"
                  label="营销日限"
                  prop="dayLimit"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.dayLimit"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '1'"
                  label="营销周限"
                  prop="weekLimit"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.weekLimit"
                  ></el-input>
                </el-form-item>
                <!-- <el-form-item
                      v-if="
                        productIdShow != '3' &&
                        productIdShow != '6' &&
                        productIdShow != '7'
                      "
                      label="计费手机号："
                      prop="feeMobile"
                    >
                      <el-input
                        class="input-w"
                        v-model="addUserStep1Forms.formData.feeMobile"
                      ></el-input>
                    </el-form-item> -->
                <el-form-item
                  label="本地监听端口"
                  prop="localBindPort"
                  v-if="addUserStep1Forms.formData.gwType == 'LT'"
                >
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.localBindPort"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="productIdShow == '3'"
                  label="内容后缀"
                  prop="contentSuffix"
                >
                  <el-input
                    class="input-w"
                    placeholder="此条信息免流 退订回T"
                    v-model="addUserStep1Forms.formData.contentSuffix"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
            <div v-if="productIdShow == '9'">
              <div style="width: 50%; float: left">
                <el-form-item
                  label="配置通道运营商"
                  label-width="180px"
                  prop="gwType"
                >
                  <el-select
                    @change="
                      productIdShowChange(addUserStep1Forms.formData.gwType)
                    "
                    v-model="addUserStep1Forms.formData.gwType"
                    placeholder="请选择"
                    style="width: 100%"
                    class="input-w"
                  >
                    <el-option
                      v-if="productIdShow == '1'"
                      label="移动"
                      value="YD"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '1'"
                      label="联通"
                      value="LT"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '1'"
                      label="电信"
                      value="DX"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '2'"
                      label="HTTP"
                      value="MMS_HTTP"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '3'"
                      label="HTTP"
                      value="VIDEO_HTTP"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '4'"
                      label="HTTP"
                      value="IMS_HTTP"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '6'"
                      label="HTTP"
                      value="VOICE_YZM"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '7'"
                      label="HTTP"
                      value="VOICE_HY"
                    ></el-option>
                    <el-option
                      v-if="productIdShow == '9'"
                      label="5G"
                      value="MSG_5G"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="绑定IP1" prop="masterIp" key="masterIp">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.masterIp"
                  ></el-input>
                </el-form-item>
                <el-form-item label="绑定IP2" prop="slaveIp" key="slaveIp">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.slaveIp"
                  ></el-input>
                </el-form-item>
                <el-form-item label="本地ip" prop="localIp" key="localIp">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.localIp"
                  ></el-input>
                </el-form-item>
                <el-form-item label="SP码号" prop="spPort" key="spPort">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.spPort"
                  ></el-input>
                </el-form-item>
                <el-form-item label="SP码号扩展" prop="spPortExtend">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.spPortExtend"
                  ></el-input>
                </el-form-item>
                <el-form-item label="默认签名" prop="defaultSignature">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.defaultSignature"
                  ></el-input>
                </el-form-item>
                <el-form-item label="网关协议版本" prop="version">
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Forms.formData.version"
                    placeholder="请选择"
                  >
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'MSG_5G'"
                      label="江苏移动5G"
                      value="MSG_5G_JSYD"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'MSG_5G'"
                      label="联通5G"
                      value="MSG_5G_LT"
                    ></el-option>
                    <el-option
                      v-if="addUserStep1Forms.formData.gwType == 'MSG_5G'"
                      label="电信5G"
                      value="MSG_5G_DX"
                    ></el-option>
                    <el-option
                      v-if="
                        addUserStep1Forms.formData.gwType != 'YD' &&
                        addUserStep1Forms.formData.gwType != 'LT' &&
                        addUserStep1Forms.formData.gwType != 'DX'
                      "
                      label="模拟成功"
                      value="MOCK_SUCCESS"
                    ></el-option>
                    <el-option
                      v-if="
                        addUserStep1Forms.formData.gwType != 'YD' &&
                        addUserStep1Forms.formData.gwType != 'LT' &&
                        addUserStep1Forms.formData.gwType != 'DX'
                      "
                      label="模拟失败"
                      value="MOCK_FAIL"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="信息格式" prop="ucs">
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Forms.formData.ucs"
                    placeholder="请选择"
                  >
                    <el-option label="UCS2编码" value="8"></el-option>
                    <el-option label="含GB汉字" value="15"></el-option>
                    <el-option label="通道固定签名" value="25"></el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item label="可信任通道：" prop="friendChannelId">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.friendChannelId"
                      @input="updateView($event)"
                    ></el-input>
                  </el-form-item> -->
                <el-form-item label="通道部署状态" prop="deployStatus">
                  <el-radio-group
                    v-model="addUserStep1Forms.formData.deployStatus"
                  >
                    <el-radio value="START">启用</el-radio>
                    <el-radio value="STOP">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="链接数" prop="connectNum">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.connectNum"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>
                <el-form-item label="最大流速" prop="maxNum">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.maxNum"
                    @input="updateView($event)"
                  ></el-input>
                </el-form-item>

                <el-form-item label="营销日限" prop="dayLimit">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.dayLimit"
                  ></el-input>
                </el-form-item>
                <el-form-item label="营销周限" prop="weekLimit">
                  <el-input
                    class="input-w"
                    v-model="addUserStep1Forms.formData.weekLimit"
                  ></el-input>
                </el-form-item>
                <el-form-item label="是否长短信转视信" prop="longSms2Fallback">
                  <el-radio-group
                    v-model="addUserStep1Forms.formData.longSms2Fallback"
                  >
                    <el-radio value="true">是</el-radio>
                    <el-radio value="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div>
                  <!-- <el-form-item label="开启阅信" prop="xsmsEnable">
                        <el-radio-group v-model="addUserStep1Forms.formData.xsmsEnable">
                          <el-radio label="true">开启</el-radio>
                          <el-radio label="false">关闭</el-radio>
                        </el-radio-group>
                      </el-form-item> -->
                  <!-- <el-form-item label="阅信请求地址：" prop="xsmsRequestUrl">
                        <el-input class="input-w" v-model="addUserStep1Forms.formData.xsmsRequestUrl"></el-input>
                      </el-form-item>
                      <el-form-item label="阅信私钥：" prop="xsmsRequestUrl">
                        <el-input class="input-w" v-model="addUserStep1Forms.formData.xsmsPrivateKey"></el-input>
                      </el-form-item> -->
                </div>
              </div>

              <div style="width: 50%; float: left">
                <el-form-item label="上行匹配规则" prop="moType">
                  <el-select
                    class="input-w"
                    v-model="addUserStep1Forms.formData.moType"
                    placeholder="请选择"
                  >
                    <el-option label="全匹配" value="BOTH"></el-option>
                    <el-option label="扩展+号码匹配" value="EXT"></el-option>
                    <el-option label="扩展匹配" value="ONLY_EXT"></el-option>
                    <el-option label="号码匹配" value="MOBILE"></el-option>
                    <el-option label="无" value="NO"></el-option>
                  </el-select>
                </el-form-item>
                <div v-if="operatorsType5g == '1'">
                  <el-form-item label="移动代理商名称" prop="ydBelongAgentName">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydBelongAgentName"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动代理商编码" prop="ydBelongAgentCode">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydBelongAgentCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动服务代码" prop="ydServiceCode">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydServiceCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="代理商归属地编码"
                    prop="belongRegionCode"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.belongRegionCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动EC集团客户编码" prop="ydCustomerNum">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydCustomerNum"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动产品订单号" prop="ydProdordSkuNum">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydProdordSkuNum"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动计费编码" prop="ydProdistSkuNum">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydProdistSkuNum"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动CSP平台编码" prop="ydCspId">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydCspId"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动账号" prop="ydAppId">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydAppId"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动密码" prop="ydPassword">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydPassword"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动私钥" prop="ydPrivateKey">
                    <el-input
                      class="input-w"
                      type="textarea"
                      v-model="addUserStep1Forms.formData.ydPrivateKey"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="移动请求IP" prop="ydRequestUrl">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydRequestUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="移动运营商接入层地址"
                    prop="ydAccessLayerUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydAccessLayerUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="移动运营商接入层回调地址"
                    prop="ydNotifyUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ydNotifyUrl"
                    ></el-input>
                  </el-form-item>
                </div>
                <div v-if="operatorsType5g == '2'">
                  <el-form-item label="联通accessKey" prop="ltAccessKey">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltAccessKey"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="联通csp接入地址" prop="ltRequestUrl">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltRequestUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="联通cspCode" prop="ltCspId">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltCspId"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="联通接入号" prop="ltServiceCode">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltServiceCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="联通chatbot访问IP白名单"
                    prop="ltIpWhiteList"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltIpWhiteList"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="chatbot接入层回调基础地址"
                    prop="ltNotifyBaseUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltNotifyBaseUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="多媒体文件上传地址"
                    prop="ltMediaUploadUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltMediaUploadUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="chatbot接口接入地址"
                    prop="ltChatbotRequestUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.ltChatbotRequestUrl"
                    ></el-input>
                  </el-form-item>
                </div>
                <div v-if="operatorsType5g == '3'">
                  <el-form-item label="电信accessKey" prop="dxAccessKey">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxAccessKey"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="电信csp接入地址" prop="dxRequestUrl">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxRequestUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="电信cspCode" prop="dxCspId">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxCspId"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="电信接入号" prop="dxServiceCode">
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxServiceCode"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="电信chatbot访问IP白名单"
                    prop="dxIpWhiteList"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxIpWhiteList"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="chatbot接入层回调基础地址"
                    prop="dxNotifyBaseUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxNotifyBaseUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="多媒体文件上传地址"
                    prop="dxMediaUploadUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxMediaUploadUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="chatbot接口接入地址"
                    prop="dxChatbotRequestUrl"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Forms.formData.dxChatbotRequestUrl"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="host" prop="dxHost">
                    <el-input
                      class="input-w"
                      type="textarea"
                      v-model="addUserStep1Forms.formData.dxHost"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
            </div>
          </el-form>
        </div>
        <div style="text-align: center">
          <el-button
            @click="addUserDialogs = false"
            style="width: 100px; padding: 9px 0"
            >取消</el-button
          >
          <el-button
            type="primary"
            :disabled="this.passDis"
            @click="submitFormss('addUserStep1Forms')"
            style="width: 100px; padding: 9px 0"
            >提交</el-button
          >
        </div>
      </el-dialog>
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from "@/components/publicComponents/ChannelView.vue";
import TableTem from "@/components/publicComponents/TableTem.vue";
import * as Vue from "vue";
import productData from "@/utils/industrysCode";
export default {
  components: {
    ChannelView,
    TableTem,
  },
  name: "PassagewayMag",
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      TDH: "", //通道配置通道号
      ChannelData: "", //传递通道值
      titleMap: {
        add: "新增通道",
        edit: "编辑通道",
      },
      productIdShow: "",
      productList: [],
      dialogStatus: "", //新增 编辑 标题
      passId: "", //通道id存
      labelIds: "", //标签id
      show: true,
      passreset: false, //通道重置按钮显示
      settimes: "未连接", //状态配置
      passresetId: "", //通道切换编号
      passDis: false,
      channelStatus: [], //批量停启用状态
      labelName: [], //标签存储
      operatorNames: [], //运营商储存
      labelpass1: [], //类型
      labelpass1Name: [], //类型名字
      labelpass2: [], //行业
      labelpass3: [], //退订格式
      labelpass4: [], //签名报备情况
      labelpass5: [], //次数限制
      channelIsfalse: false, //编辑添加通道编号状态
      ProvincesCities: [], //省市
      //查询表单数据
      formInline: {
        channelCode: "",
        channelName: "",
        operatorsType: "",
        pactType: "",
        productId: "",
        channelStatus: "",
        // labelIdList: [],
        isPrivate: "",
        channelFlag: "",
        ip: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        channelCode: "",
        channelName: "",
        operatorsType: "",
        pactType: "",
        productId: "",
        channelStatus: "",
        // labelIdList: [],
        isPrivate: "",
        ip: "",
        channelFlag: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableRow: "", //当前行列表数据
      selectId: "", //选中列表id
      dialogVisible: false,
      operatorsType5g: "",
      //新增配置通道
      addUserStep1Forms: {
        //弹窗的数据和验证
        formData: {
          //通道配置数据
          bindIp: "", //网关ip
          moChannelId: "", //上行通道
          fallbackTextChannelId: "", //回落文本5G通道
          bindPort: "", //网关端口：
          gwType: "", //运营商类型
          spPort: "", //sp号码
          spPortExtend: "", //sp码号扩展
          corpId: "", //sp企业代码
          // feeType: '',//计费用户类型字段
          deliverType: "", //返回状态确认报告：
          removeUserExt: "", //抹除扩展
          removeSign: "false", //抹除签名
          version: "", //网关协议版本：
          ucs: "", //信息格式：
          localIp: "", //本地ip：
          slaveIp: "", //主ip2：
          masterIp: "", //主ip1：
          nodeId: "", //节点编号
          smsNum: "", //发送量高于
          // checkIp: '',//检测ip
          token: "", //token
          channelVersion: "v1", //通道版本
          //------------------------5G-移动
          ydBelongAgentName: "", //代理商名称
          ydBelongAgentCode: "", //代理商编码
          ydServiceCode: "", //服务代码
          ydCustomerNum: "", //EC集团客户编码
          ydProdordSkuNum: "", //产品订单号
          ydProdistSkuNum: "", //产品订购关系ID，计费编码
          ydCspId: "", //CSP平台编码
          ydAppId: "", //账号
          ydPassword: "", //密码
          ydPrivateKey: "", //私钥
          ydRequestUrl: "", //请求IP
          ydAccessLayerUrl: "", //移动运营商接入层地址
          ydNotifyUrl: "", //移动运营商接入层回调地址
          belongRegionCode: "", //代理商归属地编码
          //------------------------5G-联通
          ltAccessKey: "", //联通accessKey
          ltRequestUrl: "", //联通csp接入地址
          ltCspId: "", //联通cspCode
          ltServiceCode: "", //联通接入号
          ltIpWhiteList: "", //联通chatbot接入层接口访问IP白名单
          ltNotifyBaseUrl: "", //chatbot接入层回调基础地址
          ltMediaUploadUrl: "", //多媒体文件上传地址
          ltChatbotRequestUrl: "", //chatbot接口接入地址
          //------------------------5G-电信
          dxAccessKey: "", //电信accessKey
          dxRequestUrl: "", //电信csp接入地址
          dxCspId: "", //电信cspCode
          dxServiceCode: "", //联通接入号
          dxIpWhiteList: "", //电信chatbot接入层接口访问IP白名单
          dxNotifyBaseUrl: "", //chatbot接入层回调基础地址
          dxMediaUploadUrl: "", //多媒体文件上传地址
          dxChatbotRequestUrl: "", //chatbot接口接入地址
          dxHost: "",
          //-------------------------
          // xsmsEnable: "false",//阅信开启
          // xsmsRequestUrl: "",//阅信请求地址
          // xsmsPrivateKey: "",//阅信私钥
          //------------------------右
          feeMobile: "", //计费手机号
          // signHandleType: "", //内容签名处理
          serviceId: "", //业务代码
          flow: "", //最大滑窗值
          contentSuffix: "", //内容后缀
          maxNum: "", //最大流速
          toRabbitMq: "false", //回执队列
          connectNum: "", //链接数
          clientPwd: "", //端口密码
          clientId: "", //用户名
          deployStatus: "", //通道部署状态
          switchRate: "", //备份通道切换成功率
          backup: "", //备份通道
          friendChannelId: "", //可信任通道
          heartInternal: "", //通道检查心跳时间
          reportRate: "", //回执低于率
          localBindPort: "", //本地监听端口
          productId: "", //产品ID
          sendUrl: "", //发送URL
          dayLimit: "", //营销日限
          weekLimit: "", //营销周限
          defaultSignature: "",
          moType: "BOTH", //上行匹配规则
          longSms2Fallback: "false", //长短信回退
        },
        formRules: {
          nodeId: [
            { required: true, message: "节点编号不能为空", trigger: "change" },
          ],
          longSms2Fallback: [
            {
              required: true,
              message: "请选择是否开启长短信回退",
              trigger: "change",
            },
          ],
          localBindPort: [
            {
              required: true,
              message: "本地监听端口不能为空",
              trigger: "change",
            },
          ],
          ucs: [
            { required: true, message: "请选择信息格式", trigger: "change" },
          ],
          removeSign: [
            {
              required: true,
              message: "请选择是否抹除签名",
              trigger: "change",
            },
          ],
          bindPort: [
            { required: true, message: "端口不能为空", trigger: "change" },
          ],
          gwType: [
            { required: true, message: "请选择运营商", trigger: "change" },
          ],
          defaultSignature: [
            {
              required: true,
              message: "签名不能为空",
              trigger: "change",
            },
          ],
          version: [
            {
              required: true,
              message: "通道版本号不能为空",
              trigger: "change",
            },
          ],
          bindIp: [
            { required: true, message: "网关ip不能为空", trigger: "change" },
            {
              pattern:
                /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/,
              message: "请输入正确的ip",
            },
          ],
          clientId: [
            {
              required: true,
              message: "网关用户名不能为空",
              trigger: "change",
            },
          ],
          clientPwd: [
            { required: true, message: "网关密码不能为空", trigger: "change" },
          ],
          clientId1: [
            {
              required: false,
              message: "网关用户名不能为空",
              trigger: "change",
            },
          ],
          clientPwd1: [
            { required: false, message: "网关密码不能为空", trigger: "change" },
          ],
          corpId: [
            { required: true, message: "企业代码不能为空", trigger: "change" },
          ],
          // serviceId: [
          //   { required: true, message: "业务代码不能为空", trigger: "change" }
          // ],
          connectNum: [
            { required: true, message: "链接数不能为空", trigger: "change" },
          ],
          maxNum: [
            { required: true, message: "最大流速不能为空", trigger: "change" },
          ],
          moType: [
            {
              required: true,
              message: "请选择上行匹配规则",
              trigger: "change",
            },
          ],
          masterIp: [
            { required: true, message: "绑定IP1不能为空", trigger: "change" },
          ],
          slaveIp: [
            { required: true, message: "绑定IP2不能为空", trigger: "change" },
          ],
          localIp: [
            { required: true, message: "本地IP不能为空", trigger: "change" },
          ],
          spPort: [
            { required: true, message: "SP码号不能为空", trigger: "change" },
          ],
          ydBelongAgentName: [
            {
              required: true,
              message: "移动代理商名称不能为空",
              trigger: "change",
            },
          ],
          ydBelongAgentCode: [
            {
              required: true,
              message: "移动代理商编码不能为空",
              trigger: "change",
            },
          ],
          ydServiceCode: [
            {
              required: true,
              message: "移动服务代码不能为空",
              trigger: "change",
            },
          ],
          belongRegionCode: [
            {
              required: true,
              message: "代理商归属地编码不能为空",
              trigger: "change",
            },
          ],
          ydCustomerNum: [
            {
              required: true,
              message: "移动EC集团客户编码不能为空",
              trigger: "change",
            },
          ],
          ydProdordSkuNum: [
            {
              required: true,
              message: "移动产品订单号不能为空",
              trigger: "change",
            },
          ],
          ydProdistSkuNum: [
            {
              required: true,
              message: "移动计费编码不能为空",
              trigger: "change",
            },
          ],
          ydCspId: [
            {
              required: true,
              message: "移动CSP平台编码不能为空",
              trigger: "change",
            },
          ],
          ydAppId: [
            {
              required: true,
              message: "移动账号不能为空",
              trigger: "change",
            },
          ],
          ydPassword: [
            {
              required: true,
              message: "移动密码不能为空",
              trigger: "change",
            },
          ],
          ydPrivateKey: [
            {
              required: true,
              message: "移动私钥不能为空",
              trigger: "change",
            },
          ],
          ydRequestUrl: [
            {
              required: true,
              message: "移动请求IP不能为空",
              trigger: "change",
            },
          ],
          ydAccessLayerUrl: [
            {
              required: true,
              message: "移动运营商接入层地址不能为空",
              trigger: "change",
            },
          ],
          ydNotifyUrl: [
            {
              required: true,
              message: "移动运营商接入层回调地址不能为空",
              trigger: "change",
            },
          ],
          // remark: [
          //   { required: false, message: "请输入备注", trigger: "blur" },
          //   {
          //     min: 1,
          //     max: 100,
          //     message: "长度在 100个字符以内",
          //     trigger: ["blur", "change"]
          //   }
          // ]
        },
      },
      //新增通道编辑通道
      addUserStep1Form: {
        //弹窗的数据和验证
        formData: {
          //通道数据
          channelCode: "", //编号

          channelName: "", //名称
          channelNumber: "", //号码
          // channelUnitPrice: "", //单价
          channelFlag: "1", //区域
          interfaceMode: "4", //接口方式
          operatorId: "", //运营商id
          madeReported: "1", //是否提前报备
          port: "", //协议推送
          pactType: "2", //新旧网关
          ip: "", //协议推送ip
          productId: "", //通道产品类型
          batch: "", //批次容量
          channelNature: "", //通道属性
          descendingVelocity: "", //下行速度
          provincialId: "", //省份id
          cityId: "", //城市id
          isPrivate: "1", //是否专属
          privateName: "", //客户专属名称
          operatorsType: "", //运营商类型
          channelStatus: "", //通道状态
          velocityRateYzm: "", //验证码流速
          velocityRateHy: "", //行业流速占比
          velocityRateYx: "", //营销流速占比
          removeSign: false, //抹除签名
          remark: "", //备注
          supportNumberPortability: "true", //支持 携号转网
        },
        formRules: {
          //验证码流速验证
          velocityRateYzm: [
            { required: true, message: "选择验证码流速", trigger: "change" },
          ],
          // cityId: [
          //   { required: true, message: "请选择城市", trigger: "change" },
          // ],
          supportNumberPortability: [
            {
              required: true,
              message: "请选择是否支持携号转网",
              trigger: "change",
            },
          ],
          //行业
          velocityRateHy: [
            { required: true, message: "选择行业流速占比", trigger: "change" },
          ],
          //营销
          velocityRateYx: [
            { required: true, message: "选择营销流速占比", trigger: "change" },
          ],
          //省份
          provincialId: [
            { required: true, message: "请选择省份", trigger: "change" },
          ],
          channelCode: [
            { required: true, message: "请输入编号", trigger: "blur" },
            {
              min: 1,
              max: 8,
              message: "长度在 1 到 8 个字符",
              trigger: ["blur", "change"],
            },
            { pattern: /^[0-9]+$/, message: "含有非法字符（只能输入数字！）" },
          ],

          channelName: [
            { required: true, message: "请输入通道名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "长度在 1 到 20 个字符",
              trigger: ["blur", "change"],
            },
          ],
          channelNumber: [
            { required: false, message: "请输入通道号码", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "长度在 1 到 20 个字符",
              trigger: ["blur", "change"],
            },
            { pattern: /^[0-9]+$/, message: "含有非法字符（只能输入数字！）" },
          ],
          // channelUnitPrice: [
          //   { required: true, message: "请输入通道单价", trigger: "blur" },
          //   {
          //     min: "1",
          //     max: "8",
          //     message: "长度在 1 到 8 个字符",
          //     trigger: ["blur", "change"]
          //   },
          //   // {pattern:/^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,5})))$/,message:'用户单价最多两位小数'}
          // ],
          channelFlag: [
            { required: true, message: "请选择区域", trigger: "change" },
          ],
          interfaceMode: [
            { required: true, message: "请选择接口方式", trigger: "change" },
          ],
          operatorId: [
            { required: true, message: "请选择运营商名称", trigger: "change" },
          ],
          madeReported: [
            {
              required: true,
              message: "请选择是否提前报备",
              trigger: "change",
            },
          ],
          port: [
            { required: false, message: "请输入协议推送端口", trigger: "blur" },
            {
              min: 1,
              max: 8,
              message: "长度在 1到8 个字符",
              trigger: ["blur", "change"],
            },
            { pattern: /^[0-9]+$/, message: "端口号只包含数字" },
          ],
          removeSign: [
            {
              required: true,
              message: "请选择内容签名处理",
              trigger: "change",
            },
          ],
          ip: [
            { required: false, message: "请输入协议推送ip", trigger: "blur" },
            {
              // type: "email",
              message: "请输入正确的协议推送ip",
              trigger: ["blur", "change"],
            },
            {
              pattern:
                /^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/,
              message: "请输入正确的ip",
            },
          ],
          productId: [
            {
              required: true,
              message: "请选择通道产品类型",
              trigger: "change",
            },
          ],
          batch: [
            { required: true, message: "请输入批次容量", trigger: "blur" },
            {
              min: 1,
              max: 8,
              message: "长度在1 到 8 个字符",
              trigger: ["blur", "change"],
            },
            { pattern: /^[0-9]+$/, message: "含有非法字符（只能输入数字！）" },
          ],
          channelNature: [
            { required: true, message: "请选择通道属性", trigger: "change" },
          ],
          descendingVelocity: [
            { required: true, message: "请输入下行流速", trigger: "blur" },
            {
              min: 1,
              max: 6,
              message: "长度在1 到 6 个字符",
              trigger: ["blur", "change"],
            },
            // { pattern: /^([1-9]|[1-9][0-9]{1,2}|1[0-9]{3}|2000)$/, message: "8字符以内，含数字、小数点" }
            {
              pattern: /^(?!0)[0-9]+$/,
              message: "6字符以内的整数，不能以0开头",
            },
          ],
          isPrivate: [
            { required: true, message: "请选择是否专属", trigger: "change" },
          ],
          privateName: [
            { required: true, message: "请输入专属客户名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "长度在 20 个字符",
              trigger: ["blur", "change"],
            },
          ],
          operatorsType: [
            { required: true, message: "请选择运营商类型", trigger: "change" },
          ],

          channelStatus: [
            { required: true, message: "请选择通道状态", trigger: "change" },
          ],
          remark: [
            { required: false, message: "请输入备注", trigger: "blur" },
            {
              min: 1,
              max: 100,
              message: "长度在 100个字符以内",
              trigger: ["blur", "change"],
            },
          ],
        },
      },
      setLabel: {
        //设置标签弹框数据
        formData: {
          style: [],
          hangye: [],
          tuidinggeshi: "",
          signqingkuang: "",
          cishu: "",
        },
        formRule: {
          //验证规则
          style: [{ required: false, message: "请选择标签", trigger: "blur" }],
          hangye: [{ required: false, message: "请选择标签", trigger: "blur" }],
          tuidinggeshi: [
            { required: false, message: "请选择标签", trigger: "blur" },
          ],
          tuidinggeshis: [
            { required: true, message: "请选择标签", trigger: "blur" },
          ],
          signqingkuang: [
            { required: false, message: "请选择标签", trigger: "blur" },
          ],
          cishu: [{ required: false, message: "请选择标签", trigger: "blur" }],
        },
      },

      addUserDialog: false, //弹窗显示
      addUserDialogs: false, //配置弹窗显示
      dataCreat: "",
      dialogFormVisible: false, //新增弹出框显示隐藏,
      rules: {
        // updatezhizhao: [
        //   { required: true, message: "请上传营业执照", trigger: "blur" }
        // ]
      },

      tableDataObj: {
        //列表数据
        total: 0,
        loading2: false,
        tableData: [],
        //折叠数据
        // tableLabelExpand: [
        //   {
        //     prop: "channelUnitPrice",
        //     showName: "通道单价:"
        //   },
        //   {
        //     prop: "descendingVelocity",
        //     showName: "下行流速:"
        //   },
        //   {
        //     prop: "operatorsType",
        //     showName: "运营商类型:",
        //     formatData: function(val) {
        //       if (val == 1) {
        //         return (val = "移动");
        //       }
        //       if (val == 2) {
        //         return (val = "联通");
        //       }
        //       if (val == 3) {
        //         return (val = "电信");
        //       }
        //       if (val == 4) {
        //         return (val = "三网");
        //       }
        //     }
        //   },
        //   {
        //     prop: "operatorName",
        //     showName: "运营商名称:"
        //   },
        //   {
        //     prop: "channelNumber",
        //     showName: "通道号码:"
        //   },
        //   {
        //     prop: "interfaceMode",
        //     showName: "接口方式:",
        //     formatData: function(val) {
        //       if (val == 1) {
        //         return (val = "http接口");
        //       }
        //       // if (val == 2) {
        //       //   return (val = "Webservice接口");
        //       // }
        //       // if (val == 3) {
        //       //   return (val = "socket接口");
        //       // }
        //       if (val == 4) {
        //         return (val = "直连协议");
        //       }
        //     }
        //   },
        //   {
        //     prop: "madeReported",
        //     showName: "是否需要提前报备:",
        //     formatData: function(val) {
        //       if (val == 1) {
        //         return (val = "不报可发");
        //       }
        //       if (val == 2) {
        //         return (val = "签名报备可发");
        //       }
        //       if (val == 3) {
        //         return (val = "签名扩展报备可发");
        //       }
        //     }
        //   },
        //   // {
        //   //   prop: "channelFlag",
        //   //   showName: "区域:",
        //   //   formatData(val,index) {
        //   //     if (val == 0) {
        //   //       return (val = "全网");
        //   //     }else{
        //   //       return (val = "省网");
        //   //     }

        //   //   }

        //   // },
        //   {
        //     prop: "isProvincialStr",
        //     showName: "区域:"
        //   },

        //   {
        //     prop: "batch",
        //     showName: "批次容量:"
        //   }

        // ],
        // tableLabel: [
        //   {
        //     prop: "channelCode",
        //     showName: "通道编号",
        //     Channel:true,
        //     width:"100px",
        //     fixed: false
        //   },
        //   {
        //     prop: "channelName",
        //     showName: "通道名称",
        //     fixed: false
        //   },
        //   {
        //     prop: "yzmNum",
        //     showName: "验证码数据量",
        //      fixed: false
        //   },
        //   {
        //     prop: "hyNum",
        //     showName: "行业数据量",
        //      fixed: false
        //   },
        //   {
        //     prop: "yxNum",
        //     showName: "营销数据量",
        //      fixed: false
        //   },
        //   {
        //     prop: "currentSmsNum",
        //     showName: "当前发送条数",
        //      fixed: false
        //   },
        //   {
        //     prop: "currentReportSuccessRate",
        //     showName: "当前回执成功率",
        //      fixed: false
        //   },
        //   {
        //     prop: "currentReportRate",
        //     showName: "当前回执率",
        //      fixed: false
        //   },
        //   {
        //     prop: "remark",
        //     showName: "备注",
        //      fixed: false
        //   },
        //   // {
        //   //   prop: "labelNames",
        //   //   showName: "标签",
        //   //   fixed: false,
        //   //   color: "#16a085",
        //   //   showTags: {
        //   //     bgColor: [
        //   //       "#16a085",
        //   //       "#ff9900",
        //   //       "#e95d69",
        //   //       "#16a085",
        //   //       "#ff9900",
        //   //       "#e95d69",
        //   //       "#16a085",
        //   //       "#ff9900",
        //   //       "#e95d69",
        //   //       "#16a085",
        //   //       "#ff9900",
        //   //       "#e95d69",
        //   //       "#e95d69"
        //   //     ]
        //   //   }
        //   // },
        //   {
        //     prop: "productId",
        //     showName: "通道类型",
        //     fixed: false,
        //     width:"100px",
        //     formatData: function(val) {
        //       if (val == 1) {
        //         return (val = "短信通道");
        //       }
        //       if (val == 2) {
        //         return (val = "彩信通道");
        //       }
        //       if (val == 3) {
        //         return (val = "视频短信");
        //       }
        //       if (val == 4) {
        //         return (val = "国际短信");
        //       }
        //       if (val == 6) {
        //         return (val = "语音验证码");
        //       }
        //       if (val == 7) {
        //         return (val = "语音通知");
        //       }
        //     }
        //   },
        //   {
        //     prop: "isPrivateStr",
        //     showName: "是否专属",
        //     width:"120px",
        //     fixed: false
        //     // formatData: function(val) {
        //     //   if (val == 1) {
        //     //     return (val = "否");
        //     //   }
        //     //   if (val == 2) {
        //     //     return (val = "是");
        //     //   }

        //     // }
        //   },
        //   {
        //     prop: "channelStatus",
        //     showName: "状态",
        //     fixed: false,
        //     width:"60px",
        //     formatData: function(val) {
        //       if (val == 1) {
        //         return (val = "启用");
        //       }
        //       if (val == 2) {
        //         return (val = "停用");
        //       }
        //     },
        //     showCondition: {
        //       condition: "2"
        //     }
        //   }
        // ],
        // tableStyle: {
        //   isSelection: true, //是否复选框
        //   // height:250,//是否固定表头
        //   isExpand: true, //是否是折叠的
        //   isDefaultExpand: false, //是否默认打开
        //   expandLine2: true, //折叠栏是否放两个
        //   style: {
        //     //表格样式,表格宽度
        //     width: "100%"
        //   },
        //   optionWidth: "290", //操作栏宽度
        //   border: true, //是否边框
        //   stripe: false //是否有条纹
        // },
        // conditionOption: [
        //   // {
        //   //   contactCondition: "channelStatus", //关联的表格属性
        //   //   contactData: "1", //关联的表格属性-值
        //   //   optionName: "停用", //按钮的显示文字
        //   //   optionMethod: "stop", //按钮的方法
        //   //   icon: "el-icon-circle-close-outline", //按钮图标
        //   //   optionButtonColor: "orange", //按钮颜色
        //   //   otherOptionName: "启用", //其他条件的按钮显示文字
        //   //   otherOptionMethod: "start", //其他条件的按钮方法
        //   //   otherIcon: "el-icon-circle-check-outline", //其他条件按钮的图标
        //   //   optionOtherButtonColor: "#16A589" //其他条件按钮的颜色
        //   // },
        //   {
        //     contactCondition: "channelStatus", //关联的表格属性
        //     contactData: "1", //关联的表格属性-值
        //     optionName: "编辑", //按钮的显示文字
        //     optionMethod: "details", //按钮的方法
        //     icon: "el-icon-edit", //按钮图标
        //     optionButtonColor: "#16A589" //按钮颜色
        //   },
        //   {
        //     contactCondition: "channelStatus", //关联的表格属性
        //     contactData: "2", //关联的表格属性-值
        //     optionName: "编辑", //按钮的显示文字
        //     optionMethod: "details", //按钮的方法
        //     icon: "el-icon-edit", //按钮图标
        //     optionButtonColor: "#16A589" //按钮颜色
        //   },
        //   // {
        //   //   contactCondition: "channelStatus", //关联的表格属性
        //   //   contactData: "1", //关联的表格属性-值
        //   //   optionName: "设置标签", //按钮的显示文字
        //   //   optionMethod: "setNote", //按钮的方法
        //   //   icon: "el-icon-setting" //按钮图标
        //   // },
        //   {
        //     contactCondition: "channelStatus", //关联的表格属性
        //     contactData: "1", //关联的表格属性-值
        //     optionName: "配置通道", //按钮的显示文字
        //     optionMethod: "addSetting", //按钮的方法
        //     icon: "el-icon-setting" //按钮图标
        //   },{
        //     contactCondition: "channelStatus", //关联的表格属性
        //     contactData: "2", //关联的表格属性-值
        //     optionName: "配置通道", //按钮的显示文字
        //     optionMethod: "addSetting", //按钮的方法
        //     icon: "el-icon-setting" //按钮图标
        //   },
        //   {
        //     contactCondition: "channelStatus", //关联的表格属性
        //     contactData: "2", //关联的表格属性-值
        //     optionName: "删除", //按钮的显示文字
        //     optionMethod: "dellog", //按钮的方法
        //     icon: "el-icon-error", //按钮图标
        //     optionButtonColor: "#f56c6c" //按钮颜色
        //   }
        // ],
        // tableOptions: []
      },
      cityList: [], //城市列表
    };
  },
  methods: {
    //获取类型的名字
    changeLocationValue(value) {
      console.log(value);
      for (let i = 0; i < value.length; i++) {
        let obj = this.labelpass1.find((item) => {
          return item.labelId === value[i];
        });
        this.labelpass1Name[i] = obj.labelContent;
      }
    },
    updateView(e) {
      this.$forceUpdate();
    },
    //------------------列表数据------------------
    gettableLIst() {
      //获取列表
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingchannelinfo/page",
        this.tabelAlllist,
        (res) => {
          for (let i = 0; i < res.records.length; i++) {
            if (res.records[i].labelNames) {
              res.records[i].labelNames = res.records[i].labelNames.split(",");
            }
          }
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.total;
          this.tableDataObj.tableData = res.records;
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    changeCity(e) {
      window.api.get(
        window.path.omcs +
          "operatingchannelprovincial/cityInProvince?provincialId=" +
          e,
        {},
        (res) => {
          if (res.code == 200) {
            this.cityList = res.data;
          } else {
            this.$message.error(res.msg);
          }
        }
      );
    },
    /***********************通道运营商 */
    getLabel() {
      console.log(11);

      //获取运营商
      window.api.get(
        window.path.omcs + "operatingchannelinfo/operator",
        {
          pageSize: 100,
          currentPage: 1,
        },
        (res) => {
          this.operatorNames = res.data;
        }
      );
    },
    //添加通道
    addtongdao() {
      this.dialogStatus = "add";
      this.addUserDialog = true;
      this.channelIsfalse = false;
      this.addUserStep1Form.formData.velocityRateYzm = "";
      this.addUserStep1Form.formData.velocityRateHy = "";
      this.addUserStep1Form.formData.velocityRateYx = "";
    },
    //添加配置
    // change(e){
    //   if(res.data[0].xsmsEnable == '1'){
    //       this.addUserStep1Forms.formData.xsmsEnable = 'true'
    //   }else if(res.data[0].xsmsEnable == '0'){
    //       this.addUserStep1Forms.formData.xsmsEnable = 'false'
    //   }
    // },
    addSetting(val) {
      this.operatorsType5g = val.row.operatorsType;
      this.productIdShow = val.row.productId;
      this.addUserStep1Forms.formData.productId = val.row.productId;
      this.addUserDialogs = true;
      this.passresetId = val.row.channelCode;
      let aa = val.row.channelCode.split();
      window.api.post(
        window.path.omcs + "channelsetting/queryChannel",
        {
          channelIds: aa,
        },
        (res) => {
          this.TDH = res.data[0].channelId;
          if (res.data != "") {
            this.$nextTick(() => {
              this.$refs.addUserStep1Forms.resetFields();
              Object.assign(this.addUserStep1Forms.formData, res.data[0]); //编辑框赋值
              // if(res.data[0].xsmsEnable){
              //   this.addUserStep1Forms.formData.xsmsEnable = res.data[0].xsmsEnable
              // }else{
              //   this.addUserStep1Forms.formData.xsmsEnable = 'false'
              // }
              if (res.data[0].channelVersion) {
                this.addUserStep1Forms.formData.channelVersion =
                  res.data[0].channelVersion;
              } else {
                this.addUserStep1Forms.formData.channelVersion = "v1";
              }
              if (res.data[0].toRabbitMq) {
                this.addUserStep1Forms.formData.toRabbitMq =
                  res.data[0].toRabbitMq;
              } else {
                this.addUserStep1Forms.formData.toRabbitMq = "false";
              }
              if (res.data[0].connect == false) {
                this.settimes = "未连接";
              } else {
                this.settimes = "已连接";
              }
              if (res.data[0].reset == false) {
                this.passreset = true;
              } else {
                this.passreset = false;
              }
              if (res.data[0].moType) {
                this.addUserStep1Forms.formData.moType = res.data[0].moType;
              } else {
                this.addUserStep1Forms.formData.moType = "BOTH";
              }
              if (res.data[0].removeSign === null) {
                this.addUserStep1Forms.formData.removeSign = "false";
              } else {
                this.addUserStep1Forms.formData.removeSign =
                  res.data[0].removeSign;
              }
              if (res.data[0].longSms2Fallback === null) {
                this.addUserStep1Forms.formData.longSms2Fallback = "false";
              } else {
                this.addUserStep1Forms.formData.longSms2Fallback =
                  res.data[0].longSms2Fallback;
              }
            });
          }
        }
      );
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val;
      this.$refs.ChannelRef.ChannelClick();
    },
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline);
      if (!this.tableDataObj.loading2) {
        this.gettableLIst();
      }
    },
    //重置关闭
    Reload() {
      this.$refs["formInline"].resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.formInline.pactType = "";
      this.gettableLIst();
    },
    // 监听配置运营商
    productIdShowChange(val) {
      if (val == "VOICE_SIP") {
        this.addUserStep1Forms.formData.version = "VOICE_SIP";
      } else {
        this.addUserStep1Forms.formData.version = "";
      }
    },
    //提交设置标签 和 批量设置标签修改
    submitForm(setLabel) {
      this.$refs[setLabel].validate((valid) => {
        let labelIds = [];
        let hangye = this.setLabel.formData.hangye;
        let style = this.setLabel.formData.style;
        if (hangye != []) {
          for (let i = 0; i < hangye.length; i++) {
            labelIds.push(hangye[i]);
          }
        }
        if (style != []) {
          for (let i = 0; i < style.length; i++) {
            labelIds.push(style[i]);
          }
        }
        if (this.setLabel.formData.cishu != "") {
          labelIds.push(this.setLabel.formData.cishu);
        }
        if (this.setLabel.formData.signqingkuang != "") {
          labelIds.push(this.setLabel.formData.signqingkuang);
        }
        if (this.setLabel.formData.tuidinggeshi != "") {
          labelIds.push(this.setLabel.formData.tuidinggeshi);
        }
        this.labelIds = labelIds.join(",");
        let arrlabel = labelIds;
        if (valid) {
          let passIds;

          if (this.selectId.length > 0 && this.passId == "") {
            this.passIds = this.selectId;
          } else {
            this.passIds = this.passId;
          }

          if (arrlabel.indexOf(55) == -1) {
            this.$confirms.confirmation(
              "put",
              "确认执行此操作吗？",
              window.path.omcs +
                "operatingchannelinfo/label?channelIds=" +
                this.passIds +
                "&labelIds=" +
                this.labelIds,
              {},
              () => {
                this.dialogFormVisible = false;
                this.gettableLIst();
              }
            );
          } else {
            if (
              (arrlabel.indexOf(55) != -1 && arrlabel.indexOf(4) != -1) ||
              (arrlabel.indexOf(55) != -1 && arrlabel.indexOf(5) != -1)
            ) {
              this.$confirms.confirmation(
                "put",
                "确认执行此操作吗？",
                window.path.omcs +
                  "operatingchannelinfo/label?channelIds=" +
                  this.passIds +
                  "&labelIds=" +
                  this.labelIds,
                {},
                () => {
                  this.dialogFormVisible = false;
                  this.gettableLIst();
                }
              );
            } else {
              this.$message({
                message: "退订格式必须选择！",
                type: "warning",
              });
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formop) {
      this.dialogFormVisible = false;
      this.$refs[formop].resetFields();
    },

    // 添加通道配置
    submitFormss(addUserStep1Forms) {
      this.$refs.addUserStep1Forms.validate((valid) => {
        if (valid) {
          this.passDis = true;
          this.addUserStep1Forms.formData.productId = this.productIdShow;
          this.addUserStep1Forms.formData.channelId = this.passresetId;
          this.$confirm("确认执行此操作吗？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              window.api.post(
                window.path.omcs + "channelsetting/insertOrUpdateChannel",
                this.addUserStep1Forms.formData,
                (res) => {
                  if (res.code == 200) {
                    this.$message({
                      type: "success",
                      message: "操作成功!",
                    });
                    this.addUserDialogs = false;
                    this.passDis = false;
                    this.gettableLIst();
                  } else {
                    this.$message({
                      type: "warning",
                      message: res.msg,
                    });
                    // this.addUserDialogs = false;
                    this.passDis = false;
                    this.gettableLIst();
                  }
                }
              );
            })
            .catch(() => {
              this.$message({
                type: "info",
                message: "已取消操作！",
              });
              this.passDis = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //通道重置操作
    passReset: function (val) {
      this.$confirms.confirmation(
        "post",
        "确认执行重置操作吗？",
        window.path.omcs + "channelsetting/channelReset",
        {
          channelId: this.passresetId,
          // reset:true
        },
        (res) => {
          this.addUserDialogs = false;
          this.gettableLIst();
        }
      );
    },
    // 添加通道 编辑提交和取消
    submitForms(addUserStep1Form) {
      this.$refs.addUserStep1Form.validate((valid) => {
        if (valid) {
          this.passDis = true;
          if (this.dialogStatus == "edit") {
            delete this.addUserStep1Form.formData.labelNames;
            this.addUserStep1Form.formData.channelId = this.passId;
            if (
              this.tableRow.channelCode ==
                this.addUserStep1Form.formData.channelCode &&
              this.tableRow.channelName ==
                this.addUserStep1Form.formData.channelName
            ) {
              if (this.addUserStep1Form.formData.productId == 1) {
                let a = Number(this.addUserStep1Form.formData.velocityRateYzm);
                let b = Number(this.addUserStep1Form.formData.velocityRateHy);
                let c = Number(this.addUserStep1Form.formData.velocityRateYx);
                let d = a * 10 + b * 10 + c * 10;
                // if (d == 10) {
                this.$confirm("确认执行此操作吗？", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    window.api.put(
                      window.path.omcs + "operatingchannelinfo",
                      this.addUserStep1Form.formData,
                      (res) => {
                        this.$message({
                          type: "success",
                          message: "操作成功!",
                        });
                        this.addUserDialog = false;
                        this.gettableLIst();
                      }
                    );
                  })
                  .catch(() => {
                    this.$message({
                      type: "info",
                      message: "已取消操作！",
                    });
                    this.passDis = false;
                  });
                // }
                // else {
                //   this.$message({
                //     message: "三种流速加起来必须等于1",
                //     type: "warning",
                //   });
                //   this.passDis = false;
                // }
              } else {
                this.$confirm("确认执行此操作吗？", "提示", {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                })
                  .then(() => {
                    window.api.put(
                      window.path.omcs + "operatingchannelinfo",
                      this.addUserStep1Form.formData,
                      (res) => {
                        this.$message({
                          type: "success",
                          message: "操作成功!",
                        });
                        this.addUserDialog = false;
                        this.gettableLIst();
                      }
                    );
                  })
                  .catch(() => {
                    this.$message({
                      type: "info",
                      message: "已取消操作！",
                    });
                    this.passDis = false;
                  });
              }
            } else {
              //验证通道名称是否存在
              window.api.get(
                window.path.omcs +
                  "operatingchannelinfo/checkName/" +
                  this.addUserStep1Form.formData.channelName,
                {},
                (res) => {
                  if (res.code != 200) {
                    this.$message({
                      message: res.msg,
                      type: "warning",
                    });
                    this.passDis = false;
                  } else {
                    if (this.addUserStep1Form.formData.productId == 1) {
                      let a = Number(
                        this.addUserStep1Form.formData.velocityRateYzm
                      );
                      let b = Number(
                        this.addUserStep1Form.formData.velocityRateHy
                      );
                      let c = Number(
                        this.addUserStep1Form.formData.velocityRateYx
                      );
                      let d = a * 10 + b * 10 + c * 10;
                      // if (d == 10) {
                      this.$confirm("确认执行此操作吗？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                      })
                        .then(() => {
                          window.api.put(
                            window.path.omcs + "operatingchannelinfo",
                            this.addUserStep1Form.formData,
                            (res) => {
                              this.$message({
                                type: "success",
                                message: "操作成功!",
                              });
                              this.addUserDialog = false;
                              this.gettableLIst();
                            }
                          );
                        })
                        .catch(() => {
                          this.$message({
                            type: "info",
                            message: "已取消操作！",
                          });
                          this.passDis = false;
                        });
                      // } else {
                      //   this.$message({
                      //     message: "三种流速加起来必须等于1",
                      //     type: "warning",
                      //   });
                      //   this.passDis = false;
                      // }
                    } else {
                      this.$confirm("确认执行此操作吗？", "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                      })
                        .then(() => {
                          window.api.put(
                            window.path.omcs + "operatingchannelinfo",
                            this.addUserStep1Form.formData,
                            (res) => {
                              this.$message({
                                type: "success",
                                message: "操作成功!",
                              });
                              this.addUserDialog = false;
                              this.gettableLIst();
                            }
                          );
                        })
                        .catch(() => {
                          this.$message({
                            type: "info",
                            message: "已取消操作！",
                          });
                          this.passDis = false;
                        });
                    }
                  }
                }
              );
            }
          } else {
            //验证通道id是否存在
            window.api.get(
              window.path.omcs +
                "/operatingchannelinfo/channelCode/" +
                this.addUserStep1Form.formData.channelCode,
              {},
              (res) => {
                if (res.code != 200) {
                  this.$message({
                    message: res.msg,
                    type: "warning",
                  });
                  this.passDis = false;
                } else {
                  //验证通道名称是否存在
                  window.api.get(
                    window.path.omcs +
                      "operatingchannelinfo/checkName/" +
                      this.addUserStep1Form.formData.channelName,
                    {},
                    (res) => {
                      if (res.code != 200) {
                        this.$message({
                          message: res.msg,
                          type: "warning",
                        });
                        this.passDis = false;
                      } else {
                        if (this.addUserStep1Form.formData.productId == 1) {
                          let a = Number(
                            this.addUserStep1Form.formData.velocityRateYzm
                          );
                          let b = Number(
                            this.addUserStep1Form.formData.velocityRateHy
                          );
                          let c = Number(
                            this.addUserStep1Form.formData.velocityRateYx
                          );
                          let d = a * 10 + b * 10 + c * 10;
                          // if (d == 10) {
                          this.$confirm("确认执行此操作吗？", "提示", {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                          })
                            .then(() => {
                              window.api.post(
                                window.path.omcs + "operatingchannelinfo",
                                this.addUserStep1Form.formData,
                                (res) => {
                                  this.$message({
                                    type: "success",
                                    message: "操作成功!",
                                  });
                                  this.addUserDialog = false;
                                  this.gettableLIst();
                                }
                              );
                            })
                            .catch(() => {
                              this.$message({
                                type: "info",
                                message: "已取消操作！",
                              });
                              this.passDis = false;
                            });
                          // } else {
                          //   this.$message({
                          //     message: "三种流速加起来必须等于1",
                          //     type: "warning",
                          //   });
                          //   this.passDis = false;
                          // }
                        } else {
                          this.$confirm("确认执行此操作吗？", "提示", {
                            confirmButtonText: "确定",
                            cancelButtonText: "取消",
                            type: "warning",
                          })
                            .then(() => {
                              window.api.post(
                                window.path.omcs + "operatingchannelinfo",
                                this.addUserStep1Form.formData,
                                (res) => {
                                  this.$message({
                                    type: "success",
                                    message: "操作成功!",
                                  });
                                  this.addUserDialog = false;
                                  this.gettableLIst();
                                }
                              );
                            })
                            .catch(() => {
                              this.$message({
                                type: "info",
                                message: "已取消操作！",
                              });
                              this.passDis = false;
                            });
                        }
                      }
                    }
                  );
                }
              }
            );
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    //编辑
    detailsRow(val) {
      this.dialogStatus = "edit";
      this.addUserDialog = true;
      this.passId = val.row.channelId;
      this.tableRow = val.row;
      this.channelIsfalse = true;
      this.$nextTick(() => {
        this.$refs.addUserStep1Form.resetFields();
        val.row.removeSign += "";
        Object.assign(this.addUserStep1Form.formData, val.row); //编辑框赋值
        console.log(
          this.addUserStep1Form.formData,
          "this.addUserStep1Form.formData"
        );

        this.addUserStep1Form.formData.pactType =
          "" + this.tableRow.pactType + "";
        if (
          val.row.supportNumberPortability ||
          val.row.supportNumberPortability == false
        ) {
          this.addUserStep1Form.formData.supportNumberPortability =
            val.row.supportNumberPortability + "";
        } else {
          this.addUserStep1Form.formData.supportNumberPortability = "true";
        }
        if (val.row.provincialId) {
          this.addUserStep1Form.formData.provincialId = parseInt(
            val.row.provincialId
          );
          this.changeCity(val.row.provincialId);
        }
        if (val.row.cityId) {
          this.addUserStep1Form.formData.cityId = val.row.cityId * 1;
        } else {
          this.addUserStep1Form.formData.cityId = "";
        }
        // console.log();
        // this.addUserStep1Form.formData.supportNumberPortability = val.row.supportNumberPortability +''
      });
    },
    //删除
    delRow: function (val) {
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据, 是否继续？",
        window.path.omcs + "operatingchannelinfo/" + val.row.channelId,
        {},
        () => {
          this.gettableLIst();
        }
      );
    },
    //验证码，行业，营销点击事件
    channeClear(val, tpye) {
      let a;
      if (tpye == "YZM") {
        a = "验证码队列";
      } else if (tpye == "HY") {
        a = "行业队列";
      } else {
        a = "营销队列";
      }
      this.$confirms.confirmation(
        "post",
        "确定执行清除通道" + val.channelCode + a + "操作？",
        window.path.omcs + "operatingchannelinfo/clear",
        {
          channelCode: val.channelCode,
          type: tpye,
        },
        () => {
          this.gettableLIst();
        }
      );
    },
    channeClearQueue(val, tpye) {
      let a;
      if (tpye == "YZM") {
        a = "验证码队列";
      } else if (tpye == "HY") {
        a = "行业队列";
      } else {
        a = "营销队列";
      }
      this.$confirms.confirmation(
        "post",
        "确定执行清除通道" + val.channelCode + a + "操作？",
        window.path.omcs + "operatingchannelinfo/clearQueue",
        {
          channelCode: val.channelCode,
          type: tpye,
        },
        () => {
          this.gettableLIst();
        }
      );
    },
    handelOptionButton: function (val) {
      if (val.methods == "details") {
        this.detailsRow(val);
      }
      if (val.methods == "start") {
        this.delStatus(val, 1);
      }
      if (val.methods == "stop") {
        window.api.get(
          window.path.omcs +
            "v3/operatingchannelgroup/checkChannel/" +
            val.row.channelCode,
          {},
          (res) => {
            if (res.code != 200) {
              this.$message({
                message: res.msg,
                type: "warning",
              });
              this.passDis = false;
            } else {
              this.delStatus(val, 2);
            }
          }
        );
      }
      // if (val.methods == "setNote") {
      //   // this.addopt(val);
      // }
      if (val.methods == "addSetting") {
        this.addSetting(val);
      }
      if (val.methods == "dellog") {
        this.delRow(val);
      }
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = [];
      let channelStatus = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].channelId);
        channelStatus.push(val[i].channelStatus);
      }
      this.channelStatus = channelStatus;
      this.selectId = selectId.join(",");
    },
  },
  // mounted() {
  //   console.log(11111);

  //   // this.getpassLabels();
  //   // 获取省市
  // },
  activated() {
    console.log(1);

    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.productList = productData.productList;
        this.gettableLIst();
        this.getLabel();
        window.api.get(
          window.path.omcs + "operatingchannelprovincial/list",
          {},
          (res) => {
            this.ProvincesCities = res.data;
          }
        );
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
    // this.gettableLIst();
  },
  created() {
    console.log(111);

    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.productList = productData.productList;
      this.gettableLIst();
      this.getLabel();
      window.api.get(
        window.path.omcs + "operatingchannelprovincial/list",
        {},
        (res) => {
          this.ProvincesCities = res.data;
        }
      );
    });
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep: true, //深度监听
    //   immediate: true //页面初始监听
    // },
    //监听验证码流速占比
    "addUserStep1Form.formData.velocityRateYzm": function (val) {
      let a = this.addUserStep1Form.formData.velocityRateYzm;
      let b = this.addUserStep1Form.formData.velocityRateHy;
      let c = this.addUserStep1Form.formData.velocityRateYx;
      if (a == 1) {
        this.addUserStep1Form.formData.velocityRateHy = 0;
        this.addUserStep1Form.formData.velocityRateYx = 0;
      } else if (b == 1) {
        this.addUserStep1Form.formData.velocityRateYzm = 0;
        this.addUserStep1Form.formData.velocityRateYx = 0;
      } else if (c == 1) {
        this.addUserStep1Form.formData.velocityRateHy = 0;
        this.addUserStep1Form.formData.velocityRateYzm = 0;
      }
    },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        //清空数据储存的
        this.labelpass1 = [];
        this.labelpass2 = [];
        this.labelpass3 = [];
        this.labelpass4 = [];
        this.labelpass5 = [];
        //清空表单里的
        this.setLabel.formData.style = [];
        this.setLabel.formData.hangye = [];
        this.setLabel.formData.tuidinggeshi = "";
        this.setLabel.formData.signqingkuang = "";
        this.setLabel.formData.cishu = "";
        this.passId = "";
      }
    },
    //监听弹框是否关闭
    addUserDialog(val) {
      if (val == false) {
        this.passDis = false;
        this.addUserStep1Form.formData.provincialId = "";
        this.addUserStep1Form.formData.cityId = "";
        this.addUserStep1Form.formData.provincial = "";
        this.addUserStep1Form.formData.privateName = "";
        this.$refs.addUserStep1Form.resetFields();
        this.addUserStep1Form.formData.isPrivate = "1";
        this.addUserStep1Form.formData.channelFlag = "1";
        this.cityList = [];
      }
    },
    addUserDialogs(val) {
      if (val == false) {
        this.TDH = "";
        this.passDis = false;
        this.settimes = "未连接";
        this.passreset = false;
        this.$refs.addUserStep1Forms.resetFields();
      }
    },
  },
};
</script>

<style scoped>
.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.clearfix {
  zoom: 1;
}
.detailsList {
  height: 42px;
  line-height: 26px;
  padding-left: 30px;
  font-size: 12px;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
.elButton i {
  margin-right: 3px;
}
.tableTem .el-form-item {
  margin-right: 0;
  margin-bottom: 0 !important;
}
.tableTem .el-form-item {
  width: 100%;
}
.tableTem .el-form-item__content {
  max-width: calc(100% - 100px);
}
.tableTem .demo-table-expand label {
  color: #99a9bf;
}
.tableTem .el-form-item--small .el-form-item__content,
.tableTem .el-form-item--small .el-form-item__label {
  line-height: 24px;
}
.comTableTag {
  display: inline-block;
  height: 30px !important;
  line-height: 27px !important;
  margin: 3px;
  padding: 3px;
  color: #fff !important;
  padding: 0 10px !important;
}
.red {
  color: red;
}
.tableTem .el-form-item.expand2Line {
  max-width: 33.33% !important;
}
.tableTem .expand2Line .el-form-item__content {
  max-width: calc(100% - 130px) !important;
  color: #b3b0b0 !important;
}
.elButton {
  cursor: pointer;
  color: #16a589;
  display: inline-block;
  padding: 0px 8px;
}
</style>

<style>
.imgss {
  width: 100px;
  height: 150px;
  margin-left: 15px;
  cursor: pointer;
}

.bkg input {
  background: #f2f2f2 !important;
}

.banban .el-dialog {
  height: 100%;
}

.signsign .el-dialog__footer {
  text-align: center;
}

.el-dialog__title {
  font-size: 16px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>