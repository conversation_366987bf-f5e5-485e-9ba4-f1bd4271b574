<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" label-width="82px" ref="queryForm">
          <el-form-item label="用户类型" label-width="82px" prop="roleId">
            <el-select v-model="formInline.roleId" @keyup.enter="Query()" clearable placeholder="不限" class="input-w">
              <el-option label="线上终端" value="22"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名" label-width="82px" prop="consumerName">
            <el-input v-model="formInline.consumerName" @keyup.enter="Query()" placeholder=""
              class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="公司名" label-width="82px" prop="compName">
            <el-input v-model="formInline.compName" @keyup.enter="Query()" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="账号状态" label-width="82px" prop="consumerStatus">
            <el-select v-model="formInline.consumerStatus" @keyup.enter="Query()" clearable placeholder="不限"
              class="input-w">
              <el-option label="启用" value="0"></el-option>
              <el-option label="停用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="付费方式" label-width="82px" prop="smsPayMethod">
            <el-select v-model="formInline.smsPayMethod" @keyup.enter="Query()" clearable placeholder="不限"
              class="input-w">
              <el-option label="不限" value="0"></el-option>
              <el-option label="预付费" value="1"></el-option>
              <el-option label="后付费" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证状态" label-width="82px" prop="certificate">
            <el-select v-model="formInline.certificate" @keyup.enter="Query()" clearable placeholder="不限"
              class="input-w">
              <el-option label="未认证" value="0"></el-option>
              <el-option label="认证中" value="1"></el-option>
              <el-option label="已认证" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合作状态" label-width="82px" @keyup.enter="Query()" prop="cooperateStatus">
            <el-select v-model="formInline.cooperateStatus" @keyup.enter="Query()" clearable placeholder="不限"
              class="input-w">
              <el-option label="测试阶段" value="TEST"></el-option>
              <el-option label="正式合作" value="NORMAL"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="创建人" label-width="82px" prop="createName">
                            <el-input v-model="formInline.createName"  @keyup.enter.native="Query()"  placeholder="" class="input-w"></el-input>
                        </el-form-item> -->
          <el-form-item label="创建时间" label-width="82px" prop="time">
            <el-date-picker class="input-w" v-model="formInline.time" value-format="YYYY-MM-DD" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="handeTime">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom" style="padding-left: 10px;">
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload('queryForm')">重置</el-button>
      </div>
      <div class="Mail-table Mail-table1">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="shopShopUserList" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;" :data="tableDataObj.tableData">

          <vxe-column field="用户名称" title="用户名称" width="180">
            <!-- 点击用户名 -->
            <template v-slot="scope">
              <div>
                <div>
                  {{ scope.row.consumerName }}
                </div>
                <div>
                  <span>{{ scope.row.remark }}</span>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="公司名" title="公司名">
            <template v-slot="scope">
              <div>{{ scope.row.compName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号">
            <template #default="{ row, rowIndex }">
              <div v-if="rowIndex != count || phone == ''" @click="linkTo(row, rowIndex)"
                style="color: #16a589; cursor: pointer">查看手机号</div>
              <div v-else>{{ phone }}</div>
            </template>
          </vxe-column>
          <vxe-column field="付费方式" title="付费方式" width="90">
            <template v-slot="scope">
              <div v-if="scope.row.smsPayMethod == 1">预付费</div>
              <div v-else-if="scope.row.smsPayMethod == 2">后付费</div>
            </template>
          </vxe-column>
          <vxe-column field="余额" title="余额" min-width="120">
            <template v-slot="scope">
              <div style="display: flex">
                <div v-for="(item, index) in scope.row.balanceList" :key="index" @click="more(scope.row)">
                  <el-tag :disable-transitions="true" v-if="item.name == '短信'" :style="'margin: 5px 0px 5px 10px;cursor: pointer;color:' +
                    tagColor[index]
                    ">{{ item.name }} : {{ item.num }}
                    {{
                      item.status == 0
                        ? '（开启）'
                        : item.status == 1
                          ? '（用户关闭）'
                          : '（运营关闭）'
                    }}</el-tag>
                </div>
                <!-- <span style="margin: 5px 0px 5px 10px;color:#67c23a;cursor: pointer;"  @click="more(scope.row)">查看更多</span> -->
              </div>
            </template>
          </vxe-column>

          <vxe-column field="账号状态" title="账号状态" width="90">
            <template v-slot="scope">
              <div v-if="scope.row.consumerStatus == 0">启用</div>
              <div v-else-if="scope.row.consumerStatus == 1" style="color: red">停用</div>
            </template>
          </vxe-column>
          <vxe-column field="用户类型/所属管理商" title="用户类型/所属管理商" width="200">
            <template v-slot="scope">
              <!-- <span v-if="scope.row.roleId==12">管理商</span>
                         -->
              <div v-if="scope.row.roleId == 12">
                <div>管理商</div>
                <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
              </div>
              <!-- <span v-else-if="scope.row.roleId==13">子用户（{{scope.row.manageName}}）</span>   -->
              <!-- <span v-else-if="scope.row.roleId==14">终端</span>  -->
              <div v-else-if="scope.row.roleId == 13">
                <div>子用户（{{ scope.row.manageName }}）</div>
                <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
              </div>
              <div v-else-if="scope.row.roleId == 14">
                <div>终端</div>
                <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
              </div>
              <div v-else-if="scope.row.roleId == 22">
                <div>线上终端</div>
                <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="合作状态" title="合作状态">
            <template v-slot="scope">
              <div v-if="scope.row.cooperateStatus == 'TEST'">测试阶段</div>
              <div v-else-if="scope.row.cooperateStatus == 'NORMAL'">正式合作</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.createTime }}</div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0"
          > -->

        <div class="paginationBox">
          <el-pagination style="float: right" class="page_bottom" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" :current-page="searchInput.currentPage"
            :page-size="searchInput.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="totalSize">
          </el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <el-dialog :title="usreTitle" v-model="moreFlag" custom-class="way" width="30%">
      <div>
        <div style="font-size: 18px; color: #000; margin: 10px">余额</div>
        <div v-for="(item, index) in balanceLists" :key="index">
          <el-tag :disable-transitions="true" :style="'margin: 5px 0px 5px 10px;color:' + tagColor[index]">{{ item.name
            }} :
            {{ item.num }}
            {{
              item.status == 0
                ? '（开启）'
                : item.status == 1
                  ? '（用户关闭）'
                  : '（运营关闭）'
            }}</el-tag>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
import { ElMessage, ElMessageBox } from "element-plus";
// 引入时间戳转换
// import {formatDate} from '@/assets/js/date.js'
// var axios = require('axios')
import axios from 'axios'
export default {
  components: {
    TableTem,
    FileUpload
  },
  name: 'UserMag',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      tagColor: [
        '#67c23a',
        '#e6a23c',
        '#ff0000b3',
        '#909399',
        '#3c61e6d1',
        '#3cafe6bf',
        '#16A589',
        '#303133',
      ],
      checkuserClientId: 0,
      moreFlag: false,
      usreTitle: '',
      phone: [],
      count: null,

      balanceLists: [],
      // sales:[],//销售
      salesName: [], //销售
      formInline: {
        roleId: '22',
        consumerName: '',
        compName: '',
        consumerStatus: '',
        service: '',
        // sales:'',
        ext: '',
        salesName: '',
        createName: '',
        smsChargeback: '',
        smsPayMethod: '',
        time: '',
        createBeginTime: '',
        createEndTime: '',
        certificate: '',
        cooperateStatus: '',
        pageSize: 10,
        currentPage: 1,
      },
      searchInput: {
        roleId: '22',
        consumerName: '',
        compName: '',
        consumerStatus: '',
        service: '',
        // sales:'',
        ext: '',
        salesName: '',
        createName: '',
        smsChargeback: '',
        smsPayMethod: '',
        time: '',
        createBeginTime: '',
        createEndTime: '',
        pageSize: 10,
        currentPage: 1,
      },
      totalSize: 0, //总条数
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  watch: {},
  methods: {
    more(row) {
      // console.log(row,'row');
      this.moreFlag = true
      this.balanceLists = row.balanceList
      this.usreTitle = row.consumerName
    },
    getTableData() {
      //获取列表数据
      this.phone = []
      let formDatas = this.searchInput
      this.tableDataObj.loading2 = true
      window.api.post(window.path.omcs + 'operatinguser/page', formDatas, (res) => {
        // console.log(res)
        this.tableDataObj.tableData = res.records
        // this.tableDataObj.tableData.forEach(item=>{
        //     item.phone = null
        // })
        this.totalSize = res.total
        this.tableDataObj.loading2 = false
      })
    },
    Query() {
      //查询
      for (let key in this.formInline) {
        this.searchInput[key] = this.formInline[key]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
    },
    Reload(formName) {
      //重置
      this.formInline.createBeginTime = ''
      this.formInline.createEndTime = ''
      this.formInline.cooperateStatus = ''
      this.$refs[formName].resetFields() //清空查询表单
      for (let k in this.formInline) {
        this.searchInput[k] = this.formInline[k]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
    },
    handeTime: function (val) {
      //获取查询时间框的值
      // console.log(val)
      if (val) {
        this.formInline.createBeginTime = this.moment(val[0]).format(
          'YYYY-MM-DD '
        )
        this.formInline.createEndTime = this.moment(val[1]).format(
          'YYYY-MM-DD '
        )
      } else {
        this.formInline.createBeginTime = ''
        this.formInline.createEndTime = ''
      }
    },

    //-----翻页操作
    handleSizeChange(size) {
      this.searchInput.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      this.searchInput.currentPage = currentPage
      this.getTableData()
    },
    linkTo(row, index) {
      // console.log(row);
      window.api.get(
        window.path.omcs + 'operatinguser/userPhone/' + row.userId,
        {},
        (res) => {
          this.count = index
          // this.tableDataObj.tableData.forEach((item,i)=>{
          //     if(i == index) item.phone = res.data

          // })
          if (res.code == 200) {
            if (res.data) {
              this.phone = res.data
            } else {
              this.phone = res.data
              ElMessage.error('该用户没有绑定手机号');
            }
          } else {
            ElMessage.error(res.msg);
          }
          // row.phone = res.data
        }
      )
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableData()
    })

    // this.getSales()
    // this.getChannel()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableData()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style scoped>
.selectedBox {
  width: 85px;
  text-align: center;
  height: 35px;
  line-height: 35px;
  border: 1px solid #1abc9c;
  position: relative;
}

.selectedBox:after {
  border-style: solid;
  right: 0;
  top: 0;
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border-width: 13px 0 0 13px;
  border-color: #1abc9c transparent transparent transparent;
}

.input-w-2 {
  width: 80% !important;
}

.red {
  color: red;
}

.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.tips {
  margin-top: 30px;
}

.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}

.mr-3 {
  margin-right: 3px;
}

.addUserDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}
</style>

<style>
.el-loading-spinner i {
  margin-top: -10px;
  margin-bottom: 10px;
  font-size: 30px;
}

.pro-style .el-checkbox {
  margin-right: 0 !important;
}

.Mail-table1 .el-table--small td {
  padding: 2px 0 !important;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
