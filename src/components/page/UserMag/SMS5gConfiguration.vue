<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title" style="display: flex; align-items: center">
        <div
          @click="goBack()"
          style="
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #16a589;
          "
        >
          <el-icon><ArrowLeftBold /></el-icon>
          <span style="display: inline-block; padding-right: 10px"> 返回</span>
        </div>
        |
        <span style="margin-left: 10px;">5G短信配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="参数｜计费设置" name="channnelRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="channnelRef"
            label-width="120px"
          >
            <el-form-item label="扩展号" prop="ext">
              <el-input
                style="width: 224px"
                v-model="addUserStep2Form.formData.ext"
              ></el-input>
            </el-form-item>
            <el-form-item label="计费方式" prop="payMethod">
              <el-radio-group v-model="addUserStep2Form.formData.payMethod">
                <el-radio :value="1">预付费</el-radio>
                <el-radio :value="2" style="margin-left: 10px">后付费</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="扣费方式" prop="chargeback">
              <el-radio-group v-model="addUserStep2Form.formData.chargeback">
                <el-radio :value="1">提交计费</el-radio>
                <el-radio :value="2" style="margin-left: 10px"
                  >成功计费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(channnelRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="状态设置" name="stateRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="stateRef"
            label-width="100px"
          >
            <el-form-item label="消息报告" prop="reportLevel">
              <el-radio-group
                v-model="addUserStep2Form.formData.reportLevel"
                class="input-w"
              >
                <el-radio value="1">不接收报告</el-radio>
                <el-radio value="2" style="margin-left: 10px"
                  >批量推送</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="上行地址推送" prop="repliesUrl">
              <el-input
                type="textarea"
                style="width: 250px"
                placeholder=""
                v-model="addUserStep2Form.formData.repliesUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取回复短信的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              v-if="addUserStep2Form.formData.reportLevel == '2'"
              label="下行地址推送"
              prop="reportUrl"
            >
              <el-input
                style="width: 250px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.reportUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取状态报告的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(stateRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="审核设置" name="auditRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="auditRef"
            label-width="140px"
          >
            <el-form-item label="回复是否自动加黑" prop="isRepliesBlack">
              <el-radio-group
                v-model="addUserStep2Form.formData.isRepliesBlack"
              >
                <el-radio :value="1">是</el-radio>
                <el-radio :value="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否启用黑号检测" prop="isUserBlack">
              <el-radio-group v-model="addUserStep2Form.formData.isUserBlack">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="黑号验证级别" prop="userBlackLevel">
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.userBlackLevel"
              >
                <el-checkbox label="一级" value="1" />
                <el-checkbox label="二级" value="2" />
                <el-checkbox label="三级" value="3" />
                <el-checkbox label="四级" value="4" />
                <el-checkbox label="五级" value="5" />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="黑词回复" prop="repliesBlackWords">
              <el-input
                style="width: 224px"
                v-model="addUserStep2Form.formData.repliesBlackWords"
              ></el-input>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(auditRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="防轰炸设置" name="bombRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="bombRef"
            label-width="160px"
          >
            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.dailyLimited"
                :min="0"
                :max="999999999"
                label="账号每日提交发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input> -->
            </el-form-item>
            <el-form-item label="24H防轰炸设置" prop="overrunMarket">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.overrunMarket"
                :min="0"
                :max="999999999"
                label="24H防轰炸设置"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.overrunMarket"></el-input> -->
            </el-form-item>
            <el-form-item label="营销七天发送限制" prop="overrunTotalMarket">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.overrunTotalMarket"
                :min="0"
                :max="999999999"
                label="24H防轰炸设置"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.overrunTotalMarket"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(bombRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="addUserStep2Form"
        label-width="160px">
        <div class="float-div-1" style="padding-right: 20px">
          <div>
            <p style="padding: 0 0 18px 40px; font-weight: bold; font-size: 14px">
              计费设置
            </p>
            <el-form-item label="计费方式" prop="payMethod">
              <el-radio-group v-model="addUserStep2Form.formData.payMethod">
                <el-radio :label="1">预付费</el-radio>
                <el-radio :label="2" style="margin-left: 10px">后付费</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="扣费方式" prop="chargeback">
              <el-radio-group v-model="addUserStep2Form.formData.chargeback">
                <el-radio :label="1">提交计费</el-radio>
                <el-radio :label="2" style="margin-left: 10px">成功计费</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <div>
            <p style="padding: 0 0 18px 40px; font-weight: bold; font-size: 14px">
              状态设置
            </p>
            <el-form-item label="消息报告" prop="reportLevel">
              <el-radio-group v-model="addUserStep2Form.formData.reportLevel" class="input-w">
                <el-radio label="1">不接收报告</el-radio>
                <el-radio label="2" style="margin-left: 10px">批量推送</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="上行地址推送" prop="repliesUrl">
              <el-input type="textarea" style="width: 250px" placeholder=""
                v-model="addUserStep2Form.formData.repliesUrl"></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取回复短信的地址</span>(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item v-if="addUserStep2Form.formData.reportLevel == '2'" label="下行地址推送" prop="reportUrl">
              <el-input style="width: 250px" placeholder="" type="textarea"
                v-model="addUserStep2Form.formData.reportUrl"></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取状态报告的地址</span>(以http/https开头)
              </p>
            </el-form-item>
          </div>
        </div>
        <div class="float-div-2" style="padding-right: 20px">
          <div>
            <p style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px">
              审核设置
            </p>
            <el-form-item label="回复是否自动加黑" prop="isRepliesBlack">
              <el-radio-group v-model="addUserStep2Form.formData.isRepliesBlack">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否启用黑号检测" prop="isUserBlack">
              <el-radio-group v-model="addUserStep2Form.formData.isUserBlack">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="黑号验证级别" prop="userBlackLevel">
              <el-checkbox-group style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                " v-model="addUserStep2Form.formData.userBlackLevel">
                <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="黑词回复" prop="repliesBlackWords">
              <el-input style="width: 224px" v-model="addUserStep2Form.formData.repliesBlackWords"></el-input>
            </el-form-item>
          </div>
          <div>
            <p style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px">
              参数设置
            </p>
            <el-form-item label="扩展号" prop="ext">
              <el-input style="width: 224px" v-model="addUserStep2Form.formData.ext"></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="float-div-3">
          <div>
            <p style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px">
              防轰炸设置
            </p>
            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
              <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input>
            </el-form-item>
            <el-form-item label="24H防轰炸设置" prop="overrunMarket">
              <el-input class="input-w-3" v-model="addUserStep2Form.formData.overrunMarket"></el-input>
            </el-form-item>
            <el-form-item label="营销七天发送限制" prop="overrunTotalMarket">
              <el-input class="input-w-3" v-model="addUserStep2Form.formData.overrunTotalMarket"></el-input>
            </el-form-item>
          </div>
        </div>
        <div style="text-align: center" class="clear-div">
          <el-button style="width:120px" @click="goBack()">返回</el-button>
          <el-button type="primary" style="margin-left: 20px;width:120px" @click="submitForm('addUserStep2Form')">提
            交</el-button>
        </div>
      </el-form> -->
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const channnelRef = ref(null);
const stateRef = ref(null);
const bombRef = ref(null);
const auditRef = ref(null);
const ruleFormRef = ref(null);
const isFirstEnter = ref(false);
const roleId = ref("");
const jsonData = reactive({
  consumerName: "",
  compName: "",
});
const smsIsContentSingleflag = ref("2");
const dialogVisible = ref(false);
const subLoding = ref(false);
const activeName = ref("channnelRef");
const ruleForm = reactive({
  remark: "",
});
const rules = reactive({
  remark: [{ required: true, message: "请填写备注", trigger: "change" }],
});
const addUserStep2Form = reactive({
  //弹窗步骤2的数据和验证
  formData: {
    userId: "",
    payMethod: null, //付费方式
    chargeback: null, //扣费方式
    isRepliesBlack: null, //回复是否自动加黑
    isUserBlack: null, //是否启用黑号检测
    overrunMarket: 0, //1天防轰炸设置
    overrunTotalMarket: 0, //7天营销发送限制
    repliesBlackWords: "", //回复黑词
    userBlackLevel: [], //黑号验证级别
    ext: "", //扩展号
    reportLevel: "1",
    repliesUrl: "", //上行推送
    reportUrl: "", //下行推送
    dailyLimited: 0, //每日限量
    //   smsPayMethod: "1",
    //   smsPrice: "",
    //   smsProductId: "",
    //   yzmProductId: "",
    //   smsChargeback: "1",
    //   yxProductId: "",
    //   // flag: '5',
    //   ext: "",

    //   smsRepliesUrl: "",
    //   smsReportUrl: "",
    //   smsRepliesPackageSize: "",
    //   smsStrepliesPackageSize: "",
    //   smsTemplateUrl: "",
    //   smsSignatureUrl: "",
    //   // 防轰炸
    //   overrunCode: "0",
    //   overrunIndustry: "0",
    //   overrunMarket: "0",
    //   overrunTotalMarket: "0",
    //   smsIsContentSingle: false,
    //   // smsSelect:'',
    //   businessTypes: "1",
    //   interfacePassword: "******", // 接口密码
    //   loginPassWord: "******", // 账号密码
    //   // ifExt: '1', // 生成扩展码：1，手动输入扩展码：2
    //   custom: "1", //是否显示自定义发送operatinguse
    //   isIndividualization: "2", //是否个性化设置
    //   smsIsOvershoot: "0", //预付费是否允许超发
    //   smsIsAudit: "1", //是否审核
    //   sign: "",
    //   smsAuditNum: 1, //审核号码数
    //   smsIsRepliesBlack: "2", //是否加黑
    //   smsRepliesBlackWords: "TD,t,T,td", //加黑关键词
    //   smsIsUserBlack: "1", //是否验证黑号
    //   smsIsExternalBlack:"2",
    //   smsIsPlatformBlack:"",
    //   smsUserBlackLevel1: ["1", "2", "3"], //黑号验证级别存储
    //   smsUserBlackLevel: "", //黑号验证级别存储
    //   isNumberPortability: "0", //携号转网
    //   encryptType: "", //加密方式
    //   secretKey: "", //秘钥
    //   secretIv: "", //偏移量
    //   mobileMask: "1", //掩码展示
    //   smsIsBlackWords: "1", //黑词是否审核
    //   reportTarget: "1", //回执推送账户
    //   // splitDb:'1',
    //   balanceTarget: "1", //扣费账户
    //   smsPushType: "2", //推送方式
    //   signSetting: "1", //短信签名位置
    //   reportPushSetting: "0", //回执推送
    //   replyPushSetting: "0", //回复推送
    //   reportCrawlSetting: "0", //回执抓取
    //   replyCrawlSetting: "0", //回复抓取
    //   smsIsSignatureExtension: "",
    // splitRatio:'',
    // provinceRatio:'',
    // remark:'', //备注
  },
  formRule: {
    // sign: [{ required: false, validator: sign, trigger: "change" }],
    smsPayMethod: [
      { required: true, message: "请选择付费方式", trigger: "change" },
    ],
    payMethod: [
      { required: true, message: "请选择付费方式", trigger: "change" },
    ],
    chargeback: [
      { required: true, message: "请选择扣费方式", trigger: "change" },
    ],
    isRepliesBlack: [
      { required: true, message: "请选择是否加黑", trigger: "blur" },
    ],
    // repliesUrl: [
    //   {
    //    required: true,
    //     message: "请输入上行地址",
    //     trigger: "blur",
    //   }
    // ],
    isUserBlack: [
      {
        required: true,
        message: "请选择是否验证黑号",
        trigger: "change",
      },
    ],
    userBlackLevel: [
      {
        required: true,
        message: "请选择黑号验证级别",
        trigger: "change",
      },
    ],
    // ext: [
    //   {
    //     required: true,
    //     message: "扩展号不能为空",
    //     trigger: "change",
    //   },
    // ],
    smsReportUrl: [
      {
        required: false,
        message: "请输入下行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
  },
});
const channel = ref([]);

onMounted(() => {
  addUserStep2Form.formData.userId = $router.currentRoute.value.query.id;
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo) {
    roleId.value = userInfo.roleId;
  }
  getBasicData();
  getChannel();
  handleEdit();
});
const getBasicData = () => {
  window.api.get(
    window.path.omcs + "operatinguser/" + addUserStep2Form.formData.userId,
    {},
    (res) => {
      jsonData.consumerName = res.data.consumerName;
      jsonData.compName = res.data.compName;
    }
  );
};
const getChannel = () => {
  window.api.get(
    window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup",
    {},
    (res) => {
      if (res.code == 200) {
        channel.value = res.data;
      }
    }
  );
};
const change = (val) => {
  if (val == 1) {
    addUserStep2Form.formData.smsIsContentSingle = true;
  } else {
    addUserStep2Form.formData.smsIsContentSingle = false;
  }
};
const goBack = () => {
  $router.push({
    path: "UserDetail",
    query: {
      id: $router.currentRoute.value.query.id,
    },
  });
};
//赋值
const handleEdit = () => {
  try {
    window.api.get(
      window.path.fiveWeb +
        "/usersetting/" +
        $router.currentRoute.value.query.id,
      {},
      (res) => {
        if (res.code == 200) {
          Object.assign(addUserStep2Form.formData, res.data);
          addUserStep2Form.formData.userBlackLevel =
            res.data.userBlackLevel.split(",");
          if (res.data.reportLevel) {
            addUserStep2Form.formData.reportLevel = res.data.reportLevel + "";
          } else {
            addUserStep2Form.formData.reportLevel == "1";
          }
          console.log(addUserStep2Form.formData);
        }
      }
    );
  } catch (error) {
    console.error(error);
  }
};
const submitForm = (formEl) => {
  console.log(formEl, "formEl");

  formEl.validate((valid, fields) => {
    if (valid) {
      let data = JSON.parse(JSON.stringify(addUserStep2Form.formData));
      data.userBlackLevel = data.userBlackLevel.join(",");
      ElMessageBox.confirm("确认修改5G短信配置？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          window.api.put(
            window.path.fiveWeb + "/usersetting",
            data,
            (res) => {
              if (res.code == 200) {
                goBack();
                ElMessage({
                  type: "success",
                  message: "修改成功",
                });
              } else {
                ElMessage({
                  type: "error",
                  message: res.msg,
                });
              }
            }
          );
        })
        .catch(() => {
          ElMessage({
            type: "info",
            message: "操作取消",
          });
        });
    } else {
      console.error("error submit!!", fields);
      return false;
    }
  });
};
const submitSetting = (formEl) => {
  formEl.validate((valid, fields) => {
    if (valid) {
      subLoding.value = true;
      addUserStep2Form.formData.remark = ruleForm.remark;
      window.api.put(
        window.path.omcs + "operatinguser/userVoiceInfo",
        addUserStep2Form.formData,
        (res) => {
          if (res.code == 200) {
            //返回用户管理页面
            subLoding.value = false;
            dialogVisible.value = false;
            goBack();
          } else {
            subLoding.value = false;
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      console.error("error submit!!", fields);
      return false;
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
};
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      ruleFormRef.value.resetFields();
    }
  }
);
</script>
<!-- <script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  name: "SMSconfiguration",
  data() {
    var checkBlackWords = (rule, value, callback) => {
      if (value == "") {
        return callback(new Error("加黑关键字不能为空"));
      } else {
        console.log(value.length);
        if (value.length <= 200) {
          var reg =
            /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,200}$/gi;
          if (!reg.test(value)) {
            return callback(
              new Error("加黑关键词包括数字、汉字、字母、多个由英文逗号隔开")
            );
          } else {
            var arr = value.split(",");
            var arr2 = [];
            for (var i = 0; i < arr.length; i++) {
              if (arr2.indexOf(arr[i]) < 0) {
                arr2.push(arr[i]);
              }
            }
            if (arr2.length != arr.length) {
              return callback(new Error("不允许重复"));
            } else {
              callback();
            }
          }
        }
        return callback(new Error("加黑关键字不能多于200个字"));
      }
    };
    var checkSignature = (rule, value, callback) => {
      if (!/^\d{1,12}$/.test(value)) {
        callback(new Error("扩展号由1-12位数字组成"));
      } else {
        window.api.get(
          window.path.omcs + "operatingSignature/checkClientExt",
          { ext: value, userId: this.addUserStep2Form.formData.userId },
          (res) => {
            if (res.data == true) {
              callback(new Error("此扩展号已存在"));
            } else {
              callback();
            }
          }
        );
      }
    };
    var sign = (rule, value, callback) => {
      if (value == "") {
        callback();
      } else {
        if (value[0] != "【" || value[value.length - 1] != "】") {
          callback(new Error("请正确填写签名格式【签名】"));
        } else if (value.length > 22) {
          callback(new Error("签名最多20位"));
        } else if (
          /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value) ||
          /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
        ) {
          callback(new Error("不能填写特殊字符"));
        } else {
          callback();
        }
      }
    };
    // var splitRatio=(rule,value,callback)=>{
    //      if(value==""){
    //         callback()
    //      }else{
    //          if(!/^(?:[1-9]?\d|100)$/.test(value)){
    //             callback(new Error('请填写0-100整数'))
    //          }else{
    //             callback()
    //          }
    //      }
    // };
    return {
      isFirstEnter: false,
      jsonData: {
        consumerName: "",
        compName: "",
      }, //用户资料
      activeName: "channnelRef",
      secretKeyShow: false, //判断是否加密
      secretIvShow: false,
      smsIsContentSingleflag: "2",
      addUserStep2Form: {
        // formData5g: {
        //   userId: "",
        //   payMethod:null,//付费方式
        //   chargeback:null,//扣费方式
        // },
        //弹窗步骤2的数据和验证
        formData: {
          userId: "",
          payMethod: null, //付费方式
          chargeback: null, //扣费方式
          isRepliesBlack: null, //回复是否自动加黑
          isUserBlack: null, //是否启用黑号检测
          overrunMarket: "", //1天防轰炸设置
          overrunTotalMarket: "", //7天营销发送限制
          repliesBlackWords: "", //回复黑词
          userBlackLevel: [], //黑号验证级别
          ext: "", //扩展号
          reportLevel: "1",
          repliesUrl: "", //上行推送
          reportUrl: "", //下行推送
          dailyLimited: "", //每日限量
          //   smsPayMethod: "1",
          //   smsPrice: "",
          //   smsProductId: "",
          //   yzmProductId: "",
          //   smsChargeback: "1",
          //   yxProductId: "",
          //   // flag: '5',
          //   ext: "",

          //   smsRepliesUrl: "",
          //   smsReportUrl: "",
          //   smsRepliesPackageSize: "",
          //   smsStrepliesPackageSize: "",
          //   smsTemplateUrl: "",
          //   smsSignatureUrl: "",
          //   // 防轰炸
          //   overrunCode: "0",
          //   overrunIndustry: "0",
          //   overrunMarket: "0",
          //   overrunTotalMarket: "0",
          //   smsIsContentSingle: false,
          //   // smsSelect:'',
          //   businessTypes: "1",
          //   interfacePassword: "******", // 接口密码
          //   loginPassWord: "******", // 账号密码
          //   // ifExt: '1', // 生成扩展码：1，手动输入扩展码：2
          //   custom: "1", //是否显示自定义发送operatinguse
          //   isIndividualization: "2", //是否个性化设置
          //   smsIsOvershoot: "0", //预付费是否允许超发
          //   smsIsAudit: "1", //是否审核
          //   sign: "",
          //   smsAuditNum: 1, //审核号码数
          //   smsIsRepliesBlack: "2", //是否加黑
          //   smsRepliesBlackWords: "TD,t,T,td", //加黑关键词
          //   smsIsUserBlack: "1", //是否验证黑号
          //   smsIsExternalBlack:"2",
          //   smsIsPlatformBlack:"",
          //   smsUserBlackLevel1: ["1", "2", "3"], //黑号验证级别存储
          //   smsUserBlackLevel: "", //黑号验证级别存储
          //   isNumberPortability: "0", //携号转网
          //   encryptType: "", //加密方式
          //   secretKey: "", //秘钥
          //   secretIv: "", //偏移量
          //   mobileMask: "1", //掩码展示
          //   smsIsBlackWords: "1", //黑词是否审核
          //   reportTarget: "1", //回执推送账户
          //   // splitDb:'1',
          //   balanceTarget: "1", //扣费账户
          //   smsPushType: "2", //推送方式
          //   signSetting: "1", //短信签名位置
          //   reportPushSetting: "0", //回执推送
          //   replyPushSetting: "0", //回复推送
          //   reportCrawlSetting: "0", //回执抓取
          //   replyCrawlSetting: "0", //回复抓取
          //   smsIsSignatureExtension: "",
          // splitRatio:'',
          // provinceRatio:'',
          // remark:'', //备注
        },
        formRule: {
          // sign: [{ required: false, validator: sign, trigger: "change" }],
          smsPayMethod: [
            { required: true, message: "请选择付费方式", trigger: "change" },
          ],
          payMethod: [
            { required: true, message: "请选择付费方式", trigger: "change" },
          ],
          chargeback: [
            { required: true, message: "请选择扣费方式", trigger: "change" },
          ],
          isRepliesBlack: [
            { required: true, message: "请选择是否加黑", trigger: "blur" },
          ],
          // repliesUrl: [
          //   {
          //    required: true,
          //     message: "请输入上行地址",
          //     trigger: "blur",
          //   }
          // ],
          isUserBlack: [
            {
              required: true,
              message: "请选择是否验证黑号",
              trigger: "change",
            },
          ],
          userBlackLevel: [
            {
              required: true,
              message: "请选择黑号验证级别",
              trigger: "change",
            },
          ],
          // ext: [
          //   {
          //     required: true,
          //     message: "扩展号不能为空",
          //     trigger: "change",
          //   },
          // ],
          smsReportUrl: [
            {
              required: false,
              message: "请输入下行地址",
              trigger: ["change", "blur"],
            },
            {
              min: 1,
              max: 255,
              message: "长度在 255 个字符以内",
              trigger: ["blur", "change"],
            },
          ],
        },
      },
      channel: [], //通道
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs +
          "operatinguser/" +
          this.addUserStep2Form.formData.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName;
          this.jsonData.compName = res.data.compName;
        }
      );
    },
    change(val) {
      if (val == 1) {
        this.addUserStep2Form.formData.smsIsContentSingle = true;
      } else {
        this.addUserStep2Form.formData.smsIsContentSingle = false;
      }
    },
    /**获取通道列表 */
    getChannel() {
      window.api.get(
        window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup",
        {},
        (res) => {
          this.channel = res.data;
        }
      );
    },
    //返回
    goBack() {
      // this.$router.push({
      //   path: "UserDetail",
      //   query: { id: this.$route.query.id, ids: this.$route.query.ids },
      // });
      this.$router.push({
        path: "UserDetail",
        query: { id: this.$route.query.id },
      });
    },
    //编辑的赋值
    handleEdit() {
      window.api.get(
        window.path.fiveWeb + "/usersetting/" + this.$route.query.id,
        {},
        (res) => {
          this.addUserStep2Form.formData = res.data;
          this.addUserStep2Form.formData.userBlackLevel =
            res.data.userBlackLevel.split(",");
          if (res.data.reportLevel) {
            this.addUserStep2Form.formData.reportLevel =
              res.data.reportLevel + "";
          } else {
            this.addUserStep2Form.formData.reportLevel == "1";
          }

          // console.log(this.addUserStep2Form.formData,'this.addUserStep2Form.formData');
          //   for (let i in res.data) {
          //     if (
          //       !(
          //         res.data[i] == null ||
          //         i == "smsProductId" ||
          //         i == "yzmProductId" ||
          //         i == "yxProductId" ||
          //         i == "smsUserBlackLevel"
          //       )
          //     ) {
          //       res.data[i] += "";
          //     }
          //   }
          //   res.data["smsUserBlackLevel1"] =
          //     res.data.smsUserBlackLevel.split(",");
          //   this.addUserStep2Form.formData = res.data;
          //   if(res.data.smsIsExternalBlack){
          //     this.addUserStep2Form.formData.smsIsExternalBlack = res.data.smsIsExternalBlack
          //   }else{
          //     this.addUserStep2Form.formData.smsIsExternalBlack="2"
          //   }
          //   if (res.data.smsIsContentSingle == "false") {
          //     this.smsIsContentSingleflag = "2";
          //   } else {
          //     this.smsIsContentSingleflag = "1";
          //   }
          //   if (res.data.smsRepliesBlackWords == null) {
          //     this.addUserStep2Form.formData.smsRepliesBlackWords = "TD,t,T,td";
          //   }
        }
      );
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          this.addUserStep2Form.formData.userBlackLevel =
            this.addUserStep2Form.formData.userBlackLevel.join(",");
          //   if (this.addUserStep2Form.formData.smsReportLevel != 2) {
          //     //消息报告如果不选批量推送时，重置批量推送下面的推送方式，上下行地址
          //     this.addUserStep2Form.formData.smsPushType = "2";
          //     this.addUserStep2Form.formData.smsRepliesUrl = "";
          //     this.addUserStep2Form.formData.smsReportUrl = "";
          //   }
          //   if (this.addUserStep2Form.formData.smsReportLevel == 1) {
          //     this.addUserStep2Form.formData.smsRepliesPackageSize = "";
          //     this.addUserStep2Form.formData.smsStrepliesPackageSize = "";
          //   }
          //   this.addUserStep2Form.formData.smsUserBlackLevel =
          //     this.addUserStep2Form.formData.smsUserBlackLevel1.join(",");
          this.$confirms.confirmation(
            "put",
            "确认修改5G短信配置？",
            window.path.fiveWeb + "/usersetting",
            this.addUserStep2Form.formData,
            (res) => {
              //返回用户管理页面
              this.goBack();
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      //   this.addUserStep2Form.formData5g.userId = this.$route.query.id;
      this.addUserStep2Form.formData.userId = this.$route.query.id;
      this.getChannel(); /**获取通道列表 */
      this.handleEdit();
      this.getBasicData();
    });
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        // this.addUserStep2Form.formData5g.userId = this.$route.query.id;
        this.addUserStep2Form.formData.userId = this.$route.query.id;
        this.getChannel(); /**获取通道列表 */
        this.handleEdit();
        this.getBasicData();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
    // console.log(11111);
  },
  deactivated() {
    this.activeName = "channnelRef";
  },
  watch: {
    "addUserStep2Form.formData.encryptType"(val) {
      //   if (val == 1) {
      //     this.secretKeyShow = true;
      //     this.secretIvShow = false;
      //   } else if (val == 2) {
      //     this.secretKeyShow = true;
      //     this.secretIvShow = true;
      //   } else {
      //     this.secretKeyShow = false;
      //     this.secretIvShow = false;
      //     this.addUserStep2Form.formData.secretKey = "";
      //     this.addUserStep2Form.formData.secretIv = "";
      //   }
    },
  },
};
</script> -->
<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}

.input-w {
  width: 345px !important;
}

.el-radio-group .el-radio {
  margin-right: 0px;
}

.fillet {
  padding: 20px;
}

.input-w-2 {
  width: 200px !important;
}

.input-w-3 {
  width: 200px !important;
}

.input-w-4 {
  width: 190px !important;
}

.input-w-sm {
  width: 126px !important;
}

.input-w-f {
  width: 80px !important;
}

.float-div-1 {
  float: left;
}

.float-div-2 {
  float: left;
}

.float-div-3 {
  float: left;
}

.clear-div {
  clear: both;
}

.red {
  color: red;
}

@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>
<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>