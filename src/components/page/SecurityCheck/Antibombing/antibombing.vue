<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon> 防轰炸设置
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" ref="formInline">
          <el-form-item label="账号" prop="username">
            <el-input v-model="formInline.username" placeholder></el-input>
          </el-form-item>
          <!-- <el-form-item label="转网运营商" prop="forwardOperator">
                            <el-select class="input-w" v-model="formInline.forwardOperator">
                                <el-option label="移动" value="1">
                                </el-option>
                                <el-option label="联通" value="2">
                                </el-option>
                                <el-option label="电信" value="3">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
      </div>
      <div class="Signature-search-fun" style="margin: 10px 0">
        <el-button type="primary" @click="addopt">新增防轰炸</el-button>
      </div>
      <div class="Mail-table">
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->
          <el-table-column label="账号">
            <template v-slot="scope">
              <span>{{ scope.row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="短信类型">
            <template v-slot="scope">
              <span v-if="scope.row.smsType == '1'">验证码</span>
              <span v-else-if="scope.row.smsType == '2'">通知</span>
              <span v-else-if="scope.row.smsType == '3'">营销推广</span>
            </template>
          </el-table-column>

          <el-table-column label="时长类型">
            <template v-slot="scope">
              <span v-if="scope.row.unit == 'MINUTE'">分钟 </span>
              <span v-else-if="scope.row.unit == 'HOUR'">小时</span>
              <span v-else-if="scope.row.unit == 'DAY'">日</span>
              <span v-else-if="scope.row.unit == 'WEEK'">周</span>
              <span v-else-if="scope.row.unit == 'MONTH'">月</span>
            </template>
          </el-table-column>
          <el-table-column label="时长">
            <template v-slot="scope">
              <span>{{ scope.row.unitLength }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发送条数">
            <template v-slot="scope">
              <span>{{ scope.row.num }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人">
            <template v-slot="scope">
              <span>{{ scope.row.createName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">
              <span>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template v-slot="scope">
              <el-button
                type="text"
                style="margin-left: 0px"
                @click="detailsRow(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                type="text"
                style="margin-left: 10px; color: red"
                @click="delState(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="1"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增账单 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        style="padding: 0 28px 0 20px"
        width="520px"
      >
        <el-form
          label-width="110px"
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="账号" prop="username">
            <el-input
              :disabled="id == '' ? false : true"
              class="input-w"
              v-model="formop.username"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="短信类型" prop="smsType">
            <el-select
              :disabled="id == '' ? false : true"
              class="input-w"
              v-model="formop.smsType"
            >
              <el-option label="验证码" value="1"> </el-option>
              <el-option label="通知" value="2"> </el-option>
              <el-option label="营销推广" value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间单位" prop="unit">
            <el-select
              :disabled="id == '' ? false : true"
              class="input-w"
              v-model="formop.unit"
            >
              <el-option label="分钟" value="MINUTE"> </el-option>
              <el-option label="小时" value="HOUR"> </el-option>
              <el-option label="天" value="DAY"> </el-option>
              <el-option label="周" value="WEEK"> </el-option>
              <el-option label="月" value="MONTH"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时长" prop="unitLength">
            <el-input
              class="input-w"
              v-model="formop.unitLength"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="限制条数" prop="num">
            <el-input
              class="input-w"
              v-model="formop.num"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  components: {
    ElIconLxEmoji,
  },
  name: 'crawlerLibrary',
  data() {
    return {
      isFirstEnter: false,
      titleMap: {
        add: '添加防轰炸设置',
        edit: '编辑防轰炸设置',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        username: '',
        // ip: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        // ip: "",
        currentPage: 1,
        pageSize: 10,
      },
      options: [],
      //添加列表
      formop: {
        username: '', //账号
        smsType: '', //短信类型1:验证码；2：通知；3；营销推广
        num: '', //发送量
        unit: '', //颗粒度 小时、天、周、月
        unitLength: '', //时长
      },
      id: '', //列表行id
      rules: {
        username: [
          {
            required: true,
            message: '账号不能为空',
            trigger: 'change',
          },
        ],
        smsType: [
          {
            required: true,
            message: '请选择短信类型',
            trigger: 'change',
          },
        ],
        num: [
          {
            required: true,
            message: '发送量不能为空',
            trigger: 'change',
          },
        ],
        unit: [
          {
            required: true,
            message: '请选择时长类型',
            trigger: 'change',
          },
        ],
        unitLength: [
          {
            required: true,
            message: '时长不能为空',
            trigger: 'change',
          },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerclientoverrun/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handedTime(e) {
      this.formop.startTime = moment(e).format('YYYY-MM-DD hh:mm:ss')
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.omcs + 'consumerclientoverrun',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.omcs + 'consumerclientoverrun',
              this.formop,
              (res) => {
                // if (res.code == 400) {
                //     this.$message({
                //         message: res.msg,
                //         type: "warning",
                //     });
                // } else {
                //     this.$message({
                //         message: res.msg,
                //         type: "success",
                //     });
                // }
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },

    detailsRow(index, val) {
      // console.log(val);
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
        this.formop.smsType = this.formop.smsType + ''
      })
      // this.formop.status = val.status+""
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确认执行此操作吗？',
        window.path.omcs + 'consumerclientoverrun?ids=' + val.id,
        {},
        (res) => {
          // if (res.code == 400) {
          //     this.$message({
          //         message: res.msg,
          //         type: "warning",
          //     });
          // } else {
          //     this.$message({
          //         message: res.msg,
          //         type: "success",
          //     });
          // }
          this.gettableLIst()
        }
      )
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.id = ''
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.input-w {
  width: 300px;
}
</style>
