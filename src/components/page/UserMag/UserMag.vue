<template>
  <div style="background: #fff; padding: 15px">
    <!-- <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon> 用户管理</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div> -->
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="100px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="queryForm"
        >
          <el-form-item label="用户类型" prop="roleId">
            <el-select
              v-model="formInline.roleId"
              @keyup.enter="Query()"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="不限" value=""></el-option>
              <el-option label="管理商" value="12"></el-option>
              <el-option label="子用户" value="13"></el-option>
              <el-option label="终端" value="14"></el-option>
              <el-option label="线上终端" value="22"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名" prop="consumerName">
            <el-input
              v-model="formInline.consumerName"
              @keyup.enter="Query()"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名" prop="compName">
            <el-input
              v-model="formInline.compName"
              @keyup.enter="Query()"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="扩展号" prop="ext">
            <el-input
              v-model="formInline.ext"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="账号状态" prop="consumerStatus">
            <el-select
              v-model="formInline.consumerStatus"
              @keyup.enter="Query()"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="启用" value="0"></el-option>
              <el-option label="停用" value="1"></el-option>
              <el-option label="冻结" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属客服" prop="service">
            <el-select
              v-model="formInline.service"
              @keyup.enter="Query()"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in services"
                :label="item.realName"
                :value="item.userId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="所属销售" prop="salesName">
                            <el-input v-model="formInline.salesName"  placeholder="" class="input-w"></el-input><div class=""></div>
                        </el-form-item> -->
          <!-- <el-form-item label="扣费方式" prop="salesName">
                            <el-select v-model="formInline.smsChargeback" clearable placeholder="不限"  class="input-w">
                                <el-option label="不限" value="0"></el-option>
                                <el-option label="提交计费" value="1"></el-option>
                                <el-option label="成功计费" value="2"></el-option>
                            </el-select>
                        </el-form-item> -->
          <el-form-item label="付费方式" prop="smsPayMethod">
            <el-select
              v-model="formInline.smsPayMethod"
              @keyup.enter="Query()"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="不限" value="0"></el-option>
              <el-option label="预付费" value="1"></el-option>
              <el-option label="后付费" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="认证状态" prop="certificate">
            <el-select
              v-model="formInline.certificate"
              @keyup.enter="Query()"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="未认证" value="0"></el-option>
              <el-option label="认证中" value="1"></el-option>
              <el-option label="已认证" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="合作状态"
            @keyup.enter="Query()"
            prop="cooperateStatus"
          >
            <el-select
              v-model="formInline.cooperateStatus"
              @keyup.enter="Query()"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="测试阶段" value="TEST"></el-option>
              <el-option label="正式合作" value="NORMAL"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="运营商下发"
            @keyup.enter="Query()"
            prop="netType"
          >
            <el-select
              v-model="formInline.netType"
              @keyup.enter="Query()"
              clearable
              placeholder="请选择单/多网"
              class="input-w"
            >
              <el-option label="单网" value="1"></el-option>
              <el-option label="多网" value="2"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="所属销售" prop="sales">
                             <el-select v-model="formInline.sales" clearable placeholder="不限"  class="input-w">
                                <el-option v-for="(item,index) in sales" :label="item.username" :value="item.userId" :key="index"></el-option>
                            </el-select>
                        </el-form-item> -->

          <el-form-item label="创建人" prop="createName">
            <el-input
              v-model="formInline.createName"
              @keyup.enter="Query()"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="time">
            <el-date-picker
              class="input-time"
              v-model="formInline.time"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handeTime"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="Query"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reload('queryForm')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table Mail-table1">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button
              @click="createUser"
              type="primary"
              v-if="
                this.$store.state.roleId == 3 ||
                this.$store.state.roleId == 4 ||
                this.$store.state.roleId == 6 ||
                this.$store.state.roleId == 19
              "
              >创建用户</el-button
            >
            <el-button
              v-show="selecteds.length >= 1"
              @click="muchStart"
              type="primary"
              v-if="this.$store.state.roleId == 3 || this.$store.state.roleId == 19"
              >批量启用</el-button
            >
            <el-button
              v-show="selecteds.length >= 1"
              @click="muchStop"
              type="warning"
              v-if="this.$store.state.roleId == 3 || this.$store.state.roleId == 19"
              >批量停用</el-button
            >
            <el-button @click="muchTag" type="primary">批量导入标签</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="UserMag"
          border
          stripe
          show-header-overflow
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称" width="270">
            <!-- 点击用户名 -->
            <template #default="scope">
              <div>
                <!-- <el-tooltip effect="dark" placement="top">
                      <div slot="content">
                        <span v-if="scope.row.roleId == '14'">终端用户</span>
                        <span v-if="scope.row.roleId == '12'">管理商</span>
                        <span v-if="scope.row.roleId == '13'">子用户</span>
                        <span v-if="scope.row.roleId == '22'">线上终端</span>
                        <span v-if="scope.row.certificate == 0">(未认证)</span>
                        <span v-if="scope.row.certificate == 1">(认证中)</span>
                        <span v-if="scope.row.certificate == 2">(已认证)</span>
                      </div>
                  
                    </el-tooltip> -->
                <div class="wrapper-text">
                  <i
                    v-if="scope.row.roleId == '14'"
                    class="iconfont icon-zhong"
                    style="font-size: 18px; color: #23227e; cursor: pointer"
                  ></i>
                  <i
                    v-if="scope.row.roleId == '12'"
                    class="iconfont icon-guan"
                    style="font-size: 18px; color: #812c7a; cursor: pointer"
                  ></i>
                  <i
                    v-if="scope.row.roleId == '13'"
                    class="iconfont icon-zimaliu"
                    style="font-size: 18px; color: #812c7a; cursor: pointer"
                  ></i>
                  <i
                    v-if="scope.row.roleId == '22'"
                    class="iconfont icon-xianshang"
                    style="font-size: 18px; color: #3a6b1e; cursor: pointer"
                  ></i>
                  <i
                    v-if="scope.row.certificate == 0"
                    class="iconfont icon-renzhengyonghu"
                    style="font-size: 18px; cursor: pointer; margin: 0 10px"
                  ></i>
                  <i
                    v-if="scope.row.certificate == 1"
                    class="iconfont icon-renzhengyonghu"
                    style="
                      font-size: 18px;
                      cursor: pointer;
                      color: #e6a23c;
                      margin: 0 10px;
                    "
                  ></i>
                  <i
                    v-if="scope.row.certificate == 2"
                    class="iconfont icon-renzhengyonghu"
                    style="
                      font-size: 18px;
                      cursor: pointer;
                      color: #16a589;
                      margin: 0 10px;
                    "
                  ></i>
                  <span
                    style="color: #409eff; cursor: pointer"
                    @click="
                      linkTo(
                        scope.row.userId,
                        scope.row.roleId,
                        scope.row.consumerName
                      )
                    "
                  >
                    {{ scope.row.consumerName }}
                  </span>
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                        @click="handleCopy(scope.row.consumerName, $event)"></i> -->
                  <CopyTemp :content="scope.row.consumerName" />
                </div>
                <div class="wrapper-text" v-if="scope.row.tagName">
                  <el-tag
:disable-transitions="true"
                    style="margin: 5px"
                    v-for="(item, index) in scope.row.tagName.split(',')"
                    :key="index + 'a'"
                    type="primary"
                    effect="dark"
                  >
                    {{ item }}
                  </el-tag>
                </div>
                <!-- <div
                      @click="linkTo(scope.row.userId, scope.row.roleId,scope.row.consumerName)"
                      style="color: #16a589; cursor: pointer"
                    >
                      {{ scope.row.consumerName }}
                    </div> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="公司名称" title="公司名称" width="180">
            <template #default="scope">
              <div>{{ scope.row.compName }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="付费方式" title="付费方式" width="90">
                <template #default="scope">
                  <span v-if="scope.row.smsPayMethod == 1">预付费</span>
                  <span v-else-if="scope.row.smsPayMethod == 2">后付费</span>
                </template>
              </vxe-column> -->
          <vxe-column field="余额" title="余额" width="200">
            <template #default="scope">
              <div style="display: flex">
                <div
                  v-for="(item, index) in scope.row.balanceList"
                  :key="index"
                  @click="more(scope.row)"
                >
                  <el-tooltip effect="dark" placement="top-end">
                    <template v-slot:content>
                      <div>
                        <span>{{
                          item.status == 0
                            ? '开启'
                            : item.status == 1
                            ? '用户关闭'
                            : '运营关闭'
                        }}</span>
                      </div>
                    </template>
                    <div>
                      <el-tag
:disable-transitions="true"
                        v-if="item.name == '短信'"
                        :style="'margin: 5px 0px 5px 10px;cursor: pointer;'"
                        :type="item.status == 0 ? 'success' : 'danger'"
                      >
                        {{ item.name }}
                        {{ item.payMethod == 1 ? '（预付）' : '(后付)' }} :
                        {{ item.num }}
                      </el-tag>
                    </div>
                  </el-tooltip>
                </div>
                <!-- <span style="margin: 5px 0px 5px 10px;color:#67c23a;cursor: pointer;"  @click="more(scope.row)">查看更多</span> -->
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column
                    field="扣费方式" title="扣费方式">
                        <template #default="scope" >
                            <span v-if="scope.row.smsChargeback==1">提交计费</span>
                            <span v-else-if="scope.row.smsChargeback==2">成功计费</span>
                        </template>
                    </vxe-column> -->
          <vxe-column field="账号状态" title="账号状态" width="90">
            <template #default="scope">
              <el-tag
:disable-transitions="true"
                v-if="scope.row.consumerStatus == 0"
                type="success"
                effect="dark"
              >
                启用
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.consumerStatus == 1"
                type="danger"
                effect="dark"
              >
                停用
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.consumerStatus == 2"
                type="danger"
                effect="dark"
              >
                冻结
              </el-tag>
            </template>
          </vxe-column>
          <vxe-column field="运营商下发" title="运营商下发" width="90">
            <template #default="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.netType == 1" type=""> 单网 </el-tag>
              <el-tag
:disable-transitions="true" v-else-if="scope.row.netType == 2" type="warning">
                多网
              </el-tag>
            </template>
          </vxe-column>
          <!-- <vxe-column
                    prop="remark"
                    field="备注" title="备注"
                    >
                    </vxe-column> -->
          <!-- <vxe-column field="用户类型/所属管理商" title="用户类型/所属管理商" width="180">
                <template #default="scope">
                  <div v-if="scope.row.roleId == 12">
                    <div>管理商</div>
                    <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                    <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                    <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
                  </div>
                  <div v-else-if="scope.row.roleId == 13">
                    <div>子用户（{{ scope.row.manageName }}）</div>
                    <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                    <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                    <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
                  </div>
                  <div v-else-if="scope.row.roleId == 14">
                    <div>终端</div>
                    <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                    <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                    <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
                  </div>
                  <div v-else-if="scope.row.roleId == 22">
                    <div>线上终端</div>
                    <span style="color: red" v-if="scope.row.certificate == 0">(未认证)</span>
                    <span style="color: red" v-if="scope.row.certificate == 1">(认证中)</span>
                    <span style="color: #16a589" v-if="scope.row.certificate == 2">(已认证)</span>
                  </div>
                </template>
              </vxe-column> -->
          <vxe-column field="合作状态" title="合作状态" width="90">
            <template #default="scope">
              <div v-if="scope.row.cooperateStatus == 'TEST'">测试阶段</div>
              <div v-else-if="scope.row.cooperateStatus == 'NORMAL'"
                >正式合作</div
              >
            </template>
          </vxe-column>
          <!-- <vxe-column  
                    field="所属通道组" title="所属通道组">  
                    <template #default="scope">
                        <span>{{scope.row.smsProductName}}</span>
                    </template>
                    </vxe-column> -->

          <!-- <vxe-column field="标签" title="标签" width="180">
                <template #default="scope">
                  <div class="wrapper-text" v-if="scope.row.tagName">
                    <el-tag
:disable-transitions="true" style="margin: 5px;" v-for="(item, index) in scope.row.tagName.split(',')" :key="index + 'a'" type=""
                      effect="dark">
                      {{ item }}
                    </el-tag>
                  </div>
                </template>
              </vxe-column> -->
          <vxe-column field="创建人" title="创建人" width="110">
            <template #default="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="100">
            <template #default="scope">
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="210">
            <template #default="scope">
              <div
                v-if="scope.row.remark"
                @click="editRemark(scope.row)"
                style="color: #409eff; cursor: pointer"
              >
                <!-- <Tooltip v-if="scope.row.remark" :content="scope.row.remark" className="multiline-text" widthpx="400px"
                    effect="light"></Tooltip> -->
                <span>{{ scope.row.remark }}</span>
              </div>
              <div
                v-else
                @click="editRemark(scope.row)"
                style="color: #409eff; cursor: pointer"
              >
                <span>(添加备注)</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="所属客服" title="所属客服" width="120">
            <template #default="scope">
              <div>{{ scope.row.serviceName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button
                style="margin-left: 5px"
                v-if="roleId == 3|| roleId == 4  || roleId == 6 || roleId == 7 || roleId == 19"
                link
                type="primary"
                @click="handleEdit(scope.$index, scope.row)"
                >
                <!-- <el-icon class="mr-2"><el-icon-edit /></el-icon> -->
                <el-icon><EditPen /></el-icon>编辑</el-button
              >
              <!-- <el-button v-if="$store.state.roleId!=13 && scope.row.roleId!=13"  link style="margin-left:0px !important;" @click="handleAdd(scope.$index, scope.row)"><i class="el-icon-edit mr-2"></i>添加产品</el-button> -->
              <el-button
                v-if="
                  scope.row.consumerStatus == 0 && (roleId == 3|| roleId == 4  || roleId == 19)
                "
                style="color: red; margin-left: 5px"
                link
                @click="handleStop(scope.$index, scope.row)"
                >
                <!-- <el-icon class="mr-2"><el-icon-remove-outline /></el-icon> -->
                <el-icon><Remove /></el-icon>停用</el-button
              >
              <!-- <template v-if="scope.row.consumerStatus == 1 && (roleId == 3 || roleId == 19)
                    ">
                    <el-button style="color: #16a589" link @click="handleStart(scope.$index, scope.row)"><i
                        class="el-icon-circle-plus-outline mr-2"></i>启用</el-button>
                  </template> -->
              <el-button
                v-if="scope.row.isDateState == 2"
                style="margin-left: 5px"
                link
                type="primary"
                @click="handleOpen(scope.$index, scope.row)"
                >
                <!-- <el-icon class="mr-2"><el-icon-circle-plus-outline /></el-icon> -->
                <el-icon><CirclePlus /></el-icon>数据开启</el-button
              >
              <el-button
                v-if="scope.row.isDateState == 1"
                style="color: orange; margin-left: 5px"
                link
                @click="handleClose(scope.$index, scope.row)"
                >
                <!-- <el-icon class="mr-2"><el-icon-remove-outline /></el-icon> -->
                <el-icon><Remove /></el-icon>数据关闭</el-button
              >
              <!-- <el-button link @click="setPhone(scope.row)">
                    <i class="el-icon-s-tools"></i>
                    设置登录手机号
                  </el-button> -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <el-pagination
              style="float: right"
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="searchInput.currentPage"
              :page-size="searchInput.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalSize"
            >
            </el-pagination>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0"
          >
            
          </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- <el-dialog title="修改通道组" width="30%" v-model="dialogFormVisible">
                <el-form :model="smsForm" :rules="smsFormRules" ref="smsForm" label-width="140px">
                    <el-form-item label="当前通道组：" prop="smsProductName">
                        <el-input class="input-w-2" v-model="smsForm.smsProductName" :readonly="true"></el-input>
                    </el-form-item>
                    <el-form-item label="通道组选择：" prop="smsProductId">
                        <el-select clearable class="input-w-2" v-model="smsForm.smsProductId" filterable placeholder="请选择通道名称">
                            <el-option v-for="(item,index) in channel" :label="item.channelGroupName" :value="item.channelGroupId" :key="index"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button type="primary" @click="smsProducUpdate('smsForm')">提 交</el-button>
                    <el-button @click="dialogFormVisible = false">取 消</el-button>
                </template>
            </el-dialog> -->
      <!-- 添加产品 -->
      <el-dialog
        title="添加产品类型"
        v-model="moreInfoDialog"
        width="560px"
        class="moreInfoDialog pro-style"
        :close-on-click-modal="false"
      >
        <el-form
          :model="moreInfoForm.formData"
          :rules="moreInfoForm.formRule2"
          ref="moreInfoForm"
          label-width="120px"
        >
          <el-form-item label="开通产品类型" prop="checked">
            <el-checkbox-group v-model="moreInfoForm.formData.checked">
              <el-checkbox label="5">空号检测</el-checkbox>
              <el-checkbox label="2" disabled>语音</el-checkbox>
              <el-checkbox label="3" disabled>彩信</el-checkbox>
              <el-checkbox label="4" disabled>国际短信</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div
            style="
              font-size: 12px;
              color: #f56c6c;
              padding: 0 0 20px 15px;
              text-align: right;
            "
          >
            （ 注：彩信、语音、国际短信等功能暂未上线）
          </div>
          <div style="text-align: right">
            <el-button @click="cancelForms('moreInfoForm')">取消</el-button>
            <el-button type="primary" @click="submitForms('moreInfoForm')"
              >确定</el-button
            >
          </div>
        </el-form>
      </el-dialog>
      <el-dialog
        :title="usreTitle"
        v-model="moreFlag"
        custom-class="way"
        width="30%"
      >
        <div>
          <div style="font-size: 18px; color: #000; margin: 10px">余额</div>
          <div v-for="(item, index) in balanceLists" :key="index">
            <div style="display: flex">
              <el-tag
:disable-transitions="true"
                :style="'margin: 5px 0px 5px 10px;color:' + tagColor[index]"
                >{{ item.name }} : {{ item.num }}
                {{
                  item.status == 0
                    ? '（开启）'
                    : item.status == 1
                    ? '（用户关闭）'
                    : '（运营关闭）'
                }}
              </el-tag>
              <div v-if="item.payMethod">
                <el-tag
:disable-transitions="true"
                  type="info"
                  style="margin: 5px 0px 5px 10px; color: #409eff"
                  v-if="item.payMethod == 1"
                >
                  预付费
                </el-tag>
                <el-tag
:disable-transitions="true"
                  type="info"
                  style="margin: 5px 0px 5px 10px"
                  v-else-if="item.payMethod == 2"
                >
                  后付费
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-dialog>
      <el-dialog
        title="修改备注"
        v-model="remarkVisible"
        width="500px"
        :before-close="handleCloseRemark"
      >
        <el-form
          :model="remarForm"
          :rules="remarRules"
          ref="remarForm"
          label-width="90px"
        >
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="remarForm.remark"
              type="textarea"
              placeholder=""
              style="width: 300px; height: 100px"
              maxlength="200"
              show-word-limit
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="操作人" prop="updateName">
                <el-input
                  v-model="remarForm.updateName"
                  placeholder=""
                  class="input-w"
                ></el-input>
              </el-form-item> -->
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="remarkVisible = false">取 消</el-button>
            <el-button type="primary" @click="handremark('remarForm')"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog
        :title="stopTitle"
        v-model="muchStopVisible"
        width="30%"
        :before-close="handleCloseRemark"
      >
        <el-form
          ref="formLabelAlign"
          label-width="80px"
          :model="formLabelAlign"
          :rules="remarRules"
        >
          <el-form-item label="备注" prop="">
            <el-input v-model="formLabelAlign.remark"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="muchStopVisible = false">取 消</el-button>
            <el-button type="primary" @click="clickStop('formLabelAlign')"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <el-dialog
        title="批量导入标签"
        v-model="tagVisible"
        :close-on-click-modal="false"
        width="40%"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="标签名称" prop="tagName">
            <input-tag
              v-on:childLabelEvent="handChildLabel"
              :t_data="tagList"
            />
          </el-form-item>
          <el-form-item label="用户名称 " prop="username">
            <el-input
              v-model="formop.username"
              class="input-w"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitFortag('formop')"
              >提 交</el-button
            >
            <el-button @click="tagVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import inputTag from '@/components/publicComponents/InputTag.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
// import clip from '../../../utils/clipboard'
// 引入时间戳转换
// import {formatDate} from '@/assets/js/date.js'
// var axios = require('axios')
export default {
  components: {
    TableTem,
    FileUpload,
    inputTag,
    Tooltip,
    CopyTemp,
    // ElIconLxEmoji,
    // ElIconEdit,
    // ElIconRemoveOutline,
    // ElIconCirclePlusOutline,
  },
  name: 'UserMag',
  data() {
    var checkStatus = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择状态'))
      } else {
        this.formInlines.parentId = this.checkuserClientId
        let selected = []
        selected.push(parseInt(this.addUserStep1Form.formData.userId))

        if (this.formInlines.parentId == 0) {
          callback()
        } else {
          window.api.post(
            window.path.omcs + 'operatinguser/checkCanBeDisabled',
            { userIds: selected },
            (res) => {
              if (res.data != '0') {
                if (this.addUserStep1Form.formData.consumerStatus == 1) {
                  return callback(
                    new Error('该管理商用户名下仍有子用户正在使用，无法停用!')
                  )
                } else {
                  callback()
                }
              } else {
                callback()
              }
            }
          )
        }
      }
    }
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      remarkVisible: false,
      tagVisible: false,
      muchStopVisible: false,
      stopTitle: '',
      formLabelAlign: {
        remark: '',
      },
      formop: {
        username: '',
        tagName: '',
        remark: '',
      },
      tagList: [],
      remarForm: {
        remark: '',
        // updateName: "",
        userId: '',
      },
      stopUserId: '',
      rules: {
        tagName: [{ required: true, message: '请添加标签', trigger: 'change' }],
        username: [
          { required: true, message: '用户名不能为空', trigger: 'change' },
        ],
      },
      remarRules: {
        // remark: [
        //   { required: true, message: "备注不能为空", trigger: "change" },
        // ],
        // updateName: [
        //   { required: true, message: "操作人不能为空", trigger: "change" },
        // ],
      },
      tagColor: [
        '#67c23a',
        '#e6a23c',
        '#ff0000b3',
        '#909399',
        '#3c61e6d1',
        '#3cafe6bf',
        '#16A589',
        '#303133',
      ],
      isDateState: 2, //数据是否开启
      checkuserClientId: 0,
      moreFlag: false,
      usreTitle: '',
      balanceLists: [],
      services: [], //客服
      // sales:[],//销售
      salesName: [], //销售
      // channel:[],//通道
      // dialogFormVisible: false, // 修改所属通道组弹出框
      // 判断管理商停用时子用户是否开启
      formInlines: {
        userName: '',
        compName: '',
        createName: '',
        userStatus: '3',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
        certificate: '',
      },
      formInline: {
        roleId: '',
        consumerName: '',
        compName: '',
        consumerStatus: '',
        service: '',
        ext: '',
        salesName: '',
        createName: '',
        smsChargeback: '',
        smsPayMethod: '',
        time: '',
        createBeginTime: '',
        createEndTime: '',
        certificate: '',
        cooperateStatus: '',
        netType: '',
        pageSize: 10,
        currentPage: 1,
      },
      searchInput: {
        roleId: '',
        consumerName: '',
        compName: '',
        consumerStatus: '',
        service: '',
        // sales:'',
        ext: '',
        salesName: '',
        createName: '',
        smsChargeback: '',
        smsPayMethod: '',
        time: '',
        createBeginTime: '',
        createEndTime: '',
        netType: '',
        pageSize: 10,
        currentPage: 1,
      },
      moreInfoDialog: false, //添加产品弹窗
      moreInfoForm: {
        formData: {
          checked: [], //添加产品复选框
        },
        formRule2: {
          checked: [
            { required: true, message: '请选择产品类型', trigger: 'change' },
          ],
        },
      },
      hasSelectBox: '', //复选框选择的内容
      totalSize: 0, //总条数
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      selected: [],
      selecteds: [],
      unsernameList: [],
      // smsForm: { // 通道组修改表单
      //     smsProductName: '',
      //     smsProductId: '',
      //     userId:'',
      // },
      // smsFormRules: {
      //     smsProductId: [
      //         {required: true, message: '通道名称不得为空！', trigger: 'change'}
      //     ]
      // }
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  watch: {
    tagVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
        // this.formop.username = ''
        this.formop.tagName = ''
        this.formop.remark = ''
        this.formop.username = this.unsernameList.join(',')
        this.tagList = []
      }
    },
    muchStopVisible(val) {
      if (!val) {
        this.formLabelAlign.remark = ''
        this.stopUserId = ''
      }
    },
    moreInfoDialog(val) {
      //产品弹框关闭
      if (val == false) {
        for (let k in this.moreInfoForm.formData) {
          this.moreInfoForm.formData[k] = ''
        }
      }
    },
    remarkVisible(val) {
      if (!val) {
        this.remarForm.userId = ''
        // this.$refs["remarForm"].resetFields();
        this.remarForm.remark = ''
      }
    },
    'addUserStep2Form.formData.smsIsAudit': function (val) {
      if (val != 1) {
        this.addUserStep2Form.formData.smsAuditNum = ''
      }
    },
    searchInput: {
      //监听查询条件
      handler() {
        this.getTableData() //刷新表格
      },
      deep: true,
    },
  },
  methods: {
    //数据开启
    handleOpen(index, row) {
      this.$confirms.confirmation(
        'put',
        '开启后该用户将展示成功量、失败率、待返回、失败数、待返回率、成功率数据。',
        window.path.omcs + 'operatinguser/updateIsDateState',
        {
          smsId: row.smsId,
          isDateState: 1,
        },
        (res) => {
          this.getTableData()
        }
      )
    },
    //数据关闭
    handleClose(index, row) {
      this.$confirms.confirmation(
        'put',
        '关闭后该用户将不在展示成功量、失败率、待返回、失败数、待返回率、成功率数据。',
        window.path.omcs + 'operatinguser/updateIsDateState',
        {
          smsId: row.smsId,
          isDateState: 2,
        },
        (res) => {
          this.getTableData()
        }
      )
    },
    more(row) {
      // console.log(row,'row');
      this.moreFlag = true
      this.balanceLists = row.balanceList
      this.usreTitle = row.consumerName
    },
    //删除用户
    deleteCurrent(index, row) {
      this.$confirms.confirmation(
        'post',
        '确认要删除当前用户吗？',
        window.path.omcs + 'operatinguser/delete',
        { userIds: [row.userId] },
        (res) => {
          this.getTableData()
        }
      )
    },
    // 限制手机号码的输入格式
    handleClick() {
      this.addUserStep1Form.formData.phone =
        this.addUserStep1Form.formData.phone.replace(/[^\d\,]/g, '')
    },
    handleCloseRemark() {
      this.remarkVisible = false
      this.muchStopVisible = false
    },
    /**添加产品提交 */
    submitForms(val) {
      this.$nextTick(() => {
        this.$refs[val].validate((valid) => {
          if (valid) {
            let a = this.moreInfoForm.formData.checked.join(',')
            this.$confirms.confirmation(
              'get',
              '确定修改更多设置吗？',
              window.path.omcs +
                'operatinguser/addition?businessTypes=' +
                a +
                '&userId=' +
                this.moreInfoForm.formData.userId,
              {},
              (res) => {
                this.moreInfoDialog = false //关闭弹窗
              }
            )
          } else {
            return false
          }
        })
      })
    },
    cancelForms(formName) {
      //取消
      this.moreInfoDialog = false //关闭弹窗
    },
    /**获取客服列表*/
    getServices() {
      window.api.get(
        window.path.upms + 'user/selectAllSalesOrService',
        { flag: 2 },
        (res) => {
          this.services = res.data
        }
      )
    },
    /**获取销售列表 */
    // getSales(){
    //     window.api.get(window.path.upms+'user/selectAllSalesOrService',{flag:1},res=>{
    //         console.log(res)
    //         this.sales=res.data;
    //     })
    // },
    getTableData() {
      //获取列表数据
      let formDatas = this.searchInput
      this.tableDataObj.loading2 = true
      window.api.post(window.path.omcs + 'operatinguser/page', formDatas, (res) => {
        // console.log(res)
        this.tableDataObj.tableData = res.records
        this.totalSize = res.total
        this.tableDataObj.loading2 = false
      })
    },
    Query() {
      if(this.tableDataObj.loading2) return; //防止重复请求
      //查询
      for (let key in this.formInline) {
        this.searchInput[key] = this.formInline[key]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
    },
    Reload(formName) {
      //重置
      this.formInline.createBeginTime = ''
      this.formInline.createEndTime = ''
      this.formInline.cooperateStatus = ''
      this.$refs[formName].resetFields() //清空查询表单
      for (let k in this.formInline) {
        this.searchInput[k] = this.formInline[k]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
    },
    handeTime: function (val) {
      //获取查询时间框的值
      // console.log(val)
      if (val) {
        this.formInline.createBeginTime = this.moment(val[0]).format(
          'YYYY-MM-DD '
        )
        this.formInline.createEndTime = this.moment(val[1]).format(
          'YYYY-MM-DD '
        )
      } else {
        this.formInline.createBeginTime = ''
        this.formInline.createEndTime = ''
      }
    },
    //创建用户
    createUser() {
      this.$router.push({ path: '/userModification', query: { i: '1' } })
    },
    // handleCopy(name, event) {
    //   clip(name, event)
    // },

    //批量导出标签
    muchTag() {
      this.tagVisible = true
    },
    handChildLabel(data) {
      this.formop.tagName = data.join(',')
      // console.log(this.configurationItem.formData.label,'data');
    },
    submitFortag(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '确定新增操作？',
            window.path.omcs + 'consumerClient/tag/batchSave',
            this.formop,
            (res) => {
              if (res.code == 200) {
                this.tagVisible = false
                this.getTableData()
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //批量启用
    muchStart() {
      // let selected = [];
      // this.hasSelectBox.forEach((item, index) => {
      //   selected.push(item.userId);
      // });
      this.$confirms.confirmation(
        'post',
        '确认要批量启用吗？',
        window.path.omcs + 'operatinguser/enable',
        { userIds: this.selecteds },
        (res) => {
          this.getTableData()
        }
      )
    },
    //批量停用
    muchStop() {
      // let selected = [];
      // this.hasSelectBox.forEach((item, index) => {
      //   this.selecteds.push(item.userId);
      // });
      this.muchStopVisible = true
      this.stopTitle = '批量停用'
    },
    allStop() {
      window.api.post(
        window.path.omcs + 'operatinguser/checkCanBeDisabled',
        { userIds: this.selecteds, remark: this.formLabelAlign.remark },
        (res) => {
          if (res.data == '0') {
            this.$confirms.confirmation(
              'post',
              '确认要批量停用吗？',
              window.path.omcs + 'operatinguser/disabledBatch',
              { userIds: this.selecteds, remark: this.formLabelAlign.remark },
              (res) => {
                this.getTableData()
                this.muchStopVisible = false
              }
            )
          } else {
            this.$message({
              message:
                '该管理商用户名下仍有子用户正在使用，无法停用该管理商账号!',
              type: 'warning',
            })
          }
        }
      )
    },
    //复选框选择
    handleSelectionChange(val) {
      // let selectionArr = [];
      // val.forEach(function (el) {
      //   selectionArr.push(el);
      // });
      // this.hasSelectBox = selectionArr;
      this.selecteds = val.records.map((item) => {
        return item.userId
      })
      this.unsernameList = val.records.map((item) => {
        return item.consumerName
      })
      this.formop.username = this.unsernameList.join(',')
    },
    //添加产品
    handleAdd(index, row) {
      window.api.get(
        window.path.omcs + 'operatinguser/' + row.userId,
        {},
        (res) => {
          let userData = res.data
          this.moreInfoForm.formData.checked = userData.businessTypes
          this.moreInfoForm.formData.userId = row.userId
          this.moreInfoDialog = true
        }
      )
    },
    //点击表格操作栏的编辑
    handleEdit(index, row) {
      this.$router.push({
        path: '/userModification',
        query: { d: row.userId },
      })
    },
    //启用
    handleStart(index, row) {
      this.$confirms.confirmation(
        'post',
        '确认要对当前用户启用吗？',
        window.path.omcs + 'operatinguser/enable',
        { userIds: [row.userId] },
        (res) => {
          this.getTableData()
        }
      )
    },
    //停用
    handleStop(index, row) {
      // let selected = [];
      this.selected.push(row.userId)
      this.stopUserId = row.userId
      // console.log(this.selected, 'this.selected');

      this.muchStopVisible = true
      this.stopTitle = '停用'
    },
    stopUser() {
      window.api.post(
        window.path.omcs + 'operatinguser/checkCanBeDisabled',
        { userIds: [this.stopUserId], remark: this.formLabelAlign.remark },
        (res) => {
          if (res.data != '0') {
            this.$message({
              message:
                '该管理商用户名下仍有子用户正在使用，无法停用该管理商账号!',
              type: 'warning',
            })
          } else {
            this.$confirms.confirmation(
              'post',
              '确认要对当前用户停用吗？',
              window.path.omcs + 'operatinguser/disabled',
              {
                userIds: [this.stopUserId],
                remark: this.formLabelAlign.remark,
              },
              (res) => {
                this.getTableData()
                this.muchStopVisible = false
              }
            )
          }
        }
      )
    },
    clickStop(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.stopTitle == '停用') {
            this.stopUser()
          } else {
            this.allStop()
          }
        }
      })
    },
    //点击表格中的用户名--跳转路由
    linkTo(val1, val2, username) {
      this.$router.push({
        path: '/UserDetail',
        query: { id: val1 },
      })
    },
    editRemark(row) {
      console.log(row, 'row')
      this.remarForm.userId = row.userId
      if (row.remark) {
        this.remarForm.remark = row.remark
      }
      this.remarkVisible = true
    },
    handremark(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + 'operatinguser/editRemark',
            this.remarForm,
            (res) => {
              if (res.code == 200) {
                this.remarkVisible = false
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.getTableData()
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        }
      })
    },
    setPhone(row) {
      this.$router.push({
        path: '/settingPhone',
        query: {
          id: row.userId,
        },
      })
    },
    //-----翻页操作
    handleSizeChange(size) {
      this.searchInput.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.searchInput.currentPage = currentPage
    },
    /**获取通道列表 */
    // getChannel(){
    //     window.api.get(window.path.omcs+'v3/operatingchannelgroup/getAllSMSGroup',{},res=>{
    //         console.log(res)
    //         this.channel=res.data;
    //     })
    // },
    // 弹出通道组修改弹框
    // changSMS(val){
    //     console.log(val);
    //     this.dialogFormVisible = true
    //     // this.smsForm.smsSelect = val.row.smsSelect
    //     this.smsForm.smsProductName = val.row.smsProductName
    //     this.smsForm.smsProductId = val.row.smsProductId;
    //     this.smsForm.userId = val.row.userId;
    // },
    //弹出通道组提交按钮
    // smsProducUpdate(val){
    //     console.log(val)
    //     this.$refs[val].validate((valid) => {
    //         if(valid){
    //             // this.dialogFormVisible = false;
    //             window.api.put(window.path.cpus+'consumerclientsms/updateByUserId',{'userId':this.smsForm.userId,'smsProductId':this.smsForm.smsProductId},res=>{
    //                 this.getTableData();
    //             })
    //         }
    //     });
    // }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableData()
      this.getServices()
    })

    // this.getSales()
    // this.getChannel()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableData()
        this.getServices()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style scoped>
.selectedBox {
  width: 85px;
  text-align: center;
  height: 35px;
  line-height: 35px;
  border: 1px solid #1abc9c;
  position: relative;
}
.selectedBox:after {
  border-style: solid;
  right: 0;
  top: 0;
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border-width: 13px 0 0 13px;
  border-color: #1abc9c transparent transparent transparent;
}
.input-w-2 {
  width: 80% !important;
}
.red {
  color: red;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.mr-3 {
  margin-right: 3px;
}
.addUserDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}
</style>

<style>
.el-loading-spinner i {
  margin-top: -10px;
  margin-bottom: 10px;
  font-size: 30px;
}

.pro-style .el-checkbox {
  margin-right: 0 !important;
}

.Mail-table1 .el-table--small td {
  padding: 2px 0 !important;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>