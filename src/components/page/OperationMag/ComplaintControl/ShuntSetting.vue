<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="产品" prop="productId">
            <el-select
              v-model="formInline.productId"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="短信" value="1"></el-option>
              <el-option label="彩信" value="2"></el-option>
              <el-option label="视频" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="启用状态" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">新增</el-button>
            <el-button
              type="primary"
              style="margin-left: 15px"
              @click="Synchronize()"
              >同 步</el-button
            >
            <el-button
              type="primary"
              @click="handeStart()"
              v-if="selectId.length != 0"
              >批量启用</el-button
            >
            <el-button
              type="warning"
              @click="handeStop()"
              v-if="selectId.length != 0"
              >批量停用</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="ShuntSetting"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
        >
          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名" title="用户名" width="140">
            <template #default="scope">
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="产品" title="产品" width="140">
            <template #default="scope">
              <div v-if="scope.row.productId == 1">短信</div>
              <div v-if="scope.row.productId == 2">彩信</div>
              <div v-if="scope.row.productId == 3">视频</div>
            </template>
          </vxe-column>
          <vxe-column field="行业" title="行业" width="140">
            <template #default="scope">
              <div v-if="scope.row.hyOpen === true">开启</div>
              <div v-else>关闭</div>
            </template>
          </vxe-column>
          <vxe-column field="移动失败代码" title="移动失败代码" width="140">
            <template #default="scope">
              <Tooltip
                v-if="scope.row.code"
                :content="scope.row.code"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
            </template>

            <!-- <template #default="scope">{{ scope.row.code }}</template> -->
          </vxe-column>
          <vxe-column field="联通失败代码" title="联通失败代码" width="140">
            <template #default="scope">
              <Tooltip
                v-if="scope.row.ltCode"
                :content="scope.row.ltCode"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>

            <!-- <template #default="scope">{{ scope.row.ltCode }}</template> -->
          </vxe-column>
          <vxe-column field="电信失败代码" title="电信失败代码" width="140">
            <template #default="scope">
              <Tooltip
                v-if="scope.row.dxCode"
                :content="scope.row.dxCode"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>

            <!-- <template #default="scope">{{ scope.row.dxCode }}</template> -->
          </vxe-column>
          <vxe-column field="高投诉区域" title="高投诉区域" width="140">
            <template #default="scope">
              <Tooltip
                v-if="scope.row.highArea"
                :content="scope.row.highArea"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>

            <!-- <template #default="scope">{{ scope.row.highArea }}</template> -->
          </vxe-column>
          <vxe-column field="低投诉区域" title="低投诉区域" width="140">
            <template #default="scope">
              <Tooltip
                v-if="scope.row.lowArea"
                :content="scope.row.lowArea"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>

            <!-- <template #default="scope">{{ scope.row.lowArea }}</template> -->
          </vxe-column>
          <!-- <vxe-column field="" title="总的百分比">
                <template #default="scope">{{ scope.row.totalPercent}}</template>
            </vxe-column> -->
          <vxe-column field="移动百分比" title="移动百分比" width="140">
            <template #default="scope">
              <div>
                {{ scope.row.ydPercent }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="联通百分比" title="联通百分比" width="140">
            <template #default="scope">
              <div>
                {{ scope.row.ltPercent }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="电信百分比" title="电信百分比" width="140">
            <template #default="scope">
              <div>
                {{ scope.row.dxPercent }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            field="最小成功率（%）"
            title="最小成功率（%）"
            width="140"
          >
            <template #default="scope">
              <div>
                {{ scope.row.successRatioLow }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            field="最大成功率（%）"
            title="最大成功率（%）"
            width="140"
          >
            <template #default="scope">
              <div>
                {{ scope.row.successRatioHigh }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="失败代码" title="失败代码" width="140">
            <template #default="scope">
              <div>
                {{ scope.row.successRatioCodes }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="有效期" title="有效期" width="170">
            <template #default="scope">
              <div
                style="color: red"
                v-if="
                  new Date().valueOf() >
                  new Date(scope.row.expireTime).valueOf()
                "
              >
                {{ scope.row.expireTime }}
              </div>
              <div v-else>{{ scope.row.expireTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态" width="140">
            <template #default="scope">
              <div v-if="scope.row.status == 1" style="color: #16a589">
                启用
              </div>
              <div v-else style="color: red">停用</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button link style="color: #409eff" @click="edit(scope.row)"
                >编 辑</el-button
              >
              <el-button
                link
                :style="
                  scope.row.status == 1 ? 'color:red;' : 'color: #409eff;'
                "
                @click="Enable(scope.row)"
                >{{ scope.row.status == 1 ? "停用" : "启用" }}</el-button
              >
              <el-button link style="color: red" @click="cancel(scope.row)"
                >删 除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <el-col
          :xs="24"
          :sm="24"
          :md="24"
          :lg="24"
          class="page"
          slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right"
        > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
        <!-- </el-col> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="1050"
      >
        <el-steps :active="steps" simple>
          <el-step title="基础设置 " icon="el-icon-edit"></el-step>
          <el-step title="前置路由设置" icon="el-icon-edit"></el-step>
          <el-step title="后置路由设置" icon="el-icon-circle-check"></el-step>
        </el-steps>
        <div style="margin-top: 10px" v-if="steps == 1">
          <el-form
            :inline="true"
            :model="formop"
            :rules="rules"
            label-width="120px"
            ref="formop"
            style="padding: 0 28px 0 20px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input
                :disabled="titleMap == '编辑' ? true : false"
                v-model="formop.username"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="产品" prop="productId">
              <el-select
                v-model="formop.productId"
                placeholder="请选择"
                clearable
                class="input-w"
              >
                <el-option label="短信" value="1"></el-option>
                <!-- <el-option label="彩信" value="2"></el-option> -->
                <el-option label="视频" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="行业" prop="hyOpen">
              <el-radio-group class="input-w" v-model="formop.hyOpen">
                <el-radio :value="true">开启</el-radio>
                <el-radio :value="false">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="关键词" prop="keywords">
              <el-input
                v-model="formop.keywords"
                placeholder="关键词拦截"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="高投诉省份" prop="highArea">
              <el-input
                v-model="formop.highArea"
                placeholder="上海:30,江苏:10"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="排除省市" prop="lowArea">
              <el-input
                v-model="formop.lowArea"
                placeholder="省份,城市,省份,城市"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="有效期" prop="expireTime">
              <el-date-picker
                v-model="formop.expireTime"
                type="datetime"
                @change="handleTime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="实号库" prop="actualNumberLibrary">
              <el-select
                v-model="formop.actualNumberLibrary"
                placeholder="请选择"
                clearable
                class="input-w"
              >
                <el-option label="一个月" value="1"></el-option>
                <el-option label="两个月" value="2"></el-option>
                <el-option label="三个月" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="空号库"
              style="width: 360px"
              prop="checkEmptyNumber"
            >
              <el-radio-group v-model="formop.checkEmptyNumber">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formop.checkEmptyNumber"
              label="三方渠道"
              prop="emptyNumberSource"
            >
              <el-checkbox-group
                style="
                  width: 360px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="formop.emptyNumberSource"
              >
                <el-checkbox
                  v-for="(item, index) in externalList"
                  :key="index"
                  :value="item.value"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
            <el-form-item
              v-if="formop.checkEmptyNumber"
              label="联电空号库"
              style="width: 360px"
              prop="checkLdEmptyNumber"
            >
              <el-radio-group v-model="formop.checkLdEmptyNumber">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formop.checkLdEmptyNumber && formop.checkEmptyNumber"
              label="联电三方渠道"
              prop="ldEmptyNumberSource"
            >
              <!-- <el-checkbox-group
                style="
                  width: 360px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="formop.ldEmptyNumberSource"
              >
                <el-checkbox
                  v-for="(item, index) in ldEmptyNumberSourceList"
                  :key="index"
                  :value="item.value"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group> -->
              <el-radio-group v-model="formop.ldEmptyNumberSource">
                <el-radio value="LJ_LD">棱镜联电</el-radio>
                <el-radio value="EWS">贰伍叁</el-radio>
                <el-radio value="BFSH">奔赴山海</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="客户号码库"
              style="width: 360px"
              prop="checkRuiXinMobile"
            >
              <el-radio-group v-model="formop.checkRuiXinMobile">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formop.checkRuiXinMobile"
              label="预留百分比"
              style="width: 360px"
              prop="reservePercent"
            >
              <el-input-number
                class="input-w"
                v-model="formop.reservePercent"
                :min="0"
                :max="100"
                label="预留百分比"
              ></el-input-number>
            </el-form-item>
            
            <el-form-item
              v-if="formop.checkRuiXinMobile"
              label="企业名称"
              style="width: 360px"
              prop="compId"
            >
            <el-select
                ref="optionRef"
                class="input-w"
                v-model="formop.compName"
                clearable
                filterable
                remote
                :remote-method="remoteMethod"
                :loading="loadingcomp"
                @change="handleCompChange"
                placeholder="请选择公司名称"
              >
                <el-option
                  v-for="(item, index) in compNamelist"
                  :key="index"
                  :label="item.company"
                  :value="item.company + '+' + item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="监控成功率"
              style="width: 360px"
              prop="reserveMaxPercent"
            >
              <el-input-number
                class="input-w"
                v-model="formop.reserveMaxPercent"
                :min="0"
                :max="100"
                label="预留百分比"
              ></el-input-number>
            </el-form-item>
            <!-- <el-form-item
              v-if="formop.checkRuiXinMobile"
              label="企业ID"
              style="width: 360px"
              prop="compId"
            >
              <el-input v-model="formop.compId" autocomplete="off" class="input-w" clearable ></el-input>
            </el-form-item> -->
            <div>
              <el-form-item
                v-if="formop.checkEmptyNumber"
                label="关机路由"
                prop="numberAsReal"
              >
                <el-radio-group class="input-w" v-model="formop.numberAsReal">
                  <el-radio :value="true">是</el-radio>
                  <el-radio :value="false">否</el-radio>
                </el-radio-group>
              </el-form-item>
              
            </div>
            <!-- <el-form-item label="关机路由" prop="numberAsReal">
              <el-radio-group class="input-w" v-model="formop.numberAsReal">
                <el-radio :value='true'>是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-form-item> -->

            <!-- <div class="borders"></div> -->
            <!-- <el-form-item label="总比例" prop="totalPercent">
                <el-input v-model="formop.totalPercent" autocomplete="off" class="input-w"></el-input>
            </el-form-item> -->
          </el-form>
        </div>
        <div style="margin-top: 10px" v-if="steps == 2">
          <el-form
            :inline="true"
            :model="formop"
            :rules="rules"
            label-width="120px"
            ref="formop"
            style="padding: 0 28px 0 20px"
          >
            <el-form-item label="移动失败代码" prop="code">
              <el-input
                type="textarea"
                v-model="formop.code"
                placeholder="code1-30,code2-30,code3-30"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="移动比例" prop="ydPercent">
              <el-input-number
                class="input-w"
                v-model="formop.ydPercent"
                :min="0"
                :max="999999999"
                label="移动比例"
              ></el-input-number>
              <!-- <el-input v-model="formop.ydPercent" autocomplete="off" class="input-w"></el-input> -->
            </el-form-item>
            <el-form-item label="联通失败代码" prop="ltCode">
              <el-input
                type="textarea"
                v-model="formop.ltCode"
                placeholder="code1-30,code2-30,code3-30"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="联通比例" prop="ltPercent">
              <el-input-number
                class="input-w"
                v-model="formop.ltPercent"
                :min="0"
                :max="999999999"
                label="联通比例"
              ></el-input-number>
              <!-- <el-input v-model="formop.ltPercent" autocomplete="off" class="input-w"></el-input> -->
            </el-form-item>
            <el-form-item label="电信失败代码" prop="dxCode">
              <el-input
                type="textarea"
                v-model="formop.dxCode"
                placeholder="code1-30,code2-30,code3-30"
                autocomplete="off"
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="电信比例" prop="dxPercent">
              <el-input-number
                class="input-w"
                v-model="formop.dxPercent"
                :min="0"
                :max="999999999"
                label="联通比例"
              ></el-input-number>
              <!-- <el-input v-model="formop.dxPercent" autocomplete="off" class="input-w"></el-input> -->
            </el-form-item>
          </el-form>
        </div>
        <div style="margin-top: 10px" v-if="steps == 3">
          <el-form
            :inline="true"
            :model="formop"
            :rules="rules"
            label-width="120px"
            ref="formop"
            style="padding: 0 28px 0 20px"
          >
            <el-form-item label="成功率最小值" prop="successRatioLow">
              <el-input-number
                style="width: 180px"
                placeholder="请输入成功率最小值"
                v-model="formop.successRatioLow"
                :min="0"
                :max="999999999"
                label="成功率最小值"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="成功率最大值" prop="successRatioHigh">
              <el-input-number
                style="width: 180px"
                placeholder="请输入成功率最大值"
                v-model="formop.successRatioHigh"
                :min="0"
                :max="999999999"
                label="成功率最大值"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="失败代码" prop="successRatioCodes">
              <el-input
                type="textarea"
                style="width: 400px"
                v-model="formop.successRatioCodes"
                placeholder="code1-30,code2-30,code3-30"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button v-if="steps > 1" type="primary" @click="prevStep('formop')"
            >上一步</el-button
          >
          <el-button v-if="steps < 3" type="primary" @click="nextStep('formop')"
            >下一步</el-button
          >
          <el-button
            v-if="steps == 3"
            type="primary"
            @click="submitForm('formop')"
            >提 交</el-button
          >
        </template>
      </el-dialog>
      <!-- 新增 -->
    </div>
  </div>
</template>
<script>
import TableTem from "../../../publicComponents/TableTem.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import moment from "moment";
export default {
  name: "ShuntSetting",
  components: {
    TableTem,
    Tooltip,
  },
  data() {
    var numberRules = (rule, value, callback) => {
      if (!/^([0-9][0-9]{0,3}|100)$/.test(value)) {
        return callback(new Error("请输入0-100的数字"));
      } else {
        callback();
      }
    };
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      titleMap: "新增",
      isFirstEnter: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      ids: "",
      steps: 1,
      selectId: [],
      // expireTime: "",
      formop: {
        username: "",
        productId: "1",
        ydPercent: "0",
        ltPercent: "0",
        dxPercent: "0",
        totalPercent: "0",
        highArea: "",
        lowArea: "",
        code: "",
        ltCode: "",
        dxCode: "",
        keywords: "",
        checkEmptyNumber: false,
        checkRuiXinMobile: false,
        reservePercent: undefined,
        reserveMaxPercent: undefined,
        emptyNumberSource: [],
        actualNumberLibrary: "3",
        checkLdEmptyNumber: false,
        ldEmptyNumberSource: "",
        expireTime: moment().add(1, "months").format("YYYY-MM-DD HH:mm:ss"),
        hyOpen: false,
        numberAsReal: false,
        successRatioHigh: "", //成功率最大值
        successRatioLow: "", //成功率最小值
        successRatioCodes: "", //失败代码
        compId: "",
        compName: "",
      },
      externalList: [
        {
          name: "棱镜",
          value: "LJ",
        },
        {
          name: "联麓",
          value: "LL",
        },
        {
          name: "流信云",
          value: "LXY",
        },
        {
          name: "先丰",
          value: "XF",
        },
        {
          name: "贰伍叁",
          value: "EWS",
        },
        {
          name: "正和顺",
          value: "ZHS",
        },
        {
          name: "奔赴山海",
          value: "BFSH",
        },
      ], //检测空号渠道列表
      ldEmptyNumberSourceList: [
        {
          name: "棱镜",
          value: "LJ_LD",
        },
        {
          name: "贰伍叁",
          value: "EWS",
        },
      ], //联电空号库列表
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "change" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        productId: [
          { required: true, message: "请选择产品", trigger: "change" },
        ],
        checkEmptyNumber: [
          {
            required: true,
            message: "请选择是否启用空号检测",
            trigger: "change",
          },
        ],
        ldEmptyNumberSource: [
          {
            required: true,
            message: "请选择联电空号库",
            trigger: "change",
          },
        ],
        hyOpen: [
          {
            required: true,
            message: "请选择是否开启行业类型",
            trigger: "change",
          },
        ],
        numberAsReal: [
          { required: true, message: "请选择是否关机", trigger: "change" },
        ],
        expireTime: [
          { required: true, message: "请选择有效期", trigger: "change" },
        ],
        emptyNumberSource: [
          { required: true, message: "请选择三方渠道渠道", trigger: "change" },
        ],
        actualNumberLibrary: [
          { required: true, message: "请选择实号库", trigger: "change" },
        ],
        ydPercent: [
          { required: true, validator: numberRules, trigger: "change" },
        ],
        ltPercent: [
          { required: true, validator: numberRules, trigger: "change" },
        ],
        dxPercent: [
          { required: true, validator: numberRules, trigger: "change" },
        ],
        totalPercent: [
          { required: true, validator: numberRules, trigger: "change" },
        ],
        reservePercent: [
          { required: true, message: '请输入预留百分比', trigger: "change" },
        ],
        reserveMaxPercent: [
          { required: true, message: '请输入最大预留百分比', trigger: "change" },
        ],
        // successRatioHigh: [
        //   { required: true, message: "请输入成功率最大值", trigger: "change" },
        // ],
        // successRatioLow: [
        //   { required: true, message: "请输入成功率最大值", trigger: "change" },
        // ],
        // successRatioCodes: [
        //   { required: true, message: "请输入失败代码", trigger: "change" },
        // ],
      },
      formInline: {
        username: "",
        productId: "",
        status: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: "",
        productId: "",
        status: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
      loadingcomp:false,
      compNamelist:[],
    };
  },
  methods: {
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      // console.log(query, "query");
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.compNamelist = [];
        this.searchAccount();
      }
    },
    handleCompChange(val) {
      if (val) {
        this.formop.compName = val.split("+")[0];
        if (val.split("+")[1] != "null") {
          this.formop.compId = val.split("+")[1];
        } else {
          this.formop.compId = "";
        }
      } else {
        this.searchAccount();
        this.formop.compName = "";
        this.formop.compId = "";
      }
      
    },
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingdivertclientsetting/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.tableData = res.data.records;
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    Reload() {
      this.$refs["formInline"].resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    externalListFun(arr, list, type) {
      // let list = ["LJ", "LL", "LXY", "XF", "EWS"];
      let sortList = arr.map((item) => {
        return item;
      });
      let emptyNumberSourceSource = sortList.concat(
        list.filter(function (v) {
          return !(sortList.indexOf(v) > -1);
        })
      );
      if (type == "external") {
        this.externalList = emptyNumberSourceSource.map((item) => {
          return {
            name:
              item == "LJ"
                ? "棱镜"
                : item == "LL"
                ? "联麓"
                : item == "LXY"
                ? "流信云"
                : item == "XF"
                ? "先丰"
                : item == "EWS"
                ? "贰伍叁"
                : item == "ZHS"
                ? "正和顺"
                : item == "BFSH"
                ? "奔赴山海"
                : "",
            value: item,
          };
        });
      }
      //  else if (type == "ldEmptyNumberSource") {
      //   this.ldEmptyNumberSourceList = emptyNumberSourceSource.map((item) => {
      //     return {
      //       name: item == "LJ" ? "棱镜" : item == "EWS" ? "贰伍叁" : "",
      //       value: item,
      //     };
      //   });
      // }
    },
    // 新增 编辑
    addopt() {
      this.titleMap = "新增";
      this.dialogFormVisible = true;
    },
    edit(val) {
      this.titleMap = "编辑";
      this.dialogFormVisible = true;
      //清除编辑默认值
      this.$nextTick(() => {
        if (this.$refs.formop) this.$refs.formop.resetFields();
        Object.assign(this.formop, val);
        this.formop.productId = val.productId == "2" ? "" : val.productId;
        this.expireTime = val.expireTime;
        // this.formop.hyOpen += "";
        if (val.numberAsReal === null) {
          this.formop.numberAsReal = false;
        } else {
          this.formop.numberAsReal = val.numberAsReal;
        }
        // console.log(this.formop,'for');
        this.formop.productId += "";
        this.formop.checkEmptyNumber = val.checkEmptyNumber || false;
        // this.formop.emptyNumberSource = val.emptyNumberSource || []
        this.formop.actualNumberLibrary = val.actualNumberLibrary || "";
        if (val.emptyNumberSource) {
          this.formop.emptyNumberSource = val.emptyNumberSource.split(",");
          let list = ["LJ", "LL", "LXY", "XF", "EWS", "ZHS", "BFSH"];
          this.externalListFun(this.formop.emptyNumberSource, list, "external");
          // let sortList = this.formop.emptyNumberSource.map((item) => {
          //   return item;
          // });
          // let emptyNumberSourceSource = sortList.concat(
          //   list.filter(function (v) {
          //     return !(sortList.indexOf(v) > -1);
          //   })
          // );
          // this.externalList = emptyNumberSourceSource.map((item) => {
          //   return {
          //     name:
          //       item == "LJ"
          //         ? "棱镜"
          //         : item == "LL"
          //         ? "联麓"
          //         : item == "LXY"
          //         ? "流信云"
          //         : item == "XF"
          //         ? "先丰"
          //         : item == "EWS"
          //         ? "贰伍叁"
          //         : "",
          //     value: item,
          //   };
          // });
        } else {
          this.formop.emptyNumberSource = [];
          this.externalList = [
            {
              name: "棱镜",
              value: "LJ",
            },
            {
              name: "联麓",
              value: "LL",
            },
            {
              name: "流信云",
              value: "LXY",
            },
            {
              name: "先丰",
              value: "XF",
            },
            {
              name: "贰伍叁",
              value: "EWS",
            },
            {
              name: "正和顺",
              value: "ZHS",
            },
            {
              name: "奔赴山海",
              value: "BFSH",
            },
          ];
        }
        this.formop.checkLdEmptyNumber = val.checkLdEmptyNumber || false;
        if (val.ldEmptyNumberSource) {
          this.formop.ldEmptyNumberSource = val.ldEmptyNumberSource;
          // let list = ["LJ", "EWS"];
          // this.externalListFun(
          //   this.formop.ldEmptyNumberSource,
          //   list,
          //   "ldEmptyNumberSource"
          // );
        } else {
          this.formop.ldEmptyNumberSource = "";
        }
      });
    },
    handleTime(e) {
      this.formop.expireTime = moment(e).format("YYYY-MM-DD HH:mm:ss");
    },
    prevStep() {
      this.steps--;
    },
    nextStep(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.steps++;
        } else {
          this.$message({
            type: "error",
            duration: 2000,
            message: "请检查输入项是否有空缺",
          });
        }
      });
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let strData = JSON.stringify(this.formop);
          let data = JSON.parse(strData);
          if (data.emptyNumberSource.length) {
            data.emptyNumberSource = data.emptyNumberSource.join(",");
          } else {
            data.emptyNumberSource = "";
          }
          // if (data.ldEmptyNumberSource.length) {
          //   data.ldEmptyNumberSource = data.ldEmptyNumberSource.join(",");
          // } else {
          //   data.ldEmptyNumberSource = "";
          // }
          this.$confirms.confirmation(
            this.titleMap == "新增" ? "post" : "put",
            this.titleMap == "新增" ? "确定新增操作？" : "确定编辑操作？",
            window.path.omcs + "operatingdivertclientsetting",
            data,
            (res) => {
              this.dialogFormVisible = false;
              this.gettableLIst();
            }
          );
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 同步
    Synchronize() {
      this.$confirms.confirmation(
        "post",
        "确定同步缓存操作？",
        window.path.omcs + "operatingdivertclientsetting/synDivert",
        {},
        (res) => {
          this.gettableLIst();
        }
      );
    },
    // 停用启用
    Enable(val) {
      if (val.status == 1) {
        this.$confirms.confirmation(
          "post",
          "确定停用操作？",
          window.path.omcs + "operatingdivertclientsetting/disable/" + val.id,
          {},
          (res) => {
            this.gettableLIst();
          }
        );
      } else {
        this.$confirms.confirmation(
          "post",
          "确定启用操作？",
          window.path.omcs + "operatingdivertclientsetting/enable/" + val.id,
          {},
          (res) => {
            this.gettableLIst();
          }
        );
      }
    },
    // 删除
    cancel(val) {
      this.$confirms.confirmation(
        "delete",
        "确定删除操作？",
        window.path.omcs + "operatingdivertclientsetting/" + val.id,
        {},
        (res) => {
          this.gettableLIst();
        }
      );
    },
    //批量
    handleSelectionChange(row) {
      if (row.records.length > 0) {
        let arr = [];
        row.records.forEach((item) => {
          arr.push(item.id);
        });
        this.selectId = arr.join(",");
      } else {
        this.selectId = "";
      }
    },
    //批量启用
    handeStart() {
      this.$confirms.confirmation(
        "post",
        "批量启用投诉控制分流！",
        window.path.omcs +
          "operatingdivertclientsetting/batch/enable?ids=" +
          this.ids,
        {},
        (res) => {
          this.gettableLIst();
          this.selectId = [];
        }
      );
    },
    //批量停用
    handeStop() {
      this.$confirms.confirmation(
        "post",
        "批量停用投诉控制分流！",
        window.path.omcs +
          "operatingdivertclientsetting/batch/disable?ids=" +
          this.ids,
        {},
        (res) => {
          this.gettableLIst();
          this.selectId = [];
        }
      );
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.gettableLIst();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields();
        this.formop.username = "";
        this.formop.productId = "1";
        this.formop.ydPercent = "0";
        this.formop.ltPercent = "0";
        this.formop.dxPercent = "0";
        this.formop.totalPercent = "0";
        this.formop.highArea = "";
        this.formop.lowArea = "";
        this.formop.code = "";
        this.formop.ltCode = "";
        this.formop.dxCode = "";
        this.formop.keywords = "";
        this.formop.checkEmptyNumber = false;
        this.formop.emptyNumberSource = [];
        this.formop.checkLdEmptyNumber = false;
        this.formop.ldEmptyNumberSource = "";
        this.formop.actualNumberLibrary = "3";
        this.formop.expireTime = moment()
          .add(1, "months")
          .format("YYYY-MM-DD HH:mm:ss");
        this.formop.hyOpen = "false";
        this.formop.successRatioHigh = ""; //成功率最大值
        this.formop.successRatioLow = ""; //成功率最小值
        this.formop.successRatioCodes = ""; //失败代码
        this.formop.reservePercent = undefined;
        this.formop.reserveMaxPercent = undefined;
        this.formop.checkRuiXinMobile = false;
        this.formop.compId = "";
        this.formop.compName = "";
        // this.expireTime = ''
        this.steps = 1;
      }
    },
  },
};
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.borders {
  width: 100%;
  /* height: 5px; */
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>


<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>