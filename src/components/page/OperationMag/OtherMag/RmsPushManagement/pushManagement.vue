<template>
  <div class="container_left">
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <el-tabs v-model="activeName" type="card" @tab-change="handleClickTab">
        <el-tab-pane label="状态推送" name="pushRecord">
          <keep-alive>
            <component
              v-if="activeName == 'pushRecord'"
              v-bind:is="currentTabComponent"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="推送记录" name="StatusPush">
          <keep-alive>
            <component
              v-if="activeName == 'StatusPush'"
              v-bind:is="currentTabComponent"
            ></component>
          </keep-alive>
        </el-tab-pane>
      </el-tabs>
      <div>
        <!-- <component v-bind:is="currentTabComponent"></component> -->
      </div>
    </div>
  </div>
</template>

<script>
import pushRecord from './components/pushRecord.vue'
import StatusPush from './components/StatusPush.vue'
export default {
  components: {
    pushRecord,
    StatusPush
  },
  name: 'pushManagement',
  data() {
    return {
      currentTabComponent: 'pushRecord',
      activeName: 'pushRecord',
    }
  },
  methods: {
    handleClickTab(tab) {
      this.currentTabComponent = tab
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
