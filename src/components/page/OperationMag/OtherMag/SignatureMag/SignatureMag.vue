<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form label-width="80px" :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
          <el-form-item label="用户名" prop="consumerName">
            <el-input v-model="formInline.consumerName" placeholder class="input-time"></el-input>
          </el-form-item>

          <el-form-item label="签名" prop="signature">
            <el-input v-model="formInline.signature" placeholder class="input-time"></el-input>
          </el-form-item>

          <el-form-item label="签名属性" prop="signatureAttr">
            <el-select v-model="formInline.signatureAttr" clearable placeholder="请选择" class="input-time">
              <el-option label="全部" value="0"></el-option>
              <el-option label="报备签名" value="1"></el-option>
              <el-option label="自定义签名" value="2"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="创建人" prop="createName">
            <el-input v-model="formInline.createName" placeholder class="input-time"></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="dataCreat">
            <el-date-picker class="input-time" v-model="formInline.dataCreat" type="daterange" range-separator="-"
              @change="hande" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item label="审核人" prop="auditUsername">
            <el-input v-model="formInline.auditUsername" placeholder class="input-time"></el-input>
          </el-form-item>
          <el-form-item label="审核时间" prop="auditdataCreat">
            <el-date-picker class="input-time" v-model="formInline.auditdataCreat" type="daterange" range-separator="-"
              @change="handes" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query">查询</el-button>
            <el-button type="primary" plain style @click="Reload('formInline')">重置</el-button>
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection" @routers="getOperations" @getIdx="getIdX"></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">创建签名</el-button>
            <el-button type="primary" style="margin-left: 15px" @click="SignafileDialog = true">签名导入</el-button>
            <el-button type="primary" style="margin-left: 15px" @click="SignaRealNameDialog = true">签名实名信息导入</el-button>
            <el-button type="danger" v-if="selectId.length > 0" @click="delAll">批量删除</el-button>
          </template>
        </vxe-toolbar>
        <vxe-table ref="tableRef" id="SignatureMag" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;" :data="tableDataObj.tableData" @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称" width="140">
            <template #default="scope">
              <div style="color: #16a589; cursor: pointer" @click="rouTz(scope.row)">
                {{ scope.row.consumerName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="签名" title="签名" width="180">
            <template #default="scope">
              <div>{{ scope.row.signature }}</div>
            </template>
          </vxe-column>
          <vxe-column field="短信示例" title="短信示例" width="240">
            <template #default="scope">
              <Tooltip v-if="scope.row.contentExample" :content="scope.row.contentExample" className="wrapper-text"
                effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="短信类型" title="短信类型" width="140">
            <template #default="scope">
              <div v-if="scope.row.serviceType == 1"> 验证码 </div>
              <div v-else-if="scope.row.serviceType == 2"> 通知 </div>
              <div v-else-if="scope.row.serviceType == 3"> 营销 </div>
            </template>
          </vxe-column>
          <vxe-column field="营销类型" title="营销类型" width="140">
            <template #default="scope">
              <div v-for="item in subServiceTypeList">
                <span v-if="item.code == scope.row.subServiceType">{{
                  item.desc
                  }}</span>
              </div>
            </template>
          </vxe-column>
          <!-- 移动实名状态 -->
          <vxe-column field="移动实名" title="移动实名" width="120">
            <template #header>
              <div class="operator-header">
                <i class="iconfont icon-yidong" style="color: #409eff; font-size: 12px; margin-right: 4px;"></i>
                <span>移动</span>
              </div>
            </template>
            <template #default="scope">
              <div class="single-operator-status">
                <el-tooltip v-if="scope.row.ydRealNameStatus == 0" 
                  content="未实名" placement="top" effect="dark">
                  <el-tag size="small" type="info" class="status-tag">
                    未实名
                  </el-tag>
                </el-tooltip>
                <div v-else-if="scope.row.ydRealNameStatus == 1" class="status-tags-container">
                  <template v-if="scope.row.ydAvalibleType">
                    <el-tag 
                      v-for="(tag, index) in scope.row.ydAvalibleType.split(',')" 
                      :key="index"
                      size="small" 
                      type="success"
                      class="status-tag success fixed-width">
                      {{ tag.trim() }}
                    </el-tag>
                  </template>
                  <el-tag v-else size="small" type="primary" class="status-tag success fixed-width">
                    已实名
                  </el-tag>
                </div>
              </div>
            </template>
          </vxe-column>

          <!-- 联通实名状态 -->
          <vxe-column field="联通实名" title="联通实名" width="120">
            <template #header>
              <div class="operator-header">
                <i class="iconfont icon-liantong" style="color: #f56c6c; font-size: 12px; margin-right: 4px;"></i>
                <span>联通</span>
              </div>
            </template>
            <template #default="scope">
              <div class="single-operator-status">
                <el-tooltip v-if="scope.row.ltRealNameStatus == 0" 
                  content="未实名" placement="top" effect="dark">
                  <el-tag size="small" type="info" class="status-tag">
                    未实名
                  </el-tag>
                </el-tooltip>
                <div v-else-if="scope.row.ltRealNameStatus == 1" class="status-tags-container">
                  <template v-if="scope.row.ltAvalibleType">
                    <el-tag 
                      v-for="(tag, index) in scope.row.ltAvalibleType.split(',')" 
                      :key="index"
                      size="small" 
                      type="success"
                      class="status-tag success fixed-width">
                      {{ tag.trim() }}
                    </el-tag>
                  </template>
                  <el-tag v-else size="small" type="primary" class="status-tag success fixed-width">
                    已实名
                  </el-tag>
                </div>
              </div>
            </template>
          </vxe-column>

          <!-- 电信实名状态 -->
          <vxe-column field="电信实名" title="电信实名" width="120">
            <template #header>
              <div class="operator-header">
                <i class="iconfont icon-dianxin" style="color: #e6a23c; font-size: 12px; margin-right: 4px;"></i>
                <span>电信</span>
              </div>
            </template>
            <template #default="scope">
              <div class="single-operator-status">
                <el-tooltip v-if="scope.row.dxRealNameStatus == 0" 
                  content="未实名" placement="top" effect="dark">
                  <el-tag size="small" type="info" class="status-tag">
                    未实名
                  </el-tag>
                </el-tooltip>
                <div v-else-if="scope.row.dxRealNameStatus == 1" class="status-tags-container">
                  <template v-if="scope.row.dxAvalibleType">
                    <el-tag 
                      v-for="(tag, index) in scope.row.dxAvalibleType.split(',')" 
                      :key="index"
                      size="small" 
                      type="success"
                      class="status-tag success fixed-width">
                      {{ tag.trim() }}
                    </el-tag>
                  </template>
                  <el-tag v-else size="small" type="primary" class="status-tag success fixed-width">
                    已实名
                  </el-tag>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="实名状态原因" title="实名状态原因" width="140">
            <template #default="scope">
              <div v-if="scope.row.ydReason">
                {{ scope.row.ydReason }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="序号" title="序号" width="140">
            <template #default="scope">
              <div style="color: #409eff; cursor: pointer" @click="getIdX(scope.row)">{{ scope.row.idx }}</div>
            </template>
          </vxe-column>
          <vxe-column field="签名属性" title="签名属性" width="140">
            <template #default="scope">
              <div v-if="scope.row.signatureAttr == 1">报备签名</div>
              <div v-else-if="scope.row.signatureAttr == 2">自定义签名</div>
            </template>
          </vxe-column>
          <vxe-column field="签名来源" title="签名来源" width="140">
            <template #default="scope">
              <!-- <div v-if="scope.row.signatureType == 1">企业名称</div>
              <div v-else-if="scope.row.signatureType == 2">事业单位：如机关，学校，科研单位，街道社区等</div>
              <div v-else-if="scope.row.signatureType == 3">商标</div>
              <div v-else-if="scope.row.signatureType == 4">产品：包括app、小程序、平台等</div> -->
              <!-- <div v-else-if="scope.row.signatureType == 5">商标全称或简称</div>
              <div v-else-if="scope.row.signatureType == 6">其他</div> -->
              <div>{{ getSignatureTypeText(scope.row.signatureType) }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="签名类型" title="签名类型" width="140">
            <template #default="scope">
              <div v-if="scope.row.signatureSubType == 0">全称</div>
              <div v-else-if="scope.row.signatureSubType == 1">简称</div>
            </template>
          </vxe-column> -->
          <vxe-column field="创建人" title="创建人" width="140">
            <template #default="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="160">
            <template #default="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                }}</div>
              <!-- <div>{{ formatDate(new Date(scope.row.createTime * 1), 'YYYY-MM-DD HH:mm:ss')}}</div> -->
            </template>
          </vxe-column>
          <vxe-column field="审核人" title="审核人" width="140">
            <template #default="scope">
              <div>{{ scope.row.auditUsername }}</div>
            </template>
          </vxe-column>
          <vxe-column field="审核时间" title="审核时间" width="160">
            <template #default="scope">
              <div v-if="scope.row.auditTime">{{
                moment(scope.row.auditTime).format("YYYY-MM-DD HH:mm:ss")
                }}</div>
              <!-- <div>{{ formatDate(new Date(scope.row.auditTime * 1), 'YYYY-MM-DD HH:mm:ss')}}</div> -->
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="160" fixed="right">
            <template #default="scope">
              <el-button link style="color: #16a589; margin-left: 10px"
                @click="handelOptionButton('details', scope.row)"><el-icon>
                  <SuccessFilled />
                </el-icon>&nbsp;详情</el-button>
              <el-button link style="color: #f56c6c; margin-left: 10px"
                @click="handelOptionButton('dellog', scope.row)"><el-icon>
                  <CircleCloseFilled />
                </el-icon>&nbsp;删除</el-button>
              <el-button type="primary" link style="margin-left: 10px" @click="handelCheck(scope.row)"><el-icon>
                  <View />
                </el-icon>&nbsp;实名明细</el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <el-col
          :xs="24"
          :sm="24"
          :md="24"
          :lg="24"
          class="page"
          slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right"
        > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </div>

        <!-- </el-col> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增签名 -->
      <!-- <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        width="520px"
      >
        <el-form :model="formop" :rules="rules" ref="formop">
          <el-form-item label="用户名" label-width="120px" prop="username">
            <el-input v-model="formop.username" autocomplete="off" class="input-w bkg" readonly></el-input>
          </el-form-item>
          <el-form-item label="签名内容" label-width="120px" prop="comsign">
            <el-input v-model="formop.comsign" autocomplete="off" class="input-w bkg" readonly></el-input>
          </el-form-item>
          <el-form-item label="签名类型" label-width="120px" prop="signstyle">
            <el-input v-model="formop.signstyle" autocomplete="off" class="input-w bkg" readonly></el-input>
          </el-form-item>
          <el-form-item label="上传营业执照" label-width="120px" prop="updatezhizhao">
            <el-upload
              class="upload-demo"
              action="https://jsonplaceholder.typicode.com/posts/"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="fileList2"
              :limit="3"
              :before-upload="beforeAvatarUpload"
              list-type="picture"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过5M</div>
            </el-upload>
          </el-form-item>
        </el-form>

        <template #footer>
          <el-button type="primary" @click="submitForm('formop')">提 交</el-button>
          <el-button @click="this.dialogFormVisible = false">取 消</el-button>
        </template>
      </el-dialog> -->
      <!-- 新增标签商结束 -->

      <!-- 编辑标签 -->
      <el-dialog :title="titleMap[dialogStatus]" v-model="dialogFormVisiblebj" width="520px">
        <el-form :model="formops" :rules="rules" ref="formops">
          <el-form-item label="用户名" label-width="120px" prop="consumerName">
            <el-input v-model="formops.consumerName" autocomplete="off" class="input-w bkg" readonly></el-input>
          </el-form-item>
          <el-form-item label="签名内容" label-width="120px" prop="signature">
            <el-input v-model="formops.signature" autocomplete="off" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="签名类型" label-width="120px" prop="signatureType">
            <el-select v-model="formops.signatureType" placeholder="请选择" class="input-w">
              <el-option label="公司名全称或简称" value="1"></el-option>
              <el-option label="APP全称或简称" value="2"></el-option>
              <el-option label="工信部备案的网站名全称或简称" value="3"></el-option>
              <el-option label="公众号或小程序名全称或简称" value="4"></el-option>
              <el-option label="商标名称或简称" value="5"></el-option>
              <el-option label="其他" value="6"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="上传营业执照" label-width="120px" prop="updatezhizhao">
            <el-upload
              class="upload-demo"
              action="https://jsonplaceholder.typicode.com/posts/"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="fileList2"
              v-model="formops.updatezhizhao"
              :limit="3"
              :before-upload="beforeAvatarUpload"
              list-type="picture"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过5M</div>
            </el-upload>
          </el-form-item> -->
          <el-form-item label="备注内容" label-width="120px" prop="remark">
            <el-input type="textarea" v-model="formops.remark"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForms('formops')">提 交</el-button>
            <el-button @click="dialogFormVisiblebj = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增签名 -->
      <el-dialog title="新增签名" v-model="dialogFormVisible" :close-on-click-modal="false" width="950px" draggable>
        <el-form :model="formopss" :rules="rules" ref="formopss" label-width="140px">
          <el-form-item label="用户名" prop="consumerName">
            <el-input v-model="formopss.consumerName" type="textarea" placeholder="多个用户名以,隔开"
              autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="签名" prop="signature">
            <el-input v-model="formopss.signature" placeholder="请输入签名" style="width: 100%"></el-input>
          </el-form-item>
          <el-form-item label="短信类型" prop="serviceType">
            <el-select style="width: 100%" v-model="formopss.serviceType" placeholder="请选择短信类型">
              <el-option label="验证码" value="1"></el-option>
              <el-option label="通知" value="2"></el-option>
              <el-option label="营销推广" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="formopss.serviceType == '3'" label="营销类型" prop="subServiceType">
            <el-select style="width: 100%" v-model="formopss.subServiceType" placeholder="请选择营销类型">
              <el-option v-for="item in subServiceTypeList" :key="item.code" :label="item.desc"
                :value="item.code"></el-option>
              <!-- <el-option label="通知" value="2"></el-option>
                            <el-option label="营销推广" value="3"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item label="短信示例" prop="contentExample">
            <el-input style="height: 100%" v-model="formopss.contentExample" type="textarea"
              placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！" maxlength="800" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="companyName">
            <el-input placeholder="请输入企业名称" v-model="formopss.companyName"></el-input>
          </el-form-item>
          <el-form-item label="社会统一信用代码" prop="creditCode">
            <el-input placeholder="请输入统一社会信用代码" v-model="formopss.creditCode"></el-input>
          </el-form-item>
          <el-form-item label="企业法人" prop="legalPerson">
            <el-input placeholder="请输入企业法人姓名" v-model="formopss.legalPerson"></el-input>
          </el-form-item>
          <el-form-item label="责任人姓名" prop="principalName">
            <el-input placeholder="请输入负责人姓名" v-model="formopss.principalName"></el-input>
          </el-form-item>
          <el-form-item label="责任人证件号码" prop="principalIdCard">
            <el-input placeholder="请输入负责人身份证号" v-model="formopss.principalIdCard"></el-input>
          </el-form-item>
          <el-form-item label="责任人手机号" prop="principalMobile">
            <el-input placeholder="请输入负责人手机号" v-model="formopss.principalMobile"></el-input>
          </el-form-item>
          <el-form-item label="签名来源" prop="signatureType">
            <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;"
              v-model="formopss.signatureType">
              <el-radio :value="1">
                <span class="sig-type-title-tips">企业名称</span>
                <!-- （须提供营业执照图片） -->
              </el-radio>
              <el-radio :value="2">
                <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
              </el-radio>
              <el-radio :value="3">
                <span class="sig-type-title-tips">商标</span>
                （须提供商标注册证书图片或在在中国商标网的商标查询截图）
              </el-radio>
              <el-radio :value="4">
                <span class="sig-type-title-tips">App </span>
                （须提供app在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio :value="5">
                <span class="sig-type-title-tips">小程序</span>
                （须提供小程序在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <!-- <el-radio :value="6">
                <span class="sig-type-title-tips">公众号</span>
                （须提供小程序与主体公司存在关联关系的证明材图片）
              </el-radio> -->
              <!-- <el-radio :value="7">
                <span class="sig-type-title-tips">网站</span>
                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
              </el-radio> -->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签名类型" prop="signatureSubType">
            <el-radio-group v-model="formopss.signatureSubType">
              <el-radio :value="0">
                <span>全称</span>
              </el-radio>
              <el-radio :value="1">
                <span>简称</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="(formopss.signatureType == 1 || formopss.signatureType == 2) && formopss.signatureSubType == 0" label="文件上传" prop="" key="imgUrl">
            <div>
              <el-upload class="upload-demo" :file-list="formopss.fileList" :headers="token"
                :action="this.API.cpus + 'v3/file/upload'" :limit="3" list-type="picture-card"
                :on-preview="handlePictureCardPreview" :on-success="handleSuccess" :on-remove="handleRemove1">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
              <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
            </div>
          </el-form-item>
          <el-form-item v-else label="文件上传" prop="imgUrl" key="imgUrl1">
            <div>
              <el-upload class="upload-demo" :file-list="formopss.fileList" :headers="token"
                :action="this.API.cpus + 'v3/file/upload'" :limit="3" list-type="picture-card"
                :on-preview="handlePictureCardPreview" :on-success="handleSuccess" :on-remove="handleRemove1">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
              <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formopss')">提 交</el-button>
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="imgUrlvisible">
        <img w-full :src="dialogImageUrl" alt="Preview Image" />
      </el-dialog>
      <el-dialog title="签名导入" v-model="SignafileDialog" :close-on-click-modal="false" width="520px">
        <el-upload class="upload-demo" :headers="token" :action="this.API.omcs + 'consumerSignature/uploadAttachment'" :limit="1"
          v-model:file-list="zipfileList" :on-success="handleSuccessZip" :on-remove="handleRemoveZip"
          :before-upload="beforeAvatarUploadc">
          <el-button size="default" type="primary" plain><el-icon style="font-size: 18px;">
              <UploadFilled />
            </el-icon>上传图片</el-button>
          <!-- <el-button size="small" type="primary">上传图片</el-button> -->
          <div slot="tip" class="el-upload__tip">
            <!-- <a style="color: #409eff;" href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/99c7bdd6bf23bba6182ab18007cd38d4" rel="noopener noreferrer">模版下载</a> -->
          </div>
        </el-upload>
        <div style="background-color: #eee;padding: 10px;border-radius: 5px;margin: 10px 0;">
          <span style="font-size: 14px;color: red;">提示仅允许导入".zip"格式文件！请将同一个签名的附件，
            放在同一个文件夹下，并且文件夹要以签名命名。文件夹结构如下：</span>
          <el-image style="width: 300px; height: 100px;margin-top:10px"
            src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1742284189539.png"
            :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1742284189539.png']"
            :append-to-body="true"
            :preview-teleported="true">
          </el-image>
        </div>
        <el-upload class="upload-demo" :headers="token" :action="actionUrl" :limit="1" v-model:file-list="fileList1"
          :on-success="fileupres" :on-remove="fileup" :before-upload="beforeAvatarUploadc1" :on-progress="onProgress">
          <el-button size="default" type="primary" plain><el-icon style="font-size: 18px;">
              <UploadFilled />
            </el-icon>文件上传</el-button>
          <div slot="tip" class="el-upload__tip">
            仅支持.xlsx .xls 等格式
          </div>
        </el-upload>
        <!-- <file-upload style="display: inline-block" :action="actionUrl" :limit="1" :showfileList="true"
          :fileStyle="fileStyle" :del="del1" :istip="false" :tip="tip" @fileup="fileup" @fileupres="fileupres"
          @onProgress="onProgress">选择上传文件</file-upload> -->
        <div>
          <a style="margin: 10px; color: #409eff"
            href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/0ff0314a4f60fe7879a10be883755e53"
            rel="noopener noreferrer">模版下载</a>
        </div>
        <div v-if="Progressflag" style="text-align: center; font-size: 35px">
          <i class="el-icon-loading"></i>
        </div>
        <!-- <div style="margin-top: 10px" v-if="failNum != 0">
          <span style="font-size: 16px"> 失败数：</span
          ><span>{{ failNum }}</span>
        </div>
        <div
          style="margin-top: 10px; word-wrap: break-word"
          v-if="failNum != 0"
        >
          <span style="font-size: 16px">失败行数：</span
          ><span>{{ failRow }}</span>
        </div> -->
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="SignafileDialog = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 编辑标签结束 -->

      <!-- 详情的弹出框 -->
      <el-dialog title="签名详情" v-model="dialogVisible" width="940px" class="signsign">
        <el-form :inline="false" :model="reamInfoList" ref="reamInfoList" class="demo-ruleForm" label-width="140px">
          <el-form-item label="签名" prop="signature">
            <el-input disabled  placeholder="请输入企业名称" v-model="reamInfoList.signature"></el-input>
          </el-form-item>
          <el-form-item label="短信示例" prop="contentExample">
            <el-input disabled  placeholder="请输入企业名称" v-model="reamInfoList.contentExample"></el-input>
          </el-form-item>
          <el-form-item label="企业名称" prop="companyName">
            <el-input disabled  placeholder="请输入企业名称" v-model="reamInfoList.companyName"></el-input>
          </el-form-item>
          <el-form-item label="社会统一信用代码" prop="creditCode">
            <el-input disabled  placeholder="请输入统一社会信用代码" v-model="reamInfoList.creditCode"></el-input>
          </el-form-item>
          <el-form-item label="企业法人" prop="legalPerson">
            <el-input disabled  placeholder="请输入企业法人姓名" v-model="reamInfoList.legalPerson"></el-input>
          </el-form-item>
          <el-form-item label="责任人姓名" prop="principalName">
            <el-input disabled  placeholder="请输入负责人姓名" v-model="reamInfoList.principalName"></el-input>
          </el-form-item>
          <el-form-item label="责任人证件号码" prop="principalIdCard">
            <el-input disabled  placeholder="请输入负责人身份证号"
              v-model="reamInfoList.principalIdCard"></el-input>
          </el-form-item>
          <el-form-item label="责任人手机号" prop="principalMobile">
            <el-input disabled  placeholder="请输入负责人手机号" v-model="reamInfoList.principalMobile"></el-input>
          </el-form-item>
          <el-form-item label="签名来源" prop="signatureType">
            <el-radio-group disabled style="display: flex;flex-direction: column;align-items: self-start;"
              v-model="reamInfoList.signatureType">
              <el-radio style="margin-top: 10px;" :value="1">
                <span class="sig-type-title-tips">企业名称</span>
                <!-- （须提供营业执照图片） -->
              </el-radio>
              <el-radio style="margin-top: 10px;" :value="2">
                <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
              </el-radio>
              <el-radio style="margin-top: 10px;" :value="3">
                <span class="sig-type-title-tips">商标</span>
                （须提供商标注册证书图片或在在中国商标网的商标查询截图）
              </el-radio>
              <el-radio :value="4">
                <span class="sig-type-title-tips">App </span>
                （须提供app在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio :value="5">
                <span class="sig-type-title-tips">小程序</span>
                （须提供小程序在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <!-- <el-radio :value="6">
                <span class="sig-type-title-tips">公众号</span>
                （须提供小程序与主体公司存在关联关系的证明材图片）
              </el-radio> -->
              <!-- <el-radio :value="7">
                <span class="sig-type-title-tips">网站</span>
                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
              </el-radio> -->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签名类型" prop="signatureSubType">
            <el-radio-group disabled v-model="reamInfoList.signatureSubType">
              <el-radio :value="0">
                <span>全称</span>
              </el-radio>
              <el-radio :value="1">
                <span>简称</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="图片">
            <div v-if="reamInfoList.imgUrl">
              <el-image v-for="(item, index) in reamInfoList.imgUrl" style="width: 100px; height: 100px" :src="item" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
              :preview-src-list="[item]" show-progress :initial-index="4" fit="cover" :append-to-body="true"
              :preview-teleported="true" />
            </div>
            <div v-else>暂无图片</div>
            
          </el-form-item>
        </el-form>
        <!-- <div v-for="(item, index) in detailsList" :key="index" class="detailsList">
          <span class="detailsList-title">{{ item.title }}</span>
          <span class="detailsList-content">{{ item.content }}</span>
        </div>
        <div style="padding-left: 30px; font-size: 12px; margin-top: 20px">
          <div style="float: left">
            <span class="">{{ detailsLists.title }}</span>
          </div>
          <div style="display: inline-block" v-for="(item, index) in detailsLists.srcs" :key="index">
            <img :src="item.src" class="imgss" alt="" @click="openBanner" />
          </div>
        </div> -->
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false" style="padding: 9px 140px">知道了</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 轮播图弹窗  -->
      <el-dialog title="有效证件照" v-model="Signzhizhao" width="700px">
        <el-carousel arrow="always" width="700px" height="600px" :autoplay="false">
          <el-carousel-item v-for="(item, index) in detailsLists.srcs" :key="index" style="text-align: center">
            <a :href="item.src" target="_blank" rel="noopener noreferrer"><img :src="item.src" alt=""
                style="width: 700px" /></a>
          </el-carousel-item>
        </el-carousel>
      </el-dialog>
      <SignatureView v-if="autonymVisible" ref="SignatureRef" :signatureData="signatureData"
        @handleClose="closeSignature">
      </SignatureView>
      <el-dialog title="签名实名信息导入" v-model="SignaRealNameDialog" width="520px">
        <el-upload class="upload-demo" :headers="token" :action="this.API.omcs + 'consumerSignature/uploadRealName'" :limit="1"
          v-model:file-list="signaRealNameFileList" :on-success="signaRealNameSuccess" :on-remove="signaRealNameRemove"
          :before-upload="signaRealNameUpload" :on-error="signaRealNameError">
          <el-button size="default" type="primary" plain><el-icon style="font-size: 18px;">
              <UploadFilled />
            </el-icon>上传文件</el-button>
        </el-upload>
        <a href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/3011c93a30b5ed30b98edebe07c29cef" rel="noopener noreferrer">模版下载</a>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="SignaRealNameDialog = false">取 消</el-button>
            <el-button type="primary" @click="signaRealNameSubmit">提 交</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- <el-dialog
        title="短信实名明细"
        v-model="autonymVisible"
        width="700px"
      >
        <div class="table-box">
          <el-tabs v-model="operator" type="card" @tab-click="handleClick">
            <el-tab-pane name="1">
              <span slot="label"
                ><i class="iconfont icon-yidong" style="color: #409eff"></i>
                移动</span
              >
            </el-tab-pane>
            <el-tab-pane name="2">
              <span slot="label"
                ><i class="iconfont icon-liantong" style="color: #f56c6c"></i>
                联通</span
              >
            </el-tab-pane>
            <el-tab-pane label="电信" name="3">
              <span slot="label"
                ><i class="iconfont icon-dianxin" style="color: #0c36f2"></i>
                电信</span
              >
            </el-tab-pane>
          </el-tabs>
        </div>

        <el-form
          label-width="80px"
          :inline="true"
          :model="checkForm"
          class="demo-form-inline"
          ref="checkForm"
        >
          <el-form-item label="通道号" prop="channelCode">
            <el-input
              v-model="checkForm.channelCode"
              placeholder
              class="input-time"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain style @click="searchAutonym"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style
              @click="resetAutonym('checkForm')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-table
          :data="autonymData"
          style="width: 100%"
          border
          max-height="400px"
        >
          <el-table-column label="产品名称" prop="">
            <template  #default="scope">
              <span>短信 </span>
            </template>
          </el-table-column>
          <el-table-column label="通道号" prop="channelCode"></el-table-column>
          <el-table-column label="子端口">
            <template  #default="scope">
              <span> {{ scope.row.spCode }}</span>
            </template>
          </el-table-column>
          <el-table-column label="扩展号">
            <template  #default="scope">
              <span> {{ scope.row.ext }}</span>
            </template>
          </el-table-column>
          <el-table-column label="可用状态">
            <template  #default="scope">
              <el-tag
:disable-transitions="true" type="success" v-if="scope.row.status == 1">可用</el-tag>
              <el-tag
:disable-transitions="true" type="danger" v-else-if="scope.row.status == 2"
                >未生成</el-tag
              >
              <el-tag
:disable-transitions="true" type="warning" v-else-if="scope.row.status == 0"
                >实名中</el-tag
              >
            </template>
          </el-table-column>
        </el-table>
        <template #footer>
          <el-button @click="autonymVisible = false">取 消</el-button>
        </template>
      </el-dialog> -->
    </div>
  </div>
</template>
<script>
import TableTem from "../../../../publicComponents/TableTem.vue";
import { formatDate } from "@/assets/js/date.js";
import FileUpload from "@/components/publicComponents/FileUpload.vue"; //文件上传
import Tooltip from "@/components/publicComponents/tooltip.vue";
import SignatureView from "@/components/publicComponents/signatureDetail.vue";
import moment from "moment";
export default {
  name: "SignatureMag",
  components: {
    TableTem,
    FileUpload,
    Tooltip,
    SignatureView,
  },
  data() {
    return {
      actionUrl: window.path.omcs + 'consumerSignature/upload',
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      token: {},
      fileListUlr: [],
      zipfileList: [],
      fileList1: [],
      signaRealNameFileList:[],
      dialogImageUrl: "",
      SignaRealNameDialog: false,
      isFirstEnter: false,
      imgUrlvisible: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      SignafileDialog: false,
      Progressflag: false,
      autonymVisible: false,
      signatoryId: "", //签名id
      operator: "1", //运营商
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls 等格式",
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls"],
      },
      failNum: "",
      failRow: "",
      dialogVisible: false,
      detailsLists: {
        title: "",
        srcs: [],
      },
      // autonymData: [],
      signatureNum: "",
      subServiceTypeList: [], //营销类型列表
      // fileList2: [],上传列表
      titleMap: {
        add: "报备签名",
        edit: "编辑签名",
      },
      dialogStatus: "", //新增编辑标题
      selectId: "", //批量操作选中id
      // checkForm: {
      //   channelCode: "",
      //   userId: "",
      // },
      signatureData: {
        signatoryId: "",
        userId: "",
        userName: "",
        productId: "1",
      },
      //查询表单
      formInline: {
        consumerName: "",
        signature: "",
        signatureAttr: "",
        createName: "",
        auditUsername: "",
        //时间
        dataCreat: [],
        createStartTime: "",
        createEndTime: "",
        auditdataCreat: [],
        auditStartTime: "",
        auditEndTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        consumerName: "",
        signature: "",
        signatureAttr: "",
        createName: "",
        auditUsername: "",
        //时间
        dataCreat: [],
        createStartTime: "",
        createEndTime: "",
        auditdataCreat: [],
        auditStartTime: "",
        auditEndTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      Signzhizhao: false, //轮播图弹出框显示隐藏
      // dialogFormVisible: false, //新增弹出框显示隐藏
      dialogFormVisiblebj: false, //编辑弹出框显示隐藏
      signatureId: "", //列表行id
      detailsRow: {}, //详情存储
      formop: {
        name: "",
      },
      formops: {
        consumerName: "",
        signature: "",
        signatureType: "",
        remark: "",
      },
      formopss: {
        consumerName: "",
        signature: "",
        serviceType: "",
        contentExample: "",
        subServiceType: "",
        companyName: "",//公司名称
        creditCode: "",//统一社会信用代码
        legalPerson: "",//法人姓名
        principalIdCard: "",//负责人身份证号
        principalName: "",//负责人姓名
        principalMobile: "",//负责人手机号
        imgUrl: "",//上传文件
        signatureType: 1,
        signatureSubType: 0,
        fileList: []
      },
      reamInfoList: {},
      rules: {
        consumerName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        signature: [
          { required: true, message: "请输入签名内容", trigger: "blur" },
        ],
        // signatureType: [
        //   { required: true, message: "请选择签名类型", trigger: "change" },
        // ],
        serviceType: [
          { required: true, message: "请选择短信类型", trigger: "change" },
        ],
        subServiceType: [
          { required: true, message: "请选择营销类型", trigger: "change" },
        ],
        contentExample: [
          { required: true, message: "请输入短信示例", trigger: "change" },
        ],
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'change' },
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        ],
        legalPerson: [
          { required: true, message: '请输入法人姓名', trigger: 'change' },
        ],
        principalIdCard: [
          { required: true, message: '请输入负责人身份证号', trigger: 'change' },
        ],
        principalName: [
          { required: true, message: '请输入负责人姓名', trigger: 'change' },
        ],
        principalMobile: [
          { required: true, message: '请输入负责人手机号', trigger: 'change' },
        ],
        imgUrl: [
          { required: true, message: '请上传文件', trigger: 'change' },
        ],
        signatureType: [
          { required: true, message: '请选择签名来源', trigger: 'change' },
        ],
        signatureSubType: [
          { required: true, message: '请选择签名类型', trigger: 'change' },
        ],
      },


      tableDataObj: {
        loading2: false,
        total: 0,
        //列表数据
        tableData: [],
        // tableLabel: [
        //   // {
        //   //   prop: "signatureId",
        //   //   showName: "签名ID",
        //   //   width:'70px',
        //   //   fixed: false
        //   // },
        //   {
        //     prop: "consumerName",
        //     showName: "用户名",
        //     fixed: false,
        //     width: '150px'
        //   },
        //   {
        //     prop: "signature",
        //     showName: "签名",
        //     width: '200px',
        //     fixed: false
        //   },
        //   {
        //     prop: "serviceType",
        //     showName: "短信类型",
        //     width: '90px',
        //     fixed: false,
        //     formatData: function (val) {
        //       if (val == 1) {
        //         return (val = "验证码")
        //       }
        //       if (val == 2) {
        //         return (val = "通知")
        //       }
        //       if (val == 3) {
        //         return (val = "营销")
        //       }
        //     },
        //   },
        //   {
        //     prop: "idx",
        //     showName: "序号",
        //     fixed: false,
        //     width: '80px'
        //   },
        //   {
        //     prop: "signatureAttr",
        //     showName: "签名属性",
        //     width: '90px',
        //     fixed: false,
        //     formatData: function (val) {
        //       if (val == 1) {
        //         return (val = "报备签名")
        //       }
        //       if (val == 2) {
        //         return (val = "自定义签名")
        //       }

        //     }
        //   },
        //   {
        //     prop: "signatureType",
        //     showName: "签名类型",
        //     fixed: false,
        //     width: '170px',
        //     formatData: function (val) {
        //       if (val == 1) {
        //         return (val = "公司名全称或简称")
        //       }
        //       if (val == 2) {
        //         return (val = "APP名全称或简称")
        //       }
        //       if (val == 3) {
        //         return (val = "工信部备案的网站名全称或简称")
        //       }
        //       if (val == 4) {
        //         return (val = "公众号或小程序名全称或简称")
        //       }
        //       if (val == 5) {
        //         return (val = "商标全称或简称")
        //       }
        //       if (val == 6) {
        //         return (val = "其他")
        //       }
        //     }
        //   },
        //   {
        //     prop: "createName",
        //     showName: "创建人",
        //     fixed: false,
        //     width: '140px'
        //   },
        //   {
        //     prop: "createTime",
        //     showName: "创建时间",
        //     fixed: false,
        //     width: '170px',
        //     formatData: function (val) {
        //       return formatDate(new Date(val * 1), 'YYYY-MM-DD HH:mm:ss');
        //     }
        //   },
        //   {
        //     prop: "auditUsername",
        //     showName: "审核人",
        //     fixed: false,
        //     width: '140px'
        //   },
        //   {
        //     prop: "auditTime",
        //     showName: "审核时间",
        //     fixed: false,
        //     width: '170px',
        //     formatData: function (val) {
        //       return formatDate(new Date(val * 1), 'YYYY-MM-DD HH:mm:ss');
        //     }
        //   }
        // ],
        // tableStyle: {
        //   isSelection: true, //是否复选框
        //   // height:250,//是否固定表头
        //   isExpand: false, //是否是折叠的
        //   style: {
        //     //表格样式,表格宽度
        //     width: "100%"
        //   },
        //   optionWidth: "180", //操作栏宽度
        //   border: true, //是否边框
        //   stripe: false //是否有条纹
        // },
        // tableOptions: [
        //   {
        //     optionName: "详情",
        //     type: "",
        //     size: "mini",
        //     optionMethod: "details",
        //     icon: "el-icon-circle-check-outline"
        //   },
        //   // {
        //   //   optionName: "编辑",
        //   //   type: "",
        //   //   size: "mini",
        //   //   optionMethod: "edit",
        //   //   icon: "el-icon-edit"
        //   // },
        //   {
        //     optionName: "删除",
        //     type: "",
        //     size: "mini",
        //     optionMethod: "dellog",
        //     icon: "el-icon-delete",
        //     color: "#F56C6C"
        //   }
        // ]
      },

      //详情列表
      detailsList: [
        {
          title: "ID",
          content: "",
        },
        {
          title: "用户名",
          content: "",
        },
        {
          title: "签名",
          content: "",
        },
        {
          title: "签名属性",
          content: "",
        },
        {
          title: "签名来源",
          content: "",
        },
        {
          title: "创建人",
          content: "",
        },
        {
          title: "创建时间",
          content: "",
        },
        {
          title: "审核人",
          content: "",
        },
        {
          title: "审核时间",
          content: "",
        },
        {
          title: "备注内容",
          content: "",
        },
      ],
    };
  },
  methods: {
    //打开轮播图
    openBanner() {
      this.Signzhizhao = true;
    },
    // //上传
    // beforeAvatarUpload(file) {
    //   const isJPG = (file.type === "image/jpeg") | "image/png";
    //   const isLt5M = file.size / 1024 / 1024 < 5;

    //   if (!isJPG) {
    //     this.$message.error("上传图片只能是 JPG PNG格式!");
    //   }
    //   if (!isLt5M) {
    //     this.$message.error("上传图片大小不能超过 5MB!");
    //   }
    //   return isJPG && isLt5M;
    // },
    // handleRemove(file, fileList) {
    //   console.log(file, fileList);
    // },
    // handlePreview(file) {
    //   console.log(file);
    // },
    //*********************************************签名列表信息 */
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "consumerSignature/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.total = res.total;
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    getSeverType() {
      window.api.post(
        window.path.omcs + "signatureaudit/allSubServiceType",
        {},
        (res) => {
          if (res.code == 200) {
            this.subServiceTypeList = res.data; //营销类型列表
          } else {
            this.$message({
              type: "error",
              duration: "2000",
              message: res.msg,
            });
          }
        }
      );
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + "consumerSignature/report",
            this.formopss,
            (res) => {
              if (res.code == 400) {
                this.$message({
                  message: res.msg,
                  type: "warning",
                });
              } else {
                this.$message({
                  message: res.msg,
                  type: "success",
                });
                this.dialogFormVisible = false;
                this.gettableLIst();
              }
            }
          );
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //移除文件
    fileup(val) {
      this.Progressflag = false;
      this.failNum = "";
      this.failRow = "";
    },
    onProgress(val, val1, val2, val3) {
      this.Progressflag = true;
    },
    //文件上传成功
    fileupres(val, val2) {
      this.Progressflag = false;
      if (val.code != 200) {
        this.$message({
          message: val.msg,
          showClose: true,
          type: "error",
          duration: 0,
        });
        // this.failNum = val.data.failNum;
        // this.failRow = val.data.failRow;
      } else {
        this.$message({
          message: "签名导入成功！",
          type: "success",
        });
        this.gettableLIst();
        this.SignafileDialog = false;
      }
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    //列表复选框的值
    handelSelection(val) {
      if (val.records.length > 0) {
        let arr = []
        val.records.forEach(item => {
          arr.push(item.signatureId)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    hande: function (val) {
      if (val) {
        //获取查询时间框的值
        this.formInline.createStartTime = this.moment(val[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.formInline.createEndTime = this.moment(val[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.formInline.createStartTime = "";
        this.formInline.createEndTime = "";
      }
    },
    handes: function (val) {
      if (val) {
        //获取查询时间框的值
        this.formInline.auditStartTime = this.moment(val[0]).format(
          "YYYY-MM-DD "
        );
        this.formInline.auditEndTime = this.moment(val[1]).format(
          "YYYY-MM-DD "
        );
      } else {
        this.formInline.auditStartTime = "";
        this.formInline.auditEndTime = "";
      }
    },
    /*******************************************列表方法 */
    //查询
    Query() {
      // console.log(111);
      Object.assign(this.tabelAlllist, this.formInline);

      this.gettableLIst();
    },
    // 导出数据
    // download() {},
    //重置关闭
    Reload() {
      this.$refs.formInline.resetFields();
      this.formInline.createStartTime = "";
      this.formInline.createEndTime = "";
      this.formInline.auditStartTime = "";
      this.formInline.auditEndTime = "";
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    /***************
     *
     * 报备签名
     */
    // submitForm(formop) {
    //   this.$refs[formop].validate(valid => {
    //     if (valid) {
    //       alert("submit!");
    //       this.dialogFormVisible = false;
    //     } else {
    //       console.log("error submit!!");
    //       return false;
    //     }
    //   });
    // },
    // resetForm(formop) {
    //   this.dialogFormVisible = false;
    // },
    addopt() {
      // this.dialogFormVisible = true;
      // this.dialogStatus = "add";
      this.dialogFormVisible = true;
      this.getSeverType();
    },

    //编辑提交
    submitForms(formops) {
      this.$refs[formops].validate((valid) => {
        if (valid) {
          this.formops.signatureId = this.signatureId;
          this.$confirms.confirmation(
            "put",
            "确认执行此操作吗？",
            window.path.omcs + "consumerSignature",
            this.formops,
            () => {
              this.dialogFormVisiblebj = false;
            }
          );
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },

    //批量删除
    delAll(val) {
      //批量删除
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据, 是否继续？",
        window.path.omcs + "consumerSignature",
        {
          ids: this.selectId,
        },
        () => {
          this.gettableLIst();
        }
      );
    },
    rouTz(val, index) {
      // console.log(val);
      this.$router.push({ path: "/UserDetail", query: { id: val.userId } });
    },
    getIdX(val) {
      console.log(val, "vla");
      this.$confirms.confirmation(
        "put",
        `是否清空${val.consumerName}${val.signature}的序号${val.idx}！`,
        window.path.omcs + "consumerSignature/idxClean/" + val.signatureId,
        {},
        () => {
          this.gettableLIst();
        }
      );
    },
    handelOptionButton: function (methods, row, index) {
      // console.log(val,'val');
      if (methods == "edit") {
        //编辑
        this.dialogFormVisiblebj = true;
        this.dialogStatus = "edit";
        this.$nextTick(() => {
          this.$refs.formops.resetFields();
          Object.assign(this.formops, row); //编辑框赋值
        });
        this.signatureId = row.signatureId;
      }
      if (methods == "details") {
        this.dialogVisible = true;
        window.api.get(
          window.path.omcs + "consumerSignature/realName?userId=" + row.userId + "&signatureId=" + row.signatureId,
          {},
          (res) => {
            if (res.code == 200) {
              this.reamInfoList = res.data; //实名信息列表
              let fileList = this.reamInfoList.imgUrl.split(",").filter(item => item.trim() !== '');
              this.reamInfoList.imgUrl = fileList.map(item => {
                return window.path.imgU + item
              })
            } else {
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
            // this.tableDataObj.total = res.total;
            // this.tableDataObj.tableData = res.records;
            // this.tableDataObj.loading2 = false;
          }
        );
        //详情
        // this.detailsLists.srcs = [];
        // this.detailsLists.title = "";
        // this.dialogVisible = true;
        // // 详情赋值
        // this.detailsRow = row;
        // let imgUrlArr = [];
        // if (this.detailsRow.imgUrl != null && this.detailsRow.imgUrl != "") {
        //   imgUrlArr = row.imgUrl.split(",");
        //   for (let i = 0; i < imgUrlArr.length; i++) {
        //     this.detailsLists.srcs.push({ src: window.path.imgU + imgUrlArr[i] });
        //   }
        //   this.detailsLists.title = "资料图片";
        // } else {
        //   imgUrlArr = [];
        // }

        // this.detailsList[0].content = this.detailsRow.signatureId;
        // this.detailsList[1].content = this.detailsRow.consumerName;
        // this.detailsList[2].content = this.detailsRow.signature;
        // if (this.detailsRow.signatureAttr == 1) {
        //   this.detailsList[3].content = "报备签名";
        // }
        // if (this.detailsRow.signatureAttr == 2) {
        //   this.detailsList[3].content = "自定义签名";
        // }

        // if (this.detailsRow.signatureType == 1) {
        //   this.detailsList[4].content = "企业名称";
        // }
        // if (this.detailsRow.signatureType == 2) {
        //   this.detailsList[4].content = "事业单位：如机关，学校，科研单位，街道社区等";
        // }
        // if (this.detailsRow.signatureType == 3) {
        //   this.detailsList[4].content = "商标";
        // }
        // if (this.detailsRow.signatureType == 4) {
        //   this.detailsList[4].content = "产品：包括app、小程序、平台等";
        // }
        // // if (this.detailsRow.signatureType == 5) {
        // //   this.detailsList[4].content = "商标全称或简称";
        // // }
        // // if (this.detailsRow.signatureType == 6) {
        // //   this.detailsList[4].content = "其他";
        // // }
        // this.detailsList[5].content = this.detailsRow.createName;
        // this.detailsList[6].content = this.detailsRow.createTime;
        // this.detailsList[7].content = this.detailsRow.auditUsername;
        // this.detailsList[8].content = this.detailsRow.auditTime;
        // this.detailsList[9].content = this.detailsRow.remark;
      }
      if (methods == "dellog") {
        //删除
        this.$confirms.confirmation(
          "delete",
          "此操作将永久删除该数据, 是否继续？",
          window.path.omcs + "consumerSignature",
          {
            ids: row.signatureId,
          },
          () => {
            this.gettableLIst();
          }
        );
      }
    },
    // searchAutonym() {
    //   this.handleClick();
    // },
    // resetAutonym() {
    //   this.checkForm.channelCode = "";
    //   this.handleClick();
    // },
    // handleClick() {
    //   window.api.post(
    //     window.path.omcs + "consumerSignature/queryRealNameInfo",
    //     {
    //       userId: this.checkForm.userId,
    //       signature: this.signatoryId,
    //       operator: this.operator,
    //       channelCode: this.checkForm.channelCode,
    //       productId: "1",
    //     },
    //     (res) => {
    //       if (res.code == 200) {
    //         this.autonymVisible = true;
    //         this.autonymData = res.data;
    //       } else {
    //         this.autonymData = [];
    //         this.$message({
    //           message: res.msg,
    //           type: "warning",
    //         });
    //       }
    //     }
    //   );
    // },
    getSignatureTypeText(type){
      const typeMap = {
        1: '企业名称',
        2: '事业单位',
        3: '商标',
        4: 'APP',
        5: '小程序',
        // 6: '公众号',
        7: '网站'
      }
      return typeMap[type] || ''
    },
    getTagType(content) {
      const trimmed = content.trim();
      if (trimmed === '验证码') {
        return 'success';
      } else if (trimmed === '行业') {
        return 'success';
      } else if (trimmed === '通知') {
        return 'success';
      }
      return 'success'; // 默认蓝色
    },
    handelCheck(row) {
      this.signatureData.signatoryId = row.signature;
      this.signatureData.userId = row.userId;
      this.signatureData.userName = row.consumerName;
      this.autonymVisible = true;
      // this.checkForm.userId = row.userId;
      // this.signatoryId = row.signature;
      // this.handleClick();
      // window.api.post(
      //   window.path.omcs + "consumerSignature/queryRealNameInfo",
      //   {
      //     signatoryId: row.userId,
      //     operator: this.operator,
      //     channelCode: this.channelCode,
      //   },
      //   (res) => {
      //     if (res.code == 200) {
      //       this.autonymVisible = true;
      //       this.autonymData = res.data;
      //     } else {
      //       this.$message({
      //         message: res.msg,
      //         type: "error",
      //       });
      //     }
      //   }
      // );
    },
    closeSignature(data) {
      this.autonymVisible = data;
    },
    handleSuccess(res) {
      if (res.code == 200) {
        this.fileListUlr.push(res.data.fullpath);
        this.formopss.imgUrl = this.fileListUlr.join(",");
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove1(file, fileList) {
      if (file.response) {
        this.fileListUlr.splice(this.fileListUlr.indexOf(file.response.data.fullpath), 1);
        this.formopss.imgUrl = this.fileListUlr.join(",");
      } else {
        this.fileListUlr.splice(this.fileListUlr.indexOf(file.name), 1);
        this.formopss.imgUrl = this.fileListUlr.join(",");
      }

    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.imgUrlvisible = true
    },
    handleSuccessZip(res) {
      console.log(res);
      if (res.code == 200) {
        let timeStamp = new Date().getTime();
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = 'data:application/octet-stream;base64,' + res.data
        link.download = timeStamp + '.xlsx'//下载后文件名
        link.click()
      }

    },
    handleRemoveZip(file, fileList) {
      this.fileListZip = [];
    },
    beforeAvatarUploadc(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const fileType = [
        "zip",
      ];
      const isLt5M = file.size / 1024 / 1024 < 20;
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning(
          "上传文件只能是zip格式!"
        );
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传缩略图大小不能超过20M！");
        return false;
      }
    },
    beforeAvatarUploadc1(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const fileType = [
        "xlsx", 'xls'
      ];
      // const isLt5M = file.size / 1024 / 1024 < 20;
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning(
          "上传文件只能是xlsx,xls格式!"
        );
        return false;
      }
    },
    signaRealNameUpload(file) {
      console.log(file)
      //仅支持上传xlsx,xls格式
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const fileType = [
        "xlsx", 'xls'
      ];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning(
          "上传文件只能是xlsx,xls格式!"
        );
        return false;
      }
    },
    signaRealNameSuccess(res) {
      console.log(res)
      if (res.code == 200) {
        this.$message.success("上传成功");
      } else {
        this.$message.error(res.msg);
      }
    },
    signaRealNameRemove(file, fileList) {
      console.log(file, fileList)
      this.signaRealNameFileList = [];
    },
    signaRealNameError(err, file, fileList) {
      console.log(err, file, fileList)
      this.$message.error("上传失败");
    },
    signaRealNameSubmit() {
     this.gettableLIst();
     this.SignaRealNameDialog = false;
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.token = {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      }
      this.gettableLIst();
      this.getSeverType();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
        this.gettableLIst();
        this.getSeverType();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist:{
    //     handler(){
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate:true //页面初始监听
    // },
    //监听弹框是否关闭
    dialogFormVisiblebj(val) {
      if (!val) {
        this.$refs.formops.resetFields();
      }
    },
    dialogFormVisible(val) {
      if (!val) {
        this.formopss.consumerName = "";
        this.formopss.signature = "";
        this.formopss.signatureType = "";
        this.formopss.companyName = "";
        this.formopss.creditCode = "";
        this.formopss.legalPerson = "";
        this.formopss.principalIdCard = "";
        this.formopss.principalMobile = "";
        this.formopss.principalName = "";
        this.formopss.imgUrl = "";
        this.formopss.signatureType = "";
        this.formopss.signatureSubType = "";
        this.formopss.fileList = [];
        this.fileListUlr = []
        this.$refs.formopss.resetFields();
      }
    },
    SignafileDialog(val) {
      if (!val) {
        // this.del1 = true;
        this.fileList1 = [];
        this.zipfileList = [];
      }
    },
    dialogVisible(val){
      if (!val) {
        this.reamInfoList = {}
      }
    },
    SignaRealNameDialog(val) {
      if (!val) {
        this.signaRealNameFileList = [];
      }
    },
    // autonymVisible(val) {
    //   if (!val) {
    //     this.autonymData = [];
    //     this.operator = "1";
    //     this.checkForm.userId = "";
    //     this.checkForm.channelCode = "";
    //   }
    // },
  },
};
</script>



<style lang="less" scoped>
.detailsList {
  /* height: 42px; */
  line-height: 42px;
  padding-left: 30px;
  font-size: 12px;
}

.detailsList-title {
  display: inline-block;
  width: 80px;
}

.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
  word-break: break-all;
}

.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter>div {
  height: 26px;
  line-height: 26px;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.sensitive {
  /* border-bottom: 1px solid #ccc; */
  margin: 40px 0 20px 10px;
}

:deep(.el-textarea__inner) {
  height: 80px;
}

.table-box :deep(.el-tabs__item) {
  width: 150px;
  text-align: center;
}

/* .boderbottom{
    border-bottom: 1px solid rgb(212, 211, 211);
    height: 50px;
} */
</style>
<style>
.imgss {
  width: 100px;
  height: 150px;
  margin-left: 15px;
  cursor: pointer;
}

.bkg input {
  background: #f2f2f2 !important;
}

.banban .el-dialog {
  height: 100%;
}

.signsign .el-dialog__footer {
  text-align: center;
}

.el-dialog__title {
  font-size: 16px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}

/* 运营商实名状态样式优化 */
.operator-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 13px;
}

.single-operator-status {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 4px 2px;
}

.status-tags-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  width: 100%;
}

.status-tag {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: auto !important;
  line-height: 1.3 !important;
  border-radius: 4px !important;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 !important;
}

.status-tag.fixed-width {
  width: 48px !important;
  min-width: 48px !important;
  max-width: 48px !important;
}

.status-tag.success {
  font-weight: 500 !important;
}

.status-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 不同类型的tag颜色 */
.status-tag.el-tag--primary {
  background-color: #ecf5ff !important;
  border-color: #b3d8ff !important;
  color: #409eff !important;
}

.status-tag.el-tag--warning {
  background-color: #fdf6ec !important;
  border-color: #f5dab1 !important;
  color: #e6a23c !important;
}

.status-tag.el-tag--success {
  background-color: #f0f9ff !important;
  border-color: #c6f6d5 !important;
  color: #67c23a !important;
}

.status-tag.el-tag--info {
  background-color: #f4f4f5 !important;
  border-color: #d3d4d6 !important;
  color: #909399 !important;
}
</style>