<template>
  <div>
    <div class="Top_title">
      <span
        style="
          display: inline-block;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><el-icon><el-icon-arrow-left /></el-icon> 返回</span
      >|
      <span style="display: inline-block; padding-left: 6px">查看访问详情</span>
    </div>
    <div class="fillet Statistics-box">
      <div class="sensitive-fun">
        <span class="sensitive-list-header">访问量列表</span>
        <span style="font-weight: 400; color: #16a085"
          >访问量：共 {{ oriNum }} 次</span
        >
        <div style="text-align: right">
          <el-date-picker
            v-model="valueData"
            type="daterange"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="Mail-table" style="padding-bottom: 40px">
        <el-table :data="tableDataObj.tableData" border style="width: 100%">
          <el-table-column type="index" width="60" label="序号">
          </el-table-column>
          <el-table-column
            prop="shortLinkTime"
            label="发送时间"
          ></el-table-column>
          <el-table-column prop="happenTime" label="访问时间"></el-table-column>
          <el-table-column prop="count" label="访问次数"></el-table-column>
        </el-table>
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            >
            </el-pagination>
          </el-col>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
export default {
  components: {
    DatePlugin,
    ElIconArrowLeft,
  },
  name: 'reportForm',
  data() {
    return {
      name: 'reportForm',
      oriNum: '', //访问次数
      // 日期选择
      valueData: '',
      beginTime: '',
      endTime: '',
      formInlines: {
        //分页
        currentPage: 1,
        pageSize: 10,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'shortlink/getsldetaillist',
        {
          shortCode: this.$route.query.code,
          userId: this.$route.query.id,
          countType: 3,
          beginTime: this.beginTime,
          endTime: this.endTime,
          pageSize: this.formInlines.pageSize,
          currentPage: this.formInlines.currentPage,
        },
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.datalist.records
          this.tableDataObj.total = res.data.datalist.total
          this.oriNum = res.data.openurlcount
        }
      )
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
    //返回
    goBack() {
      this.$router.push({ path: '/shortChainAnalysis' })
    },
  },
  mounted() {
    this.InquireList()
  },
  watch: {
    // 监听日期
    valueData: function (val) {
      if (val == null) {
        this.beginTime = ''
        this.endTime = ''
      } else {
        this.beginTime = val[0] + ' 00:00:00'
        this.endTime = val[1] + ' 23:59:59'
      }
      this.InquireList()
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>
