/*------------------
     Utility
-------------------*/
@import "mixin";
.clearfix {
    .clearfix();
}
.border-box {
    .border-box();
}
.hide {
    display: none;
}
.show {
    display: block;
}
.floated {
    &.left {
        float: left
    }
    &.right {
        float: right;
    }
    &.none {
        float: none;
    }
    &.clear {
        clear: both;
    }
}
.wrap {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
}
.aligned {
    &.left {
        text-align: left;
    }
    &.right {
        text-align: right;
    }
    &.center {
        text-align: center;
    }
}
.vm {
    vertical-align: middle;
}
.ui-bottom-coating {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 34px;
    padding: 10px 0;
    text-align: center;
    z-index: 400;

    .coating-opacity {
        background-color: #dddddd;
        opacity: 0.8;
        filter: alpha(opacity=80);
        height: 100%;
        width: 100%;
        padding: 10px 0;
    }
    .btn {
        position: absolute;
        z-index: 2;
        top: 16px;
        left: 50%;
        margin-left: -59px;
        padding-left: 30px;
        padding-right: 30px;
        outline: none;
    }
}