<template>
  <div class="container_left">
    <div class="Templat-matter">
      <p style="font-weight: bolder">tips：自动审核的注意事项</p>
      <p>1、【客户资质】：直客终端</p>
      <p>2、【模板类型】：行业通知</p>
      <p>3、【模板签名】：完全匹配</p>
      <p>4、【模板数量】：每个客户最多300个模板</p>
    </div>
    <div style="padding: 10px">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="100px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称" prop="compId">
              <el-select
                ref="optionRef"
                class="input-w"
                v-model="sensitiveCondition.compId"
                clearable
                filterable
                remote
                :remote-method="remoteMethod"
                :loading="loadingcomp"
                placeholder="请选择公司名称"
              >
                <el-option
                  v-for="(item, index) in compNamelist"
                  :key="index"
                  :label="item.company"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <!-- <el-input class="input-w" v-model="addUserStep1Form.formData.compName"></el-input> -->
            </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input
              v-model="sensitiveCondition.content"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" prop="status">
            <el-select class="input-w" v-model="sensitiveCondition.status">
              <el-option label="审核成功" value="1"> </el-option>
              <el-option label="审核不通过" value="2"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="触发次数区间" label-width="120px">
            <el-input
              style="width: 100px"
              v-model="sensitiveCondition.minNum"
              placeholder="最小值"
            ></el-input>
            &nbsp;-&nbsp;
            <el-input
              style="width: 100px"
              v-model="sensitiveCondition.maxNum"
              placeholder="最大值"
            ></el-input>
          </el-form-item>
          <el-form-item label="相似度区间" label-width="120px">
            <el-input
              style="width: 100px"
              v-model="sensitiveCondition.minPercent"
              placeholder="最小值"
            ></el-input>
            &nbsp;-&nbsp;
            <el-input
              style="width: 100px"
              v-model="sensitiveCondition.maxPercent"
              placeholder="最大值"
            ></el-input>
          </el-form-item>
          
          <el-form-item label="更新时间" prop="time">
            <!-- <el-date-picker
                    class="input-w"
                    v-model="sensitiveCondition.time"
                    value-format="YYYY-MM-DD"
                    type="datetimerange"
                    range-separator="-"
                    :clearable="true"
                    @change="getTimeOperating"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker> -->
            <el-date-picker
              v-model="sensitiveCondition.time"
              @change="getTimeOperating"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>

          <div>
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div>
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addClientsmssimilar"
              >添加自动审核规则</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="clientsmssimilar"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px"
          :data="tableDataObj.tableData"
        >
          <vxe-column field="ID" title="ID" width="90">
            <template v-slot="scope">
              <div>{{ scope.row.id }}</div>
            </template>
          </vxe-column>
          <vxe-column field="用户名" title="用户名" width="120">
            <template v-slot="scope">
              <div
                :style="
                  scope.row.mobCount > 99 ? 'color:#f56c6c' : 'color:#16A589;'
                "
              >
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="公司主体" title="公司主体" width="180">
            <template v-slot="scope">
              <div>{{ scope.row.companyName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="签名" title="签名" width="120">
            <template v-slot="scope">
              <div>{{ scope.row.signature }}</div>
            </template>
          </vxe-column>
          <vxe-column field="模板内容" title="模板内容" min-width="450">
            <template v-slot="scope">
              <div style="display: flex">
                <div>
                  <span class="spanColor" v-html="scope.row.content"></span>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="关键字" title="关键字" width="180">
            <template v-slot="scope">
              <div>{{ scope.row.keywords }}</div>
            </template>
          </vxe-column>
          <vxe-column field="相似度" title="相似度" width="80">
            <template v-slot="scope">
              <div>{{ scope.row.percent }}%</div>
            </template>
          </vxe-column>
          <vxe-column field="触发次数" title="触发次数" width="80">
            <template v-slot="scope">
              <div>{{ scope.row.num }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="更新时间" title="更新时间" width="170">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="审核状态" title="审核状态" width="90">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.auditStatus == '2'" style="color: #f56c6c"
                  >审核不通过</span
                >
                <span v-else style="color: #409eff">审核通过</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <div>
                <el-button
                  link
                  style="color: #409eff"
                  @click="editClientsmssimilar(scope.row)"
                  ><el-icon style="margin-right: 3px"><EditPen /></el-icon
                  >编辑</el-button
                >
                <el-button
                  link
                  style="color: red"
                  @click="delClientsmssimilar(scope.row)"
                  ><el-icon style="margin-right: 3px"><Delete /></el-icon
                  >删除</el-button
                >
              </div>
              <div>
                <el-button
                  link
                  :style="scope.row.compId?'color: #e6a23c':'color: #409eff'"
                  @click="changeSubjectStatus(scope.row)"
                  ><el-icon style="margin-right: 3px"><Switch /></el-icon
                  >{{scope.row.compId?"不支持主体共享":"支持主体共享"}}</el-button
                >
              </div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <div
          style="display: flex; justify-content: space-between; padding: 10px"
        >
          <div></div> -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      :close-on-click-modal="false"
      width="40%"
    >
      <!-- <el-steps :active="active" finish-status="success">
            <el-step title="步骤 1"> </el-step>
            <el-step title="步骤 2"> </el-step>
          </el-steps> -->
      <!-- <div class="first" v-if="active == 0">
            <div class="form_first">
              <el-form
                :model="formop"
                :rules="rules"
                label-width="100px"
                ref="formop"
                style="padding: 0 28px 0 20px"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input
                    style="width: 400px"
                    v-model="formop.username"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="模板内容" prop="content">
                  <el-input
                    type="textarea"
                    style="width: 400px; height: 100px"
                    v-model="formop.content"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="关键字" prop="keywords">
                  <el-input
                    style="width: 400px"
                    v-model="formop.keywords"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div> -->
      <div class="first">
        <!-- <div>
              <div>
                <el-descriptions
                  title="相似度信息"
                  direction="vertical"
                  :column="4"
                  border
                >
                  <el-descriptions-item label="用户名">{{
                    formop.username
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    :contentStyle="{ width: '300px' }"
                    label="模板内容"
                    >{{ formop.content }}</el-descriptions-item
                  >
                  <el-descriptions-item label="参考相似度">
                    <span style="color: red">
                      {{ tableDatasimilar.minPercent }}% -
                      {{ tableDatasimilar.maxPercent }}%
                    </span>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div class="title">相似度列表</div>
              <el-table
                v-loading="tableDatasimilar.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="tableDatasimilar.similarList"
                style="width: 100%"
              >
                <el-table-column label="用户名" width="120">
                  <template #default="scope">
                    <span>{{ scope.row.username }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="签名" width="120">
                  <template #default="scope">
                    <span>{{ scope.row.signature }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="模板内容" min-width="300">
                  <template #default="scope">
                    <div style="display: flex">
                      <div>
                        <span class="spanColor" v-html="scope.row.content"></span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="相似度">
                  <template #default="scope">
                    <span>{{ scope.row.percent }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div> -->
        <!-- <div class="title">自定义相似度</div> -->
        <div>
          <el-form
            :model="similarFormop"
            :rules="similaRrules"
            label-width="140px"
            ref="similarFormop"
            style="padding: 0 28px 0 20px"
          >
            <el-form-item label="用户名" prop="username">
              <el-input
                style="width: 300px"
                :disabled="id ? true : false"
                v-model="similarFormop.username"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="模板内容" prop="content">
              <el-input
                :disabled="id ? true : false"
                type="textarea"
                style="width: 300px; height: 110px"
                v-model="similarFormop.content"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="关键字" prop="keywords">
              <el-input
                style="width: 300px"
                v-model="similarFormop.keywords"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select
                style="width: 300px"
                v-model="similarFormop.auditStatus"
                placeholder="请选择"
              >
                <el-option label="审核通过" value="1"> </el-option>
                <el-option label="审核不通过" value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="相似度" prop="percent">
              <!-- <el-input
                style="width: 300px"
                placeholder="相似度为正整数，请输入正确数字"
                v-model="similarFormop.percent"
                autocomplete="off"
              ></el-input> -->
              <el-input-number
                style="width: 300px"
                v-model="similarFormop.percent"
                :min="50"
                :max="100"
                label="相似度为正整数，请输入正确数字"
              ></el-input-number>
              <!-- <p style="font-size: 12px; color: red" v-if="flag">
                    相似度在于{{ tableDatasimilar.minPercent }}-{{
                      tableDatasimilar.maxPercent
                    }}之间"
                  </p> -->
            </el-form-item>
            <el-form-item label="是否校验签名" prop="checkSign">
              <el-radio-group v-model="similarFormop.checkSign">
                <el-radio value="0">是</el-radio>
                <el-radio value="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="!id" label="是否支持公司主体" prop="supportCompany">
              <el-radio-group v-model="similarFormop.supportCompany">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template v-slot:footer>
        <div class="dialog-footer">
          <!-- <el-button v-if="active == 0" type="primary" @click="next('formop')"
              >下一步</el-button
            ><el-button
              v-else-if="active == 1"
              type="primary"
              @click="handpop('formop')"
              >上一步</el-button
            > -->
          <el-button type="primary" @click="submitForm('similarFormop')"
            >提 交</el-button
          >
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
export default {
  data() {
    var percent = (rule, value, callback) => {
      if (value != "") {
        let reg = /^[0-9]*[1-9][0-9]*$/;
        if (reg.test(value)) {
          if (value >= 50 && value <= 100) {
            callback();
          } else {
            callback(new Error("相似度在于50-100之间"));
          }
        } else {
          callback(new Error("相似度为正整数，请输入正确数字"));
        }
      } else {
        callback(new Error("相似度不能为空"));
      }
    };
    return {
      customConfig: {
        storage: true,
      },
      isFirstEnter: false,
      dialogVisible: false,
      id: "",
      title: "",
      active: 0,
      compNamelist:[],
      loadingcomp: false,
      //查询条件的值
      sensitiveCondition: {
        username: "",
        compId:"",
        content: "",
        minNum: "",
        maxNum: "",
        minPercent: "",
        maxPercent: "",
        status: "",
        beginTime: "",
        endTime: "",
        time: [],
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        username: "",
        compId:"",
        content: "",
        minNum: "",
        maxNum: "",
        minPercent: "",
        maxPercent: "",
        status: "",
        beginTime: "",
        endTime: "",
        time: [],
        currentPage: 1,
        pageSize: 10,
      },
      formop: {
        username: "",
        content: "",
        keywords: "",
      },
      similarFormop: {
        username: "",
        content: "",
        percent: undefined,
        keywords: "",
        auditStatus: "",
        snapshot: "",
        checkSign: "0",
        supportCompany: false,
      },
      similaRrules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "change" },
        ],
        content: [
          { required: true, message: "内容不能为空", trigger: "change" },
        ],
        keywords: [
          { required: true, message: "关键词不能为空", trigger: "change" },
        ],
        auditStatus: [
          { required: true, message: "请选择审核状态", trigger: "change" },
        ],
        checkSign: [
          {
            required: true,
            message: "请选择模板是否校验签名",
            trigger: "change",
          },
        ],
        percent: [{ required: true, validator: percent, trigger: "change" }],
        supportCompany: [
          {
            required: true,
            message: "请选择是否支持公司主体",
            trigger: "change",
          },
        ],
      },
      rules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "change" },
        ],
        content: [
          { required: true, message: "内容不能为空", trigger: "change" },
        ],
        keywords: [
          { required: true, message: "关键词不能为空", trigger: "change" },
        ],
      },
      //   similarList:[],
      //   maxPercent:"",
      //   minPercent:""
      tableDatasimilar: {
        loading2: false,
        tableData: [],
        maxPercent: "",
        minPercent: "",
      },
    };
  },
  created() {
    this.isFirstEnter = true;
    this.getTableDtate();
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      window.api.post(
        window.path.omcs + "operatingclientsmssimilar/page",
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
     //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    remoteMethod(query) {
      console.log(query, "query");
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.compNamelist = [];
        this.searchAccount();
      }
    },
    //查询
    sensitiveQuery() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
    },
    //重置
    sensitiveReload() {
      this.$refs.sensitiveCondition.resetFields();
      this.sensitiveCondition.minNum = "";
      this.sensitiveCondition.maxNum = "";
      this.sensitiveCondition.minPercent = "";
      this.sensitiveCondition.maxPercent = "";
      this.sensitiveCondition.beginTime = "";
      this.sensitiveCondition.endTime = "";
      this.sensitiveCondition.time = [];
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
    },
    //时间
    getTimeOperating(val) {
      if (val) {
        this.sensitiveCondition.beginTime = moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.sensitiveCondition.endTime = moment(val[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.sensitiveCondition.time = [
          moment(val[0]).format("YYYY-MM-DD HH:mm:ss"),
          moment(val[1]).format("YYYY-MM-DD HH:mm:ss"),
        ];
      } else {
        this.sensitiveCondition.beginTime = "";
        this.sensitiveCondition.endTime = "";
        this.sensitiveCondition.time = [];
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size;
      this.getTableDtate();
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage;
      this.getTableDtate();
    },
    addClientsmssimilar() {
      this.title = "添加自动审核规则";
      this.dialogVisible = true;
    },
    editClientsmssimilar(row) {
      this.title = "编辑自动审核规则";
      this.id = row.id;
      // this.formop.username = row.username;
      // this.formop.content = row.content;
      // this.formop.keywords = row.keywords;
      this.similarFormop.username = row.username;
      this.similarFormop.content = row.content;
      this.similarFormop.keywords = row.keywords;
      this.similarFormop.supportCompany = row.supportCompany ? true : false;
      if (row.auditStatus == "2") {
        this.similarFormop.auditStatus = row.auditStatus + "";
      } else {
        this.similarFormop.auditStatus = "1";
      }
      if (row.checkSign) {
        this.similarFormop.checkSign = row.checkSign + "";
      } else {
        this.similarFormop.checkSign = "0";
      }

      //   this.similarFormop.content = row.content;
      this.similarFormop.percent = row.percent * 1;
      // this.similarFormop.keywords = row.keywords;
      this.dialogVisible = true;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    delClientsmssimilar(row) {
      this.$confirms.confirmation(
        "delete",
        "此操作不可逆，是否继续?",
        window.path.omcs + "operatingclientsmssimilar/" + row.id,
        {},
        (res) => {
          this.getTableDtate();
        }
      );
    },
    changeSubjectStatus(row){
      this.$confirms.confirmation(
        "put",
        row.compId?"该模板支持公司主体共享，确认改变公司主体不共享？":"该模板不支持公司主体共享，确认改变公司主体共享？",
        window.path.omcs + "operatingclientsmssimilar/support",
        {
          id: row.id,
          support:row.compId?false:true,
          username: row.username,
        },
        (res) => {
          this.getTableDtate();
        }
      );
    },
    next(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + "operatingclientsmssimilar/percent",
            this.formop,
            (res) => {
              if (res.code == 200) {
                this.tableDatasimilar.similarList = res.data.similarList;
                this.tableDatasimilar.maxPercent = res.data.maxPercent;
                this.tableDatasimilar.minPercent = res.data.minPercent;
                // this.similarFormop.username = this.formop.username;
                // this.similarFormop.content = this.formop.content;
                // this.similarFormop.keywords = this.formop.keywords;
                // this.similarFormop.snapshot = JSON.stringify(res.data);
                if (this.active++ > 1) this.active = 0;
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            }
          );
        }
      });
    },
    handpop() {
      if (this.active-- == 0) return;
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.title == "添加自动审核规则") {
            window.api.post(
              window.path.omcs + "operatingclientsmssimilar",
              this.similarFormop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.dialogVisible = false;
                  this.getTableDtate();
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              }
            );
          } else {
            this.similarFormop.id = this.id;
            window.api.put(
              window.path.omcs + "operatingclientsmssimilar",
              this.similarFormop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.dialogVisible = false;
                  this.getTableDtate();
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              }
            );
          }
        }
      });
    },
  },
  watch: {
    dialogVisible(val) {
      if (val == false) {
        // if (this.active == 1) {

        // }
        this.$refs["similarFormop"].resetFields();
        this.id = "";
        this.active = 0;
        this.similarFormop.id = "";
        this.formop.username = "";
        this.formop.content = "";
        this.formop.keywords = "";
        this.similarFormop.username = "";
        this.similarFormop.content = "";
        this.similarFormop.percent = undefined;
        this.similarFormop.keywords = "";
        //   console.log(11111);
        //   this.id = ''
        //   this.formop.id = ''
        //   console.log(this.$refs.formopForm,'this.$refs.formopForm');
        //   this.$refs.formopForm.resetFields();
        //   this.formop.id = ''
      }
    },
  },
};
</script>

<style scoped>
.button {
  margin: 10px 0;
}
.first {
  margin: 10px 0;
  width: 100%;
}
.form_first {
  width: 650px;
  margin: 0 auto;
}
.title {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  margin: 10px 0;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Templat-matter > p {
  padding: 5px 0;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
</style>
