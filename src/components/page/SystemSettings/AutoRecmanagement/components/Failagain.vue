<template>
  <div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="失败代码" prop="failureCode">
            <el-input
              v-model="formInline.failureCode"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button type="primary" @click="addopt">新增</el-button>
            <el-button
              type="danger"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delAll"
              >批量删除</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增运营商 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="100px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="失败代码" prop="failureCode">
            <el-input
              class="input-w"
              v-model="formop.failureCode"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增运营商结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  name: 'Failagain',
  components: {
    TableTem,
  },
  data() {
    return {
      isFirstEnter: false,
      titleMap: {
        add: '添加失败代码补发',
        edit: '编辑失败代码补发',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      id: '', //失败代码补发id

      tableRow: '', //当前行列表数据
      selectId: '',
      formop: {
        //表单数据
        failureCode: '',
      },
      rules: {
        //验证规则
        failureCode: [
          { required: true, message: '请输入失败代码', trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '长度在 1 到 50 个字符',
            trigger: ['blur', 'change'],
          },
          // {
          //   pattern: /^[\u4e00-\u9fa5]+$/,
          //   message: "含有非法字符（只能输入汉字！）"
          // }
        ],
      },
      tabelAlllist: {
        //存储查询数据
        failureCode: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      formInline: {
        //查询数据
        failureCode: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        id: 'Failagain',
        custom: true,
        currentPage: '1',
        pageSize: '10',
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'id',
            showName: 'ID',
          },
          {
            prop: 'failureCode',
            showName: '失败代码',
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: 160,
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'EditPen',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'Delete',
            color: '#f56c6c',
          },
        ],
      },
      pldel: false,
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs +
          'operatingFailureCodeReissue/selectAllFailureCodeReissue',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    Query() {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handelSelection(row) {
      //列表复选框的值
      if (row.length > 0) {
        let arr = []
        row.forEach(item => {
          arr.push(item.id)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    Reload() {
      //重置
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //导出 （发送记录）
    export1() {
      let aa = {}
      Object.assign(aa, this.tabelAlllist)
      // aa.flag = this.flag;
      // aa.sendBeginTime = this.Times.beginTime;
      // aa.sendEndTime = this.Times.endTime;
      // aa.isDownload = 1;
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingRechargeConsumer/export',
          aa,
          '充值记录报表.xlsx'
        )
      }
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //验证唯一性
            window.api.get(
              window.path.omcs +
                'operatingFailureCodeReissue/checkFailureCode/' +
                this.formop.failureCode,
              {},
              (res) => {
                if (res.code == 200) {
                  //添加
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs +
                      'operatingFailureCodeReissue/insertFailureCodeReissue',
                    {
                      failureCode: this.formop.failureCode,
                    },
                    () => {
                      this.dialogFormVisible = false
                      this.gettableLIst()
                    }
                  )
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            //验证唯一
            if (this.formop.failureCode == this.tableRow.failureCode) {
              this.$confirms.confirmation(
                'post',
                '确认执行此操作吗？',
                window.path.omcs +
                  'operatingFailureCodeReissue/updateFailureCodeReissue',
                this.formop,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              window.api.get(
                window.path.omcs +
                  'operatingFailureCodeReissue/checkFailureCode/' +
                  this.formop.failureCode,
                {},
                (res) => {
                  if (res.code == 200) {
                    this.$confirms.confirmation(
                      'post',
                      '确认执行此操作吗？',
                      window.path.omcs +
                        'operatingFailureCodeReissue/updateFailureCodeReissue',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  } else {
                    this.$message({
                      message: res.msg,
                      type: 'warning',
                    })
                  }
                }
              )
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    addopt() {
      //添加
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    delAll() {
      // window.api.get(
      //   window.path.omcs+"operatingindustrycategories/checkNoteInCodeCount/" +
      //     this.selectId,
      //   {},
      //   res => {
      //     if (res.code == 400) {
      //       this.$message({
      //         message: "行业类别已被使用，不可以删除！",
      //         type: "warning"
      //       });
      //     } else {
      //批量删除

      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs +
          'operatingFailureCodeReissue/batchDeleteFailureCodeReissue/' +
          this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )

      //     }
      //   }
      // );
    },
    detailsRow(val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.row.id
      this.tableRow = val.row
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val.row)
      })
    },
    //删除
    delRow: function (val) {
      //删除验证
      // window.api.get(window.path.omcs+'operatingoperatorinfo/checkChannelIsUsed/'+val.row.id,
      // {},
      // res=>{
      //     if (res.code==400) {
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }else{

      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs +
          'operatingFailureCodeReissue/batchDeleteFailureCodeReissue/' +
          val.row.id,
        {},
        () => {
          this.gettableLIst()
        }
      )

      //     }
      // })
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }

      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  mounted() {},
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  watch: {
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
