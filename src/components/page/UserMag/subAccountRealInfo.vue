<template>
    <div class="container_left">
        <div class="OuterFrame fillet">
            <div>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="queryForm">
                    <el-form-item label="用户名" prop="consumerName">
                        <el-input v-model="formInline.consumerName" @keyup.enter="Query()" placeholder=""
                            class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="公司名称" prop="compName">
                        <el-input v-model="formInline.compName" @keyup.enter="Query()" placeholder=""
                            class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain style="" @click="Query">查询</el-button>
                        <el-button type="primary" plain style="" @click="Reload(queryForm)">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div>
                <vxe-toolbar ref="toolbarRef" custom>
                    <template #buttons>
                    </template>
                </vxe-toolbar>

                <vxe-table ref="tableRef" id="subAccountRealInfoTable" border stripe show-header-overflow
                    :custom-config="customConfig" :column-config="{ resizable: true }" :row-config="{ isHover: true }"
                    min-height="1" v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                    element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;"
                    :data="tableDataObj.tableData">
                    <vxe-column field="用户名称" title="用户名称">
                        <template #default="scope">
                            <div>{{ scope.row.consumerName }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="公司名称" title="公司名称">
                        <template #default="scope">
                            <div>{{ scope.row.compName }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="实名企业名称" title="实名企业名称">
                        <template #default="scope">
                            <div v-if="scope.row.realInfo">{{ scope.row.realInfo.companyName }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="社会统一信用代码" title="社会统一信用代码">
                        <template #default="scope">
                            <div v-if="scope.row.realInfo">{{ scope.row.realInfo.creditCode }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="企业法人" title="企业法人">
                        <template #default="scope">
                            <div v-if="scope.row.realInfo">{{ scope.row.realInfo.legalPerson }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="责任人姓名" title="责任人姓名">
                        <template #default="scope">
                            <div v-if="scope.row.realInfo">{{ scope.row.realInfo.principalName }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="责任人证件号码" title="责任人证件号码">
                        <template #default="scope">
                            <div v-if="scope.row.realInfo">{{ scope.row.realInfo.principalIdCard }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="实名状态" title="实名状态">
                        <template #default="scope">
                            <div v-if="scope.row.realInfo">
                                <el-tag effect="light" type="info" v-if="scope.row.realInfo.companyStatus == '0'">待校验</el-tag>
                                <el-tag effect="light" type="danger"
                                    v-else-if="scope.row.realInfo.companyStatus == '1'">校验不通过</el-tag>
                                <el-tag effect="light" type="success"
                                    v-else-if="scope.row.realInfo.companyStatus == '2'">校验通过</el-tag>
                                <el-tag effect="light" type="warning" v-else-if="scope.row.realInfo.companyStatus == '3'">未实名</el-tag>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="原因" title="原因">
                        <template #default="scope">
                            <span  v-if="scope.row.realInfo">{{ scope.row.realInfo.remark }}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="创建时间" title="创建时间" width="180">
                        <template #default="scope">
                            <div>{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                        </template>
                    </vxe-column>
                    
                    
                    <vxe-column field="操作" title="操作" width="200" fixed="right">
                        <template #default="scope">
                            <el-button text type="primary" @click="handleRealName(scope.row)">实名信息更改</el-button>
                        </template>
                    </vxe-column>

                </vxe-table>
                <!--分页-->
                <div style="display: flex;justify-content: space-between;margin-top: 10px;">
                    <div></div>
                    <el-pagination class="page_bottom" @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" :current-page="formInline.currentPage"
                        :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalSize">
                    </el-pagination>
                </div>

            </div>
        </div>
        <el-dialog title="实名信息补充" v-model="realNameDialogVisible" width="550px" class="LoginCellPhoneDialog"
            :close-on-click-modal="false" :before-close="handelClose">
            <div>
                <el-form :inline="false" :model="realNameInfo" :rules="formRules" ref="realNameInfoRef"
                    class="demo-ruleForm" label-width="140px">
                    <el-form-item label="企业名称" prop="companyName">
                        <el-input class="input-w" placeholder="请输入企业名称" v-model="realNameInfo.companyName"></el-input>
                    </el-form-item>
                    <el-form-item label="社会统一信用代码" prop="creditCode">
                        <el-input class="input-w" placeholder="请输入统一社会信用代码"
                            v-model="realNameInfo.creditCode"></el-input>
                    </el-form-item>
                    <el-form-item label="企业法人" prop="legalPerson">
                        <el-input class="input-w" placeholder="请输入企业法人姓名" v-model="realNameInfo.legalPerson"></el-input>
                    </el-form-item>
                    <el-form-item label="责任人姓名" prop="principalName">
                        <el-input class="input-w" placeholder="请输入负责人姓名"
                            v-model="realNameInfo.principalName"></el-input>
                    </el-form-item>
                    <el-form-item label="责任人证件号码" prop="principalIdCard">
                        <el-input class="input-w" placeholder="请输入负责人身份证号"
                            v-model="realNameInfo.principalIdCard"></el-input>
                    </el-form-item>
                    <el-form-item label="责任人手机号" prop="principalMobile">
                        <el-input class="input-w" placeholder="请输入负责人手机号" v-model="realNameInfo.principalMobile"></el-input>
                    </el-form-item>
                </el-form>
                <el-button @click="realNameDialogVisible = false"
                    style="width: 100px; padding: 9px 0; margin-left: 160px">取消</el-button>
                <el-button type="primary" @click="submitFormRealNameInfo(realNameInfoRef)"
                    style="width: 100px; padding: 9px 0">提交</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import moment from 'moment';
const queryForm = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const realNameInfoRef = ref(null);
const customConfig = {
    storage: true
};
const formInline = reactive({
    consumerName: '',
    compName: '',
    currentPage: 1,
    pageSize: 10,
});
const tableDataObj = reactive({
    loading2: false,
    tableData: [],
    totalSize: 0,
});
const realNameDialogVisible = ref(false);
const realNameInfo = reactive({
    username: '',
    companyName: '',
    creditCode: '',
    legalPerson: '',
    principalName: '',
    principalIdCard: '',
    principalMobile:'',
});
const formRules = {
    companyName: [
        { required: true, message: '请输入企业名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    creditCode: [
        { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    legalPerson: [
        { required: true, message: '请输入企业法人姓名', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    principalName: [
        { required: true, message: '请输入负责人姓名', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    principalIdCard: [
        { required: true, message: '请输入负责人身份证号', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    ],
    principalMobile: [
          { required: true, message: '请输入负责人手机号', trigger: 'change' },
    ],
};
const gettableLIst = () => {
    //获取列表
    tableDataObj.loading2 = true;
    window.api.post(
        window.path.omcs + "consumerclientinfo/realinfo/page",
        formInline,
        (res) => {
            tableDataObj.loading2 = false;
            tableDataObj.totalSize = res.total;
            tableDataObj.tableData = res.records;
        }
    );
};
const Query = () => {
    gettableLIst();
}

const Reload = (formName) => {
    console.log(formName);
    // ref(formName).resetFields();
    formName.resetFields();
    gettableLIst();
}
const handleCurrentChange = (currentPage) => {
    formInline.currentPage = currentPage;
    gettableLIst();
};
const handleSizeChange = (pageSize) => {
    formInline.pageSize = pageSize;
    gettableLIst();
};
const handleRealName = (row) => {
    realNameDialogVisible.value = true;
    realNameInfo.username = row.consumerName;
    if (row.realInfo) {
        realNameInfo.companyName = row.realInfo.companyName;
        realNameInfo.creditCode = row.realInfo.creditCode;
        realNameInfo.legalPerson = row.realInfo.legalPerson;
        realNameInfo.principalName = row.realInfo.principalName;
        realNameInfo.principalIdCard = row.realInfo.principalIdCard;
        realNameInfo.principalMobile = row.realInfo.principalMobile;
    }
}
const submitFormRealNameInfo = (form) => {
    form.validate((valid) => {
        if (valid) {
            try {
                ElMessageBox.confirm("确定要提交实名信息吗？", "提醒", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                    .then(() => {
                        window.api.post(
                            window.path.omcs + "consumerclientinfo/realinfo/supply",
                            realNameInfo,
                            (res) => {
                                if (res.code == 200) {
                                    ElMessage({
                                        type: "success",
                                        message: res.msg,
                                    });
                                    realNameDialogVisible.value = false;
                                    gettableLIst();
                                } else {
                                    ElMessage({
                                        type: "error",
                                        message: res.msg,
                                    });
                                }
                            }
                        );
                    })
                    .catch(() => {
                        ElMessage({
                            type: "info",
                            message: "已取消",
                        });
                    });
            } catch (error) {
                console.error(error, 'ee');
            }
        } else {
            ElMessage.error('请检查输入项');
            return false;
        }
    });
}
const handelClose = (done) => {
    realNameDialogVisible.value = false;
}
onMounted(() => {
    const $table = tableRef.value;
    const $toolbar = toolbarRef.value;
    if ($table && $toolbar) {
        $table.connect($toolbar);
    }
    gettableLIst();
});
</script>

<style lang="less" scoped>
.OuterFrame {
    padding: 20px;
}
</style>