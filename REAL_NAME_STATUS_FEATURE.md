# 📋 实名状态功能说明

## 🎯 功能概述

在 `TemplateMag.vue` 模板管理页面中新增了实名状态功能，包括联通状态（ltStatus）和移动状态（ydStatus）的显示和筛选。

## ✨ 新增功能

### 1. 状态定义
- **0**: 报备中 (PENDING)
- **1**: 通过 (APPROVED)

### 2. 功能模块

#### **A. 表格列显示**
在模板列表中新增两列：
- **联通状态**: 显示联通运营商的实名状态
- **移动状态**: 显示移动运营商的实名状态

状态以彩色标签形式显示：
- 🟡 **报备中**: 黄色警告标签
- 🟢 **通过**: 绿色成功标签

#### **B. 搜索筛选**
在搜索表单中新增筛选条件：
- **联通状态筛选**: 可选择"不限"、"报备中"、"通过"
- **移动状态筛选**: 可选择"不限"、"报备中"、"通过"

#### **C. 详情对话框**
在模板详情弹窗中显示实名状态信息：
- 联通状态
- 移动状态

## 🔧 技术实现

### 1. 常量定义
```javascript
// src/constants/template.js
export const REAL_NAME_STATUS = {
  PENDING: 0,  // 报备中
  APPROVED: 1  // 通过
}

export const REAL_NAME_STATUS_LABELS = {
  [REAL_NAME_STATUS.PENDING]: '报备中',
  [REAL_NAME_STATUS.APPROVED]: '通过'
}

export const REAL_NAME_STATUS_COLORS = {
  [REAL_NAME_STATUS.PENDING]: 'warning',
  [REAL_NAME_STATUS.APPROVED]: 'success'
}
```

### 2. 数据结构更新
```javascript
// 搜索表单数据
formInline: {
  // ... 其他字段
  ltStatus: '', // 联通状态
  ydStatus: '', // 移动状态
}

// 详情列表数据
detailsList: [
  { title: '用户名', content: '' },
  { title: '模板ID', content: '' },
  { title: '模板类型', content: '' },
  { title: '模板格式', content: '' },
  { title: '模板名称', content: '' },
  { title: '模板内容', content: '' },
  { title: '参数检测', content: '' },
  { title: '联通状态', content: '' }, // 新增
  { title: '移动状态', content: '' }, // 新增
  { title: '申请时间', content: '' },
  { title: '审核人', content: '' },
  { title: '审核时间', content: '' },
  { title: '申请说明', content: '' },
]
```

### 3. 工具方法
```javascript
// 获取实名状态标签
getRealNameStatusLabel(status) {
  return REAL_NAME_STATUS_LABELS[status] || '未知'
},

// 获取实名状态颜色
getRealNameStatusColor(status) {
  return REAL_NAME_STATUS_COLORS[status] || 'info'
}
```

### 4. 表格列配置
```vue
<vxe-column field="联通状态" title="联通状态" width="100px">
  <template v-slot="scope">
    <el-tag 
      :type="getRealNameStatusColor(scope.row.ltStatus)"
      size="small"
    >
      {{ getRealNameStatusLabel(scope.row.ltStatus) }}
    </el-tag>
  </template>
</vxe-column>

<vxe-column field="移动状态" title="移动状态" width="100px">
  <template v-slot="scope">
    <el-tag 
      :type="getRealNameStatusColor(scope.row.ydStatus)"
      size="small"
    >
      {{ getRealNameStatusLabel(scope.row.ydStatus) }}
    </el-tag>
  </template>
</vxe-column>
```

### 5. 搜索表单配置
```vue
<el-form-item label="联通状态" prop="ltStatus">
  <el-select v-model="formInline.ltStatus" clearable placeholder="请选择" class="input-time">
    <el-option label="不限" value=""></el-option>
    <el-option label="报备中" :value="REAL_NAME_STATUS.PENDING"></el-option>
    <el-option label="通过" :value="REAL_NAME_STATUS.APPROVED"></el-option>
  </el-select>
</el-form-item>

<el-form-item label="移动状态" prop="ydStatus">
  <el-select v-model="formInline.ydStatus" clearable placeholder="请选择" class="input-time">
    <el-option label="不限" value=""></el-option>
    <el-option label="报备中" :value="REAL_NAME_STATUS.PENDING"></el-option>
    <el-option label="通过" :value="REAL_NAME_STATUS.APPROVED"></el-option>
  </el-select>
</el-form-item>
```

## 🎨 样式设计

### 状态标签样式
- **报备中**: 黄色警告样式 (`type="warning"`)
- **通过**: 绿色成功样式 (`type="success"`)
- **未知**: 灰色信息样式 (`type="info"`)

### 自定义CSS样式
```css
/* 实名状态样式 */
.real-name-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.real-name-status.pending {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #f5dab1;
}

.real-name-status.approved {
  background-color: #f0f9ff;
  color: #67c23a;
  border: 1px solid #b3d8ff;
}
```

## 📊 数据字段说明

### API数据字段
后端需要在模板数据中提供以下字段：

```javascript
{
  temId: 12345,           // 模板ID
  clientName: "用户名",    // 用户名
  temName: "模板名称",     // 模板名称
  temContent: "模板内容",  // 模板内容
  ltStatus: 0,            // 联通状态: 0-报备中, 1-通过
  ydStatus: 1,            // 移动状态: 0-报备中, 1-通过
  // ... 其他字段
}
```

### 搜索参数
前端向后端发送的搜索参数包含：

```javascript
{
  temId: "",              // 模板ID
  clientName: "",         // 用户名
  temName: "",            // 模板名称
  temContent: "",         // 模板内容
  temType: "",            // 模板类型
  temFormat: "",          // 模板格式
  ltStatus: "",           // 联通状态筛选
  ydStatus: "",           // 移动状态筛选
  beginTime1: "",         // 申请开始时间
  endTime1: "",           // 申请结束时间
  beginTime2: "",         // 审核开始时间
  endTime2: "",           // 审核结束时间
  currentPage: 1,         // 当前页
  pageSize: 10            // 页面大小
}
```

## 🔄 重置功能

在重置搜索表单时，实名状态筛选条件也会被清空：

```javascript
Reload() {
  this.$refs.formInline.resetFields()
  this.formInline.beginTime1 = ''
  this.formInline.endTime1 = ''
  this.formInline.beginTime2 = ''
  this.formInline.endTime2 = ''
  this.formInline.ltStatus = ''  // 重置联通状态
  this.formInline.ydStatus = ''  // 重置移动状态
  Object.assign(this.tabelAlllist, this.formInline)
  this.gettableLIst()
}
```

## 🎯 使用说明

### 1. 查看实名状态
- 在模板列表中可以直接看到每个模板的联通状态和移动状态
- 状态以彩色标签形式显示，一目了然

### 2. 筛选功能
- 可以根据联通状态筛选模板
- 可以根据移动状态筛选模板
- 支持组合筛选（同时按联通和移动状态筛选）

### 3. 详情查看
- 点击模板ID可以查看详细信息
- 详情弹窗中包含实名状态信息

## 🚀 扩展建议

### 1. 状态更新功能
可以考虑添加实名状态的手动更新功能：
- 批量更新实名状态
- 单个模板状态更新
- 状态变更历史记录

### 2. 状态统计
可以添加实名状态的统计功能：
- 各状态模板数量统计
- 状态分布图表
- 趋势分析

### 3. 状态提醒
可以添加状态变更提醒：
- 状态变更通知
- 待处理状态提醒
- 自动状态检查

---

**注意**: 此功能已完全集成到现有的模板管理系统中，保持了代码的一致性和可维护性。
