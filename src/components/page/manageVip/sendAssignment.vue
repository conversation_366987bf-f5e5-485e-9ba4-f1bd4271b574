<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <el-form
        :inline="true"
        :model="formInline"
        class="demo-form-inline"
        ref="formInline"
      >
        <el-form-item label="用户名称" prop="customerName">
          <el-input
            v-model="formInline.customerName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="触发名称" prop="ruleName">
          <el-input
            v-model="formInline.ruleName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click="onSubmit()">查询</el-button>
          <el-button type="primary" plain @click="resetForm('formInline')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="margin: 10px 0">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :stripe="true"
        :data="tableDataObj.tableData"
        style="width: 100%"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="sendAssignment"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="用户名称" title="用户名称" prop="id" align="left">
          <template v-slot="{ row }">
            <Tooltip
              v-if="row.customerName"
              :content="row.customerName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <span>{{ row.customerName }}</span> -->
          </template>
        </vxe-column>
        <vxe-column field="触发名称" title="触发名称" prop="id" align="left">
          <template v-slot="{ row }">
            <Tooltip
              v-if="row.ruleName"
              :content="row.ruleName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <span>{{ row.ruleName }}</span> -->
          </template>
        </vxe-column>
        <vxe-column field="模板Id" title="模板Id" prop="tempId" align="left">
          <template v-slot="{ row }">
            <div>{{ row.tempId }}</div>
          </template>
        </vxe-column>
        <vxe-column field="模板名称" title="模板名称" prop="tempId" align="left">
          <template v-slot="{ row }">
            <Tooltip
              v-if="row.tempName"
              :content="row.tempName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <span>{{ row.tempName }}</span> -->
          </template>
        </vxe-column>
        <vxe-column field="短信签名" title="短信签名" prop="id" align="left">
          <template v-slot="{ row }">
            <div>{{ row.signature }}</div>
          </template>
        </vxe-column>
        <vxe-column field="触发字段" title="触发字段" prop="id" align="left" width="170px">
          <template v-slot="{ row }">
            <div v-if="row.contactMappingDTO"
              >{{ row.contactMappingDTO.columnDefine }}（{{
                row.contactMappingDTO.paramName
              }}）</div
            >
          </template>
        </vxe-column>
        <vxe-column field="触发条件" title="触发条件" prop="id" align="left">
          <template v-slot="{ row }">
            <div v-if="row.ruleCondition == 1">当天</div>
            <div v-if="row.ruleCondition == 2">当月</div>
            <div v-if="row.ruleCondition == 3">提前 {{ row.days }}天</div>
            <div v-if="row.ruleCondition == 4">超过{{ row.days }}天</div>
          </template>
        </vxe-column>
        <vxe-column field="触发频率" title="触发频率" prop="id" align="left">
          <template v-slot="{ row }">
            <div v-if="row.ruleType == 1">一次</div>
            <div v-if="row.ruleType == 2">每年循环</div>
          </template>
        </vxe-column>
        <vxe-column field="上次触发时间" title="上次触发时间" align="left" width="160px">
          <template v-slot="{ row }">
            <div v-if="row.lastTime">{{
              moment(row.lastTime).format('YYYY-MM-DD HH:mm:ss')
            }}</div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" align="left" width="160px">
          <template v-slot="{ row }">
            <div v-if="row.createTime">{{
              moment(row.createTime).format('YYYY-MM-DD HH:mm:ss')
            }}</div>
          </template>
        </vxe-column>
        <vxe-column field="任务状态" title="任务状态" prop="id" align="left">
          <template v-slot="{ row }">
            <div v-if="row.status == 1" style="color: #42b983">启用</div>
            <div v-if="row.status == 2" style="color: red">停用</div>
          </template>
        </vxe-column>
        <vxe-column
          field="操作"
          title="操作"
          align="left"
          class-name="small-padding fixed-width"
        >
          <template v-slot="{ row }">
            <el-button link style="color: #409eff;" @click="checked(row)">
              <el-icon><Tickets /></el-icon> 查看
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="paginationBox">
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="formInlines.currentPage"
          :page-size="formInlines.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :title="'详情' + ' - ' + detailObj.ruleName"
      v-model="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <div class="checked">
        <div class="item">
          <span class="item_name">触发名称：</span>
          <span>{{ detailObj.ruleName }}</span>
        </div>
        <div>
          <span class="item_name">触发人群：</span>
          <el-tag
:disable-transitions="true">{{ detailObj.groupNames }}</el-tag>
        </div>
        <div>
          <span class="item_name">触发字段：</span>
          <span v-if="detailObj.mappingId"
            >{{ detailObj.contactMappingDTO.columnDefine }}（{{
              detailObj.contactMappingDTO.paramName
            }}）</span
          >
        </div>
        <div>
          <span class="item_name">触发条件：</span>
          <span
            :class="detailObj.ruleCondition == 1 ? 'active' : 'item_content'"
            >当天</span
          >
          <span
            :class="detailObj.ruleCondition == 2 ? 'active' : 'item_content'"
            >当月</span
          >
          <span
            :class="detailObj.ruleCondition == 3 ? 'active' : 'item_content'"
            >提前 {{ detailObj.days }}天</span
          >
          <span
            :class="detailObj.ruleCondition == 4 ? 'active' : 'item_content'"
            >超过{{ detailObj.days }}天</span
          >
        </div>
        <div>
          <span class="item_name">触发频率：</span>
          <span :class="detailObj.ruleType == 1 ? 'active' : 'item_content'"
            >单次</span
          >
          <span :class="detailObj.ruleType == 2 ? 'active' : 'item_content'"
            >循环</span
          >
        </div>
        <div>
          <span class="item_name">短信签名：</span>
          <span>{{ detailObj.signature }}</span>
        </div>
        <div>
          <span class="item_name">短信模板ID：</span>
          <span>{{ detailObj.tempId }}</span>
        </div>
        <div>
          <span class="item_name">短信内容：</span>
          <span v-if="detailObj.temContent">{{ detailObj.temContent }}</span>
        </div>
        <!-- <div>
              <span>触发人群：</span>
              <span v-if="detailObj.ruleType == 1">单次</span>
              <span v-if="detailObj.ruleType == 2">循环</span>
            </div> -->
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <!-- <el-button type="primary" @click="dialogVisible = false"
              >确 定</el-button
            > -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    Tooltip
  },
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      dialogVisible: false,
      titlet: '',
      detailObj: {},
      options: [],
      //查询条件的值
      formInline: {
        customerName: '', //用户名
        ruleName: '', //触发名称
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      formInlines: {
        customerName: '', //用户名
        ruleName: '', //触发名称
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getList()
      //   this.getGroupName()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getList()
        // this.getGroupName()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getList() {
      window.api.post(
        window.path.omcs + '/customercontactsendrule/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //触发人群
    // getGroupName() {
    //     window.api.get(
    //     window.path.contact + "/customercontactgroup/list",
    //     {},
    //     (res) => {
    //         this.options = res.data;
    //     }
    //   );
    // },
    onSubmit() {
      Object.assign(this.formInlines, this.formInline)
      this.getList()
    },
    resetForm(form) {
      this.$refs.formInline.resetFields()
      Object.assign(this.formInlines, this.formInline)
      this.getList()
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.getList()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.getList()
    },
    //查看
    checked(row) {
      this.dialogVisible = true
      this.detailObj = row
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
}
</script>

<style scoped>
.pagination {
  display: flex;
  justify-content: space-between;
}
.checked {
  width: 100%;
  line-height: 35px;
}
.item_name {
  display: inline-block;
  width: 85px;
}
.item_content {
  display: inline-block;
  width: 85px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 1px solid #ccc;
  margin: 0 2px;
}
.active {
  display: inline-block;
  width: 85px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 1px solid #ccc;
  margin: 0 2px;
  background: #409eff;
  color: #fff;
}
</style>
