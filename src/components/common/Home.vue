<template>
  <div class="wrapper">
    <v-head :hName="name" :HportraitUrl="portraitUrl"></v-head>
    <v-sidebar></v-sidebar>
    <div class="content-box" :class="{ 'content-collapse': collapse }">
      <v-tags></v-tags>
      <div class="content">
        <router-view v-slot="{ Component }">
          <transition v-if="$route.meta.keepAlive">
            <keep-alive :include="tagsList">
              <component :is="Component" />
            </keep-alive>
          </transition>
          <transition v-else>
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
import { mapState, mapMutations, mapActions } from "vuex";
import vHead from "./Header.vue";
import vSidebar from "./Sidebar.vue";
import vTags from "./Tags.vue";
import bus from "./bus";
export default {
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      tagsList: ["userModification"],
      collapse: false,
      isRouterAlive: true,
      showDialog: false,
      loading: false,
      //   path: "ws://***********:9999/websocket/",
      path: "wss://opt.zthysms.com/websocket/",
      socket: "",
      message: "",
      dialogArr: [],
      userid: "",
      name: "",
      portraitUrl: "",
      token: "",
      ws: "",
      title: "",
      timeout: 50000, //50秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      stcount: null,
      smcount: null,
      vcount: null,
      rcount: null,
      vtcount: null,
      originalUrl: null,
      chatbotcount: null,
      fgcount: null,
      chatbotTempCount: null,
      menuCount: null,
    };
  },
  components: {
    vHead,
    vSidebar,
    vTags,
  },
  computed: {
    ...mapState({
      userId: (state) => state.userId,
    }),
  },
  methods: {
    ...mapActions([
      //比如'movies/getHotMovies
      "saveInfo",
    ]),
    init: function () {
      let that = this;
      // console.log(this.userId,'ll');
      //  console.log(this.path+this.token,);
      if (typeof WebSocket === "undefined") {
        this.$notify({
          title: "您的浏览器不支持socket",
        });
        // alert("您的浏览器不支持socket")
      } else {
        // // 实例化socket
        // this.socket = new WebSocket(this.path+this.token)
        // // 监听socket连接
        // this.socket.onopen = this.open
        // // 监听socket错误信息
        // this.socket.onerror = this.error
        // // 监听socket消息
        // this.socket.onmessage = this.getMessage
        that.ws = new WebSocket(this.path + this.token);
        that.global.setWs(that.ws);
        that.ws.onopen = function () {
          // console.log("连接成功");
          that.start();
        };
        // 监听socket消息
        that.ws.onmessage = (msg) => {
          // console.log(msg.data, "msg");
          //   that.showDialog = true
          const h = this.$createElement;
          if (msg.data == "REGISTER") {
            that.showDialog = true;
            that.rcount++;
            // this.$notify.info({
            //     title: '注册账号资质审核',
            //     message: h('i', { style: 'color: teal'},`注册账号审核有${that.rcount}新消息，请查看！` ),
            //     duration:0,
            // });
          } else if (msg.data == "SMS_SIGNATURE") {
            that.showDialog = true;
            that.smcount++;
            // this.$notify({
            //         title: '短信签名审核',
            //         message: h('i', { style: 'color: teal'},`短信签名审核有${that.smcount}新消息，请查看！` ),
            //         duration:0,
            //         iconClass:"el-icon-info",
            //     });
          } else if (msg.data == "SMS_TEMPLATE") {
            that.showDialog = true;
            that.stcount++;
            // this.$notify.info({
            //         title: '短信模板审核',
            //         message: h('i', { style: 'color: teal'},`短信模板审核有${that.stcount}新消息，请查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == "VIDEO_TEMPLATE") {
            that.showDialog = true;
            that.vtcount++;
            // this.$notify.info({
            //         title: '视频短信模板',
            //         message: h('i', { style: 'color: teal'},`视频短信模板审核有${that.vtcount}新消息，请查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == "SHORT_CHART") {
            that.showDialog = true;
            that.originalUrl++;
            //  this.$notify.info({
            //         title: '短链提醒',
            //         message: h('i', { style: 'color: teal'},`有${that.originalUrl}条新短链提醒，请查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == "CHATBOT_NEW_NOTICE") {
            that.showDialog = true;
            that.chatbotcount++;
            // this.$notify.info({
            //         title: 'chatbot审核',
            //         message: h('i', { style: 'color: teal'},`chatbot新增${that.chatbotcount}条审核通知，请及时查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == "CUSTOMER_NEW_NOTICE") {
            that.showDialog = true;
            that.fgcount++;
            // this.$notify.info({
            //         title: '5G消息',
            //         message: h('i', { style: 'color: teal'},'有新的客户开启5G消息，请及时查看！' ),
            //         duration:0,
            //     });
          } else if (msg.data == "TEMPLATE_AUDIT_NOTICE") {
            that.showDialog = true;
            that.chatbotTempCount++;
            // this.$notify.info({
            //         title: '5G模板审核',
            //         message: h('i', { style: 'color: teal'},`新增${that.chatbotTempCount}条5G模板审核通知，请及时查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == "MENU_NOTICE") {
            that.showDialog = true;
            that.menuCount++;
            // this.$notify.info({
            //         title: '固定菜单审核',
            //         message: h('i', { style: 'color: teal'},`新增${that.menuCount}条固定菜单审核通知，请及时查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == "token为空或无效，无法继续连接！") {
            that.vcount++;
            if (that.vcount > 5) {
              that.title = "token为空";
              that.global.ws.close();
            }
          }
          that.reset();
        };
        //连接错误
        that.ws.onerror = function () {
          console.log("WebSocket连接发生错误");
          //重连
          if (that.title == "token为空") {
            that.global.ws.close();
          } else {
            that.reconnect();
          }
        };
        that.ws.onclose = function () {
          // 关闭 websocket
          console.log("连接已关闭....");
          that.global.ws.close();
          //断线重新连接
          //   setTimeout(() => {
          //     that.init();
          //   }, 2000);
        };
        // console.log(that.global.setWs);
      }
    },
    reconnect() {
      //重新连接
      let that = this;
      if (that.global.ws && that.global.ws.readyState == 1) {
        clearInterval(that.timeoutnum);
        that.timeoutnum = null;
        that.timeNum = 0;
        return;
      }
      if (!that.timeoutnum) {
        that.timeoutnum = setInterval(function () {
          if (that.global.ws && that.global.ws.readyState == 1) {
            clearInterval(that.timeoutnum);
            that.timeoutnum = null;
            that.timeNum = 0;
            return;
          }
          //新连接
          that.init();
          that.timeNum++;
          if (that.timeNum >= 3) {
            clearInterval(that.timeoutnum);
            that.timeoutnum = null;
            that.timeNum = 0;
          }
        }, 1000);
      }
    },
    reset() {
      //重置心跳
      //清除时间
      clearTimeout(this.timeoutObj);
      clearTimeout(this.serverTimeoutObj);
      //   this.smcount = null
      //   this.stcount = null
      //   this.vcount = null
      //   this.rcount = null
      //重启心跳
      this.start();
    },
    handleShow() {
      this.showDialog = !this.showDialog;
    },
    start() {
      //开启心跳
      //   console.log('www','ll');
      let that = this;
      let date = new Date();
      that.timeoutObj && clearTimeout(that.timeoutObj);
      that.serverTimeoutObj && clearTimeout(that.serverTimeoutObj);
      that.timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        if (that.global.ws && that.global.ws.readyState == 1) {
          //如果连接正常
          //   console.log(that.global.ws.readyState,'lll');

          that.global.ws.send(`发送心跳给后端${date}`);
        } else {
          //否则重连
          that.reconnect();
        }
      }, that.timeout);
    },
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(() => {
        this.isRouterAlive = true;
      });
    },
    handclose() {
      this.showDialog = false;
      this.smcount = null;
      this.rcount = null;
      this.vtcount = null;
      this.stcount = null;
      this.originalUrl = null;
    },
    // 模拟异步数据加载过程
    startLoading() {
      this.loading = true;
      // 模拟数据加载过程，1秒后隐藏加载动画
      setTimeout(() => {
        this.loading = false;
      }, 1000); // 设置为实际加载数据的时间
    },
  },
  mounted() {
    this.startLoading();
    // console.log(this.userid,'this.userid1');
  },
  watch: {
    $route(to, from) {
      this.startLoading();
    },
  },
  created() {
    window.api.get(
      window.path.omcs + "operatingaccountinfo/account",
      null,
      (res) => {
        // console.log(res,'resss');
        this.name = res.data.username;
        this.portraitUrl = res.data.portraitUrl;
        this.userid = res.data.userId;
        localStorage.setItem("userInfo", JSON.stringify(res.data));
        // console.log(this.userid,'this.userId');
        //vuex
        let USER_NAME = res.data.username;
        let USER_ID = res.data.userId;
        let ROLE_ID = res.data.roleId;
        let SET_PHONE = res.data.phone;
        let SET_CREATETIME = res.data.createLocalDateTime;
        let SET_ROLEDESC = res.data.roleDesc;
        let SET_PASSWORD = res.data.password;
        let SET_COMPNAME = res.data.compName;
        let SET_CUSTOM = res.data.custom;
        let SET_WARINGREMIND = res.data.warningRemind;
        let SET_ISDATESTATE = res.data.isDateState;
        let SET_PORTRAITURL = res.data.portraitUrl;
        let SET_REALNAME = res.data.realName;
        let param = {
          userName: USER_NAME,
          userId: USER_ID,
          roleId: ROLE_ID,
          phone: SET_PHONE,
          createLocalDateTime: SET_CREATETIME,
          roleDesc: SET_ROLEDESC,
          password: SET_PASSWORD,
          compName: SET_COMPNAME,
          custom: SET_CUSTOM,
          warningRemind: SET_WARINGREMIND,
          isDateState: SET_ISDATESTATE,
          portraitUrl: SET_PORTRAITURL,
          realName: SET_REALNAME,
        };
        this.saveInfo(param);
      }
    );
    this.token = window.common.getCookie("ZTADMIN_TOKEN");
    // console.log(this.token,'token');
    // this.init();
    $on(bus, "collapse", (msg) => {
      this.collapse = msg;
    });

    // 只有在标签页列表里的页面才使用keep-alive，即关闭标签之后就不保存到内存中了。
    $on(bus, "tags", (msg) => {
      let arr = [];
      for (let i = 0, len = msg.length; i < len; i++) {
        msg[i].name && arr.push(msg[i].name);
      }
      this.tagsList = arr;
    });
  },
  unmounted() {
    let that = this;
    // that.global.ws.close();
    // console.log(1111111);
    // 销毁监听
    // this.socket.onclose = this.close
  },
};
</script>

<style scoped>
/* .move-enter-active,
.move-leave-active {
  transition: opacity 0.2s;
}

.move-enter,
.move-leave-to {
  opacity: 0;
} */

.socket {
  width: 270px;
  height: 250px;
  font-size: 12px;
  border-radius: 0 6px 0 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  position: fixed;
  right: 5%;
  margin-right: -75px;
  margin-bottom: -220px;
  bottom: 0;
  transition: 0.5s;
}
#dialog {
  width: 100%;
  height: 100%;
  color: #fff;
  border-radius: 0 6px 0 0;
  position: relative;
}
.sockets {
  width: 270px;
  height: 250px;
  font-size: 12px;
  border-radius: 0 6px 0 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  position: fixed;
  right: 5%;
  margin-right: -75px;
  bottom: 0;
  margin-bottom: 0px;
  transition: 0.5s;
}
@media (min-width: 750px) {
  #dialog {
    left: 0;
    right: 0;
    margin: 0 auto;
  }
}
@media screen and (min-width: 480px) {
  .socket {
    width: 400px;
    height: 250px;
    font-size: 16px;
    border-radius: 0 6px 0 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    position: fixed;
    right: 0;
    margin-right: 20px;
    margin-bottom: -220px;
    bottom: 0;
    transition: 0.5s;
    cursor: pointer;
  }
  .sockets {
    width: 400px;
    height: 250px;
    font-size: 16px;
    border-radius: 0 6px 0 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    position: fixed;
    right: 0;
    margin-right: 20px;
    bottom: 0;
    margin-bottom: 0px;
    transition: 0.5s;
  }
}
.dialog-button {
  width: 95%;
  background-color: #6367d5;
  border-width: 0;
  border-radius: 360px;
  padding: 10px;
  outline: none;
  color: white;
  position: absolute;
  bottom: 5%;
}
.dialog-button:focus {
  outline: none;
}
.dialog-button:active {
  background-color: #585dbe;
  border-color: #585dbe;
}
.dialog-enter-active,
.dialog-leave-active {
  transition: all 0.5s;
}
.dialog-enter,
.dialog-leave-to {
  opacity: 0;
  transform: translateY(300px);
}
.close {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 35px;
  font-weight: 800;
  margin-top: -8px;
  margin-right: -5px;
  cursor: pointer;
}
.icon_la {
  width: 100%;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: rgba(196, 145, 34, 0.5);
  color: #fff;
  cursor: pointer;
}
.loading-spinner {
  border: 8px solid #f3f3f3;
  border-radius: 50%;
  border-top: 8px solid #3498db;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
  margin: 100px auto;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

<style>
.el-notification .el-icon-info {
  color: #e6a23c !important;
}

.move-enter-active,
.move-leave-active {
  transition: opacity 0.1s;
}

.move-enter,
.move-leave-to {
  opacity: 0;
}
.content {
  overflow-y: auto !important;
}
</style>
