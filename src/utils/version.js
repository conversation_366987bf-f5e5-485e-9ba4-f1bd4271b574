export const version = 'v3.1.6.7'
export const contentList = [
    {
        id: '91',
        time: "2025/7/17更新内容",
        content: [
            {
                
            }
    【短信】客户端：UI界面优化（查询操作集中）
【短信】客户端：模版管理”-“添加模版”：短信内容提示文字有歧义，需要更新
【短信】客户端：管理商支持导出子账号发送明细数据
【短信】客户端：终端账户管理员可以设置不同手机用户的权限
【短信】运营端：客服查询签名是否可发送的场景优化
【短信】运营端：变量长度的说明文字需要检查更新
【短信】5G报备chatbot附件上传机制调整
【短信】变量模板others类型正则表达式优化
【短信】短信内容号码、链接的提取正则支持动态配置
【短信】后续需要每周 / 次的频率查看引流信息补充情况，需要实现自动化输出数据
【短信】接口：定制接口对接（众安保险）
【资源】实名状态查询优化：江苏通道、报备成功后，超过10天未发送的子端口自动改为待报备
【资源】实名状态查询优化：系统没有同步客服通过“通道简单报备”提交上来的三方通道数据
【资源】实名状态查询优化：检测到通道关闭后，自动将该通道移出通道组
【资源】实名状态查询优化：根据通道组类型（验证码 / 行业 / 营销）区分实名状态
【资源】签名报备模版新增：IP短信模版（vivo / 小米 / oppo）
【资源】子端口被删预警
【资源】可以每天定时导出指定通道的子端口数据，并压缩成zip压缩包
【资源】支持联通线下模板报备（已发布）
【资源】“通道子端口实名管理”增加“短信类型”筛选框
【资源】子端口引流信息 & 模版报备步骤优化：先进行子端口实名报备，回传数据后才可进行数据导出、引流信息 & 模版报备（并行改串行）
【资源】子端口引流信息 & 模版报备步骤优化：引流信息导出报备需要进行系统回填，系统才可将这批数据改为“报备中”
【资源】子端口引流信息 & 模版报备步骤优化：子端口引流信息 & 模版报备完成后的自动批量测试
【资源】子端口引流信息 & 模版报备步骤优化：测试失败的支持单独导出（处理后重新报备）
【资源】子端口引流信息 & 模版报备步骤优化：销售查询页面增加“引流信息”报备时间进展（运营回填信息后为“报备中”，测试后得到最终报备结果）
        ]
    },
{
    id: '92',
        time: "2025/7/24更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：融合平台客户端页面风格统一",
                },
                {
                    value:
                        "2、【短信】客户端：管理商支持给子用户创建模版",
                },
                {
                    value:
                        "3、【短信】客户端：直客同一个企业模版报备签名自动共享",
                },
                {
                    value:
                        "4、【短信】客户端：针对电信模版错误问题调整：控制变量汇总长度，不能超过模版总长度的70%",
                },
                {
                    value:
                        "5、【短信】客户端：优化模版报备的系统流程：客户提交模版要有系统过滤",
                },
                {
                    value:
                        "6、【短信】运营端：优化模版报备的系统流程：清理当前模板库（不符合规则的模版单独存储）",
                },
                {
                    value:
                        "7、【短信】运营端：优化模版报备的系统流程：top100客户的全部符合规则的模版导入、匹配引流信息",
                },
                {
                    value:
                        "8、【短信】运营端：联通不合规整改数据接口处理",
                },
                {
                    value:
                        "9、【短信】运营端：引流信息报备模版新增：中国移动",
                },
                {
                    value:
                        "10、【短信】运营端：空号检测新增供应商-奔赴",
                },
                {
                    value:
                        "11、【短信】5G chatbot清理&复制优化",
                },
                {
                    value:
                        "12、【资源】签名报备模版更新：上海移动1293",
                },
                {
                    value:
                        "13、【资源】优化联通子端口报备规则：签名是企业简称&全称的机审，商标&APP的人工审核，接口传输分类提报",
                },
                {
                    value:
                        "14、【资源】优化模版报备的系统流程：重新确认模版规则、更改模版生成的逻辑",
                },
                {
                    value:
                        "15、【短信】客户端：融合平台客户端页面风格统一",
                },
                {
                    value:
                        "16、【短信】客户端：管理商支持给子用户创建模版",
                },
            ],
    },
{
    id: '90',
        time: "2025/7/10更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：子用户需要可以对管理商创建的名下签名创建模版",
                },
                {
                    value:
                        "2、【短信】客户端：同一主体下的不同账号可编辑该主体下的所有引流&授权信息(子账号除外)",
                },
                {
                    value:
                        "3、【短信】客户端：新增域名备案的时候，引导客户去补充链接信息",
                },
                {
                    value:
                        "4、【短信】运营端：运营商投诉（导入/导出）表格整理",
                },
                {
                    value:
                        "5、【短信】运营端：“审核通知编辑告警联系人”页面修复",
                },
                {
                    value:
                        "6、【短信】运营端：手动补发优化： 三个选择：1、原通道补发；2、指定通道组补发；3、指定通道补发。",
                },
                {
                    value:
                        "7、【短信】运营端：短信状态补推支持根据错误码补推",
                },
                {
                    value:
                        "8、【短信】平台：内网查询签名的亮灯状态与客户端签名管理界面亮灯状态不一致",
                },
                {
                    value:
                        "9、【短信】平台：签名自动化测试",
                },
                {
                    value:
                        "10、【短信】接口：短信发送接口校验实际扣款人的余额",
                },
                {
                    value:
                        "11、【短信】接口：还呗个性化语音验证码接口调整",
                },
            ],
    },
{
    id: '89',
        time: "2025/7/3更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端/接口：链接备案信息的证明附件，支持对多个企业授权，新增短信链接，长链接",
                },
                {
                    value:
                        "2、【短信】运营端：链接备案信息的证明附件添加审核流程",
                },
                {
                    value:
                        "3、【短信】运营端：针对模板号码带-的进行格式化",
                },
                {
                    value:
                        "4、【短信】客户端：号码备案企业号码新增法人信息",
                },
                {
                    value:
                        "5、【短信】客户端：号码备案支持批量上传",
                },
                {
                    value:
                        "6、【短信】客户端：在线注册实名认证企业营业执照必填",
                },
                {
                    value:
                        "7、【短信】客户端：针对新提交数据，系统引导按照移动的要求来“不允许带空格或其他特殊字符”",
                },
                {
                    value:
                        "8、【短信】接口：短信明细查询接口返回失败原因",
                },
                {
                    value:
                        "9、【短信】接口：管理商新开子用户，用户名支持“_”符号",
                },
                {
                    value:
                        "10、【资源】资源平台通道子端口实名管理导出功能优化：新增签名主体与企业名称一致性匹配字段",
                },
                {
                    value:
                        "11、【资源】服务号码判断规则：针对存量数据，不需要客户做更改，系统更新缓存",
                },
                {
                    value:
                        "12、【资源】增加移动链接和号码数据缓存",
                },
                {
                    value:
                        "13、【资源】系统支持移动备案信息导出",
                }
            ],
    },
{
    id: '88',
        time: "2025/6/26更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：手机号备案管理接口新增类型，填写企业名称和社会统一信用代码",
                },
                {
                    value:
                        "2、【短信】客户端：域名备案增加审核流程，以及备案图片和盖章扫描件",
                },
                {
                    value:
                        "3、【短信】客户端：文件上传发送，读取excel的sheet不一致的问题",
                },
                {
                    value:
                        "4、【短信】运营端：视频产品增加下失败统计数据",
                },
                {
                    value:
                        "5、【短信】匹配模板发送，根据缓存结构变更",
                },
                {
                    value:
                        "6、【短信】同步热模板缓存任务",
                },
                {
                    value:
                        "7、【短信】备案信息接口",
                },
                {
                    value:
                        "8、【短信】添加登录手机号，将手机号加成一级白名单",
                },
                {
                    value:
                        "9、【短信】个性化语音验证码接口",
                },
                {
                    value:
                        "10、【短信】自动补发失败回执补齐不再忽略渠道用户",
                },
                {
                    value:
                        "11、【短信】客户端-帮助中心增加动态与公告信息",
                },
            ],
    },
{
    id: '87',
        time: "2025/6/19更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：签名管理增加批量导入手机号功能",
                },
                {
                    value:
                        "2、【短信】客户端：黑名单列表，手机号做掩码处理",
                },
                {
                    value:
                        "3、【短信】客户端：短信模板验证码类型支持数字变量",
                },
                {
                    value:
                        "4、【短信】客户端：移动手机号备案信息维护",
                },
                {
                    value:
                        "5、【短信】客户端：移动链接备案信息维护",
                },
                {
                    value:
                        "6、【短信】客户端/运营端：添加手机号之后，签名管理界面签名批量自动恢复亮灯",
                },
                {
                    value:
                        "7、【短信】客户端/接口：支持管理商给子账号设置模板推送地址",
                },
                {
                    value:
                        "8、【短信】运营端：号码审核界面增加一键加白功能",
                },
                {
                    value:
                        "9、【短信】客户端：阅信模版列表显示不全修复",
                },
                {
                    value:
                        "10、【短信】客户端：签名管理，上传资质图片加在线预览",
                },
                {
                    value:
                        "11、【短信】运营端：“运营批量操作”：添加白名单/黑名单，自动加逗号",
                },
                {
                    value:
                        "12、【短信】运营端：电信5g通道支持复制功能",
                },
                {
                    value:
                        "13、【短信】针对电信链接跟手机拦截做自动补发功能",
                },
                {
                    value:
                        "14、【短信】签名自动审核规则更新",
                },
                {
                    value:
                        "15、【短信】短信热模板队列自动维护",
                },
                {
                    value:
                        "16、【资源】移动实名：针对存量签名，客户补充责任人手机号后，移动子端口自动更新",
                }
            ],
    },
{
    id: '86',
        time: "2025/6/12更新内容",
            content: [
                {
                    value:
                        "1、【短信】联通号码短信发送匹配模板发送",
                },
                {
                    value:
                        "2、【短信】夜间通道流速自动调整",
                },
                {
                    value:
                        "3、【短信】客户端自动提取模版功能",
                },
                {
                    value:
                        "4、【资源系统】江苏真实子端口失败原因“不存在”改为“待报备”",
                },
                {
                    value:
                        "5、【资源系统】“通道管理-通道子端口实名管理”，重点客户的签名，可以通过表格导入处理后导出，得到该签名在所有通道报备的情况",
                },
                {
                    value:
                        "6、【资源系统】优化模版子端口太多导致签名实名状态同步延迟问题",
                },
            ],
    },
{
    id: '85',
        time: "2025/6/4更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：模板管理界面支持变量模版批量导入",
                },
                {
                    value:
                        "2、【短信】客户端：模板管理界面增加模板状态筛选条件",
                },
                {
                    value:
                        "3、【短信】客户端：模板管理界面短信模板导出增加模板的驳回原因",
                },
                {
                    value:
                        "4、【短信】运营端：携号转网库支持导入表格的功能",
                },
                {
                    value:
                        "5、【短信】运营端：电信模版导入后不进审核，直接变为“审核成功”",
                },
                {
                    value:
                        "6、【短信】运营端：模版审核页面，增加排除用户",
                },
                {
                    value:
                        "7、【短信】运营端：针对电信号码和链接拦截下发失败的特定错误码，支持针对账号批量补发，同时发送状态暂时不推送给客户",
                },
                {
                    value:
                        "8、【短信】接口&客户端：去掉签名报备页面的模版示例提交（不支持提交多个）",
                },
                {
                    value:
                        "9、【短信】接口&客户端：签名报备界面添加经办人和责任人手机号",
                },
                {
                    value:
                        "10、【短信】电信号码匹配电信模版增加IP&号码匹配",
                },
                {
                    value:
                        "11、【短信】安全相关：目前绑定的内网IP全部清理掉",
                },
                {
                    value:
                        "12、【5G】电信5G chatbot，新增成功的状态同步到资源系统",
                },
                {
                    value:
                        "13、【资源】江苏真实子端口失败原因“不存在”改为“待报备”",
                },
                {
                    value:
                        "14、【资源】“通道管理-通道子端口实名管理”，重点客户的签名，可以通过表格导入处理后导出，得到该签名在所有通道报备的情况",
                },
            ],
    },
{
    id: '84',
        time: "2025/5/27更新内容",
            content: [
                {
                    value:
                        "1、【短信】电信模版报备",
                },
                {
                    value:
                        "2、【短信】运营端：补发管理可以直接添加账号，便于针对某账号进行补发",
                },
                {
                    value:
                        "3、【短信】运营端：模版审核页面，每页显示数量调整为默认50条",
                },
                {
                    value:
                        "4、【短信】运营端：针对运营平台支持发送内容解码查询",
                },
                {
                    value:
                        "5、【短信】运营端：模版审核页面，增加排除用户",
                },
                {
                    value:
                        "6、【短信】运营端：短信支持运营商端审核增加短信细类选择",
                },
                {
                    value:
                        "7、【短信】运营端：支持导入模版示例",
                },
                {
                    value:
                        "8、【短信】bug修复：1289通道有SP码号扩展导致下发的数据匹配不上，最后只能根据手机号匹配上行",
                },
                {
                    value:
                        "9、【短信】优化账号解密不正常问题",
                },
            ],
    },
{
    id: '83',
        time: "2025/5/22更新内容",
            content: [
                {
                    value:
                        "1、【短信】安全审计：管理商编辑子账户信息接口，登录手机号不再明文存储",
                },
                {
                    value:
                        "2、【接口BUG：管理商查询子账户信息接口，查询不到子账户的登录手机号",
                },
                {
                    value:
                        "3、【视频短信】视频通道区分全网、省网，省网优先",
                },
                {
                    value:
                        "4、【资源系统】优化签名简单报备匹配逻辑",
                },
                {
                    value:
                        "5、【短信】回执推送增加手机号运营商信息",
                },
                {
                    value:
                        "6、【短信】运营端：导入签名示例",
                },
            ],
    },
{
    id: '82',
        time: "2025/5/15更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：管理商创建子账户时，默认签名可以选择管理商已有的",
                },
                {
                    value:
                        "2、【短信】客户端：新增签名填写报备示例界面",
                },
                {
                    value:
                        "3、【短信】运营端：账号解密功能（便于接口日志加密后的查询）",
                },
                {
                    value:
                        "4、【短信】运营端：短信安全相关–新开账号接口密码强度加大（32+）",
                },
                {
                    value:
                        "5、【短信】运营端：短信安全相关—接口IP/动态IP不允许内网IP提交绑定",
                },
                {
                    value:
                        "6、【短信】运营端：优化当前查询客户账号配置通道步骤",
                },
                {
                    value:
                        "7、【短信】运营端：新增外部黑名单检测（供应商：253）",
                },
                {
                    value:
                        "8、【接口】增加短信签示例新增接口（终端、管理商、管理商子账号）",
                },
                {
                    value:
                        "9、【接口】增加短信签示例修改接口（终端、管理商、管理商子账号）",
                },
                {
                    value:
                        "10、【5G】5G上架半自动流程优化",
                },
                {
                    value:
                        "11、【资源】联通子端口报备失败原因为【经办人姓名与其证件号码实名制校验失败；责任人姓名与其证件号码实名制校验失败】自动批量改状态为待更新，重新提交给到联通",
                },
            ],
    },
{
    id: '81',
        time: "2025/5/8更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端/接口：调整签名报备规则（简称附件必填）",
                },
                {
                    value:
                        "2、【短信】客户端/接口：签名报备管理界面-签名来源-网站这个类型拿掉",
                },
                {
                    value:
                        "3、【短信】客户端:添加默认签名字符支持16字符",
                },
                {
                    value:
                        "4、【短信】运营端：签名自动审核逻辑变更（仅只保留全称可以审核通过）",
                },
                {
                    value:
                        "5、【短信】自动补发逻辑变更：携号转网号码通道筛选过滤出携号转网的通道",
                },
                {
                    value:
                        "6、【短信】变量模版发送检查变量值包含手机号进审核、黑词（诈骗关键词）不受账号设置的限制，全局生效、只替换变量值",
                },
                {
                    value:
                        "7、【短信】安全审计：平台端-数据隐私与合规性审计-日志明文需要整改",
                },
                {
                    value:
                        "8、【短信】针对“移动实名失败：一人多企”的批量处理&提交",
                },
                {
                    value:
                        "9、【短信】资源：签名实名信息支持根据签名导出",
                },
                {
                    value:
                        "10、【短信】资源：联通通道子端口签名实名信息同步",
                },
                {
                    value:
                        "11、【短信】融合平台5G上架半自动",
                },
                {
                    value:
                        "12、【视频】取消针对5G回落视频支持5G消息判断的功能",
                },
            ],
    },
{
    id: '80',
        time: "2025/4/28更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：短链统计支持短链删除 （华墨展览）",
                },
                {
                    value:
                        "2、【短信】运营端：签名审核界面增加“最近审核时间”的筛选项，且将“首次审核时间”与“最近审核时间”两列的位置对调",
                },
                {
                    value:
                        "3、【短信】运营端：短信内容审核号码识别优化",
                },
                {
                    value:
                        "4、【短信】运营端：短信管理-短信自动补发管理-增加 添加按公司名称来补发",
                },
                {
                    value:
                        "5、【短信】运营端：签名失败代码监控",
                },
                {
                    value:
                        "6、【短信】收钱吧：客户端实名信息两个账号同步优化",
                },
                {
                    value:
                        "7、【短信】德邦：客户定制化接口开发",
                },
                {
                    value:
                        "8、【短信】电信通道流速低时行业消息延迟问题",
                },
                {
                    value:
                        "9、【资源】直接显示签名报备的具体特殊原因",
                },
                {
                    value:
                        "10、【资源】增加江苏电信子端口报备模版",
                }
            ],
    },
{
    id: '79',
        time: "2025/4/24更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：签名管理页面，客户根据实名失败原因重新提交资料后，状态需要刷新（审核中，且去掉实名失败原因）",
                },
                {
                    value:
                        "2、【短信】客户需求：管理商希望在“签名管理”可以搜索名下所有子账户的签名状态",
                },
                {
                    value:
                        "3、【短信】后端需求：防轰炸号码频次限制",
                },
                {
                    value:
                        "4、【短信】后端需求：子账户的签名被审核不通过后，展示驳回原因",
                },
                {
                    value:
                        "5、【短信】针对发送内容中带有座机、手机号码、400电话的，进行扫描审核，可以在账户上设置白名单免审不进审核",
                },
                {
                    value:
                        "6、【短信】收钱吧：客户端实名信息两个账号同步问题",
                },
                {
                    value:
                        "7、【短信】还呗：客户要求的部分错误码映射到指定code",
                },
                {
                    value:
                        "8、【短信】管理商&子账户余额查询接口，支持其他产品的余额条数",
                },
                {
                    value:
                        "9、【短信】客户需求：在获取子账号签名列表接口数据，增加运营商的审核状态（实名）",
                },
                {
                    value:
                        "10、【短信】调整管理商子用户实名签名共享规则",
                }
            ],
    },
{
    id: '78',
        time: "2025/4/17更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：“视频短信模版管理”页面需要增加一个“签名”筛选",
                },
                {
                    value:
                        "2、【短信】客户端：商场产品根据账号属性来展示/隐藏",
                },
                {
                    value:
                        "3、【短信】客户端：签名报备界面的示例图要按照运营商要求更新",
                },
                {
                    value:
                        "4、【短信】客户端：客户批量上传签名，签名不会带在短信示例中",
                },
                {
                    value:
                        "5、【短信】客户端：视频短信的回复明细需要能明码导出",
                },
                {
                    value:
                        "6、【短信】运营端：视频短信-”发送详情“支持签名筛选",
                },
                {
                    value:
                        "7、【短信】运营端：“商城管理-线上注册用户”添加注册来源",
                },
                {
                    value:
                        "8、【短信】运营端：外部黑名单支持“泰迪熊”",
                },
                {
                    value:
                        "9、【短信】运营端：黑模板在黑词里面显示修复",
                },
                {
                    value:
                        "10、【短信】还呗回执状态推送新增号码运营商",
                },
                {
                    value:
                        "11、【资源】资源系统：支持导入签名excel表格，能批量输入通道，更改相应通道调整签名对应的子端口状态，将报备失败的状态更新为“待更新",
                },
                {
                    value:
                        "12、【资源】资源系统：“签名实名信息”页面增加“身份证”条件筛选",
                },
                {
                    value:
                        "13、【资源】资源系统：“签名实名信息”导出附件优化",
                },
                {
                    value:
                        "14、【资源】资源系统：子端口报备附件下载优化",
                }
            ],
    },
{
    id: '77',
        time: "2025/4/10更新内容",
            content: [
                {
                    value:
                        "1、【资源】子端口报备签名证明信息合并导出",
                },
                {
                    value:
                        "2、【资源】子端口自动延期增加新签名规则",
                },
                {
                    value:
                        "3、【短信】运营端cmpp账号管理增加修改密码功能",
                },
                {
                    value:
                        "4、【短信】运营端签名审核查询条件增加用户类型筛选",
                },
                {
                    value:
                        "5、【短信】签名实名审核通知优化通知到对应销售",
                },
                {
                    value:
                        "6、【短信】运营端支持达能定制化发送流程",
                },
                {
                    value:
                        "7、【短信】用户监控-短信成功率监控",
                },
                {
                    value:
                        "8、【短信】用户监控-短信回执率监控",
                },
                {
                    value:
                        "9、【短信】签名自动审核规则新增企业名称和签名一致的情况",
                },
                {
                    value:
                        "10、【短信】数据中心添加了投诉的号码自动同步到融合平台成为黑名单",
                },
                {
                    value:
                        "11、【短信】管理商用户，在子账户“用户详情”更改“默认签名”时，需要可以同步添加“短信示例”",
                },
                {
                    value:
                        "12、【短信】客户端与接口端签名报备界面把签名内容自动带入短信内容中",
                },
                {
                    value:
                        "13、【短信】运营端：签名审核页面添加搜索条件，可以通过搜索管理商直接展示名下所有子账户签名",
                },
                {
                    value:
                        "14、【短信】客户端与接口端签名报备界面把短信类型和短信内容加*为必填项",
                },
                {
                    value:
                        "15、【短信】融合平台签名实名状态匹配规则调整：剔除用户通道组中的省网通道",
                },
                {
                    value:
                        "16、【短信】客户端视频短信-“模版管理”显示签名",
                },
                {
                    value:
                        "17、【短信】运营端：“签名审核”页面去掉“请确认以下注意事项”部分",
                },
                {
                    value:
                        "18、【短信】废弃签名报备老接口",
                },
                {
                    value:
                        "19、【短信】还呗错误码映射",
                },
                {
                    value:
                        "20、【短信】管理商增加子用户签名接口，支持设置默认签名",
                },
                {
                    value:
                        "21、【5g】5g企业注册实名信息支持非直客企业",
                },
            ],
    },
{
    id: '76',
        time: "2025/3/27更新内容",
            content: [
                {
                    value:
                        "1、【短信】接口和web端、资源签名报备签名来源由产品调整成APP、小程序、公众号、网站",
                },
                {
                    value:
                        "2、【短信】运营端签名审核列表支持批量导出签名功能",
                },
                {
                    value:
                        "3、【短信】运营端：“批量加白或者加黑”，支持子账号、以及手机号支持xlsx、xls导入",
                },
                {
                    value:
                        "4、【短信】新增签名实名信息报备结果查询页面",
                },
                {
                    value:
                        "5、【短信】签名实名信息校验、数据中心同步签名实名信息失败提醒至企业微信群",
                },
                {
                    value:
                        "6、【资源】资源系统子端口报备支持配置不同签名来源",
                },
                {
                    value:
                        "7、【短信】签名实名信息校验，法人姓名带有中文括号处理优化",
                },
                {
                    value:
                        "8、【短信】签名报备实名信息经办人是法人，不再校验是否是一人多企",
                },
                {
                    value:
                        "9、【短信】签名来源是企业名称、且签名是企业简称，则自动审核通过",
                },
                {
                    value:
                        "10、【短信】优化签名实名合并推送，重新审核后，可重新推送实名状态",
                }
            ],
    },
{
    id: '75',
        time: "2025/3/20更新内容",
            content: [
                {
                    value:
                        "1、【短信】5g通道企业更新增加字段",
                },
                {
                    value:
                        "2、【短信】客户端、运营端签名列表优化实名状态的准确性",
                },
                {
                    value:
                        "3、【短信】客户端、接口支持管理商给子账户配置签名推送地址",
                },
                {
                    value:
                        "4、【短信】客户端管理商用户详情更改子账号“默认签名”时，如果签名存在或者签名没有实名信息，弹出对话框让其补充新签名的相关资质信息，再进入审核",
                },
                {
                    value:
                        "5、【短信】运营端用户增加是否能购买线上产品的能力配置",
                },
                {
                    value:
                        "6、【短信】运营端签名审核界面支持排除用户的功能、增加创建人、更新人展示",
                },
                {
                    value:
                        "7、【短信】运营端管理角色授权页面修复编辑缓存问题",
                },
                {
                    value:
                        "8、【资源】运营端管理客户签名增加签名实名信息",
                },
                {
                    value:
                        "9、【阅信】北森签名报备定制化接口：1.法人字段不设为必填；2、图片要base 64编码后的字符，没有url",
                },
                {
                    value:
                        "10、【短信】阅信VIVO变量支持",
                }
            ],
    },
{
    id: '74',
        time: "2025/3/13更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端完善签名实名信息时支持图片上传",
                },
                {
                    value:
                        "2、【短信】客户端签名导入支持实名信息导入",
                },
                {
                    value:
                        "3、【短信】客户端签名列表展示签名实名状态",
                },
                {
                    value:
                        "4、【短信】客户端管理商用户管理列表新增实名信息审核状态",
                },
                {
                    value:
                        "5、【短信】客户端支持管理商编辑子账号签名实名信息，且支持图片上传",
                },
                {
                    value:
                        "6、【短信】运营端管理商子用户实名信息支持编辑",
                },
                {
                    value:
                        "7、【短信】运营端签名审核列表增加是否首次审核",
                },
                {
                    value:
                        "8、【资源】长时间未使用的联通子端口实名状态自动更新",
                },
                {
                    value:
                        "9、【阅信】快手阅信接口ip优化",
                },
                {
                    value:
                        "10、【短信】签名报备接口增加附件上传",
                },
                {
                    value:
                        "11、【短信】管理商新增给子用户签名实名报备接口",
                },

            ],
    },
{
    id: '73',
        time: "2025/3/11更新内容",
            content: [
                {
                    value:
                        "1、【短信】接口端&客户端，资质材料上传功能优化",
                },
                {
                    value:
                        "2、【短信】“签名管理”-“创建签名”中的“签名类型”，去掉部分选项",
                },
                {
                    value:
                        "3、【短信】“签名管理”-“创建签名”中的“选择主体”，优化显示方式并添加提示",
                },
                {
                    value:
                        "4、【短信】运营端-“签名审核”-“实名信息”框调成可以移动的",
                },
                {
                    value:
                        "5、【短信】运营端-“签名审核”-“实名信息”既展示当前主体、也展示其他主体信息",
                },
                {
                    value:
                        "6、【短信】新增子用户时，会录入资质信息，默认签名报备生成子端口时需要匹配使用该子用户的资质信息",
                },
                {
                    value:
                        "7、【短信】驳回的签名，重新点击编辑，没有选择当前主体和其他主体的按钮",
                },
                {
                    value:
                        "8、【短信】资源系统签名管理功能",
                },

            ],
    },
{
    id: '72',
        time: "2025/3/6更新内容",
            content: [
                {
                    value:
                        "1、【短信】新增代报签名报备接口，接口含提交实名信息",
                },
                {
                    value:
                        "2、【短信】实名信息校验通过的同步到资源系统",
                },
                {
                    value:
                        "3、【短信】客户端和接口签名报备，校验子账号是否补充实名信息，未补充不给报备",
                },
                {
                    value:
                        "4、【短信】客户端和接口管理商新增补充子用户实名信息",
                },
                {
                    value:
                        "5、【短信】客户端和接口管理商创建子用户填写实名信息，客户端必填，接口不必填",
                },
                {
                    value:
                        "6、【短信】新签名报备成功、生成子端口后，立即触发给客户绑定的手机号发一条短信",
                }

            ],
    },
{
    id: '71',
        time: "2025/2/27更新内容",
            content: [
                {
                    value:
                        "1、【短信】调整回调推送的状态码为7位长度",
                },
                {
                    value:
                        "2、【短信】用户行为监控：超日限额提醒（针对行业发送量管控）",
                },
                {
                    value:
                        "3、【短信】阅信--产品维护更新（移动当前支持8个厂商）",
                },
                {
                    value:
                        "4、【短信】“独立平台”更新信用飞定制代码",
                },
                {
                    value:
                        "5、【短信】客户需求（快手）定制短信发送接口",
                },
                {
                    value:
                        "6、【短信】实名资料未完善弹窗废弃",
                },
                {
                    value:
                        "7、【短信】链接申请申请白名单域名自动提取",
                }

            ],
    },
{
    id: '70',
        time: "2025/2/20更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端一键认证支持统计总览数据导出",
                },
                {
                    value:
                        "2、【短信】运营端短链追踪统计页面优化",
                },
                {
                    value:
                        "3、【短信】运营端动态ip支持批量续期",
                },
                {
                    value:
                        "4、【短信】运营端支持配置回复数据改写的账号和百分比",
                },
                {
                    value:
                        "5、【短信】增加消息id查询的统计接口+明细接口",
                },
                {
                    value:
                        "6、【短信】提供给客户使用域名区分的测试发送接口、且支持白名单号码正常发送接收(霸王茶姬)",
                },
                {
                    value:
                        "7、【视频短信】视频短信发送明细支持一键加白功能",
                },
                {
                    value:
                        "8、【短信】修复发送活动页面时间选择器出现null的问题",
                },
                {
                    value:
                        "9、【短信】客户端登陆优化用户名大小写错误提示",
                },

            ],
    },
{
    id: '69',
        time: "2025/2/13更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端支持导出明文短链追踪的数据",
                },
                {
                    value:
                        "2、【短信】元保短信发送内容html转义处理",
                },
                {
                    value:
                        "3、【短信】余额扣款异常区分余额不足",
                },
                {
                    value:
                        "4、【短信】提取黑模版库内容中的链接添加到固定敏感词标签库，该标签库下黑词直接下发失败",
                },
                {
                    value:
                        "5、【短信】维护短信发送明细的补发通道号和补发状态码，用于数据中心计算实际下发通道的利润",
                },
                {
                    value:
                        "6、【短信】达能回执、回复定制推送",
                },
                {
                    value:
                        "7、【短信】联通数科定制化回执报文推送",
                },
                {
                    value:
                        "8、【短信】优化阅信页面关闭后重新打开请求无效错误",
                },

            ],
    },
{
    id: '68',
        time: "2025/1/21更新内容",
            content: [
                {
                    value:
                        "1、【短信】账号号码库分流检测（瑞辛）",
                },
                {
                    value:
                        "2、【短信】加白过的IP，过期审核后自动续期。",
                },
                {
                    value:
                        "3、【短信】融合平台弹窗开发（针对三个月未发送客户冻结通知）",
                },
                {
                    value:
                        "4、【短信】资源系统签名报备成功的，自动回传到融合",
                },
                {
                    value:
                        "5、【短信】“客户模版管理-参数检测”设置权限开放给客服",
                },
                {
                    value:
                        "6、【短信】“cmpp-ip”绑定权限开放给客服",
                },
                {
                    value:
                        "7、【短信】“系统管理-平台操作日志”查看权限开放给客服",
                },
                {
                    value:
                        "8、【短信】“客户管理-用户账号管理-web功能、短信接口加密方式”编辑权限开放给客服",
                },

            ],
    },
{
    id: '67',
        time: "2025/1/16更新内容",
            content: [
                {
                    value:
                        "1、【前端】融合平台开放客服重启账号的权限",
                },
                {
                    value:
                        "2、【前端】融合平台相关权限开放给客服 （开启/关闭接口端提交）",
                },
                {
                    value:
                        "3、【短信】上行匹配规则调整新增通道+扩展号匹配",
                },
                {
                    value:
                        "4、短信】黑模版功能优化删除关键词、默认相似度80% ，增加导入功能",
                },
                {
                    value:
                        "5、【短信】支持API接口进行管理账号",
                },

            ],
    },
{
    id: '66',
        time: "2025/1/9更新内容",
            content: [
                {
                    value:
                        "1、【前端】运营端按照签名筛选客户同一类型签名，进行批量操作",
                },
                {
                    value:
                        "2、【前端】运营端模板审核增加切换验证码功能，目前仅能切换营销通知",
                },
                {
                    value:
                        "3、【短信】运营端针对账号可配置最晚回执时间 （默认最晚68小时回执）",
                },
                {
                    value:
                        "4、【短信】接口和客户端各自校验是否开启了API能力、WEB能力",
                },
                {
                    value:
                        "5、【短信】联通营销下发按照生成的实际+“1”扩展展示",
                },
                {
                    value:
                        "6、【短信】长短信回执表手机号加密  （当前号码为明文）",
                },
                {
                    value:
                        "7、【短信】客户账单支持多列解密（手机号和发送内容） 当前仅支持号码解密",
                },
                {
                    value:
                        "8、【短信】动态ip需要支持ip段",
                }

            ],
    },
{
    id: '65',
        time: "2024/12/24更新内容",
            content: [
                {
                    value:
                        "1、【短信】贰伍叁接口更换",
                },
                {
                    value:
                        "2、【短信】空号检测供应商(正和顺)对接",
                },
                {
                    value:
                        "3、【短信】动态ip续期需要处理管理商的动态ip",
                },
                {
                    value:
                        "4、【短信】非常用ip进审核，如果是非验证码类型 需要提高模板匹配相似度到85%（跟模板本身阈值比较 取最大）",
                },
                {
                    value:
                        "5、【短信】日限量检查提到检查流程优先级最高，删除后付费的判断",
                },
                {
                    value:
                        "6、【短信】短信黑模板库管理",
                },
                {
                    value:
                        "7、【短信】调整一下审核查询内容标识只需要区分不包含号码和链接、包含号码或链接两种筛选",
                },
                {
                    value:
                        "8、【短信】每日提交限量通知",
                },
                {
                    value:
                        "9、【短信】主体支持模板相似度匹配，用户配置中增加模板匹配类型选择 账号或主体",
                },
                {
                    value:
                        "10、【短信】开发大学漏洞修复",
                }

            ],
    },
{
    id: '64',
        time: "2024/12/12更新内容",
            content: [
                {
                    value:
                        "1、【短信】动态ip续期",
                },
                {
                    value:
                        "2、【短信】内容审核中增加联系方式、链接的标识",
                },
                {
                    value:
                        "3、【短信】发送内容审核号码和链接突出显示",
                },
                {
                    value:
                        "4、【短信】内容审核不自动刷新 ，审核处理完的删除这条记录",
                },
                {
                    value:
                        "5、【短信】后付费账号（主要是子账号）设定每日上限额度",
                },
                {
                    value:
                        "6、【短信】优化签名实名推送方式",
                }

            ],
    },
{
    id: '63',
        time: "2024/12/03更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端模板、自定义、个性化发送定时短信调整成5分钟可发送",
                },
                {
                    value:
                        "2、【短信】运营端可配置24小时共享手机号的账号",
                },
                {
                    value:
                        "3、【短信】后付费账号增加月度发送额度限制",
                },
                {
                    value:
                        "4、【短信】系统自动删除三个月内未触发的自动审核短信模板",
                },
                {
                    value:
                        "5、【视频短信】视频短信子账号回执推送给管理商",
                },
                {
                    value:
                        "6、【短信】联电空号检测支持贰伍叁，并且识别关机停机状态",
                }

            ],
    },
{
    id: '62',
        time: "2024/11/28更新内容",
            content: [
                {
                    value:
                        "1、【短信】非常用IP拦截进审核",
                },
                {
                    value:
                        "2、【短信】凌晨0～8点行业发送数量管控",
                }
            ],
    },
{
    id: '61',
        time: "2024/11/21更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端线上注册一个号码只能注册一个账号",
                },
                {
                    value:
                        "2、【短信】客户端公告系统消息新增公告通知",
                },
                {
                    value:
                        "3、【短信】客户端发送明细导出时间控件支持到小时",
                },
                {
                    value:
                        "4、【短信】运营端支持客服查看与添加客户接口IP",
                },
                {
                    value:
                        "5、【短信】运营端空号检测支持配置贰伍叁供应商",
                },
                {
                    value:
                        "6、【短信】运营端可以查询空号检测明细、成功率监控明细",
                },
                {
                    value:
                        "7、【短信】运营端黑名单规则支持批量删除",
                },
                {
                    value:
                        "8、【短信】空号检测性能优化",
                },
                {
                    value:
                        "9、【短信】可按账号设置同步推送运营商签名报备状态（广州贺氏）",
                },
                {
                    value:
                        "10、【短信】针对测试账号的发送明细，缓存使用redis替换tablestore (南通卓望)",
                },
                {
                    value:
                        "11、【通道】对接新语音通道（南京中太智和）",
                },
                {
                    value:
                        "12、【短信】发送成功率的告警机制，10分钟内没有短信发送的告警、对短信状态报告进行归类处理（北京车之家）",
                },
                {
                    value:
                        "13、【短信】根据客户提供的文档，定制短信发送接口和上下行推送功能（北京微财）",
                },
                {
                    value:
                        "14【短信】根据客户提供的文档，对接字节跳动报备签名接口和签名报备状态的推送功能（字节跳动）",
                }
            ],
    },
{
    id: '60',
        time: "2024/11/14更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端客户提交授权后，客户端回复记录&导出发送内容明码展示",
                },
                {
                    value:
                        "2、【短信】客户端管理商支持查询子账户的签名",
                },
                {
                    value:
                        "3、【短信】运营端短信明细查询支持数字掩码查询",
                },
                {
                    value:
                        "4、【短信】运营端短链支持批量续期",
                },
                {
                    value:
                        "5、【短信】运营端支持联电空号检测设置",
                },
                {
                    value:
                        "6、【短信】手动补发扣费失败，再次补发未扣费问题优化",
                },
                {
                    value:
                        "7、【短信】新增客户默认开启IP短信",
                },
                {
                    value:
                        "8、【短信】新客户定制接口开发",
                },
                {
                    value:
                        "9、【短信】IP短信离线库维护",
                },
                {
                    value:
                        "10、【短信】针对回落视信没有报备好的文本，走其他5G通道",
                }
            ],
    },
{
    id: '59',
        time: "2024/11/12更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端定时发送变量模板，校验手机号和变量",
                },
                {
                    value:
                        "2、【短信】客户端英文签名字数限制32字符内",
                },
                {
                    value:
                        "3、【短信】运营端通道组配置操作优化，提前改配置",
                },
                {
                    value:
                        "4、【短信】签名审核结果个性化推送",
                },
                {
                    value:
                        "5、【短信】资源子端口实名状态同步到平台",
                },
                {
                    value:
                        "6、【短信】针对轻松筹账号，每两天更换一次子端口",
                },
                {
                    value:
                        "7、【短信】自定义接口发送营销队列拆分",
                },
                {
                    value:
                        "8、【短信】黑名单优化，匹配账号所有满足的规则，满足其一则认为是黑名单",
                },
                {
                    value:
                        "9、【视频短信】视频短信支持错误码路由",
                },
                {
                    value:
                        "10、【视频短信】视频短信支持过外部黑名单",
                }
            ],
    },
{
    id: '58',
        time: "2024/10/31更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端发送明细支持模板ID导出",
                },
                {
                    value:
                        "2、【短信】短信自动补发支持按权重优先选择通道",
                },
                {
                    value:
                        "3、【短信】IP短信支持根据机型、运营商按比例选择已报备过签名的通道下发",
                },
                {
                    value:
                        "4、【短信】360成功率优化（棱镜停机号码可路由）",
                },
                {
                    value:
                        "5、【短信】运营端审核通过新签名2天内不路由",
                },
                {
                    value:
                        "6、【短信】部分虚拟运营商号段不路由",
                },
                {
                    value:
                        "7、【短信】诈骗电话违禁一级黑词导入",
                },
                {
                    value:
                        "8、【前端】终端、子用户支持手机端登录平台查看",
                },
                {
                    value:
                        "9、【资源】联通子端口实名，企业名称去除空格，只支持中文括号",
                },
            ],
    },
{
    id: '57',
        time: "2024/10/24更新内容",
            content: [
                {
                    value:
                        "1、【短信】数据明码加密",
                },
                {
                    value:
                        "2、【短信】客户端优化短链有效期提醒",
                },
            ],
    },
{
    id: '56',
        time: "2024/10/15更新内容",
            content: [
                {
                    value:
                        "1、【短信】防轰炸并发拦截失败调整",
                },
                {
                    value:
                        "2、【短信】手动补发一级白名单不过黑名单",
                },
                {
                    value:
                        "3、【短信】新开账号，默认签名进入短信签名审核，同时添加备注",
                },
                {
                    value:
                        "4、【短信】切换通道进审核，默认选择“人工通道组”",
                },
                {
                    value:
                        "5、【短信】短信条件审核列表，可调整放在审核界面",
                },
                {
                    value:
                        "6、【短信】复制模板id的时候有弹窗，影响操作",
                },
                {
                    value:
                        "7、【短信】长短信转视频，按照客户需求增加标题行",
                }
            ],
    },
{
    id: '55',
        time: "2024/10/11更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端支持查看登录手机号明码",
                },
                {
                    value:
                        "2、【短信】5G短信下发区分行业、营销码号",
                },
                {
                    value:
                        "3、【短信】运营端路由设置新增关机路由设置",
                },
                {
                    value:
                        "4、【短信】路由明细提取短链调整为多线程处理",
                },
                {
                    value:
                        "5、【短信】先丰空号检测http请求调整为长连接",
                },
                {
                    value:
                        "6、【短信】防轰炸并发拦截失败调整",
                },
                {
                    value:
                        "7、【短信】黑名单升级2.0",
                }
            ],
    },
{
    id: '54',
        time: "2024/09/25更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端签名支持批量审核（剔除短信类型、行业类型、短信模板校验不过的数据）",
                },
                {
                    value:
                        "2、【短信】运营端管理商支持子账号签名共享",
                },
                {
                    value:
                        "3、【短信】运营端今日发送明细查询优化",
                }
            ],
    },
{
    id: '53',
        time: "2024/09/19更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端已审核通过的模板支持复制。",
                },
                {
                    value:
                        "2、【短信】客户端看发送内容带“拒收请回复R”，下发明细没有（长链转换短链，链接识别不准优化）。",
                },
                {
                    value:
                        "3、【短信】运营端发送计划支持自动间隔半小时提取号码发送。",
                },
                {
                    value:
                        "4、【短信】运营端签名报备支持查看签名实名情况。",
                },
                {
                    value:
                        "5、【短信】运营端账号编辑新签名自动报备。",
                },
                {
                    value:
                        "6、【短信】运营端增加账号白名单校验号码格式。",
                },
                {
                    value:
                        "7、【短信】运营端可配置通道组不走5G。",
                },
                {
                    value:
                        "8、【视频】运营商视频短信支持测试发送。",
                },
                {
                    value:
                        "9、【短信】联通短信实名子端口共享。",
                },
                {
                    value:
                        "10、【短信】账号绑定IP限制，支持IP段。",
                },
                {
                    value:
                        "11、【语音】语音通道支持自动切换",
                },
                {
                    value:
                        "12、【语音】语音短信根据账号设置和错误码自动补发设置自动重播",
                }
            ],
    },
{
    id: '52',
        time: "2024/09/05更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端短信回复查询增加按发送内容查询。",
                },
                {
                    value:
                        "2、【短信】客户端语音和国际发送明细支持明码导出。",
                },
                {
                    value:
                        "3、【短信】短信导出明细，单个Excel的数量由5万调整为50万。",
                },
                {
                    value:
                        "4、【短信】运营端子账号防轰炸2.0没有设置，就展示管理商的设置。",
                },
                {
                    value:
                        "5、【短信】运营端通道改写规则删除时展示通道信息。",
                },
                {
                    value:
                        "6、【短信】运营端所有产品发送明细查询30天调整为近一个月。",
                },
                {
                    value:
                        "7、【视频】运营端视频绑定阅信链接的有效期和阅信链接有效期一致。",
                },
                {
                    value:
                        "8、【短信】运营端成功率监控配置迁移到路由明细，且号码过空号库。",
                },
                {
                    value:
                        "9、【短信】运营端支持批量切换单网和多网账户、批量加白、加黑。",
                },
                {
                    value:
                        "10、【短信】短信长短信计费方式修改。",
                },
                {
                    value:
                        "11、【短信】长短信转视频支持视信回落。",
                },
                {
                    value:
                        "12、【短信】同一主体下的不同账号签名共享。",
                },
                {
                    value:
                        "13、【短信】短信监控通道掉线，权重降低，所有通道均掉线后，发起语音告警。",
                },
                {
                    value:
                        "14、【短信】短信错误码自动补发设置，通道号涵义变更为排除通道号。",
                },
                {
                    value:
                        "15、【短信】短信变量模板发送，支持根据账号设置不校验模板变量。",
                },
                {
                    value:
                        "16、【短信】短链域名白名单支持接口方式提交和审核结果推送。",
                },
                {
                    value:
                        "17、【短信】管理商统计接口信息成功号码数、失败号码数。",
                },
                {
                    value:
                        "18、【短信】管理商发送明细接口支持发送状态查询。",
                }
            ],
    },
{
    id: '51',
        time: "2024/08/29更新内容",
            content: [
                {
                    value:
                        "1、【阅信】支持移动新模板",
                },
                {
                    value:
                        "2、【阅信】老模板优化。",
                },
                {
                    value:
                        "3、【阅信】支持运营渠道全量配置",
                },
                {
                    value:
                        "4、【阅信】优化页面优化查询",
                }
            ],
    },
{
    id: '50',
        time: "2024/08/22更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端支持重置手机号码密文解码次数。",
                },
                {
                    value:
                        "2、【视频短信】客户端视频短信web任务支持导出。",
                },
                {
                    value:
                        "3、【视频短信】客户端视频短信模板编辑默认添加“拒收请回复R”。",
                },
                {
                    value:
                        "4、【视频短信】客户端已审核通过视频短信模板支持复制。",
                },
                {
                    value:
                        "5、【短信】运营端支持查询长短信回执明细。",
                },
                {
                    value:
                        "6、【短信】长短信转视频发送。",
                },
                {
                    value:
                        "7、【短信】运营端发送明细支持模板ID、管理商查询。",
                },
                {
                    value:
                        "8、【短信】运营端通道设置支持市网通道设置。",
                },
                {
                    value:
                        "9、【短信】运营端、接口查询短信变量模板兼容历史数据（新老模板变量规则不一致）。",
                },
                {
                    value:
                        "10、【短信】短链点击导出表格支持点击时间、点击IP。",
                },
                {
                    value:
                        "11、【短信】携号转网失败回执也推送给客户（当前携号转网失败的回执不推送给客户）。",
                },
                {
                    value:
                        "12、【短信】首次发送失败的行业和验证码类型的号码均过三方库。",
                },
                {
                    value:
                        "13、【短信】短信营销类型的短信支持特定失败代码自动补发（行业和验证码类型必定补发）。",
                },
                {
                    value:
                        "14、【短信】空号检测对接先丰供应商。",
                },
                {
                    value:
                        "15、【短信】实号库、空号库维护(发送成功的入实号库（有1、2、3有效期），直连通道的特定错误码、根据运营商入空号库)。",
                },
                {
                    value:
                        "16、【短信】实号库、三方空号库使用规则调整 (按账号设置去选择实号库校验、账号配置支持多个空号库校验)。",
                },
                {
                    value:
                        "17、【短信】接口密码升级，历史数据加密处理。",
                },
                {
                    value:
                        "18、【语音】阿里云语音通道对接。",
                }
            ],
    },
{
    id: '49',
        time: "2024/08/08更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端签名导入支持模板示例。",
                },
                {
                    value:
                        "2、【管理商】管理商可以查看子账户登录手机号。",
                },
                {
                    value:
                        "3、【管理商】管理商子给子用户充值，充值提醒给子账号预警联系人。",
                },
                {
                    value:
                        "4、【短信】可按短信类型设置每日指定时间段，其他时间均挂审。",
                },
                {
                    value:
                        "5、【短信】按区域提取发送内容中长链，转换为助通短链，并支持导出。",
                },
                {
                    value:
                        "6 、【短信】可设置指定账户监控发送成功率（360成功率监控）。",
                },
                {
                    value:
                        "7 、【签名】增加删除签名接口。",
                },
                {
                    value:
                        "8 、【视频短信】视频模板下发通道选择区分主备通道。",
                },
                {
                    value:
                        "9 、【语音】客服端和接口增加中文失败代码注释。",
                },
                {
                    value:
                        "10 、【资源】江苏移动子端口自动报备。",
                },
                {
                    value:
                        "11 、【资源】签名查询子端口实名信息。",
                },
                {
                    value:
                        "12 、【资源】实名子端口自动化测试。",
                },
                {
                    value:
                        "13 、【资源】子端口实名VIP优先。",
                },
                {
                    value:
                        "14 、【资源】联通子端口同步查询状态。",
                },
                {
                    value:
                        "15 、前端】管理商移动端修改登录手机号、接口密码、密钥等。",
                },
                {
                    value:
                        "16 、【接口密码】接口密码数据库存储升级，运营端开账号接口密码系统自动生成，运营人员暂时不能修改账户接口密码。",
                }
            ],
    },
{
    id: '48',
        time: "2024/07/25更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端通道配置添加是否抹签名、新增通道时维护通道状态。",
                },
                {
                    value:
                        "2、【短信】运营端添加/导入签名需录入短信模板/短信类型/营销二类。",
                },
                {
                    value:
                        "3、【短信】运营端路由明细支持查询运营商/省份/城市。",
                },
                {
                    value:
                        "4、【短信】新开账号验证码不过黑名单。",
                },
                {
                    value:
                        "5、【短信】新开账号默认签名自动报备。",
                },
                {
                    value:
                        "6 、【短信】短信下发时的扩展逻辑保持一致。",
                },
                {
                    value:
                        "7 、【短信】一级白名单不能降为二级白名单。",
                },
                {
                    value:
                        "8 、【短信】登录手机号管理升级，支持管理员手机号。",
                },
                {
                    value:
                        "9 、【短信】回执、回复、视频模板审核状态推送地址含中文参数优化",
                },
                {
                    value:
                        "10 、【短信】融合平台短信先报再发校验签名。",
                },
                {
                    value:
                        "11 、【视频短信】视频模板审核推送新增变量名。",
                }
            ],
    },
{
    id: '47',
        time: "2024/07/11更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端未实名账号新增实名资料完善页面。",
                },
                {
                    value:
                        "2、【短信】客户端短信回复明细支持明码导出。",
                },
                {
                    value:
                        "3、【短信】客户端和接口签名报备添加短信内容示例。",
                },
                {
                    value:
                        "4、【短信】运营端签名审核添加短信类型、行业类型、内容示例。",
                },
                {
                    value:
                        "5、【短信】运营端域名白名单支持删除。",
                },
                {
                    value:
                        "6 、【短信】运营端发送统计错误码中文解释优化。",
                },
                {
                    value:
                        "7 、【短信】所有产品客户端、运营的发送任务号码掩码。",
                },
                {
                    value:
                        "8 、【短信】自动补发失败回执补齐。",
                },
                {
                    value:
                        "9 、【资源】IP短信数据导入。",
                },
                {
                    value:
                        "10 、【资源】营业执照和身份证OCR识别校验，原因分类。",
                },
                {
                    value:
                        "11 、【资源】签名扩展+账号序号、账号扩展+签名序号子端口收集。",
                }
            ],
    },
{
    id: '46',
        time: "2024/07/8更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端失败统计失败代码加中文注释。",
                },
                {
                    value:
                        "2、【短信】发送明细新增内容计费数字段。",
                },
                {
                    value:
                        "3、【短信】按账号标签可推送下发状态的回执（标签：下发回执）。",
                },
                {
                    value:
                        "4、【短信】账号配置推送地址不再仅限于推送。",
                },
                {
                    value:
                        "5、【短信】按账号设置过空号检测。",
                },
                {
                    value:
                        "6 、【短信】信飞接口联调。",
                },
                {
                    value:
                        "7 、【短信】短链存量数据处理。",
                },
                {
                    value:
                        "8 、【短信】阅信全量发送任务&& 短信创建模板支持长链转短链时校验链接 。",
                },
                {
                    value:
                        "9 、【5G】移动chatbot注册新增字段。",
                },
                {
                    value:
                        "10 、管理商手机端支持短信明细查询。",
                }
            ],
    },
{
    id: '45',
        time: "2024/06/27更新内容",
            content: [
                {
                    value:
                        "1、【短信】短信、视频发送明细支持明码导出。",
                },
                {
                    value:
                        "2、【短信】长短信回执保持一致。",
                },
                {
                    value:
                        "3、【短信】子账户余额变动记录查询添加备注。",
                },
                {
                    value:
                        "4、【短信】运营端实时统计。",
                },
                {
                    value:
                        "5、【短信】通道组可设置比例发送。",
                },
                {
                    value:
                        "6 、【短链】短链申请域名白名单设置。",
                }
            ],
    },
{
    id: '44',
        time: "2024/06/20更新内容",
            content: [
                {
                    value:
                        "1、【短信】信飞定制接口。",
                },
                {
                    value:
                        "2、【短信】查询明细接口反参新增失败原因。",
                },
                {
                    value:
                        "3、【短信】客户端支持签名和模板批量导入。",
                },
                {
                    value:
                        "4、【短信】运营端发送明细支持自动补发/手动补发记录查询。",
                },
                {
                    value:
                        "5、【短信】运营端通道切换支持按短信类型切换。",
                },
                {
                    value:
                        "6 、【短信】运营端配置通道同步通道缓存。",
                },
                {
                    value:
                        "7 、【短信】运营端支持批量操作账号配置。",
                },
                {
                    value:
                        "8 、【视频】视频转5G通道报备失败的模板，通知到客服通知群。",
                },
                {
                    value:
                        "9 、【闪验】运营端闪验app列表支持appid查询。",
                },
                {
                    value:
                        "10 、【国际】国际短信回执、补发报文新增计费金额&处理中心发送记录监控。",
                },
                {
                    value:
                        "11 、【5G】优化5G企业注册身份证信息。",
                },
                {
                    value:
                        "12 、【前端】密码双因子问题优化。",
                },
                {
                    value:
                        "13 、【前端】管理商手机端优化。",
                }
            ],
    },
{
    id: '43',
        time: "2024/06/13更新内容",
            content: [
                {
                    value:
                        "1、【短信】余额提醒（设置时自动重置）。",
                },
                {
                    value:
                        "2、【短信】定期清理已过期的自动审核模板。",
                },
                {
                    value:
                        "3、【短信】运营端账单发送列表支持收件箱搜索。",
                },
                {
                    value:
                        "4、【视频】chatbot上架成功之后视频模板自动重新报备。",
                },
                {
                    value:
                        "5、【视频】视频短信手动补发优先选其他非5G跳转通道补发。",
                },
                {
                    value:
                        "6 、【视频】视频模板审核推送新增视频标题。",
                },
                {
                    value:
                        "7 、【5G】企业列表支持创建时间查询。",
                },
                {
                    value:
                        "8 、【5G】电信转5G上行回复、电信转5G支持多媒体消息。",
                }
            ],
    },
{
    id: '42',
        time: "2024/06/06更新内容",
            content: [
                {
                    value:
                        "1、【短信】支持电信号码转到5G通道下发。",
                },
                {
                    value:
                        "2、【短信】修复编辑自定义定时短信任务，未校验签名。",
                },
                {
                    value:
                        "3、【短信】会员、商城、计费中心操作日志关联登录手机号。",
                },
                {
                    value:
                        "4、【视频短信】近30天内视频模板5G通道临期自动续期。",
                },
                {
                    value:
                        "5、【视频短信】京东接口优化响应和异步提交。",
                },
                {
                    value:
                        "6 、【5G】电信chatbot子端口报备。",
                },
                {
                    value:
                        "7 、【5G】电信文本消息发送&上行。",
                }
            ],
    },
{
    id: '41',
        time: "2024/05/29更新内容",
            content: [
                {
                    value:
                        "1、【短信】修复还呗短信发送加解密密钥配置错误的问题。",
                },
                {
                    value:
                        "2、【视频短信】视频短信支持12小时内自动补发。",
                },
                {
                    value:
                        "3、【视频短信】视频模板支持重新报备、新增报备和添加、删除已报备的模板。",
                },
                {
                    value:
                        "4、【5G】回落时间（视信1小时回落）。",
                }
            ],
    },
{
    id: '40',
        time: "2024/05/09更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端、运营端支持登陆日志、操作日志查询，同时记录手机号。",
                },
                {
                    value:
                        "2、【短信】支持棱镜空号检测。",
                },
                {
                    value:
                        "3、【短信】运营端新开账号接口密码强度提升。",
                },
                {
                    value:
                        "4、【短信】短信内容如果是报备签名则扩展以报备签名扩展下发。",
                },
                {
                    value:
                        "5、【短信】运营端统一产品设置页面风格。",
                },
                {
                    value:
                        "6、【短信】运营端注册用户界面优化。",
                },
                {
                    value:
                        "7、【视频短信】视频转5G，移动模板审核状态忽略5G模板的状态。",
                },
                {
                    value:
                        "8、【5g】修复复制的chatbot主动上架 导致状态不一致问题",
                }
            ],
    },
{
    id: '39',
        time: "2024/04/25更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端创建签名规范提示优化、运营端登录之后跳转首页。",
                },
                {
                    value:
                        "2、【短信】创建、编辑账号校验默认签名(【】置空)。",
                },
                {
                    value:
                        "3、【短信】运营端新建账号删除默认签名、并且默认不支持自定义扩展。",
                },
                {
                    value:
                        "4、【短信】web发送任务发送号码掩码。",
                },
                {
                    value:
                        "5、【短信】短链新增产品启用状态，未启用的账号不支持创建短链。",
                },
                {
                    value:
                        "6、【短信】短信审核不再支持指定扩展下发。",
                },
                {
                    value:
                        "7、【短信】联通通道选择实现先报后发。",
                },
                {
                    value:
                        "8、【视频短信】视频转5G，优化签名和模板报备。",
                }
            ],
    },
{
    id: '38',
        time: "2024/04/11更新内容",
            content: [
                {
                    value:
                        "1、【视频短信】视频短信转5G发送。",
                }
            ],
    },
{
    id: '37',
        time: "2024/03/28更新内容",
            content: [
                {
                    value:
                        "1、【短信】在线注册实名合同地址存储地址优化。",
                },
                {
                    value:
                        "2、【短信】回复明细支持消息ID查询。",
                },
                {
                    value:
                        "3、【短信】长链转短链,长链接字符数限制最大1000。",
                },
                {
                    value:
                        "4、【短信】5G企业注册新增企业ID。",
                },
                {
                    value:
                        "5、【短信】新增账号IP白名单拦截进审核。",
                },
                {
                    value:
                        "6、【短信】5G通道编辑行业类型编码支持关键字查询。",
                },
                {
                    value:
                        "7、【资源】5Gchatbot子端口报备逻辑变更。",
                },
                {
                    value:
                        "8、【彩信】彩信转视频下发。",
                },
                {
                    value:
                        "9、【视频短信】视频短信转5G下发。",
                }
            ],
    },
{
    id: '36',
        time: "2024/03/21更新内容",
            content: [
                {
                    value:
                        "1、【短信】自动审核模板支持忽略签名校验。",
                },
                {
                    value:
                        "2、【短信】短信防轰炸手机号格式化(手机号86，+86问题)。",
                },
                {
                    value:
                        "3、【短信】账单解码邮件发送任务结果异步通知数据中心。",
                },
                {
                    value:
                        "4、【短信】联通子端口先报后发准备(支持报备通道间子端口互相复制、账号扩展、签名扩展报备)。",
                },
                {
                    value:
                        "5、【彩信】彩信下发通道报文添加签名。",
                },
                {
                    value:
                        "6、【视频短信】视频短信号码以原始运营商为准，携号转网的号码不再修正。",
                },
                {
                    value:
                        "7、【视频短信】API接口创建的视频，完善模板大小、备注。",
                }
            ],
    },
{
    id: '35',
        time: "2024/03/14更新内容",
            content: [
                {
                    value:
                        "1、【短信】短信报备模板支持拒收回复R快捷方式。",
                },
                {
                    value:
                        "2、【短信】新增获取短链打开次数的接口。",
                },
                {
                    value:
                        "3、【短信】测试账号前20条手机号添加二级白名单。",
                },
                {
                    value:
                        "4、【短信】运营端能支持连接检测。",
                },
                {
                    value:
                        "5、【视频短信】客户端视频短信大小展示。",
                },
                {
                    value:
                        "6、【视频短信】客户端视频短信待返回数统计优化。",
                },
                {
                    value:
                        "7、【视频短信】视频短信快速检索。",
                },
                {
                    value:
                        "8、【视频短信】新增视频短信查询接口。",
                },
            ],
    },
{
    id: '34',
        time: "2023/03/7更新内容",
            content: [
                {
                    value:
                        "1、【短信】网页发送的信息回执 不再推送至rabbitmq。",
                },
                {
                    value:
                        "2、【短信】运营端个人信息、5G企业信息列表号码掩码。",
                },
                {
                    value:
                        "3、【短信】运营端测试发送界面支持短信发送类型选择。",
                },
                {
                    value:
                        "4、【短信】优化运营端查询密文手机号规则。",
                },
                {
                    value:
                        "5、【短信】防轰炸性能优化。",
                },
                {
                    value:
                        "6、【短信】短信国际通道跳转。",
                },
                {
                    value:
                        "7、【短信】运营端新增账单发送列表、以及账单解密邮件发送。",
                },
                {
                    value:
                        "8、【5G】签名报备使用复制通道之后码号以最新输入的为主。",
                },
                {
                    value:
                        "9、【视频短信】优化web端发送，失败数统计问题。",
                }
            ],
    },
{
    id: '33',
        time: "2024/01/30更新内容",
            content: [
                {
                    value:
                        "1、【短信】在线认证+电子合同 + 合同迁移。",
                },
                {
                    value:
                        "2、【短信】运营端注册用户审核列表展示合同编号。",
                },
                {
                    value:
                        "3、【短信】风控只允许开已认证企业的账号（有社会统一信用代码）。",
                },
                {
                    value:
                        "4、【短信】凌晨发送防护机制,【凌晨免监控名单】标签账号除外。",
                },
                {
                    value:
                        "5、【短信】在线注册的账号提醒增加注册来源。",
                },
                {
                    value:
                        "6、【短信】错误码分流低投诉区域不分。",
                },
                {
                    value:
                        "7、【短信】错误码分流结果一致缓存时间可按错误码配置。",
                },
                {
                    value:
                        "8、【短信】cmpp连接列表支持IP查询。",
                },
                {
                    value:
                        "9、【5G】5G chatbot可编辑扩展码。",
                },
                {
                    value:
                        "10、【5G】5G chatbot多签名问题。",
                },
                {
                    value:
                        "11、【5G】5G 新增chatbot删除功能。",
                },
                {
                    value:
                        "12、【5G】运营端chatbot手动复制功能，支持相同通道复制。",
                },
                {
                    value:
                        "13、【阅信BUG】修复运营端编辑模版，替换素材后，素材信息不统一问题。",
                }
            ],
    },
{
    id: '32',
        time: "2024/01/11更新内容",
            content: [
                {
                    value:
                        "1、【短信】外部黑名单渠道新增棱镜实时设置。",
                },
                {
                    value:
                        "2、【短信】管理商子账户防轰炸设置优化。",
                },
                {
                    value:
                        "3、【短信】运营端号码诊断界面优化。",
                },
                {
                    value:
                        "4、【短信】签名列表增加签名查询。",
                },
                {
                    value:
                        "5、【语音】语音个性化发送模板文件替换。",
                },
                {
                    value:
                        "6、【阅信】阅信模板列表不再展示已删除的模板。",
                },
                {
                    value:
                        "7、【阅信】阅信链接申请回执推送增加模板信息",
                },
                {
                    value:
                        "8、【阅信】阅信链接列表展示备注。",
                },
                {
                    value:
                        "9、【5g】优化运营端企业、chatbot批量导入。",
                },
                {
                    value:
                        "10、【5g】chatbot列表新增时间查询、支持导出。",
                }
            ],
    },
{
    id: '31',
        time: "2023/12/14更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端支持视频短信压缩。",
                },
                {
                    value:
                        "2、【短信】客户端不再支持自定义扩展。",
                },
                {
                    value:
                        "3、【短信】在线注册主体同步至资源系统。",
                },
                {
                    value:
                        "4、【短信】联通子端口接口报备。",
                },
                {
                    value:
                        "5、【短信】每日提醒找不到对应企业的子端口。",
                },
                {
                    value:
                        "6、【短信】运营端修复短信状态补推分页不生效问题。",
                }
            ],
    },
{
    id: '30',
        time: "2023/11/30更新内容",
            content: [
                {
                    value:
                        "1、【短信】黑名单扣费余额成负数的问题。",
                },
                {
                    value:
                        "2、【短信】运营端支持手机号一键加白。",
                },
                {
                    value:
                        "3、【短信】短链转换参数问题。",
                },
                {
                    value:
                        "4、【短信】运营端短信测试功能。",
                },
                {
                    value:
                        "5、【短信】cmpp链接ip问题。",
                },
                {
                    value:
                        "6、【短信】敏感词直接失败的匹配自动模板审核流程。",
                }
            ],
    },
{
    id: '29',
        time: "2023/11/16更新内容",
            content: [
                {
                    value:
                        "1、【短信】短信/彩信/视频/语音/国际 防轰炸策略调整。",
                },
                {
                    value:
                        "2、短信、彩信、视频路由机制调整。",
                },
                {
                    value:
                        "3、携号转网添加有效期。",
                }
            ],
    },
{
    id: '28',
        time: "2023/11/9更新内容",
            content: [
                {
                    value:
                        "1、【短信】短信下发防轰炸和黑名单，优先过防轰炸。",
                },
                {
                    value:
                        "2、【短信】运营端新增账号白名单设置，替代系统白名单。",
                },
                {
                    value:
                        "3、【短信】白名单有效期内发送成功，有效期自动延长N个月。",
                },
                {
                    value:
                        "4、【短信】新增账号标签设置。",
                },
                {
                    value:
                        "5、【语音】语音支持防轰炸设置。",
                },
                {
                    value:
                        "6、【5G】chatbot通道复制。",
                },
                {
                    value:
                        "7、【语音】新增语音统计接口。",
                }
            ],
    },
{
    id: '27',
        time: "2023/10/26更新内容",
            content: [
                {
                    value:
                        "1、【短信】某些通道不支持携号转网号码下发。",
                },
                {
                    value:
                        "2、【短信】自动补发验证码类型拆分队列补发。",
                },
                {
                    value:
                        "3、【短信】人工补发系统错误码自动过黑名单。",
                },
                {
                    value:
                        "4、【短信】运营商用户列表可清空账号备注。",
                },
                {
                    value:
                        "5、【短信】新建cmpp账号时，自动调整该账号短信回执为主动抓取。",
                },
                {
                    value:
                        "6、【短信】余额提醒带上公司名。",
                },
                {
                    value:
                        "7、【短信】视频短信模板报备标题长度限制20。",
                },
                {
                    value:
                        "8、【短信】短信审核列表删除黑词关键字。",
                },
                {
                    value:
                        "9、【短信】优化grpc接口tKey校验规则。",
                },
                {
                    value:
                        "10、【视频短信】发送明细支持运营商查询。",
                },
            ],
    },
{
    id: '26',
        time: "2023/10/12更新内容",
            content: [
                {
                    value:
                        "1、【短信】短信模板发送性能优化。",
                },
                {
                    value:
                        "2、【语音】语音通知内容限制500字。",
                },
                {
                    value:
                        "3、【短信通道】运营端：通道设置增加上行匹配规则。",
                },
                {
                    value:
                        "4、【在线注册】在线注册账号接口初始密码调整为复杂密码。",
                },
                {
                    value:
                        "5、【bugfix】修复短信web定时任务批量取消。",
                },
                {
                    value:
                        "6、【bugfix】短信自动审核管理支持状态查询。",
                },
            ],
    },
{
    id: '24',
        time: "2023/9/14更新内容",
            content: [
                {
                    value:
                        "1、【短信】 自定义短信支持自动追加短信内容。",
                },
                {
                    value:
                        "2、【短信】 5G通道跳转可配置最大计费数。",
                },
            ],
    },
{
    id: '23',
        time: "2023/8/03更新内容",
            content: [
                {
                    value:
                        "1、【短信】新增15天、30天防轰炸限制",
                },
                {
                    value:
                        "2、【5Gchatbot检测】新增chatbot通道是否同步配置、是否缓存签名状态展示",
                },
                {
                    value:
                        "3、【阅信优化】运营端可编辑模板、审核展示素材信息、展示文本输入框字数",
                }
            ],
    },
{
    id: '22',
        time: "2023/7/13更新内容",
            content: [
                {
                    value:
                        "1、【短信】若自定义发送的内容和审核通过的普通模板的内容一致，则免审下发",
                },
                {
                    value:
                        "2、【短信】运营端：短信发送支持只发报备签名。拦截代码 ZT:1021",
                },
                {
                    value:
                        "3、【短信】运营端：5G通道筛选逻辑变更，由根据省份匹配规则变成省份+消息类型匹配规则",
                },
                {
                    value:
                        "4、【阅信】运营端：短链管理支持时间搜索",
                },
                {
                    value:
                        "5、【短信】运营端：新增短信内容字数，计费数计算小工具",
                }
            ],
    },
{
    id: '21',
        time: "2023/7/6更新内容",
            content: [
                {
                    value:
                        "1、【短信】短信验证码/行业信息发送失败后检测是否第三方携号转网库（棱镜），不补发失败代码除外",
                },
                {
                    value:
                        "2、【短信】运营端：支持配置外部黑名单",
                },
            ],
    },
{
    id: '20',
        time: "2023/6/29更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：管理商查询子用户发送明细支持模板id查询",
                },
                {
                    value:
                        "2、【短信】运营端：短信审核内容对接可信分发系统，结果仅供审核参考",
                },
                {
                    value:
                        "3、【短信】运营端：短信模板审核时，同时支持变更模板类型",
                },
                {
                    value:
                        "4、【阅信】新增阅信模板追踪链接事件",
                },
                {
                    value:
                        "5、【5G】运营端：新增视频在线压缩",
                },
                {
                    value:
                        "6、【5G】5G企业自动同步优化（审核不过不再同步），5Gchatbot批量导入自动报备，调试后自动上架",
                },
                {
                    value:
                        "7、【短链跳转】增加爬虫IP库，可过滤可疑IP",
                },
            ],
    },
{
    id: '19',
        time: "2023/6/15更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端：通道跳转、个性化通道设置新增创建人、更新人",
                },
                {
                    value:
                        "2、【阅信】华为阅信对接",
                },
                {
                    value:
                        "3、【阅信】优化阅信界面刷新问题，切换tab页之后数据保留",
                },
                {
                    value:
                        "4、【阅信】优化信发送选择模板后，系统自动填充上个发送的签名急内容",
                },
                {
                    value:
                        "5、【阅信】优化信阅信发送任务横滑的问题，界面上太多内容，导致页面需要横滑才能展示全，用图标或者其他方式展示，一屏展示",
                },
                {
                    value:
                        "6、【阅信】新增助通短链统计页面",
                },
                {
                    value:
                        "7、【5G】企业注册新增删除功能",
                },
                {
                    value:
                        "8、【bugfix】号码诊断修复点击加白解除按钮无效问题",
                },
            ],
    },
{
    id: '18',
        time: "2023/6/8更新内容",
            content: [
                {
                    value:
                        "1、【短信】阅信发送任务新增标签",
                },
                {
                    value:
                        "2、【阅信】阅信支持定时发送、取消发送",
                },
                {
                    value:
                        "3、【阅信】运营端新增发送任务列表、取消发送",
                },
                {
                    value:
                        "4、【阅信】发送任务统计、支持文件下载",
                },
                {
                    value:
                        "5、【阅信】模板管理支持更多条件查询",
                },
                {
                    value:
                        "6、【5G-web】新增同步报备企业、chatbot功能",
                },
            ],
    },
{
    id: '17',
        time: "2023/5/30更新内容",
            content: [
                {
                    value:
                        "1、【阅信】web端支持阅信发送",
                },
                {
                    value:
                        "2、【阅信】新增渠道对接（聚梦）",
                },
                {
                    value:
                        "3、【短信】号码诊断优化",
                },
                {
                    value:
                        "4、【短信】修复接口密码修改时验证码与勾选的号码发送不一致问题",
                },
                {
                    value:
                        "5、【短信】修复短信模板编辑是不能保存问题",
                },
            ],
    },
{
    id: '16',
        time: "2023/5/18更新内容",
            content: [
                {
                    value:
                        "1、【短信】下发通道信息新增通道版本号。",
                },
                {
                    value:
                        "2、【短信】通道设置支持清空rabbitQueue队列 ",
                },
                {
                    value:
                        "3、【阅信】平台生成短链以及测试发送支持自定义跳转链接。",
                },
                {
                    value:
                        "4、【阅信】优化个性化链接失败解析数，前端不再用预扣减成功直接取值failCount",
                },
            ],
    },
{
    id: '15',
        time: "2023/5/11更新内容",
            content: [
                {
                    value:
                        "1、【短信】账号每日限量发送",
                },
                {
                    value:
                        "2、【短信】新版添加模板上线",
                },
                {
                    value:
                        "3、【短信】web发送管理时间查询优化 (开始时间 00:00:00-结束时间23:59:59)",
                },
                {
                    value:
                        "4、【短信】短信明细补发列表展示消息ID",
                },
                {
                    value:
                        "5、【短信】修复视频短信明细重置查询",
                },
                {
                    value:
                        "6、【短信】优化记住用户名退出浏览器不清除",
                },
                {
                    value:
                        "7、【阅信】阅信模板列表以及短链列表展示签名，详情展示更多信息",
                },
            ],
    },
{
    id: '14',
        time: "2023/4/26更新内容",
            content: [
                {
                    value:
                        "1、【短信】路由设置优化-错误码和有效期",
                },
                {
                    value:
                        "2、【短信】点击链接查询页面支持时间查询",
                },
                {
                    value:
                        "3、【bugfix】修复语音通知统计时间查询问题",
                },
            ],
    },
{
    id: '13',
        time: "2023/4/20更新内容",
            content: [
                {
                    value:
                        "1、【短信】新增携号转网库页面",
                },
                {
                    value:
                        "2、【5G】chatbot新增通道去除扩展码",
                },
                {
                    value:
                        "3、【5G】去除阅信相关页面；（阅信模板管理；短链管理；阅信费用明细；阅信统计）",
                },
                {
                    value:
                        "4、【5G】5G通道跳转设置修改，新增短信行业类型",
                },
                {
                    value:
                        "5、【阅信】新增行程通知以及电商类模板",
                },
                {
                    value:
                        "6、【阅信】优化运营端阅信模板审核加载问题",
                },
                {
                    value:
                        "7、【阅信】修复阅信公用模板分页失效问题",
                },
            ],
    },
{
    id: '12',
        time: "2023/4/11更新内容",
            content: [
                {
                    value:
                        "1、【会员管理】：支持自定义内容发送",
                },
                {
                    value:
                        "2、【视频短信】支持一键报备",
                },
                {
                    value:
                        "3、【阅信】支持图片轮播、视频图文、图文视频、账单类、增强通知类模板",
                },
                {
                    value:
                        "4、【阅信】新增公用模板库",
                },
                {
                    value:
                        "5、【阅信】支持自定义域名",
                },
                {
                    value:
                        "6、【阅信】优化筛选条件以及文案提示",
                },
                {
                    value:
                        "7、【5G】阅信增强版 置换掉 5G消息回落的 阅信",
                },
            ],
    },
{
    id: '11',
        time: "2023/4/4更新内容",
            content: [
                {
                    value:
                        "1、【短信】5G通道可以针对某个省份可以不区分长短信与单条计费，只要报备过的都走到5G上面发送",
                },
                {
                    value:
                        "2、【阅信】运营端阅信审核页面厂商支持状态查看",
                },
                {
                    value:
                        "3、【阅信】管理商网页端 阅信充值/扣款等",
                },
                {
                    value:
                        "4、【阅信】阅信点击事件不再支持打开链接",
                }
            ],
    },
{
    id: '10',
        time: "2023/3/30更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端：国际短信 + 语音短信回执补推",
                },
                {
                    value:
                        "2、【短信】运营端：短信支持敏感词直接返回失败",
                },
                {
                    value:
                        "3、【5G】运营端：新增chatbot流速展示",
                },
                {
                    value:
                        "4、【5G】运营端：徐州代理商，企业通道码号8位后面自动以80开头；该字段来自5G通道配置",
                },
                {
                    value:
                        "5、【阅信】阅信平台上线",
                }
            ],
    },
{
    id: '9',
        time: "2023/3/23更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：短信下发明细添加对应内容字数显示",
                },
                {
                    value:
                        "2、【短信】运营端：web任务支持发送id查询",
                },
                {
                    value:
                        "3、【短信】监听消息消息推送",
                },
                {
                    value:
                        "4、【5G】删除上行你好自动下发系统模板",
                },
                {
                    value:
                        "5、【阅信】新增推送功能",
                },
                {
                    value:
                        "6、【阅信】修复模板审核预览",
                },
            ],
    },
{
    id: '8',
        time: "2023/3/16更新内容",
            content: [
                {
                    value:
                        "1、【短信】上行报文新增extend",
                },
                {
                    value:
                        "2、【短信】运营端：通道改写支持省份查询",
                },
                {
                    value:
                        "3、【短信】运营端：视频短信模板管理新增通道审核状态查询",
                },
                {
                    value:
                        "4、【短信】运营端：风险账号管理",
                },
                {
                    value:
                        "5、【短信】优化已审核计费数",
                },
            ],
    },
{
    id: '7',
        time: "2023/3/9更新内容",
            content: [
                {
                    value:
                        "1、【短信】web发送任务查询条件",
                },
                {
                    value:
                        "2、【短信】测试短信内容自动下发",
                },
                {
                    value:
                        "3、【短信】运营端：支持签名统计设置",
                },
                {
                    value:
                        "4、【视频短信】视频短信审核提醒",
                },
                {
                    value:
                        "5、【短信明细】修复短信明细空格显示问题",
                },
            ],
    },
{
    id: '6',
        time: "2023/3/2更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端：优化条件审核慢的问题、记录审核数",
                },
                {
                    value:
                        "2、【短信】运营端：黑名单公司 新开账号提醒",
                },
                {
                    value:
                        "3、【短信】支持多扩展，按周动态获取系统扩展",
                },
                {
                    value:
                        "4、【短信】回复明细支持通道号、省份查询",
                },
                {
                    value:
                        "5、【短信】状态补推记录总数",
                },
                {
                    value:
                        "6、【5G】运营端新增一键上架、一键上线、一键停用功能",
                },
            ],
    },
{
    id: '5',
        time: "2023/2/23更新内容",
            content: [
                {
                    value:
                        "1、【短信】运营端：按照账号设置是否返回通道错误代码和中文解释",
                },
                {
                    value:
                        "2、【短信】运营端：新增是否返回通道错误代码设置",
                },
                {
                    value:
                        "3、【短信】运营端：回复页面展示来源通道、省份地区",
                },
                {
                    value:
                        "4、【商城】支付宝证书更新",
                },
            ],
    },
{
    id: '4',
        time: "2023/2/7更新内容",
            content: [
                {
                    value:
                        "1、【短信配置】运营端：产品设置增加开启机型匹配：是 否",
                },
                {
                    value:
                        "2、【短信模板】客户端：短信模板导出功能",
                },
            ],
    },
{
    id: '3',
        time: "2023/2/1更新内容",
            content: [
                {
                    value:
                        "1、【5G跳转配置】运营端：5g跳转配置新增排除省份。",
                },
                {
                    value:
                        "2、【通道配置】运营端：优化短信通道配置回执处理方式默认redis。",
                },
                {
                    value:
                        "3、【会员管理】：修复会员管理生日bug。",
                },
            ],
    },
{
    id: '2',
        time: "2023/1/5更新内容",
            content: [
                {
                    value:
                        "1、【明细导出】客户端：导出调整为明文号码导出，下载文件需短信验证码验证。",
                },
                {
                    value:
                        "2、【视频短信】运营端：视频短信审核预览，模板变量高亮展示。",
                },
                {
                    value:
                        "3、【短信】号码运营商归属地信息更新。",
                },
                {
                    value:
                        "4、【通道配置】运营端：短信通道配置新增回执处理方式：1.redis，2.rabbitMQ。",
                },
            ],
    },
{
    id: '1',
        time: "2022/12/15更新内容",
            content: [
                {
                    value:
                        "1、【短信】客户端：web发送任务成功计费数统计：1）开启数据账号页面展示提交计费数/成功计费数。2）当日统计频率5分钟，T+1和T+4会重新修正一次。",
                },
                {
                    value:
                        "2、【短信】客户端：上行回复增加签名搜索，今日发布后的上行才支持搜索。",
                },
                {
                    value:
                        "3、【短信】运营端：模板审核参数高亮展示（红色代表已有定义变量，黄色代表未定义变量）。黄色可能会出现变量不被替换，审核人员需注意。",
                },
                {
                    value:
                        "4、【短信】运营端：增加签名扩展设置：是否自动添加签名序号。可针对同行或者一个签名一个扩展的客户进行放开。",
                },
                {
                    value:
                        "5、【短信】短信发送：签名首次下发时，系统会自动分配序号。规则：账号扩展+签名序号+用户自带扩展。",
                },
                {
                    value:
                        "6、【签名序号】规则：第1-79个签名（01-79），第80到第1080个签名（8000-8999)，第1081到第999999个签名（900000-999999）",
                },
                {
                    value: "7、【签名序号】资源系统：可查询签名对应序号。",
                },
                {
                    value:
                        "8、【客户端】修复创建模板界面复制内容行换导致识别变量问题",
                },
                {
                    value: "9、【客户端】修复短信发送时文件上传和号码发送冲突问题",
                },
            ],
    },
]
