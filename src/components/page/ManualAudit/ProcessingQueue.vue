<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
            label-width="100px"
          >
            <el-form-item label="用户名称" prop="clientName">
              <el-input
                v-model="formInline.clientName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="发送ID" prop="taskSmsId">
              <el-input
                v-model="formInline.taskSmsId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="消息ID" prop="msgid">
              <el-input
                v-model="formInline.msgid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="短信内容" prop="content">
              <el-input
                v-model="formInline.content"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="来源" prop="source">
              <el-input v-model="formInline.source" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送类型" prop="sendType">
              <el-select
                v-model="formInline.sendType"
                clearable
                placeholder="请选择类型"
                class="input-w"
              >
                <el-option label="全部" value="0"></el-option>
                <el-option label="模板发送" value="1"></el-option>
                <el-option label="自定义发送" value="2"></el-option>
                <el-option label="个性化发送" value="3"></el-option>
                <el-option label="自定义变量发送" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发送状态" prop="status">
              <el-select
                v-model="formInline.status"
                clearable
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="待处理" value="0"></el-option>
                <el-option label="处理中" value="1"></el-option>
                <el-option label="已完成" value="2"></el-option>
                <el-option label="取消发送" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label-width="100px"
              label="自动审核状态"
              prop="matchSimilar"
            >
              <el-select
                v-model="formInline.matchSimilar"
                clearable
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="已审核" value="1"></el-option>
                <el-option label="未审核" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发送时间类型" prop="isTiming">
              <el-select
                v-model="formInline.isTiming"
                clearable
                placeholder="请选择类型"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="定时发送" value="1"></el-option>
                <el-option label="立即发送" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发送时间" prop="time">
              <el-date-picker
                v-model="formInline.time"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                :clearable="true"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click.prevent="Query()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
              <!-- <el-date-picker class="input-w" v-model="formInline.time1" value-format="YYYY-MM-DD" type="daterange"
                    range-separator="-" :clearable='false' @change="getTimeOperating1" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                  </el-date-picker> -->
            </div>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
          
            </div> -->
        <div class="Mail-table" style="padding-bottom: 40px;">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handelSelection"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
              <el-button
                type="danger"
                style="margin-right: 10px"
                @click="batchDeletion"
                v-if="selectId.length"
                >批量取消</el-button
              >
              <el-button
                type="primary"
                v-if="selectId.length != 0"
                @click="handeExtract()"
                >提取模板</el-button
              >
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="ProcessingQueue"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handelSelection"
            @checkbox-change="handelSelection">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column width="90" field="发送ID" title="发送ID">
              <template v-slot="scope">
                <div>
                  {{ scope.row.taskSmsId }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="消息id" title="消息id" width="200">
              <template v-slot="scope">
                <div>
                  <span>{{ scope.row.msgid }}</span>
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                  <CopyTemp :content="scope.row.msgid" />
                </div>
              </template>
            </vxe-column>
            <vxe-column width="120" field="用户名" title="用户名">
              <template v-slot="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.userName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.userName}}
                                    </span>
                                </el-popover> -->
                <div>{{ scope.row.userName }}</div>
              </template>
            </vxe-column>
            <vxe-column field="任务名称" title="任务名称" min-width="120">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.taskName"
                  :content="scope.row.taskName"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
                <!-- {{ scope.row.taskName }} -->
              </template>
            </vxe-column>
            <vxe-column field="发送类型" title="发送类型" width="120">
              <template v-slot="scope">
                <div>
                  <span v-if="scope.row.sendType == 2">自定义发送</span>
                  <span v-else-if="scope.row.sendType == 1">模版发送</span>
                  <span v-else-if="scope.row.sendType == 3">个性化发送</span>
                  <span v-else-if="scope.row.sendType == 4">自定义变量发送</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送内容" title="发送内容" min-width="500">
              <template v-slot="scope">
                <div>{{ scope.row.content }}</div>
              </template>
            </vxe-column>
            <vxe-column field="文件名/手机号" title="文件名/手机号" width="150">
              <template v-slot="scope">
                <div
                  v-if="scope.row.filePath && roleId == 3"
                  style="cursor: pointer; color: #16a589"
                  @click="download(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.fileMobile"
                    :content="scope.row.fileMobile"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- {{ scope.row.fileMobile }} -->
                </div>
                <div v-else :title="scope.row.fileMobile">
                  <Tooltip
                    v-if="scope.row.fileMobile"
                    :content="scope.row.fileMobile"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- {{ scope.row.fileMobile }} -->
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送时间" title="发送时间" width="110">
              <template v-slot="scope">
                <p v-if="scope.row.sendTime">
                  {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.sendTime">
                  {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
                </p>
                <!-- <span>{{ scope.row.sendTime }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="发送时间类型" title="发送时间类型" width="120">
              <template v-slot="scope">
                <div>
                  <span v-if="scope.row.isTiming == 0">立即发送</span>
                  <span v-else>定时发送</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="提交号码数（总行数）" title="提交号码数（总行数）" width="150">
              <template v-slot="scope">
                <div>{{ scope.row.totalNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="有效号码（有效行数）" title="有效号码（有效行数）" width="150">
              <template v-slot="scope">
                <div>{{ scope.row.effectiveNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="无效号码（无效行）" title="无效号码（无效行）" width="140">
              <template v-slot="scope">
                <div>{{ scope.row.invalidNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="发送状态" title="发送状态" width="100">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.status == 1"
                  type="warning"
                  effect="dark"
                >
                  处理中
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 2"
                  type="success"
                  effect="dark"
                >
                  已完成
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 3"
                  type="info"
                  effect="dark"
                >
                  取消发送
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 0"
                  type="info"
                  effect="dark"
                >
                  待处理
                </el-tag>
                <!-- <span v-if="scope.row.status == 1">处理中</span>
                    <span v-else-if="scope.row.status == 2">已完成</span>
                    <span v-else-if="scope.row.status == 3">取消发送</span>
                    <span v-else-if="scope.row.status == 0">待处理</span> -->
                <el-tooltip
                  v-if="scope.row.status == -1"
                  class="item"
                  effect="dark"
                  :content="scope.row.reason"
                  placement="top"
                >
                  <el-tag
                    :disable-transitions="true"
                    type="danger"
                    effect="dark"
                  >
                    处理异常
                  </el-tag>
                  <!-- <span style="color: red" v-if="scope.row.status == -1">处理异常</span> -->
                </el-tooltip>
              </template>
            </vxe-column>
            <vxe-column field="来源" title="来源" width="90">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.source"
                  :content="scope.row.source"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
                <!-- <span>{{ scope.row.source }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="120">
              <template v-slot="scope">
                <!-- <el-button type="text" v-if="scope.row.status!=-1&&scope.row.status!=3&&scope.row.status!=2&&scope.row.isTiming!=0" style="color:#16A589;margin-left:0px;" @click="edit(scope.row)"><i class="el-icon-edit"></i> 编辑</el-button> -->
                <el-button
                  type="text"
                  v-if="scope.row.status == 0"
                  style="color: red; margin-left: 0px"
                  @click="cancel(scope.row)"
                  ><el-icon><CircleCloseFilled /></el-icon> 取消</el-button
                >
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
    <!-- 相似度 -->
    <SimilarTemlpate
      :dialogVisibles="dialogVisible"
      :tableDatas="extractObj.tableData"
      :similarTemplateLists="extractObj.similarTemplateList"
      @handleClose="handleCloses"
    >
    </SimilarTemlpate>
  </div>
</template>

<script>
import moment from 'moment'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
import UserLists from '@/components/publicComponents/userList.vue'
import SimilarTemlpate from '@/components/publicComponents/similarTemlpate.vue' //相似度
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    UserLists,
    SimilarTemlpate,
    Tooltip,
    CopyTemp
  },
  name: 'ProcessingQueue',
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      name: 'ProcessingQueue',
      //复选框值
      selectId: '',
      userFlag: false,
      dialogVisible: false,
      contentFlag: false,
      contentList: [],
      nameover: '',
      // 搜索数据
      formInline: {
        content: '',
        taskSmsId: '',
        msgid: '',
        source: '',
        isTiming: '',
        clientName: '',
        sendType: '0',
        startTime: '',
        stopTime: '',
        matchSimilar: '',
        status: '',
        beginTime: moment().format('YYYY-MM-DD 00:00:00'),
        endTime: moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().format('YYYY-MM-DD 00:00:00'),
          moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        content: '',
        taskSmsId: '',
        msgid: '',
        source: '',
        isTiming: '',
        clientName: '',
        sendType: '0',
        startTime: '',
        stopTime: '',
        matchSimilar: '',
        status: '',
        beginTime: moment().format('YYYY-MM-DD 00:00:00'),
        endTime: moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().format('YYYY-MM-DD 00:00:00'),
          moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      extractObj: {
        loading2: false,
        tableData: [],
        similarTemplateList: [],
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        // tableLabel:[]
        //     {
        //     prop:"taskSmsId",
        //     showName:'发送ID',
        //     width:'60',
        //     fixed:false
        //     },
        //     {
        //     prop:"content",
        //     showName:'发送内容',
        //     fixed:false
        //     },
        //     {
        //     prop:"fileMobile",
        //     showName:'文件名/手机号',
        //     fixed:false
        //     },
        //     {
        //     prop:"sendTime",
        //     showName:'发送时间',
        //     width:'140',
        //     fixed:false
        //     },
        //     {
        //     prop:"isTiming",
        //     showName:'发送类型',
        //     width:'120',
        //     fixed:false,
        //     formatData:function(val){
        //         let type=''
        //         if(val==0){
        //             type='立即发送'
        //         }else{
        //             type='定时发送'
        //         }
        //         return type
        //     }},
        //     {
        //     prop:"totalNum",
        //     showName:'提交号码数(总行数）',
        //     width:'140',
        //     fixed:false
        //     },{
        //     prop:"effectiveNum",
        //     showName:'有效号码（有效行）',
        //     width:'140',
        //     fixed:false
        //     },{
        //     prop:"invalidNum",
        //     showName:'无效号码（无效行）',
        //     width:'140',
        //     fixed:false
        //     },
        //     {
        //     prop:"status",
        //     showName:'发送状态',
        //     fixed:false,
        //     formatData:function(val){
        //         let type=''
        //         if(val==1){
        //             type='发送中'
        //         }else if(val==2){
        //             type='已完成'
        //         }else if(val==-1){
        //             type='异常'
        //         }else if(val==0){
        //             type='待处理'
        //         }
        //         return type
        //     }}
        // ],
        // tableStyle:{
        //     isSelection:true,//是否复选框
        //     isExpand:false,//是否是折叠的
        //     style: {//表格样式,表格宽度
        //         width:"100%"
        //     },
        //     optionWidth:'120',//操作栏宽度
        //     border:true,//是否边框
        //     stripe:false,//是否有条纹
        // },
        // tableOptions:[
        //     {
        //         optionName:'编辑',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'edit',
        //         icon:''
        //     },
        //     {
        //         optionName:'取消',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'cancel',
        //         icon:''
        //     }
        // ],
        //  conditionOption:[
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'0',//关联的表格属性-值
        //         optionName:'编辑',//按钮的显示文字
        //         optionMethod:'edit',//按钮的方法
        //         // icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //     },
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'0',//关联的表格属性-值
        //         optionName:'取消',//按钮的显示文字
        //         optionMethod:'cancel',//按钮的方法
        //         // icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'red',//按钮颜色
        //     }
        // ]
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        'post',
        '确定取消定时发送？',
        window.path.omcs + 'consumertasksms/cancel',
        // window.path.omcs + "v3/web-task/cancel",
        { ids: this.selectId },
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //提取相似度
    handeExtract() {
      if (this.contentFlag) {
        window.api.post(
          window.path.omcs + 'operatingclientsmssimilar/content/percent',
          this.contentList,
          (res) => {
            if (res.code == 200) {
              this.dialogVisible = true
              this.extractObj.tableData = res.data.similarList
              this.extractObj.similarTemplateList = res.data.similarTemplateList
            }
          }
        )
      } else {
        this.$message({
          message: '请选择同一用户名！',
          type: 'warning',
        })
      }
    },
    handleCloses(val) {
      this.dialogVisible = val
    },
    // handleCopy(name,event){
    //   clip(name, event)
    // },
    isAllEqual(array) {
      if (array.length > 0) {
        return !array.some(function (value, index) {
          return value.userName !== array[0].userName
        })
      } else {
        return true
      }
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].taskSmsId)
      }
      this.selectId = selectId //批量操作选中id
      this.contentFlag = this.isAllEqual(val)
      //   this.timingStatus = timingStatus; //批量操作选中项的 状态
      //   this.sendTimess = sendTimess; //批量操作选中的发送时间
      //   const newListLength = new Set(val.map((item) => item.userName)).size;
      //   console.log(newListLength,'newListLength');
      //   const listLength = val.length;
      //   console.log(listLength,'listLength');
      //   if (listLength > newListLength) {
      //     console.log(1);
      //     this.contentFlag = false;
      //   } else {
      //     console.log(2);
      //     this.contentFlag = true;
      //   }
      if (this.contentFlag) {
        this.contentList = val.records.map((item) => {
          //   return item.content;
          return {
            username: item.userName,
            content: item.content,
          }
        })
      }
    },
    // 发送请求方法
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumertasksms/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 编辑
    edit(val) {
      if (Date.parse(val.sendTime) - Date.now() < 1800000) {
        this.$message({
          message: '定时小于现在30分钟无法编辑',
          type: 'warning',
        })
      } else {
        if (val.sendType == 3) {
          this.$router.push({
            path: '/PersonalizedSendEditing?SmSId=' + val.taskSmsId,
          })
        } else {
          this.$router.push({ path: '/SendSMSEdit?SmSId=' + val.taskSmsId })
        }
      }
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        'post',
        '确定取消定时？',
        window.path.omcs + 'consumertasksms/cancel',
        { ids: [val.taskSmsId] },
        (res) => {
          this.getTableDtate()
        }
      )
    },
    // 下载
    download(val) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          'M+': this.getMonth() + 1,
          'd+': this.getDate(),
          'h+': this.getHours(),
          'm+': this.getMinutes(),
          's+': this.getSeconds(),
          'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        }
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + '').substr(4 - RegExp.$1.length)
          )
        for (var i in args) {
          var n = args[i]
          if (new RegExp('(' + i + ')').test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
            )
        }
        return format
      }
      var that = this
      filedownload()
      function filedownload() {
        fetch(
          that.API.cpus +
            'v3/file/download?fileName=' +
            val.fileMobile +
            '&group=group1&path=' +
            val.filePath.slice(7),
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
            // body: JSON.stringify({
            //     batchNo: val.row.batchNo,
            // })
          }
        )
          .then((res) => res.blob())
          .then((data) => {
            let blobUrl = window.URL.createObjectURL(data)
            download(blobUrl)
          })
      }
      function download(blobUrl) {
        var a = document.createElement('a')
        a.style.display = 'none'
        a.download =
          '(' + new Date().format('YYYY-MM-DD HH:mm:ss') + ') ' + val.fileMobile
        a.href = blobUrl
        a.click()
      }
    },
    // 查询
    Query() {
      if(this.tableDataObj.loading2) return; //防止重复请求
      Object.assign(this.formInlines, this.formInline)
      this.getTableDtate()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      //   this.formInline.time = [];
      //   this.formInline.time1 = [];
      this.formInline.beginTime = moment().format('YYYY-MM-DD 00:00:00')
      this.formInline.endTime = moment()
        .subtract(-7, 'days')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline.startTime = ''
      this.formInline.stopTime = ''
      this.formInline.source = ''
      this.formInline.clientName = ''
      this.formInline.isTiming = ''
      this.formInline.sendType = ''
      this.formInline.status = ''
      this.formInline.matchSimilar = ''
      Object.assign(this.formInlines, this.formInline)
      this.getTableDtate()
    },
    // 定时时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0]
        this.formInline.endTime = val[1]
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + ' 00:00:00'
        this.formInline.stopTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.startTime = ''
        this.formInline.stopTime = ''
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.getTableDtate()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.getTableDtate()
    },
    loacl() {
      let obj = JSON.parse(localStorage.getItem('webTask'))
      if (obj) {
        this.formInline.clientName = obj.username
        this.formInline.isTiming = obj.isTiming
        this.formInline.sendType = obj.sendType
        this.formInline.status = obj.status
        this.formInline.matchSimilar = obj.matchSimilar
        this.formInline.beginTime = obj.beginTime
        this.formInline.endTime = obj.endTime
        this.formInline.time = [obj.beginTime, obj.endTime]
        Object.assign(this.formInlines, this.formInline)
      }
    },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.loacl()
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.loacl()
    this.getTableDtate()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  deactivated() {
    if (JSON.parse(localStorage.getItem('webTask'))) {
      localStorage.removeItem('webTask')
    }
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.getTableDtate()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.title {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  margin: 10px 0;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
</style>
