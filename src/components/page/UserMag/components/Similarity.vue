<template>
  <div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="模板名称" label-width="80px" prop="templateName">
            <el-input
              v-model="formInline.templateName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="任务名称" label-width="80px" prop="groupName">
                <el-input
                  v-model="formInline.groupName"
                  placeholder
                  class="input-w"
                ></el-input>
              </el-form-item> -->
          <el-form-item label-width="80px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="boderbottom"></div> -->
      <div class="Signature-search-fun" style="margin: 10px 0">
        <el-button type="primary" @click="addopt">创建模板</el-button>
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!--分页-->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->
          <el-table-column label="用户名称" width="120">
            <template v-slot="scope">
              <span>{{ scope.row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模板名称" width="90">
            <template v-slot="scope">
              <span>{{ scope.row.templateName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模板ID" width="150">
            <template v-slot="scope">
              <span>{{ scope.row.id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="签名" width="120">
            <template v-slot="scope">
              <span>{{ scope.row.signature }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模板内容">
            <template v-slot="scope">
              <span>{{ scope.row.templateContent }}</span>
            </template>
          </el-table-column>
          <el-table-column label="关键词" width="200">
            <template v-slot="scope">
              <span>{{ scope.row.keys }}</span>
            </template>
          </el-table-column>
          <el-table-column label="最低匹配度" width="90">
            <template v-slot="scope">
              <span>{{ scope.row.threshold }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" width="100">
            <template v-slot="scope">
              <span>{{ scope.row.createUser }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="150">
            <template v-slot="scope">
              <span>{{
                moment(scope.row.createTime).format('YYYY-MM-DD hh:mm:ss')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template v-slot="scope">
              <el-button
                type="text"
                @click="detailsRow(scope.$index, scope.row)"
                ><el-icon><el-icon-edit /></el-icon>&nbsp;修改</el-button
              >
              <el-button
                type="text"
                style="margin: 0px 10px; color: red"
                @click="delState(scope.$index, scope.row)"
                ><el-icon><el-icon-delete /></el-icon>&nbsp;删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
            
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增模板 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        style="padding: 0 28px 0 20px"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model="formop.templateName"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="签名" prop="signature">
            <el-input
              v-model="formop.signature"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="模板内容" prop="templateContent">
            <el-input
              type="textarea"
              style="height: 100px"
              v-model="formop.templateContent"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="最低匹配度" prop="threshold">
            <el-input
              v-model="formop.threshold"
              placeholder="取值范围 0<x<=1.0"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="关键词" prop="">
            <div v-for="(item, index) in keywords" :key="index">
              <el-input
                style="width: 200px; margin: 5px 0"
                v-model="item.value"
                autocomplete="off"
              ></el-input>
              <el-icon style="font-size: 16px; margin: 0 10px; cursor: pointer"
                ><el-icon-delete
              /></el-icon>
            </div>
            <el-button v-if="keywords.length < 3" @click="handelAddkeyword"
              >添加</el-button
            >
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import TableTem from "../../../../publicComponents/TableTem";
// import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import moment from 'moment'
import { List } from 'echarts'
export default {
  name: 'Similarity',
  data() {
    var threshold = (rule, value, callback) => {
      if (value != '') {
        // console.log(value.length, "ll");
        let reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!reg.test(value)) {
          callback(new Error('最多只保留两位小数'))
        } else {
          callback()
        }
      } else {
        callback(new Error('最低匹配的度不能为空'))
      }
    }
    return {
      username: '',
      isFirstEnter: false,
      titleMap: {
        add: '创建模板',
        edit: '修改模板',
      },
      id: '',
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        username: '',
        userId: '',
        templateName: '',
        templateContent: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        userId: '',
        templateName: '',
        templateContent: '',
        currentPage: 1,
        pageSize: 10,
      },
      //添加列表
      formop: {
        templateName: '',
        signature: '',
        templateContent: '',
        keys: '',
        threshold: '',
        username: '',
      },
      rules: {
        templateName: [
          {
            required: true,
            message: '模板名称不能为空',
            trigger: 'change',
          },
        ],
        signature: [
          {
            required: true,
            message: '签名不能为空',
            trigger: 'change',
          },
        ],
        templateContent: [
          {
            required: true,
            message: '模板内容不能为空',
            trigger: 'change',
          },
        ],
        threshold: [
          {
            required: true,
            validator: threshold,
            trigger: 'change',
          },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
      keywords: [],
    }
  },
  methods: {
    /**---------（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs + 'operatinguser/' + this.formInline.userId,
        {},
        (res) => {
          this.formInline.username = res.data.consumerName
          Object.assign(this.tabelAlllist, this.formInline)
          this.gettableLIst()
        }
      )
    },
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.similarity + 'similarity/template/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(val) {
      //   console.log(val);
      //   let selectId = [];
      //   // let wordsCount = [];
      //   for (let i = 0; i < val.length; i++) {
      //     selectId.push(val[i].id);
      //     // wordsCount.push(val[i].wordsCount);
      //   }
      //   this.selectId = selectId.join(","); //批量操作选中id
      //   // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.formop.username = this.formInline.username
          this.formop.userId = this.formInline.userId
          let list = this.keywords.map((item) => {
            return item.value
          })
          this.formop.keys = list.join('|')
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.similarity + 'similarity/template',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.similarity + 'similarity/template',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'success',
                  // })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'warning',
                  // })
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // settingState(index, row) {
    //   console.log(row);
    //   this.$router.push({
    //     path: "/webtaskpool",
    //     query: {
    //       groupId: row.id,
    //       userName: row.userName,
    //     },
    //   });
    // },
    addopt() {
      this.dialogFormVisible = true
      this.flagPut = false
      this.dialogStatus = 'add'
    },

    detailsRow(index, val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      if (val.keys != '') {
        let arr = val.keys.split('|')
        this.keywords = arr.map((item) => {
          return {
            value: item,
          }
        })
      }
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })
    },
    //操作状态功能（删除）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除此模板？',
        window.path.similarity + 'similarity/template/' + val.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
      //   this.$confirms.confirmation(
      //     "delete",
      //     "确定删除此任务？",
      //     window.path.similarity + "consumerwebtaskgroup",
      //     {
      //         id:val.id
      //         // groupName:val.groupName,
      //         // updateName:val.updateName
      //     },
      //     (res) => {
      //       if (res.code == 200) {
      //         this.$message({
      //           message: res.msg,
      //           type: "success",
      //         });
      //         this.gettableLIst();
      //       } else {
      //         this.$message({
      //           message: res.msg,
      //           type: "warning",
      //         });
      //       }

      //     }
      //   );
    },
    //添加关键词
    handelAddkeyword() {
      let obj = {
        value: '',
      }
      if (this.keywords.length < 3) {
        this.keywords.push(obj)
      } else {
        this.$message({
          message: '最多可添加3个关键词',
          type: 'warning',
        })
      }
    },
    handelDelKeyword(index) {
      this.keywords.splice(index, 1)
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        let { id, username } = this.$route.query
        this.formInline.userId = id
        Object.assign(this.tabelAlllist, this.formInline)
        this.getBasicData()
        // this.gettableLIst();
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    let { id, username } = this.$route.query
    this.formInline.userId = id
    Object.assign(this.tabelAlllist, this.formInline)
    this.$nextTick(() => {
      this.getBasicData()
      //   this.gettableLIst();
    })
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.formop.idx = ''
        this.formop.name = ''
        this.formop.keys = ''
        this.keywords = []
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
</style>
