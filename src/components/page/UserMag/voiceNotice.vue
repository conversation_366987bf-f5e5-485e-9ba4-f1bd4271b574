<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title" style="display: flex; align-items: center">
        <div
          @click="goBack()"
          style="
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #16a589;
          "
        >
          <el-icon><ArrowLeftBold /></el-icon>
          <span style="display: inline-block; padding-right: 10px"> 返回</span>
        </div>
        |
        <span style="margin-left: 10px;">语音通知配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="通道｜计费设置" name="channnelRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="channnelRef"
            label-width="120px"
          >
            <el-form-item label="通道组名称" prop="voiceProductId">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.voiceProductId"
                filterable
                placeholder="请选择通道名称"
              >
                <el-option
                  v-for="(item, index) in channel"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="付费方式" prop="voicePayMethod">
              <el-radio-group
                v-model="addUserStep2Form.formData.voicePayMethod"
              >
                <el-radio disabled value="1">预付费</el-radio>
                <el-radio disabled value="2" style="margin-left: 10px"
                  >后付费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(channnelRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="状态设置" name="stateRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="stateRef"
            label-width="100px"
          >
            <el-form-item label="消息报告" prop="voiceReportLevel">
              <el-radio-group
                v-model="addUserStep2Form.formData.voiceReportLevel"
                class="input-w"
              >
                <el-radio value="1">不接收报告</el-radio>
                <el-radio value="2" style="margin-left: 10px"
                  >批量推送</el-radio
                >
                <el-radio value="3" style="margin-left: 10px"
                  >主动抓取</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="上行地址"
              prop="voiceRepliesUrl"
              v-if="addUserStep2Form.formData.voiceReportLevel == 2"
            >
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.voiceRepliesUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取回复短信的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="下行地址"
              prop="voiceReportUrl"
              v-if="addUserStep2Form.formData.voiceReportLevel == 2"
            >
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.voiceReportUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取状态报告的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="回复推送数量"
              prop="voiceRepliesPackageSize"
              v-if="addUserStep2Form.formData.voiceReportLevel == 2"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceRepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                v-model="addUserStep2Form.formData.voiceRepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="回复抓取数量"
              prop="voiceRepliesPackageSize"
              v-if="addUserStep2Form.formData.voiceReportLevel == 3"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceRepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                v-model="addUserStep2Form.formData.voiceRepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="状态推送数量"
              prop="voiceStrepliesPackageSize"
              v-if="addUserStep2Form.formData.voiceReportLevel == 2"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceStrepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                v-model="addUserStep2Form.formData.voiceStrepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="状态抓取数量"
              prop="voiceStrepliesPackageSize"
              v-if="addUserStep2Form.formData.voiceReportLevel == 3"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceStrepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                v-model="addUserStep2Form.formData.voiceStrepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(stateRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="审核设置" name="auditRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="auditRef"
            label-width="140px"
          >
            <el-form-item label="计费条数免审设置" prop="voiceIsAudit">
              <el-radio-group v-model="addUserStep2Form.formData.voiceIsAudit">
                <el-radio value="1">是</el-radio>
                <el-radio value="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="免审计费条数"
              prop="voiceAuditNum"
              v-if="addUserStep2Form.formData.voiceIsAudit == 1"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceAuditNum"
                :min="1"
                :max="50000"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="黑词是否审核" prop="voiceIsBlackWords">
              <el-radio-group
                v-model="addUserStep2Form.formData.voiceIsBlackWords"
              >
                <el-radio value="1">是</el-radio>
                <el-radio value="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="是否验证黑号"
              style="width: 360px"
              prop="voiceIsUserBlack"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.voiceIsUserBlack"
              >
                <el-radio value="1">是</el-radio>
                <el-radio value="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="黑号验证级别"
              prop="voiceUserBlackLevel1"
              v-if="addUserStep2Form.formData.voiceIsUserBlack == 1"
            >
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.voiceUserBlackLevel1"
              >
                <el-checkbox label="一级" value="1" />
                <el-checkbox label="二级" value="2" />
                <el-checkbox label="三级" value="3" />
                <el-checkbox label="四级" value="4" />
                <el-checkbox label="五级" value="5" />
              </el-checkbox-group>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(auditRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="重播设置" name="replay">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="channnelRef"
            label-width="120px"
          >
            <el-form-item label="是否重播" prop="voiceIsReplay">
              <el-radio-group v-model="addUserStep2Form.formData.voiceIsReplay">
                <el-radio value="1">重播</el-radio>
                <el-radio value="2" style="margin-left: 10px">不重播</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="addUserStep2Form.formData.voiceIsReplay == 1"
              label="首次重播方式"
              prop="replayFirstMethod"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.replayFirstMethod"
              >
                <el-radio value="1">即时重播</el-radio>
                <el-radio value="2" style="margin-left: 10px"
                  >延迟重播</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="addUserStep2Form.formData.voiceIsReplay == 1"
              label="重播次数"
              prop="replayMax"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.replayMax"
                :min="0"
                :max="10"
                label="重播次数"
              ></el-input-number>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(channnelRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="防轰炸设置" name="bombRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="bombRef"
            label-width="160px"
          >
            <el-form-item label="号码24h发送限制" prop="voiceOverrunMarket">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceOverrunMarket"
                :min="0"
                :max="999999999"
                label="号码24h发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.voiceOverrunMarket"></el-input> -->
            </el-form-item>
            <el-form-item
              label="号码七天发送限制"
              prop="voiceOverrunTotalMarket"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.voiceOverrunTotalMarket"
                :min="0"
                :max="999999999"
                label="号码七天发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3"
                                v-model="addUserStep2Form.formData.voiceOverrunTotalMarket"></el-input> -->
            </el-form-item>
            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.dailyLimited"
                :min="0"
                :max="999999999"
                label="账号每日提交发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(bombRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="addUserStep2Form"
                    label-width="160px">
                    <div class="float-div-1" style="padding-right:20px;">
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">通道设置</p>
                            <el-form-item label="通道组名称" prop="voiceProductId">
                                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.voiceProductId"
                                    filterable placeholder="请选择通道名称">
                                    <el-option v-for="(item, index) in channel" :label="item.channelGroupName"
                                        :value="item.channelGroupId" :key="index"></el-option>
                                </el-select>
                            </el-form-item>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">计费设置</p>
                            <el-form-item label="付费方式" prop="voicePayMethod">
                                <el-radio-group v-model="addUserStep2Form.formData.voicePayMethod">
                                    <el-radio disabled label="1">预付费</el-radio>
                                    <el-radio disabled label="2" style="margin-left:10px">后付费</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </div>
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">状态设置</p>
                            <el-form-item label="消息报告" prop="voiceReportLevel">
                                <el-radio-group v-model="addUserStep2Form.formData.voiceReportLevel" class="input-w">
                                    <el-radio label="1">不接收报告</el-radio>
                                    <el-radio label="2" style="margin-left:10px">批量推送</el-radio>
                                    <el-radio label="3" style="margin-left:10px">主动抓取</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="上行地址" prop="voiceRepliesUrl"
                                v-if="addUserStep2Form.formData.voiceReportLevel == 2">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.voiceRepliesUrl"></el-input>
                                <p style="width: 345px;"><span style="font-size:12px;">注：获取回复短信的地址</span>(以http/https开头)</p>
                            </el-form-item>
                            <el-form-item label="下行地址" prop="voiceReportUrl"
                                v-if="addUserStep2Form.formData.voiceReportLevel == 2">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.voiceReportUrl"></el-input>
                                <p style="width: 345px;"><span style="font-size:12px;">注：获取状态报告的地址</span>(以http/https开头)</p>
                            </el-form-item>
                            <el-form-item label="回复推送数量" prop="voiceRepliesPackageSize"
                                v-if="addUserStep2Form.formData.voiceReportLevel == 2">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.voiceRepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="回复抓取数量" prop="voiceRepliesPackageSize"
                                v-if="addUserStep2Form.formData.voiceReportLevel == 3">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.voiceRepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="状态推送数量" prop="voiceStrepliesPackageSize"
                                v-if="addUserStep2Form.formData.voiceReportLevel == 2">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.voiceStrepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="状态抓取数量" prop="voiceStrepliesPackageSize"
                                v-if="addUserStep2Form.formData.voiceReportLevel == 3">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.voiceStrepliesPackageSize"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="float-div-2" style="padding-right:20px;">

                        <div>
                            <p style="padding:0 0 18px 0px;font-weight:bold;font-size:14px;">审核设置</p>
                            <el-form-item label="计费条数免审设置" prop='voiceIsAudit'>
                                <el-radio-group v-model="addUserStep2Form.formData.voiceIsAudit">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="免审计费条数" prop="voiceAuditNum" v-if="addUserStep2Form.formData.voiceIsAudit == 1">
                                <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.voiceAuditNum" :min="1"
                                    :max="50000"></el-input-number>
                            </el-form-item>
                            <el-form-item label="黑词是否审核" prop='voiceIsBlackWords'>
                                <el-radio-group v-model="addUserStep2Form.formData.voiceIsBlackWords">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="是否验证黑号" style="width:360px;" prop='voiceIsUserBlack'>
                                <el-radio-group v-model="addUserStep2Form.formData.voiceIsUserBlack">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="黑号验证级别" prop="voiceUserBlackLevel1"
                                v-if="addUserStep2Form.formData.voiceIsUserBlack == 1">
                                <el-checkbox-group style="width:250px;display: flex;flex-shrink: 0;flex-wrap: wrap;"
                                    v-model="addUserStep2Form.formData.voiceUserBlackLevel1">
                                    <el-checkbox style="margin-left:0" label="1">一级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="2">二级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="3">三级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="4">四级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="5">五级</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        </div>

                    </div>
                    <div class="float-div-3">
                        <div>
                            <p style="padding:0 0 18px 0px;font-weight:bold;font-size:14px;">防轰炸设置</p>
                            <el-form-item label="号码24h发送限制" prop="voiceOverrunMarket">
                                <el-input class="input-w-3" v-model="addUserStep2Form.formData.voiceOverrunMarket"></el-input>
                            </el-form-item>
                            <el-form-item label="号码七天发送限制" prop="voiceOverrunTotalMarket">
                                <el-input class="input-w-3"
                                    v-model="addUserStep2Form.formData.voiceOverrunTotalMarket"></el-input>
                            </el-form-item>
                            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
                                <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div style="text-align: center" class="clear-div">
                        <el-button style="width:120px" @click="goBack()">返回</el-button>
                        <el-button type="primary" style="margin-left: 20px;width:120px"
                            @click="submitForm('addUserStep2Form')">提 交</el-button>
                    </div>
                </el-form> -->
      <el-dialog
        title="语音通知配置"
        v-model="dialogVisible"
        width="30%"
        :before-close="handleClose"
      >
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleFormRef"
          label-width="70px"
          class="demo-ruleForm"
        >
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.remark"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button
              v-if="!subLoding"
              type="primary"
              @click="submitSetting(ruleFormRef)"
              >确 定</el-button
            >
            <el-button v-else type="primary" :loading="subLoding" disabled
              >稍 等</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const channnelRef = ref(null);
const stateRef = ref(null);
const bombRef = ref(null);
const auditRef = ref(null);
const ruleFormRef = ref(null);
var checkBlackWords = (rule, value, callback) => {
  if (value == "") {
    return callback(new Error("加黑关键字不能为空"));
  } else {
    console.log(value.length);
    if (value.length < 256) {
      var reg =
        /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,9}$/gi;
      if (!reg.test(value)) {
        return callback(
          new Error(
            "加黑关键词包括数字、汉字、字母、多个由英文逗号隔开最多10组"
          )
        );
      } else {
        var arr = value.split(",");
        var arr2 = [];
        for (var i = 0; i < arr.length; i++) {
          if (arr2.indexOf(arr[i]) < 0) {
            arr2.push(arr[i]);
          }
        }
        if (arr2.length != arr.length) {
          return callback(new Error("不允许重复"));
        } else {
          callback();
        }
      }
    }
    return callback(new Error("加黑关键字不能多于255个字"));
  }
};
var checkSignature = (rule, value, callback) => {
  if (!/^\d{1,12}$/.test(value)) {
    callback(new Error("扩展号由1-12位数字组成"));
  } else {
    window.api.get(
      window.path.omcs + "operatingSignature/checkClientExt",
      { ext: value, userId: addUserStep2Form.formData.userId },
      (res) => {
        if (res.data == true) {
          callback(new Error("此扩展号已存在"));
        } else {
          callback();
        }
      }
    );
  }
};
var sign = (rule, value, callback) => {
  if (value == "") {
    callback();
  } else {
    if (value[0] != "【" || value[value.length - 1] != "】") {
      callback(new Error("请正确填写签名格式【签名】"));
    } else if (value.length > 22) {
      callback(new Error("签名最多20位"));
    } else if (
      /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value) ||
      /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
    ) {
      callback(new Error("不能填写特殊字符"));
    } else {
      callback();
    }
  }
};
var splitRatio = (rule, value, callback) => {
  if (value == "") {
    callback();
  } else {
    if (!/^(?:[1-9]?\d|100)$/.test(value)) {
      callback(new Error("请填写0-100整数"));
    } else {
      callback();
    }
  }
};
const isFirstEnter = ref(false);
const roleId = ref("");
const jsonData = reactive({
  consumerName: "",
  compName: "",
});
const dialogVisible = ref(false);
const subLoding = ref(false);
const secretKeyShow = ref(false);
const secretIvShow = ref(false);
const activeName = ref("channnelRef");
const ruleForm = reactive({
  remark: "",
});
const rules = reactive({
  remark: [{ required: true, message: "请填写备注", trigger: "change" }],
});
const addUserStep2Form = reactive({
  //弹窗步骤2的数据和验证
  formData: {
    type: "1", //语音验证码
    userId: "",
    voicePayMethod: "1",
    voicePrice: "",
    voiceProductId: "",
    // flag: '5',
    ext: "",
    voiceReportLevel: "1",
    voiceRepliesUrl: "",
    voiceReportUrl: "",
    voiceRepliesPackageSize: "",
    voiceStrepliesPackageSize: "",
    // 防轰炸
    voiceOverrunMarket: 0,
    voiceOverrunTotalMarket: 0,
    // voiceSelect:'',
    businessTypes: "1",
    interfacePassword: "******", // 接口密码
    loginPassWord: "******", // 账号密码
    // ifExt: '1', // 生成扩展码：1，手动输入扩展码：2
    custom: "1", //是否显示自定义发送operatinguse
    isIndividualization: "2", //是否个性化设置
    voiceIsAudit: 1, //是否审核
    sign: "",
    voiceAuditNum: 1, //审核号码数
    voiceIsUserBlack: "1", //是否验证黑号
    voiceUserBlackLevel1: ["1", "2", "3"], //黑号验证级别存储
    voiceUserBlackLevel: "", //黑号验证级别存储
    isNumberPortability: "0", //携号转网
    secretKey: "", //秘钥
    secretIv: "", //偏移量
    mobileMask: "1", //掩码展示
    voiceIsBlackWords: "1", //黑词是否审核
    reportTarget: "1", //回执推送账户
    splitDb: "1",
    balanceTarget: "1", //扣费账户
    signSetting: "1", //短信签名位置
    reportPushSetting: "0", //回执推送
    replyPushSetting: "0", //回复推送
    reportCrawlSetting: "0", //回执抓取
    replyCrawlSetting: "0", //回复抓取
    splitRatio: "",
    provinceRatio: "",
    dailyLimited: null, //每日限量
    voicePayMethod: "",
    voiceIsReplay: "2", //是否重播
    replayFirstMethod: "1", //重播首条方式
    replayMax: 5, //重播最大条数
    remark: "", //备注
  },
  formRule: {
    sign: [{ required: false, validator: sign, trigger: "change" }],
    voicePayMethod: [
      { required: true, message: "请选择付费方式", trigger: "change" },
    ],
    voicePrice: [
      { required: false, message: "请输入用户单价", trigger: "change" },
      {
        pattern:
          /^(([1-9][0-9]{0,1})|(([0]\.\d{1,5}|[1-9][0-9]{0,1}\.\d{1,5})))$/gi,
        message: "用户单价为2位整数或5位以内小数",
      },
    ],
    reportTarget: [
      { required: true, message: "回执推送账户必填！", trigger: "blur" },
    ],
    balanceTarget: [
      { required: true, message: "扣费账户必填！", trigger: "blur" },
    ],
    voiceIsBlackWords: [
      { required: true, message: "请选择黑词是否审核", trigger: "blur" },
    ],
    voiceIsUserBlack: [
      {
        required: true,
        message: "请选择是否验证黑号",
        trigger: "change",
      },
    ],
    voiceUserBlackLevel1: [
      {
        required: true,
        message: "请选择黑号验证级别",
        trigger: "change",
      },
    ],
    isIndividualization: [
      {
        required: true,
        message: "请选择是否个性化设置",
        trigger: "blur",
      },
    ],
    ext: [{ required: true, validator: checkSignature, trigger: ["blur"] }],
    voiceRepliesUrl: [
      {
        required: false,
        message: "请输入上行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    voiceReportUrl: [
      {
        required: false,
        message: "请输入下行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    voiceProductId: [
      { required: true, message: "请选择通道", trigger: "change" },
    ],
    voiceIsAudit: [
      { required: true, message: "请选择是否审核", trigger: "blur" },
    ],
    isNumberPortability: [
      { required: true, message: "请选择是否携号转网", trigger: "blur" },
    ],
    secretKey: [{ required: true, message: "请输入秘钥", trigger: "blur" }],
    mobileMask: [
      { required: true, message: "请选择是否掩码展示", trigger: "blur" },
    ],
    voiceAuditNum: [
      { required: true, message: "请输入审核条数", trigger: "change" },
    ],
    signSetting: [
      { required: true, message: "请选择签名位置", trigger: "blur" },
    ],
    reportPushSetting: [
      {
        required: true,
        message: "请选择回执推送方式",
        trigger: "change",
      },
    ],
    replyPushSetting: [
      {
        required: true,
        message: "请选择回复推送方式",
        trigger: "change",
      },
    ],
    reportCrawlSetting: [
      {
        required: true,
        message: "请选择回执抓取方式",
        trigger: "change",
      },
    ],
    replyCrawlSetting: [
      {
        required: true,
        message: "请选择回复抓取方式",
        trigger: "change",
      },
    ],
    splitRatio: [{ required: false, validator: splitRatio, trigger: "change" }],
    provinceRatio: [
      { required: false, trigger: ["change", "blur"] },
      {
        min: 1,
        max: 200,
        message: "长度在 200 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    voiceIsReplay: [
      { required: true, message: "请选择是否重播", trigger: "blur" },
    ],
    replayFirstMethod: [
      { required: true, message: "请选择重播首条方式", trigger: "blur" },
    ],
    replayMax: [
      { required: true, message: "请输入重播最大条数", trigger: "blur" },
    ],
  },
});
const channel = ref([]);

onMounted(() => {
  addUserStep2Form.formData.userId = $router.currentRoute.value.query.id;
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo) {
    roleId.value = userInfo.roleId;
  }
  getBasicData();
  getChannel();
  handleEdit();
});
const getBasicData = () => {
  window.api.get(
    window.path.omcs + "operatinguser/" + addUserStep2Form.formData.userId,
    {},
    (res) => {
      jsonData.consumerName = res.data.consumerName;
      jsonData.compName = res.data.compName;
    }
  );
};
const getChannel = () => {
  window.api.get(
    window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup?productId=7",
    {},
    (res) => {
      if (res.code == 200) {
        channel.value = res.data;
      }
    }
  );
};
const goBack = () => {
  $router.push({
    path: "UserDetail",
    query: {
      id: $router.currentRoute.value.query.id,
    },
  });
};
//赋值
const handleEdit = () => {
  try {
    window.api.get(
      window.path.omcs +
        "operatinguser/userVoiceInfo/notice/" +
        $router.currentRoute.value.query.id,
      {},
      (res) => {
        if (res.code == 200) {
          for (let i in res.data) {
            if (!(res.data[i] == null)) {
              res.data[i] += "";
            }
          }
          res.data["voiceUserBlackLevel1"] =
            res.data.voiceUserBlackLevel.split(",");
          // console.error(res);
          Object.assign(addUserStep2Form.formData, res.data);
          for (let i in addUserStep2Form.formData) {
            if (
              i == "voiceProductId" ||
              i == "voiceRepliesPackageSize" ||
              i == "voiceStrepliesPackageSize" ||
              i == "voiceOverrunMarket" ||
              i == "voiceOverrunTotalMarket" ||
              i == "dailyLimited"
            ) {
              if (addUserStep2Form.formData[i] != null) {
                addUserStep2Form.formData[i] = parseInt(res.data[i]);
              }
            }
          }
          if(res.data.voiceIsReplay){
            addUserStep2Form.formData.voiceIsReplay = res.data.voiceIsReplay
          }else{
            addUserStep2Form.formData.voiceIsReplay = '2'
          }
          
          // console.log(addUserStep2Form.formData);
        }
      }
    );
  } catch (error) {
    console.error(error);
  }
};
const submitForm = (formEl) => {
  console.log(formEl, "formEl");

  formEl.validate((valid, fields) => {
    if (valid) {
      if (addUserStep2Form.formData.voiceReportLevel != 2) {
        //消息报告如果不选批量推送时，重置批量推送下面的推送方式，上下行地址
        addUserStep2Form.formData.voiceRepliesUrl = "";
        addUserStep2Form.formData.voiceReportUrl = "";
      }
      if (addUserStep2Form.formData.voiceReportLevel == 1) {
        addUserStep2Form.formData.voiceRepliesPackageSize = "";
        addUserStep2Form.formData.voiceStrepliesPackageSize = "";
      }
      addUserStep2Form.formData.voiceUserBlackLevel =
        addUserStep2Form.formData.voiceUserBlackLevel1.join(",");
      dialogVisible.value = true;
    } else {
      console.error("error submit!!", fields);
      ElMessage({
        type: "error",
        message: "请检查必填项是否有遗漏！",
      });
      return false;
    }
  });
};
const submitSetting = (formEl) => {
  formEl.validate((valid, fields) => {
    if (valid) {
      subLoding.value = true;
      addUserStep2Form.formData.remark = ruleForm.remark;
      window.api.put(
        window.path.omcs + "operatinguser/userVoiceInfo",
        addUserStep2Form.formData,
        (res) => {
          if (res.code == 200) {
            //返回用户管理页面
            subLoding.value = false;
            dialogVisible.value = false;
            goBack();
          } else {
            subLoding.value = false;
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      console.error("error submit!!", fields);
      return false;
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
};
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      ruleFormRef.value.resetFields();
      subLoding.value = false;
    }
  }
);
</script>
<!-- <script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  name: "voiceNotice",
  data() {
    var checkBlackWords = (rule, value, callback) => {
      if (value == "") {
        return callback(new Error("加黑关键字不能为空"));
      } else {
        console.log(value.length);
        if (value.length < 256) {
          var reg =
            /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,9}$/gi;
          if (!reg.test(value)) {
            return callback(
              new Error(
                "加黑关键词包括数字、汉字、字母、多个由英文逗号隔开最多10组"
              )
            );
          } else {
            var arr = value.split(",");
            var arr2 = [];
            for (var i = 0; i < arr.length; i++) {
              if (arr2.indexOf(arr[i]) < 0) {
                arr2.push(arr[i]);
              }
            }
            if (arr2.length != arr.length) {
              return callback(new Error("不允许重复"));
            } else {
              callback();
            }
          }
        }
        return callback(new Error("加黑关键字不能多于255个字"));
      }
    };
    var checkSignature = (rule, value, callback) => {
      if (!/^\d{1,12}$/.test(value)) {
        callback(new Error("扩展号由1-12位数字组成"));
      } else {
        window.api.get(
          window.path.omcs + "operatingSignature/checkClientExt",
          { ext: value, userId: this.addUserStep2Form.formData.userId },
          (res) => {
            if (res.data == true) {
              callback(new Error("此扩展号已存在"));
            } else {
              callback();
            }
          }
        );
      }
    };
    var sign = (rule, value, callback) => {
      if (value == "") {
        callback();
      } else {
        if (value[0] != "【" || value[value.length - 1] != "】") {
          callback(new Error("请正确填写签名格式【签名】"));
        } else if (value.length > 22) {
          callback(new Error("签名最多20位"));
        } else if (
          /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value) ||
          /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
        ) {
          callback(new Error("不能填写特殊字符"));
        } else {
          callback();
        }
      }
    };
    var splitRatio = (rule, value, callback) => {
      if (value == "") {
        callback();
      } else {
        if (!/^(?:[1-9]?\d|100)$/.test(value)) {
          callback(new Error("请填写0-100整数"));
        } else {
          callback();
        }
      }
    };
    return {
      isFirstEnter: false,
      dialogVisible: false,
      activeName: "channnelRef",
      jsonData: {
        consumerName: "",
        compName: "",
      }, //用户资料
      ruleForm: {
        remark: "",
      },
      rules: {
        remark: [{ required: true, message: "请填写备注", trigger: "change" }],
      },
      secretKeyShow: false, //判断是否加密
      secretIvShow: false,
      addUserStep2Form: {
        //弹窗步骤2的数据和验证
        formData: {
          type: "1", //语音验证码
          userId: "",
          voicePayMethod: "1",
          voicePrice: "",
          voiceProductId: "",
          // flag: '5',
          ext: "",
          voiceReportLevel: "1",
          voiceRepliesUrl: "",
          voiceReportUrl: "",
          voiceRepliesPackageSize: "",
          voiceStrepliesPackageSize: "",
          // 防轰炸
          voiceOverrunMarket: "0",
          voiceOverrunTotalMarket: "0",
          // voiceSelect:'',
          businessTypes: "1",
          interfacePassword: "******", // 接口密码
          loginPassWord: "******", // 账号密码
          // ifExt: '1', // 生成扩展码：1，手动输入扩展码：2
          custom: "1", //是否显示自定义发送operatinguse
          isIndividualization: "2", //是否个性化设置
          voiceIsAudit: "1", //是否审核
          sign: "",
          voiceAuditNum: 1, //审核号码数
          voiceIsUserBlack: "1", //是否验证黑号
          voiceUserBlackLevel1: ["1", "2", "3"], //黑号验证级别存储
          voiceUserBlackLevel: "", //黑号验证级别存储
          isNumberPortability: "0", //携号转网
          secretKey: "", //秘钥
          secretIv: "", //偏移量
          mobileMask: "1", //掩码展示
          voiceIsBlackWords: "1", //黑词是否审核
          reportTarget: "1", //回执推送账户
          splitDb: "1",
          balanceTarget: "1", //扣费账户
          signSetting: "1", //短信签名位置
          reportPushSetting: "0", //回执推送
          replyPushSetting: "0", //回复推送
          reportCrawlSetting: "0", //回执抓取
          replyCrawlSetting: "0", //回复抓取
          splitRatio: "",
          provinceRatio: "",
          dailyLimited: "", //每日限量
          remark: "", //备注
        },
        formRule: {
          sign: [{ required: false, validator: sign, trigger: "change" }],
          voicePayMethod: [
            { required: true, message: "请选择付费方式", trigger: "change" },
          ],
          voicePrice: [
            { required: false, message: "请输入用户单价", trigger: "change" },
            {
              pattern:
                /^(([1-9][0-9]{0,1})|(([0]\.\d{1,5}|[1-9][0-9]{0,1}\.\d{1,5})))$/gi,
              message: "用户单价为2位整数或5位以内小数",
            },
          ],
          reportTarget: [
            { required: true, message: "回执推送账户必填！", trigger: "blur" },
          ],
          balanceTarget: [
            { required: true, message: "扣费账户必填！", trigger: "blur" },
          ],
          voiceIsBlackWords: [
            { required: true, message: "请选择黑词是否审核", trigger: "blur" },
          ],
          voiceIsUserBlack: [
            {
              required: true,
              message: "请选择是否验证黑号",
              trigger: "change",
            },
          ],
          voiceUserBlackLevel1: [
            {
              required: true,
              message: "请选择黑号验证级别",
              trigger: "change",
            },
          ],
          isIndividualization: [
            {
              required: true,
              message: "请选择是否个性化设置",
              trigger: "blur",
            },
          ],
          ext: [
            { required: true, validator: checkSignature, trigger: ["blur"] },
          ],
          voiceRepliesUrl: [
            {
              required: false,
              message: "请输入上行地址",
              trigger: ["change", "blur"],
            },
            {
              min: 1,
              max: 255,
              message: "长度在 255 个字符以内",
              trigger: ["blur", "change"],
            },
          ],
          voiceReportUrl: [
            {
              required: false,
              message: "请输入下行地址",
              trigger: ["change", "blur"],
            },
            {
              min: 1,
              max: 255,
              message: "长度在 255 个字符以内",
              trigger: ["blur", "change"],
            },
          ],
          voiceProductId: [
            { required: true, message: "请选择通道", trigger: "change" },
          ],
          voiceIsAudit: [
            { required: true, message: "请选择是否审核", trigger: "blur" },
          ],
          isNumberPortability: [
            { required: true, message: "请选择是否携号转网", trigger: "blur" },
          ],
          secretKey: [
            { required: true, message: "请输入秘钥", trigger: "blur" },
          ],
          mobileMask: [
            { required: true, message: "请选择是否掩码展示", trigger: "blur" },
          ],
          voiceAuditNum: [
            { required: true, message: "请输入审核条数", trigger: "change" },
          ],
          signSetting: [
            { required: true, message: "请选择签名位置", trigger: "blur" },
          ],
          reportPushSetting: [
            {
              required: true,
              message: "请选择回执推送方式",
              trigger: "change",
            },
          ],
          replyPushSetting: [
            {
              required: true,
              message: "请选择回复推送方式",
              trigger: "change",
            },
          ],
          reportCrawlSetting: [
            {
              required: true,
              message: "请选择回执抓取方式",
              trigger: "change",
            },
          ],
          replyCrawlSetting: [
            {
              required: true,
              message: "请选择回复抓取方式",
              trigger: "change",
            },
          ],
          splitRatio: [
            { required: false, validator: splitRatio, trigger: "change" },
          ],
          provinceRatio: [
            { required: false, trigger: ["change", "blur"] },
            {
              min: 1,
              max: 200,
              message: "长度在 200 个字符以内",
              trigger: ["blur", "change"],
            },
          ],
        },
      },
      channel: [], //通道
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs +
          "operatinguser/" +
          this.addUserStep2Form.formData.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName;
          this.jsonData.compName = res.data.compName;
        }
      );
    },
    /**获取通道列表 */
    getChannel() {
      window.api.get(
        window.path.omcs +
          "v3/operatingchannelgroup/getAllSMSGroup?productId=7",
        {},
        (res) => {
          this.channel = res.data;
        }
      );
    },
    //返回
    goBack() {
      // this.$router.push({ path: 'UserDetail',query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({
        path: "UserDetail",
        query: { id: this.$route.query.id },
      });
    },
    //编辑的赋值
    handleEdit() {
      window.api.get(
        window.path.omcs +
          "operatinguser/userVoiceInfo/notice/" +
          this.$route.query.id,
        {},
        (res) => {
          for (let i in res.data) {
            if (
              !(
                res.data[i] == null ||
                i == "voiceProductId" ||
                i == "voiceUserBlackLevel"
              )
            ) {
              res.data[i] += "";
            }
          }
          res.data["voiceUserBlackLevel1"] =
            res.data.voiceUserBlackLevel.split(",");
          this.addUserStep2Form.formData = res.data;
        }
      );
    },
    handleClose() {
      this.dialogVisible = false;
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          if (this.addUserStep2Form.formData.voiceReportLevel != 2) {
            //消息报告如果不选批量推送时，重置批量推送下面的推送方式，上下行地址
            this.addUserStep2Form.formData.voiceRepliesUrl = "";
            this.addUserStep2Form.formData.voiceReportUrl = "";
          }
          if (this.addUserStep2Form.formData.voiceReportLevel == 1) {
            this.addUserStep2Form.formData.voiceRepliesPackageSize = "";
            this.addUserStep2Form.formData.voiceStrepliesPackageSize = "";
          }
          this.addUserStep2Form.formData.voiceUserBlackLevel =
            this.addUserStep2Form.formData.voiceUserBlackLevel1.join(",");
          this.dialogVisible = true;
        } else {
          return false;
        }
      });
    },
    submitSetting(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addUserStep2Form.formData.remark = this.ruleForm.remark;
          window.api.put(
            window.path.omcs + "operatinguser/userVoiceInfo",
            this.addUserStep2Form.formData,
            (res) => {
              if (res.code == 200) {
                //返回用户管理页面
                this.dialogVisible = false;
                this.goBack();
              } else {
                this.$message.error(res.msg);
              }
            }
          );
          // this.$confirms.confirmation('put','确认修改语音通知配置？',window.path.omcs+'operatinguser/userVoiceInfo',this.addUserStep2Form.formData,res =>{
          //         //返回用户管理页面
          //         this.dialogVisible = false
          //         this.goBack()
          //     });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.addUserStep2Form.formData.userId = this.$route.query.id;
      this.getChannel(); /**获取通道列表 */
      this.handleEdit();
      this.getBasicData();
    });
  },
  // activated() {
  //     if (this.$route.meta.isBack || !this.isFirstEnter) {
  //         this.$nextTick(() => {
  //             this.addUserStep2Form.formData.userId = this.$route.query.id
  //             this.getChannel();  /**获取通道列表 */
  //             this.handleEdit()
  //             this.getBasicData()
  //         });
  //     } else {
  //         this.$route.meta.isBack = false
  //         this.isFirstEnter = false;
  //     }

  // },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs["ruleForm"].resetFields();
      }
    },
  },
};
</script> -->

<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}
.input-w {
  width: 345px !important;
}
.el-radio-group .el-radio {
  margin-right: 0px;
}
.fillet {
  padding: 20px;
}
.input-w-2 {
  width: 200px !important;
}
.input-w-3 {
  width: 200px !important;
}
.input-w-4 {
  width: 190px !important;
}
.input-w-sm {
  width: 126px !important;
}
.input-w-f {
  width: 80px !important;
}
.float-div-1 {
  float: left;
}
.float-div-2 {
  float: left;
}
.float-div-3 {
  float: left;
}
.clear-div {
  clear: both;
}
.red {
  color: red;
}
@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>

<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>
