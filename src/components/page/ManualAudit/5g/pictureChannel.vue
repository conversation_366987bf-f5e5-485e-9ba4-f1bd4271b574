<template>
  <div class="bag" style="background: #fff; padding: 10px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-page-header
            style="color: #16a589"
            @back="goBack"
            :content="username ? `素材报备--${username}` : '素材报备'"
          >
          </el-page-header
        ></el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div style="padding: 0 20px">
      <el-button type="primary" @click="refresh"
        ><el-icon><RefreshRight /></el-icon>刷新</el-button
      >
      <el-button type="primary" @click="addChannel">添加通道</el-button>
    </div>
    <div style="padding: 0 20px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
      > -->
      <vxe-toolbar ref="toolbarRef">
        <template #buttons>
        </template>
      </vxe-toolbar>
      <vxe-table
        ref="tableRef"
        id="pictureChannel"
        border
        stripe
        show-overflow
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true}"
        :cell-config="{height: 100}"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="素材" title="素材">
          <template v-slot="scope">
            <div
              style="margin: 10px 0; width: 100px; height: 70px"
              v-if="fileList.fileType == 1"
            >
              <img
                v-if="fileList.fileUrl"
                :src="fileList.fileUrl"
                alt=""
                style="width: 100%"
              />
            </div>
            <div
              style="margin: 10px 0; width: 150px; height: 70px"
              v-if="fileList.fileType == 2"
            >
              <video
                v-if="fileList.fileUrl"
                style="width: 100%; height: 70px"
                class="avatar video-avatar"
                controls="controls"
                :src="fileList.fileUrl"
              ></video>
            </div>
            <div
              style="margin: 10px 0; width: 150px; height: 70px"
              v-if="fileList.fileType == 3"
            >
              <audio
                v-if="fileList.fileUrl"
                style="width: 100%"
                controls="controls"
                preload="auto"
                :src="fileList.fileUrl"
              ></audio>
            </div>
          </template>
        </vxe-column>

        <vxe-column field="通道名称" title="通道名称">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelName }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="通道编码" title="通道编码">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelId }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="通道运营商" title="通道运营商">
          <template v-slot="scope">
            <div>
              <div
                v-if="scope.row.channelOperator == 1"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yidong"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div
                v-else-if="scope.row.channelOperator == 2"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-liantong"
                  style="font-size: 16px; color: #f56c6c"
                ></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div
                v-else-if="scope.row.channelOperator == 3"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-dianxin"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">电信</span>
              </div>
              <div v-else></div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="状态" title="状态">
          <template v-slot="scope">
            <div>
              <span v-if="scope.row.status == 0">待审核</span>
              <span style="color: #f56c6c" v-else-if="scope.row.status == 10"
                >审核中</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == 20"
                >新增失败</span
              >
              <span style="color: #16a589" v-else-if="scope.row.status == 30"
                >新增成功</span
              >
            </div>
          </template>
        </vxe-column>
        <vxe-column field="原因" title="原因">
          <template v-slot="scope">
            <div>
              {{ scope.row.reason }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间">
          <template v-slot="scope">
            <div>{{
              moment(scope.row.createdTime).format('YYYY-MM-DD HH:mm:ss')
            }}</div>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" width="180">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.status == 0 || scope.row.status == 20"
              @click="declareChannel(scope.row)"
              type="text"
              style="margin-left: 10px"
              >报备</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-dialog
      title="添加通道"
      v-model="dialogVisible"
      width="40%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <!-- <span>这是一段信息</span> -->
      <el-form
        :model="formInline"
        :rules="rules"
        label-width="120px"
        ref="formInline"
        class="demo-form-inline"
      >
        <el-form-item label="通道" prop="channelId">
          <el-select
            class="input-w"
            v-model="formInline.channelId"
            @change="handChannel"
            placeholder="选择通道"
          >
            <el-option
              v-for="item in channel"
              :key="item.channelId"
              :label="item.channelName"
              :value="item.channelId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitChannel('formInline')"
            >提交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'pictureChannel',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      id: '',
      dialogVisible: false,
      isFirstEnter: false,
      formInline: {
        channelId: '',
        channelName: '',
        channelOperator: '',
        materialId: '',
      },
      fileType: '',
      fileUrl: '',
      fileList: {},
      rules: {
        channelId: [
          { required: true, message: '请选择通道', trigger: 'change' },
        ],
      },
      channel: [],
      tableDataObj: {
        tableData: [],
        loading2: false,
      },
      username: '',
      fileName: '',
    }
  },
  created() {
    this.isFirstEnter = true
    this.id = this.$route.query.materialId
    if (this.$route.query.username) {
      this.username = this.$route.query.username
    }
    if (this.$route.query.fileName) {
      this.fileName = this.$route.query.fileName
    }
    this.fileList = JSON.parse(localStorage.getItem('fileList'))
    this.token = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    this.getList()
    this.channel5GList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.id = this.$route.query.materialId
        this.fileList = JSON.parse(localStorage.getItem('fileList'))
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
        this.getList()
        this.channel5GList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    goBack() {
      this.$router.push({
        path: '/pictureAudit',
        query: {
          username: this.$route.query.username,
          fileName: this.$route.query.fileName,
        },
      })
    },
    getList() {
      this.tableDataObj.loading2 = true
      window.api.get(
        window.path.fiveWeb + '/operator/material/channelList/' + this.id,
        {},
        (res) => {
          this.tableDataObj.tableData = res.data
          this.tableDataObj.loading2 = false
        }
      )
    },
    /**获取通道*/
    channel5GList() {
      window.api.get(
        window.path.fiveWeb + '/operator/material/getAvailableChannel/' + this.id,
        {},
        (res) => {
          this.channel = res.data
        }
      )
    },
    handChannel(e) {
      // console.log(e,'e');
      this.channel.forEach((item) => {
        if (e == item.channelId) {
          this.formInline.channelName = item.channelName
          // this.formInline.channelId = item.channelCode;
          this.formInline.channelOperator = item.channelOperator
        }
      })
    },
    addChannel() {
      this.dialogVisible = true
      this.formInline.chatbotId = this.$route.query.chatbotId
      this.channel5GList()
    },
    refresh() {
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
      this.whiteDialogVisible = false
      this.onlinedialog = false
    },
    //添加通道
    submitChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formInline.materialId = this.$route.query.materialId
          window.api.post(
            window.path.fiveWeb + '/operator/material/addChannel',
            this.formInline,
            (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.dialogVisible = false
                this.getList()
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        }
      })
    },
    //报备通道
    declareChannel(row) {
      this.$confirms.confirmation(
        'get',
        '报备通道',
        window.path.fiveWeb + '/operator/material/reapply/' + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.getList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs['formInline'].resetFields()
        this.formInline.channelName = ''
        this.formInline.channelOperator = ''
        this.formInline.channelCode = ''
        this.formInline.whiteList = ''
      }
    },
  },
}
</script>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>