<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        label-width="82px"
        ref="formBlackList"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="formBlackList.username"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="formBlackList.phone"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>

        <el-form-item label="投诉类别" prop="complaintType">
          <el-select
            v-model="formBlackList.complaintType"
            clearable
            placeholder="请选择"
            class="input-w"
          >
            <!-- <el-option label="运营商投诉" value="1"></el-option> -->
            <!-- <el-option label="工信部投诉" value="2"></el-option> -->
            <!-- <el-option label="同行共享投诉" value="3"></el-option> -->
            <el-option label="上行内容加黑" value="2"></el-option>
            <el-option label="用户自主加黑" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建人" prop="createName">
          <el-input
            v-model="formBlackList.createName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            class="input-time"
            v-model="formBlackList.createTime"
            format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="hande"
          >
          </el-date-picker>
        </el-form-item>

        <div>
          <el-button type="primary" plain @click="quenryBlack()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
        </div>
      </el-form>

      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button type="primary" @click="blackListDialog = true">添加黑名单</el-button>
            <el-button type="primary" @click="exportBlacks">导入黑名单</el-button>
            <el-button type="danger" @click="batchDeletion" v-if="selectId.length > 0">批量删除</el-button>
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formBlackListObj.currentPage"
            :page-size="formBlackListObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 弹窗（新增） -->
      <el-dialog
        title="添加用户黑名单"
        v-model="blackListDialog"
        width="520px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="blackList.formData"
          :rules="blackList.formRule"
          ref="blackList"
          label-width="96px"
          style="padding: 0 28px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="blackList.formData.username"></el-input>
          </el-form-item>
          <el-form-item
            label="手机号码"
            prop="phone"
            :rules="
              filter_rules({
                required: true,
                type: 'fiveMobiles',
                message: '手机号码不能为空！',
              })
            "
          >
            <el-input v-model="blackList.formData.phone"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="5"
              resize="none"
              v-model="blackList.formData.remark"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              @click="blackListDialog = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              type="primary"
              @click="blacklist_ok_add('blackList')"
              >确 认</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 导入黑名单 弹窗 -->
      <el-dialog
        title="导入黑名单"
        v-model="SigDialogVisible"
        width="520px"
        :close-on-click-modal="false"
      >
        <el-form
          label-width="90px"
          style="padding-right: 14px"
          :model="blackList3.formData"
          :rules="blackList3.formRule"
          ref="signatureFrom"
        >
          <el-form-item label="用户名" prop="username">
            <el-input v-model="blackList3.formData.username"></el-input>
          </el-form-item>
          <el-form-item label="上传文件">
            <el-upload
              class="upload-demo"
              ref="uploading"
              :action="actionUrl"
              :on-remove="handleRemove"
              multiple
              :limit="1"
              :data="{
                username: blackList3.formData.username,
                remark: blackList3.formData.remark,
              }"
              :headers="header"
              :auto-upload="false"
              :on-success="handleAvatarSuccess"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <span style="color: red; font-size: 12px; margin-left: 10px"
                >只支持.txt格式导入</span
              >
              <!-- <div slot="tip" class="el-upload__tip" style="color:red;">1.只支持Excel格式导入（每行一列，仅第一列有效）</div>
                            <div slot="tip" class="el-upload__tip" style="color:red;">2.单次最多上传100万行！</div> -->
            </el-upload>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="5"
              resize="none"
              v-model="blackList3.formData.remark"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="signature_add('signatureFrom')"
              >确 定</el-button
            >
            <el-button @click="SigDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
export default {
  components: {
    TableTem
  },
  name: 'UserBlackLIst',
  data() {
    return {
      actionUrl: window.path.omcs + '/OperatingClientBlacklist/upload',
      isFirstEnter: false,
      SigDialogVisible: false,
      fileList: [],
      header: '',
      formBlackList: {
        //查询框的值
        username: '',
        phone: '',
        complaintType: '',
        createTime: [],
        createName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      formBlackListObj: {
        //copy查询框的值
        username: '',
        phone: '',
        complaintType: '',
        createTime: [],
        createName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      selectId: [], //批量操作选中id
      rowId: '', //操作行的id
      blackListDialog: false, //弹框显示隐藏
      blackList: {
        formData: {
          phone: '',
          username: '',
          remark: '',
        },
        formRule: {
          // phone:[
          //     { required: true,  validator: fiveMobiles, trigger: 'change' }
          // ],
          username: [
            { required: true, message: '请输入用户名', trigger: 'change' },
          ],
          remark: [
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      blackList3: {
        formData: {
          username: '',
          remark: '',
        },
        formRule: {
          username: [
            { required: true, message: '请输入用户名', trigger: 'change' },
          ],
          remark: [
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        custom: true,
        id: "UserBlackLIst",
        tableData: [],
        title: '',
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        tableLabel: [
          {
            prop: 'username',
            showName: '用户名',
          },
          {
            prop: 'phone',
            showName: '手机号码',
          },
          {
            prop: 'complaintType',
            showName: '投诉类别',
            formatData: function (val) {
              // if(val == 1){
              //     return '运营商投诉'
              // }else if(val == 2){
              //     return '工信部投诉'
              // }else if(val == 3){
              //     return '同行共享投诉'
              // }else
              if (val == 1) {
                return '用户自主加黑'
              } else {
                return '上行内容加黑'
              }
            },
          },

          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '160px',
          },
          {
            prop: 'remark',
            showName: '备注',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '100', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'delet',
            icon: 'CircleCloseFilled',
            color: '#F56C6C',
          },
        ],
      },
    }
  },
  methods: {
    //-------------   列表信息   --------------
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true //打开loading
      window.api.post(
        window.path.omcs + 'OperatingClientBlacklist/page',
        this.formBlackListObj,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.totalRow = res.total
          this.tableDataObj.loading2 = false //关闭loading
        }
      )
    },
    //查询
    quenryBlack() {
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande: function (val) {
      if (val) {
        this.formBlackList.beginTime = this.moment(val[0]).format('YYYY-MM-DD')
        this.formBlackList.endTime = this.moment(val[1]).format('YYYY-MM-DD')
      } else {
        this.formBlackList.beginTime = ''
        this.formBlackList.endTime = ''
      }
    },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.formBlackListObj.pageSize = size
      this.getTableDtate()
    },
    //导入
    exportBlacks() {
      this.SigDialogVisible = true
    },
    //导出
    exportSensit() {
      let aa = Object.assign({}, this.formBlackList)
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else if (aa.username == '') {
        this.$message({
          message: '请输入用户名！',
          type: 'warning',
        })
      } else {
        window.api.post(
          window.path.omcs + 'OperatingClientBlacklist/validateExport',
          aa,
          (res) => {
            if (res.code == 200) {
              this.$File.export(
                window.path.omcs +
                  'OperatingClientBlacklist/exportClientBlacklist',
                aa,
                `用户黑名单报表.zip`
              )
            } else {
              this.$message({
                message: res.msg,
                type: 'warning',
              })
            }
          }
        )
      }
    },
    //改变上传文件
    handleChange(file, fileList) {
      let endingCode = file.name //结尾字符
      this.endingName = endingCode.slice(
        endingCode.lastIndexOf('.') + 1,
        endingCode.length
      )
      let isStyle = false //文件格式
      let fileStyle = ['txt']
      for (let i = 0; i < fileStyle.length; i++) {
        if (this.endingName === fileStyle[i]) {
          isStyle = true
          break
        }
      }
      if (isStyle == true) {
        this.fileList = fileList
      } else {
        this.fileList = []
        this.$message({
          message: '只能上传.txt 结尾的文件！',
          type: 'warning',
        })
      }
    },
    //移除上传文件
    handleRemove(file, fileList) {
      this.fileList = []
    },
    //上传成功
    handleAvatarSuccess(res, file) {
      console.log(res)
      this.SigDialogVisible = false
      if (res.code == 200) {
        this.$message({
          message: '上传成功！！',
          type: 'success',
        })
        setTimeout(() => {
          this.getTableDtate()
        }, 1000)
      } else {
        this.$message.error('上传失败')
      }
    },
    //上传文件过多提示
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      )
    },
    //确定上传
    signature_add(formName) {
      this.$refs[formName].validate((valid) => {
        //表单数据验证
        if (valid) {
          window.api.get(
            window.path.upms +
              'online/existUser/' +
              this.blackList3.formData.username,
            {},
            (res) => {
              if (res.data == 1) {
                if (this.fileList.length != 0) {
                  this.$refs.uploading.submit()
                } else {
                  this.$message({
                    message: '选择上传文件！',
                    type: 'warning',
                  })
                }
              } else {
                this.$message({
                  message: '用户名不存在，禁止添加！',
                  type: 'warning',
                })
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.formBlackListObj.currentPage = currentPage
      this.getTableDtate()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.formBlackList.beginTime = ''
      this.formBlackList.endTime = ''
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].clientBlacklistId)
      }
      this.selectId = selectId
      //    this.selectId = selectId.join(',');
    },
    //-------------   列表信息   --------------

    //-------------   新增功能开始   --------------
    blacklist_ok_add(formName) {
      //提交
      this.$refs[formName].validate((valid) => {
        //表单数据验证
        if (valid) {
          window.api.get(
            window.path.upms +
              'online/existUser/' +
              this.blackList.formData.username,
            {},
            (res) => {
              if (res.data == 1) {
                this.$confirms.confirmation(
                  'post',
                  '确定新增?',
                  window.path.omcs + 'OperatingClientBlacklist/add',
                  this.blackList.formData,
                  (res) => {
                    this.getTableDtate()
                    this.blackListDialog = false //隐藏弹窗
                  }
                )
              } else {
                this.$message({
                  message: '用户名不存在，禁止添加！',
                  type: 'warning',
                })
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //-------------  新增功能结束  --------------
    handelOptionButton: function (val) {
      //操作按钮
      this.rowId = val.row.clientBlacklistId //获得所选行的id
      if (val.methods == 'delet') {
        //删除
        this.$confirms.confirmation(
          'get',
          '此操作将永久删除该条数据, 是否继续?',
          window.path.omcs + 'OperatingClientBlacklist/deleteById/' + this.rowId,
          {},
          (res) => {
            this.getTableDtate()
          }
        )
      }
    },
    // deletecd(){ //条件删除
    //     if (this.formBlackList.username == '') {
    //         this.$message({
    //             message: '请输入要删除的用户名！',
    //             type: 'warning'
    //         });
    //     } else {
    //         this.$confirms.confirmation('post','此操作将永久删除该数据, 是否继续?',window.path.omcs+'OperatingClientBlacklist/conditionDelete', this.formBlackList,res =>{
    //             this.getTableDtate();
    //         })
    //     }
    // },
    batchDeletion() {
      //批量删除
      this.$confirms.confirmation(
        'post',
        '此操作将永久删除该数据, 是否继续?',
        window.path.omcs + 'OperatingClientBlacklist/batchDelete',
        { ids: this.selectId },
        (res) => {
          this.getTableDtate()
        }
      )
    },
  },
  mounted() {
    this.header = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  watch: {
    //监听查询框对象的变化
    // formBlackListObj:{
    //     handler(){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    blackListDialog(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.blackList.resetFields() //清空表单
      }
    },
    //监听导入弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.$refs.signatureFrom.resetFields() //清空表单
        this.fileList = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
