<template>
  <div class="container_left">
    <div class="Top_title" style="display: flex; align-items: center;">
      <span
        style="
          display: flex;
          align-items: center;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><el-icon><ArrowLeft /></el-icon> 返回</span
      >|
      <span style="margin-left: 10px;">用户列表</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          ref="formInline"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item label="用户名称" label-width="80px" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称" label-width="80px" prop="compName">
            <el-input
              v-model="formInline.compName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道类型" label-width="80px" prop="compName">
            <el-select class="input-w" v-model="formInline.type" placeholder="请选择">
              <el-option label="行业通道" value="1"> </el-option>
              <el-option label="营销通道" value="2"> </el-option>
              <el-option label="验证码通道" value="3"> </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload('formInline')"
          >重置</el-button
        >
      </div>
      <div>
        <el-form
          v-model="formop"
          ref="formop"
          style="color: #16a589 !important; font-style: italic"
        >
          <el-form-item label="当前选择通道组:">
            <!-- <el-input v-model="formop.passNum" autocomplete="off" ></el-input> -->
            <span>{{ this.group.channelGroupName }}</span>
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button
              type="primary"
              plain
              v-if="selectId.length > 0"
              @click="updatePasss"
              >批量切换通道组</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!--  -->
      <el-dialog
        title="通道组切换"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="110px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="当前通道组编号" prop="passNum">
            <!-- <el-input v-model="formop.passNum" autocomplete="off" ></el-input> -->
            <span>{{ this.formop.channelGroupId }}</span>
          </el-form-item>
          <el-form-item label="当前通道组名称" prop="passName">
            <!-- <el-input v-model="formop.passName" autocomplete="off"></el-input> -->
            <span>{{ this.formop.channelGroupName }}</span>
          </el-form-item>
          <el-form-item label="通道类型" prop="pass">
            <!-- <el-radio-group v-model="formop.pass">
                    <el-radio label="1">手动通道组</el-radio>
                </el-radio-group> -->
            <span v-if="formop.type == '1'">行业通道组</span>
            <span v-else-if="formop.type == '2'">营销通道组</span>
            <span v-else-if="formop.type == '3'">验证码通道组</span>
          </el-form-item>
          <el-form-item label="通道组选择" prop="productId">
            <el-select
              v-model="formop.productId"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in labelpassName"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增标签商结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    TableTem
  },
  name: 'userList',
  data() {
    return {
      labelpassName: [], //通道组列表
      channelGroupId: '', //通道组id
      group: {},
      selectId: '', //批量选中 id
      userId: '', //用户id
      dialogFormVisible: false, //弹出框显示隐藏
      active: 0,
      isShow: true,
      rules: {
        productId: [
          { required: true, message: '请选择通道组', trigger: 'blur' },
        ],
      },

      formInline: {
        userName: '',
        compName: '',
        type: '1',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        userName: '',
        compName: '',
        type: '1',
        currentPage: 1,
        pageSize: 10,
      },
      formop: {
        channelGroupId: '',
        channelGroupName: '',
        type: '',
        pass: '1',
        productId: '',
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        id: "OperationMagUserList",
        total: 0,
        tableData: [],
        tableLabel: [
          {
            prop: 'userId',
            showName: '用户ID',
          },
          {
            prop: 'consumerName',
            showName: '用户名',
          },
          // {
          // prop:"compName",
          // showName:'公司名称',
          // },
          {
            prop: 'consumerStatus',
            showName: '用户状态',
            formatData: function (val) {
              if (val == 0) {
                return (val = '启用')
              }
              if (val == 1) {
                return (val = "<span style='color:red;'>停用</span>")
              }
            },
            showColorTag: {
              color: '#3BB19C',
            },
          },
          {
            prop: 'updateName',
            showName: '操作人',
          },
          {
            prop: 'updateTime',
            showName: '操作时间',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '240', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },

        tableOptions: [
          {
            optionName: '切换通道组',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'Refresh',
          },
        ],
      },
    }
  },
  methods: {
    //列表信息-----------------------------
    getRouterData() {
      this.channelGroupId = this.$route.query.channelGroupId
      this.tableDataObj.loading2 = true
      this.tabelAlllist.productId = this.channelGroupId
      window.api.post(
        window.path.omcs + 'v3/operatingchannelgroup/clients/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.page.total
          this.tableDataObj.tableData = res.page.records
          this.group = res.group
        }
      )
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
    },
    getpass() {
      window.api.get(
        window.path.omcs + 'v3/operatingchannelgroup/list',
        {},
        (res) => {
          this.labelpassName = res
        }
      )
    },
    //-----------------------------------列表操作------------
    goBack() {
      //返回上一层
      this.$router.go(-1)
    },
    //  查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.getRouterData()
    },
    // 重置
    Reload() {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
    },
    //批量转换通道
    updatePasss() {
      this.dialogFormVisible = true
      Object.assign(this.formop, this.group)
      this.formop.type = this.formInline.type
    },
    //转换通道
    updatePass(val) {
      this.userId = val.row.userId
      if (val) {
        this.dialogFormVisible = true
        // console.log(this.group,'ll');
        Object.assign(this.formop, this.group)
        this.formop.type = this.formInline.type
      }
    },
    //提交
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let passIds
          console.log(this.userId)
          if (this.selectId.length > 0 && this.userId == '') {
            this.passIds = this.selectId
          } else {
            this.passIds = this.userId
          }

          this.$confirms.confirmation(
            'post',
            '确认执行此操作吗？',
            window.path.omcs + 'v3/operatingchannelgroup/clients',
            {
              productId: this.formop.productId,
              clientIds: this.passIds,
              type: this.formop.type,
            },
            () => {
              this.dialogFormVisible = false
              this.getRouterData()
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].userId)
      }
      this.selectId = selectId.join(',') //批量操作选中id
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.updatePass(val)
      }
    },
  },
  mounted() {
    this.getpass()
    // console.log(111,'lll');
  },
  activated() {
    // console.log(111);
    // this.addUserStep2Form.formData.userId = this.$route.query.id;
    // this.getChannel(); /**获取通道列表 */
    // this.handleEdit();
    // this.getBasicData();
    this.getRouterData()
  },
  watch: {
    //监听查询框对象的变化
    tabelAlllist: {
      handler() {
        this.getRouterData()
      },
      deep: true, //深度监听
      immediate: true,
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.userId = ''
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Signature-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
