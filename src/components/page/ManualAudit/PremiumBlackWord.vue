<template>
  <div>
    <div class="Top_title">
      <span>特级黑词审核</span>
    </div>
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="82px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" label-width="82px" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="内容" label-width="82px" prop="content">
            <el-input
              v-model="sensitiveCondition.content"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="排除用户"
            label-width="82px"
            prop="excludeUsername"
          >
            <el-input
              v-model="sensitiveCondition.excludeUsername"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道组" label-width="82px" prop="channelGroup">
            <el-select
              v-model="sensitiveCondition.channelGroup"
              filterable
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelGroupName"
                :key="index"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核状态" label-width="82px" prop="auditStatus">
            <el-select
              v-model="sensitiveCondition.auditStatus"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="提交时间" label-width="82px"  prop="time1">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time1"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande1"
                            :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div class="boderbottom" style="padding-left: 10px">
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">黑词审核列表</span> -->
        <!-- <el-button type="primary"  @click="establishSig()">创建签名</el-button> -->
        <el-button
          type="primary"
          @click="handleAdopt1()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量通过</el-button
        >
        <el-button
          type="primary"
          @click="sensitiveBatchSet()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量不通过</el-button
        >
        <el-button
          type="primary"
          @click="batchChannelGroup()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量切换通道</el-button
        >
      </div>
      <div class="sensitive-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          :row-class-name="tableRowClassName"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="46"></el-table-column>
          <!-- <el-table-column label="签名ID" width="80">
                            <template #default="scope">{{ scope.row.signatureId }}</template>
                        </el-table-column> -->
          <el-table-column label="用户名" width="120">
            <template v-slot="scope">
              {{ scope.row.username }}
            </template>
          </el-table-column>
          <el-table-column label="发送内容" min-width="500">
            <template v-slot="scope">
              <div class="spanColor" v-html="scope.row.content"></div>
            </template>
          </el-table-column>
          <el-table-column label="字数" width="60">
            <template v-slot="scope">
              <span>{{ scope.row.contentSize }}</span>
            </template>
          </el-table-column>
          <el-table-column label="号码数" width="60">
            <template v-slot="scope">
              <span>{{ scope.row.mobCount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="签名报备" width="70">
            <template v-slot="scope">
              <span v-if="scope.row.signReport == '1'">是</span>
              <span style="color: #f56c6c" v-if="scope.row.signReport == '0'"
                >否</span
              >
            </template>
          </el-table-column>
          <el-table-column label="敏感词" width="180">
            <template v-slot="scope">
              <span style="color: #f56c6c">{{ scope.row.prompt }}</span>
              <el-tag
:disable-transitions="true"
                size="default"
                style="color: #fff"
                :color="bgColor[index]"
                v-for="(item, index) in scope.row.labelName"
                :key="index"
                >{{ item }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="敏感词标签" width="145">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                size="default"
                style="color: #fff"
                :color="bgColor[index]"
                v-for="(item, index) in scope.row.labelName"
                :key="index"
                >{{ item }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column v-if="KZNum != '1'" label="快照预览" width="70">
            <template v-slot="scope">
              <div
                class="demo-image__preview"
                style="cursor: pointer"
                @click="KZCK(scope.row.auditPic)"
              >
                <el-image
                  :z-index="9999"
                  style="width: 50px; height: 50px"
                  :src="
                    scope.row.auditPic
                      ? API.imgU + scope.row.auditPic.split(',')[0]
                      : ''
                  "
                  :preview-src-list="KFck.srcList"
                  :append-to-body="true"
                  :preview-teleported="true"
                >
                  <template v-slot:error>
                    <div class="image-slot">
                      <el-icon><el-icon-picture-outline /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="提交时间" width="170">
            <template v-slot="scope">
              {{ scope.row.time.split('.')[0] }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="通道组ID" width='80'>
                            <template #default="scope">
                                {{scope.row.channelGroup}}
                            </template>
                        </el-table-column> -->
          <el-table-column label="通道组" width="120">
            <template v-slot="scope">
              <div v-for="(item, index) in channelGroupName" :key="index">
                <span v-if="item.channelGroupId == scope.row.channelGroup">
                  {{ item.channelGroupName }}
                </span>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="审核状态" width="90">
                            <template #default="scope" >
                                <span v-if="scope.row.auditStatus=='1'">待审核</span>
                                <span v-else-if="scope.row.auditStatus=='2'"  style="color:#16A589">审核通过</span>
                                <span v-else  style="color:#f56c6c">审核不通过</span>
                            </template>
                        </el-table-column> -->
          <el-table-column label="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                v-if="scope.row.auditStatus == '1'"
                @click="handleAdopt3(scope.$index, scope.row)"
                ><el-icon><el-icon-success /></el-icon>&nbsp;审核通过</el-button
              >
              <el-button
                link
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.auditStatus == '3' && scope.row.today == '1'"
                @click="handleAdopt2(scope.$index, scope.row, true)"
                ><el-icon><el-icon-error /></el-icon>&nbsp;驳回审核</el-button
              >
              <el-button
                link
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.auditStatus == '2' && scope.row.today == '1'"
                @click="turnDown(scope.$index, scope.row, true)"
                ><el-icon><el-icon-error /></el-icon>&nbsp;驳回审核</el-button
              >
              <el-button
                link
                style="color: orange; margin-left: 0px"
                v-if="scope.row.auditStatus == '1'"
                @click="handleReject(scope.$index, scope.row)"
                ><el-icon><el-icon-error /></el-icon>&nbsp;审核不通过</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[100, 500, 1000]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>


          <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹窗（查看图片） -->
      <el-dialog
        :title="titleS"
        v-model="imagesshow"
        width="600px"
        :close-on-click-modal="false"
      >
        <template>
          <el-carousel
            :interval="5000"
            arrow="always"
            :autoplay="false"
            style="height: 560px"
          >
            <el-carousel-item
              style="height: 560px"
              v-for="(item, index) in imgUrl"
              :key="index"
            >
              <img style="width: 100%; max-height: 580px" :src="item" alt="" />
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-dialog>
      <!-- 弹框 （审核 通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="600px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="100px"
          style="padding: 0 28px"
        >
          <el-form-item label="不通过原因:" prop="labelCheckList">
            <el-checkbox-group v-model="remarkObj.formData.labelCheckList">
              <el-checkbox
                :label="item.id"
                v-for="(item, index) in labelCheckListArry"
                :key="index"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 新增 弹框 -->
      <el-dialog
        title="创建短信签名"
        v-model="SigDialogVisible"
        width="680px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="signatureFrom.formData"
          :rules="signatureFrom.formRule"
          label-width="80px"
          style="padding-right: 14px"
          ref="signatureFrom"
        >
          <el-form-item
            label="用户名称"
            prop="clientName"
            :rules="
              filter_rules({ required: true, message: '用户名称不能为空！' })
            "
          >
            <el-input v-model="signatureFrom.formData.clientName"></el-input>
          </el-form-item>
          <el-form-item
            label="签名内容"
            prop="signature"
            style="position: relative"
          >
            <el-input v-model="signatureFrom.formData.signature"></el-input>
            <span style="position: absolute; top: 2px; left: 0px">【</span>
            <span style="position: absolute; top: 2px; right: 0px">】</span>
          </el-form-item>
          <el-form-item label="签名类型" class="sig-type" prop="signatureType">
            <el-radio-group v-model="signatureFrom.formData.signatureType">
              <el-radio label="1" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">公司名全称或简称：</span
                >须提供营业执照截图</el-radio
              >
              <el-radio label="2" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">APP名全称或简称：</span
                >须提供任一应用商店的下载链接与该应用商店的后台管理截图</el-radio
              >
              <el-radio label="3" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips"
                  >工信部备案的网站名全称或简称：</span
                >提供域名备案服务商的后台备案截图</el-radio
              >
              <el-radio label="4" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips"
                  >公众号或小程序名全称或简称：</span
                >须提供公众号（小程序）微信开放平台截图</el-radio
              >
              <el-radio label="5" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">商标全称或简称：</span
                >须提供商标注册证书截图</el-radio
              >
              <el-radio label="6" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">其他</span></el-radio
              >
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="上传图片" v-if="signatureFrom.formData.signatureType != 6">
                            <file-upload style="display: inline-block; "
                                :action="window.path.omcs + 'signatureaudit/uploadFile'"
                                :limit= 3
                                listType= 'picture'
                                tip='格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M'
                                :fileStyle= fileStyle
                                :del='del1'
                                :showfileList=true
                                @fileup="fileup"
                                @fileupres="fileupres"
                            >选择上传文件</file-upload>
                        </el-form-item> -->
          <el-form-item label="备注内容" prop="remark">
            <el-input
              type="textarea"
              v-model="signatureFrom.formData.remark"
              placeholder="请输入备注内容"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button
              type="primary"
              @click="signature_add('signatureFrom', 'signatureFrom.title')"
              >确 定</el-button
            >
            <el-button @click="SigDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 编辑弹框 -->
      <!-- 切换通道 -->
      <el-dialog
        title="切换通道组"
        v-model="SwitchChannelGroup"
        width="530px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <el-form
          :model="SwitchChannelForm"
          :rules="SwitchChannelRules"
          label-width="100px"
          style="padding: 10px 30px"
          ref="refForm"
        >
          <el-form-item label="扩展号" prop="ext">
            <el-input
              v-model="SwitchChannelForm.ext"
              class="input-w"
            ></el-input>
            <span style="color: #f56c6c; font-size: 12px">不可随意填写</span>
          </el-form-item>
          <el-form-item label="选择通道" prop="aisle">
            <el-select
              v-model="SwitchChannelForm.aisle"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="人工通道组" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道组名称" prop="channelGroupId">
            <el-select
              v-model="SwitchChannelForm.channelGroupId"
              placeholder="请选择"
              filterable
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelGroupName"
                :key="index"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="移动" prop="channelYd">
            <el-select
              v-model="SwitchChannelForm.channelYd"
              placeholder="请选择"
              filterable
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelYd"
                :key="index"
                :label="'移动通道:' + item[1] + ' 单价:' + item[2]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联通" prop="channelLt">
            <el-select
              v-model="SwitchChannelForm.channelLt"
              placeholder="请选择"
              filterable
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelLt"
                :key="index"
                :label="'联通通道:' + item[1] + ' 单价:' + item[2]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="电信" prop="channelDx">
            <el-select
              v-model="SwitchChannelForm.channelDx"
              placeholder="请选择"
              filterable
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelDx"
                :key="index"
                :label="'电信通道:' + item[1] + ' 单价:' + item[2]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="confirmSwitchChannel()"
              >确 定</el-button
            >
            <el-button @click="SwitchChannelGroup = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 长传图片 -->
      <el-dialog
        title="上传图片"
        v-model="uploadImage"
        width="530px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <el-upload
          class="upload-demo"
          :action="actionUrl"
          :headers="token"
          :file-list="fileList1"
          :on-remove="handleRemove1"
          :on-success="handleAvatarSuccess1"
          :before-upload="beforeAvatarUpload1"
          list-type="picture"
        >
          <el-button size="default" type="primary">点击上传</el-button>
          <template v-slot:tip>
            <div class="el-upload__tip">请上传图片</div>
          </template>
        </el-upload>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="uploadReview">确 定</el-button>
            <el-button @click="uploadImage = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    TableTem,
    FileUpload
  },
  name: 'PremiumBlackWord',
  data() {
    var ext = (rule, value, callback) => {
      if (value == '' || value == null) {
        callback()
      } else {
        if (!/^\d{2,15}$/.test(value)) {
          return callback(new Error('请输入2-15位扩展号'))
        } else {
          callback()
        }
      }
    }
    return {
      actionUrl: window.path.cpus + 'v3/file/upload',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime()
          if (maxDate) {
            this.pickerMinDate = ''
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
            return false
          }
          if (this.pickerMinDate !== '') {
            const day30 = 2 * 24 * 3600 * 1000
            let maxTime = this.pickerMinDate
            if (maxTime > Date.now()) {
              maxTime = Date.now()
            }
            const minTime = this.pickerMinDate - day30
            return time.getTime() < minTime || time.getTime() > maxTime
          }
          return time.getTime() > Date.now()
        },
      },
      KZNum: '1',
      KFck: {
        url: '',
        srcList: [window.path.imgU],
      },
      auditTime: '',
      turnStatus: false, //是否驳回操作
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      SigDialogVisible: false, //弹出框创建签名显示隐藏
      remarkshow: false,
      labelCheckListArry: [],
      channelGroupName: [],
      // 切换通道组
      SwitchChannelGroup: false,
      // 切换通道组数据
      SwitchChannelForm: {
        auditStatus: 2,
        ext: '',
        channelGroupId: '',
        channelYd: '',
        channelLt: '',
        channelDx: '',
      },
      // 移动
      channelYd: [],
      // 联通
      channelLt: [],
      // 电信
      channelDx: [],
      // 切换通道组验证
      SwitchChannelRules: {
        ext: [{ required: false, validator: ext, trigger: 'blur' }],
        aisle: [{ required: true, message: '请选择通道', trigger: 'change' }],
        channelGroupId: [
          { required: true, message: '请选择通道组名称', trigger: 'change' },
        ],
        channelYd: [
          { required: true, message: '请选择移动通道', trigger: 'change' },
        ],
        channelLt: [
          { required: true, message: '请选择联通通道', trigger: 'change' },
        ],
        channelDx: [
          { required: true, message: '请选择电信通道', trigger: 'change' },
        ],
      },
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          idArr: [],
          labelCheckList: [],
        },
        formRule: {
          labelCheckList: [
            {
              type: 'array',
              required: true,
              message: '请选择拒绝原因',
              trigger: 'change',
            },
          ],
        },
      },
      idArrs: [], // 驳回的id
      uploadImage: false,
      selectionflag: true, //赋值批量检测号码数
      token: {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      },
      fileList1: [],
      fileImgs: [],
      rowId: '',
      fileauditpic: [],
      title: '',
      signatureFrom: {
        title: '',
        formData: {
          signature: '',
          clientName: '',
          signatureType: '1',
          remark: '',
          imgUrl: '',
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: '该输入项为必填项!', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
          ],
          signatureType: [
            { required: true, message: '请选择签名类型', trigger: 'change' },
          ],
          imgUrl: [
            { required: true, message: '请选择上传图片', trigger: 'change' },
          ],
        },
        signature: '', //签名
        signatureId: '', //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        username: '',
        content: '',
        excludeUsername: '',
        channelGroup: '',
        time1: [],
        auditStatus: '1',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 100,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        username: '',
        content: '',
        excludeUsername: '',
        channelGroup: '',
        time1: [],
        auditStatus: '1',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 100,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
      // 标签颜色
      bgColor: [
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
      ],
    }
  },
  created() {
    // 查询拒绝原因标签
    window.api.get(window.path.omcs + 'servicerefusereason/tag', {}, (res) => {
      this.labelCheckListArry = res.data
    })
    // 查询通道组
    window.api.get(
      window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
      {},
      (res) => {
        this.channelGroupName = res.data
      }
    )
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.sensitiveConObj.blackWordLevel = 0
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'v3/serviceaduitsms/page',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    sensitiveQuery() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.KZNum = this.sensitiveConObj.auditStatus
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      this.sensitiveCondition.auditStartTime = ''
      this.sensitiveCondition.auditEndTime = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.KZNum = '1'
    },
    //快照查看
    KZCK(val) {
      if (val) {
        this.KFck.srcList = []
        let a = val.split(',')
        console.log(val.split(','))
        for (var i = 0; i < a.length; i++) {
          this.KFck.srcList.push(window.path.imgU + a[i])
        }
      }
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.blackWordLevel == 0) {
        return 'warning-rows'
      }
      return ''
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.auditEndTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.auditStartTime = ''
        this.sensitiveCondition.auditEndTime = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
    },
    //删除
    handleEdit(index, row) {
      this.$confirms.confirmation(
        'get',
        '此操作将永久删除该条数据, 是否继续?',
        window.path.omcs + 'signatureaudit/delete/' + row.signatureId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //批量通过
    handleAdopt1() {
      if (!this.selectionflag) {
        this.$message({
          message:
            '选项中包含大于等于100个号码数，如批量通过，请先将其单个审核通过！',
          type: 'warning',
        })
      } else {
        this.rowId = this.remarkObj.formData.idArr
        this.uploadImage = true
        // this.$confirms.confirmation('post','是否确认审核通过？',window.path.omcs+'v3/serviceaduitsms/audit',{
        //     overrule:false,
        //     auditStatus: 2,
        //     ids:this.remarkObj.formData.idArr,
        // },res =>{
        //     this.getTableDtate();
        // });
      }
    },
    //驳回审核
    handleAdopt2(index, row, status) {
      this.$confirms.confirmation(
        'post',
        '是否确认审核通过？',
        window.path.omcs + 'v3/serviceaduitsms/audit',
        {
          overrule: true,
          auditStatus: 2,
          ids: row.id,
        },
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //审核通过todey
    handleAdopt3(index, row, status) {
      // if(row.mobCount>=100){
      this.rowId = row.id
      this.uploadImage = true
      // }else{
      //     this.$confirms.confirmation('post','是否确认审核通过？',window.path.omcs+'v3/serviceaduitsms/audit',{
      //         overrule:false,
      //         auditStatus: 2,
      //         ids:row.id,
      //     },res =>{
      //         this.getTableDtate();
      //     });
      // }
    },
    // handleAdopt(index,row,status){
    //     if(!status&&index&&row){
    //         this.$confirms.confirmation('post','是否确认审核通过？',window.path.omcs+'v3/serviceaduitsms/audit',{
    //             overrule:status||false,
    //             auditStatus: 2,
    //             ids:row.id,
    //         },res =>{
    //             this.getTableDtate();
    //         });
    //     }else{
    //         if(!row){
    //             this.$confirms.confirmation('post','是否确认审核通过？',window.path.omcs+'v3/serviceaduitsms/audit',{
    //                 overrule:status||false,
    //                 auditStatus: 2,
    //                 ids: this.remarkObj.formData.idArr.length==0?row.id:this.remarkObj.formData.idArr,
    //             },res =>{
    //                 this.getTableDtate();
    //             });
    //         }else{
    //             let myDate=new Date()
    //             if(row.auditTime.split(" ")[0]==myDate.getFullYear() + '-'+(myDate.getMonth()+1 < 10 ? '0'+(myDate.getMonth()+1) : myDate.getMonth()+1) + '-' +myDate.getDate()){
    //                 this.$confirms.confirmation('post','是否确认审核通过？',window.path.omcs+'v3/serviceaduitsms/audit',{
    //                     overrule:status||false,
    //                     auditStatus: 2,
    //                     ids: this.remarkObj.formData.idArr.length==0?row.id:this.remarkObj.formData.idArr,
    //                 },res =>{
    //                     this.getTableDtate();
    //                 });
    //             }else{
    //                 this.$message({
    //                     message: '驳回审核只允许操作今日数据',
    //                     type: 'warning'
    //                 });
    //             }
    //         }
    //     }
    // },
    // 审核驳回通过
    turnDown(index, row, status) {
      // if(!row.auditTime){
      //     this.idArrs='';
      //     this.remarkshow = true;
      //     this.title = '驳回审核'
      //     this.idArrs=row.id; //赋值ID
      //     this.turnStatus=status||false
      // }else{
      // let myDate=new Date()
      // if(row.auditTime.split(" ")[0]==myDate.getFullYear() + '-'+(myDate.getMonth()+1 < 10 ? '0'+(myDate.getMonth()+1) : myDate.getMonth()+1) + '-' +myDate.getDate()){
      this.idArrs = ''
      this.remarkObj.formData.idArr = ''
      this.remarkshow = true
      this.title = '驳回审核'
      this.idArrs = row.id //赋值ID
      this.turnStatus = status || false
      // }else{
      //     this.$message({
      //         message: '驳回审核只允许操作今日数据',
      //         type: 'warning'
      //     });
      // }
      // }
    },
    //批量通过
    sensitiveBatchDel() {
      let status = true
      //判断选项中是否有审核过的状态
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != '1') {
          status = false
          break
        }
      }
      if (status) {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.omcs + 'signatureaudit/auditPass',
          {
            auditStatus: 2,
            ids: this.remarkObj.formData.idArr,
          },
          (res) => {
            this.getTableDtate()
            this.remarkObj.formData.idArr = []
          }
        )
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //驳回
    handleReject(index, row) {
      this.idArrs = ''
      this.remarkObj.formData.idArr = ''
      this.remarkshow = true
      this.title = '审核不通过'
      this.idArrs = row.id //赋值ID
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '此操作不可逆，是否继续?',
            window.path.omcs + 'v3/serviceaduitsms/audit',
            {
              overrule: this.turnStatus || false,
              auditStatus: 3,
              ids:
                this.remarkObj.formData.idArr.length == 0
                  ? this.idArrs
                  : this.remarkObj.formData.idArr,
              remark: this.remarkObj.formData.auditReason,
              tag: this.remarkObj.formData.labelCheckList.join(','),
            },
            (res) => {
              this.getTableDtate()
              this.remarkshow = false
              this.remarkObj.formData.auditReason = ''
            }
          )
        } else {
          return false
        }
      })
    },
    //批量驳回
    sensitiveBatchSet() {
      if (!this.selectionflag) {
        this.$message({
          message:
            '选项中包含大于等于100个号码数，如批量通过，请先将其单个审核通过！',
          type: 'warning',
        })
      } else {
        this.title = '批量驳回备注'
        this.remarkshow = true
      }
    },
    //批量切换通道组
    batchChannelGroup() {
      this.SwitchChannelGroup = true
    },
    uploadReview() {
      console.log(this.fileauditpic.join(','))
      this.$confirms.confirmation(
        'post',
        '是否确认审核通过？',
        window.path.omcs + 'v3/serviceaduitsms/audit',
        {
          overrule: false,
          auditStatus: 2,
          ids: this.rowId,
          auditPic: this.fileauditpic.join(','),
        },
        (res) => {
          this.uploadImage = false
          this.getTableDtate()
        }
      )
    },
    handleRemove1(file, fileList) {
      this.fileauditpic = []
      for (let i = 0; i < fileList.length; i++) {
        this.fileauditpic.push(fileList[i].response.data.fullpath)
      }
    },
    //图片上传成功
    handleAvatarSuccess1(res, file) {
      this.fileauditpic.push(res.data.fullpath)
    },
    // 图片上传限制
    beforeAvatarUpload1(file) {
      const isJPG1 = file.type === 'image/png'
      const isJPG2 = file.type === 'image/jpeg'
      const isJPG3 = file.type === 'image/tiff'
      const isJPG4 = file.type === 'image/raw'
      const isJPG5 = file.type === 'image/bmp'
      // const isLt2M = file.size / 1024 / 1024 < 1;
      if (!isJPG1 && !isJPG2 && !isJPG3 && !isJPG4 && !isJPG5) {
        this.$message.error('只允许上传图像')
        return false
      }
      // if (!isLt2M) {
      //     this.$message.error('上传头像图片大小不能超过 1MB!');
      // }
      // return isJPG && isLt2M;
    },
    // 确认切换通道
    confirmSwitchChannel() {
      this.$refs.refForm.validate((valid) => {
        if (valid) {
          // 单条切换通道
          this.SwitchChannelForm.ids = this.remarkObj.formData.idArr
          this.$confirms.confirmation(
            'post',
            '确认切换通道组？',
            window.path.omcs + 'v3/serviceaduitsms/audit',
            this.SwitchChannelForm,
            (res) => {
              this.SwitchChannelGroup = false //关闭弹窗
              this.getTableDtate()
            }
          )
        }
      })
    },
    //列表复选框的值
    handleSelectionChange(val) {
      this.selectionflag = true
      let selectId = []
      let auditStatus = []
      for (let i = 0; i < val.length; i++) {
        if (val[i].mobCount >= 100) {
          this.selectionflag = false
          // this.$message({
          //     message: '选项中包含大于等于100个号码数，如批量通过，请先将其单个审核通过！',
          //     type: 'warning'
          // });
        }
        selectId.push(val[i].id)
        //批量操作列表选中项的审核状态
        auditStatus.push(val[i].auditStatus)
      }
      this.auditStatus = auditStatus //选中项的审核状态
      this.remarkObj.formData.idArr = selectId.join(',') //批量通过，不通过的id数组
    },
  },
  watch: {
    //监听查询框对象的变化
    sensitiveConObj: {
      handler(val) {
        this.getTableDtate()
      },
      deep: true,
      immediate: true,
    },
    uploadImage(val) {
      if (val == false) {
        this.fileList1 = []
        this.fileauditpic = []
        this.rowId = ''
        this.getTableDtate()
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.turnStatus = false
          this.operationFlag = true //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.idArr = [] //置空ID
          this.getTableDtate()
        }
      }
    },
    // 监听切换通道组是否关闭
    SwitchChannelGroup(val) {
      if (this.$refs.refForm) {
        if (val == false) {
          this.$refs.refForm.resetFields()
          this.idArrs = []
          this.SwitchChannelForm.idArr = []
          this.getTableDtate()
        }
      }
    },
    // 监听通道组改变
    'SwitchChannelForm.channelGroupId'(val) {
      this.SwitchChannelForm.channelYd = ''
      this.SwitchChannelForm.channelLt = ''
      this.SwitchChannelForm.channelDx = ''
      if (val) {
        window.api.get(
          window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup/' + val,
          {},
          (res) => {
            this.channelYd = res.listYD2
            this.channelLt = res.listLT2
            this.channelDx = res.listDX2
          }
        )
      } else {
        this.channelYd = []
        this.channelLt = []
        this.channelDx = []
      }
    },
  },
}
</script>

<style scoped>
.el-checkbox-group > .el-checkbox {
  margin-left: 30px;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>

<style lang="less">
.spanColor {
  .prompt {
    color: #f56c6c;
  }
}
// .spanColor .prompt {
//   color: #f56c6c;
// }
.el-table--small th {
  background: #f5f5f5;
}
.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>
