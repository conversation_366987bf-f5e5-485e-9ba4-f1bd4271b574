<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="角色名称" prop="roleName">
              <el-input
                v-model="formInline.roleName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="角色状态" prop="delFlag">
              <el-select
                v-model="formInline.delFlag"
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="不限" value=""></el-option>
                <el-option label="启用" value="0"></el-option>
                <el-option label="停用" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>
        <div class="sensitive-fun" style="margin: 10px 0">
          <!-- <span class="sensitive-list-header">角色列表</span> -->
          <!-- <el-button type="primary" @click="roleDialog='添加角色';addCharacterPopUps=true;">添加角色</el-button> -->
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- 表格和分页开始 -->
          <table-tem
            :tableDataObj="tableDataObj"
            @handelOptionButton="handelOptionButton"
          ></table-tem>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>
      <!-- 添加角色 -->
      <el-dialog
        :title="roleDialog"
        v-model="addCharacterPopUps"
        width="25%"
        :close-on-click-modal="false"
      >
        <div class="addC" style="padding-right: 50px">
          <el-form
            ref="formAdd"
            :rules="rules"
            :model="formAdd"
            label-width="100px"
          >
            <el-form-item label="角色名称" prop="roleName">
              <el-input v-model="formAdd.roleName"></el-input>
            </el-form-item>
            <el-form-item label="角色状态" prop="delFlag">
              <el-select v-model="formAdd.delFlag" placeholder="请选择">
                <el-option label="不限" value=""></el-option>
                <el-option label="启用" value="0"></el-option>
                <el-option label="停用" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="角色标识" prop="roleCode">
              <el-input v-model="formAdd.roleCode"></el-input>
            </el-form-item>
            <el-form-item label="角色描述" prop="roleDesc">
              <el-input type="textarea" v-model="formAdd.roleDesc"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="addCharacterPopUps = false">取 消</el-button>
            <el-button type="primary" @click="RoleADD('formAdd')"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 权限分配 -->
      <el-dialog
        title="权限分配"
        v-model="PermissionPopUps"
        width="400px"
        :close-on-click-modal="false"
      >
        <el-tree
          :data="data2"
          show-checkbox
          node-key="id"
          ref="role"
          :default-checked-keys="PermissionArray"
          :props="defaultProps"
        >
        </el-tree>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="PermissionPopUps = false">取 消</el-button>
            <el-button type="primary" @click="roleID">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    DatePlugin,
    TableTem
  },
  name: 'RoleManagement',
  data() {
    // 验证角色名称
    var realName = (rule, value, callback) => {
      if (!/^[A-Za-z]+$/.test(value)) {
        return callback(new Error('请正确输入角色名称'))
      } else {
        callback()
      }
    }
    // 验证角色标识
    var roleCode = (rule, value, callback) => {
      if (!/^[A-Z_\u4e00-]+$/.test(value)) {
        return callback(new Error('请正确输入角色标识'))
      } else {
        callback()
      }
    }
    return {
      roleDialog: '',
      rules: {
        roleName: [
          { required: true, validator: realName, trigger: 'blur' },
          { min: 5, max: 30, message: '长度在 5 到 30个字符', trigger: 'blur' },
        ],
        delFlag: [
          { required: true, message: '角色状态不能为空', trigger: 'change' },
        ],
        roleCode: [{ required: true, validator: roleCode, trigger: 'blur' }],
        roleDesc: [
          {
            min: 3,
            max: 15,
            message: '长度在 5 到 15 个字符',
            trigger: 'blur',
          },
        ],
      },
      // 查询列表数据
      formInline: {
        roleName: '',
        delFlag: '',
        createName: '',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      //储存查询列表数据
      formInlines: {
        roleName: '',
        delFlag: '',
        createName: '',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 添加角色弹出窗
      addCharacterPopUps: false,
      // 添加角色
      formAdd: {
        roleName: '',
        delFlag: '',
        roleCode: '',
        roleDesc: '',
      },
      // 权限分配弹出窗
      PermissionPopUps: false,
      // 当前ID
      roleIDs: '',
      // 默认展开权限数组
      PermissionArray: [],
      //角色列表数据
      tableDataObj: {
        loading2: false,
        custom: true,
        id: "RoleManagement",
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'roleName',
            showName: '角色名称',
          },
          {
            prop: 'roleCode',
            showName: '角色标识',
          },
          {
            prop: 'delFlag',
            showName: '角色状态',
            width: '130',
            showCondition: {
              condition: '1',
            },
            formatData: function (val) {
              return val == '0' ? '启用' : '停用'
            },
          },
          {
            prop: 'roleDesc',
            showName: '角色描述',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '170',
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '140', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'delFlag', //关联的表格属性
            contactData: '0', //关联的表格属性-值
            optionName: '权限分配', //按钮的显示文字
            optionMethod: 'Permission', //按钮的方法
            icon: 'Star', //按钮图标
            optionButtonColor: '', //按钮颜色
          },
          // {
          //     contactCondition:'delFlag',//关联的表格属性
          //     contactData:'0',//关联的表格属性-值
          //     optionName:'编辑',//按钮的显示文字
          //     optionMethod:'edit',//按钮的方法
          //     icon:'el-icon-edit',//按钮图标
          //     optionButtonColor:'',//按钮颜色
          // },
          // {
          //     contactCondition:'delFlag',//关联的表格属性
          //     contactData:'0',//关联的表格属性-值
          //     optionName:'停用',//按钮的显示文字
          //     optionMethod:'DisableActivation',//按钮的方法
          //     icon:'el-icon-circle-close-outline',//按钮图标
          //     optionButtonColor:'orange',//按钮颜色
          //     otherOptionName:'启用',//其他条件的按钮显示文字
          //     otherOptionMethod:'DisableActivation',//其他条件的按钮方法
          //     otherIcon:'el-icon-circle-check-outline',//其他条件按钮的图标
          //     optionOtherButtonColor:''//其他条件按钮的颜色
          // },
          // {
          //     contactCondition:'delFlag',//关联的表格属性
          //     contactData:'1',//关联的表格属性-值
          //     optionName:'删除',//按钮的显示文字
          //     optionMethod:'delete',//按钮的方法
          //     icon:'el-icon-delete',//按钮图标
          //     optionButtonColor:'#f56c6c',//按钮颜色
          // }
        ],
      },
      // 权限分配数据
      data2: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.upms + 'role/rolePage',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      Object.assign(this.formInlines, this.formInline)
    },
    // 创建时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0]
        this.formInline.endTime = val[1] + ' 23:00'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 设置权限
    roleID() {
      this.$confirms.confirmation(
        'put',
        '确认分配权限',
        window.path.upms +
          'role/roleMenuUpd?roleId=' +
          this.roleIDs +
          '&menuIds=' +
          this.$refs.role
            .getCheckedKeys()
            .concat(this.$refs.role.getHalfCheckedKeys())
            .toString(),
        {},
        (res) => {
          this.PermissionPopUps = false
        }
      )
    },
    // 角色操作
    handelOptionButton: function (val) {
      console.log(val)
      if (val.methods == 'Permission') {
        this.roleIDs = val.row.roleId
        this.PermissionPopUps = true
        let a = { platform: '' }
        if (
          val.row.roleCode == 'ROLE_MC' ||
          val.row.roleCode == 'ROLE_SU' ||
          val.row.roleCode == 'ROLE_EU' ||
          val.row.roleCode == 'ROLE_EUW'
        ) {
          a.platform = '3'
        } else {
          a.platform = '1'
        }
        a.flag = '1'
        window.api.post(window.path.upms + 'menu/allTree', a, (resz) => {
          window.api.get(
            window.path.upms + 'role/getMenus/' + val.row.roleId,
            {},
            (res) => {
              this.data2 = resz
              this.PermissionArray = (res + '').split(',')
            }
          )
        })
      } else if (val.methods == 'edit') {
        this.roleDialog = '编辑角色'
        this.addCharacterPopUps = true
        this.$nextTick(() => {
          this.$refs.formAdd.resetFields()
          Object.assign(this.formAdd, val.row)
        })
      } else if (val.methods == 'DisableActivation') {
        this.$confirms.confirmation(
          'get',
          val.row.delFlag == 0 ? '确认停用' : '确认启用',
          window.path.upms +
            'role/updateStatus/' +
            val.row.roleId +
            '/' +
            (val.row.delFlag == 0 ? 1 : 0),
          {},
          (res) => {
            this.InquireList()
            this.addCharacterPopUps = false
          }
        )
      } else if (val.methods == 'delete') {
        this.$confirms.confirmation(
          'delete',
          '确认删除',
          window.path.upms + 'role/' + val.row.roleId,
          {},
          (res) => {
            this.InquireList()
          }
        )
      }
    },
    // 添加、编辑角色
    RoleADD(formAdd) {
      this.$refs[formAdd].validate((valid) => {
        if (valid) {
          window.api.get(
            window.path.upms + 'role/exists/' + this.formAdd.roleName,
            {},
            (res) => {
              if (res.code == 400) {
                this.$message({
                  message: res.msg,
                  type: 'warning',
                })
              } else if (res.code == 0) {
                if (this.roleDialog == '添加角色') {
                  this.$confirms.confirmation(
                    'post',
                    '确认添加',
                    window.path.upms + 'role',
                    this.formAdd,
                    (res) => {
                      this.InquireList()
                      this.addCharacterPopUps = false
                    }
                  )
                } else {
                  this.$confirms.confirmation(
                    'put',
                    '确认编辑',
                    window.path.upms + 'role',
                    this.formAdd,
                    (res) => {
                      this.InquireList()
                      this.addCharacterPopUps = false
                    }
                  )
                }
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
    // 父级菜单选中
    handleChange(value) {
      console.log(value)
      this.formAdd.RoleMenu = value[value.length - 1]
    },
  },
  activated() {
    this.InquireList()
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps: function (val) {
      if (val == false) {
        this.$refs.formAdd.resetFields() //清空表单
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
    PermissionPopUps: function (val) {
      if (!val) {
        this.data2 = []
        this.PermissionArray = []
      }
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
