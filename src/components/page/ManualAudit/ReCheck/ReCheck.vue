<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="82px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" label-width="82px" prop="consumerName">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称" label-width="82px" prop="signature">
            <el-input
              v-model="sensitiveCondition.compName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" label-width="82px" prop="status">
            <el-select
              v-model="sensitiveCondition.status"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="初审通过" value="1"></el-option>
              <el-option label="认证通过" value="3"></el-option>
              <el-option label="复核不通过" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间" label-width="82px" prop="time1">
            <el-date-picker
              class="input-w"
              v-model="sensitiveCondition.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande1"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="审核时间" label-width="82px"  prop="time2">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time2"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande2"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div class="boderbottom" style="padding-left: 10px">
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-fun">
        <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">签名审核列表</span> -->
        <!-- <el-button type="primary"  @click="establishSig()">创建签名</el-button> -->
        <!-- <el-button type="primary" @click="sensitiveBatchDel()" v-if="remarkObj.formData.idArr.length != 0">批量通过</el-button>
                    <el-button type="primary" @click="sensitiveBatchSet()" v-if="remarkObj.formData.idArr.length != 0">批量驳回</el-button> -->
      </div>
      <div class="sensitive-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="46"></el-table-column>
          <!-- <el-table-column label="签名ID" width="80">
                            <template #default="scope">{{ scope.row.id }}</template>
                        </el-table-column> -->
          <el-table-column label="用户名称">
            <template v-slot="scope">{{ scope.row.username }}</template>
          </el-table-column>
          <el-table-column label="公司名称">
            <template v-slot="scope">
              <span>{{ scope.row.signature }}</span>
              <span style="color: #f56c6c">{{ scope.row.compName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名">
            <template v-slot="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="手机号码">
            <template v-slot="scope">{{ scope.row.phone }}</template>
          </el-table-column>
          <!-- <el-table-column label="身份证号码">
                             <template #default="scope">{{ scope.row.idCard}}</template>
                        </el-table-column> -->
          <!-- <el-table-column label="证照编号">
                             <template #default="scope">{{ scope.row.number}}</template>
                        </el-table-column> -->
          <!-- <el-table-column label="证件照" width="80">
                            <template #default="scope">
                                <el-button type="text" @click="lookUp(scope.$index, scope.row)"><i class="el-icon-picture"></i>&nbsp;预览</el-button>
                             </template>
                        </el-table-column> -->
          <el-table-column label="初申请时间">
            <template v-slot="scope">
              <span v-if="scope.row.auditTime">{{
                scope.row.auditTime | fmtDate
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="90">
            <template v-slot="scope">
              <span v-if="scope.row.status == '1'">初审通过</span>
              <span v-else-if="scope.row.status == '3'" style="color: #16a589"
                >认证通过</span
              >
              <span v-else-if="scope.row.status == '4'" style="color: #f56c6c"
                >复审不通过</span
              >
            </template>
          </el-table-column>
          <el-table-column label="复申请时间">
            <template
              v-if="scope.row.status == '3' || scope.row.status == '4'"
              v-slot="scope"
            >
              <span>{{ scope.row.checkTime | fmtDate }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="remark" label="备注内容">
                            <template #default="scope" >
                                <span>{{scope.row.remark}}</span>
                            </template>
                        </el-table-column> -->
          <el-table-column label="操作" width="120">
            <template v-slot="scope">
              <!-- <el-button type="text" style="color:#f56c6c;margin-left:0px;" @click="handleEdit(scope.$index, scope.row)"><i class="el-icon-delete"></i>&nbsp;删除</el-button> -->
              <el-button
                type="text"
                style="margin-left: 0px"
                @click="handleAdopt(1, scope.row)"
                ><el-icon><el-icon-tickets /></el-icon>&nbsp;详情</el-button
              >
              <!-- <el-button type="text" style="margin-left:0px;" @click="handleAdopt(3,scope.row)"><i class="el-icon-success"></i>&nbsp;通过</el-button>
                                <el-button type="text" style="color:orange;margin-left:0px;" @click="handleReject(4,scope.row)"><i class="el-icon-error"></i>&nbsp;不通过</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹框 （审核 通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="500px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="80px"
          style="padding: 0 28px"
        >
          <el-form-item label="拒绝原因" prop="auditReason">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog title="证件照" v-model="dialogVisible" width="30%">
        <el-image
          style="width: 250px; height: 250px"
          :src="authorization"
          :z-index="9999"
          :preview-src-list="srcList"
          :append-to-body="true"
                                    :preview-teleported="true"
        >
        </el-image>
        <el-image
          style="width: 250px; height: 250px"
          :src="businessLicense"
          :z-index="9999"
          :preview-src-list="srcList1"
          :append-to-body="true"
                                    :preview-teleported="true"
        >
        </el-image>
      </el-dialog>
      <el-dialog
        title="详情"
        v-model="dialogTemplate"
        :close-on-click-modal="false"
        width="700px"
      >
        <div>
          <div class="detailsList">
            <span class="detailsList-title">用户名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.createName
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">公司名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.compName
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">姓名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.name
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">手机号:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.phone
            }}</span>
          </div>
          <!-- <div class="detailsList">
                <span class="detailsList-title">身份证号:</span>
                <span class="detailsList-content" style="width: 300px;">{{TemplateObject.idCard}}</span>
              </div> -->
          <!-- <div class="detailsList">
                <span class="detailsList-title">证照编号:</span>
                <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span>
              </div> -->
          <div class="detailsList">
            <span class="detailsList-title">身份证正反照:</span>
            <el-image
              v-if="TemplateObject.idCardFront"
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + TemplateObject.idCardFront"
              :preview-src-list="srcList2"
              :append-to-body="true"
                                    :preview-teleported="true"
            >
            </el-image>
            <el-image
              v-if="TemplateObject.idCardBack"
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + TemplateObject.idCardBack"
              :preview-src-list="srcList3"
              :append-to-body="true"
                                    :preview-teleported="true"
            >
            </el-image>
            <!-- <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span> -->
          </div>
          <div class="detailsList">
            <span class="detailsList-title">证件照:</span>
            <el-image
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + TemplateObject.authorization"
              :preview-src-list="srcList"
              :append-to-body="true"
                                    :preview-teleported="true"
            >
            </el-image>
            <el-image
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + TemplateObject.businessLicense"
              :preview-src-list="srcList1"
              :append-to-body="true"
                                    :preview-teleported="true"
            >
            </el-image>
            <!-- <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span> -->
          </div>
          <div class="detailsList">
            <span class="detailsList-title">审核状态:</span>
            <span
              class="detailsList-content"
              v-if="TemplateObject.status == 1"
              style="width: 300px"
              >初审通过</span
            >
            <span
              class="detailsList-content"
              v-if="TemplateObject.status == 3"
              style="width: 300px; color: #16a589"
              >审核通过</span
            >
            <span
              class="detailsList-content"
              v-if="TemplateObject.status == 4"
              style="width: 300px; color: #f56c6c"
              >审核不通过</span
            >
          </div>
          <div style="display: flex">
            <div
              v-if="TemplateObject.status == 3 || TemplateObject.status == 4"
            >
              <div class="detailsList">
                <span class="detailsList-titles">初审审核人:</span>
                <span class="detailsList-contents">{{
                  TemplateObject.auditName
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-titles">初审时间:</span>
                <span class="detailsList-contents">{{
                  moment(TemplateObject.auditTime).format('YYYY-MM-DD HH:mm:ss')
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-titles">初审备注:</span>
                <span class="detailsList-contents">{{
                  TemplateObject.auditReason
                }}</span>
              </div>
            </div>
            <div v-else>
              <div class="detailsList">
                <span class="detailsList-title">初审审核人:</span>
                <span class="detailsList-content">{{
                  TemplateObject.auditName
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-title">初审时间:</span>
                <span class="detailsList-content">{{
                  moment(TemplateObject.auditTime).format('YYYY-MM-DD HH:mm:ss')
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-title">初审备注:</span>
                <span class="detailsList-content">{{
                  TemplateObject.auditReason
                }}</span>
              </div>
            </div>
            <div
              v-if="TemplateObject.status == 3 || TemplateObject.status == 4"
            >
              <div class="detailsList">
                <span class="detailsList-titles">复审审核人:</span>
                <span class="detailsList-contents">{{
                  TemplateObject.checkName
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-titles">复审时间:</span>
                <span class="detailsList-contents">{{
                  moment(TemplateObject.checkTime).format('YYYY-MM-DD HH:mm:ss')
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-titles">复审备注:</span>
                <span class="detailsList-contents">{{
                  TemplateObject.checkReason
                }}</span>
              </div>
            </div>
          </div>
          <div class="detailsList" v-if="TemplateObject.status == 1">
            <span class="detailsList-title">备注:</span>
            <span class="bs">*</span
            ><el-input
              style="width: 300px; margin-top: 10px"
              type="textarea"
              v-model="auditReason"
            ></el-input>
            <!-- <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span> -->
          </div>
        </div>
        <template v-slot:footer>
          <div class="dialog-footer" v-if="TemplateObject.status == 1">
            <el-button type="primary" @click="templaS()">通过</el-button>
            <el-button type="danger" @click="templaF()">不通过</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import moment from 'moment'
export default {
  components: {
    TableTem,
    FileUpload
  },
  name: 'SignatureAudit',
  data() {
    return {
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      SigDialogVisible: false, //弹出框创建签名显示隐藏
      remarkshow: false,
      dialogTemplate: false,

      dialogVisible: false, //证件照
      //驳回弹出框的值
      TemplateObject: {},
      auditReason: '',
      authorization: '',
      remarkObj: {
        formData: {
          auditReason: '',
          idArr: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: '请输入拒绝原因', trigger: 'blur' },
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      srcList: [],
      srcList1: [],
      srcList2: [],
      srcList3: [],
      authorization: '',
      businessLicense: '',
      idArrs: [], // 驳回的id
      title: '',
      signatureFrom: {
        title: '',
        formData: {
          signature: '',
          clientName: '',
          signatureType: '1',
          remark: '',
          imgUrl: '',
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: '该输入项为必填项!', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
          ],
          signatureType: [
            { required: true, message: '请选择签名类型', trigger: 'change' },
          ],
          imgUrl: [
            { required: true, message: '请选择上传图片', trigger: 'change' },
          ],
        },
        signature: '', //签名
        id: '', //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      status: [],
      //查询条件的值
      sensitiveCondition: {
        compName: '',
        phone: '',
        status: '1',
        consumerName: '',
        beginTime: '',
        endTime: '',
        time1: [],
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        compName: '',
        phone: '',
        status: '1',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
    }
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatinguser/certificate/page',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    sensitiveQuery() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      this.sensitiveCondition.status = '1'
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.auditEndTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.auditStartTime = ''
        this.sensitiveCondition.auditEndTime = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
    },
    //查看图片
    lookUp(index, row) {
      console.log(row)
      // this.imgUrl = row.image.split(',')
      // console.log();
      this.authorization = window.path.imgU + row.authorization
      this.businessLicense = window.path.imgU + row.businessLicense
      console.log(this.authorization, 'll')
      this.srcList.push(window.path.imgU + row.authorization)
      this.srcList1.push(window.path.imgU + row.businessLicense)
      // this.srcList.push(window.path.imgU+this.imgUrl[0])
      // this.srcList1.push(window.path.imgU+this.imgUrl[1])
      // console.log(this.srcList);
      this.dialogVisible = true
    },
    //删除
    handleEdit(index, row) {
      this.$confirms.confirmation(
        'get',
        '此操作将永久删除该条数据, 是否继续?',
        window.path.omcs + 'signatureaudit/delete/' + row.id,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //通过
    handleAdopt(status, row) {
      this.dialogTemplate = true
      this.TemplateObject = row
      this.srcList.push(window.path.imgU + row.authorization)
      this.srcList1.push(window.path.imgU + row.businessLicense)
      this.srcList2.push(window.path.imgU + row.idCardFront)
      this.srcList3.push(window.path.imgU + row.idCardBack)
      // console.log(row,'ss');
      // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs+'operatinguser/certificate/audit/check',{
      //     auditStatus:status,
      //     ids:row.id
      // },res =>{
      //     this.getTableDtate();
      // });
    },
    templaS() {
      if (this.auditReason == '') {
        this.$message({
          type: 'warning',
          duration: '2000',
          message: '请填写备注',
        })
      } else {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.omcs + 'operatinguser/certificate/audit/check',
          {
            auditStatus: 3,
            ids: this.TemplateObject.id,
            auditReason: this.auditReason,
          },
          (res) => {
            this.dialogTemplate = false
            this.getTableDtate()
          }
        )
      }
    },
    templaF() {
      if (this.auditReason == '') {
        this.$message({
          type: 'warning',
          duration: '2000',
          message: '请填写备注',
        })
      } else {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.omcs + 'operatinguser/certificate/audit/check',
          {
            auditStatus: 4,
            ids: this.TemplateObject.id,
            auditReason: this.auditReason,
          },
          (res) => {
            this.dialogTemplate = false
            this.getTableDtate()
          }
        )
      }
    },
    //批量通过
    sensitiveBatchDel() {
      // let status = true;
      // //判断选项中是否有审核过的状态
      // for(let i =0; i< this.status.length; i++){
      //     if(this.status[i] != '1'){
      //         status = false;
      //         break;
      //     }
      // }
      let ids = this.remarkObj.formData.idArr.join(',')
      console.log(ids)
      this.$confirms.confirmation(
        'post',
        '此操作不可逆，是否继续?',
        window.path.omcs + 'operatinguser/certificate/audit/check',
        { auditStatus: 3, ids: ids },
        (res) => {
          this.getTableDtate()
          this.remarkObj.formData.idArr = []
        }
      )

      // if(status){
      //     this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs+'operatinguser/certificate/audit/check',{auditStatus:3,ids:ids},res =>{
      //         this.getTableDtate();
      //         this.remarkObj.formData.idArr = [];
      //     });
      // }else{
      //     this.$message({
      //         message: '选项中包含已审核项，需重新选择待审核项！',
      //         type: 'warning'
      //     });
      // }
    },
    //驳回
    handleReject(index, row) {
      this.idArrs = []
      this.remarkshow = true
      this.title = '驳回备注'
      this.idArrs.push(row.id) //赋值ID
      console.log(this.idArrs, 'll')
      this.idArrs.join(',')
      console.log(this.idArrs.join(','))
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let aa = {}
          if (this.title == '驳回备注') {
            aa.auditReason = this.remarkObj.formData.auditReason
            aa.ids = this.idArrs.join(',')
            aa.auditStatus = 4
          } else {
            this.operationFlag = false //操作进行中（按钮的显示）
            aa.auditReason = this.remarkObj.formData.auditReason
            aa.ids = this.remarkObj.formData.idArr.join(',')
            aa.auditStatus = 4
          }
          this.$confirm('此操作不可逆，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
          })
            .then(() => {
              window.api.post(
                window.path.omcs + 'operatinguser/certificate/audit/check',
                aa,
                (res) => {
                  this.getTableDtate()
                  this.remarkshow = false
                  this.remarkObj.formData.idArr = [] //置空ID
                  if (res.code == 200) {
                    this.$message({
                      type: 'success',
                      duration: '2000',
                      message: '操作成功!',
                    })
                  } else {
                    this.$message({
                      type: 'error',
                      duration: '2000',
                      message: res.msg,
                    })
                  }
                }
              )
            })
            .catch(() => {
              this.operationFlag = true
              this.$message({
                type: 'info',
                duration: '2000',
                message: '已取消操作!',
              })
            })
          // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs + 'signatureaudit/auditNotPass', aa,res =>{
          //         this.getTableDtate();
          //         this.remarkshow = false;
          //         this.remarkObj.formData.idArr=[]; //置空ID
          //     });
        } else {
          return false
        }
      })
    },
    //批量驳回
    sensitiveBatchSet() {
      this.remarkshow = true
      // let status = true;
      // //判断选项中是否有审核过的状态
      // this.title = '批量驳回备注'
      // for(let i =0; i< this.status.length; i++){
      //     if(this.status[i] != '1'){
      //         status = false;
      //         break;
      //     }
      // }
      // if(status){

      // }else{
      //     this.$message({
      //         message: '选项中包含已审核项，需重新选择待审核项！',
      //         type: 'warning'
      //     });
      // }
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let status = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
        //批量操作列表选中项的审核状态
        status.push(val[i].status)
      }
      this.status = status //选中项的审核状态
      this.remarkObj.formData.idArr = selectId //批量通过，不通过的id数组
    },
    //创建签名
    establishSig() {
      this.SigDialogVisible = true
      this.del1 = true
    },
    //提交新增签名的表单
    signature_add(formName, title) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {}
          Object.assign(formDates, this.signatureFrom.formData)
          if (this.signatureFrom.formData.signatureType != 6) {
            if (this.signatureFrom.formData.imgUrl != '') {
              this.sendRequest(
                'post',
                '确定新增签名',
                window.path.omcs + 'signatureaudit/addResourceSignature',
                formDates
              )
            } else {
              this.$message({
                message: '请上传图片！',
                type: 'warning',
              })
            }
          } else {
            this.sendRequest(
              'post',
              '确定新增签名',
              window.path.omcs + 'signatureaudit/addResourceSignature',
              formDates
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //发送新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      window.api.get(
        window.path.omcs +
          'signatureaudit/findModelBySignature/' +
          this.signatureFrom.formData.signature,
        {},
        (res) => {
          if (res.code == 200 && res.data == '0') {
            //发送新增的请求
            this.$confirms.confirmation(
              type,
              title,
              action,
              formDates,
              (res) => {
                this.getTableDtate() //重载列表
                this.SigDialogVisible = false //隐藏弹窗
              }
            )
          } else {
            this.$message({
              message: '签名已存在，切勿重复！',
              type: 'warning',
            })
          }
        }
      )
    },
    //移除文件
    fileup(val) {
      let aa = this.signatureFrom.formData.imgUrl.split(',')
      aa.splice(aa.indexOf(val), 1)
      this.signatureFrom.formData.imgUrl = aa.join(',')
    },
    //文件上传成功
    fileupres(val) {
      this.signatureFrom.imgUrl.push(val)
      this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(',')
    },
  },
  watch: {
    //监听查询框对象的变化
    sensitiveConObj: {
      handler(val) {
        this.getTableDtate()
      },
      deep: true,
      immediate: true,
    },
    //监听设置标签弹框是否关闭
    setLabelDialog(val) {
      if (this.$refs.setLabel) {
        if (val == false) {
          this.$refs.setLabel.resetFields()
        }
      }
    },
    //监听创建签名弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.signatureFrom.imgUrl = []
        this.signatureFrom.formData.imgUrl = ''
        this.del1 = false
        this.$refs.signatureFrom.resetFields() //清空表单
      }
    },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true
      } else {
        this.disabledinput = false
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.operationFlag = true //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.idArr = [] //置空ID
          this.getTableDtate()
        }
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.detailsList {
  line-height: 42px;
  padding-left: 30px;
  font-size: 12px;
  position: relative;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.bs {
  position: absolute;
  top: 0px;
  left: 100px;
  color: red;
  font-size: 14px;
  z-index: 9999;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.detailsList-titles {
  display: inline-block;
  width: 80px;
}
.detailsList-contents {
  display: inline-block;
  width: 170px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>
