# 📊 SendRecord.vue 查询条件优化

## 🎯 优化目标

优化SendRecord.vue组件的查询逻辑，实现以下功能：
1. **智能条件传递**: 从"今天"切换到"近一个月"时，自动带过去已有的查询条件
2. **条件检测**: 如果"近一个月"没有查询条件，则不自动调用接口
3. **避免重复请求**: 防止切换时间选项时触发多次接口调用

## ✨ 核心功能特性

### 📋 主要优化点
- **条件保持**: 切换时间范围时保持现有查询条件
- **智能查询**: 根据是否有查询条件决定是否自动查询
- **防重复调用**: 避免时间选项切换时的重复接口请求
- **用户体验**: 提升查询操作的流畅性和合理性

## 🔧 技术实现

### 1. 数据结构新增

#### **标志位控制**
```javascript
data() {
  return {
    // ... 其他数据
    isTimeOptionsChanging: false, // 标记是否正在切换时间选项
    // ... 其他数据
  }
}
```

### 2. 核心方法实现

#### **查询条件检测方法**
```javascript
// 检查是否有查询条件
hasSearchConditions() {
  const conditions = [
    'clientName', 'channelId', 'mobile', 'provincial', 'operator',
    'signature', 'content', 'msgid', 'smsType', 'smsStatus', 
    'temId', 'originalCode', 'ip', 'source', 'parentUsername'
  ];
  
  return conditions.some(key => {
    const value = this.form[key];
    return value && value.toString().trim() !== '';
  }) || (this.form.sendType && this.form.sendType !== '2'); // sendType默认值是'2'
}
```

#### **优化后的时间选项切换方法**
```javascript
handleChangeTimeOptions: function (val) {
  // 设置标志位，防止Times监听器重复调用接口
  this.isTimeOptionsChanging = true;
  
  // 保存当前的查询条件
  const hasSearchConditions = this.hasSearchConditions();
  
  if (val === "1") {
    // 切换到今天
    this.Times.beginTime = moment()
      .startOf("day")
      .format("YYYY-MM-DD HH:mm:ss");
    this.Times.endTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
    this.datePluginValue = [
      moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
      moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    ];
    this.flag = "1";
    
    // 今天默认查询
    this.$nextTick(() => {
      Object.assign(this.formData, this.form);
      this.getTableDtate();
      this.isTimeOptionsChanging = false;
    });
  } else if (val === "2") {
    // 切换到近一个月
    this.Times.beginTime = moment()
      .subtract(30, "days")
      .format("YYYY-MM-DD 00:00:00");
    this.Times.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
    this.datePluginValue = [
      moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      moment().format("YYYY-MM-DD HH:mm:ss"),
    ];
    this.flag = "2";
    
    // 近一个月只有在有查询条件时才自动查询
    if (hasSearchConditions) {
      this.$nextTick(() => {
        Object.assign(this.formData, this.form);
        this.getTableDtate();
        this.isTimeOptionsChanging = false;
      });
    } else {
      // 没有查询条件，不自动调用接口，但要重置标志位
      this.$nextTick(() => {
        this.isTimeOptionsChanging = false;
      });
    }
  }
}
```

#### **优化后的时间监听器**
```javascript
//监听时间是否改变
Times: {
  handler(val) {
    // 只有在手动选择时间时才自动查询，避免切换时间选项时重复查询
    if (val && !this.isTimeOptionsChanging) {
      this.getTableDtate();
    }
  },
  deep: true,
}
```

## 🔄 功能流程

### 1. 今天 → 近一个月切换流程
```
用户选择"近一个月" → 检查当前查询条件 → 设置时间范围 → 
有条件：自动查询 | 无条件：不查询，等待用户输入条件
```

### 2. 近一个月 → 今天切换流程
```
用户选择"今天" → 设置时间范围 → 自动查询（今天默认查询）
```

### 3. 手动时间选择流程
```
用户手动选择时间 → Times监听器触发 → 自动查询
```

## 🎯 查询条件检测

### 支持的查询字段
- **基础信息**: clientName（客户名称）、channelId（通道ID）、mobile（手机号）
- **地域运营商**: provincial（省份）、operator（运营商）
- **内容相关**: signature（签名）、content（内容）、msgid（消息ID）
- **状态类型**: smsStatus（短信状态）、sendType（发送类型）
- **技术字段**: temId（模板ID）、originalCode（原始码）、ip（IP地址）
- **来源信息**: source（来源）、parentUsername（父用户名）

### 不作为查询条件的字段
- **smsType（短信类型）**: 即使有此条件，"近一个月"也不会自动查询

### 条件判断逻辑
```javascript
// 检查是否有查询条件（排除smsType）
hasSearchConditions() {
  const conditions = [
    'clientName', 'channelId', 'mobile', 'provincial', 'operator',
    'signature', 'content', 'msgid', 'smsStatus',
    'temId', 'originalCode', 'ip', 'source', 'parentUsername'
  ];

  return conditions.some(key => {
    const value = this.form[key];
    return value && value.toString().trim() !== '';
  }) || (this.form.sendType && this.form.sendType !== '2');
}
```

**注意**: `smsType` 字段已从条件检测中移除，即使有此条件，"近一个月"也不会自动查询。

## 🎨 用户体验优化

### 1. 智能查询策略
- **今天**: 切换后立即查询，符合用户查看当日数据的习惯
- **近一个月**: 只有在有查询条件时才查询，避免无条件的大数据量请求

### 2. 防重复请求
- **标志位控制**: 使用`isTimeOptionsChanging`标志位防止重复调用
- **监听器优化**: Times监听器只在手动选择时间时触发

### 3. 条件保持
- **无缝切换**: 切换时间范围时保持所有已输入的查询条件
- **状态同步**: 确保formData与form数据同步

## 📊 性能优化

### 1. 接口调用优化
- **条件检测**: 避免无条件的大数据量查询
- **防重复**: 避免切换时的多次接口调用
- **异步处理**: 使用$nextTick确保DOM更新后再调用接口

### 2. 内存优化
- **标志位管理**: 及时重置标志位，避免内存泄漏
- **深度监听**: 合理使用深度监听，避免不必要的性能消耗

## 🔍 使用场景

### 场景1：有查询条件的切换
```
用户在"今天"输入了客户名称 → 切换到"近一个月" → 
自动带过客户名称条件 → 自动查询近一个月该客户的数据
```

### 场景2：无查询条件的切换
```
用户在"今天"没有输入任何条件 → 切换到"近一个月" → 
不自动查询 → 等待用户输入查询条件后手动查询
```

### 场景3：手动时间选择
```
用户手动选择时间范围 → Times监听器触发 → 自动查询
```

## 📋 注意事项

### 1. 数据同步
- 确保formData与form数据的同步
- 切换时保持所有查询条件不丢失

### 2. 标志位管理
- 及时设置和重置isTimeOptionsChanging标志位
- 避免标志位状态异常导致的功能问题

### 3. 异步处理
- 使用$nextTick确保DOM更新完成
- 避免异步操作的时序问题

## 🚀 扩展建议

### 1. 缓存机制
- 添加查询结果缓存，避免重复查询相同条件
- 实现智能缓存失效机制

### 2. 条件预设
- 支持保存常用查询条件组合
- 提供快速查询模板功能

### 3. 性能监控
- 添加查询性能监控
- 统计查询频率和响应时间

### 4. 用户偏好
- 记住用户的时间范围偏好
- 支持自定义默认查询条件

## 📁 相关文件

### 主要文件
- **组件文件**: `src/components/page/StatisticalAnalysis/components/SendRecord.vue`
- **功能**: 发送记录查询页面的时间选项优化

### 核心方法
- **handleChangeTimeOptions**: 时间选项切换处理
- **hasSearchConditions**: 查询条件检测
- **Times监听器**: 时间变化监听优化

---

**优化完成**: SendRecord.vue的查询条件切换逻辑已优化，实现了智能条件传递和防重复查询功能，提升了用户体验和系统性能。
