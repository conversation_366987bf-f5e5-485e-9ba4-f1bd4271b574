// 推送管理相关路由
export default [
  {
    path: '/pushManagement',
    meta: { title: ' 状态推送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/pushManagement/pushManagement.vue'),
  },
  {
    path: '/RMSPushManagement',
    meta: { title: ' 视频短信补推', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/RmsPushManagement/pushManagement.vue'),
  },
  {
    path: '/IMSPushManagement',
    meta: { title: ' 国际短信补推', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/ImsPushManagement/pushManagement.vue'),
  },
  {
    path: '/voiceCodePushManagement',
    meta: { title: ' 语音验证码补推', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/voiceCodePushManagement/pushManagement.vue'),
  },
  {
    path: '/voicePushManagement',
    meta: { title: ' 语言通知补推', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/voicePushManagement/pushManagement.vue'),
  },
] 