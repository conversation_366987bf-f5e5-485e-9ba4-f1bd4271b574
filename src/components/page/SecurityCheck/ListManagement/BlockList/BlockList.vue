<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        ref="formBlackList"
      >
        <el-form-item label="签名" prop="sign">
          <el-input
            v-model="formBlackList.sign"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="quenryBlack()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="BlockList"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column
            field="签名"
            title="签名"
            width="150"
          >
            <template v-slot="scope">
              <div>
                {{ scope.row.sign }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="生效时间" title="生效时间">
            <template v-slot="scope">
              <div>
                {{ scope.row.startTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag
                :disable-transitions="true" v-if="scope.row.status == 1" type="danger">屏蔽</el-tag>
              <el-tag
                :disable-transitions="true" v-else type="success">不屏蔽</el-tag>
              <!-- <span v-if="scope.row.status==1">屏蔽</span>
                                  <span v-else>不屏蔽</span> -->
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>
                {{ scope.row.createTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="更新时间" title="更新时间">
            <template v-slot="scope">
              <div>
                {{ scope.row.updateTime }}
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column  field="操作" title="操作" width="100">
                             <template #default="scope">
                                 <el-button type="text" style="color:#F56C6C"  @click="delet(scope.row)"><i class=""></i>&nbsp;解&nbsp;除</el-button>
                             </template>
                         </vxe-column> -->
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formBlackListObj.currentPage"
            :page-size="formBlackListObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import moment from 'moment'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    TableTem
  },
  name: 'BlackLIst',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      SigDialogVisible: false,
      fileList: [],
      header: '',
      formBlackList: {
        //查询框的值
        // blacklistCity: '',
        // blacklistPhone:'',
        // blacklistLevel:'',
        // blacklistType:'',
        // isDelete:'',
        // createTime:[],
        // createName:'',
        // begTime:'',
        // endTime:'',
        sign: '',
        currentPage: 1,
        pageSize: 10,
      },
      formBlackListObj: {
        //copy查询框的值
        // blacklistCity: '',
        // blacklistPhone:'',
        // blacklistLevel:'',
        // blacklistType:'',
        // isDelete:'',
        // createTime:[],
        // createName:'',
        // begTime:'',
        // endTime:'',
        currentPage: 1,
        pageSize: 10,
      },
      selectId: '', //批量操作选中id
      rowId: '', //操作行的id
      blackListDialog: false, //弹框显示隐藏
      blackList: {
        formData: {
          blacklistPhone: '',
          blacklistType: '',
          blacklistLevels: [],
          remark: '',
        },
        formRule: {
          blacklistType: [
            { required: true, message: '请选择投诉类型', trigger: 'change' },
          ],
          blacklistLevels: [
            { required: true, message: '请选择黑号等级', trigger: 'change' },
          ],
          remark: [
            {
              min: 1,
              max: 100,
              message: '长度在 1 到 100 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      blackList3: {
        formData: {
          blacklistType: '',
          blacklistLevel: '',
        },
        formRule: {
          blacklistType: [
            { required: true, message: '请选择投诉类型', trigger: 'change' },
          ],
          blacklistLevel: [
            { required: true, message: '请选择黑号等级', trigger: 'change' },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        tableData: [],
        sign: '',
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //-------------   列表信息   --------------
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true //打开loading
      window.api.post(
        window.path.omcs + 'operatingblacksign/page',
        this.formBlackListObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          // console.log(this.tableDataObj.tableData,'data');
          // this.tableDataObj.tableData.forEach(item=>{
          //     // console.log(item,'ll');
          //     // // this.formatData(item.createTime)
          //     // this.moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')
          //     // this.moment(item.startTime).format()
          //     // this.moment(item.updateTime).format()
          // })

          this.tableDataObj.tablecurrent.totalRow = res.data.total
          this.tableDataObj.loading2 = false //关闭loading
        }
      )
    },
    formatData(val) {
      console.log(val)
      return formatDate(new Date(val * 1), 'YYYY-MM-DD HH:mm:ss')
    },
    //查询
    quenryBlack() {
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    // hande: function (val) {
    //     if(val){
    //         this.formBlackList.begTime = this.moment(val[0]).format("YYYY-MM-DD");
    //         this.formBlackList.endTime = this.moment(val[1]).format("YYYY-MM-DD");
    //     }else{
    //         this.formBlackList.begTime = '';
    //         this.formBlackList.endTime = '';
    //     }
    // },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.formBlackListObj.pageSize = size
      this.getTableDtate()
    },

    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.formBlackListObj.currentPage = currentPage
      this.getTableDtate()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.formBlackList.begTime = ''
      this.formBlackList.endTime = ''
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    handelSelection(val) {
      //列表复选框的值
      console.log(val, 'val')
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
      }
      this.selectId = selectId.join(',')
      //    console.log(this.selectId);
    },
  },
  mounted() {
    this.header = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  watch: {
    //监听查询框对象的变化
    // formBlackListObj:{
    //     handler(){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    blackListDialog(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.blackList.resetFields() //清空表单
      }
    },
    //监听导入弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.$refs.signatureFrom.resetFields() //清空表单
        this.fileList = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
