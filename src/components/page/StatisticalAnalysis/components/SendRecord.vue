<template>
  <div>
    <div class="passageWay-title">
      <div style="display: flex; align-items: center; width: 650px">
        <span style="
            display: inline-block;
            width: 68px;
            text-align: right;
            padding-right: 10px;
          ">时间</span>
        <el-radio-group v-model="flag" size="default" text-color="#fff" fill="#16a589"
          @change="handleChangeTimeOptions">
          <el-radio-button value="1">今天</el-radio-button>
          <el-radio-button value="2">近一个月</el-radio-button>
        </el-radio-group>
        <!-- <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin> -->
        <el-date-picker v-model="datePluginValue" type="datetimerange" format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss" range-separator="至" @change="timeClick" :picker-options="pickerOptions"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </div>
      <!-- 查询框开始 -->
      <el-form :inline="true" :model="form" label-width="100px" class="demo-form-inline" ref="sendjl">
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form.clientName" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="通道ID" prop="channelId">
          <el-input v-model="form.channelId" class="input-w" onInput="value=value.replace(/[^\d]/g,'')"
            maxlength="10"></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form.mobile" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="省份选择" prop="provincial">
          <el-select v-model="form.provincial" placeholder="请选择" clearable filterable class="input-w">
            <el-option v-for="(item, index) in ProvincesCities" :key="index" :label="item.provincial"
              :value="item.provincial"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运营商" prop="operator">
          <el-select v-model="form.operator" placeholder="请选择" clearable filterable class="input-w">
            <el-option label="移动" value="1"></el-option>
            <el-option label="联通" value="2"></el-option>
            <el-option label="电信" value="3"></el-option>
            <el-option label="未知" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信签名" prop="signature">
          <el-input v-model="form.signature" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="短信内容" prop="content">
          <el-input v-model="form.content" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="msgId" prop="msgid">
          <el-input v-model="form.msgid" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="短信类型" prop="smsType">
          <el-select v-model="form.smsType" class="input-w">
            <el-option label="全部" value=""></el-option>
            <el-option label="验证码" value="1"></el-option>
            <el-option label="通知" value="2"></el-option>
            <el-option label="营销推广" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发送类型" prop="sendType">
          <el-select v-model="form.sendType" class="input-w">
            <el-option label="首次发送" value="2"></el-option>
            <el-option label="自动补发" value="1"></el-option>
            <el-option label="手动补发" value="3"></el-option>
            <el-option label="携号转网" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发送状态" prop="smsStatus">
          <el-select v-model="form.smsStatus" class="input-w">
            <el-option label="全部" value=""></el-option>
            <el-option label="成功" value="1"></el-option>
            <el-option label="失败" value="2"></el-option>
            <el-option label="待返回" value="3"></el-option>
            <!-- <el-option label="补发待返回" value="4"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="模板ID" prop="temId">
          <el-input v-model="form.temId" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="失败代码" prop="originalCode">
          <el-input v-model="form.originalCode" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="IP" prop="ip">
          <el-input v-model="form.ip" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-input v-model="form.source" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="管理商名称" prop="parentUsername">
          <el-input v-model="form.parentUsername" class="input-w"></el-input>
        </el-form-item>
        <!-- <el-form-item>
          
        </el-form-item> -->
      </el-form>
      <div class="boderbottom">
        <el-button type="primary" plain @click.native.prevent="Query()" @keyup.enter.native="Query()">查询</el-button>
        <el-button type="primary" plain @click="reset('sendjl')">重置</el-button>
        <el-button type="primary" v-if="selectId.length != 0" @click="handeExtract()">提取模板</el-button>
        <!-- <el-button type="primary" plain  @click="export1()">导出</el-button> -->
      </div>
      <!-- 查询框结束 -->
      <div class="passage-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          @selection-change="handelSelection"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons> </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="statisticalAnalysisSendRecord" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px" :data="tableDataObj.tableData" @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">
          <vxe-column fixed="left" type="checkbox" width="50"></vxe-column>
          <vxe-column fixed="left" field="用户名称" title="用户名称" width="120px">
            <template #default="scope">
              <!-- <el-popover
                                placement="top-start"
                                width="700px"
                                trigger="hover"
                                @show='userList(scope.row,scope.$index)'>
                                <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.clientName"/>
                                <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                 {{ scope.row.clientName}}
                                </span>
                            </el-popover> -->
              <div style="color: #16a589; cursor: pointer" @click="rouTz(scope.row)">
                {{ scope.row.clientName || '-' }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="msgId" title="msgId" width="225px">
            <template #default="scope">
              <div style="display: flex; flex-wrap: wrap; flex-shrink: 0">
                <div style="
                    color: #16a589;
                    cursor: pointer;
                    width: auto;
                    max-width: 100%;
                  " @click="tableContent(scope.row.smsInfoId)">
                  <Tooltip v-if="scope.row.msgid" :content="scope.row.msgid" className="wrapper-text" effect="light">
                  </Tooltip>
                  <!-- <span>{{ scope.row.msgid }}</span> -->
                </div>
                <div>
                  <!-- <i
                    style="
                      color: #409eff;
                      cursor: pointer;
                      margin: 4px;
                      font-size: 14px;
                    "
                    class="el-icon-document-copy"
                    @click="handleCopy(scope.row.msgid, $event)"
                  ></i> -->
                  <CopyTemp style="margin-left: 4px" :content="scope.row.msgid" />
                  <el-tooltip v-if="scope.row.hasAutoReissue" class="item" effect="dark" content="自动补发" placement="top">
                    <i v-if="scope.row.hasAutoReissue" style="
                        color: #67c23a;
                        cursor: pointer;
                        font-size: 16px;
                        margin-left: 4px;
                      " class="iconfont icon-a"></i>
                  </el-tooltip>
                  <el-tooltip v-if="scope.row.hasPersonReissue" class="item" effect="dark" content="手动补发"
                    placement="top">
                    <i v-if="scope.row.hasPersonReissue" style="
                        color: #e6a23c;
                        cursor: pointer;
                        font-size: 16px;
                        margin-left: 4px;
                      " class="iconfont icon-M"></i>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="120px">
            <template #default="{ row, rowIndex }">
              <!-- <div style="color: #16a589; cursor: pointer" @click="tableContent(row.smsInfoId)">
                <span>{{ row.mobile }}</span>
              </div> -->
              <div v-if="rowIndex == pindex" style="cursor: pointer">
                {{ row.mobile || '-' }}
              </div>
              <div v-else style="cursor: pointer; color: #16a589" @click="phoneClickTable(rowIndex, row)">
                {{ row.maskMobile || '-' }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="短信内容" title="短信内容" width="500px">
            <template #default="{ row, rowIndex }">
              <el-tooltip effect="dark" placement="top-start">
                <template #content> {{ row.content.length }}个字符 </template>
                <div v-if="roleId == 3 || roleId == 6">
                  <div >
                    <span v-if="rowIndex == smsIndex">
                      {{ row.content || '-' }}
                    </span>
                    <span v-else style="cursor: pointer; color: #16a589" @click="smsContent(row.smsInfoId, rowIndex)">
                      {{ row.maskContent || '-' }}
                    </span>
                    
                  </div>
                  <!-- <div v-else>
                    <span class="span">{{ row.content }}</span>
                  </div> -->
                </div>
                <div v-else>
                  <div v-if="row.smsType != 3">
                    <span class="span">{{ row.content || '-' }}</span>
                  </div>
                  <div v-else>
                    <span v-if="rowIndex == smsIndex">
                      {{ row.content || '-' }}
                    </span>
                    <span v-else style="cursor: pointer; color: #16a589" @click="smsContent(row.smsInfoId, rowIndex)">
                      {{ row.maskContent || '-' }}
                    </span>
                  </div>
                </div>
              </el-tooltip>
              <!-- <Tooltip v-if="scope.row.content" :content="scope.row.content" className="multiline-text" :count="scope.row.content.length" widthpx="400px" effect="light">
              </Tooltip> -->
            </template>
          </vxe-column>
          <vxe-column field="计费数" title="计费数" width="80px">
            <template #default="scope">
              <div>
                {{ scope.row.chargeNum || '-' }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="提交时间" title="提交时间" width="100px">
            <template #default="scope">
              <p class="wrapper-text" v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
              </p>
              <p class="wrapper-text" v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{h}:{i}:{s}") }}
              </p>
              <span v-else-if="!scope.row.createTime">-</span>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="100px">
            <template #default="scope">
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, "{y}-{m}-{d}") }}
              </p>
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, "{h}:{i}:{s}") }}
              </p>
              <span v-else-if="!scope.row.sendTime">-</span>
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间" width="100px">
            <template #default="scope">
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, "{y}-{m}-{d}") }}
              </p>
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, "{h}:{i}:{s}") }}
              </p>
              <span v-else-if="!scope.row.reportTime">-</span>
            </template>
          </vxe-column>
          <vxe-column field="发送状态" title="发送状态" width="85px">
            <template #default="scope">
              <el-tag :disable-transitions="true" v-if="scope.row.smsStatus == 1" type="success" effect="dark">
                成功
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.smsStatus == 2" type="danger" effect="dark">
                失败
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.smsStatus == 3" type="info" effect="dark">
                待返回
              </el-tag>
              <!-- <span v-if="scope.row.smsStatus == 1">成功</span>
              <span v-else-if="scope.row.smsStatus == 2">失败</span>
              <span v-else-if="scope.row.smsStatus == 3">待返回</span> -->
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="140">
            <template #default="scope">
              <!-- <div v-if="scope.row.originalCode">{{ removeContentInParentheses(scope.row.originalCode) }}</div> -->
              <Tooltip v-if="scope.row.originalCode" :content="scope.row.originalCode" className="wrapper-text"
                effect="light">
              </Tooltip>
              <!-- <div class="wrapper-text">{{ extractContentInParentheses(scope.row.originalCode) }}</div> -->
              <!-- <div v-if="scope.row.originalCode && extractContentInParentheses(scope.row.originalCode)">
                <el-tooltip effect="dark" placement="top">
                  <div slot="content">{{ extractContentInParentheses(scope.row.originalCode) }}</div>
                  <div class="wrapper-text">({{
                    extractContentInParentheses(scope.row.originalCode)
                  }})</div>

                </el-tooltip>
              </div> -->
            </template>
          </vxe-column>
          <vxe-column field="错误码类别" title="错误码类别" width="140">
            <template #default="scope">
              <!-- <div v-if="scope.row.originalCodeGroup">{{ removeContentInParentheses(scope.row.originalCodeGroup) }}</div>
              <div v-if="scope.row.originalCodeGroup && extractContentInParentheses(scope.row.originalCodeGroup)">
                {{ extractContentInParentheses(scope.row.originalCodeGroup) }}
              </div> -->
              <Tooltip v-if="scope.row.originalCodeGroup" :content="scope.row.originalCodeGroup"
                className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="发送通道号" title="发送通道号" width="85px">
            <template #default="scope">
              <div @click="save(scope.row.channelId)" style="color: rgb(22, 165, 137); cursor: pointer">
                {{ scope.row.channelId ||'-' }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="模版ID" title="模版ID" width="120px">
          <template #default="{ row, rowIndex }">
            {{row.temId || '-'}}
          </template>
        </vxe-column>
          <vxe-column field="归属地" title="归属地" width="100px">
            <template #default="scope">
              <div>
                <span>{{
                  (scope.row.provincial || "未知") +
                  "/" +
                  (scope.row.city || "未知")
                }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="运营商" title="运营商" width="100px">
            <template #default="scope">
              <div v-if="scope.row.operator == 1" style="display: flex; align-items: center">
                <i class="iconfont icon-yidong" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div v-else-if="scope.row.operator == 2" style="display: flex; align-items: center">
                <i class="iconfont icon-liantong" style="font-size: 16px; color: #f56c6c"></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div v-else-if="scope.row.operator == 3" style="display: flex; align-items: center">
                <i class="iconfont icon-dianxin" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">电信</span>
              </div>
              <div v-else>未知</div>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源" width="100px">
            <template #default="scope">
              <Tooltip v-if="scope.row.source" :content="scope.row.source" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="SP码号" title="SP码号" width="100">
            <template #default="scope">
              <div>
                <span>{{ scope.row.spPort || '-' }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="扩展号" title="扩展号" width="100px">
            <template #default="scope">
              <div>
                <span>{{ scope.row.ext ||'-' }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="短信类型" title="短信类型" width="100px">
            <template #default="scope">
              <div>
                <span v-if="scope.row.smsType == 1">验证码</span>
                <span v-if="scope.row.smsType == 2">通知</span>
                <span v-if="scope.row.smsType == 3">营销推广</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="发送类型" title="发送类型" width="100px">
            <template #default="scope">
              <div>
                <span v-if="scope.row.sendType == 1">自动补发</span>
                <span v-if="scope.row.sendType == 2">首次发送</span>
                <span v-if="scope.row.sendType == 3">营手动补发</span>
                <span v-if="scope.row.sendType == 4">携号转网</span>
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column
                            field="" title="补发通道号"
                            width="85px"
                        >
                        <template #default="scope">
                            <span @click="save(scope.row.reissueChannelId)" style="color:rgb(22, 165, 137);cursor: pointer;">{{scope.row.reissueChannelId }}</span>  
                        </template>
                        </vxe-column> -->
          <vxe-column field="提交Ip" title="提交Ip" width="110px">
            <template #default="scope">
              <Tooltip v-if="scope.row.ip" :content="scope.row.ip" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="成功计费数" title="成功计费数" width="100px">
            <template #default="scope">
              <div>
                <span>{{ scope.row.chargeSuccessNum || '-' }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column fixed="right" field="操作" title="操作" width="125">
            <template #default="scope">
              <!-- <el-tooltip effect="dark" content="号码诊断" placement="top">
                <el-button @click="handleClick(scope.row)" type="text" size="default">
                  <i class="iconfont icon-zonghezhenduan" style="font-size: 22px;"></i>
                </el-button>
              </el-tooltip> -->
              <div>
                <div>
                  <el-tooltip effect="dark" content="实名明细" placement="top">
                    <el-button style="margin-left: 10px; color: #409eff" @click="handelCheck(scope.row)" link
                      size="default">
                      <el-icon style="font-size: 18px">
                        <View />
                      </el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="加白" placement="top">
                    <el-button v-if="scope.row.sendType == 2" style="margin-left: 10px; color: #409eff"
                      @click="handlewhite(scope.row)" link size="default">
                      <i class="iconfont icon-jiarubaimingdan" style="font-size: 18px"></i>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip effect="dark" content="长短信回执" placement="top">
                    <el-button v-show="scope.row.channelId != '0' && scope.row.chargeNum > 1
                      " style="margin-left: 10px; color: #409eff" @click="longsSms(scope.row)" link size="default">
                      <i class="iconfont icon-zhinengjiance1" style="font-size: 18px !important"></i>
                    </el-button>
                  </el-tooltip>
                </div>
                <div>
                  <!-- <el-button
                    type="text"
                    style="margin-left: 10px"
                    @click="handelCheck(scope.row)"
                    ><i class="el-icon-view"></i>&nbsp;实名明细</el-button
                  > -->
                </div>
              </div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <el-col
          :xs="24"
          :sm="24"
          :md="24"
          :lg="24"
          class="page"
          slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right"
        > -->
        <!-- <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="formData.currentPage" :page-size="formData.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
          </el-pagination> -->
        <div class="paginationBox">
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="formData.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="formData.pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
          </el-pagination>
        </div>
        <!-- </el-col> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <!-- 点手机号查询补发 -->
    <el-dialog title="补发详情" v-model="moreInfoDialog" width="1000px" class="pro-style" :close-on-click-modal="false">
      <el-table v-loading="tableDataObj1.loading2" element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
        border :data="tableDataObj1.tableData" style="width: 100%">
        <el-table-column label="发送类型" width="80px">
          <template #default="scope">
            <span v-if="scope.row.sendType == 1">自动补发</span>
            <span v-else-if="scope.row.sendType == 2">首次发送</span>
            <span v-else-if="scope.row.sendType == 3">手动补发</span>
            <span v-else-if="scope.row.sendType == 4">携号转网</span>
          </template>
        </el-table-column>
        <el-table-column prop="msgid" label="msgId" width="180px">
        </el-table-column>
        <el-table-column label="手机号" width="100px">
          <template #default="{ row, rowIndex }">
            <span style="cursor: pointer" @click="phoneClick(rowIndex, row)">{{
              row.mobile
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通道ID" width="70px">
          <template #default="scope">
            <span @click="save(scope.row.channelId)" style="color: rgb(22, 165, 137); cursor: pointer">{{
              scope.row.channelId }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="smsStatusStr" label="状态" width="80px">
        </el-table-column>
        <el-table-column prop="originalCode" label="失败代码" width="150px">
        </el-table-column>
        <el-table-column prop="ext" label="扩展码" width="150px">
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="150px">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="140px">
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 20px">
        <el-button type="primary" @click="moreInfoDialog = false">确定</el-button>
      </div>
    </el-dialog>
    <!-- 相似度 -->
    <SimilarTemlpate :dialogVisibles="dialogVisible" :tableDatas="extractObj.tableData"
      :similarTemplateLists="extractObj.similarTemplateList" @handleClose="handleCloses">
    </SimilarTemlpate>
    <!-- 诊断 -->
    <!-- <el-dialog :title="'号码诊断（' + username + '）'" v-model="dialogVisibleImp" width="30%"
      :before-close="handleCloseImp">
      <el-timeline>
        <el-timeline-item v-for="(activity, index) in activities" :key="index" :color="activity.colour">
          <div style="display: flex">
            <div style="height: 35px">{{ activity.msg }}</div>
            <div style="margin: 0 10px">
              <i class="iconfont icon-tianjiabaimingdan" v-if="activity.event == 'zt_1005_event' ||
                activity.event == 'zt_1006_event' ||
                activity.event == 'zt_1007_event'
                " type="primary" @click="handelBlack(activity.event, false)">加白</i>
              <i class="iconfont icon-tianjiabaimingdan" v-if="activity.event == 'zt_divert_event'
                " type="primary" @click="handelBlack(activity.event, false)">加白解除黑名单</i>

              <el-tooltip class="box-item" effect="dark" content="加白解除黑名单补发" placement="top-start">
                <i class="iconfont icon-bufa" style="font-size: 28px;margin-top: -10px;color: #409EFF;cursor: pointer;"
                  v-if="activity.event == 'zt_divert_event'" @click="handelBlack('zt_divert_event', true)"></i>
              </el-tooltip>

            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
      <template #footer>
        <el-button @click="dialogVisibleImp = false">关闭</el-button>
      </template>
    </el-dialog> -->
    <!-- 加白 -->
    <el-dialog title="一键加白" v-model="whiteVisible" width="30%" :before-close="handleCloseImp">
      <el-form :model="whiltlist" label-width="100px" ref="whiltlist" style="padding: 0 28px 0 20px">
        <el-form-item label="用户名称" prop="level">
          <div>{{ whiltlist.username }}</div>
        </el-form-item>
        <el-form-item label="加白手机号" prop="level">
          <div>{{ maskMobile }}</div>
        </el-form-item>
        <el-form-item label="白名单等级" prop="level">
          <el-radio-group v-model="whiltlist.level">
            <el-radio value="1">一级白名单</el-radio>
            <el-radio value="2" style="margin-left: 10px">二级白名单</el-radio>
          </el-radio-group>
          <div style="color: #606266; font-size: 12px">
            (白名单有效期默认180天)
          </div>
        </el-form-item>
        <el-form-item label="是否补发" prop="resend">
          <el-radio-group v-model="whiltlist.resend">
            <el-radio :value="true">是</el-radio>
            <el-radio :value="false" style="margin-left: 10px">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="whiteVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('whiltlist')">确定</el-button>
      </template>
    </el-dialog>

    <!-- 查看长短信回执 -->
    <el-dialog title="补发详情" v-model="longsSmsVisible" width="1000px" class="pro-style" :close-on-click-modal="false">
      <el-table v-loading="longsSmsData.loading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable" border :data="longsSmsData.tableData"
        style="width: 100%">
        <el-table-column prop="username" label="用户名称"> </el-table-column>
        <el-table-column label="msgId" width="200px">
          <template #default="scope">
            {{ scope.row.msgId }}
            <i style="
                color: #409eff;
                cursor: pointer;
                margin: 4px;
                font-size: 14px;
              " class="el-icon-document-copy" @click="handleCopy(scope.row.msgId, $event)"></i>
          </template>
        </el-table-column>
        <el-table-column label="手机号" width="100px">
          <template #default="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column prop="num" label="短信条数"> </el-table-column>
        <el-table-column prop="pnum" label="回执序号"> </el-table-column>
        <el-table-column prop="channelId" label="通道号"> </el-table-column>
        <el-table-column prop="time" label="回执时间" width="170px">
        </el-table-column>
        <el-table-column label="发送状态">
          <template #default="scope">
            <el-tag :disable-transitions="true" v-if="scope.row.smsStatus == 1" type="success" effect="dark">
              成功
            </el-tag>
            <el-tag :disable-transitions="true" v-else-if="scope.row.smsStatus == 2" type="danger" effect="dark">
              失败
            </el-tag>
            <el-tag :disable-transitions="true" v-else-if="scope.row.smsStatus == 3" type="info" effect="dark">
              待返回
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="错误码" width="170px">
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 20px">
        <el-button type="primary" @click="longsSmsVisible = false">确定</el-button>
      </div>
    </el-dialog>
    <!-- 实名明细 -->
    <!-- <el-dialog
      title="短信实名明细"
      v-model="autonymVisible"
      width="700px"
    >
      <div class="table-box">
        <el-tabs v-model="operator" type="card" @tab-click="handleClick">
          <el-tab-pane name="1">
            <span slot="label"
              ><i class="iconfont icon-yidong" style="color: #409eff"></i>
              移动</span
            >
          </el-tab-pane>
          <el-tab-pane name="2">
            <span slot="label"
              ><i class="iconfont icon-liantong" style="color: #f56c6c"></i>
              联通</span
            >
          </el-tab-pane>
          <el-tab-pane label="电信" name="3">
            <span slot="label"
              ><i class="iconfont icon-dianxin" style="color: #0c36f2"></i>
              电信</span
            >
          </el-tab-pane>
        </el-tabs>
      </div>

      <el-form
        label-width="80px"
        :inline="true"
        :model="checkForm"
        class="demo-form-inline"
        ref="checkForm"
      >
        <el-form-item label="通道号" prop="channelCode">
          <el-input
            v-model="checkForm.channelCode"
            placeholder
            class="input-time"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain style @click="searchAutonym"
            >查询</el-button
          >
          <el-button
            type="primary"
            plain
            style
            @click="resetAutonym('checkForm')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="autonymData"
        style="width: 100%"
        border
        max-height="400px"
      >
      <el-table-column label="产品名称" prop="">
        <template #default="scope">
            <span>短信</span>
        </template>
      </el-table-column>
        <el-table-column label="通道号" prop="channelCode"></el-table-column>
        <el-table-column label="子端口">
          <template #default="scope">
            <span> {{ scope.row.spCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="扩展号">
          <template #default="scope">
            <span> {{ scope.row.ext }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用状态">
          <template #default="scope">
            <el-tag
:disable-transitions="true" type="success" v-if="scope.row.status == 1">可用</el-tag>
            <el-tag
:disable-transitions="true" type="danger" v-else-if="scope.row.status == 2"
              >未生成</el-tag
            >
            <el-tag
:disable-transitions="true" type="warning" v-else-if="scope.row.status == 0"
              >实名中</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <el-button @click="autonymVisible = false">取 消</el-button>
      </template>
    </el-dialog> -->
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
    <SignatureView v-if="autonymVisible" ref="SignatureRef" :signatureData="signatureData"
      @handleClose="closeSignature">
    </SignatureView>
  </div>
</template>
<script>
import ChannelView from "@/components/publicComponents/ChannelView.vue";
import DatePlugin from "@/components/publicComponents/DatePlugin.vue"; //时间
import TableTem from "@/components/publicComponents/TableTem.vue"; //列表
import UserLists from "@/components/publicComponents/userList.vue";
import SimilarTemlpate from "@/components/publicComponents/similarTemlpate.vue"; //相似度
import Tooltip from "@/components/publicComponents/tooltip.vue";
import SignatureView from "@/components/publicComponents/signatureDetail.vue";
import moment from "moment";
import clip from "../../../../utils/clipboard";
import common from "../../../../assets/js/common";
import CopyTemp from "@/components/publicComponents/CopyTemp.vue";
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  name: "SendRecord",
  components: {
    DatePlugin,
    TableTem,
    ChannelView,
    UserLists,
    SimilarTemlpate,
    Tooltip,
    SignatureView,
    CopyTemp,
  },
  data() {
    return {
      customConfig: {
        storage: true,
      },
      isFirstEnter: false,
      userFlag: false,
      isExpand: true, // 默认为展开状态
      isTimeOptionsChanging: false, // 标记是否正在切换时间选项
      dialogVisibleImp: false,
      pindex: -1,
      smsIndex: -1,
      nameover: "",
      ChannelData: "", //传递通道值
      dialogStatus: "", //新增编辑标题
      selectId: [], //列表id
      ProvincesCities: [], //省份
      idStr: "",
      moreInfoDialog: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: "",
      },
      flag: "1", //选择那一天
      Times: {
        beginTime: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      },
      datePluginValue: [
        moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        // onPick: ({ maxDate, minDate }) => {
        //     this.pickerMinDate = minDate.getTime();
        //     if (maxDate) {
        //         this.pickerMinDate = ''
        //     }
        // },
        // disabledDate: (time) => {
        //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
        //         return false
        //     }
        //     if (this.pickerMinDate !=='') {
        //         const day7 = 6 * 24 * 3600 * 1000
        //         let maxTime = this.pickerMinDate + day7
        //         if (maxTime >  Date.now()) {
        //             maxTime =  Date.now()
        //         }
        //         const minTime = this.pickerMinDate - day7
        //         return time.getTime() < minTime || time.getTime() > maxTime
        //     }
        //     return time.getTime() > Date.now();
        // }
      },
      //    datePluginValue:''
      // },
      //发送查询的值
      form: {
        mobile: "",
        channelId: "",
        clientName: "",
        signature: "",
        smsStatus: "",
        content: "",
        msgid: "",
        temId: "",
        parentUsername: "",
        smsType: "",
        sendType: "2",
        ip: "",
        source: "",
        provincial: "",
        originalCode: "",
        operator: "",
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: "",
        channelId: "",
        clientName: "",
        signature: "",
        smsStatus: "",
        content: "",
        msgid: "",
        smsType: "",
        sendType: "2",
        temId: "",
        parentUsername: "",
        ip: "",
        source: "",
        provincial: "",
        originalCode: "",
        operator: "",
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      activeName2: "first", //发送和回复选项卡
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        totalRow: 0,
        tableData: [],
      },
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      // 长短信回执
      longsSmsData: {
        tableData: [],
        loading: false,
      },
      //相似度
      contentFlag: false,
      dialogVisible: false,
      contentList: [],
      extractObj: {
        loading2: false,
        tableData: [],
        similarTemplateList: [],
      },
      whiteVisible: false,
      longsSmsVisible: false,
      maskMobile: "",
      whiltlist: {
        cipherMobile: "",
        keyId: "",
        level: "1",
        resend: false,
        smsInfoId: "",
        username: "",
      },
      copyData: [],
      // path: " ws://opt.zthysms.cn/websocket/diagnosis/",
      // socket: "",
      // token: "",
      // ws: "",
      // timeout: 50000, //50秒一次心跳
      // timeoutObj: null, //心跳心跳倒计时
      // serverTimeoutObj: null, //心跳倒计时
      // timeoutnum: null, //断开 重连倒计时
      activities: [],
      username: "",
      smsInfoId: "",
      autonymVisible: false,
      // signatoryId: "",
      // checkForm: {
      //   channelCode: "",
      //   userId: "",
      // },
      // autonymData: [],
      // operator: "1",
      signatureData: {
        signatoryId: "",
        userId: "",
        userName: "",
        productId: "1",
      },
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //创建socket链接
    // init: function () {
    //   let that = this;
    //   if (typeof WebSocket === "undefined") {
    //     this.$notify({
    //       title: "您的浏览器不支持socket",
    //     });
    //     // alert("您的浏览器不支持socket")
    //   } else {
    //     // // 实例化socket
    //     that.ws = new WebSocket(this.path + this.token);
    //     that.global.setWs(that.ws);
    //     that.ws.onopen = function () {
    //       console.log("连接成功");
    //       that.start();
    //     };
    //     // 监听socket消息
    //     that.ws.onmessage = (msg) => {
    //       console.log(msg,'msg');

    //       if(msg.data != '连接成功！'){

    //         let data = JSON.parse(msg.data)
    //         // console.log(Object.prototype.toString.call(data),'data');
    //         that.activities.push(data)
    //       }
    //       that.resetStart();
    //     };
    //     //连接错误
    //     that.ws.onerror = function () {
    //       console.log("WebSocket连接发生错误");
    //       //重连
    //       if (that.title == "token为空") {
    //         that.global.ws.close();
    //       } else {
    //         that.reconnect();
    //       }
    //     };
    //     that.ws.onclose = function () {
    //       // 关闭 websocket
    //       console.log("连接已关闭....1111");
    //       // that.global.ws.close();
    //       // 断线重新连接
    //       setTimeout(() => {
    //         that.init();
    //       }, 2000);
    //     };
    //     // console.log(that.global.setWs);
    //   }
    // },
    // //重新连接
    // reconnect() {
    //   let that = this;
    //   if (that.global.ws && that.global.ws.readyState == 1) {
    //     clearInterval(that.timeoutnum);
    //     that.timeoutnum = null;
    //     that.timeNum = 0;
    //     return;
    //   }
    //   if (!that.timeoutnum) {
    //     that.timeoutnum = setInterval(function () {
    //       if (that.global.ws && that.global.ws.readyState == 1) {
    //         clearInterval(that.timeoutnum);
    //         that.timeoutnum = null;
    //         that.timeNum = 0;
    //         return;
    //       }
    //       //新连接
    //       that.init();
    //       that.timeNum++;
    //       if (that.timeNum >= 3) {
    //         clearInterval(that.timeoutnum);
    //         that.timeoutnum = null;
    //         that.timeNum = 0;
    //       }
    //     }, 1000);
    //   }
    // },
    // //重置心跳
    // //清除时间
    // resetStart() {
    //   clearTimeout(this.timeoutObj);
    //   clearTimeout(this.serverTimeoutObj);
    //   //重启心跳
    //   this.start();
    // },
    // //开启心跳
    // start() {
    //   //   console.log('www','ll');
    //   let that = this;
    //   let date = new Date();
    //   that.timeoutObj && clearTimeout(that.timeoutObj);
    //   that.serverTimeoutObj && clearTimeout(that.serverTimeoutObj);
    //   that.timeoutObj = setTimeout(function () {
    //     //这里发送一个心跳，后端收到后，返回一个心跳消息，
    //     if (that.global.ws && that.global.ws.readyState == 1) {
    //       //如果连接正常
    //       //   console.log(that.global.ws.readyState,'lll');

    //       that.global.ws.send(`发送心跳给后端${date}`);
    //     } else {
    //       //否则重连
    //       that.reconnect();
    //     }
    //   }, that.timeout);
    // },
    extractContentInParentheses(text) {
      const regex = /\((.*?)\)/;
      const matches = text.match(regex);
      if (matches && matches.length > 1) {
        return matches[1];
      } else {
        return null;
      }
    },
    removeContentInParentheses(text) {
      const regex = /\((.*?)\)/g;
      const result = text.replace(regex, "");

      return result;
    },
    isAllEqual(array) {
      if (array.length > 0) {
        return !array.some(function (value, index) {
          return value.clientName !== array[0].clientName;
        });
      } else {
        return true;
      }
    },
    handleCopy(name, event) {
      clip(name, event);
    },
    handelSelection(row) {
      //列表复选框的值
      // this.selectId = val.map((item) => {
      //   return item.smsReplyId;
      // });
      // this.contentFlag = this.isAllEqual(val);
      // if (this.contentFlag) {
      //   this.contentList = val.map((item) => {
      //     return {
      //       username: item.clientName,
      //       content: item.content,
      //     };
      //   });
      // }

      if (row.records.length > 0) {
        let arr = [];
        row.records.forEach((item) => {
          arr.push(item.smsReplyId);
        });
        this.selectId = arr;
        this.contentFlag = this.isAllEqual(row.records);
        if (this.contentFlag) {
          this.contentList = row.records.map((item) => {
            return {
              username: item.clientName,
              content: item.content,
            };
          });
        }
      } else {
        this.selectId = [];
      }
    },
    getProvincial() {
      // 获取省市
      window.api.get(
        window.path.omcs + "operatingchannelprovincial/list",
        {},
        (res) => {
          this.ProvincesCities = res.data;
        }
      );
    },
    //提取相似度
    handeExtract() {
      if (this.contentFlag) {
        window.api.post(
          window.path.omcs + "operatingclientsmssimilar/content/percent",
          this.contentList,
          (res) => {
            if (res.code == 200) {
              this.dialogVisible = true;
              this.extractObj.tableData = res.data.similarList;
              this.extractObj.similarTemplateList =
                res.data.similarTemplateList;
            }
          }
        );
      } else {
        this.$message({
          message: "请选择同一用户名！",
          type: "warning",
        });
      }
    },
    handleCloses(val) {
      this.dialogVisible = val;
    },
    handleCloseImp() {
      this.dialogVisibleImp = false;
      this.whiteVisible = false;
    },
    // 点击手机号
    tableContent(smsInfoId) {
      // console.log(11111);
      window.api.get(
        window.path.omcs + "smsMessage/details?smsInfoId=" + smsInfoId,
        {},
        (res) => {
          if (res.code == 200) {
            this.tableDataObj1.loading2 = false;
            this.tableDataObj1.tableData = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
      this.moreInfoDialog = true;
    },
    //获取 发送记录数据
    getTableDtate() {
      this.pindex = -1;
      this.smsIndex = -1;
      console.log();
      let aa = {};
      Object.assign(aa, this.formData);
      aa.flag = this.flag;
      aa.sendBeginTime = this.Times.beginTime;
      aa.sendEndTime = this.Times.endTime;
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingStatisticalAnalysis/sendSmsInfo/page",
        aa,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false;
            this.tableDataObj.tableData = res.data.records;
            this.tableDataObj.tableData.forEach((item) => {
              item.maskContent = item.content;
            });
            let data = JSON.stringify(res.data.records);
            this.copyData = JSON.parse(data);
            this.tableDataObj.totalRow = res.data.total;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    //导出 （发送记录）
    export1() {
      let aa = {};
      Object.assign(aa, this.formData);
      aa.flag = this.flag;
      aa.sendBeginTime = this.Times.beginTime;
      aa.sendEndTime = this.Times.endTime;
      aa.isDownload = 1;
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      } else {
        this.$File.export(
          window.path.omcs + "operatingStatisticalAnalysis/sendSmsInfo/page",
          aa,
          "发送记录报表.xlsx"
        );
      }
    },
    timeClick(val) {
      // console.log(11111);
      if (val) {
        this.Times.beginTime = this.moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.Times.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      } else {
        this.Times.beginTime = "";
        this.Times.endTime = "";
      }
      this.flag = "3";
      // if(val){
      //     // console.log(val);

      //     this.flag='5'
      // }else{
      //     console.log(2);
      //     this.flag='1'
      //     this.Times.beginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
      //     this.Times.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      // }
    },
    phoneClickTable(index, row) {
      this.pindex = index;
      console.log(this.copyData, "this.copyData");
      window.api.post(
        window.path.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    phoneClick(index, row) {
      window.api.post(
        window.path.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj1.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    smsContent(smsInfoId, index) {
      this.smsIndex = index;
      common.fetchSmsMessage(smsInfoId).then((res) => {
        if (res.code == 200) {
          this.tableDataObj.tableData[index].content = res.data;
        } else {
          this.$message({
            message: res.msg,
            type: "error",
          });
        }
      });
    },
    handleChangeTimeOptions: function (val) {
      // 设置标志位，防止Times监听器重复调用接口
      this.isTimeOptionsChanging = true;

      // 保存当前的查询条件
      const hasSearchConditions = this.hasSearchConditions();

      if (val === "1") {
        // 切换到今天
        this.Times.beginTime = moment()
          .startOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        this.Times.endTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
        this.datePluginValue = [
          moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        ];
        this.flag = "1";

        // 今天默认查询
        this.$nextTick(() => {
          Object.assign(this.formData, this.form);
          this.getTableDtate();
          this.isTimeOptionsChanging = false;
        });
      } else if (val === "2") {
        // 切换到近一个月
        this.Times.beginTime = moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD 00:00:00");
        this.Times.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
        this.datePluginValue = [
          moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD HH:mm:ss"),
        ];
        this.flag = "2";

        // 近一个月只有在有查询条件时才自动查询
        this.$nextTick(() => {
          // 同步表单数据，但不一定调用接口
          Object.assign(this.formData, this.form);

          if (hasSearchConditions) {
            // 有查询条件，调用接口
            this.getTableDtate();
          }
          // 无论是否有条件，都要重置标志位
          this.isTimeOptionsChanging = false;
        });
      }
    },

    // 检查是否有查询条件
    hasSearchConditions() {
      const conditions = [
        'clientName', 'channelId', 'mobile', 'provincial', 'operator',
        'signature', 'content', 'msgid', 'smsStatus',
        'temId', 'originalCode', 'ip', 'source', 'parentUsername'
      ];

      return conditions.some(key => {
        const value = this.form[key];
        return value && value.toString().trim() !== '';
      }) || (this.form.sendType && this.form.sendType !== '2'); // sendType默认值是'2'，如果不是默认值说明有条件
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val;
      this.$refs.ChannelRef.ChannelClick();
    },
    //发送查询
    Query() {
      if (this.tableDataObj.loading2) return; //防止重复请求
      Object.assign(this.formData, this.form);
      this.getTableDtate();
    },
    //发送重置
    reset() {
      if (this.flag == "1") {
        // console.log(11);
        this.$refs.sendjl.resetFields();
        this.Times.beginTime = moment()
          .startOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        this.Times.endTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
        this.datePluginValue = [
          moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        ];
        Object.assign(this.formData, this.form);
        this.getTableDtate();
      } else {
        this.flag = "1";
        this.$refs.sendjl.resetFields();
        Object.assign(this.formData, this.form);
      }
      // this.flag='1'
      // this.$refs.sendjl.resetFields();
      // Object.assign(this.formData,this.form);
    },
    rouTz(val) {
      this.$router.push({ path: "/UserDetail", query: { id: val.userId } });
    },
    userList(row, index) {
      this.userFlag = true;
      this.nameover = index;
    },
    handleSizeChange(size) {
      this.smsIndex = -1;
      this.pindex = -1;
      this.formData.pageSize = size;
      // Object.assign(this.form, this.formData);
    },
    handleCurrentChange: function (currentPage) {
      this.smsIndex = -1;
      this.pindex = -1;
      // console.log(currentPage, 'currentPage');
      this.formData.currentPage = currentPage;
      // Object.assign(this.form, this.formData);
    },
    //号码诊断
    handleClick(row) {
      let that = this;
      this.smsInfoId = row.smsInfoId;
      this.username = row.clientName;
      let obj = {
        clientName: row.clientName,
        cipherMobile: row.cipherMobile,
        keyId: row.keyId,
        smsType: row.smsType,
        provincial: row.provincial,
        signature: row.signature,
        msgid: row.msgid,
        batchNo: row.batchNo,
        createTime: row.createTime,
      };
      window.api.post(
        window.path.omcs + "smsMessage/diagnosis/check",
        obj,
        (res) => {
          that.dialogVisibleImp = true;
          if (res.code == 200) {
            this.activities = res.data;
            this.activities[0].msg =
              this.activities[0].msg + `(${row.maskMobile})`;
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
          // that.global.ws.onmessage = function (msg) {
          //   console.log(msg, "msg");
          // };
          // if (res.code == 200) {
          //   console.log(res,'res');
          // }
        }
      );
    },
    //加白
    handlewhite(row) {
      this.maskMobile = row.maskMobile;
      this.username = row.clientName;
      this.whiltlist.username = row.clientName;
      this.whiltlist.cipherMobile = row.cipherMobile;
      this.whiltlist.smsInfoId = row.smsInfoId;
      this.whiltlist.keyId = row.keyId;
      this.whiteVisible = true;
    },
    // 查看长短信回执
    longsSms(item) {
      this.longsSmsData.tableData = [];
      this.longsSmsData.loading = true;
      window.api.post(
        window.path.omcs + "consumersms/receipt/page",
        { smsInfoId: item.smsInfoId },
        (res) => {
          if (res.code == 200) {
            if (res.data.records) {
              this.longsSmsData.tableData = res.data.records;
            }
            this.longsSmsData.loading = false;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
      this.longsSmsVisible = true;
    },
    submitForm() {
      let data = {
        smsInfoId: this.whiltlist.smsInfoId,
        resend: this.whiltlist.resend,
        event: "zt_divert_event",
        level: this.whiltlist.level,
      };
      this.$confirms.confirmation(
        "post",
        "确认执行此操作吗？",
        window.path.omcs + "smsMessage/diagnosis",
        data,
        (res) => {
          if (res.code == 200) {
            this.whiteVisible = false;
            this.getTableDtate();
          }

          // this.gettableLIst();
        }
      );
    },
    handelCheck(row) {
      // this.checkForm.userId = row.userId;
      // this.signatoryId = row.signature;
      this.signatureData.signatoryId = row.signature;
      this.signatureData.userId = row.userId;
      this.signatureData.userName = row.clientName;
      this.autonymVisible = true;
      // this.handleClick();
    },
    closeSignature(data) {
      this.autonymVisible = data;
    },
    // searchAutonym() {
    //   this.handleClick();
    // },
    // resetAutonym() {
    //   this.checkForm.channelCode = "";
    //   this.handleClick();
    // },
    // handleClick() {
    //   window.api.post(
    //     window.path.omcs + "consumerSignature/queryRealNameInfo",
    //     {
    //       userId: this.checkForm.userId,
    //       signature: this.signatoryId,
    //       operator: this.operator,
    //       channelCode: this.checkForm.channelCode,
    //       productId: "1",
    //     },
    //     (res) => {
    //       if (res.code == 200) {
    //         this.autonymVisible = true;
    //         this.autonymData = res.data;
    //       } else {
    //         this.autonymData = [];
    //         this.$message({
    //           message: res.msg,
    //           type: "warning",
    //         });
    //       }
    //     }
    //   );
    // },
    // handelBlack(event, resend) {
    //   let obj = {
    //     event,
    //     resend,
    //     smsInfoId: this.smsInfoId,
    //   };
    //   let tit = "";
    //   if (resend) {
    //     tit = "确定加白解除黑名单补发？";
    //   } else {
    //     tit = "确定加白解除黑名单？";
    //   }
    //   this.$confirms.confirmation(
    //     "post",
    //     tit,
    //     window.path.omcs + "smsMessage/diagnosis",
    //     obj,
    //     (res) => {
    //       console.log(res, "res");
    //     }
    //   );
    // },
  },
  // created(){
  //     // 注册回车事件
  //     var _self = this;
  //     document.onkeydown = function(e){
  //         if(window.event == undefined){
  //             var key = e.keyCode;
  //         }else{
  //             var key = window.event.keyCode;
  //         }
  //         if(key == 13){
  //             _self.Query();
  //         }
  //     }
  //     this.getTableDtate();
  // },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.token = this.$common.getCookie("ZTADMIN_TOKEN");
        this.getTableDtate();
        this.getProvincial();
        // this.init();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  created() {
    // 注册回车事件
    var _self = this;
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode;
      } else {
        var key = window.event.keyCode;
      }
      if (key == 13) {
        _self.Query();
      }
    };
    this.isFirstEnter = true;
    this.token = this.$common.getCookie("ZTADMIN_TOKEN");
    this.getTableDtate();
    this.getProvincial();
    // this.init();
    // this.$nextTick(() => {

    // });
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  // deactivated() {
  //   let that = this;
  //   that.global.ws.close();
  // },
  watch: {
    //监听时间范围
    flag: function (val) {
      // console.log(val,'val');
      // if(val != '5'){
      //     this.Times.beginTime = '';
      //     this.Times.endTime = '';
      //     this.datePluginValue='';
      // }
      if (val == "1") {
        this.Times.beginTime = moment()
          .startOf("day")
          .format("YYYY-MM-DD HH:mm:ss");
        this.Times.endTime = moment(new Date()).format("YYYY-MM-DD HH:mm:ss");
        this.datePluginValue = [
          moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          moment(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        ];
      } else if (val == "2") {
        this.Times.beginTime = moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD 00:00:00");
        this.Times.endTime = moment().format("YYYY-MM-DD HH:mm:ss");
        this.datePluginValue = [
          moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD HH:mm:ss"),
        ];
      }
      // this.getTableDtate();
    },
    //监听时间是否改变
    Times: {
      handler(val) {
        // 只有在手动选择时间时才自动查询，避免切换时间选项时重复查询
        if (val && !this.isTimeOptionsChanging) {
          this.getTableDtate();
        }
      },
      deep: true,
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields();
      }
    },
    formData: {
      handler(val) {
        // 避免在切换时间选项时触发查询
        if (!this.isTimeOptionsChanging) {
          // 如果是近一个月且没有查询条件，则不调用接口
          if (this.flag === "2" && !this.hasSearchConditions()) {
            return;
          }
          this.getTableDtate();
        }
      },
      deep: true,
    },
    moreInfoDialog(val) {
      if (val == false) {
        this.tableDataObj1.loading2 = false;
        this.tableDataObj1.tableData = [];
      }
    },
    dialogVisibleImp(val) {
      if (!val) {
        this.activities = [];
      }
    },
    whiteVisible(val) {
      if (!val) {
        (this.whiltlist.username = ""), (this.whiltlist.cipherMobile = "");
        this.whiltlist.smsInfoId = "";
        this.whiltlist.keyId = "";
        this.whiltlist.level = "1";
        this.whiltlist.resend = false;
      }
    },
  },
};
</script>
<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}

.demo-form-inline {
  margin-top: 12px;
}

.passage-table {
  margin-bottom: 40px;
}

.span {
  white-space: pre-wrap;
}
</style>
<style>
.el-textarea__inner {
  height: 100%;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>