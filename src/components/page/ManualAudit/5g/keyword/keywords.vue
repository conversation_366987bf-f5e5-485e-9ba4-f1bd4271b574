<template>
  <div class="container_left">
    <div style="padding: 10px">
      <div>
        <el-form inline ref="form" :model="form">
          <el-form-item label="用户名" prop="username">
            <el-input class="input-w" v-model="form.username"></el-input>
          </el-form-item>
          <el-form-item label="关键词" prop="word">
            <el-input class="input-w" v-model="form.word"></el-input>
          </el-form-item>
          <div style="margin-bottom: 8px">
            <el-button plain type="primary" @click="search">查询</el-button>
            <el-button plain type="primary" @click="Reset('form')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div>
        <!-- <el-table
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableData.data"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="keywords"
          border
          stripe
          show-overflow
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableData.data">

          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>
                {{ scope.row.createdUser }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="关键字" title="关键字">
            <template v-slot="scope">
              <div>
                {{ scope.row.word }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="事件" title="事件">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.eventCode == 'reply'">自动回复</span>
                <span v-else-if="scope.row.eventCode == 'unsubscribe'"
                  >解除黑名单</span
                >
                <span v-else-if="scope.row.eventCode == 'subscribe'"
                  >加入黑名单</span
                >
                <span v-else-if="scope.row.eventCode == 'webhook'">推送</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="模板Id" title="模板Id">
            <template v-slot="scope">
              <div>{{ scope.row.templateId }}</div>
            </template>
          </vxe-column>

          <!-- <vxe-column field="" title="是否关联按钮">
              <template #default="scope">
                <span>{{ scope.row.callbackPhone }}</span>
              </template>
            </vxe-column> -->
          <vxe-column field="更新时间" title="更新时间">
            <template v-slot="scope">
              <!-- <p v-if="scope.row.updateTime">{{ $parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</p>
                                <p v-if="scope.row.updateTime">{{ $parseTime(scope.row.updateTime, '{h}:{i}:{s}') }}</p> -->
              <div>{{
                moment(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注">
            <template v-slot="scope">
              <div>{{ scope.row.remark }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="操作" width="180">
                            <template #default="scope">
                                <el-button type="text" @click="editKeyword(scope.row)"
                                    style="color: #16a589; margin-left: 0px"><i class="el-icon-edit"></i> 编辑</el-button>
                                <el-button @click="delKeyword(scope.row)" type="text" style="color: red; margin-left: 10px"><i
                                        class="el-icon-delete"></i> 删除</el-button>
                            </template>
                        </vxe-column> -->
        </vxe-table>
        <div class="send_foot">
          <div></div>
          <div style="margin: 10px 0">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
export default {
  name: 'reportRecord',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      dialogVisible: false,
      value1: '',
      message: '群发记录',
      type: '',
      detail: {},
      form: {
        word: '',
        username: '',
        pageSize: 10,
        currentPage: 1,
      },
      tabelAlllist: {
        word: '',
        username: '',
        pageSize: 10,
        currentPage: 1,
      },
      tableData: {
        loading2: false,
        data: [],
        total: 0,
      },
      title: '',
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  computed: {
    ...mapState({
      userId: (state) => state.userId,
    }),
  },
  methods: {
    getList() {
      this.tableData.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/keyword/page',
        this.form,
        (res) => {
          this.tableData.loading2 = false
          this.tableData.total = res.data.total
          this.tableData.data = res.data.records
          this.tableData.data.forEach((item) => {
            if (item.rscContent) {
              let str = item.rscContent
              item.xsmstitle = str.substring(0, str.indexOf('：'))
              item.rscContent = str.substring(str.indexOf('：') + 1, str.length)
            }
          })
        }
      )
    },
    search() {
      // Object.assign(this.form,this.tabelAlllist);
      this.getList()
    },
    Reset(formName) {
      this.$refs[formName].resetFields()
      // Object.assign(this.form,this.tabelAlllist);
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSizeChange(size) {
      this.form.pageSize = size
      this.getList()
    },
    handleCurrentChange: function (currentPage) {
      this.form.currentPage = currentPage
      this.getList()
    },
  },
}
</script>

<style scoped>
.send_foot {
  display: flex;
  justify-content: space-between;
}
</style>

<style>
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
