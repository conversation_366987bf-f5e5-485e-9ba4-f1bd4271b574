<template>
  <el-button type="primary" @click="exportTableData">{{ buttonText || '导出' }}</el-button>
</template>

<script>
export default {
  name: 'ExportButton',
  props: {
    // 要导出的表格数据
    tableData: {
      type: Array,
      required: true
    },
    // 使用的模板解析时间函数
    parseTime: {
      type: Function,
      required: true
    },
    // 表头配置，包含title和对应的字段名
    headers: {
      type: Array,
      default: () => []
    },
    // 导出文件名前缀
    fileNamePrefix: {
      type: String,
      default: '数据导出'
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: '导出'
    },
    // 日期字段名列表，这些字段会被格式化
    dateFields: {
      type: Array,
      default: () => []
    },
    // 自定义字段格式化规则
    formatters: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    exportTableData() {
      this.$emit('export-start');
      this.$message.info('正在导出数据，请稍候...');
      
      // 直接使用传入的表格数据
      let dataToExport = [...this.tableData]; // 复制当前表格数据
      
      // 没有数据时提示
      if (dataToExport.length === 0) {
        this.$message.warning('没有数据可导出');
        this.$emit('export-end', false);
        return;
      }
      
      // 构建Excel数据
      const excelData = [];
      
      // 添加表头
      let headers;
      if (this.headers && this.headers.length > 0) {
        // 使用配置的表头
        headers = this.headers.map(h => h.title || h);
        excelData.push(headers);
        
        // 添加数据行 - 使用配置的字段
        dataToExport.forEach(item => {
          const row = this.headers.map(header => {
            const fieldName = header.field || header;
            let value = item[fieldName] !== undefined ? item[fieldName] : '';
            
            // 应用自定义格式化
            if (this.formatters[fieldName]) {
              value = this.formatters[fieldName](value, item);
            } 
            // 日期字段格式化
            else if (this.dateFields.includes(fieldName) && value) {
              value = this.parseTime(value, "{y}-{m}-{d} {h}:{i}:{s}");
            }
            // 默认文本处理 - 清理HTML和特殊字符
            else if (typeof value === 'string') {
              // 清理HTML标签
              value = this.cleanHtmlContent(value);
            }
            
            return value;
          });
          
          excelData.push(row);
        });
      } else {
        // 没有配置表头，导出全部字段
        if (dataToExport.length > 0) {
          headers = Object.keys(dataToExport[0]);
          excelData.push(headers);
          
          dataToExport.forEach(item => {
            const row = headers.map(key => {
              let value = item[key] !== undefined ? item[key] : '';
              
              // 应用自定义格式化
              if (this.formatters[key]) {
                value = this.formatters[key](value, item);
              } 
              // 日期字段格式化
              else if (this.dateFields.includes(key) && value) {
                value = this.parseTime(value, "{y}-{m}-{d} {h}:{i}:{s}");
              }
              // 默认文本处理 - 清理HTML和特殊字符
              else if (typeof value === 'string') {
                // 清理HTML标签
                value = this.cleanHtmlContent(value);
              }
              
              return value;
            });
            
            excelData.push(row);
          });
        }
      }
      
      // 尝试导出Excel
      try {
        // 生成csv格式
        const csvContent = excelData.map(row => 
          row.map(cell => {
            // 处理单元格内容，确保CSV格式正确
            if (cell === null || cell === undefined) return '';
            
            const cellStr = String(cell);
            // 如果内容包含逗号、引号或换行符，需要用引号包裹并转义引号
            if (/[",\n\r]/.test(cellStr)) {
              return '"' + cellStr.replace(/"/g, '""') + '"';
            }
            return cellStr;
          }).join(',')
        ).join('\r\n'); // 使用Windows换行符以保证兼容性
        
        // 创建Blob并触发下载
        const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), csvContent], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const filename = this.fileNamePrefix + '_' + this.formatDate(new Date()) + '.xlsx';
        
        if (navigator.msSaveBlob) {
          // IE浏览器导出
          navigator.msSaveBlob(blob, filename);
        } else {
          // 其他浏览器
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.setAttribute('download', filename);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
        
        this.$message.success('导出成功');
        this.$emit('export-end', true);
      } catch (e) {
        console.error('导出失败', e);
        this.$message.error('导出失败：' + e.message);
        this.$emit('export-end', false);
      }
    },
    
    // 清理HTML内容和特殊字符
    cleanHtmlContent(html) {
      if (!html) return '';
      
      // 将<br>和</p>等标签转换为空格
      let text = html.replace(/<br\s*\/?>/gi, ' ')
                     .replace(/<\/p>/gi, ' ');
                     
      // 移除所有HTML标签
      text = text.replace(/<\/?[^>]+(>|$)/g, '');
      
      // 解码HTML实体
      const textarea = document.createElement('textarea');
      textarea.innerHTML = text;
      text = textarea.value;
      
      // 移除多余空格
      text = text.replace(/\s+/g, ' ').trim();
      
      return text;
    },
    
    // 格式化日期为字符串
    formatDate(date) {
      const y = date.getFullYear();
      const m = (date.getMonth() + 1).toString().padStart(2, '0');
      const d = date.getDate().toString().padStart(2, '0');
      const h = date.getHours().toString().padStart(2, '0');
      const min = date.getMinutes().toString().padStart(2, '0');
      const s = date.getSeconds().toString().padStart(2, '0');
      return `${y}${m}${d}_${h}${min}${s}`;
    },
  }
};
</script>

<style>
</style> 