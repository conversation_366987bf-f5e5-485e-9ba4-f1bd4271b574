<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="主图文" name="first">
        <el-form
          ref="Firstform"
          :model="firstFrom"
          disabled
          label-width="100px"
        >
          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">图片设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="选择图片" prop="imgcontents.sourceId">
                <div
                  class="drawe_item_img"
                  v-if="firstFrom.imgcontents.sourceId == ''"
                >
                  <img
                    src="../../../../../../assets/images/ssobg.png"
                    alt=""
                    class="image_d"
                  />
                  <div class="add_img">
                    <el-icon><el-icon-plus /></el-icon>
                  </div>
                </div>
                <div v-else class="drawe_item_img">
                  <div class="media_icon">
                    <img
                      v-if="firstFrom.imgcontents.srcType == '1'"
                      src="../../../../../../assets/images/photoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="firstFrom.imgcontents.srcType == '2'"
                      src="../../../../../../assets/images/videoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="firstFrom.imgcontents.srcType == '3'"
                      src="../../../../../../assets/images/audioIcon.png"
                      alt=""
                    />
                  </div>
                  <img
                    v-if="firstFrom.imgcontents.srcType == '1'"
                    :src="firstFrom.imgcontents.url"
                    class="image_d"
                  />
                  <img
                    v-if="
                      (firstFrom.imgcontents.srcType == '2' ||
                        firstFrom.imgcontents.srcType == '3') &&
                      firstFrom.imgcontents.thumbnailUrl
                    "
                    :src="firstFrom.imgcontents.thumbnailUrl"
                    alt=""
                    class="image_d"
                  />
                </div>
                <div style="color: #999">图片比例16:9，建议1248x702px</div>
              </el-form-item>
              <el-form-item label="事件类型" prop="imgcontents.actionType">
                <el-select
                  style="width: 250px"
                  v-model="firstFrom.imgcontents.actionType"
                  placeholder="请选择"
                >
                  <el-option label="打开链接" value="OPEN_URL"> </el-option>
                  <el-option label="拨打电话" value="DIAL_PHONE"> </el-option>
                  <el-option label="打开浏览器" value="OPEN_BROWSER">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="firstFrom.imgcontents.actionType == 'OPEN_URL'"
                label="打开链接"
                prop="imgcontents.action.target"
              >
                <a
                  :href="firstFrom.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ firstFrom.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="firstFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="firstFrom.imgcontents.actionType == 'OPEN_BROWSER'"
                label="打开浏览器"
                prop="imgcontents.action.target"
              >
                <a
                  :href="firstFrom.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ firstFrom.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="firstFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="
                  firstFrom.imgcontents.actionType == 'OPEN_URL' ||
                  firstFrom.imgcontents.actionType == 'OPEN_BROWSER'
                "
                label="商户名称"
                prop="imgcontents.action.merchantName"
              >
                <div>{{ firstFrom.imgcontents.action.merchantName }}</div>
                <!-- <el-input
                      style="width: 250px"
                      v-model="firstFrom.imgcontents.action.merchantName"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="firstFrom.imgcontents.actionType == 'DIAL_PHONE'"
                label="拨打电话"
                prop="imgcontents.action.target"
              >
                <div>{{ firstFrom.imgcontents.action.target }}</div>
                <!-- <el-input
                      class="textarea_text"
                      type="textarea"
                      v-model="firstFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>

          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">内容设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="标题名称" prop="titlecontents.content">
                <div>{{ firstFrom.titlecontents.content }}</div>
                <!-- <el-input
                      style="width: 250px"
                      maxlength="30"
                      show-word-limit
                      v-model="firstFrom.titlecontents.content"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane
        v-if="list.number == '2' || list.number == '3' || list.number == '4'"
        label="副图文1"
        name="second"
      >
        <el-form
          ref="secondform"
          :model="secondFrom"
          disabled
          label-width="100px"
        >
          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">图片设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="选择图片" prop="imgcontents.sourceId">
                <div
                  class="drawe_item_img_1"
                  v-if="secondFrom.imgcontents.sourceId == ''"
                >
                  <img
                    src="../../../../../../assets/images/ssobg.png"
                    alt=""
                    class="image_d"
                  />
                  <div class="add_img_1">
                    <el-icon><el-icon-plus /></el-icon>
                  </div>
                </div>
                <div v-else class="drawe_item_img_1">
                  <div class="media_icon">
                    <img
                      v-if="secondFrom.imgcontents.srcType == '1'"
                      src="../../../../../../assets/images/photoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="secondFrom.imgcontents.srcType == '2'"
                      src="../../../../../../assets/images/videoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="secondFrom.imgcontents.srcType == '3'"
                      src="../../../../../../assets/images/audioIcon.png"
                      alt=""
                    />
                  </div>
                  <img
                    v-if="secondFrom.imgcontents.srcType == '1'"
                    :src="secondFrom.imgcontents.url"
                    class="image_d"
                  />
                  <img
                    v-if="
                      (secondFrom.imgcontents.srcType == '2' ||
                        secondFrom.imgcontents.srcType == '3') &&
                      secondFrom.imgcontents.thumbnailUrl
                    "
                    :src="secondFrom.imgcontents.thumbnailUrl"
                    alt=""
                    class="image_d"
                  />
                </div>
                <div style="color: #999">图片比例1:1，建议192x192px</div>
              </el-form-item>
              <el-form-item label="事件类型" prop="imgcontents.actionType">
                <el-select
                  style="width: 250px"
                  v-model="secondFrom.imgcontents.actionType"
                  placeholder="请选择"
                >
                  <el-option label="打开链接" value="OPEN_URL"> </el-option>
                  <el-option label="拨打电话" value="DIAL_PHONE"> </el-option>
                  <el-option label="打开浏览器" value="OPEN_BROWSER">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="secondFrom.imgcontents.actionType == 'OPEN_URL'"
                label="打开链接"
                prop="imgcontents.action.target"
              >
                <a
                  :href="secondFrom.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ secondFrom.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="secondFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="secondFrom.imgcontents.actionType == 'OPEN_BROWSER'"
                label="打开浏览器"
                prop="imgcontents.action.target"
              >
                <a
                  :href="secondFrom.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ secondFrom.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="secondFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="
                  secondFrom.imgcontents.actionType == 'OPEN_URL' ||
                  secondFrom.imgcontents.actionType == 'OPEN_BROWSER'
                "
                label="商户名称"
                prop="imgcontents.action.merchantName"
              >
                <div>{{ secondFrom.imgcontents.action.merchantName }}</div>
                <!-- <el-input
                      style="width: 250px"
                      v-model="secondFrom.imgcontents.action.merchantName"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="secondFrom.imgcontents.actionType == 'DIAL_PHONE'"
                label="拨打电话"
                prop="imgcontents.action.target"
              >
                <div>{{ secondFrom.imgcontents.action.target }}</div>
                <!-- <el-input
                      class="textarea_text"
                      type="textarea"
                      v-model="secondFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>

          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">内容设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="标题名称" prop="titlecontents.content">
                <div>{{ secondFrom.titlecontents.content }}</div>
                <!-- <el-input
                      style="width: 250px"
                      maxlength="26"
                      show-word-limit
                      v-model="secondFrom.titlecontents.content"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane
        v-if="list.number == '3' || list.number == '4'"
        label="副图文2"
        name="third"
      >
        <el-form
          ref="thirdform"
          :model="thirdForm"
          disabled
          label-width="100px"
        >
          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">图片设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="选择图片" prop="imgcontents.sourceId">
                <div
                  class="drawe_item_img_1"
                  v-if="thirdForm.imgcontents.sourceId == ''"
                >
                  <img
                    src="../../../../../../assets/images/ssobg.png"
                    alt=""
                    class="image_d"
                  />
                  <div class="add_img_1">
                    <el-icon><el-icon-plus /></el-icon>
                  </div>
                </div>
                <div v-else class="drawe_item_img_1">
                  <div class="media_icon">
                    <img
                      v-if="thirdForm.imgcontents.srcType == '1'"
                      src="../../../../../../assets/images/photoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="thirdForm.imgcontents.srcType == '2'"
                      src="../../../../../../assets/images/videoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="thirdForm.imgcontents.srcType == '3'"
                      src="../../../../../../assets/images/audioIcon.png"
                      alt=""
                    />
                  </div>
                  <img
                    v-if="thirdForm.imgcontents.srcType == '1'"
                    :src="thirdForm.imgcontents.url"
                    class="image_d"
                  />
                  <img
                    v-if="
                      (thirdForm.imgcontents.srcType == '2' ||
                        thirdForm.imgcontents.srcType == '3') &&
                      thirdForm.imgcontents.thumbnailUrl
                    "
                    :src="thirdForm.imgcontents.thumbnailUrl"
                    alt=""
                    class="image_d"
                  />
                </div>
                <div style="color: #999">图片比例1:1，建议192x192px</div>
              </el-form-item>
              <el-form-item label="事件类型" prop="imgcontents.actionType">
                <el-select
                  style="width: 250px"
                  v-model="thirdForm.imgcontents.actionType"
                  placeholder="请选择"
                >
                  <el-option label="打开链接" value="OPEN_URL"> </el-option>
                  <el-option label="拨打电话" value="DIAL_PHONE"> </el-option>
                  <el-option label="打开浏览器" value="OPEN_BROWSER">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="thirdForm.imgcontents.actionType == 'OPEN_URL'"
                label="打开链接"
                prop="imgcontents.action.target"
              >
                <a
                  :href="thirdForm.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ thirdForm.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="thirdForm.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="thirdForm.imgcontents.actionType == 'OPEN_BROWSER'"
                label="打开浏览器"
                prop="imgcontents.action.target"
              >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="thirdForm.imgcontents.action.target"
                    ></el-input> -->
                <a
                  :href="thirdForm.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ thirdForm.imgcontents.action.target }}</a
                >
              </el-form-item>
              <el-form-item
                v-if="
                  thirdForm.imgcontents.actionType == 'OPEN_URL' ||
                  thirdForm.imgcontents.actionType == 'OPEN_BROWSER'
                "
                label="商户名称"
                prop="imgcontents.action.merchantName"
              >
                <div>{{ thirdForm.imgcontents.action.merchantName }}</div>
                <!-- <el-input
                      style="width: 250px"
                      v-model="thirdForm.imgcontents.action.merchantName"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="thirdForm.imgcontents.actionType == 'DIAL_PHONE'"
                label="拨打电话"
                prop="imgcontents.action.target"
              >
                <div>{{ thirdForm.imgcontents.action.target }}</div>
                <!-- <el-input
                      class="textarea_text"
                      type="textarea"
                      v-model="thirdForm.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>

          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">内容设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="标题名称" prop="titlecontents.content">
                <!-- <el-input
                      style="width: 250px"
                      maxlength="26"
                      show-word-limit
                      v-model="thirdForm.titlecontents.content"
                    ></el-input> -->
                <div>{{ thirdForm.titlecontents.content }}</div>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane v-if="list.number == '4'" label="副图文3" name="fourth">
        <el-form
          ref="fourthform"
          :model="fourthFrom"
          disabled
          label-width="100px"
        >
          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">图片设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="选择图片" prop="imgcontents.sourceId">
                <div
                  class="drawe_item_img_1"
                  v-if="fourthFrom.imgcontents.sourceId == ''"
                >
                  <img
                    src="../../../../../../assets/images/ssobg.png"
                    alt=""
                    class="image_d"
                  />
                  <div class="add_img_1">
                    <el-icon><el-icon-plus /></el-icon>
                  </div>
                </div>
                <div v-else class="drawe_item_img_1">
                  <div class="media_icon">
                    <img
                      v-if="fourthFrom.imgcontents.srcType == '1'"
                      src="../../../../../../assets/images/photoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="fourthFrom.imgcontents.srcType == '2'"
                      src="../../../../../../assets/images/videoIcon.png"
                      alt=""
                    />
                    <img
                      v-if="fourthFrom.imgcontents.srcType == '3'"
                      src="../../../../../../assets/images/audioIcon.png"
                      alt=""
                    />
                  </div>
                  <img
                    v-if="fourthFrom.imgcontents.srcType == '1'"
                    :src="fourthFrom.imgcontents.url"
                    class="image_d"
                  />
                  <img
                    v-if="
                      (fourthFrom.imgcontents.srcType == '2' ||
                        fourthFrom.imgcontents.srcType == '3') &&
                      fourthFrom.imgcontents.thumbnailUrl
                    "
                    :src="fourthFrom.imgcontents.thumbnailUrl"
                    alt=""
                    class="image_d"
                  />
                </div>
                <div style="color: #999">图片比例1:1，建议192x192px</div>
              </el-form-item>
              <el-form-item label="事件类型" prop="imgcontents.actionType">
                <el-select
                  style="width: 250px"
                  v-model="fourthFrom.imgcontents.actionType"
                  placeholder="请选择"
                >
                  <el-option label="打开链接" value="OPEN_URL"> </el-option>
                  <el-option label="拨打电话" value="DIAL_PHONE"> </el-option>
                  <el-option label="打开浏览器" value="OPEN_BROWSER">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="fourthFrom.imgcontents.actionType == 'OPEN_URL'"
                label="打开链接"
                prop="imgcontents.action.target"
              >
                <a
                  :href="fourthFrom.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ fourthFrom.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="fourthFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="fourthFrom.imgcontents.actionType == 'OPEN_BROWSER'"
                label="打开浏览器"
                prop="imgcontents.action.target"
              >
                <a
                  :href="fourthFrom.imgcontents.action.target"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ fourthFrom.imgcontents.action.target }}</a
                >
                <!-- <el-input
                      class="textarea_text"
                      placeholder="链接格式为https://开头"
                      type="textarea"
                      v-model="fourthFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="
                  fourthFrom.imgcontents.actionType == 'OPEN_URL' ||
                  fourthFrom.imgcontents.actionType == 'OPEN_BROWSER'
                "
                label="商户名称"
                prop="imgcontents.action.merchantName"
              >
                <div>{{ fourthFrom.imgcontents.action.merchantName }}</div>
                <!-- <el-input
                      style="width: 250px"
                      v-model="fourthFrom.imgcontents.action.merchantName"
                    ></el-input> -->
              </el-form-item>
              <el-form-item
                v-if="fourthFrom.imgcontents.actionType == 'DIAL_PHONE'"
                label="拨打电话"
                prop="imgcontents.action.target"
              >
                <div>{{ fourthFrom.imgcontents.action.target }}</div>
                <!-- <el-input
                      class="textarea_text"
                      type="textarea"
                      v-model="fourthFrom.imgcontents.action.target"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>

          <div>
            <div class="drawer_img">
              <span style="margin-left: 10px">内容设置</span>
            </div>
            <div class="drawer_img_content">
              <el-form-item label="标题名称" prop="titlecontents.content">
                <div>{{ fourthFrom.titlecontents.content }}</div>
                <!-- <el-input
                      style="width: 250px"
                      maxlength="26"
                      show-word-limit
                      v-model="fourthFrom.titlecontents.content"
                    ></el-input> -->
              </el-form-item>
            </div>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  props: ['list'],
  data() {
    return {
      activeName: 'first',
      firstFrom: {
        imgcontents: {
          type: 'image',
          srcType: '',
          sourceId: '',
          url: '',
          thumbnailUrl: '',
          actionType: '',
          action: {
            target: '',
            merchantName: '',
          },
          positionNumber: 1,
        },
        titlecontents: {
          type: 'text',
          content: '',
          textTitle: true,
          positionNumber: 2,
        },
      },
      firstRules: {
        imgcontents: {
          sourceId: [
            {
              required: true,
              message: '请选择素材',
              trigger: 'change',
            },
          ],
          actionType: [
            {
              required: true,
              message: '请选择事件',
              trigger: 'change',
            },
          ],
          action: {
            target: [
              {
                required: true,
                message: '事件内容不能为空',
                trigger: 'change',
              },
            ],
            merchantName: [
              {
                required: true,
                message: '商户名称不能为空',
                trigger: 'change',
              },
            ],
          },
        },
        titlecontents: {
          content: [
            {
              required: true,
              message: '标题不可为空',
              trigger: 'change',
            },
          ],
        },
      },
      secondFrom: {
        imgcontents: {
          type: 'image',
          srcType: '',
          sourceId: '',
          url: '',
          thumbnailUrl: '',
          actionType: '',
          action: {
            target: '',
            merchantName: '',
          },
          positionNumber: 3,
        },
        titlecontents: {
          type: 'text',
          content: '',
          textTitle: true,
          positionNumber: 4,
        },
      },
      secondRules: {
        imgcontents: {
          sourceId: [
            {
              required: true,
              message: '请选择素材',
              trigger: 'change',
            },
          ],
          actionType: [
            {
              required: true,
              message: '请选择事件',
              trigger: 'change',
            },
          ],
          action: {
            target: [
              {
                required: true,
                message: '事件内容不能为空',
                trigger: 'change',
              },
            ],
            merchantName: [
              {
                required: true,
                message: '商户名称不能为空',
                trigger: 'change',
              },
            ],
          },
        },
        titlecontents: {
          content: [
            {
              required: true,
              message: '标题不可为空',
              trigger: 'change',
            },
          ],
        },
      },
      thirdForm: {
        imgcontents: {
          type: 'image',
          srcType: '',
          sourceId: '',
          url: '',
          thumbnailUrl: '',
          actionType: '',
          action: {
            target: '',
            merchantName: '',
          },
          positionNumber: 5,
        },
        titlecontents: {
          type: 'text',
          content: '',
          textTitle: true,
          positionNumber: 6,
        },
      },
      thirdRules: {
        imgcontents: {
          sourceId: [
            {
              required: true,
              message: '请选择素材',
              trigger: 'change',
            },
          ],
          actionType: [
            {
              required: true,
              message: '请选择事件',
              trigger: 'change',
            },
          ],
          action: {
            target: [
              {
                required: true,
                message: '事件内容不能为空',
                trigger: 'change',
              },
            ],
            merchantName: [
              {
                required: true,
                message: '商户名称不能为空',
                trigger: 'change',
              },
            ],
          },
        },
        titlecontents: {
          content: [
            {
              required: true,
              message: '标题不可为空',
              trigger: 'change',
            },
          ],
        },
      },
      fourthFrom: {
        imgcontents: {
          type: 'image',
          srcType: '',
          sourceId: '',
          url: '',
          thumbnailUrl: '',
          actionType: '',
          action: {
            target: '',
            merchantName: '',
          },
          positionNumber: 7,
        },
        titlecontents: {
          type: 'text',
          content: '',
          textTitle: true,
          positionNumber: 8,
        },
      },
      fourthRules: {
        imgcontents: {
          sourceId: [
            {
              required: true,
              message: '请选择素材',
              trigger: 'change',
            },
          ],
          actionType: [
            {
              required: true,
              message: '请选择事件',
              trigger: 'change',
            },
          ],
          action: {
            target: [
              {
                required: true,
                message: '事件内容不能为空',
                trigger: 'change',
              },
            ],
            merchantName: [
              {
                required: true,
                message: '商户名称不能为空',
                trigger: 'change',
              },
            ],
          },
        },
        titlecontents: {
          content: [
            {
              required: true,
              message: '标题不可为空',
              trigger: 'change',
            },
          ],
        },
      },
    }
  },
  methods: {},
  watch: {
    list: {
      handler(val) {
        this.firstFrom.imgcontents = val.ssosContentFrom.firstImg
        this.firstFrom.titlecontents = val.ssosContentFrom.firstTit
        this.secondFrom.imgcontents = val.ssosContentFrom.secondImg
        this.secondFrom.titlecontents = val.ssosContentFrom.secondTit
        this.thirdForm.imgcontents = val.ssosContentFrom.thirdImg
        this.thirdForm.titlecontents = val.ssosContentFrom.thirdTit
        this.fourthFrom.imgcontents = val.ssosContentFrom.fourthImg
        this.fourthFrom.titlecontents = val.ssosContentFrom.fourthTit
        this.chatbotId = val.chatbotId
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
}
</script>

<style scoped>
.drawer_img {
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: #eee;
}
.drawer_img_content {
  margin-top: 10px;
}
.drawe_item_img {
  width: 240px;
  height: 135px;
  position: relative;
}
.drawe_item_img_1 {
  width: 135px;
  height: 135px;
  position: relative;
}
.image_d {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
.add_img {
  position: absolute;
  font-size: 30px;
  font-weight: 800;
  top: 0;
  margin-left: 110px;
  margin-top: 50px;
  color: #fff;
}
.add_img_1 {
  position: absolute;
  font-size: 30px;
  font-weight: 800;
  top: 0;
  margin-left: 55px;
  margin-top: 50px;
  color: #fff;
}
.file_item {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-shrink: 0;
}
.item_medias {
  width: 194px;
  height: 215px;
  background: #fff;
  border-radius: 8px;
  position: relative;
  margin: 10px;
  border: 1px solid #eee;
  z-index: 9;
}
.item_medias_active {
  width: 194px;
  height: 215px;
  background: #fff;
  border-radius: 8px;
  position: relative;
  margin: 10px;
  border: 2px solid #409eff;
  z-index: 9;
}
.media_h {
  width: 100%;
  height: 124px;
}
.temp_media_h {
  width: 100%;
  height: 100%;
}
.media_t {
  width: 100%;
  height: 62px;
  font-size: 12px;
  line-height: 20px;
}
.image {
  width: 100%;
  height: 100%;
  border-radius: 8px 8px 0px 0px;
}
.madia_footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.media_name {
  display: inline-block;
  margin-left: 10px;
  margin-top: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 170px;
}
.media_icon {
  width: 24px;
  height: 22px;
  position: absolute;
  left: 2.08%;
  right: 2.08%;
  top: 6.25%;
  bottom: 5.9%;
}
.replace {
  position: absolute;
  width: 20px;
  height: 20px;
  background: #409eff;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  color: #fff;
  right: 0;
  top: 0;
  margin: 10px;
  z-index: 9;
}
.textarea_text {
  width: 315px;
  height: 90px;
  transform: scale(0.795);
  transform-origin: 0px 0px;
}
</style>

<style>
.el-tabs__nav-scroll {
  margin-left: 10px;
}
</style>
