<template>
  <el-dialog
    title="签名查询工具"
    v-model="dialogVisible"
    width="1200px"
    :before-close="handleClose"
    class="signature-query-dialog"
  >
    <!-- 查询表单 -->
    <div class="query-form-card">
      <el-card shadow="never" class="query-card">
        <template #header>
          <div class="card-header">
            <i class="el-icon-search"></i>
            <span>查询条件</span>
          </div>
        </template>
        <el-form :inline="true" :model="queryForm" class="query-form">
          <el-form-item label="用户名">
            <el-input
              v-model="queryForm.consumerName"
              placeholder="请输入用户名"
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="签名">
            <el-input
              v-model="queryForm.signature"
              placeholder="请输入签名"
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="签名属性">
            <el-select
              v-model="queryForm.signatureAttr"
              placeholder="请选择"
              clearable
              style="width: 140px;"
            >
              <el-option label="全部" value="0" />
              <el-option label="报备签名" value="1" />
              <el-option label="自定义签名" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :loading="loading">
              <i class="el-icon-search"></i>
              查询
            </el-button>
            <el-button @click="handleReset">
              <i class="el-icon-refresh"></i>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 卡片式数据展示 -->
    <div class="cards-container" v-loading="loading">
      <div class="signature-cards">
        <el-card
          v-for="(group, signature) in groupedData"
          :key="signature"
          shadow="hover"
          class="signature-card"
        >
          <!-- 卡片头部 - 签名基本信息 -->
          <template #header>
            <div class="signature-header">
              <div class="signature-info">
                <div class="signature-name">
                  <i class="el-icon-edit"></i>
                  <span class="signature-text">{{ signature }}</span>
                  <el-tag
                    :type="getSignatureAttrTag(group[0].signatureAttr).type"
                    size="small"
                    class="attr-tag"
                  >
                    {{ getSignatureAttrTag(group[0].signatureAttr).text }}
                  </el-tag>
                  <el-tag
                    v-if="group.length > 1"
                    type="warning"
                    size="small"
                    class="count-tag"
                  >
                    {{ group.length }}个用户
                  </el-tag>
                </div>
                <div class="signature-meta">
                  <span class="service-type">
                    <i class="el-icon-message"></i>
                    {{ getServiceTypeTag(group[0].serviceType).text }}
                  </span>
                  <span class="create-time">
                    <i class="el-icon-time"></i>
                    {{ formatDate(group[0].createTime) }}
                  </span>
                </div>
              </div>
              <div class="signature-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="toggleExpand(signature)"
                  class="expand-btn"
                >
                  <i :class="expandedCards[signature] ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                  {{ expandedCards[signature] ? '收起' : '展开' }}
                </el-button>
              </div>
            </div>
          </template>

          <!-- 卡片内容 - 签名详细信息 -->
          <div class="signature-content">
            <!-- 短信示例 -->
            <div class="content-example" v-if="group[0].contentExample">
              <div class="example-label">
                <i class="el-icon-chat-line-square"></i>
                短信示例
              </div>
              <div class="example-text">{{ group[0].contentExample }}</div>
            </div>

            <!-- 实名状态概览 -->
            <div class="realname-overview">
              <div class="overview-label">
                <i class="el-icon-shield"></i>
                实名状态
              </div>
              <div class="operator-status-row">
                <div class="operator-item">
                  <i class="iconfont icon-yidong operator-icon yd"></i>
                  <span class="operator-name">移动</span>
                  <el-tag
                    :type="group[0].ydRealNameStatus == 1 ? 'success' : 'info'"
                    size="small"
                  >
                    {{ group[0].ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                </div>
                <div class="operator-item">
                  <i class="iconfont icon-liantong operator-icon lt"></i>
                  <span class="operator-name">联通</span>
                  <el-tag
                    :type="group[0].ltRealNameStatus == 1 ? 'success' : 'info'"
                    size="small"
                  >
                    {{ group[0].ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                </div>
                <div class="operator-item">
                  <i class="iconfont icon-dianxin operator-icon dx"></i>
                  <span class="operator-name">电信</span>
                  <el-tag
                    :type="group[0].dxRealNameStatus == 1 ? 'success' : 'info'"
                    size="small"
                  >
                    {{ group[0].dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 展开内容 - 用户列表和详细实名信息 -->
            <el-collapse-transition>
              <div v-show="expandedCards[signature]" class="expanded-content">
                <!-- 用户列表 -->
                <div class="users-list" v-if="group.length > 1">
                  <div class="users-label">
                    <i class="el-icon-user"></i>
                    使用该签名的用户 ({{ group.length }}个)
                  </div>
                  <div class="users-grid">
                    <div
                      v-for="item in group"
                      :key="item.signatureId"
                      class="user-item"
                      @click="handleViewDetail(item)"
                    >
                      <div class="user-name">{{ item.consumerName }}</div>
                      <div class="user-meta">
                        <span class="user-id">ID: {{ item.userId }}</span>
                        <span class="create-date">{{ formatDate(item.createTime) }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 详细实名信息 -->
                <div class="detailed-realname">
                  <div class="realname-label">
                    <i class="el-icon-document-checked"></i>
                    详细实名信息
                  </div>
                  <el-row :gutter="16" class="realname-cards">
                    <!-- 移动实名详情 -->
                    <el-col :span="8">
                      <div class="realname-card yd-card">
                        <div class="realname-card-header">
                          <i class="iconfont icon-yidong"></i>
                          <span>移动</span>
                        </div>
                        <div class="realname-card-content">
                          <el-tag
                            :type="group[0].ydRealNameStatus == 1 ? 'success' : 'info'"
                            size="large"
                          >
                            {{ group[0].ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                          </el-tag>
                          <div v-if="group[0].ydAvalibleType" class="available-types">
                            <div class="types-label">可用类型：</div>
                            <el-tag
                              v-for="type in group[0].ydAvalibleType.split(',')"
                              :key="type"
                              size="small"
                              type="success"
                              class="type-tag"
                            >
                              {{ type.trim() }}
                            </el-tag>
                          </div>
                        </div>
                      </div>
                    </el-col>
                    <!-- 联通实名详情 -->
                    <el-col :span="8">
                      <div class="realname-card lt-card">
                        <div class="realname-card-header">
                          <i class="iconfont icon-liantong"></i>
                          <span>联通</span>
                        </div>
                        <div class="realname-card-content">
                          <el-tag
                            :type="group[0].ltRealNameStatus == 1 ? 'success' : 'info'"
                            size="large"
                          >
                            {{ group[0].ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                          </el-tag>
                          <div v-if="group[0].ltAvalibleType" class="available-types">
                            <div class="types-label">可用类型：</div>
                            <el-tag
                              v-for="type in group[0].ltAvalibleType.split(',')"
                              :key="type"
                              size="small"
                              type="success"
                              class="type-tag"
                            >
                              {{ type.trim() }}
                            </el-tag>
                          </div>
                        </div>
                      </div>
                    </el-col>
                    <!-- 电信实名详情 -->
                    <el-col :span="8">
                      <div class="realname-card dx-card">
                        <div class="realname-card-header">
                          <i class="iconfont icon-dianxin"></i>
                          <span>电信</span>
                        </div>
                        <div class="realname-card-content">
                          <el-tag
                            :type="group[0].dxRealNameStatus == 1 ? 'success' : 'info'"
                            size="large"
                          >
                            {{ group[0].dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                          </el-tag>
                          <div v-if="group[0].dxAvalibleType" class="available-types">
                            <div class="types-label">可用类型：</div>
                            <el-tag
                              v-for="type in group[0].dxAvalibleType.split(',')"
                              :key="type"
                              size="small"
                              type="success"
                              class="type-tag"
                            >
                              {{ type.trim() }}
                            </el-tag>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <!-- 操作按钮 -->
                <div class="card-actions">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleViewDetail(group[0])"
                  >
                    <i class="el-icon-view"></i>
                    查看详情
                  </el-button>
                  <el-button
                    v-if="group[0].imgUrl"
                    type="success"
                    size="small"
                    @click="handleViewImages(group[0])"
                  >
                    <i class="el-icon-picture"></i>
                    查看资质
                  </el-button>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && Object.keys(groupedData).length === 0"
        description="暂无数据"
        class="empty-state"
      >
        <el-button type="primary" @click="handleReset">重新查询</el-button>
      </el-empty>
    </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

    <!-- 详情弹窗 -->
    <el-dialog
      title="签名详情"
      v-model="detailVisible"
      width="800px"
      append-to-body
      class="signature-detail-dialog"
    >
      <div v-loading="detailLoading" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="签名">
            {{ detailData.signature }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名称">
            {{ detailData.consumerName }}
          </el-descriptions-item>
          <el-descriptions-item label="短信示例">
            {{ detailData.contentExample }}
          </el-descriptions-item>
          <el-descriptions-item label="短信类型">
            <el-tag :type="getServiceTypeTag(detailData.serviceType).type" size="small">
              {{ getServiceTypeTag(detailData.serviceType).text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="签名属性">
            <el-tag :type="detailData.signatureAttr == 1 ? 'success' : 'warning'" size="small">
              {{ detailData.signatureAttr == 1 ? '报备签名' : '自定义签名' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(detailData.createTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 实名状态详情 -->
        <div class="realname-detail" v-if="detailData.signatureId">
          <h4>实名状态详情</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="operator-card yd-card">
                <template #header>
                  <div class="card-header">
                    <i class="iconfont icon-yidong"></i>
                    <span>移动</span>
                  </div>
                </template>
                <div class="status-content">
                  <el-tag 
                    :type="detailData.ydRealNameStatus == 1 ? 'success' : 'info'" 
                    size="large"
                  >
                    {{ detailData.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                  <div v-if="detailData.ydAvalibleType" class="available-types">
                    <span class="label">可用类型：</span>
                    <el-tag 
                      v-for="type in detailData.ydAvalibleType.split(',')" 
                      :key="type"
                      size="small" 
                      type="success"
                      style="margin-right: 5px;"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="operator-card lt-card">
                <template #header>
                  <div class="card-header">
                    <i class="iconfont icon-liantong"></i>
                    <span>联通</span>
                  </div>
                </template>
                <div class="status-content">
                  <el-tag 
                    :type="detailData.ltRealNameStatus == 1 ? 'success' : 'info'" 
                    size="large"
                  >
                    {{ detailData.ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                  <div v-if="detailData.ltAvalibleType" class="available-types">
                    <span class="label">可用类型：</span>
                    <el-tag 
                      v-for="type in detailData.ltAvalibleType.split(',')" 
                      :key="type"
                      size="small" 
                      type="success"
                      style="margin-right: 5px;"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="operator-card dx-card">
                <template #header>
                  <div class="card-header">
                    <i class="iconfont icon-dianxin"></i>
                    <span>电信</span>
                  </div>
                </template>
                <div class="status-content">
                  <el-tag 
                    :type="detailData.dxRealNameStatus == 1 ? 'success' : 'info'" 
                    size="large"
                  >
                    {{ detailData.dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                  <div v-if="detailData.dxAvalibleType" class="available-types">
                    <span class="label">可用类型：</span>
                    <el-tag 
                      v-for="type in detailData.dxAvalibleType.split(',')" 
                      :key="type"
                      size="small" 
                      type="success"
                      style="margin-right: 5px;"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 资质图片 -->
        <div class="qualification-images" v-if="detailData.imgUrl">
          <h4>资质图片</h4>
          <div class="image-gallery">
            <el-image
              v-for="(img, index) in getImageList(detailData.imgUrl)"
              :key="index"
              :src="img"
              :preview-src-list="getImageList(detailData.imgUrl)"
              fit="cover"
              class="qualification-image"
            />
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog
      title="资质图片预览"
      v-model="imagePreviewVisible"
      width="800px"
      append-to-body
      class="image-preview-dialog"
    >
      <div class="image-preview-container">
        <el-image
          v-for="(img, index) in previewImages"
          :key="index"
          :src="img"
          :preview-src-list="previewImages"
          fit="contain"
          class="preview-image"
        />
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  name: 'SignatureQuery',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    },
    // 按签名分组数据
    groupedData() {
      const groups = {}
      this.tableData.forEach(item => {
        const signature = item.signature
        if (!groups[signature]) {
          groups[signature] = []
        }
        groups[signature].push(item)
      })
      // 按创建时间排序每个组内的数据
      Object.keys(groups).forEach(signature => {
        groups[signature].sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      })
      return groups
    }
  },
  data() {
    return {
      loading: false,
      detailLoading: false,
      detailVisible: false,
      queryForm: {
        consumerName: '',
        signature: '',
        signatureAttr: ''
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      detailData: {},
      expandedCards: {}, // 展开的卡片状态
      imagePreviewVisible: false, // 图片预览弹窗
      previewImages: [] // 预览图片列表
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.handleQuery()
      } else {
        this.handleReset()
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true
      const params = {
        current: this.pagination.current,
        size: this.pagination.size,
        ...this.queryForm
      }
      
      window.api.post(
        window.path.omcs + 'consumerSignature/page',
        params,
        (res) => {
          console.log(res,'res');
          
          this.loading = false
          this.tableData = res.records || []
            this.pagination.total = res.total || 0
        },
        (error) => {
          this.loading = false
          this.$message.error('查询失败')
        }
      )
    },

    // 重置
    handleReset() {
      this.queryForm = {
        consumerName: '',
        signature: '',
        signatureAttr: ''
      }
      this.pagination.current = 1
      this.tableData = []
      this.pagination.total = 0
      this.expandedCards = {} // 重置展开状态
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.handleQuery()
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.handleQuery()
    },

    // 行点击
    handleRowClick(row) {
      // 可以在这里添加行点击逻辑
    },

    // 查看详情
    handleViewDetail(row) {
      this.detailVisible = true
      this.detailLoading = true
      
      window.api.get(
        window.path.omcs + "consumerSignature/realName?userId=" + row.userId + "&signatureId=" + row.signatureId,
        {},
        (res) => {
          this.detailLoading = false
          if (res.code === 200) {
            this.detailData = res.data
          } else {
            this.$message.error(res.msg || '获取详情失败')
          }
        },
        (error) => {
          this.detailLoading = false
          this.$message.error('获取详情失败')
        }
      )
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
    },

    // 获取服务类型标签
    getServiceTypeTag(type) {
      const typeMap = {
        1: { text: '验证码', type: 'primary' },
        2: { text: '通知', type: 'success' },
        3: { text: '营销', type: 'warning' }
      }
      return typeMap[type] || { text: '未知', type: 'info' }
    },

    // 获取签名属性标签
    getSignatureAttrTag(attr) {
      const attrMap = {
        1: { text: '报备签名', type: 'success' },
        2: { text: '自定义签名', type: 'warning' }
      }
      return attrMap[attr] || { text: '未知', type: 'info' }
    },

    // 切换卡片展开状态
    toggleExpand(signature) {
      // Vue 3 兼容的响应式更新方式
      const newExpandedCards = { ...this.expandedCards }
      newExpandedCards[signature] = !newExpandedCards[signature]
      this.expandedCards = newExpandedCards
    },

    // 查看图片
    handleViewImages(row) {
      if (row.imgUrl) {
        this.previewImages = this.getImageList(row.imgUrl)
        this.imagePreviewVisible = true
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },

    // 获取图片列表
    getImageList(imgUrl) {
      if (!imgUrl) return []
      return imgUrl.split(',').filter(item => item.trim() !== '').map(item => {
        return item.startsWith('http') ? item : window.path.img + item
      })
    }
  }
}
</script>

<style scoped>
.signature-query-dialog {
  /* 查询表单卡片样式 */
  .query-form-card {
    margin-bottom: 20px;

    .query-card {
      border: 1px solid #e4e7ed;

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #303133;

        i {
          color: #409eff;
          font-size: 16px;
        }
      }

      .query-form {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }
  }

  /* 卡片容器样式 */
  .cards-container {
    min-height: 400px;

    .signature-cards {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .empty-state {
      margin-top: 60px;
    }
  }

  /* 签名卡片样式 */
  .signature-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    }

    .signature-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .signature-info {
        flex: 1;

        .signature-name {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          i {
            color: #409eff;
            font-size: 16px;
          }

          .signature-text {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }

          .attr-tag {
            margin-left: 8px;
          }

          .count-tag {
            margin-left: 4px;
          }
        }

        .signature-meta {
          display: flex;
          gap: 16px;
          font-size: 13px;
          color: #909399;

          span {
            display: flex;
            align-items: center;
            gap: 4px;

            i {
              font-size: 14px;
            }
          }
        }
      }

      .signature-actions {
        .expand-btn {
          color: #409eff;

          &:hover {
            background-color: #ecf5ff;
          }
        }
      }
    }

    .signature-content {
      .content-example {
        margin-bottom: 16px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 3px solid #409eff;

        .example-label {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
          font-weight: 600;
          color: #606266;
          margin-bottom: 8px;

          i {
            color: #409eff;
          }
        }

        .example-text {
          font-size: 14px;
          color: #303133;
          line-height: 1.5;
        }
      }

      .realname-overview {
        margin-bottom: 16px;

        .overview-label {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
          font-weight: 600;
          color: #606266;
          margin-bottom: 12px;

          i {
            color: #67c23a;
          }
        }

        .operator-status-row {
          display: flex;
          gap: 24px;

          .operator-item {
            display: flex;
            align-items: center;
            gap: 6px;

            .operator-icon {
              font-size: 16px;

              &.yd {
                color: #409eff;
              }

              &.lt {
                color: #f56c6c;
              }

              &.dx {
                color: #e6a23c;
              }
            }

            .operator-name {
              font-size: 13px;
              color: #606266;
              min-width: 32px;
            }
          }
        }
      }

      .expanded-content {
        border-top: 1px solid #e4e7ed;
        padding-top: 16px;
        margin-top: 16px;

        .users-list {
          margin-bottom: 20px;

          .users-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 600;
            color: #606266;
            margin-bottom: 12px;

            i {
              color: #e6a23c;
            }
          }

          .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;

            .user-item {
              padding: 12px;
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                border-color: #409eff;
                background-color: #ecf5ff;
              }

              .user-name {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
              }

              .user-meta {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #909399;

                .user-id {
                  color: #409eff;
                }
              }
            }
          }
        }

        .detailed-realname {
          margin-bottom: 20px;

          .realname-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 600;
            color: #606266;
            margin-bottom: 16px;

            i {
              color: #67c23a;
            }
          }

          .realname-cards {
            .realname-card {
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              overflow: hidden;

              .realname-card-header {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: #f8f9fa;
                border-bottom: 1px solid #e4e7ed;
                font-weight: 600;

                i {
                  font-size: 16px;
                }
              }

              &.yd-card .realname-card-header i {
                color: #409eff;
              }

              &.lt-card .realname-card-header i {
                color: #f56c6c;
              }

              &.dx-card .realname-card-header i {
                color: #e6a23c;
              }

              .realname-card-content {
                padding: 16px;
                text-align: center;

                .available-types {
                  margin-top: 12px;
                  text-align: left;

                  .types-label {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 6px;
                  }

                  .type-tag {
                    margin-right: 6px;
                    margin-bottom: 4px;
                  }
                }
              }
            }
          }
        }

        .card-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
          padding-top: 16px;
          border-top: 1px solid #e4e7ed;
        }
      }
    }
  }

  /* 分页容器 */
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

/* 详情弹窗样式 */
.signature-detail-dialog {
  .detail-content {
    .realname-detail {
      margin-top: 30px;

      h4 {
        margin-bottom: 15px;
        color: #303133;
        font-weight: 600;
      }

      .operator-card {
        height: 140px;

        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;

          i {
            font-size: 16px;
          }
        }

        &.yd-card .card-header i {
          color: #409eff;
        }

        &.lt-card .card-header i {
          color: #f56c6c;
        }

        &.dx-card .card-header i {
          color: #e6a23c;
        }

        .status-content {
          text-align: center;

          .available-types {
            margin-top: 10px;
            text-align: left;

            .label {
              font-size: 12px;
              color: #909399;
              margin-bottom: 5px;
              display: block;
            }
          }
        }
      }
    }

    .qualification-images {
      margin-top: 30px;

      h4 {
        margin-bottom: 15px;
        color: #303133;
        font-weight: 600;
      }

      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .qualification-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          cursor: pointer;
          border: 2px solid #e4e7ed;
          transition: border-color 0.3s;

          &:hover {
            border-color: #409eff;
          }
        }
      }
    }
  }
}

/* 图片预览弹窗样式 */
.image-preview-dialog {
  .image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: center;

    .preview-image {
      max-width: 300px;
      max-height: 300px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409eff;
        transform: scale(1.02);
      }
    }
  }
}

.signature-detail-dialog {
  .detail-content {
    .realname-detail {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 15px;
        color: #303133;
        font-weight: 600;
      }
      
      .operator-card {
        height: 140px;
        
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          
          i {
            font-size: 16px;
          }
        }
        
        &.yd-card .card-header i {
          color: #409eff;
        }
        
        &.lt-card .card-header i {
          color: #f56c6c;
        }
        
        &.dx-card .card-header i {
          color: #e6a23c;
        }
        
        .status-content {
          text-align: center;
          
          .available-types {
            margin-top: 10px;
            text-align: left;
            
            .label {
              font-size: 12px;
              color: #909399;
              margin-bottom: 5px;
              display: block;
            }
          }
        }
      }
    }
    
    .qualification-images {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 15px;
        color: #303133;
        font-weight: 600;
      }
      
      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .qualification-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          cursor: pointer;
          border: 2px solid #e4e7ed;
          transition: border-color 0.3s;
          
          &:hover {
            border-color: #409eff;
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .signature-query-dialog {
    .query-form {
      .el-form--inline .el-form-item {
        display: block;
        margin-bottom: 15px;
      }
    }
    
    .realname-status {
      flex-direction: column;
      gap: 8px;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .signature-query-dialog {
    .signature-card {
      .signature-content {
        .expanded-content {
          .detailed-realname {
            .realname-cards {
              .el-col {
                margin-bottom: 16px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .signature-query-dialog {
    .query-form-card {
      .query-form {
        .el-form-item {
          display: block;
          margin-bottom: 16px;

          .el-input,
          .el-select {
            width: 100% !important;
          }
        }
      }
    }

    .signature-card {
      .signature-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .signature-actions {
          align-self: flex-end;
        }
      }

      .signature-content {
        .realname-overview {
          .operator-status-row {
            flex-direction: column;
            gap: 12px;
          }
        }

        .expanded-content {
          .users-list {
            .users-grid {
              grid-template-columns: 1fr;
            }
          }

          .detailed-realname {
            .realname-cards {
              .el-col {
                width: 100%;
                margin-bottom: 16px;
              }
            }
          }

          .card-actions {
            flex-direction: column;

            .el-button {
              width: 100%;
            }
          }
        }
      }
    }
  }

  .image-preview-dialog {
    .image-preview-container {
      .preview-image {
        max-width: 100%;
        max-height: 200px;
      }
    }
  }
}
</style>
