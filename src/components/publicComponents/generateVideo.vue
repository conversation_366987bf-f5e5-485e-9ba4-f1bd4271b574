<template>
  <div>
    <el-dialog
      title="视频生成"
      v-model="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <div class="upVideo">
        <div>
          <el-upload
            v-bind:data="{
              FoldPath: '上传目录',
              SecretKey: '安全验证',
            }"
            v-bind:on-progress="uploadVideoProcess"
            v-bind:before-upload="beforeUploadVideo"
            :action="action"
            :headers="token"
            list-type="picture-card"
            :file-list="fileList"
            :class="{ hide: hideUpload }"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
          >
            <template v-slot:default>
              <el-icon><el-icon-plus /></el-icon>
            </template>
            <template v-slot:file="{ file }">
              <div>
                <video
                  style="width: 146px; height: 146px"
                  :src="path"
                  controls="controls"
                ></video>
                <span class="el-upload-list__item-actions">
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleRemove"
                  >
                    <el-icon><el-icon-delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
        </div>
        <div v-if="formInline.url">
          <div class="detailsList">
            <span class="detailsList-title">文件名称:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.fileName
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">文件大小:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.size + 'MB'
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">视频宽:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.width
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">视频高:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.height
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">视频帧数:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.frameRate
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">清晰度:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.videoBitRate
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">音质:</span>
            <span class="detailsList-content" style="width: 300px">{{
              formInline.audioBitRate
            }}</span>
          </div>
        </div>
      </div>
      <div class="demarcation"></div>
      <div class="upVideo">
        <div v-if="formInline.url">
          <el-form
            :inline="false"
            label-width="90px"
            :model="editFormInline"
            class="demo-form-inline"
            ref="formTemp"
          >
            <el-form-item label="视频宽" prop="width">
              <el-input
                v-model="editFormInline.width"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
            <el-form-item label="视频高" prop="height">
              <el-input
                v-model="editFormInline.height"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
            <el-form-item label="帧数" prop="frameRate">
              <el-input
                v-model="editFormInline.frameRate"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
            <el-form-item label="清晰度" prop="videoBitRate">
              <el-input
                v-model="editFormInline.videoBitRate"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
            <el-form-item label="音质" prop="audioBitRate">
              <el-input
                v-model="editFormInline.audioBitRate"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="editFormInline.size"
              label="文件大小"
              prop="size"
            >
              <el-input
                disabled
                v-model="editFormInline.size"
                placeholder="请输入内容"
              ></el-input>
              <div style="color: red">tips:填写参数不可比原参数大</div>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="formInline.url" class="btn_video">
          <el-button v-if="!pressLoding" type="primary">
            <i
              class="iconfont icon-compress-video"
              style=""
              @click="compressVideo"
              >压缩</i
            >
          </el-button>
          <el-button v-else type="primary" :loading="true">压缩中</el-button>
          <div style="margin-top: 5px">
            <el-button v-if="videoPath" type="primary">
              <a
                style="color: #fff"
                @click="downVideo(videoPath)"
                href="javascript:void(0)"
                rel="noopener noreferrer"
              >
                <i class="iconfont icon-download-1-copy" style="">下载</i>
              </a>
            </el-button>
          </div>
        </div>
        <div v-if="videoPath != ''" class="video_time">
          <video
            style="width: 300px; height: 300px"
            :src="videoPath"
            controls="controls"
          ></video>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <!-- <el-button type="primary" @click="dialogVisible = false">下载</el-button> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Plus as ElIconPlus, Delete as ElIconDelete } from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import bus from '../common/bus'
export default {
  components: {
    ElIconPlus,
    ElIconDelete,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      action: window.path.fiveWeb + '/compress/upload',
      token: {},
      fileList: [],
      videoFlag: false,
      videoUploadPercent: '',
      loadingFlag: false,
      titleW: '',
      path: '',
      disabled: false,
      formInline: {},
      editFormInline: {},
      videoPath: '',
      pressLoding: false,
      hideUpload: false,
    }
  },
  mounted() {
    this.token = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
      $emit(bus, 'closeVideo', this.dialogVisible)
    },
    uploadVideoProcess(event, file, fileList) {
      this.videoFlag = true
      this.videoUploadPercent = file.percentage.toFixed(0) * 1
      this.loadingFlag = true
      this.hideUpload = true
      // this.titleW = "视频压缩中大致需要30秒至60秒···";
    },
    beforeUploadVideo(file) {
      console.log(file.type, 'file.type')
      const isLt5M = file.size / 1024 / 1024 < 20
      if (
        ['video/mp4'].indexOf(file.type) == -1 &&
        ['video/WEBM'].indexOf(file.type) == -1
      ) {
        this.$message({
          type: 'warning',
          message: '请上传正确的视频格式',
        })
        return false
      }
      if (!isLt5M) {
        this.$message({
          type: 'warning',
          message: '视频大小不能超过20MB',
        })
        return false
      }
      // this.upVideo = false;
    },
    //清除素材地址
    handleRemove() {
      this.fileList = []
      this.path = ''
      this.formInline = {}
      this.editFormInline = {}
      this.videoPath = ''
      this.hideUpload = false
    },
    //上穿成功钩子函数
    handleSuccess(res, fileList) {
      // console.log(res, 'res');
      if (res.code == 200) {
        this.videoFlag = false
        this.videoUploadPercent = 0
        this.path = res.data.url
        this.formInline = res.data
        let obj = JSON.stringify(res.data)
        this.editFormInline = JSON.parse(obj)
        this.hideUpload = true
      } else {
        this.$message({
          type: 'error',
          message: res.msg,
        })
        this.fileList = []
        this.path = ''
        this.loadingFlag = false
        this.hideUpload = false
      }
    },
    //压缩
    compressVideo() {
      this.pressLoding = true
      window.api.post(
        window.path.fiveWeb + '/compress/video',
        this.editFormInline,
        (res) => {
          if (res.code == 200) {
            this.pressLoding = false
            this.videoPath = res.data.url
            this.editFormInline.size = res.data.size
          } else {
            this.$message.error(res.msg)
          }
          // this.tableDataObj.tableData = res.data.records;
          // this.tableDataObj.total = res.data.total;
          // this.tableDataObj.loading2 = false;
          // this.$refs.plTable.reloadData(res.data.records);
        }
      )
    },
    //下载
    downVideo(url) {
      window.open(`${url}?response-content-type=application%2Foctet-stream`)
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.fileList = []
        this.path = ''
        this.formInline = {}
        this.editFormInline = {}
        this.videoPath = ''
        this.hideUpload = false
      }
    },
  },
  emits: ['closeVideo'],
}
</script>

<style lang="less" scoped>
.upVideo {
  display: flex;
  position: relative;
}
.detailsList {
  line-height: 42px;
  padding-left: 30px;
  font-size: 12px;
  position: relative;
}
.bs {
  position: absolute;
  top: 0px;
  left: 100px;
  color: red;
  font-size: 14px;
  z-index: 9999;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.btn_video {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -50px;
}
.video_time {
  position: absolute;
  right: 5%;
}
.demarcation {
  width: 100%;
  height: 1px;
  margin: 10px 0;
  border-top: 1px dashed #ccc;
}
.hide {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}
</style>
