<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="失败代码" prop="code">
            <el-input
              v-model="formInline.code"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="代码类别" prop="codeNoteIndustry">
            <el-input
              v-model="formInline.codeNoteIndustry"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="代码注释" prop="annotation">
            <el-input
              v-model="formInline.annotation"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="运营商" prop="operator">
            <el-select
              v-model="formInline.operator"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建人" prop="buildName">
            <el-input
              v-model="formInline.buildName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="dataCreat">
            <el-date-picker
              v-model="formInline.dataCreat"
              class="input-time"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
              <el-button type="primary" @click="addopt">新增</el-button>
              <el-button type="primary" style="margin-left: 15px" @click="updatefail">上传失败代码</el-button>
              <!-- <el-button type="primary"  @click="keyupdate">一键同步</el-button> -->
              <el-button
                type="danger"
                style="margin-left: 15px;"
                v-if="selectId.length > 0"
                @click="delAll"
              >批量删除</el-button>
          </template>
        </table-tem>
        <!--分页-->
        <!-- <div v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
          
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </div> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="440px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="120px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="失败代码" prop="failureCode">
            <el-input
              v-model="formop.failureCode"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="失败代码注释" prop="codeNote">
            <el-input
              v-model="formop.codeNote"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="失败代码类别" prop="codeNoteId">
            <el-select
              v-model="formop.codeNoteId"
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in codeNotes"
                :label="item.failureCodeNote"
                :value="item.failureCodeNoteId"
                :key="index"
              ></el-option>
              <!-- <el-option label="空号、停机、关机" value="空号、停机、关机"></el-option>
                  <el-option label="地区网关屏蔽" value="地区网关屏蔽"></el-option>
                  <el-option label="手机存储空间满" value="手机存储空间满"></el-option>
                  <el-option label="网关不支持此号段" value="网关不支持此号段"></el-option>
                  <el-option label="网关未接收到状态" value="网关未接收到状态"></el-option>
                  <el-option label="网关黑名单" value="网关黑名单"></el-option>
                  <el-option label="网关流速控制错误" value="网关流速控制错误"></el-option>
                  <el-option label="移动内部错误" value="移动内部错误"></el-option>
                  <el-option label="用户不在服务区错误" value="用户不在服务区错误"></el-option>
                  <el-option label="超时未接收到响应消息" value="超时未接收到响应消息"></el-option>
                  <el-option label="超限制发送" value="超限制发送"></el-option>
                  <el-option label="签名未报备" value="签名未报备"></el-option>
                  <el-option label="屏蔽短息业务" value="屏蔽短息业务"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item label="运营商" prop="carriers">
            <el-select
              v-model="formop.carriers"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增结束 -->
      <!-- 上传失败代码 -->
      <el-dialog
        title="导入失败代码"
        v-model="dialogFormUpdate"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formupcode"
          :rules="rules"
          ref="formupcode"
          label-width="120px"
        >
          <el-form-item label="运营商" prop="carriers">
            <el-select
              v-model="formupcode.carriers"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="失败代码类别" prop="codeNoteId">
            <el-select
              v-model="formupcode.codeNoteId"
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in codeNotes"
                :label="item.failureCodeNote"
                :value="item.failureCodeNoteId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="上传文件">
            <el-upload
              class="upload-demo"
              ref="uploading"
              :action="actionUrl"
              :on-remove="handleRemove"
              multiple
              :data="myData"
              :limit="1"
              :headers="myHeader"
              :auto-upload="false"
              :on-success="handleAvatarSuccess"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <template v-slot:tip>
                <div class="el-upload__tip">
                  只支持Excel格式导入：每行两列，包括失败代码和失败代码注释。
                </div>
              </template>
            </el-upload>
            <a
              style="color: #409eff"
              href="https://doc.zthysms.com/Public/Uploads/2020-11-27/5fc0693f5b522.xlsx"
              >模板下载</a
            >
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForms('formupcode')"
              >提 交</el-button
            >
            <el-button @click="dialogFormUpdate = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 上传失败代码结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
export default {
  components: {
    TableTem,
  },
  name: 'FailurecodeMag',
  data() {
    return {
      actionUrl: window.path.omcs + 'operatingfailurecode/uploadFailureCode', //上传失败代码
      isFirstEnter: false,
      myHeader: '', //请求头
      fileList: [], //上传储存
      abc: false,
      titleMap: {
        add: '新增失败代码',
        // edit: "编辑运营商"
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      dialogFormUpdate: false, //导入失败代码隐藏
      selectId: '',
      codeNotes: [],
      formInline: {
        //失败代码查询
        dataCreat: [],
        code: '',
        annotation: '',
        operator: '',
        codeNoteIndustry: '',
        buildName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //失败代码查询储存
        dataCreat: [],
        code: '',
        annotation: '',
        codeNoteIndustry: '',
        operator: '',
        buildName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      formop: {
        //新增失败代码
        failureCode: '',
        codeNote: '',
        codeNoteId: '',
        carriers: '',
      },
      //提交失败代码上传
      formupcode: {
        carriers: '',
        codeNoteId: '',
      },
      rules: {
        failureCode: [
          { required: true, message: '请输入失败代码', trigger: 'blur' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: ['blur', 'change'],
          },
          { pattern: /^[^\u4e00-\u9fa5]+$/, message: '不能输入汉字' },
        ],
        codeNote: [
          { required: true, message: '请输入失败代码注释', trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '长度在 1 到 50 个字符',
            trigger: ['blur', 'change'],
          },
        ],
        codeNoteId: [
          { required: true, message: '请选择失败代码类别', trigger: 'change' },
        ],
        carriers: [
          { required: true, message: '请选择运营商名称', trigger: 'change' },
        ],
      },

      tableDataObj: {
        //列表数据
        total: 0,
        id: 'FailurecodeMag',
        custom: true,
        loading2: false,
        tableData: [],
        tableLabel: [
          // {
          //   prop: "failureCodeId",
          //   showName: "序号",
          //   fixed: 'false',
          //   width:'60'
          // },
          {
            prop: 'failureCode',
            showName: '失败代码',
          },
          {
            prop: 'codeNote',
            showName: '失败代码注释',
          },
          {
            prop: 'carriers',
            showName: '运营商',
          },
          {
            prop: 'codeNoteIndustry',
            showName: '失败代码类别',
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '100', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          // {
          //   optionName: "同步",
          //   type: "",
          //   size: "mini",
          //   optionMethod: "details",
          //   icon: "el-icon-circle-check-outline"
          // },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'Delete',
            color: '#f56c6c',
          },
        ],
      },
    }
  },
  methods: {
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingfailurecode/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //------------------------ 列表信息 ---------------------------------
    //------------------------ 操作列表 ---------------------------------
    getLabel() {
      //获取失败代码类别
      window.api.post(
        window.path.omcs + 'operatingfailurecodenote/page',
        {
          pageSize: 500,
          currentPage: 1,
        },
        (res) => {
          this.codeNotes = res.records
        }
      )
    },
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD 00:00:00')
        this.formInline.endTime =
          this.moment(val[1]).format('YYYY-MM-DD ') + '23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    handelSelection(val) {
      //列表复选框的值
      if (val.length > 0) {
        let arr = []
        val.forEach(item => {
          arr.push(item.failureCodeId)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    //查询功能
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      //重置
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      //提交新增失败代码
      this.$refs[formop].validate((valid) => {
        if (valid) {
          window.api.get(
            window.path.omcs +
              'operatingfailurecode/exists/' +
              this.formop.failureCode +
              '/' +
              this.formop.carriers,
            {},
            (res) => {
              if (res.code == 400) {
                this.$message({
                  message: '失败代码重复，重新输入！',
                  type: 'warning',
                })
              } else {
                this.$confirms.confirmation(
                  'post',
                  '确认执行此操作吗？',
                  window.path.omcs + 'operatingfailurecode',
                  this.formop,
                  () => {
                    this.dialogFormVisible = false
                    this.gettableLIst()
                  }
                )
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //新增
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
      this.getLabel()
    },
    delAll() {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingfailurecode/deleteBatch/' + this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },

    delRow: function (val) {
      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingfailurecode/' + val.row.failureCodeId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    //------------------上传文件开始--------------
    //上传失败代码弹窗
    updatefail() {
      this.dialogFormUpdate = true
      this.getLabel()
    },
    //改变上传文件
    handleChange(file, fileList) {
      let endingCode = file.name //结尾字符
      this.endingName = endingCode.slice(
        endingCode.lastIndexOf('.') + 1,
        endingCode.length
      )
      let isStyle = false //文件格式
      let fileStyle = ['xls', 'xlsx']
      for (let i = 0; i < fileStyle.length; i++) {
        if (this.endingName === fileStyle[i]) {
          isStyle = true
          break
        }
      }
      if (isStyle == true) {
        this.fileList = fileList
      } else {
        this.fileList = []
        this.$message({
          message: '只能上传.xls .xlsx 结尾的文件！',
          type: 'warning',
        })
      }
    },
    //移除上传文件
    handleRemove(file, fileList) {
      this.fileList = []
    },
    //上传成功
    handleAvatarSuccess(res, file) {
      console.log(res)
      this.dialogFormUpdate = false
      this.gettableLIst()
      if (res.code == 200) {
        this.$message({
          message: '文件上传成功！此次共上传' + res.data + '条数据！',
          type: 'success',
        })
      } else {
        this.$message.error('文件上传失败！')
      }
    },
    submitForms(formupcode) {
      //上传失败代码
      this.$refs[formupcode].validate((valid) => {
        if (valid) {
          if (this.fileList.length <= 0) {
            this.$message.error('请至少上传一个文件！')
            return false
          } else {
            this.$confirm('上传文件, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                //上传成功
                this.$refs.uploading.submit()
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消上传',
                })
              })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //上传文件过多提示
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      )
    },
    //---------------------方法结束--------------

    handelOptionButton: function (val) {
      //操作栏
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
    //------------------------ 操作列表 ---------------------------------
  },
  computed: {
    myData() {
      return {
        carriers: this.formupcode.carriers,
        codeNoteId: this.formupcode.codeNoteId,
      }
    },
  },
  mounted() {
    this.myHeader = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    dialogFormUpdate(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.formupcode.resetFields() //清空表单
        this.fileList = []
      }
    },
    dialogFormVisible(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.formop.resetFields() //清空表单
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
