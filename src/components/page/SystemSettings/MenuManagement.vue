<template>
  <div class="container_left">
    <div class="fillet" style="padding: 20px">
      <div class="fillet Statistics-box" style="margin-top: 10px">
        <el-tabs v-model="activeName2" type="card" style="margin-bottom: 6px">
          <el-tab-pane label="后台管理平台" name="1"></el-tab-pane>
          <!-- <el-tab-pane label="客服审核平台" name="2"></el-tab-pane>  -->
          <el-tab-pane label="客户端" name="3"></el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <!--工具栏-->
        <el-form :model="menuForm" :inline="true" ref="menuForm">
          <el-form-item label="菜单名称" prop="name">
            <el-input v-model="menuForm.name" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="菜单类型" prop="type">
            <el-select v-model="menuForm.type" class="input-w">
              <el-option label="全部" value=""></el-option>
              <el-option label="菜单" value="0"></el-option>
              <el-option label="按钮" value="1"></el-option>
            </el-select>
          </el-form-item>
          <div class="boderbottom">
            <el-button type="primary" plain @click="menuQuery()"
              >查询</el-button
            >
            <el-button type="primary" plain @click="menuReload('menuForm')">
              重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header">菜单列表</span>  -->
        <el-button type="primary" @click="handleAdd">添加菜单</el-button>
      </div>

      <!--表格树内容栏-->
      <el-table
        :data="tableTreeDdata"
        border
        size="default"
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column
          prop="id"
          header-align="center"
          align="center"
          width="60"
          label="ID"
        ></el-table-column>
        <table-tree-column
          prop="name"
          treeKey="id"
          label="菜单名称"
          @updateTreeData="updateTreeData"
        ></table-tree-column>
        <el-table-column
          prop="component"
          :show-overflow-tooltip="true"
          label="菜单URL"
        ></el-table-column>
        <el-table-column prop="type" align="center" label="菜单类型" width="90">
          <template v-slot="scope">
            <el-tag
:disable-transitions="true" v-if="scope.row.type === '0'" size="default" type="success"
              >菜单</el-tag
            >
            <el-tag
:disable-transitions="true" v-if="scope.row.type === '1'" size="default" type="info"
              >按钮</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          header-align="center"
          width="80"
          align="center"
          label="图标"
        >
          <template v-slot="scope">
            <i :class="[{ icon: true, iconfont: true }, scope.row.icon]"></i>
          </template>
        </el-table-column>
        <el-table-column
          prop="levels"
          header-align="center"
          align="center"
          label="排序"
          width="60"
        ></el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="140"
          label="操作"
        >
          <template v-slot="scope">
            <el-button @click="handleEdit(scope.row)" link style="color: #409eff;"
              ><el-icon><EditPen /></el-icon> 编辑
            </el-button>
            <el-button
              link
              @click="handleDelete(scope.row)"
              style="color: #f56c6c"
              ><el-icon><CircleCloseFilled /></el-icon>
              删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 新增修改界面 -->
      <el-dialog
        :title="title"
        width="520px"
        v-model="dialogVisible"
        :close-on-click-modal="false"
      >
        <el-form
          :model="dataForm1"
          ref="dataForm1"
          label-width="80px"
          style="padding: 0 20px"
        >
          <el-form-item label="菜单类型" prop="type">
            <el-radio-group v-model="dataForm1.type">
              <el-radio value="0">菜单</el-radio>
              <el-radio value="1">按钮</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          @keyup.enter="submitForm()"
          label-width="80px"
          style="padding: 0 20px; height: 280px"
          class="addMenu"
        >
          <el-form-item label="父级菜单" prop="selectedOptions">
            <el-cascader
              style="display: inline-block; width: 100%"
              :options="addMenu"
              v-model="dataForm.selectedOptions"
              :props="props"
              @change="handleChange"
              change-on-select
              :show-all-levels="false"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item
            label="菜单名称"
            prop="name"
            v-if="dataForm1.type === '0'"
          >
            <el-input
              v-model="dataForm.name"
              placeholder="请填写菜单名称"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="按钮名称"
            prop="name"
            v-if="dataForm1.type === '1'"
          >
            <el-input
              v-model="dataForm.name"
              placeholder="请填写按钮名称"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="dataForm1.type === '0'"
            label="菜单url"
            prop="component"
          >
            <el-input
              v-model="dataForm.component"
              placeholder="菜单url"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="dataForm1.type === '0'"
            label="菜单路由"
            prop="component"
          >
            <el-input v-model="dataForm.url" placeholder="菜单路由"></el-input>
          </el-form-item>
          <el-form-item
            v-if="dataForm1.type === '0'"
            label="菜单图标"
            prop="icon"
          >
            <el-input
              v-model="dataForm.icon"
              v-popover:iconListPopover
              :readonly="false"
              placeholder="菜单图标名称（如：fa fa-home fa-lg）"
              class="icon-list__input"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="权限标识"
            v-if="dataForm1.type === '1'"
            prop="permission"
          >
            <el-input
              v-model="dataForm.permission"
              placeholder="权限标识"
            ></el-input>
          </el-form-item>
          <el-form-item label="菜单排序" prop="levels">
            <el-input-number
              v-model="dataForm.levels"
              :min="1"
              :max="100"
              placeholder="菜单排序"
            ></el-input-number>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm()">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTreeColumn from './TableTreeColumn.vue'
export default {
  components: {
    TableTreeColumn
  },
  data() {
    return {
      isFirstEnter: false,
      loading: false,
      menuForm: {
        //查询框的值
        name: '',
        type: '',
        flag: 1,
        platform: '1', //平台标识符
      },
      menuForms: {
        //查询框的值
        name: '',
        type: '',
        flag: 1,
        platform: '1', //平台标识符
      },
      activeName2: '1',
      addMenu: [],
      tableTreeDdata: [],
      dialogVisible: false,
      title: '新增',
      dataForm1: {
        type: '0',
      },
      dataForm: {
        menuId: '',
        name: '',
        parentId: 0,
        selectedOptions: [], //所选父级菜单
        parentName: '',
        component: '',
        url: '',
        platform: '',
        sort: 0,
        icon: '',
        levels: 1,
        permission: '',
      },
      menuName: '',
      props: {
        value: 'id',
        label: 'name',
        children: 'children',
        checkStrictly: true
      },
      echoDisplay: {}, //回显的值
      dataRule: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' },
          // { pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+$/, message: '由中文，英文和数字组成，不允许输入空格等特殊符号' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        selectedOptions: [
          { required: true, message: '父级菜单不能为空', trigger: 'change' },
        ],
        permission: [
          { required: true, message: '权限标识不能为空', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '菜单类型不能为空', trigger: 'blur' },
        ],
        component: [
          {
            min: 1,
            max: 40,
            message: '长度在 1 到 40 个字符',
            trigger: 'blur',
          },
        ],
        // icon: [{ min: 1, max: 40, message: '长度在 1 到 40 个字符', trigger: 'blur' }],
        levels: [
          { required: true, message: '菜单等级不能为空', trigger: 'blur' },
        ],
      },
    }
  },
  methods: {
    // 数据展开
    updateTreeData(data) {
      if (data) {
        this.tableTreeDdata = data
      }
    },
    //查询
    menuQuery() {
      this.findTreeDatas()
    },
    //重置
    menuReload(formName) {
      this.$refs[formName].resetFields()
      this.findTreeDatas()
    },
    // 获取列表数据
    findTreeDatas: function () {
      Object.assign(this.menuForms, this.menuForm)
      window.api.post(window.path.upms + 'menu/allTree', this.menuForms, (res) => {
        this.tableTreeDdata = res
      })
    },
    //弹框父级菜单下拉框数据
    findTreeData: function () {
      window.api.post(
        window.path.upms + 'menu/allTree',
        {
          platform: this.activeName2,
          flag: 1,
        },
        (res) => {
          this.addMenu = [] //置空
          let parent = {
            id: -1,
            name: '顶级菜单',
          }
          let datas = res

          for (let i = 0; i < datas.length; i++) {
            if (datas[i].children.length == 0) {
              delete datas[i].children
            } else {
              for (let j = 0; j < datas[i].children.length; j++) {
                if (datas[i].children[j].children.length == 0) {
                  delete datas[i].children[j].children
                } else {
                  for (
                    let k = 0;
                    k < datas[i].children[j].children.length;
                    k++
                  ) {
                    if (datas[i].children[j].children[k].children.length == 0) {
                      delete datas[i].children[j].children[k].children
                    } else {
                      for (
                        let Q = 0;
                        Q < datas[i].children[j].children[k].children.length;
                        Q++
                      ) {
                        if (
                          datas[i].children[j].children[k].children[Q].children
                            .length == 0
                        ) {
                          delete datas[i].children[j].children[k].children[Q]
                            .children
                        } else {
                          for (
                            let y = 0;
                            y <
                            datas[i].children[j].children[k].children[Q]
                              .children.length;
                            y++
                          ) {
                            if (
                              datas[i].children[j].children[k].children[Q]
                                .children[y].children.length == 0
                            ) {
                              delete datas[i].children[j].children[k].children[
                                Q
                              ].children[y].children
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          parent.children = datas
          if (parent.children.length == 0) {
            delete parent.children
          }
          this.addMenu = [parent]
        }
      )
    },
    getTreeDeepArr(key, treeData) {
      let arr = [] // 在递归时操作的数组
      let returnArr = [] // 存放结果的数组
      let depth = 0 // 定义全局层级
      // 定义递归函数
      function childrenEach(childrenData, depthN) {
        for (var j = 0; j < childrenData.length; j++) {
          depth = depthN // 将执行的层级赋值 到 全局层级
          arr[depthN] = childrenData[j].id
          if (childrenData[j].id == key) {
            returnArr = arr.slice(0, depthN + 1) //将目前匹配的数组，截断并保存到结果数组，
            break
          } else {
            if (childrenData[j].children) {
              depth++
              childrenEach(childrenData[j].children, depth)
            }
          }
        }
        return returnArr
      }
      return childrenEach(treeData, depth)
    },
    // 新增
    handleAdd: function () {
      this.title = '新增'
      this.dialogVisible = true
      this.dataForm.platform = this.activeName2
    },
    // 编辑
    handleEdit: function (row) {
      this.title = '编辑'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.dataForm1.resetFields()
        this.$refs.dataForm.resetFields()
        Object.assign(this.dataForm, row)
        this.echoDisplay = row // 回显的值
        this.dataForm.permission = row.code
        this.dataForm.menuId = row.id
        this.menuName = row.name //标记符（记录菜单名称）
        this.dataForm1.type = this.echoDisplay.type
        this.dataForm.selectedOptions = this.getTreeDeepArr(
          row.parentId,
          this.addMenu
        )
      })
    },
    // 删除
    handleDelete(row) {
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续?',
        window.path.upms + 'menu/' + row.id,
        {},
        (res) => {
          this.findTreeDatas()
          this.findTreeData()
        }
      )
    },
    // 表单提交
    submitForm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let dataForms = Object.assign({}, this.dataForm)
          dataForms.type = this.dataForm1.type //菜单还是按钮
          dataForms.sort = this.dataForm.selectedOptions.length //菜单样式的级别
          if (this.title == '新增') {
            delete dataForms.menuId
            delete dataForms.id
            this.sendAjax('post', '确定新增菜单', dataForms)
          } else {
            if (this.dataForm.type === '0') {
              delete dataForms.permission
            } else {
              delete dataForms.component
              delete dataForms.url
            }
            if (this.menuName == this.dataForm.name) {
              this.$confirms.confirmation(
                'put',
                '确定编辑菜单',
                window.path.upms + 'menu',
                dataForms,
                (res) => {
                  this.findTreeDatas()
                  this.findTreeData()
                  this.dialogVisible = false //隐藏弹窗
                }
              )
            } else {
              this.sendAjax('put', '确定编辑菜单', dataForms)
            }
          }
        }
      })
    },
    //新增，编辑发送请求
    sendAjax(type, tips, params) {
      // window.api.get(window.path.upms+'menu/exists/'+this.dataForm.name+'/'+this.activeName2,{},res=>{
      //     if(res.code != 200){
      //         this.$message({
      //             message: '此菜单已存在，切勿重复！',
      //             type: 'warning'
      //         });
      //     }else{
      this.$confirms.confirmation(
        type,
        tips,
        window.path.upms + 'menu',
        params,
        (res) => {
          this.findTreeDatas()
          this.findTreeData()
          this.dialogVisible = false //隐藏弹窗
        }
      )
      //     }
      // })
    },
    // 父级菜单选中的ID
    handleChange(value) {
      this.dataForm.parentId = value[value.length - 1]
    },
  },
  // mounted() {
  //     this.findTreeDatas();
  //     this.findTreeData();
  // },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.findTreeDatas()
        this.findTreeData()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.findTreeDatas()
      this.findTreeData()
    })
  },
  // beforeDestroy(){
  //     router.app.message && router.app.message.close();
  // },
  watch: {
    //监听弹框是否关闭
    dialogVisible(val) {
      if (val == false) {
        this.dataForm1.type = '0'
        this.$refs.dataForm.resetFields()
        this.dataForm.menuId = ''
        this.dataForm.name = ''
        this.dataForm.parentId = ''
        this.dataForm.parentName = ''
        this.dataForm.component = ''
        this.dataForm.sort = ''
        this.dataForm.icon = ''
        this.dataForm.permission = ''
        this.dataForm.selectedOptions = []
      }
    },
    activeName2(val) {
      this.menuForm.platform = val
      this.findTreeDatas()
      this.findTreeData()
    },
  },
  emits: ['update:value'],
}
</script>

<style scoped>
.setMenu-title {
  margin: 10px 0;
  padding-left: 5px;
}
</style>

<style>
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
.addMenu .el-cascader__label {
  padding: 2px 25px 0 15px;
}
.el-table--mini td {
  padding: 2px 0 !important;
}
</style>
