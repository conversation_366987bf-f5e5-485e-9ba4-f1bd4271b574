<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户类型" prop="roleId">
            <el-select
              v-model="formInline.roleId"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="不限" value=""></el-option>
              <el-option label="终端用户" value="14"></el-option>
              <el-option label="管理商用户" value="12"></el-option>
              <el-option label="子用户" value="13"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="用户名" prop="consumerName">
            <el-input
              v-model="formInline.consumerName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>

          <el-form-item label="公司名" prop="conCompName">
            <el-input
              v-model="formInline.conCompName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formInline.status"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="签名" prop="signature">
            <el-input
              v-model="formInline.signature"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="扩展号" prop="ext">
            <el-input
              v-model="formInline.ext"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道号" prop="channel">
            <el-input
              v-model="formInline.channel"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建人" prop="createName">
            <el-input
              v-model="formInline.createName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="time">
            <el-date-picker
              class="input-time"
              v-model="formInline.time"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              @change="hande"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <!-- <div class="boderbottom">
        
            <el-button type="primary" plain style @click="download">导出数据</el-button>
          </div> -->
      <div class="Signature-search-fun">
        <!-- <span class="Signature-list-header">签名扩展号列表</span> -->
        <el-button type="primary" @click="addopt">创建签名扩展</el-button>
        <el-button
          type="primary"
          style="margin-left: 15px"
          @click="SignafileDialog = true"
          >签名扩展导入</el-button
        >
        <el-button
          type="danger"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delAll"
          >批量删除</el-button
        >
        <el-button
          type="primary"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delStatus(1)"
          >批量启用</el-button
        >
        <el-button
          type="warning"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delStatus(2)"
          >批量停用</el-button
        >
        <el-button
          type="primary"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="Batchtest()"
          >批量测试</el-button
        >
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
          @save="save"
        ></table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 签名扩展导入 -->
      <el-dialog
        title="签名扩展导入"
        v-model="SignafileDialog"
        :close-on-click-modal="false"
        width="520px"
      >
        <file-upload
          style="display: inline-block"
          :action="actionUrl"
          :limit="1"
          :showfileList="true"
          :fileStyle="fileStyle"
          :del="del1"
          :istip="false"
          :tip="tip"
          @fileup="fileup"
          @fileupres="fileupres"
          @onProgress="onProgress"
          >选择上传文件</file-upload
        >
        <div v-if="Progressflag" style="text-align: center; font-size: 35px">
          <el-icon class="is-loading">
            <Loading />
          </el-icon>
        </div>
        <div style="margin-top: 10px" v-if="failNum != 0">
          <span style="font-size: 16px"> 失败数：</span
          ><span>{{ failNum }}</span>
        </div>
        <div
          style="margin-top: 10px; word-wrap: break-word"
          v-if="failNum != 0"
        >
          <span style="font-size: 16px">失败行数：</span
          ><span>{{ failRow }}</span>
        </div>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button @click="SignafileDialog = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 新增签名 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <!-- <el-form-item label="用户名" label-width="80px" prop="userName" :rules="filter_rules({required:true,type:'userName',min:4,max:20,message:'必填项'})"> -->
          <el-form-item label="用户名" label-width="80px" prop="userName">
            <el-input
              v-model="formop.userName"
              autocomplete="off"
              :disabled="flagPut"
              @blur="compName"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称" label-width="80px" prop="conCompName">
            <el-input
              v-model="formop.conCompName"
              autocomplete="off"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="签名" label-width="80px" prop="signature">
                <el-input v-model="formop.signature" autocomplete="off" ></el-input>
              </el-form-item> -->
          <el-form-item label="签名" label-width="80px" prop="signature">
            <el-select
              v-model="formop.signature"
              placeholder="请选择"
              filterable
              allow-create
              default-first-option
              :disabled="flagPut"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in singName"
                :label="item"
                :value="item"
                :key="index"
                :disabled="flagPut"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扩展号" label-width="80px" prop="ext">
            <el-input v-model="formop.ext" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="通道号" label-width="80px" prop="channel">
            <el-input v-model="formop.channel" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增标签商结束 -->

      <!-- 批量测试 -->
      <el-dialog
        title="批量测试"
        v-model="dialogBatchTest"
        :close-on-click-modal="false"
        style="padding: 0 28px 0 20px"
        width="520px"
      >
        <el-form
          :model="formoTest"
          :rules="rulesTest"
          ref="formoTest"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="手机号" label-width="80px" prop="mobile">
            <el-input v-model="formoTest.mobile" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="通道号" label-width="80px" prop="channelId">
            <el-input
              v-model="formoTest.channelId"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitBatchtest()"
              >提 交</el-button
            >
            <el-button @click="dialogBatchTest = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 批量测试 -->
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import TableTem from '../../../../publicComponents/TableTem.vue'
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
export default {
  components: {
    TableTem,
    FileUpload,
    ChannelView,
  },
  name: 'SignatureNumMag',
  data() {
    return {
      actionUrl: window.path.omcs + 'operatingSignature/importSignExt',
      isFirstEnter: false,
      ChannelData: '', //传递通道值
      titleMap: {
        add: '添加签名扩展',
        edit: '编辑签名扩展',
      },
      flagPut: '', //用户名禁止状态 签名禁止状态
      dialogStatus: '', //新增编辑标题
      singName: [], //接受标签列表
      dialogFormVisible: false, //新增弹出框显示隐藏
      SignafileDialog: false,
      dialogBatchTest: false, //批量测试
      //查询表单
      formInline: {
        roleId: '',
        consumerName: '',
        conCompName: '',
        status: '',
        signature: '',
        channel: '',
        ext: '',
        createName: '',
        isDown: 2,
        //时间
        time: [],
        createStartTime: '',
        createEndTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        roleId: '',
        consumerName: '',
        conCompName: '',
        status: '',
        signature: '',
        channel: '',
        ext: '',
        createName: '',
        isDown: 2,
        //时间
        time: [],
        createStartTime: '',
        createEndTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //批量测试
      formoTest: {
        channelId: '',
        mobile: '',
      },
      rulesTest: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确在手机号',
          },
        ],
        channelId: [
          { required: true, message: '请输入通道号', trigger: 'change' },
          {
            pattern: /^([0-9]*[1-9][0-9]*)(,([0-9]*[1-9][0-9]*))*/,
            message: '含有非法字符（只能输入数字或逗号！）',
          },
        ],
      },
      //添加列表
      formop: {
        userName: '',
        conCompName: '',
        signature: '',
        channel: '',
        ext: '',
      },
      signatureId: '', //列表行id
      selectId: '', //批量操作选中id
      tableRow: '', //当前行列表数据
      rules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'change' },
        ],
        conCompName: [
          { required: true, message: '请展示公司名称', trigger: 'change' },
        ],
        signature: [
          { required: true, message: '请选择签名', trigger: 'change' },
        ],
        channel: [
          { required: false, message: '请输入通道号', trigger: 'change' },
          {
            pattern: /^([0-9]*[1-9][0-9]*)(,([0-9]*[1-9][0-9]*))*/,
            message: '含有非法字符（只能输入数字或逗号！）',
          },
        ],
        ext: [
          { required: true, message: '请输入扩展号', trigger: 'change' },
          { min: 1, max: 12, message: '长度在 1 到 12位', trigger: 'change' },
          { pattern: /^[0-9]+$/, message: '含有非法字符（只能输入数字！）' },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'signatureId',
            showName: 'ID',
            fixed: false,
            width: '70px',
          },
          {
            prop: 'userName',
            showName: '用户名',
            fixed: false,
            showColorTag: {
              color: '#3BB19C',
            },
          },
          {
            prop: 'signature',
            showName: '签名',
            fixed: false,
          },
          {
            prop: 'ext',
            showName: '扩展号',
            fixed: false,
          },
          {
            prop: 'channel',
            showName: '通道号',
            Channel: true,
            fixed: false,
          },
          {
            prop: 'status',
            showName: '状态',
            fixed: false,
            width: '50px',
            formatData: function (val) {
              return val == '1' ? '启用' : '停用'
            },
            showCondition: {
              condition: '2',
            },
          },
          {
            prop: 'roleId',
            showName: '用户类型',
            fixed: false,
            width: '90px',
            formatData: function (val) {
              if (val == 12) {
                return (val = '管理商用户')
              }
              if (val == 13) {
                return (val = '子用户')
              }
              if (val == 14) {
                return (val = '终端用户')
              }
              // if (val==4) {
              //   return (val = "V5终端")
              // }
              // if (val==5) {
              //   return (val = "验证码管理商")
              // }
              // if (val==6) {
              //   return (val = "验证码终端")
              // }
              // if (val==7) {
              //   return (val = "验证码子用户")
              // }
              // if (val==9) {
              //   return (val = "终端")
              // }
              // if (val==10) {
              //   return (val = "管理商")
              // }
            },
          },
          // {
          //   prop: "conCompName",
          //   showName: "公司名称",
          //   fixed: false
          // },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
            width: '170px',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '140', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'status', //关联的表格属性
            contactData: '1', //关联的表格属性-值
            optionName: '停用', //按钮的显示文字
            optionMethod: 'stop', //按钮的方法
            icon: 'el-icon-circle-close-outline', //按钮图标
            optionButtonColor: 'orange', //按钮颜色
            otherOptionName: '启用', //其他条件的按钮显示文字
            otherOptionMethod: 'start', //其他条件的按钮方法
            otherIcon: 'el-icon-circle-check-outline', //其他条件按钮的图标
            optionOtherButtonColor: '#16A589', //其他条件按钮的颜色
          },
          {
            contactCondition: 'status', //关联的表格属性
            contactData: '1', //关联的表格属性-值
            optionName: '编辑', //按钮的显示文字
            optionMethod: 'details', //按钮的方法
            icon: 'EditPen', //按钮图标
            optionButtonColor: '#16A589', //按钮颜色
          },
          {
            contactCondition: 'status', //关联的表格属性
            contactData: '2', //关联的表格属性-值
            optionName: '删除', //按钮的显示文字
            optionMethod: 'dellog', //按钮的方法
            icon: 'el-icon-delete', //按钮图标
            optionButtonColor: '#f56c6c', //按钮颜色
          },
        ],
        tableOptions: [
          //操作栏不加状态
        ],
      },
      Progressflag: false,
      failNum: '',
      failRow: '',
      fileStyle: {
        size: 245678943234,
        style: ['xlsx', 'xls'],
      },
      tip: '仅支持.xlsx .xls 等格式',
      del1: true, //关闭弹框时清空图片
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingSignature/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },

    hande: function (val) {
      if (val) {
        //获取查询时间框的值
        this.formInline.createStartTime = this.moment(val[0]).format(
          'YYYY-MM-DD '
        )
        this.formInline.createEndTime =
          this.moment(val[1]).format('YYYY-MM-DD ') + '23:59:59'
      } else {
        this.formInline.createStartTime = ''
        this.formInline.createEndTime = ''
      }
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      // let wordsCount = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].signatureId)
        // wordsCount.push(val[i].wordsCount);
      }
      this.selectId = selectId.join(',') //批量操作选中id
      // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },

    // 导出数据
    download() {
      let aa = Object.assign({}, this.tabelAlllist)
      aa.isDown = 1
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingSignature/download',
          aa,
          '签名拓展号报表.xlsx'
        )
      }
    },
    Reload() {
      //重置
      this.$refs.formInline.resetFields()
      this.formInline.createStartTime = ''
      this.formInline.createEndTime = ''
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //根据用户名查公司和签名
    compName() {
      this.formop.signature = ''
      this.singName = []
      window.api.get(
        window.path.omcs + 'operatingSignature/compName/' + this.formop.userName,
        {},
        (res) => {
          if (res.code != 400) {
            this.formop.conCompName = res.data.compName
            this.singName = res.data.signList
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
            this.formop.conCompName = ''
            this.singName = []
          }
        }
      )
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //添加
            window.api.get(
              window.path.omcs +
                'operatingSignature/checkSignExt?ext=' +
                this.formop.ext +
                '&sign=' +
                this.formop.signature,
              {},
              (res) => {
                if (res.code == 400) {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                } else {
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs + 'operatingSignature',
                    this.formop,
                    () => {
                      this.dialogFormVisible = false
                      this.gettableLIst()
                    }
                  )
                }
              }
            )
          } else {
            //编辑
            this.formop.signatureId = this.signatureId
            if (this.tableRow.ext == this.formop.ext) {
              this.$confirms.confirmation(
                'put',
                '确认执行此操作吗？',
                window.path.omcs + 'operatingSignature',
                this.formop,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              window.api.get(
                window.path.omcs +
                  'operatingSignature/checkSignExt?ext=' +
                  this.formop.ext,
                {},
                (res) => {
                  if (res.code == 400) {
                    this.$message({
                      message: res.msg,
                      type: 'warning',
                    })
                  } else {
                    this.$confirms.confirmation(
                      'put',
                      '确认执行此操作吗？',
                      window.path.omcs + 'operatingSignature',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  }
                }
              )
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.flagPut = false
      this.dialogStatus = 'add'
    },
    //移除文件
    fileup(val) {
      this.Progressflag = false
      this.failNum = ''
      this.failRow = ''
    },
    onProgress(val, val1, val2, val3) {
      this.Progressflag = true
    },
    //文件上传成功
    fileupres(val, val2) {
      this.Progressflag = false
      if (val.data.failNum != 0) {
        this.$message({
          message: '签名导入失败！',
          type: 'warning',
        })
        this.failNum = val.data.failNum
        this.failRow = val.data.failRow
      } else {
        this.$message({
          message: '签名导入成功！',
          type: 'success',
        })
      }
    },
    delAll(val) {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingSignature',
        {
          ids: this.selectId,
        },
        () => {
          this.gettableLIst()
        }
      )
    },

    delStatus(val) {
      //批量启用停用
      if (val == 1) {
        //启用
        this.$confirms.confirmation(
          'put',
          '此操作将启用所选数据, 是否继续？',
          window.path.omcs +
            'operatingSignature/status?ids=' +
            this.selectId +
            '&status=' +
            val,
          {},
          () => {
            this.gettableLIst()
          }
        )
      } else if (val == 2) {
        this.$confirms.confirmation(
          'put',
          '此操作将停用所选数据, 是否继续？',
          window.path.omcs +
            'operatingSignature/status?ids=' +
            this.selectId +
            '&status=' +
            val,
          {},
          () => {
            this.gettableLIst()
          }
        )
      }
    },
    detailsRow(val) {
      //编辑
      this.dialogFormVisible = true
      this.flagPut = true
      this.dialogStatus = 'edit'
      this.signatureId = val.row.signatureId
      this.tableRow = val.row
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val.row) //编辑框赋值
      })
    },
    delRow: function (val) {
      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingSignature',
        {
          ids: val.row.signatureId,
        },
        () => {
          this.gettableLIst()
        }
      )
    },
    //操作状态功能（启用停用）
    delState: function (val, index) {
      this.$confirms.confirmation(
        'put',
        '确认执行启用或停用操作吗？',
        window.path.omcs +
          'operatingSignature/status?ids=' +
          val.row.signatureId +
          '&status=' +
          index,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    // 批量测试
    Batchtest() {
      this.dialogBatchTest = true
    },
    submitBatchtest() {
      this.formoTest.ids = this.selectId
      this.$confirms.confirmation(
        'post',
        '请确认操作！',
        window.path.omcs + 'operatingSignature/ext/check',
        this.formoTest,
        () => {
          this.gettableLIst()
          this.dialogBatchTest = false
        }
      )
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
      if (val.methods == 'start') {
        this.delState(val, 1)
      }
      if (val.methods == 'stop') {
        this.delState(val, 2)
      }
      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist:{
    //     handler(){
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate:true
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.formop.userName = ''
        this.$refs.formop.resetFields()
      }
    },
    'formop.userName'(val) {
      if (val == '') {
        this.formop.userName = ''
      }
    },
    SignafileDialog(val) {
      this.del1 = true
      if (val == false) {
        this.del1 = false
        this.failNum = ''
        this.failRow = ''
      }
    },
    dialogBatchTest(val) {
      if (val == false) {
        this.$refs.formoTest.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.sensitive {
  margin: 40px 0 20px 10px;
}
</style>
