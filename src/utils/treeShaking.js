/**
 * Tree Shaking 优化配置
 * 提供按需导入和未使用代码消除的工具函数
 */

/**
 * Element Plus 按需导入配置
 * 避免导入整个UI库，只导入使用的组件
 */
export const optimizedElementImports = {
  // 基础组件
  'el-button': () => import('element-plus/es/components/button/index'),
  'el-input': () => import('element-plus/es/components/input/index'),
  'el-select': () => import('element-plus/es/components/select/index'),
  'el-option': () => import('element-plus/es/components/option/index'),
  'el-form': () => import('element-plus/es/components/form/index'),
  'el-form-item': () => import('element-plus/es/components/form-item/index'),
  
  // 数据展示
  'el-table': () => import('element-plus/es/components/table/index'),
  'el-table-column': () => import('element-plus/es/components/table-column/index'),
  'el-pagination': () => import('element-plus/es/components/pagination/index'),
  'el-tag': () => import('element-plus/es/components/tag/index'),
  
  // 反馈组件
  'el-dialog': () => import('element-plus/es/components/dialog/index'),
  'el-message': () => import('element-plus/es/components/message/index'),
  'el-message-box': () => import('element-plus/es/components/message-box/index'),
  'el-loading': () => import('element-plus/es/components/loading/index'),
  
  // 导航组件
  'el-menu': () => import('element-plus/es/components/menu/index'),
  'el-menu-item': () => import('element-plus/es/components/menu-item/index'),
  'el-submenu': () => import('element-plus/es/components/sub-menu/index'),
  'el-breadcrumb': () => import('element-plus/es/components/breadcrumb/index'),
  'el-tabs': () => import('element-plus/es/components/tabs/index'),
  
  // 布局组件
  'el-row': () => import('element-plus/es/components/row/index'),
  'el-col': () => import('element-plus/es/components/col/index'),
  'el-card': () => import('element-plus/es/components/card/index'),
  'el-container': () => import('element-plus/es/components/container/index')
}

/**
 * Lodash 按需导入优化
 * 避免导入整个lodash库，只导入使用的函数
 */
export const optimizedLodashImports = {
  // 数组方法
  'chunk': () => import('lodash/chunk'),
  'compact': () => import('lodash/compact'),
  'concat': () => import('lodash/concat'),
  'difference': () => import('lodash/difference'),
  'drop': () => import('lodash/drop'),
  'flatten': () => import('lodash/flatten'),
  'uniq': () => import('lodash/uniq'),
  'union': () => import('lodash/union'),
  
  // 对象方法
  'assign': () => import('lodash/assign'),
  'clone': () => import('lodash/clone'),
  'cloneDeep': () => import('lodash/cloneDeep'),
  'merge': () => import('lodash/merge'),
  'omit': () => import('lodash/omit'),
  'pick': () => import('lodash/pick'),
  'get': () => import('lodash/get'),
  'set': () => import('lodash/set'),
  
  // 函数方法
  'debounce': () => import('lodash/debounce'),
  'throttle': () => import('lodash/throttle'),
  'once': () => import('lodash/once'),
  'memoize': () => import('lodash/memoize'),
  
  // 工具方法
  'isArray': () => import('lodash/isArray'),
  'isObject': () => import('lodash/isObject'),
  'isEmpty': () => import('lodash/isEmpty'),
  'isNil': () => import('lodash/isNil')
}

/**
 * ECharts 按需导入配置
 * 只导入需要的图表类型和组件
 */
export const optimizedEchartsImports = {
  // 核心
  'core': () => import('echarts/core'),
  
  // 图表类型
  'BarChart': () => import('echarts/charts').then(m => m.BarChart),
  'LineChart': () => import('echarts/charts').then(m => m.LineChart),
  'PieChart': () => import('echarts/charts').then(m => m.PieChart),
  'ScatterChart': () => import('echarts/charts').then(m => m.ScatterChart),
  'RadarChart': () => import('echarts/charts').then(m => m.RadarChart),
  'TreeChart': () => import('echarts/charts').then(m => m.TreeChart),
  'TreemapChart': () => import('echarts/charts').then(m => m.TreemapChart),
  'GraphChart': () => import('echarts/charts').then(m => m.GraphChart),
  'GaugeChart': () => import('echarts/charts').then(m => m.GaugeChart),
  
  // 组件
  'TitleComponent': () => import('echarts/components').then(m => m.TitleComponent),
  'TooltipComponent': () => import('echarts/components').then(m => m.TooltipComponent),
  'GridComponent': () => import('echarts/components').then(m => m.GridComponent),
  'LegendComponent': () => import('echarts/components').then(m => m.LegendComponent),
  'ToolboxComponent': () => import('echarts/components').then(m => m.ToolboxComponent),
  'DataZoomComponent': () => import('echarts/components').then(m => m.DataZoomComponent),
  'VisualMapComponent': () => import('echarts/components').then(m => m.VisualMapComponent),
  'TimelineComponent': () => import('echarts/components').then(m => m.TimelineComponent),
  'CalendarComponent': () => import('echarts/components').then(m => m.CalendarComponent),
  
  // 渲染器
  'CanvasRenderer': () => import('echarts/renderers').then(m => m.CanvasRenderer),
  'SVGRenderer': () => import('echarts/renderers').then(m => m.SVGRenderer)
}

/**
 * 创建优化的ECharts实例
 * @param {Array} requiredCharts - 需要的图表类型
 * @param {Array} requiredComponents - 需要的组件
 * @param {string} renderer - 渲染器类型 ('canvas' | 'svg')
 * @returns {Promise} ECharts实例
 */
export async function createOptimizedECharts(
  requiredCharts = ['BarChart', 'LineChart'], 
  requiredComponents = ['TitleComponent', 'TooltipComponent', 'GridComponent', 'LegendComponent'],
  renderer = 'canvas'
) {
  try {
    // 动态导入ECharts核心
    const { use } = await optimizedEchartsImports.core()
    
    // 导入所需的图表类型
    const chartImports = await Promise.all(
      requiredCharts.map(chart => optimizedEchartsImports[chart]())
    )
    
    // 导入所需的组件
    const componentImports = await Promise.all(
      requiredComponents.map(component => optimizedEchartsImports[component]())
    )
    
    // 导入渲染器
    const rendererImport = await optimizedEchartsImports[
      renderer === 'svg' ? 'SVGRenderer' : 'CanvasRenderer'
    ]()
    
    // 注册所有导入的功能
    use([
      ...chartImports,
      ...componentImports,
      rendererImport
    ])
    
    return { use }
  } catch (error) {
    console.error('Failed to create optimized ECharts:', error)
    // 降级到完整导入
    return import('echarts')
  }
}

/**
 * 创建优化的Lodash工具集
 * @param {Array} requiredMethods - 需要的方法名数组
 * @returns {Promise<Object>} 优化的Lodash对象
 */
export async function createOptimizedLodash(requiredMethods = []) {
  const optimizedLodash = {}
  
  const importPromises = requiredMethods.map(async (method) => {
    if (optimizedLodashImports[method]) {
      try {
        const importedMethod = await optimizedLodashImports[method]()
        optimizedLodash[method] = importedMethod.default || importedMethod
      } catch (error) {
        console.warn(`Failed to import lodash method ${method}:`, error)
      }
    }
  })
  
  await Promise.all(importPromises)
  return optimizedLodash
}

/**
 * 分析和检测未使用的代码
 */
export class UnusedCodeDetector {
  constructor() {
    this.usedModules = new Set()
    this.importedModules = new Set()
  }

  /**
   * 记录模块使用
   * @param {string} moduleName - 模块名称
   */
  markAsUsed(moduleName) {
    this.usedModules.add(moduleName)
  }

  /**
   * 记录模块导入
   * @param {string} moduleName - 模块名称
   */
  markAsImported(moduleName) {
    this.importedModules.add(moduleName)
  }

  /**
   * 获取未使用的模块
   * @returns {Array} 未使用的模块列表
   */
  getUnusedModules() {
    return Array.from(this.importedModules).filter(
      module => !this.usedModules.has(module)
    )
  }

  /**
   * 生成使用报告
   * @returns {Object} 使用情况报告
   */
  generateReport() {
    const totalImported = this.importedModules.size
    const totalUsed = this.usedModules.size
    const unused = this.getUnusedModules()
    
    return {
      totalImported,
      totalUsed,
      unused,
      usageRate: totalImported > 0 ? (totalUsed / totalImported * 100).toFixed(2) : 0,
      recommendations: this.generateRecommendations(unused)
    }
  }

  /**
   * 生成优化建议
   * @param {Array} unused - 未使用的模块
   * @returns {Array} 建议列表
   */
  generateRecommendations(unused) {
    const recommendations = []
    
    if (unused.length > 0) {
      recommendations.push({
        type: 'remove-unused',
        message: `移除${unused.length}个未使用的导入`,
        modules: unused,
        impact: 'medium'
      })
    }

    if (this.importedModules.has('lodash') && !this.usedModules.has('lodash')) {
      recommendations.push({
        type: 'optimize-lodash',
        message: '使用按需导入替代完整的lodash库',
        impact: 'high'
      })
    }

    if (this.importedModules.has('element-plus') && !this.usedModules.has('element-plus')) {
      recommendations.push({
        type: 'optimize-element',
        message: '使用按需导入替代完整的Element Plus库',
        impact: 'high'
      })
    }

    return recommendations
  }
}

/**
 * 全局代码使用检测器
 */
export const codeDetector = new UnusedCodeDetector()

/**
 * 自动Tree Shaking优化配置
 */
export const treeShakingConfig = {
  // 标记为side-effect-free的包
  sideEffectFreePackages: [
    'lodash',
    'lodash-es',
    'ramda',
    'date-fns',
    '@vueuse/core'
  ],
  
  // 需要保留side effects的包
  preserveSideEffects: [
    'element-plus/theme-chalk/index.css',
    'vxe-table/lib/style.css',
    'normalize.css'
  ],
  
  // 优化提示
  optimizationHints: {
    // 当导入整个库时给出警告
    warnOnFullLibraryImport: true,
    // 建议按需导入的库
    suggestTreeShaking: [
      'lodash',
      'element-plus',
      'echarts',
      'moment',
      'antd'
    ]
  }
}

/**
 * 运行时Tree Shaking检查
 * @param {string} importPath - 导入路径
 * @param {any} importedValue - 导入的值
 */
export function checkTreeShaking(importPath, importedValue) {
  if (process.env.NODE_ENV === 'development') {
    const config = treeShakingConfig
    
    // 检查是否导入了整个库
    if (config.optimizationHints.suggestTreeShaking.some(lib => importPath.includes(lib))) {
      if (typeof importedValue === 'object' && Object.keys(importedValue).length > 10) {
        console.warn(
          `[Tree Shaking Warning] 建议对 ${importPath} 使用按需导入以减少包大小`
        )
      }
    }
  }
}

/**
 * Bundle分析工具
 */
export class BundleAnalyzer {
  constructor() {
    this.chunkSizes = new Map()
    this.loadTimes = new Map()
  }

  /**
   * 记录chunk大小
   * @param {string} chunkName - chunk名称
   * @param {number} size - 大小（字节）
   */
  recordChunkSize(chunkName, size) {
    this.chunkSizes.set(chunkName, size)
  }

  /**
   * 记录加载时间
   * @param {string} chunkName - chunk名称
   * @param {number} loadTime - 加载时间（毫秒）
   */
  recordLoadTime(chunkName, loadTime) {
    this.loadTimes.set(chunkName, loadTime)
  }

  /**
   * 生成分析报告
   * @returns {Object} 分析报告
   */
  generateAnalysis() {
    const totalSize = Array.from(this.chunkSizes.values()).reduce((sum, size) => sum + size, 0)
    const averageLoadTime = Array.from(this.loadTimes.values()).reduce((sum, time) => sum + time, 0) / this.loadTimes.size
    
    return {
      totalChunks: this.chunkSizes.size,
      totalSize: totalSize,
      averageChunkSize: totalSize / this.chunkSizes.size,
      averageLoadTime: averageLoadTime,
      largestChunks: this.getLargestChunks(5),
      slowestChunks: this.getSlowestChunks(5),
      recommendations: this.generateOptimizationSuggestions()
    }
  }

  /**
   * 获取最大的chunks
   * @param {number} count - 数量
   * @returns {Array} 最大chunks列表
   */
  getLargestChunks(count = 5) {
    return Array.from(this.chunkSizes.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([name, size]) => ({ name, size }))
  }

  /**
   * 获取最慢的chunks
   * @param {number} count - 数量
   * @returns {Array} 最慢chunks列表
   */
  getSlowestChunks(count = 5) {
    return Array.from(this.loadTimes.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([name, time]) => ({ name, time }))
  }

  /**
   * 生成优化建议
   * @returns {Array} 优化建议列表
   */
  generateOptimizationSuggestions() {
    const suggestions = []
    const largestChunks = this.getLargestChunks(3)
    
    largestChunks.forEach(chunk => {
      if (chunk.size > 1024 * 1024) { // 大于1MB
        suggestions.push({
          type: 'large-chunk',
          message: `${chunk.name} chunk过大 (${(chunk.size / 1024 / 1024).toFixed(2)}MB)，建议进一步拆分`,
          severity: 'high'
        })
      }
    })

    return suggestions
  }
}

// 导出全局分析器实例
export const bundleAnalyzer = new BundleAnalyzer() 