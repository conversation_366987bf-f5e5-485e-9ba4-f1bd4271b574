<template>
  <div class="manager-sub-box">
    <div class="fillet OuterFrame">
      <el-tabs v-model="formInlines.userStatus" type="card">
        <el-tab-pane label="全部" name="3"></el-tab-pane>
        <el-tab-pane label="启用" name="0"></el-tab-pane>
        <el-tab-pane label="停用" name="1"></el-tab-pane>
      </el-tabs>
      <div class="fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名" label-width="80px" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="公司名称" label-width="80px" prop="compName">
              <el-input
                v-model="formInline.compName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="创建人" label-width="80px" prop="createName">
              <el-input
                v-model="formInline.createName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="创建时间" label-width="80px" prop="time">
              <el-date-picker
                class="input-w"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>
        <div class="sensitive">
          <!-- <span class="sensitive-list-header">用户列表</span> -->
          <!-- <el-button v-if="GetStatus==0" type="primary"  @click="Subadd">新增子用户</el-button> -->
          <!-- <el-button type="primary" @click="batchDeletion" v-if="selectId.length>0">批量删除</el-button> -->
          <el-button
            type="warning"
            @click="Disable"
            v-if="
              selecteds.length > 0 &&
              formInlines.userStatus != 0 &&
              formInlines.userStatus != 1 &&
              (this.$store.state.roleId == 3 || this.$store.state.roleId == 19)
            "
            >批量停用</el-button
          >
          <el-button
            type="primary"
            @click="Enable"
            v-if="
              selecteds.length > 0 &&
              formInlines.userStatus != 0 &&
              formInlines.userStatus != 1 &&
              (this.$store.state.roleId == 3 || this.$store.state.roleId == 19)
            "
            >批量启用</el-button
          >
          <!-- <el-button
                type="warning"
                @click="Disable"
                v-if="selectId.length > 0 && formInlines.userStatus == 0"
                >批量停用</el-button
              >
              <el-button
                type="primary"
                @click="Enable"
                v-if="selectId.length > 0 && formInlines.userStatus == 1"
                >批量启用</el-button
              > -->
          <el-button
            type="primary"
            @click="SwitchChannel"
            v-if="selecteds.length > 0"
            >批量切换通道组</el-button
          >
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <table-tem
            :tableDataObj="tableDataObj"
            @handelOptionButton="handelOptionButton"
            @handelSelection="handelSelection"
          ></table-tem>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>
          
            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
    <!-- 新增与编辑弹出层 -->
    <el-dialog
      :title="SubDialog"
      v-model="dialogVisible"
      width="960px"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <el-steps simple :active="active" style="margin-bottom: 20px">
        <el-step title="子用户基础信息" :icon="Edit"></el-step>
        <el-step title="短信产品配置" :icon="UploadFilled"></el-step>
      </el-steps>
      <div v-if="active == 0">
        <el-form
          :model="form"
          :rules="rules"
          ref="formSub"
          label-width="110px"
          style="padding: 10px 150px 0 100px"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-if="SubDialog == '新增子用户'"
              v-model="form.userName"
            ></el-input>
            <el-input
              v-if="SubDialog == '编辑子用户'"
              style="background: #00000014"
              disabled
              v-model="form.userName"
            ></el-input>
          </el-form-item>
          <el-form-item label="接口密码" prop="apiPassword">
            <el-input
              v-if="SubDialog == '编辑子用户' && flag1"
              style="background: #00000014"
              disabled
              model-value="*********"
            ></el-input>
            <el-input v-else readonly v-model="form.apiPassword"></el-input>
            <el-button
              v-if="SubDialog == '新增子用户'"
              type="primary"
              class="generatePassword"
              style="margin-right: 10px; margin: 20px 0"
              @click="getPassword"
              >生成密码</el-button
            >
            <el-button
              v-if="SubDialog != '新增子用户'"
              type="primary"
              class="generatePassword"
              style="margin-right: 10px; margin: 20px 0"
              @click="getPassword"
              >重置密码</el-button
            >
          </el-form-item>
          <el-form-item
            label="登录密码"
            prop="Password"
            v-if="SubDialog == '编辑子用户'"
          >
            <el-input
              v-if="SubDialog == '编辑子用户' && flag2"
              style="background: #00000014"
              disabled
              model-value="*********"
            ></el-input>
            <el-input v-else readonly v-model="form.loginPassWord"></el-input>
            <el-button
              type="primary"
              class="generatePassword"
              style="margin-right: 10px; margin: 20px 0"
              @click="getLoginPassword"
              >重置密码</el-button
            >
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="form.email"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="mobileNumber">
            <el-input v-model="form.mobileNumber"></el-input>
            <span></span>
          </el-form-item>
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="form.companyName"></el-input>
          </el-form-item>
          <el-form-item label="IP地址" prop="ip">
            <el-input v-model="form.ip"></el-input>
          </el-form-item>
          <el-form-item label="账户状态" prop="userStatus">
            <el-radio-group v-model="form.userStatus">
              <el-radio label="0">启用</el-radio>
              <el-radio label="1">停用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="产品类型">
            <el-checkbox-group v-model="form.product">
              <el-checkbox
                v-for="(item, index) in businessProduct"
                :key="index"
                :disabled="item.disabled"
                :label="item.label"
                name="product"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="active == 1">
        <el-form
          ref="formSub1"
          :model="form"
          :inline="true"
          :rules="rules"
          label-width="110px"
          style="padding: 10px 0 10px 10px"
        >
          <p
            style="
              color: #16a589;
              font-weight: bold;
              padding-bottom: 16px;
              padding-left: 14px;
            "
          >
            基本信息
          </p>
          <el-form-item label="SMS用户单价" prop="smsPrice">
            <el-input v-model="form.smsPrice" class="input-wd"></el-input>
          </el-form-item>
          <el-form-item label="产品余额(条)" prop="productBalance">
            <el-input
              v-model="form.productBalance"
              class="input-wd"
            ></el-input>
          </el-form-item>
          <el-form-item label="选择通道" prop="smsSelect">
            <el-select clearable class="input-wd" v-model="form.smsSelect">
              <el-option label="人工通道组" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="通道组名称"
            prop="smsProductId"
            v-if="form.smsSelect == '1'"
          >
            <el-select
              clearable
              class="input-wd"
              v-model="form.smsProductId"
              placeholder="请选择通道名称"
            >
              <el-option
                v-for="(item, index) in channel"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="消息报告" prop="smsReportLevel">
            <el-radio-group v-model="form.smsReportLevel" class="input-wd">
              <el-radio label="1">不接收报告</el-radio>
              <el-radio label="2">批量推送</el-radio>
              <el-radio label="3">主动抓取</el-radio>
            </el-radio-group>
          </el-form-item>
          <transition name="el-fade-in-linear">
            <el-form-item
              label="推送方式"
              prop="smsPushType"
              v-if="form.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-radio-group v-model="form.smsPushType" class="input-wd">
                <el-radio label="1">V5平台推送方式</el-radio>
                <el-radio label="2">融合平台推送方式</el-radio>
              </el-radio-group>
            </el-form-item>
          </transition>
          <transition name="el-fade-in-linear">
            <el-form-item
              label="上行地址"
              prop="smsRepliesUrl"
              v-if="form.smsReportLevel == '2'"
            >
              <el-input
                v-model="form.smsRepliesUrl"
                class="input-wd"
              ></el-input>
              <div>
                <span style="color: red; font-size: 12px"
                  >注：获取回复短信的地址 </span
                ><span style="font-size: 12px"> (以http/https开头)</span>
              </div>
            </el-form-item>
          </transition>
          <transition name="el-fade-in-linear">
            <el-form-item
              label="下行地址"
              prop="smsReportUrl"
              v-if="form.smsReportLevel == '2'"
            >
              <el-input
                v-model="form.smsReportUrl"
                class="input-wd"
              ></el-input>
              <div>
                <span style="color: red; font-size: 12px"
                  >注：获取状态报告的地址 </span
                ><span style="font-size: 12px"> (以http/https开头)</span>
              </div>
            </el-form-item>
          </transition>
          <p
            style="
              color: #16a589;
              font-weight: bold;
              padding-bottom: 16px;
              padding-left: 14px;
            "
          >
            参数配置
          </p>
          <el-form-item label="指定扩展号" prop="ext">
            <el-input v-model="form.ext" class="input-wd"></el-input>
          </el-form-item>
          <el-form-item label="自定义发送">
            <el-radio-group v-model="form.custom" class="input-wd">
              <el-radio label="1">是</el-radio>
              <el-radio label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="短信签名位置" prop="signSetting">
            <el-radio-group v-model="form.signSetting" class="input-wd">
              <el-radio :label="1">签名前置</el-radio>
              <el-radio :label="2">签名后置</el-radio>
              <el-radio :label="3">无限制</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否审核" prop="smsIsAudit">
            <el-radio-group v-model="form.smsIsAudit" class="input-wd">
              <el-radio label="1">是</el-radio>
              <el-radio label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="审核号码数"
            prop="smsAuditNum"
            v-if="form.smsIsAudit == 1"
          >
            <el-input-number
              class="input-wd"
              v-model="form.smsAuditNum"
              :min="1"
              :max="50000"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="黑词审核" prop="smsIsBlackWords">
            <el-radio-group v-model="form.smsIsBlackWords" class="input-wd">
              <el-radio label="1">是</el-radio>
              <el-radio label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="黑词审核等级"
            prop="smsBlackWordLevel"
            v-if="form.smsIsBlackWords == 1"
          >
            <el-checkbox-group
              v-model="form.smsBlackWordLevel"
              class="input-wd"
            >
              <el-checkbox label="1">一级</el-checkbox>
              <el-checkbox label="2">二级</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="是否加黑" prop="smsIsRepliesBlack">
            <el-radio-group v-model="form.smsIsRepliesBlack" class="input-wd">
              <el-radio label="1">是</el-radio>
              <el-radio label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="加黑关键词"
            prop="smsRepliesBlackWords"
            v-if="form.smsIsRepliesBlack == '1'"
          >
            <el-input
              placeholder=" 多个用逗号(,)隔开"
              v-model="form.smsRepliesBlackWords"
              class="input-wd"
            ></el-input>
          </el-form-item>
          <p
            style="
              color: #16a589;
              font-weight: bold;
              padding-bottom: 16px;
              padding-left: 14px;
            "
          >
            特殊流程设置
          </p>
          <el-form-item label="回复抓取" prop="replyCrawlSetting">
            <el-select
              clearable
              class="input-wd"
              v-model="form.replyCrawlSetting"
            >
              <el-option value="0" label="标准回复抓取流程"></el-option>
              <el-option
                v-for="(item, index) in replyGrabbing"
                :label="item.processName"
                :value="item.id + ',' + item.processId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="回执抓取" prop="reportCrawlSetting">
            <el-select
              clearable
              class="input-wd"
              v-model="form.reportCrawlSetting"
            >
              <el-option value="0" label="标准回执抓取流程"></el-option>
              <el-option
                v-for="(item, index) in receiptGrabbing"
                :label="item.processName"
                :value="item.id + ',' + item.processId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="回复推送" prop="replyPushSetting">
            <el-select
              clearable
              class="input-wd"
              v-model="form.replyPushSetting"
            >
              <el-option value="0" label="标准回复推送流程"></el-option>
              <el-option
                v-for="(item, index) in replyPush"
                :label="item.processName"
                :value="item.id + ',' + item.processId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="回执推送" prop="reportPushSetting">
            <el-select
              clearable
              class="input-wd"
              v-model="form.reportPushSetting"
            >
              <el-option value="0" label="标准回执推送流程"></el-option>
              <el-option
                v-for="(item, index) in receiptPush"
                :label="item.processName"
                :value="item.id + ',' + item.processId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="form.remark"
              class="input-wd"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div v-if="active == 0" class="dialog-footer">
          <el-button type="primary" @click="next('formSub')"
            >下一步</el-button
          >
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
        <div v-if="active == 1" class="dialog-footer">
          <el-button @click="Previous">上一步</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 新增与编辑弹出层完 -->
    <!-- 批量切换通道 -->
    <el-dialog
      title="批量测试"
      v-model="dialogChannel"
      :close-on-click-modal="false"
      style="padding: 0 28px 0 20px"
      width="520px"
    >
      <el-form style="padding: 0 28px 0 20px">
        <el-form-item label="通道类型" label-width="80px">
          <el-select v-model="formType.type" placeholder="请选择">
            <el-option label="行业通道" value="1"> </el-option>
            <el-option label="营销通道" value="2"> </el-option>
            <el-option label="验证码通道" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通道组名称" prop="smsProductId">
          <el-select
            clearable
            class="input-w-2"
            v-model="formType.smsProductId"
            filterable
            placeholder="请选择通道名称"
          >
            <el-option
              v-for="(item, index) in channel"
              :label="item.channelGroupName"
              :value="item.channelGroupId"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitChannel()">提 交</el-button>
          <el-button @click="dialogChannel = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 批量切换通道 -->
    <el-dialog
      :title="stopTitle"
      v-model="muchStopVisible"
      width="30%"
      :before-close="handleCloseRemark"
    >
      <el-form
        ref="formLabelAlign"
        label-width="80px"
        :model="formLabelAlign"
        :rules="remarRules"
      >
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formLabelAlign.remark"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="muchStopVisible = false">取 消</el-button>
          <el-button type="primary" @click="clickStop('formLabelAlign')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import { Edit, UploadFilled } from '@element-plus/icons-vue'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  data() {
    //用户名唯一验证
    var userName = (rule, value, callback) => {
      if (value != '') {
        if (!/[^0-9a-zA-Z]/g.test(value)) {
          // let reg = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,20}$/;
          let reg = /([a-zA-Z0-9]*[a-zA-Z][a-zA-Z0-9]*)/
          if (
            /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value) ||
            /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
          ) {
            callback(new Error('由大小写字母数字组成!'))
          } else if (reg.test(value)) {
            if (this.SubDialog == '新增子用户') {
              window.api.get(
                window.path.upms + 'online/existUser/' + value,
                {},
                (res) => {
                  if (res.data == 0) {
                    callback()
                  } else {
                    callback(new Error('用户名已经存在'))
                  }
                }
              )
            } else {
              window.api.get(
                window.path.upms + 'online/existUser/' + value,
                {},
                (res) => {
                  if (res.data == 0) {
                    callback()
                  } else {
                    callback(new Error('用户名已经存在'))
                  }
                }
              )
            }
          } else {
            callback(new Error('由大小写字母数字组成!'))
          }
        } else {
          callback(new Error('由大小写字母数字组成!'))
        }
      } else {
        callback(new Error('用户名不能为空'))
      }
    }
    //邮箱验证
    var email = (rule, value, callback) => {
      if (value == '') {
        callback()
      } else {
        if (
          !/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/.test(
            value
          )
        ) {
          return callback(new Error('请正确填写邮箱'))
        } else {
          callback()
        }
      }
    }
    // 验证IP规则
    var ip = (rule, value, callback) => {
      if (value == '') {
        callback()
      } else {
        if (
          !/^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}(,(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3})*$/.test(
            value
          )
        ) {
          return callback(new Error('请正确填写IP地址,最多5个,请用(,)隔开'))
        } else {
          var ipArr = this.form.ip.split(',')
          var flag = false
          if (ipArr.length > 5) {
            return callback(new Error('最多添加5个IP地址'))
          } else {
            if (ipArr.length > 1) {
              for (var i = 0; i < ipArr.length; i++) {
                for (var j = i; j < ipArr.length; j++) {
                  if (ipArr[i] == ipArr[j + 1]) {
                    flag = true
                  }
                }
              }
            }
            if (flag) {
              return callback(new Error('IP地址不可重复添加'))
            } else {
              callback()
            }
          }
        }
      }
    }
    // 多个手机号验证
    var phone = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('手机号码不能为空'))
      } else if (
        !/^1[3|4|5|6|7|8|9]\d{9}(,1[3|4|5|6|7|8|9]\d{9})*$/.test(value)
      ) {
        return callback(new Error('请正确填写手机号,最多5个,请用(,)隔开'))
      } else {
        var phoneArr = this.form.mobileNumber.split(',')
        var flag = false
        if (phoneArr.length > 5) {
          return callback(new Error('最多添加5个手机号'))
        } else {
          if (phoneArr.length > 1) {
            for (var i = 0; i < phoneArr.length; i++) {
              for (var j = i; j < phoneArr.length; j++) {
                if (phoneArr[i] == phoneArr[j + 1]) {
                  flag = true
                }
              }
            }
          }
          if (flag) {
            return callback(new Error('手机号不可重复添加'))
          } else {
            callback()
          }
        }
      }
    }
    // 验证用户单价
    var smsPrice = (rule, value, callback) => {
      if (value == '' && value != 0) {
        callback()
      } else if (value < this.clientSmsPrice) {
        return callback(new Error('低于成本单价'))
      } else if (!/^\d{1,2}(?:\.\d{1,5})?$/.test(value)) {
        return callback(new Error('最多2位整数和5位小数'))
      } else {
        callback()
      }
    }
    // 验证产品余额
    var productBalance = (rule, value, callback) => {
      if (value == '' && value != 0) {
        return callback(new Error('余额不能为空'))
      } else if (
        this.PaymentMethods == '1' &&
        value > this.clientProductBalance
      ) {
        return callback(new Error('超出您的剩余额度'))
      } else if (!/^[+]{0,1}(\d+)$/.test(value)) {
        return callback(new Error('请输入大于0的整数'))
      } else {
        callback()
      }
    }
    //验证扩展号
    var ext = (rule, value, callback) => {
      if (value) {
        if (!/^[+]{0,1}(\d+)$/.test(value)) {
          return callback(new Error('请输入大于0的整数'))
        } else {
          if (value == '') {
            callback()
          } else {
            window.api.get(
              window.path.omcs + 'operatingSignature/checkClientExt',
              {
                ext: value,
                userId: this.SubDialog == '新增子用户' ? null : this.subUserId,
              },
              (res) => {
                if (res.code == 200) {
                  callback()
                } else {
                  return callback(new Error(res.msg))
                }
              }
            )
          }
        }
      } else {
        callback()
      }
    }
    // 验证网址
    var url1 = (rule, value, callback) => {
      if (this.form.smsReportUrl) {
        callback()
      } else {
        if (
          !/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(
            value
          )
        ) {
          return callback(new Error('请输入上行地址'))
        } else {
          callback()
        }
      }
      callback()
    }
    var url2 = (rule, value, callback) => {
      if (this.form.smsRepliesUrl) {
        callback()
      } else {
        if (
          !/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(
            value
          )
        ) {
          return callback(new Error('请输入下行地址'))
        } else {
          callback()
        }
      }
    }
    //加黑关键词
    var checkBlackWords = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('加黑关键字不能为空'))
      } else {
        console.log(value.length)
        if (value.length < 256) {
          var reg =
            /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,9}$/gi
          if (!reg.test(value)) {
            return callback(
              new Error(
                '加黑关键词包括数字、汉字、字母、多个由英文逗号隔开最多10组'
              )
            )
          } else {
            var arr = value.split(',')
            var arr2 = []
            for (var i = 0; i < arr.length; i++) {
              if (arr2.indexOf(arr[i]) < 0) {
                arr2.push(arr[i])
              }
            }
            if (arr2.length != arr.length) {
              return callback(new Error('不允许重复'))
            } else {
              callback()
            }
          }
        }
        return callback(new Error('加黑关键字不能多于255个字'))
      }
    }
    return {
      flag1: true,
      flag2: true,
      //批量切换通道
      dialogChannel: false,
      muchStopVisible: false,
      stopTitle: '',
      formLabelAlign: {
        remark: '',
      },
      selecteds: [],
      selected: [],
      //通道
      channel: [],
      //切换通道value
      smsProductId: '',
      //列表复选框的id
      selectId: '',
      //通道列表
      channel: [],
      //回复抓取
      replyGrabbing: [],
      // 回执抓取
      receiptGrabbing: [],
      //回执推送
      receiptPush: [],
      //回复推送
      replyPush: [],
      rules: {
        userName: [
          { required: true, validator: userName, trigger: 'blur' },
          {
            min: 4,
            max: 20,
            message: '长度在 4 到 20 个字符',
            trigger: ['blur', 'change'],
          },
        ],
        apiPassword: [
          { required: true, message: '接口密码不能为空', trigger: 'blur' },
        ],
        Password: [
          { required: false, message: '登录密码不能为空', trigger: 'blur' },
        ],
        email: [
          { required: false, validator: email, trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '长度在 50 个字符以内',
            trigger: ['blur', 'change'],
          },
        ],
        companyName: [
          { required: true, message: '公司名称不能为空', trigger: 'blur' },
          {
            min: 4,
            max: 200,
            message: '长度在 4 到 200 个字符',
            trigger: 'blur',
          },
        ],
        ip: [{ required: false, validator: ip, trigger: 'blur' }],
        // type:[
        //     { type: 'array', required: true, message: '请至少选择一个业务类型', trigger: 'change' }
        // ],
        smsProductId: [
          { required: true, message: '请选择通道', trigger: 'change' },
        ],
        ext: [
          { required: false, validator: ext, trigger: 'blur' },
          {
            min: 1,
            max: 12,
            message: '指定扩展号由1-12位数字组成',
            trigger: 'blur',
          },
        ],
        mobileNumber: [{ required: true, validator: phone, trigger: 'change' }],
        smsPushType: [
          { required: true, message: '推送方式必填！', trigger: 'blur' },
        ],
        smsIsBlackWords: [
          { required: true, message: '请选择黑词是否审核', trigger: 'blur' },
        ],
        smsIsRepliesBlack: [
          { required: true, message: '请选择是否加黑', trigger: 'blur' },
        ],
        smsBlackWordLevel: [
          {
            required: true,
            message: '请选择黑词等级必填',
            trigger: 'blur,change',
          },
        ],
        smsPrice: [
          { required: true, validator: smsPrice, trigger: 'change' }, //验证待修改
        ],
        productBalance: [
          { required: false, validator: productBalance, trigger: 'blur' }, //验证待修改
        ],
        smsRepliesUrl: [
          { required: false, validator: url1, trigger: ['change', 'blur'] },
          {
            min: 1,
            max: 200,
            message: '长度在 200 个字符以内',
            trigger: ['blur', 'change'],
          },
        ],
        smsReportUrl: [
          { required: false, validator: url2, trigger: ['change', 'blur'] },
          {
            min: 1,
            max: 200,
            message: '长度在 200 个字符以内',
            trigger: ['blur', 'change'],
          },
        ],
        smsRepliesBlackWords: [
          { required: true, validator: checkBlackWords, trigger: 'change' },
        ],
        smsIsAudit: [
          { required: true, message: '请选择是否审核', trigger: 'blur' },
        ],
        smsAuditNum: [
          { required: true, message: '请输入审核条数', trigger: 'change' },
        ],
        smsSelect: [
          { required: true, message: '请选择通道名称', trigger: 'change' },
        ],
        signSetting: [
          { required: true, message: '请选择签名位置', trigger: 'blur' },
        ],
        reportPushSetting: [
          { required: true, message: '请选择回执推送方式', trigger: 'change' },
        ],
        replyPushSetting: [
          { required: true, message: '请选择回复推送方式', trigger: 'change' },
        ],
        reportCrawlSetting: [
          { required: true, message: '请选择回执抓取方式', trigger: 'change' },
        ],
        replyCrawlSetting: [
          { required: true, message: '请选择回复抓取方式', trigger: 'change' },
        ],
      },
      rulesDialogChannel: {
        smsProductId: [
          { required: true, message: '请选择通道', trigger: 'change' },
        ],
      },
      remarRules: {
        remark: [
          { required: true, message: '备注不能为空', trigger: 'change' },
        ],
        // updateName: [
        //   { required: true, message: "操作人不能为空", trigger: "change" },
        // ],
      },
      SubDialog: '',
      // 存储用户停用启用
      GetStatus: '',
      // 新增/编辑弹出层
      dialogVisible: false,
      // 步骤条
      active: 0,
      // 存储付费方式
      PaymentMethods: '',
      //编辑存储父产品单价与余额
      clientSmsPrice: '',
      clientProductBalance: '',
      // 编辑子用户存储userid
      subUserId: '',
      //存储父id
      clientId: '',
      // 存储编辑产品ID
      smsIDdata: '',
      //储存子用户管理商产品师傅存在状态
      editStatus: '',
      business: [],
      businessProduct: [],
      businessData: {},
      form: {
        userName: '',
        apiPassword: '',
        loginPassWord: '',
        email: '',
        mobileNumber: '',
        companyName: '',
        ip: '',
        userStatus: '0',
        smsId: '',
        type: ['1'],
        product: ['1'],
        businessTypes: '',
        userId: '',
        smsPrice: '',
        productBalance: '',
        ext: '',
        custom: '1',
        smsReportLevel: '1',
        smsRepliesUrl: '',
        smsReportUrl: '',
        remark: '',
        smsIsAudit: '2', //是否审核
        smsAuditNum: 1, //审核号码数
        smsIsRepliesBlack: '2', //是否加黑
        smsRepliesBlackWords: '', //加黑关键词
        smsIsBlackWords: '1', //黑词是否审核
        smsPushType: '2', //推送方式
        smsBlackWordLevel: ['1', '2'], //黑词等级
        smsSelect: '1', //选择通道
        smsProductId: '', //通道组名称
        signSetting: 1, //短信签名位置
        reportPushSetting: '0', //回执推送
        replyPushSetting: '0', //回复推送
        reportCrawlSetting: '0', //回执抓取
        replyCrawlSetting: '0', //回复抓取
      },
      // 搜索数据
      formInline: {
        userName: '',
        compName: '',
        createName: '',
        userStatus: '3',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      formType: {
        type: '1',
        smsProductId: '',
      },
      // 存储搜索数据
      formInlines: {
        userName: '',
        compName: '',
        createName: '',
        userStatus: '3',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      //子用户列表数据
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        id: 'ManagerSub',
        tableData: [], //列表数据
        tableLabel: [
          {
            prop: 'userName',
            showName: '用户名',
            fixed: "",
          },
          {
            prop: 'company',
            showName: '单位名称',
            fixed: "",
          },
          {
            prop: 'userStatus',
            showName: '状态',
            width: '60',
            fixed: "",
            showCondition: {
              condition: '1',
            },
            formatData: function (val) {
              return val == '0' ? '启用' : '停用'
            },
          },
          {
            prop: 'productBalance',
            showName: 'SMS产品余额',
            fixed: "",
          },
          // {
          //   prop: "smsPrice",
          //   showName: "单价",
          //   width: "80",
          //   fixed: false,
          // },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: "",
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '140',
            fixed: "",
          },
          {
            prop: 'remark',
            showName: '备注',
            fixed: "",
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '80', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          // {
          //     contactCondition:'userStatus',//关联的表格属性
          //     contactData:'0',//关联的表格属性-值
          //     optionName:'编辑',//按钮的显示文字
          //     optionMethod:'edit',//按钮的方法
          //     icon:'el-icon-edit',//按钮图标
          //     optionButtonColor:'',//按钮颜色
          // },
          {
            contactCondition: 'userStatus', //关联的表格属性
            contactData: '0', //关联的表格属性-值
            optionName: '停用', //按钮的显示文字
            optionMethod: 'Disable', //按钮的方法
            icon: 'el-icon-error', //按钮图标
            optionButtonColor: '#f56c6c', //按钮颜色
            otherOptionName: '启用', //其他条件的按钮显示文字
            otherOptionMethod: 'Disable', //其他条件的按钮方法
            otherIcon: 'el-icon-success', //其他条件按钮的图标
            optionOtherButtonColor: '', //其他条件按钮的颜色
          },
        ],
      },
      tableDataObj1: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [], //列表数据
        tableLabel: [
          {
            prop: 'userName',
            showName: '用户名',
            fixed: "",
          },
          {
            prop: 'company',
            showName: '单位名称',
            fixed: "",
          },
          {
            prop: 'userStatus',
            showName: '状态',
            width: '60',
            fixed: "",
            showCondition: {
              condition: '1',
            },
            formatData: function (val) {
              return val == '0' ? '启用' : '停用'
            },
          },
          {
            prop: 'productBalance',
            showName: 'SMS产品余额',
            fixed: "",
          },
          {
            prop: 'smsPrice',
            showName: '单价',
            width: '80',
            fixed: "",
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: "",
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '140',
            fixed: "",
          },
          {
            prop: 'remark',
            showName: '备注',
            fixed: "",
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '80', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      }
    }
  },
  components: {
    DatePlugin,
    TableTem
  },
  name: 'authorityManagement',
  methods: {
    //返回
    goBack() {
      this.$router.go(-1)
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      this.formInlines.parentId = this.$route.query.ids
      window.api.post(
        window.path.omcs + 'operatinguser/querysubuser',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
          // if(this.$store.state.roleId==3 || this.$store.state.roleId==19){

          // }else{
          //     this.tableDataObj1.loading2 = false;
          //       this.tableDataObj1.tableData = res.records;
          //       this.tableDataObj1.tablecurrent.total = res.total;
          // }
        }
      )
    },
    /**获取通道列表 */
    getChannel() {
      window.api.get(
        window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
        {},
        (res) => {
          this.channel = res.data
        }
      )
    },
    // 新增子用户弹出
    Subadd() {
      // 产品数据
      window.api.get(
        window.path.omcs +
          'operatinguser/getUserInfoById/' +
          this.$route.query.ids,
        {},
        (res) => {
          this.clientSmsPrice = res.smsPrice
          this.clientProductBalance = res.productBalance
          this.GetStatus = res.userStatus

          this.form.custom = res.custom //自定义发送
          this.form.smsIsAudit = res.smsIsAudit + '' //是否审核
          this.form.smsIsBlackWords = res.smsIsBlackWords + '' //黑词是否审核
          this.form.smsBlackWordLevel = res.smsBlackWordLevel.split(',') //黑词等级
          this.form.smsProductId = res.smsProductId //通道
          this.form.smsAuditNum = res.smsAuditNum //审核条数
        }
      )
      this.SubDialog = '新增子用户'
      this.dialogVisible = true
    },
    // 子用户弹窗关闭
    handleClose(done) {
      this.dialogVisible = false
    },
    //生成密码
    getPassword() {
      window.api.get(window.path.omcs + 'operatinguser/getPassWord', {}, (res) => {
        this.flag1 = false
        this.form.apiPassword = res.data
      })
    },
    //生成密码
    getLoginPassword() {
      window.api.get(window.path.omcs + 'operatinguser/getPassWord', {}, (res) => {
        this.flag2 = false
        this.form.loginPassWord = res.data
        console.log(this.form.loginPassWord)
      })
    },
    // 下一步
    next(val) {
      this.$refs[val].validate((valid, vas) => {
        if (valid) {
          // 新增检测用户唯一
          window.api.get(
            window.path.upms + 'online/existUser/' + this.form.userName,
            {},
            (res) => {
              if (res.data != 0) {
                this.$message({
                  message: '用户名已存在，切勿重复！',
                  type: 'warning',
                })
              } else {
                this.active = 1
              }
            }
          )
        } else {
          return false
        }
      })
    },
    // 上一步
    Previous() {
      this.active = 0
    },
    // 新增/编辑子用户
    submit() {
      this.$refs['formSub1'].validate((valid, validVal) => {
        if (validVal && !validVal.type) {
          if (
            Object.getOwnPropertyNames(validVal).length == 1 &&
            validVal.smsPrice[0] == 'Error: 低于成本单价'
          ) {
            valid = true
          }
        }
        if (valid) {
          if (this.SubDialog == '新增子用户') {
            this.form.businessTypes = this.form.product.join(',')
            let aaa = {}
            Object.assign(aaa, this.form)
            aaa.clientId = this.$route.query.Id
            aaa.parentId = this.$route.query.ids
            if (this.form.product[0] == '1') {
              aaa.smsId = this.businessData.smsId
            }
            aaa.userId = this.businessData.userId
            aaa.smsBlackWordLevel = this.form.smsBlackWordLevel.join(',')
            this.$confirms.confirmation(
              'post',
              '确定添加子用户',
              window.path.omcs + 'operatinguser/addsubuserinfo',
              aaa,
              (res) => {
                this.dialogVisible = false
                this.InquireList()
              }
            )
          } else if (this.SubDialog == '编辑子用户') {
            let formAdd = {}
            Object.assign(formAdd, this.form)
            if (this.form.product[0] == '1') {
              formAdd.smsId = this.smsIDdata
            }
            if (this.form.replyCrawlSetting == '0') {
              formAdd.replyCrawlSetting = ''
            }
            if (this.form.replyPushSetting == '0') {
              formAdd.replyPushSetting = ''
            }
            if (this.form.reportCrawlSetting == '0') {
              formAdd.reportCrawlSetting = ''
            }
            if (this.form.reportPushSetting == '0') {
              formAdd.reportPushSetting = ''
            }
            // 编辑时密码传null
            formAdd.parentId = this.$route.query.ids
            formAdd.userId = this.subUserId
            formAdd.businessTypes = this.form.product.join(',')
            formAdd.smsBlackWordLevel = this.form.smsBlackWordLevel.join(',')
            if (!formAdd.loginPassWord) formAdd.loginPassWord = ''
            if (formAdd.apiPassword.length > 12) formAdd.apiPassword = ''
            console.log(formAdd.loginPassWord, formAdd.apiPassword)
            this.$confirms.confirmation(
              'put',
              '确定修改子用户',
              window.path.omcs + 'operatinguser/updatesubuserinfo',
              formAdd,
              (res) => {
                this.dialogVisible = false
                this.InquireList()
              }
            )
          }
        } else {
          return false
        }
      })
    },
    // 批量切换通道
    SwitchChannel() {
      this.dialogChannel = true
    },
    submitChannel() {
      if (this.formType.smsProductId) {
        this.$confirms.confirmation(
          'post',
          '确定修改通道',
          window.path.omcs + 'v3/operatingchannelgroup/clients',
          {
            productId: this.formType.smsProductId,
            clientIds: this.selecteds.join(','),
            type: this.formType.type,
          },
          (res) => {
            this.dialogChannel = false
            this.InquireList()
          }
        )
      } else {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '请先选择通道',
        })
      }
    },
    /**获取特殊流程 */
    // getSpecialProcess(){
    //     window.api.get(window.path.omcs+'processlist/getProcessList/1',{},res=>{
    //         this.replyGrabbing = res; //回复抓取
    //     })
    //     window.api.get(window.path.omcs+'processlist/getProcessList/2',{},res=>{
    //         this.receiptGrabbing=res; // 回执抓取
    //     })
    //     window.api.get(window.path.omcs+'processlist/getProcessList/3',{},res=>{
    //         this.replyPush=res;//回复推送
    //     })
    //     window.api.get(window.path.omcs+'processlist/getProcessList/4',{},res=>{
    //         this.receiptPush=res; //回执推送
    //     })
    // },
    // 操作
    handelOptionButton: function (val) {
      if (val.methods == 'edit') {
        //编辑
        if (this.editStatus == 0) {
          // 产品数据
          this.flag1 = true
          this.flag2 = true
          window.api.get(
            window.path.omcs +
              'operatinguser/getUserInfoById/' +
              this.$route.query.ids,
            {},
            (res) => {
              this.clientSmsPrice = res.smsPrice
              this.clientProductBalance = res.productBalance
              this.GetStatus = res.userStatus
              this.SubDialog = '编辑子用户'
              this.subUserId = val.row.userId
              window.api.get(
                window.path.omcs +
                  'operatinguser/getUserInfoById/' +
                  val.row.userId,
                {},
                (res) => {
                  this.form.product = res.businessTypes.split(',')
                  this.clientProductBalance += res.productBalance
                  this.dialogVisible = true
                  this.$nextTick(function () {
                    // console.log(res)
                    this.$refs.formSub.resetFields()
                    res.smsReportLevel += ''
                    if (res.businessTypes != null) {
                      res.businessTypes = res.businessTypes.split(',')
                    }
                    this.smsIDdata = res.smsId
                    res.type = ['1']
                    Object.assign(this.form, res)
                    if (
                      res.smsBlackWordLevel != null &&
                      res.smsBlackWordLevel != ''
                    ) {
                      this.form.smsBlackWordLevel =
                        res.smsBlackWordLevel.split(',')
                    } else {
                      this.form.smsBlackWordLevel = []
                    }
                    if (res.replyCrawlSetting == '') {
                      this.form.replyCrawlSetting = '0'
                    }
                    if (res.replyPushSetting == '') {
                      this.form.replyPushSetting = '0'
                    }
                    if (res.reportCrawlSetting == '') {
                      this.form.reportCrawlSetting = '0'
                    }
                    if (res.reportPushSetting == '') {
                      this.form.reportPushSetting = '0'
                    }
                    this.form.smsIsBlackWords += ''
                    this.form.smsIsAudit += ''
                    this.form.smsIsRepliesBlack += ''
                    this.form.smsPushType += ''
                  })
                }
              )
            }
          )
        } else {
          this.$message({
            type: 'error',
            duration: '2000',
            message: '该用户管理商无产品信息无法编辑',
          })
        }
      }
      if (val.methods == 'Disable') {
        //状态

        if (val.row.userStatus == '0') {
          this.selected = [val.row.userId]
          this.muchStopVisible = true
          this.stopTitle = '停用'
        } else if (val.row.userStatus == '1') {
          this.$confirms.confirmation(
            'post',
            '确认要对当前用户启用吗？',
            window.path.omcs + 'operatinguser/enable',
            { userIds: [val.row.userId] },
            (res) => {
              this.InquireList()
            }
          )
        }
      }
    },
    stopUser() {
      this.$confirms.confirmation(
        'post',
        '确认要对当前用户停用吗？',
        window.path.omcs + 'operatinguser/disabled',
        { userIds: this.selected, remark: this.formLabelAlign.remark },
        (res) => {
          this.InquireList()
          this.muchStopVisible = false
        }
      )
    },
    handleCloseRemark() {
      this.muchStopVisible = false
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.ListSearch()
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0]
        this.formInline.endTime = val[1]
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    clickStop(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.stopTitle == '停用') {
            this.stopUser()
          } else {
            this.allStop()
          }
        }
      })
    },
    //列表复选框的值
    handelSelection(val) {
      // let selectId = [];
      // for (let i = 0; i < val.length; i++) {
      //   selectId.push(val[i].userId);
      // }
      // this.selectId = selectId.join(","); //批量操作选中id
      this.selecteds = val.map((item) => {
        return item.userId
      })
    },
    //批量删除
    // batchDeletion(){
    //     this.$confirms.confirmation('delete','此操作将永久删除该数据, 是否继续?',window.path.upms+'user/"'+ this.selectId+'"',{},res =>{
    //         this.InquireList();
    //     })
    // },
    //批量停用
    Disable() {
      this.muchStopVisible = true
      this.stopTitle = '批量停用'
      // let aa = this.selectId.split(",");
    },
    allStop() {
      this.$confirms.confirmation(
        'post',
        '确认批量停用',
        window.path.omcs + 'operatinguser/disabled',
        { userIds: this.selecteds, remark: this.formLabelAlign.remark },
        (res) => {
          this.InquireList()
          this.muchStopVisible = false
          // this.addCharacterPopUps1 = false;
        }
      )
    },
    //批量启用
    Enable() {
      let aa = this.selectId.split(',')
      this.$confirms.confirmation(
        'post',
        '确认批量启用',
        window.path.omcs + 'operatinguser/enable',
        { userIds: this.selecteds },
        (res) => {
          this.InquireList()
          // this.addCharacterPopUps1 = false;
        }
      )
    },
    // 分页回调
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
  },
  created() {
    //获取通道
    this.getChannel()
    // this.getSpecialProcess();
    //产品数据
    // window.api.get(window.path.omcs+'operatinguser/getSms/'+this.$route.query.ids,{},res=>{
    //     if(res.code==400){
    //         this.editStatus=1
    //     }else{
    //         this.businessData=res.data[0]
    //         this.PaymentMethods=res.data[0].smsPayMethod
    //     //  this.business=JSON.parse(res.data)[0].businessTypes.split(',')
    //         this.editStatus=0
    //     }
    // })
    // window.api.get(window.path.omcs+'operatinguser/getUserInfoById/'+this.$route.query.ids,{},res=>{
    //     this.business=res.businessTypes
    //     this.clientSmsPrice=res.smsPrice
    //     this.clientProductBalance=res.productBalance
    //     this.GetStatus=res.userStatus
    //     let businessArray=res.businessTypes.split(',')
    //     for(var i=0;i<businessArray.length;i++){
    //         if(businessArray[i]=='1'){
    //             this.businessProduct.push({
    //                 name:"短信",
    //                 label:"1",
    //                 disabled:true
    //             })
    //         }else if(businessArray[i]=='5'){
    //             this.businessProduct.push({
    //                 name:"空号检测",
    //                 label:"5",
    //                 disabled:false
    //             })
    //         }
    //     }
    // })
  },
  activated() {
    this.InquireList()
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  watch: {
    muchStopVisible(val) {
      if (!val) {
        this.formLabelAlign.remark = ''
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
    // 监听弹出层关闭
    dialogVisible: function (val) {
      if (val == false) {
        this.$refs.formSub.resetFields() //清空表单
        this.$refs.formSub1.resetFields() //清空表单
        this.form.custom = 1
        this.active = 0
        this.form.product = ['1']
        this.form.smsRepliesUrl = ''
        this.form.smsReportUrl = ''
        ;(this.form.smsIsRepliesBlack = '2'), //是否加黑
          (this.form.smsRepliesBlackWords = '') //加黑关键词
        this.form.smsPushType = '2'
      }
    },
    dialogChannel: function (val) {
      if (val == false) {
        this.smsProductId = ''
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  background: #fff;
  padding-bottom: 60px;
  padding: 20px;
}
.OuterFrame_title {
  height: 40px;
  line-height: 40px;
  padding-left: 5px;
}
.sensitive {
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.searchFor {
  padding: 15px 0;
  height: 32px;
  line-height: 32px;
}
.searchFor div {
  float: right;
  margin-left: 10px;
}
.ProductChoice {
  display: inline-block;
}
.ProductChoice label {
  width: 125px;
}
.ProductChoice .el-form-item__content {
  margin-left: 130px;
}
.Choices .el-form-item--small {
  margin-bottom: 0;
}
.uploadSC .upload-demo {
  position: absolute;
  top: 0;
  right: -90px;
}
.generatePassword {
  background-color: #16a589;
  border-color: #16a589;
  position: absolute;
  top: -20px;
  right: -93px;
}
.generatePassword:hover {
  background: rgb(69, 183, 161);
  border-color: rgb(69, 183, 161);
  color: #fff;
}
.CZtable .el-input {
  float: right;
}
.CZtable .tableTem {
  margin-top: 70px;
}
.input-wd {
  width: 330px !important;
}
</style>

<style>
/* .manager-sub-box .el-radio+.el-radio{
    margin-left:0px !important;
} */
</style>
