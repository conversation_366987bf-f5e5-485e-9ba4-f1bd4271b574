<template>
  <div class="container_left">
    <div class="fillet shortChain-box">
      <el-tabs v-model="formInline.type" type="card" @tab-change="handleClick">
        <el-tab-pane label="用户白名单" name="1">
          <div v-show="formInline.type == '1'" class="short-box">
            <div>
              <el-form
                :inline="true"
                :model="formInline"
                label-width="80px"
                class="demo-form-inline"
                ref="queryForm1"
              >
                <el-form-item label="用户名" prop="userName">
                  <el-input
                    v-model="formInline.userName"
                    placeholder=""
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="公司名" prop="compName">
                  <el-input
                    v-model="formInline.compName"
                    placeholder=""
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="域名" prop="domain">
                  <el-input
                    v-model="formInline.domain"
                    placeholder=""
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核状态" prop="auditStatus">
                  <el-select
                    v-model="formInline.auditStatus"
                    placeholder="请选择"
                    class="input-w"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="审核中" value="1"></el-option>
                    <el-option label="审核通过" value="2"></el-option>
                    <el-option label="审核未通过" value="3"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="创建时间" prop="time">
                  <el-date-picker
                    type="daterange"
                    v-model="formInline.time"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="input-time"
                    @change="handleTimeChange"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" plain style="" @click="Query"
                    >查询</el-button
                  >
                  <el-button
                    type="primary"
                    plain
                    style=""
                    @click="Reload('queryForm1')"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
            </div>

            <div class="Mail-table Mail-table1">
              <!-- 表格和分页开始 -->
              <!-- <el-table
                v-loading="tableDataObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="tableDataObj.tableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              > -->

              <vxe-toolbar ref="toolbarRef1" custom>
                <template #buttons>
                  <el-button @click="addWhiteList" type="primary"
                    >添加白名单</el-button
                  >
                  <el-button
                    :disabled="selectId.length == 0"
                    @click="batchDelete"
                    type="danger"
                    >批量删除</el-button
                  >
                </template>
              </vxe-toolbar>

              <vxe-table
                ref="tableRef1"
                id="shortDomainUser"
                border
                stripe
                :custom-config="customConfig"
                :column-config="{ resizable: true }"
                :row-config="{ isHover: true }"
                min-height="1"
                v-loading="tableDataObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                style="font-size: 12px;"
                :data="tableDataObj.tableData"
                @checkbox-all="handleSelectionChange"
                @checkbox-change="handleSelectionChange">

                <vxe-column type="checkbox" width="50"></vxe-column>
                <vxe-column field="用户名" title="用户名" width="140">
                  <template #default="scope">
                    <div>{{ scope.row.userName }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="公司名" title="公司名" width="200">
                  <template #default="scope">
                    <div>{{ scope.row.compName }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="域名" title="域名" width="200">
                  <template #default="scope">
                    <div>{{ scope.row.domain }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="状态" title="状态">
                  <template #default="scope">
                    <el-tag
                      :disable-transitions="true"
                      type="info"
                      effect="dark"
                      v-if="scope.row.auditStatus == '1'"
                      
                      >待审核</el-tag
                    >
                    <el-tag
                      :disable-transitions="true"
                      type="success"
                      effect="dark"
                      v-else-if="scope.row.auditStatus == '2'"
                      
                      >审核通过</el-tag
                    >
                    <el-tag
                      :disable-transitions="true"
                      type="danger"
                      effect="dark"
                      v-else-if="scope.row.auditStatus == '3'"
                      
                      >审核未通过</el-tag
                    >
                    <el-tooltip
                      v-else-if="scope.row.auditStatus == '3'"
                      class="item"
                      effect="dark"
                      :content="scope.row.auditReason"
                      placement="top"
                    >
                      <el-icon style="color: #f56c6c"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <span v-else></span>
                  </template>
                </vxe-column>
                <!-- <vxe-column field="" title="材料">
                                        <template #default="scope">
                                            <el-image v-if="scope.row.relationFile" style="width: 70px; height: 50px"
                                                :src="API.imgU + scope.row.relationFile"
                                                :preview-src-list="[API.imgU + scope.row.relationFile]">
                                            </el-image>
                                            <el-image v-else>
                                                <div slot="error" class="image-slot">
                                                    <i style="font-size: 20px;" class="el-icon-picture-outline"></i>
                                                </div>
                                            </el-image>
                                        </template>
                                    </vxe-column> -->
                <vxe-column field="标识" title="标识">
                  <template #default="scope">
                    <div v-if="scope.row.thirdDomain == 0">私有域名</div>
                    <div v-else-if="scope.row.thirdDomain == 1"
                      >第三方域名</div
                    >
                  </template>
                </vxe-column>
                <vxe-column field="原因" title="原因">
                  <template #default="scope">
                    <Tooltip
                      v-if="scope.row.auditReason"
                      :content="scope.row.auditReason"
                      className="wrapper-text"
                      effect="light"
                    >
                    </Tooltip>
                  </template>
                </vxe-column>
                <vxe-column field="审核人" title="审核人">
                  <template #default="scope">
                    <div>{{ scope.row.auditName }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="审核时间" title="审核时间" width="170">
                  <template #default="scope">
                    <div>{{
                      scope.row.auditTime
                        ? moment(scope.row.auditTime).format(
                            'YYYY-MM-DD HH:mm:ss'
                          )
                        : ''
                    }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="操作" title="操作" width="180" fixed="right">
                  <template #default="scope">
                    <el-button
                      v-if="scope.row.auditStatus == '1'"
                      link
                      style="color: #67c23a; margin-left: 10px"
                      @click="handleReject(2, scope.row)"
                      ><el-icon><Check /></el-icon>&nbsp;通过</el-button
                    >
                    <el-button
                      v-if="scope.row.auditStatus == '1'"
                      link
                      style="color: #f56c6c; margin-left: 10px"
                      @click="handleReject(3, scope.row)"
                      ><el-icon><CircleCloseFilled /></el-icon>&nbsp;不通过</el-button
                    >
                    <el-button
                      link
                      style="color: #f56c6c; margin-left: 10px"
                      @click="handleDelete(scope.row)"
                      ><el-icon><Delete /></el-icon>&nbsp;删除</el-button
                    >
                  </template>
                </vxe-column>
              </vxe-table>
              <!--分页-->
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  margin-top: 10px;
                "
              >
                <div></div>
                <el-pagination
                  class="page_bottom"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="formInline.currentPage"
                  :page-size="formInline.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="tableDataObj.total"
                >
                </el-pagination>
              </div>
              <!-- 表格和分页结束 -->
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="系统白名单" name="0">
          <div class="short-box" v-show="formInline.type == '0'">
            <div>
              <el-form
                :inline="true"
                :model="formInline"
                label-width="80px"
                class="demo-form-inline"
                ref="queryForm2"
              >
                <el-form-item label="域名" prop="domain">
                  <el-input
                    v-model="formInline.domain"
                    placeholder=""
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核状态" prop="auditStatus">
                  <el-select
                    v-model="formInline.auditStatus"
                    placeholder="请选择"
                    class="input-w"
                  >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="审核中" value="1"></el-option>
                    <el-option label="审核通过" value="2"></el-option>
                    <el-option label="审核未通过" value="3"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="创建时间" prop="time">
                  <el-date-picker
                    type="daterange"
                    v-model="formInline.time"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="input-time"
                    @change="handleTimeChange"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" plain style="" @click="Query"
                    >查询</el-button
                  >
                  <el-button
                    type="primary"
                    plain
                    style=""
                    @click="Reload('queryForm2')"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
            <div class="Mail-table Mail-table1">
              <!-- 表格和分页开始 -->
              <!-- <el-table
                v-loading="tableDataObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="tableDataObj.tableData"
                style="width: 100%"
                @selection-change="handleSelectionChange"
              > -->

              <vxe-toolbar ref="toolbarRef2" custom>
                <template #buttons>
                    <el-button @click="addWhiteList" type="primary"
                      >添加白名单</el-button
                    >
                    <el-button
                      :disabled="selectId.length == 0"
                      @click="batchDelete"
                      type="danger"
                      >批量删除</el-button
                    >
                </template>
              </vxe-toolbar>

              <vxe-table
                ref="tableRef2"
                id="shortDomainSystem"
                border
                stripe
                :custom-config="customConfig"
                :column-config="{ resizable: true }"
                :row-config="{ isHover: true }"
                min-height="1"
                v-loading="tableDataObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                style="font-size: 12px;"
                :data="tableDataObj.tableData"
                @checkbox-all="handleSelectionChange"
                @checkbox-change="handleSelectionChange">

                <vxe-column type="checkbox" width="50"></vxe-column>
                <vxe-column field="域名" title="域名">
                  <template #default="scope">
                    <div>{{ scope.row.domain }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="状态" title="状态">
                  <template #default="scope">
                    <el-tag
                      :disable-transitions="true"
                      type="info"
                      effect="dark"
                      v-if="scope.row.auditStatus == '1'"
                      
                      >待审核</el-tag
                    >
                    <el-tag
                      :disable-transitions="true"
                      type="success"
                      effect="dark"
                      v-else-if="scope.row.auditStatus == '2'"
                      
                      >审核通过</el-tag
                    >
                    <el-tag
                      :disable-transitions="true"
                      type="danger"
                      effect="dark"
                      v-else-if="scope.row.auditStatus == '3'"
                      
                      >审核未通过</el-tag
                    >
                    <el-tooltip
                      v-else-if="scope.row.auditStatus == '3'"
                      class="item"
                      effect="dark"
                      :content="scope.row.auditReason"
                      placement="top"
                    >
                      <el-icon style="color: #f56c6c"><InfoFilled /></el-icon>
                    </el-tooltip>
                    <span v-else></span>
                  </template>
                </vxe-column>
                <vxe-column field="创建人" title="创建人">
                  <template #default="scope">
                    <div>{{ scope.row.createName }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="创建时间" title="创建时间" width="170">
                  <template #default="scope">
                    <div>{{
                      scope.row.createTime
                        ? moment(scope.row.createTime).format(
                            'YYYY-MM-DD HH:mm:ss'
                          )
                        : ''
                    }}</div>
                  </template>
                </vxe-column>
                <vxe-column field="操作" title="操作" width="120" fixed="right">
                  <template #default="scope">
                    <el-button
                      link
                      style="color: #f56c6c; margin-left: 10px"
                      @click="handleDelete(scope.row)"
                      ><el-icon><Delete /></el-icon>&nbsp;删除</el-button
                    >
                  </template>
                </vxe-column>
              </vxe-table>
              <!--分页-->
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  margin-top: 10px;
                "
              >
                <div></div>
                <el-pagination
                  class="page_bottom"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="formInline.currentPage"
                  :page-size="formInline.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="tableDataObj.total"
                >
                </el-pagination>
              </div>
              <!-- 表格和分页结束 -->
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
      :title="auditData.auditStatus == '3' ? '审核不通过' : '审核通过'"
      v-model="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="域名标识" prop="thirdDomain">
          <el-radio-group v-model="ruleForm.thirdDomain">
            <el-radio value="0">私有域名</el-radio>
            <el-radio value="1">第三方域名</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="原因" prop="auditReason">
          <el-input
            type="textarea"
            v-model="ruleForm.auditReason"
            :placeholder="
              auditData.auditStatus == '3'
                ? '请输入审核不通过原因'
                : '请输入审核通过原因'
            "
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit('ruleForm')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="添加白名单"
      v-model="whiteVisible"
      width="550px"
      :before-close="handleClose"
    >
      <el-form
        :model="whiteForm"
        :rules="rules"
        ref="whiteForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="白名单类型" prop="type">
          <el-radio-group v-model="whiteForm.type">
            <el-radio :disabled="formInline.type == '0'" value="1"
              >用户</el-radio
            >
            <el-radio :disabled="formInline.type == '1'" value="0"
              >系统</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="whiteForm.type == '1'"
          label="用户名"
          prop="userName"
        >
          <el-input
            v-model="whiteForm.userName"
            placeholder="用户名"
          ></el-input>
        </el-form-item>
        <el-form-item label="域名" prop="domain">
          <el-input
            type="textarea"
            v-model="whiteForm.domain"
            placeholder="多个域名以，分割"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="whiteForm.type == '1'"
          label="域名标识"
          prop="thirdDomain"
        >
          <el-radio-group v-model="whiteForm.thirdDomain">
            <el-radio value="0">私有域名</el-radio>
            <el-radio value="1">第三方域名</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="whiteVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleWhiteSubmit('whiteForm')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    Tooltip
  },
  name: 'shortDomain',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      shortStatus: 0, //短链状态
      dialogVisible: false, //审核不通过弹窗
      whiteVisible: false, //添加白名单弹窗
      formInline: {
        userName: '',
        compName: '',
        domain: '',
        auditStatus: '',
        type: '1',
        time: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        total: 0, //总条数
      },
      ruleForm: {
        auditReason: '',
        thirdDomain: '0',
      },
      whiteForm: {
        type: '1',
        thirdDomain: '0',
        userName: '',
        domain: '',
      },
      rules: {
        auditReason: [
          { required: true, message: '请输入审核不通过原因', trigger: 'blur' },
        ],
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        domain: [{ required: true, message: '请输入域名', trigger: 'blur' }],
      },
      auditData: {},
      selectId: [],
      isFirstEnter: false,
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getShortList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getShortList()
    })
  },
  mounted() {
    const $table1 = this.$refs.tableRef1
    const $toolbar1 = this.$refs.toolbarRef1
    if ($table1 && $toolbar1) {
      $table1.connect($toolbar1)
    }
    const $table2 = this.$refs.tableRef2
    const $toolbar2 = this.$refs.toolbarRef2
    if ($table2 && $toolbar2) {
      $table2.connect($toolbar2)
    }
  },
  methods: {
    //获取短链列表
    getShortList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.slms + 'shortLink/domain/page',
        this.formInline,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.total = res.data.total
            this.tableDataObj.loading2 = false
          }
        }
      )
    },
    //查询
    Query() {
      this.getShortList()
    },
    //重置
    Reload(formName) {
      console.log(formName,'formName');
      
      this.$refs[formName].resetFields() //清空查询表单
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.getShortList()
    },
    //-----复制功能
    // handleCopy(name, event) {
    //     clip(name, event)
    // },
    //-----翻页操作
    handleSizeChange(size) {
      this.formInline.pageSize = size
      this.getShortList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInline.currentPage = currentPage
      this.getShortList()
    },
    handleTimeChange(val) {
      if (val) {
        this.formInline.beginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formInline.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    //全选
    handleSelectionChange(val) {
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
      }
      this.selectId = selectId
      console.log(selectId, 'selectId')
    },
    batchDelete() {
      this.$confirms.confirmation(
        'post',
        '确认是否删除白名单？',
        window.path.slms + 'shortLink/domain/optDelete',
        { ids: this.selectId, type: this.formInline.type },
        (res) => {
          if (res.code == 200) {
            this.getShortList()
          }
        }
      )
    },
    handleDelete(row) {
      this.$confirms.confirmation(
        'post',
        '确认是否删除白名单？',
        window.path.slms + 'shortLink/domain/optDelete',
        { ids: [row.id], type: this.formInline.type },
        (res) => {
          if (res.code == 200) {
            this.getShortList()
          }
        }
      )
    },
    //审核通过
    handlePass(status, row) {
      let data = {
        // userName: row.userName,
        auditStatus: status,
        id: row.id,
      }
      this.$confirms.confirmation(
        'post',
        '域名申请审核确认通过！',
        window.path.slms + 'shortLink/domain/audit',
        data,
        (res) => {
          this.getShortList()
        }
      )
    },
    //审核不通过弹窗关闭
    handleClose() {
      this.dialogVisible = false
    },
    //审核不通过
    handleReject(status, row) {
      this.auditData = {
        auditStatus: status,
        id: row.id,
      }

      this.dialogVisible = true
    },
    //审核不通过
    handleSubmit(formName) {
      this.auditData.thirdDomain = this.ruleForm.thirdDomain
      this.auditData.auditReason = this.ruleForm.auditReason
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            this.auditData.auditStatus == '3'
              ? '域名申请审核不通过！'
              : '域名申请审核通过！',
            window.path.slms + 'shortLink/domain/audit',
            this.auditData,
            (res) => {
              this.getShortList()
              this.dialogVisible = false
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleClick(tab, event) {
      this.$refs['queryForm'].resetFields() //清空查询表单
      this.selectId = []
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.formInline.currentPage = 1
      this.formInline.pageSize = 10
      this.whiteForm.type = tab
      if (tab == '0') {
        this.whiteForm.thirdDomain = ''
      } else {
        this.whiteForm.thirdDomain = '0'
      }
      this.getShortList()
    },
    addWhiteList() {
      this.whiteVisible = true
    },
    //添加白名单确认
    handleWhiteSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            domainList: [
              {
                type: this.whiteForm.type,
                userName: this.whiteForm.userName,
                domain: this.whiteForm.domain,
              },
            ],
          }
          this.$confirms.confirmation(
            'post',
            '添加白名单确认！',
            window.path.slms + 'shortLink/domainAdd',
            data,
            (res) => {
              this.whiteVisible = false
              this.getShortList()
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs.ruleForm.resetFields() //清空表单
        this.auditData = {}
      }
    },
    whiteVisible(val) {
      if (!val) {
        this.$refs.whiteForm.resetFields() //清空表单
        this.whiteForm.type = this.formInline.type
        if (this.formInline.type == '0') {
          this.whiteForm.thirdDomain = ''
        } else {
          this.whiteForm.thirdDomain = '0'
        }
      }
    },
  },
}
</script>

<style lang="less" scoped>
.shortChain-box {
  padding: 20px;
}
.shortChain-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  font-size: 12px;
}
.shortChain-matter > p {
  padding: 5px 0px;
}
.short-box {
  margin: 10px 0;
}
.short-btn {
  margin-bottom: 10px;
}
:deep(.el-table__row) {
  height: 48px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
