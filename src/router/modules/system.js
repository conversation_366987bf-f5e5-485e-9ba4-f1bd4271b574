// 系统设置相关路由
export default [
  {
    path: '/AutoRecmanagement',
    meta: { title: '自动补发管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/AutoRecmanagement/AutoRecmanagement.vue'),
  },
  {
    path: '/salesManagement',
    meta: { title: '销售管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/salesManagement.vue'),
  },
  {
    path: '/MenuManagement',
    meta: { title: '菜单管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/MenuManagement.vue'),
  },
  {
    path: '/RoleManagement',
    meta: { title: '角色管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/RoleManagement.vue'),
  },
  {
    path: '/authorityManagement',
    meta: { title: '后台用户管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/authorityManagement.vue'),
  },
  {
    path: '/LoginLog',
    meta: { title: '登录日志', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/LoginLog.vue'),
  },
  {
    path: '/LoginCellPhone',
    meta: { title: '手机号登录管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/LoginCellPhone.vue'),
  },
  {
    path: '/OperationLog',
    meta: { title: '平台操作日志', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/OperationLog.vue'),
  },
  {
    path: '/PersonalInformation',
    meta: { title: '个人信息', keepAlive: true, isBack: false },
    component: () => import('@/components/page/MyAccount/PersonalInformation.vue'),
  },
  {
    path: '/Personalization',
    meta: { title: '个性化设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/Personalization.vue'),
  },
  {
    path: '/ComplaintManagement',
    meta: { title: '投诉管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/ComplaintManagement.vue'),
  },
] 