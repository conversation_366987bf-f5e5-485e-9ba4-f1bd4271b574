<template>
  <div class="materialTab">
    <div class="materialTab-left">
      <h3 style="font-size: 14px">基本信息</h3>
      <el-form ref="basicInfo" style="margin-top: 10px" label-width="100px">
        <el-form-item label="用户名称：" prop="userName">
          <p class="itemtext">{{ basicData.consumerName }}</p>
        </el-form-item>
        <el-form-item label="公司名称：" prop="companyName">
          <p class="itemtext">{{ basicData.compName }}</p>
        </el-form-item>
        <el-form-item label="手机号码：" prop="phoneNum">
          <p class="itemtext">{{ basicData.phone }}</p>
        </el-form-item>
        <el-form-item label="行业类别：" prop="industryType">
          <p class="itemtext">{{ basicData.industryName }}</p>
        </el-form-item>
        <el-form-item label="用户类型：" prop="userType">
          <p class="itemtext" v-if="basicData.roleId == 12">管理商</p>
          <p class="itemtext" v-if="basicData.roleId == 14">终端</p>
          <p
            class="itemtext"
            v-if="basicData.roleId != 12 && basicData.roleId != 14"
          >
            子用户
          </p>
        </el-form-item>
        <el-form-item label="所属客服：" prop="kefu">
          <p class="itemtext">{{ basicData.serviceName }}</p>
        </el-form-item>
        <el-form-item label="邮箱：" prop="kefu">
          <p class="itemtext">{{ basicData.email }}</p>
        </el-form-item>
        <el-form-item label="IP地址：" prop="ipNum">
          <p class="itemtext">{{ basicData.ip }}</p>
        </el-form-item>
        <el-form-item label="创建时间：">
          <p class="itemtext">{{ basicData.createTime }}</p>
        </el-form-item>
      </el-form>
    </div>
    <div class="materialTab-right">
      <h3 style="font-size: 14px">合同信息</h3>
      <el-form ref="basicInfo2" style="margin-top: 10px" label-width="110px">
        <el-form-item label="负责销售：" prop="saler">
          <p class="itemtext">{{ basicData.salesName }}</p>
        </el-form-item>
        <el-form-item label="所属区域：" prop="zone">
          <!-- <p class="itemtext">{{basicData}}</p> -->
        </el-form-item>
        <el-form-item label="上传合同：">
          {{ basicData.contractImage }}
        </el-form-item>
        <el-form-item label="上传营业执照：">
          {{ basicData.licenseImage }}
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'material',
  data() {
    return {
      clientId: '',
      basicData: {},
      industrys: [],
      client: '',
    }
  },
  methods: {
    getBasicData() {
      //获取基本信息
      window.api.get(
        window.path.omcs + 'operatinguser/' + this.clientId,
        {},
        (res) => {
          this.basicData = res.data
          console.log(this.basicData)
        }
      )
    },
  },
  created() {
    this.clientId = this.$route.query.id
    this.getBasicData()
  },
}
</script>

<style scoped>
.materialTab {
  overflow: hidden;
  clear: both;
  padding: 30px 0;
  border-bottom: 1px solid #e5e5e5;
}
.materialTab-left {
  float: left;
  width: 35%;
  min-width: 350px;
}
.materialTab-right {
  float: left;
  width: 35%;
}
.itemtext {
  font-size: 12px;
  color: #999;
}
</style>
