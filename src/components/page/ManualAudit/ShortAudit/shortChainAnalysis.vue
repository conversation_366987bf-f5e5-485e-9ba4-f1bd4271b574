<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名称" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="短链" prop="shortCode">
              <el-input
                v-model="formInline.shortCode"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="创建时间" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="true"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click="ListSearch"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>

        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            @selection-change="handleSelectionChange"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
              <el-button
                type="primary"
                v-if="ids.length != 0"
                @click="batchChecked(0, '可用')"
                >批量生效</el-button
              >
              <el-button
                type="danger"
                @click="batchChecked(1, '不可用')"
                v-if="ids.length != 0"
                >批量失效</el-button
              >
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="shortChainAnalysis"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handleSelectionChange"
            @checkbox-change="handleSelectionChange">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="用户名称" title="用户名称">
              <template #default="scope">
                <div>{{ scope.row.userName }}</div>
              </template>
            </vxe-column>
            <vxe-column field="短链接" title="短链接">
              <template #default="scope">
                <!-- <a class="url" :href="'https://' + scope.row.shortCode" target="_blank" rel="noopener noreferrer">{{
                      scope.row.shortCode }}</a> -->
                <Tooltip
                  v-if="scope.row.shortCode"
                  :content="scope.row.shortCode"
                  typeLable="a"
                  className="wrapper-text"
                  effect="light"
                ></Tooltip>
                <!-- {{ scope.row.shortCode }} -->
              </template>
            </vxe-column>
            <vxe-column field="长连接" title="长连接" width="200">
              <template #default="scope">
                <div style="display: flex; align-items: center">
                  <!-- <i style="color:#409eff;cursor: pointer;margin-right:10px;font-size: 14px;"
                        class="el-icon-document-copy" @click="handleCopy(scope.row.originalUrl, $event)"></i> -->
                  <CopyTemp :content="scope.row.originalUrl" />
                  <div style="width: 150px">
                    <Tooltip
                      v-if="scope.row.originalUrl"
                      :content="scope.row.originalUrl"
                      typeLable="a"
                      className="wrapper-text"
                      effect="light"
                    ></Tooltip>
                  </div>
                </div>

                <!-- <a class="url" :href="scope.row.originalUrl" target="_blank" rel="noopener noreferrer">{{
                      scope.row.originalUrl }}</a> -->
                <!-- <span>{{ scope.row.originalUrl }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="有效时间" title="有效时间" width="110">
              <template #default="scope">
                <p v-if="scope.row.expireTime">
                  {{ $parseTime(scope.row.expireTime, "{y}-{m}-{d}") }}
                </p>
                <p v-if="scope.row.expireTime">
                  {{ $parseTime(scope.row.expireTime, "{h}:{i}:{s}") }}
                </p>
                <!-- <span>{{
                      moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                    }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="创建时间" title="创建时间" width="110">
              <template #default="scope">
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
                </p>
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, "{h}:{i}:{s}") }}
                </p>
                <!-- <span>{{
                      moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                    }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="链接打开次数" title="链接打开次数">
              <template #default="scope">
                <div>{{ scope.row.countNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="短链可用状态" title="短链可用状态" width="120">
              <template #default="scope">
                <div style="color: red" v-if="scope.row.available == 1"
                  >失效</div
                >
                <div style="color: #16a589" v-else>生效</div>
              </template>
            </vxe-column>
            <vxe-column field="备注" title="备注">
              <template #default="scope">
                <Tooltip
                  v-if="scope.row.reason"
                  :content="scope.row.reason"
                  className="wrapper-text"
                  effect="dark"
                >
                </Tooltip>
                <!-- <span>{{ scope.row.reason }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="170">
              <template #default="scope">
                <el-button
                  v-if="scope.row.available == 0"
                  link
                  style="color: red; margin-left: 10px"
                  @click="checked(scope.row, 1, '不可用')"
                  ><el-icon><Remove /></el-icon> 失效</el-button
                >
                <el-button
                  v-if="scope.row.available == 1"
                  link
                  style="color: #16a589; margin-left: 10px"
                  @click="checked(scope.row, 0, '可用')"
                  ><el-icon><CirclePlus /></el-icon> 生效</el-button
                >
                <el-button
                  v-if="scope.row.available == 0"
                  link
                  style="color: #16a589; margin-left: 10px"
                  @click="setAvailableTime(scope.row)"
                  ><el-icon><Tools /></el-icon> 设置有效期</el-button
                >
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @routers='routers'></table-tem> -->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

          <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form ref="from" :model="from" :rules="rules" class="demo-form-inline">
        <el-form-item label="备注" prop="reason">
          <el-input
            type="textarea"
            v-model="from.reason"
            placeholder="备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm('from')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="设置有效期"
      v-model="timeVisible"
      width="450px"
      :before-close="handleClose"
    >
      <el-form
        :model="shotReseTimeData"
        :rules="rules"
        ref="shotReseTimeData"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            disabled
            v-model="shotReseTimeData.userName"
            placeholder="用户名"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="短链码" prop="shortCode">
          <el-input
            disabled
            class="input-w"
            v-model="shotReseTimeData.shortCode"
            placeholder="短链码"
          ></el-input>
        </el-form-item>
        <el-form-item label="有效期" prop="resetTime">
          <el-date-picker
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="shotReseTimeData.resetTime"
            type="datetime"
            clearable
            placeholder="选择日期时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="timeVisible = false">取 消</el-button>
          <el-button
            type="primary"
            @click="handleTimeSubmit('shotReseTimeData')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="批量续期"
      v-model="renewalvisible"
      width="450px"
      :before-close="handleClose"
    >
      <el-form
        :model="renewalForm"
        :rules="renewalRules"
        ref="renewalForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="renewalForm.userName"
            placeholder="用户名"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="有效期" prop="resetTime">
          <el-date-picker
            :picker-options="pickerOptions"
            v-model="renewalForm.resetTime"
            type="datetime"
            placeholder="选择日期时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="文件上传" prop="fileList">
          <el-upload
            class="upload-demo"
            :action="window.path.cpus + 'v3/file/upload'"
            :headers="token"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
            :before-upload="beforeAvatarUpload"
            :limit="1"
            :file-list="renewalForm.fileList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              <a
                style="margin-left: 10px"
                href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/e56a609a1589c9452b66d6489f578560"
                target="_blank"
                rel="noopener noreferrer"
                >模版下载</a
              >
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="renewalvisible = false">取 消</el-button>
          <el-button type="primary" @click="handleRenewalSubmit('renewalForm')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
// import clip from '../../../../utils/clipboard'
import moment from "moment";
import CopyTemp from "@/components/publicComponents/CopyTemp.vue";
export default {
  components: {
    DatePlugin,
    TableTem,
    Tooltip,
    CopyTemp,
  },
  name: "shortChainAnalysis",
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      dialogVisible: false,
      timeVisible: false,
      renewalvisible: false,
      token: {},
      title: "",
      times: [
        new Date().toLocaleDateString().split("/").join("-"),
        new Date().toLocaleDateString().split("/").join("-"),
      ],
      ids: [],
      // 搜索数据
      formInline: {
        userName: "",
        shortCode: "",
        time: [
          moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        ],
        beginTime: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        userName: "",
        shortCode: "",
        time: [
          moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        ],
        beginTime: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        pageSize: 10,
        currentPage: 1,
      },
      from: {
        reason: "",
        available: "",
        id: "",
      },
      rules: {
        reason: [
          {
            required: true,
            message: "请填写备注!",
            trigger: "change",
          },
        ],
        resetTime: [
          {
            required: true,
            message: "请选择有效期!",
            trigger: "change",
          },
        ],
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],

        // tableLabel: [
        //   // { prop:"shortLinkTime",showName:'发送时间', width:'140', fixed:false},
        //   { prop: "userName", showName: "用户名称", fixed: false },
        //   { prop: "shortCode", showName: "短链接", fixed: false },
        //   { prop: "originalUrl", showName: "长连接", fixed: false },
        //   { prop: "createTime", showName: "创建时间", fixed: false },
        //   { prop: "countNum", showName: "链接打开次数", fixed: false },
        // ],
        // tableStyle: {
        //   isSelection: false, //是否复选框
        //   isExpand: false, //是否是折叠的
        //   style: {
        //     //表格样式,表格宽度
        //     width: "100%",
        //   },
        //   optionWidth: "120", //操作栏宽度
        //   border: true, //是否边框
        //   stripe: false, //是否有条纹
        // },
        // tableOptions: [
        //   {
        //       optionName:'查看详情',
        //       type:'',
        //       size:'mini',
        //       optionMethod:'exportNums',
        //       icon:''
        //   },
        // ],
      },
      shotReseTimeData: {
        userName: "",
        shortCode: "",
        resetTime: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000;
        },
      },
      renewalForm: {
        fileUrl: "",
        userName: "",
        resetTime: "",
        fileList: [],
      },
      renewalRules: {
        userName: [
          {
            required: true,
            message: "请填写用户名!",
            trigger: "change",
          },
        ],
        resetTime: [
          {
            required: true,
            message: "请选择有效期!",
            trigger: "change",
          },
        ],
        fileList: [
          {
            required: true,
            message: "请上传文件!",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.slms + "v3/shortLink/report/all",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.list;
          this.tableDataObj.tablecurrent.total = res.data.total;
        }
      );
    },
    //查看详情跳转
    handelOptionButton(val) {
      if (val.methods == "exportNums") {
        this.$router.push({
          path:
            "/detailShort?code=" + val.row.shortCode + "&id=" + val.row.userId,
        });
      }
    },
    routers(val) {
      this.$router.push({ path: "/UserDetail", query: { id: val.userId } });
    },
    // 查询
    ListSearch() {
      if (this.tableDataObj.loading2) return; //防止重复请求
      if (this.formInline.userName != "" || this.formInline.shortCode != "") {
        Object.assign(this.formInlines, this.formInline);
        this.InquireList();
      }else {
        this.$message.warning("请输入用户名或短链码进行搜索");
        return;
      }
    },
    // 重置
    Reset(formName) {
      // this.times=[new Date().toLocaleDateString().split('/').join('-'),new Date().toLocaleDateString().split('/').join('-')];
      // this.formInline.beginTime=new Date().toLocaleDateString().split('/').join('-')+' 00:00:00';
      // this.formInline.endTime=new Date().toLocaleDateString().split('/').join('-')+" 23:59:59";
      this.$refs[formName].resetFields();
      this.formInline.userName = "";
      this.formInline.beginTime = moment().startOf("day").format("YYYY-MM-DD HH:mm:ss");
      this.formInline.endTime = moment().endOf("day").format("YYYY-MM-DD HH:mm:ss");
      this.formInline.time = [moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"), moment().endOf("day").format("YYYY-MM-DD HH:mm:ss")];
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.formInline.endTime = this.moment(val[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
    // handleCopy(name, event) {
    //   clip(name, event)
    // },
    handleClose() {
      this.dialogVisible = false;
      this.timeVisible = false;
    },
    checked(row, val, tit) {
      this.dialogVisible = true;
      this.title = tit;
      this.from.available = val;
      this.from.id = row.id;
    },
    handleSelectionChange(val) {
      // console.log(val);
      let selectId = [];
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id);
      }
      this.ids = selectId;
      // console.log(this.ids);
    },
    batchChecked(status, tit) {
      this.$confirms.confirmation(
        "post",
        `是否${tit}该短链？`,
        window.path.slms + "v3/shortLink/report/available/batch",
        {
          available: status,
          idArr: this.ids,
        },
        (res) => {
          // this.dialogVisible = false
          this.InquireList();
        }
      );
    },
    submitForm(from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            `是否${this.title}该短链？`,
            window.path.slms + "v3/shortLink/report/available",
            this.from,
            (res) => {
              this.dialogVisible = false;
              this.InquireList();
            }
          );
        }
      });
    },
    setAvailableTime(row) {
      this.shotReseTimeData.userName = row.userName;
      this.shotReseTimeData.shortCode = row.originShortCode;
      this.timeVisible = true;
      // this.shotReseTimeData.resetTime = row.availableTime;
    },
    handleTimeSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.shotReseTimeData.resetTime = moment(
            this.shotReseTimeData.resetTime
          ).format("YYYY-MM-DD HH:mm:ss");
          this.$confirms.confirmation(
            "post",
            `是否设置有效期？`,
            window.path.slms + "shortLink/resetTime",
            this.shotReseTimeData,
            (res) => {
              this.timeVisible = false;
              this.InquireList();
            }
          );
        } else {
          console.log("请选择有效期");
          //  this.$message.error('请选择有效期')
        }
      });
    },
    batchReset() {
      this.renewalvisible = true;
    },
    beforeAvatarUpload(file) {
      const siJPGGIF = file.name.split(".")[1];
      const fileType = ["txt"];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning("上传文件只能是txt格式!");
        return false;
      }
    },
    handleRemove(file, fileList) {
      this.renewalForm.fileList = [];
      this.renewalForm.fileUrl = "";
    },
    handleSuccess(res, file, fileList) {
      if (res.code == 200) {
        this.renewalForm.fileList = fileList;
        this.renewalForm.fileUrl = res.data.fullpath;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRenewalSubmit(formName) {
      this.$refs[formName].validate((valid, f) => {
        if (valid) {
          let data = {
            userName: this.renewalForm.userName,
            resetTime: moment(this.renewalForm.resetTime).format(
              "YYYY-MM-DD HH:mm:ss"
            ),
            fileUrl: this.renewalForm.fileUrl,
          };
          this.$confirms.confirmation(
            "post",
            `是否批量续期？`,
            window.path.slms + "v3/shortLink/resetBatch",
            data,
            (res) => {
              if (res.code == 200) {
                this.renewalvisible = false;
                this.InquireList();
              }
            }
          );
        } else {
          console.error("error submit!!");
        }
      });
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.token = {
          Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
        };
        // this.InquireList();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.token = {
          Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
        };
      // this.InquireList();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.from.available = "";
        this.from.id = "";
        this.from.reason = "";
        this.$refs["from"].resetFields();
      }
    },
    timeVisible(val) {
      if (!val) {
        this.shotReseTimeData.resetTime = "";
        this.$refs["shotReseTimeData"].resetFields();
      }
    },
    renewalvisible(val) {
      if (!val) {
        this.renewalForm.fileUrl = "";
        this.renewalForm.fileList = [];
        this.renewalForm.userName = "";
        this.renewalForm.resetTime = "";
        this.$refs["renewalForm"].resetFields();
      }
    },
    // 监听搜索/分页数据
    // fthis.InquireList()
  },
};
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.url {
  color: #409eff;
  text-decoration: underline;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
