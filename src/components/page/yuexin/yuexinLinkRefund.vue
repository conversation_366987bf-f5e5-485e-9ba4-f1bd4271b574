<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>

        <el-form-item label="返还状态" prop="status">
          <el-select
            class="input-w"
            v-model="ruleForm.status"
            clearable
            placeholder="请选择"
          >
            <el-option label="全部 " value=""> </el-option>
            <el-option label="待返回 " value="0"> </el-option>
            <el-option label="已返还" value="1"> </el-option>
            <el-option label="返回失败" value="2"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短链创建时间" prop="value1">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handelDate"
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="短链" prop="link">
                        <el-input v-model="ruleForm.link" placeholder="阅信链接/AIM链接"></el-input>
                    </el-form-item> -->
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="yuexinLinkRefund"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column
          field="用户名称"
          title="用户名称"
          width="120"
        >
          <template v-slot="scope">
            <div>
              {{ scope.row.username }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="链接ID" title="链接ID" width="120">
          <template v-slot="scope">
            <div>
              {{ scope.row.linkId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                          class="el-icon-document-copy" @click="handleCopy(scope.row.linkId, $event)"></i> -->
              <CopyTemp :content="scope.row.linkId" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="模板ID" title="模板ID" width="190">
          <template v-slot="scope">
            <div>
              <!-- <Tooltip v-if="scope.row.templateId" :content="scope.row.templateId" className="wrapper-text"
                                  effect="light">
                              </Tooltip> -->
              {{ scope.row.templateId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                          class="el-icon-document-copy" @click="handleCopy(scope.row.templateId, $event)"></i> -->
              <CopyTemp :content="scope.row.templateId" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="模板名称" title="模板名称" width="120">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.templateName"
              :content="scope.row.templateName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- {{ scope.row.templateName }} -->
          </template>
        </vxe-column>
        <vxe-column field="短链创建时间" title="短链创建时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time" v-if="scope.row.createTime">{{
                                    moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
          </template>
        </vxe-column>
        <!-- <vxe-column title="阅信链接" width="120">
                        <template #default="scope">
                            <a v-if="scope.row.link" :href="'https://' + scope.row.link" target="_blank"
                                rel="noopener noreferrer">{{
                                        scope.row.link
                                }}</a>
                        </template>
                    </vxe-column> -->
        <vxe-column field="阅信解析次数上限" title="阅信解析次数上限">
          <template v-slot="scope">
            <div>
              {{ scope.row.total }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="阅信已解析次数" title="阅信已解析次数">
          <template v-slot="scope">
            <div>
              {{ scope.row.used }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="阅信返还解析次数" title="阅信返还解析次数">
          <template v-slot="scope">
            <div>
              {{ scope.row.refund }}
            </div>
          </template>
        </vxe-column>
        <!-- <vxe-column title="操作人" width="120">
                        <template #default="scope">{{ scope.row.operatorUser }}</template>
                    </vxe-column> -->
        <vxe-column field="操作时间" title="操作时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.operatorTime">
              {{ $parseTime(scope.row.operatorTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.operatorTime">
              {{ $parseTime(scope.row.operatorTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time" v-if="scope.row.refundTime">{{
                                    moment(scope.row.refundTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="返还状态" title="返还状态" fixed="right" width="120">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.status == 0"
            >
              待返还
            </div>
            <div
              class="item_status"
              style="
                background: #dae9ff;
                color: #4e95ff;
                border: 1px solid #c2d6f3;
              "
              v-if="scope.row.status == 1"
            >
              已返还
            </div>
            <div
              class="item_status"
              style="
                background: #ffe7e7;
                color: #ff6666;
                border: 1px solid #fac2c2;
              "
              v-if="scope.row.status == 2"
            >
              返还失败
            </div>
          </template>
        </vxe-column>
        <vxe-column field="返还原因" title="返还原因" fixed="right" width="120">
          <template v-slot="scope">
            <div v-if="scope.row.refundReason == 1">失败返还</div>
            <div v-if="scope.row.refundReason == 2">到期返还</div>
          </template>
        </vxe-column>
        <!-- <vxe-column title="操作" fixed="right" width="150">
                        <template #default="scope">
                            <el-button v-if="scope.row.status == 0 || scope.row.status == 2" type="text" style="color: #409EFF; margin-left: 0px"
                                @click="regenerate(scope.row)">返还金额</el-button>
                        </template>
                    </vxe-column> -->
      </vxe-table>
      <div class="paginationBox">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      title="返还金额"
      v-model="dialogEditNum"
      width="30%"
      :before-close="handleClose"
    >
      <el-descriptions title="阅信信息" size="medium" :column="2">
        <el-descriptions-item label="模板名称">{{
          updateList.templateName
        }}</el-descriptions-item>
        <!-- 阅信 -->
        <el-descriptions-item label="阅信链接">{{
          updateList.link
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信解析数上限">
          <span>{{ updateList.total }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="阅信已解析数">{{
          updateList.used
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信返还解析次数">{{
          updateList.refund
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信更新时间">
          <span v-if="updateList.operatorTime">{{
            moment(updateList.operatorTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </el-descriptions-item>
        <!-- AIM -->
      </el-descriptions>
      <el-form :model="formInline" class="demo-form-inline">
        <!-- <el-form-item label="阅信已解析次数">
                        <el-input style="width:200px"  v-model="formInline.usedTimes" placeholder="请输入解析次数"></el-input>
                    </el-form-item> -->
        <el-form-item label="">
          <el-checkbox v-model="formInline.checked"
            >确认以上信息无误</el-checkbox
          >
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogEditNum = false">取 消</el-button>
          <el-button
            type="primary"
            :disabled="!formInline.checked"
            @click="handelquite"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../utils/clipboard'
// import viewXsmsTemo from "./components/viewXsms.vue";
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    Tooltip,
CopyTemp,
  },
  data() {

return {
  customConfig: {
    storage: true,
    // mode: "popup"
  },
  status: '',
title: '',
reason: '',
usedTimes: '',
xsmsUrl: '',
usedTimesIndex: 0,
dialogVisible: false,
dialogEditNum: false,
codeFlag: true,
value1: [],
formInline: {
checked: false,
id: '',
},
updateList: {},
ruleForm: {
// value1: [],
status: '',
startTime: '',
endTime: '',
username: '',
// status: '',
link: '',
currentPage: 1,
pageSize: 10,
},
reasonFrom: {
reason: '',
},
rules: {
reason: [
{ required: true, message: '审核原因不能为空', trigger: 'blur' },
],
},
tabelAlllist: {
// value1: [],
username: '',
status: '',
startTime: '',
endTime: '',
link: '',
currentPage: 1,
pageSize: 10,
},
detail: {},
tableDataObj: {
loading2: false,
tableData: [],
total: 0,
},
isFirstEnter: false,
}
},
name: 'xsmsRecord',
created() {
this.isFirstEnter = true
this.$nextTick(() => {
this.getchatList()
})
},
mounted() {
  const $table = this.$refs.tableRef
  const $toolbar = this.$refs.toolbarRef
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
},
activated() {
if (this.$route.meta.isBack || !this.isFirstEnter) {
this.$nextTick(() => {
  this.getchatList()
})
} else {
this.$route.meta.isBack = false
this.isFirstEnter = false
}
},
methods: {
getchatList() {
this.tableDataObj.loading2 = true
// this.tabelAlllist.status = this.tabelAlllist.shortStatus.join(',')
window.api.post(
  window.path.yuexin + '/operator/link/refund/page',
  this.tabelAlllist,
  (res) => {
    this.tableDataObj.tableData = res.data.records
    this.tableDataObj.total = res.data.total
    this.tableDataObj.loading2 = false
    // console.log(res, "res");
  }
)
},
// getRowClass({ rowIndex, columnIndex }) {
//     if (columnIndex == 13||columnIndex == 14||columnIndex == 15||columnIndex == 16||columnIndex == 17||columnIndex == 18||columnIndex == 19) {
//         return "background:#EEFBEE;";
//     }else if(columnIndex == 6||columnIndex == 7||columnIndex == 8||columnIndex == 9||columnIndex == 10 ||columnIndex == 11||columnIndex == 12){
//         return "background:#EDF4FF;";
//     }
// },
regenerate(row) {
this.updateList = row
this.dialogEditNum = true
this.formInline.id = row.id
},
// handleCopy(name, event) {
//     clip(name, event)
// },
handelquite() {
this.$confirms.confirmation(
  'post',
  '是否确认返还！',
  window.path.yuexin + '/operator/link/refund',
  {
    id: this.formInline.id,
  },
  (res) => {
    if (res.code == 200) {
      this.getchatList()
      this.dialogEditNum = false
    }
  }
)
},
handleClose() {
// this.dialogVisible = false;
this.dialogEditNum = false
},
handelDate(val) {
if (val) {
  this.ruleForm.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
  this.ruleForm.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
} else {
  this.ruleForm.startTime = ''
  this.ruleForm.endTime = ''
}
},
handleSizeChange(size) {
this.tabelAlllist.pageSize = size
this.getchatList()
},
handleCurrentChange(currentPage) {
this.tabelAlllist.currentPage = currentPage
this.getchatList()
},
submitForm() {
Object.assign(this.tabelAlllist, this.ruleForm)
this.getchatList()
},
resetForm() {
this.$refs.ruleForm.resetFields()
this.ruleForm.startTime = ''
this.ruleForm.endTime = ''
Object.assign(this.tabelAlllist, this.ruleForm)
this.value1 = []
this.getchatList()
},
checked(row) {
this.dialogVisible = true
this.registerFrom = row
this.registerFrom.renewed = row.renewed + ''
this.registerFrom.industryTypeCode = row.industryTypeCode * 1
this.status = row.customerStatus
//   console.log(this.registerFrom );
if (row.code) {
  this.codeFlag = true
} else {
  this.codeFlag = false
}
},
},
watch: {
dialogVisible(val) {
if (!val) {
  this.reason = ''
  this.reasonFrom.reason = ''
  // this.$refs['reasonFrom'].resetFields();
  // this.detail = {};
  if (this.detail.status == 0 || this.detail.status == 5) {
    this.$refs['reasonFrom'].resetFields()
  }
}
},
dialogEditNum(val) {
if (!val) {
  this.formInline.checked = false
  this.formInline.id = ''
}
},
}
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>

<style>
/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>