<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" label-width="82px" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道编号" label-width="82px" prop="channelId">
            <el-input
              v-model="formInline.channelId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" label-width="82px" prop="status">
            <el-select
              v-model="formInline.status"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="启用" value="0"></el-option>
              <el-option label="禁用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运营商" label-width="82px" prop="operator">
            <el-select
              v-model="formInline.operator"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份选择" prop="provincialId" label-width="82px">
            <el-select
              v-model="formInline.provincialId"
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in ProvincesCities"
                :key="index"
                :label="item.provincial"
                :value="item.provincialId"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label-width="82px">
                       
                        </el-form-item> -->
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          style
          @click="showDialog"
          >添加通道</el-button
        >
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @save="save"
        ></table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
      width="480px"
    >
      <el-form
        :model="formAddData"
        :rules="formRule"
        ref="formRules"
        label-width="110px"
        style="padding: 0 28px"
      >
        <el-form-item label="用户名" label-width="82px" prop="username">
          <el-input
            v-model="formAddData.username"
            :disabled="title == '编辑个性化通道'"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="title != '编辑个性化通道'"
          label="运营商"
          label-width="82px"
          prop="operators"
        >
          <el-select
            v-model="formAddData.operators"
            multiple
            placeholder="请选择"
            class="input-w"
          >
            <el-option label="移动" value="1"></el-option>
            <el-option label="联通" value="2"></el-option>
            <el-option label="电信" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="运营商" label-width="82px" prop="operator">
          <el-select
            v-model="formAddData.operator"
            :disabled="title == '编辑个性化通道'"
            placeholder="请选择"
            class="input-w"
          >
            <el-option label="移动" value="1"></el-option>
            <el-option label="联通" value="2"></el-option>
            <el-option label="电信" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="省份选择" prop="provincialId" label-width="82px">
          <el-select
            :disabled="title == '编辑个性化通道'"
            v-model="formAddData.provincialId"
            placeholder="请选择"
            class="input-w"
          >
            <el-option
              v-for="(item, index) in ProvincesCities"
              :key="index"
              :label="item.provincial"
              :value="item.provincialId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通道编号" label-width="82px" prop="channelId">
          <el-input
            v-model="formAddData.channelId"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="量比(%)" label-width="82px" prop="percent">
          <el-input-number
            class="input-w"
            v-model="formAddData.percent"
            :min="0"
            :max="999999999"
            label="量比"
          ></el-input-number>
          <!-- <el-input v-model="formAddData.percent" placeholder="" class="input-w"></el-input> -->
        </el-form-item>
        <el-form-item label="生效时间" label-width="82px" prop="time">
          <el-date-picker
            class="input-w"
            v-model="time"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="hande1"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm('formRules')"
            >提 交</el-button
          >
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
// import { LxEmoji as ElIconLxEmoji } from "@element-plus/icons";
import ChannelView from "@/components/publicComponents/ChannelView.vue";
import TableTem from "@/components/publicComponents/TableTem.vue";
export default {
  components: {
    ChannelView,
    TableTem,
    // ElIconLxEmoji,
  },
  data() {
    var percents = (rule, value, callback) => {
      if (value == "" || value == null) {
        callback();
      } else {
        if (!/^([1-9]?\d|100)$/.test(value)) {
          return callback(new Error("请输入0-100正整数"));
        } else {
          callback();
        }
      }
    };
    return {
      isFirstEnter: false,
      //传递通道值
      ChannelData: "",
      formInline: {
        channelId: "",
        username: "",
        operator: "",
        status: "",
        provincialId: "",
        currentPage: 1,
        pageSize: 10,
      },
      //查询赋值数据
      tabelAlllist: {
        channelId: "",
        username: "",
        operator: "",
        status: "",
        provincialId: "",
        currentPage: 1,
        pageSize: 10,
      },
      title: "新增个性化通道",
      dialogFormVisible: false,
      //省份选择
      ProvincesCities: [],
      UserId: "",
      formAddData: {
        channelId: "",
        username: "",
        operators: "",
        operator: "",
        provincialId: "",
        percent: 0,
        beginTime: "",
        endTime: "",
      },
      time: [],
      formRule: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "change" },
        ],
        provincialId: [
          { required: true, message: "省份不能为空", trigger: "change" },
        ],
        operators: [
          { required: true, message: "运营商不能为空", trigger: "change" },
        ],
        channelId: [
          { required: true, message: "通道编号不能为空", trigger: "change" },
        ],
        percent: [{ required: false, validator: percents, trigger: "change" }],
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        custom: true,
        total: 0,
        id: "SpecialChannel",
        tableData: [],
        tableLabel: [
          {
            prop: "username",
            showName: "用户名",
            width: "90",
          },
          {
            prop: "operator",
            showName: "运营商",
            formatData: function (val) {
              if (val == 1) {
                return (val = "移动");
              }
              if (val == 2) {
                return (val = "联通");
              }
              if (val == 3) {
                return (val = "电信");
              }
            },
          },
          {
            prop: "provincial",
            showName: "省市",
          },
          {
            prop: "channelId",
            showName: "通道编号",
            Channel: true,
          },
          {
            prop: "percent",
            showName: "量比(%)",
          },
          {
            prop: "status",
            showName: "状态",
            formatData: function (val) {
              if (val == 0) {
                return (val = "启用");
              }
              if (val == 1) {
                return (val = "禁用");
              }
            },
            showCondition: {
              condition: "1",
            },
          },
          {
            prop: "beginTime",
            showName: "生效开始时间",
            width: 160,
          },
          {
            prop: "endTime",
            showName: "生效结束时间",
            width: 160,
          },
          {
            prop: "createTime",
            showName: "创建时间",
            width: 160,
          },
          {
            prop: "createName",
            showName: "创建人",
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "200", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: "status", //关联的表格属性
            contactData: "0", //关联的表格属性-值
            optionName: "停用", //按钮的显示文字
            optionMethod: "start", //按钮的方法
            icon: "el-icon-circle-close-outline", //按钮图标
            optionButtonColor: "orange", //按钮颜色
            otherOptionName: "启用", //其他条件的按钮显示文字
            otherOptionMethod: "start", //其他条件的按钮方法
            otherIcon: "el-icon-circle-check-outline", //其他条件按钮的图标
            optionOtherButtonColor: "#16A589", //其他条件按钮的颜色
          },
          {
            contactCondition: "", //关联的表格属性
            contactData: "", //关联的表格属性-值
            optionName: "", //按钮的显示文字
            optionMethod: "", //按钮的方法
            icon: "EditPen", //按钮图标
            optionButtonColor: "", //按钮颜色
            otherOptionName: "编辑", //其他条件的按钮显示文字
            otherOptionMethod: "edit", //其他条件的按钮方法
            otherIcon: "EditPen", //其他条件按钮的图标
            optionOtherButtonColor: "#16A589", //其他条件按钮的颜色
          },
          {
            contactCondition: "", //关联的表格属性
            contactData: "", //关联的表格属性-值
            optionName: "", //按钮的显示文字
            optionMethod: "", //按钮的方法
            icon: "CircleCloseFilled", //按钮图标
            optionButtonColor: "", //按钮颜色
            otherOptionName: "删除", //其他条件的按钮显示文字
            otherOptionMethod: "dle", //其他条件的按钮方法
            otherIcon: "CircleCloseFilled", //其他条件按钮的图标
            optionOtherButtonColor: "#f56c6c", //其他条件按钮的颜色
          },
        ],
      },
    };
  },
  name: "SpecialChannel",
  methods: {
    showDialog() {
      this.title = '新增个性化通道';
      this.dialogFormVisible = true;
    },
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingchannelclient/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.tableData = res.data.records;
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val;
      this.$refs.ChannelRef.ChannelClick();
    },
    // 查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    //重置
    Reload() {
      this.$refs.formInline.resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.formAddData.beginTime = val[0];
        this.formAddData.endTime = val[1];
      } else {
        this.formAddData.beginTime = "";
        this.formAddData.endTime = "";
      }
    },
    handelOptionButton: function (val) {
      this.UserId = val.row.id;
      if (val.methods == "edit") {
        //编辑
        this.title = "编辑个性化通道";
        this.formAddData.username = val.row.username;
        this.formAddData.channelId = val.row.channelId;
        this.formAddData.operator = val.row.operator + "";
        this.formAddData.provincialId = val.row.provincialId;
        this.formAddData.percent = val.row.percent;
        this.formAddData.beginTime = val.row.beginTime;
        this.formAddData.endTime = val.row.endTime;
        if (val.row.beginTime && val.row.endTime) {
          this.time = [val.row.beginTime, val.row.endTime];
        }
        this.dialogFormVisible = true;
      } else if (val.methods == "start") {
        this.$confirms.confirmation(
          "put",
          val.row.status == 0 ? "确认停用通道？" : "确认启用通道？",
          window.path.omcs + "operatingchannelclient/enable",
          { id: this.UserId, enable: val.row.status == 0 ? false : true },
          () => {
            this.gettableLIst();
          }
        );
      } else if (val.methods == "dle") {
        this.$confirms.confirmation(
          "delete",
          "确认删除通道？",
          window.path.omcs + "operatingchannelclient/" + this.UserId,
          {},
          () => {
            this.gettableLIst();
          }
        );
      }
    },
    // 添加通道配置
    submitForm(formRules) {
      this.$refs.formRules.validate((valid) => {
        if (valid) {
          if (this.title == "新增个性化通道") {
            this.formAddData.operators = this.formAddData.operators.join(",");
            this.$confirms.confirmation(
              "post",
              "确认新增通道？",
              window.path.omcs + "operatingchannelclient",
              this.formAddData,
              () => {
                this.dialogFormVisible = false;
                this.gettableLIst();
              }
            );
          } else {
            this.formAddData.id = this.UserId;
            this.$confirms.confirmation(
              "put",
              "确认编辑通道？",
              window.path.omcs + "operatingchannelclient",
              this.formAddData,
              () => {
                this.dialogFormVisible = false;
                this.gettableLIst();
              }
            );
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  mounted() {
    // 获取省市
    window.api.get(
      window.path.omcs + "operatingchannelprovincial/list",
      {},
      (res) => {
        this.ProvincesCities = res.data;
      }
    );
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.gettableLIst();
    });
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //     handler() {
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate: true
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.formAddData = {
          channelId: "",
          username: "",
          operator: "",
          operators: "",
          provincialId: "",
          percent: 0,
          beginTime: "",
          endTime: "",
        };
        this.time = [];
        this.$refs.formRules.resetFields();
      }
    },
  },
};
</script>

<style>
.yunyingshang .el-form-item__content {
  margin-left: 10px !important;
}
</style>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 20px 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
