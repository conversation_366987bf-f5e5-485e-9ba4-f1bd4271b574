<template>
  <div class="sidebar">
    <el-menu
      class="sidebar-el-menu"
      :default-active="onRoutes"
      :collapse="collapse"
      @select="handleSelect"
      background-color="#fff"
      text-color="#303133"
      active-text-color="#409eff"
      unique-opened
      router
    >
      <template v-for="item in getMenus">
          <template v-if="item.children.length">
            <el-sub-menu :index="item.component + ''" :key="item.id" popper-class="popperClass">
              <template #title>
                <i :class="[{ icon: true, iconfont: true }, item.icon]" style="margin-right: 10px"></i>
                <span>{{ item.name }}</span>
              </template>
              <template v-for="subItem in item.children">
                <el-sub-menu v-if="subItem.children.length" :index="subItem.component + ''" :key="subItem.id">
                  <template #title>{{ subItem.name }}</template>
                  <el-menu-item v-for="(threeItem, i) in subItem.children" :key="i" :index="threeItem.component + ''">
                    {{ threeItem.name }}
                  </el-menu-item>
                </el-sub-menu>
                <el-menu-item v-else :index="subItem.component + ''" :key="subItem.id + 'a'">
                  {{ subItem.name }}
                </el-menu-item>
              </template>
            </el-sub-menu>
          </template>
          <template v-else>
            <el-menu-item :index="item.component + ''" :key="item.id">
              <i :class="[{ icon: true, iconfont: true }, item.icon]" style="margin-right: 10px"></i><span>{{
                item.name }}</span>
            </el-menu-item>
          </template>
        </template>
    </el-menu>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import bus from '../common/bus'
export default {
  data() {
    return {
      collapse: false,
      getMenus: [],
      onRoutes: '',
      items: [
        {
          icon: 'el-icon-lx-home',
          index: 'dashboard',
          title: '系统首页',
        },
        {
          icon: 'el-icon-lx-cascades',
          index: 'table',
          title: '基础表格',
        },
        {
          icon: 'el-icon-lx-copy',
          index: 'tabs',
          title: 'tab选项卡',
        },
        {
          icon: 'el-icon-lx-calendar',
          index: '3',
          title: '表单相关',
          children: [
            {
              index: 'form',
              title: '基本表单',
            },
            {
              index: '3-2',
              title: '三级菜单',
              children: [
                {
                  index: 'editor',
                  title: '富文本编辑器',
                },
                {
                  index: 'markdown',
                  title: 'markdown编辑器',
                },
              ],
            },
            {
              index: 'upload',
              title: '文件上传',
            },
          ],
        },
        {
          icon: 'el-icon-lx-emoji',
          index: 'icon',
          title: '自定义图标',
        },
        {
          icon: 'el-icon-lx-favor',
          index: 'charts',
          title: 'schart图表',
        },
        {
          icon: 'el-icon-rank',
          index: 'drag',
          title: '拖拽列表',
        },
        {
          icon: 'el-icon-lx-warn',
          index: '6',
          title: '错误处理',
          children: [
            {
              index: 'permission',
              title: '权限测试',
            },
            {
              index: '404',
              title: '404页面',
            },
          ],
        },
      ],
      path: 'wss://opt.zthysms.com/websocket/',
      socket: '',
      token: '',
      ws: '',
      title: '',
      timeout: 50000, //50秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      stcount: null,
      smcount: null,
      vcount: null,
      rcount: null,
      vtcount: null,
      originalUrl: null,
      chatbotcount: null,
      fgcount: null,
      chatbotTempCount: null,
      menuCount: null,
      yxCount: null,
    }
  },
  computed: {
    // onRoutes(){
    //     return this.$route.path.replace('/','');
    // }
  },
  created() {
    // 通过 Event Bus 进行组件间通信，来折叠侧边栏
    window.api.post(
      window.path.upms + 'menu/allTree',
      {
        platform: '1',
      },
      (res) => {
        // console.log(res,'res')
        this.getMenus = res
      }
    )
    this.token = window.common.getCookie('ZTADMIN_TOKEN')
    // console.log(this.token,'token');
    // this.init();
    let path = sessionStorage.getItem('path')
    if (path) {
      // console.log(path,'path');
      this.onRoutes = path
    }
    $on(bus, 'collapse', (msg) => {
      this.collapse = msg
    })
    $on(bus, 'path', (msg) => {
      this.onRoutes = msg
    })
  },
  methods: {
    deepSearchArrayForField(arr, value) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].component == value) {
          return arr[i]
        } else if (arr[i].children) {
          let result = this.deepSearchArrayForField(arr[i].children, value)
          if (result) {
            return result
          }
        }
      }
    },
    handleSelect(key, keyPath) {
      if (key == '/SignatureAudit') {
        this.smcount = null
      } else if (key == '/TemplateAudit') {
        this.stcount = null
      } else if (key == '/AuditCertification') {
        this.rcount = null
      } else if (key == '/RMSAudit') {
        this.vtcount = null
      } else if (key == '/shortChainAnalysis') {
        this.originalUrl = null
      } else if (key == '/chatbotAudit') {
        this.chatbotcount = null
      } else if (key == '/5gAuditCertification') {
        this.fgcount = null
      } else if (key == '/templateCardAudit') {
        this.chatbotTempCount = null
      } else if (key == '/MenuAudit') {
        this.menuCount = null
      } else if (key == '/yuexinAudit') {
        this.yxCount = null
      }
      this.onRoutes = key
      sessionStorage.setItem('path', key)
      let obj = this.deepSearchArrayForField(this.getMenus, key)
      localStorage.setItem('menu', JSON.stringify(obj))
      // console.log(obj, 'obj')
      // this.title = obj.name;
    },
    init: function () {
      let that = this
      if (typeof WebSocket === 'undefined') {
        this.$notify({
          title: '您的浏览器不支持socket',
        })
        // alert("您的浏览器不支持socket")
      } else {
        // // 实例化socket
        // this.socket = new WebSocket(this.path+this.token)
        // // 监听socket连接
        // this.socket.onopen = this.open
        // // 监听socket错误信息
        // this.socket.onerror = this.error
        // // 监听socket消息
        // this.socket.onmessage = this.getMessage
        that.ws = new WebSocket(window.path.socket + this.token)
        console.log(this.token, 'this.token')
        that.global.setWs(that.ws)
        that.ws.onopen = function () {
          // console.log("连接成功");
          that.start()
        }
        // 监听socket消息
        that.ws.onmessage = (msg) => {
          console.log(msg.data, 'msg')
          //   that.showDialog = true
          const h = this.$createElement
          if (msg.data == 'REGISTER') {
            that.showDialog = true
            that.rcount++
            // this.$notify.info({
            //     title: '注册账号资质审核',
            //     message: h('i', { style: 'color: teal'},`注册账号审核有${that.rcount}新消息，请查看！` ),
            //     duration:0,
            // });
          } else if (msg.data == 'SMS_SIGNATURE') {
            that.showDialog = true
            that.smcount++
            // this.$notify({
            //         title: '短信签名审核',
            //         message: h('i', { style: 'color: teal'},`短信签名审核有${that.smcount}新消息，请查看！` ),
            //         duration:0,
            //         iconClass:"el-icon-info",
            //     });
          } else if (msg.data == 'SMS_TEMPLATE') {
            that.showDialog = true
            that.stcount++
            // this.$notify.info({
            //         title: '短信模板审核',
            //         message: h('i', { style: 'color: teal'},`短信模板审核有${that.stcount}新消息，请查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == 'VIDEO_TEMPLATE') {
            that.showDialog = true
            that.vtcount++
            // this.$notify.info({
            //         title: '视频短信模板',
            //         message: h('i', { style: 'color: teal'},`视频短信模板审核有${that.vtcount}新消息，请查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == 'SHORT_CHART') {
            that.showDialog = true
            that.originalUrl++
            //  this.$notify.info({
            //         title: '短链提醒',
            //         message: h('i', { style: 'color: teal'},`有${that.originalUrl}条新短链提醒，请查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == 'CHATBOT_NEW_NOTICE') {
            that.showDialog = true
            that.chatbotcount++
            // this.$notify.info({
            //         title: 'chatbot审核',
            //         message: h('i', { style: 'color: teal'},`chatbot新增${that.chatbotcount}条审核通知，请及时查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == 'CUSTOMER_NEW_NOTICE') {
            that.showDialog = true
            that.fgcount++
            // this.$notify.info({
            //         title: '5G消息',
            //         message: h('i', { style: 'color: teal'},'有新的客户开启5G消息，请及时查看！' ),
            //         duration:0,
            //     });
          } else if (msg.data == 'TEMPLATE_AUDIT_NOTICE') {
            that.showDialog = true
            that.chatbotTempCount++
            // this.$notify.info({
            //         title: '5G模板审核',
            //         message: h('i', { style: 'color: teal'},`新增${that.chatbotTempCount}条5G模板审核通知，请及时查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == 'MENU_NOTICE') {
            that.showDialog = true
            that.menuCount++
            // this.$notify.info({
            //         title: '固定菜单审核',
            //         message: h('i', { style: 'color: teal'},`新增${that.menuCount}条固定菜单审核通知，请及时查看！` ),
            //         duration:0,
            //     });
          } else if (msg.data == 'YX_TEMPLATE_AUDIT') {
            that.showDialog = true
            that.yxCount++
          }
          that.reset()
        }
        //连接错误
        that.ws.onerror = function () {
          console.log('WebSocket连接发生错误')
          that.reconnect()
          //重连
          // if (that.title == "token为空") {
          //   that.global.ws.close();
          // } else {

          // }
        }
        that.ws.onclose = function () {
          // 关闭 websocket
          console.log('连接已关闭....1')
          that.global.ws.close()
          //断线重新连接
          //   setTimeout(() => {
          //     that.init();
          //   }, 2000);
        }
        // console.log(that.global.setWs);
      }
    },
    reconnect() {
      //重新连接
      let that = this
      if (that.global.ws && that.global.ws.readyState == 1) {
        clearInterval(that.timeoutnum)
        that.timeoutnum = null
        that.timeNum = 0
        return
      }
      if (!that.timeoutnum) {
        that.timeoutnum = setInterval(function () {
          if (that.global.ws && that.global.ws.readyState == 1) {
            clearInterval(that.timeoutnum)
            that.timeoutnum = null
            that.timeNum = 0
            return
          }
          //新连接
          that.init()
          that.timeNum++
          if (that.timeNum >= 3) {
            clearInterval(that.timeoutnum)
            that.timeoutnum = null
            that.timeNum = 0
          }
        }, 1000)
      }
    },
    reset() {
      //重置心跳
      //清除时间
      clearTimeout(this.timeoutObj)
      clearTimeout(this.serverTimeoutObj)
      //   this.smcount = null
      //   this.stcount = null
      //   this.vcount = null
      //   this.rcount = null
      //重启心跳
      this.start()
    },
    handleShow() {
      this.showDialog = !this.showDialog
    },
    start() {
      //开启心跳
      //   console.log('www','ll');
      let that = this
      let date = new Date()
      that.timeoutObj && clearTimeout(that.timeoutObj)
      that.serverTimeoutObj && clearTimeout(that.serverTimeoutObj)
      that.timeoutObj = setTimeout(function () {
        //这里发送一个心跳，后端收到后，返回一个心跳消息，
        if (that.global.ws && that.global.ws.readyState == 1) {
          //如果连接正常
          //   console.log(that.global.ws.readyState,'lll');

          that.global.ws.send(`发送心跳给后端${date}`)
        } else {
          //否则重连
          that.reconnect()
        }
      }, that.timeout)
    },
    reload() {
      this.isRouterAlive = false
      this.$nextTick(() => {
        this.isRouterAlive = true
      })
    },
    handclose() {
      this.showDialog = false
      this.smcount = null
      this.rcount = null
      this.vtcount = null
      this.stcount = null
      this.originalUrl = null
      this.yxCount = null
    },
  },
  watch: {
    $route(to, from) {
      this.onRoutes = to.path
      // console.log(to)
      // console.log(`前往路由：${to.path}`);
    },
  },
}
</script>

<style scoped>
.sidebar {
  display: block;
  position: absolute;
  left: 0;
  top: 50px;
  bottom: 0;
  overflow-y: scroll;
}
.sidebar::-webkit-scrollbar {
  width: 0;
}
.sidebar-el-menu:not(.el-menu--collapse) {
  width: 225px;
}
.sidebar > ul {
  min-height: 100%;
}
.tit_cont {
  display: inline-block;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  text-align: center;
  line-height: 15px;
  background: red;
  color: #fff;
  font-size: 12px;
}
</style>
