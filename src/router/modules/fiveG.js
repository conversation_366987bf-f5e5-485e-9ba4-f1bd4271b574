// 5G短信相关路由
export default [
  {
    path: '/5gAuditCertification',
    meta: { title: '5G资质', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/5gAuditCertification.vue'),
  },
  {
    path: '/5gChannel',
    meta: { title: '5G通道', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/channel5G.vue'),
  },
  {
    path: '/chatbotChannel',
    meta: { title: 'chatbot通道', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/chatbotChannel.vue'),
  },
  {
    path: '/5gkeywords',
    meta: { title: '关键词回复', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/keyword/keywords.vue'),
  },
  {
    path: '/chatbotAudit',
    meta: { title: 'chatbot审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/chatbot.vue'),
  },
  {
    path: '/pictureAudit',
    meta: { title: '素材审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/pictureAudit.vue'),
  },
  {
    path: '/pictureChannel',
    meta: { title: '素材通道', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/pictureChannel.vue'),
  },
  {
    path: '/templateCardAudit',
    meta: { title: '模板审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/templateCardAudit.vue'),
  },
  {
    path: '/MenuAudit',
    meta: { title: '固定菜单审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/MenuAudit.vue'),
  },
  {
    path: '/MenuChannel',
    meta: { title: '菜单报备', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/MenuChannel/menuChannel.vue'),
  },
  {
    path: '/xsmsTemplate',
    meta: { title: '5G阅信模板', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/xsmstemplate.vue'),
  },
  {
    path: '/xsmsSetting',
    meta: { title: '5G阅信解析配置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/xsmsSetting/xsmsSetting.vue'),
  },
  {
    path: '/xsmsRecord',
    meta: { title: '5G阅信费用返还', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/xsmsSetting/xsmsRecord.vue'),
  },
  {
    path: '/5gSendRecord',
    meta: { title: '发送记录', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/sendRecord.vue'),
  },
  {
    path: '/reportRecord',
    meta: { title: '回执记录', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/reportRecord.vue'),
  },
  {
    path: '/5gSetting',
    meta: { title: '5g配置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/5gSetting.vue'),
  },
  {
    path: '/5gSmsChannelSetting',
    meta: { title: '5g通道计费数配置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/5g/5gSmsChannelSetting.vue'),
  },
] 