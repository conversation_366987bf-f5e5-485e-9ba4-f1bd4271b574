<template>
  <div>
    <el-tooltip
      :placement="placement"
      :effect="effect"
      :disabled="isShowTooltip"
    >
      <template #content>
        <div v-if="temp" style="width: auto; max-width: 400px">
          <div :class="htmlClassName" v-html="content"></div>
          <div v-if="count" style="font-weight: 800; font-size: 14px">
            字数：{{ count }}
          </div>
        </div>
        <div v-else style="width: auto; max-width: 400px">
          <div>{{ content }}</div>
          <div v-if="count" style="font-weight: 800; font-size: 14px">
            {{ `字数：${count}` }}
          </div>
        </div>
      </template>
      <div ref="parent" @mouseover="onMouseOver()" :class="className">
        <span
          :class="htmlClassName"
          v-if="temp"
          ref="child"
          v-html="content"
        ></span>
        <a
          class="url"
          ref="child"
          v-if="typeLable == 'a'"
          :href="content"
          target="_blank"
          rel="noopener noreferrer"
          >{{ content }}</a
        >
        <span v-else ref="child">{{ `${content}` }}</span>
      </div>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    placement: {
      type: String,
      default: 'top',
    },
    //主题
    effect: {
      type: String,
      default: 'dark',
    },
    // 内容
    content: {
      type: [String, Number],
      default: '',
      validator: (value) => {
        return [String, Number].includes(value.constructor);
      }
    },
    // 类名
    className: {
      type: String,
      default: '',
    },
    // 模版
    temp: {
      type: String,
      default: '',
    },
    htmlClassName: {
      type: String,
      default: '',
    },
    // 字数
    count: {
      type: String,
      default: '',
    },
    // 类型
    typeLable: {
      type: String,
      default: '',
    },
    widthpx: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      // parent: "",
      // child: "",
      isShowTooltip: false,
    }
  },
  methods: {
    onMouseOver() {
      const parentWidth = this.$refs.parent.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs.child.offsetWidth // 获取元素可视宽度
      if (this.className == 'multiline-text') {
        this.isShowTooltip = false
      } else {
        this.isShowTooltip = contentWidth <= parentWidth
      }

      // console.log(parentWidth,'parentWidth')
      // console.log(contentWidth,'contentWidth')
      // console.log(this.isShowTooltip,'this.isShowTooltip')
    },
  },
  mounted() {},
}
</script>

<style lang="less" scoped>
.spanColor {
  width: 400px;
}
.url {
  color: #409eff;
  text-decoration: underline;
} /*// .tempContent*//*//   .prompt {
*/
//     color: #F56C6C;
//     display: inline-block;
//     font-weight: 800;
//     /* background: rgba(0, 0, 0, .5); */
//     /* text-shadow: 3px 3px 5px #FF0000; */
//   }

//   .prompt_error {
//     color: #E6A23C;
//     display: inline-block;
//     text-decoration: underline;
//     font-weight: 800;
//     /* background: rgba(0, 0, 0, .5); */
//     /* text-shadow: 3px 3px 5px #FF0000; */
//   }
//}
</style>
