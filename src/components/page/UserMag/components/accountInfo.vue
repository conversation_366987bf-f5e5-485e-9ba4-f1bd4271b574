<template>
  <div>
    <div class="sensitive-fun" style="margin: 10px 0">
      <span class="Signature-list-header">基本信息</span>
    </div>
    <!-- 基本信息 -->
    <table-tem
      class="basicInfo"
      :tableDataObj="basicInfoTable"
      @handelOptionButton="handelOptionButton"
    ></table-tem>
    <div style="margin-top: 30px">
      <div v-if="jsonData.roleId == 22" class="sensitive-fun">
        <span class="Signature-list-header">认证信息</span>
      </div>
      <div v-if="infoList" style="display: flex">
        <div>
          <div class="detailsList">
            <span class="detailsList-title">用户名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              infoList.createName
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">公司名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              infoList.compName
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">姓名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              infoList.name
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">手机号:</span>
            <span class="detailsList-content" style="width: 300px">{{
              infoList.phone
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">认证状态:</span>
            <span v-if="infoList.status == '0'">待认证</span>
            <span v-else-if="infoList.status == '1'" style="color: #f56c6c"
              >认证中</span
            >
            <span v-else-if="infoList.status == '2'" style="color: #f56c6c"
              >认证不通过</span
            >
            <span v-else-if="infoList.status == '3'" style="color: #16a589"
              >已认证</span
            >
            <span v-else-if="infoList.status == '4'" style="color: #f56c6c"
              >合同签署中</span
            >
            <a
              v-if="infoList.viewUrl"
              :href="infoList.viewUrl"
              style="margin-left: 10px"
              target="_blank"
              rel="noopener noreferrer"
              >查看合同</a
            >
            <!-- <span class="detailsList-content" v-if="infoList.status ==2" style="width: 300px;color:#f56c6c">已认证</span> -->
            <!-- <span class="detailsList-content" style="width: 300px;">{{infoList.auditReason}}</span> -->
          </div>
        </div>
        <div style="margin: 20px">
          <div class="detailsList">
            <span class="detailsList-title">证件照:</span>
            <el-image
              v-if="infoList.authorization"
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + infoList.authorization"
              :preview-src-list="srcList"
            >
            </el-image>
            <el-image
              v-if="infoList.businessLicense"
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + infoList.businessLicense"
              :preview-src-list="srcList1"
            >
            </el-image>
          </div>
        </div>
        <div style="margin: 20px">
          <div class="detailsList">
            <span class="detailsList-title">审核人:</span>
            <span class="detailsList-content">{{ infoList.auditName }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">审核时间:</span>
            <span class="detailsList-content" v-if="infoList.auditTime">{{
              moment(infoList.auditTime).format("YYYY-MM-DD HH:mm:ss")
            }}</span>
            <span class="detailsList-content" v-else></span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">审核备注:</span>
            <span class="detailsList-content">{{ infoList.auditReason }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title" style="color: red">原因:</span>
            <span style="color: red" class="detailsList-content">{{
              infoList.checkReason
            }}</span>
          </div>
        </div>
        <!-- <div style="margin-left:20px">
             <div class="detailsList">
                <span class="detailsList-title">复审审核人:</span>
                <span class="detailsList-content">{{infoList.checkName}}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-title">复审审核时间:</span>
                <span class="detailsList-content" v-if="infoList.checkTime">{{moment(infoList.checkTime).format('YYYY-MM-DD HH:mm:ss')}}</span>
                <span class="detailsList-content" v-else></span>
              </div>
              <div class="detailsList">
                <span class="detailsList-title">复审审核备注:</span>
                <span class="detailsList-content">{{infoList.checkReason}}</span>
              </div>
              </div> -->
      </div>
      <div v-if="jsonData.roleId == 22 && !infoList">
        <span>认证状态：</span>
        <span style="color: red; font-size: 14px">未认证</span>
      </div>
    </div>
    <!-- <div v-if="!infoList.status">未认证</div> -->

    <div
      class="sensitive-fun"
      style="position: relative; padding-top: 40px; margin: 20px 0"
    >
      <span class="Signature-list-header" style="margin: 20px 0"
        >近期充值信息</span
      >
    </div>
    <!-- 表格组件 -->
    <el-table
      v-loading="recentTableDataObj.loading2"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.6)"
      ref="multipleTable"
      border
      :data="recentTableDataObj.tableData"
      style="width: 100%"
    >
      <el-table-column label="充值条数">
        <template v-slot="scope">
          <span>{{ scope.row.rechargeNum + "(" + scope.row.unit + ")" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="充值类型">
        <template v-slot="scope">
          <span>{{ scope.row.productName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单号">
        <template v-slot="scope">
          <span>{{ scope.row.orderNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时间">
        <template v-slot="scope">
          <span>{{ scope.row.rechargeTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <table-tem :tableDataObj="recentTableDataObj"></table-tem> -->
  </div>
</template>

<script>
// var axios = require('axios')
import TableTem from "@/components/publicComponents/TableTem.vue";
// 引入时间戳转换
// import {formatDate} from '@/assets/js/date.js'
export default {
  name: "accountInfo",
  components: {
    TableTem,
  },
  data() {
    return {
      isFirstEnter: false,
      corporateName: "", //公司名称
      accountBalance: 2,
      jsonData: "", //基本信息=》获取行业类别
      // reportStatusNum:"1",
      channel: [], //通道列表
      replyGrabbing: [], //回复抓取
      receiptGrabbing: [], // 回执抓取
      receiptPush: [], //回执推送
      replyPush: [], //回复推送
      hasSelectBox: "", //复选框
      clientId: "", //userID
      roleId: "", //角色ID
      parentId: "", //传给子用户列表的id
      infoList: {},
      srcList: [],
      srcList1: [],
      basicInfoTable: {
        id: "basicInfoTable",
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: "consumerName",
            showName: "用户名称",
            showColorTag: {
              color: "#16A589",
            },
          },
          {
            prop: "phone",
            showName: "手机号",
          },
          {
            prop: "compName",
            showName: "公司名称",
          },
          {
            prop: "consumerStatus",
            showName: "用户状态",
            formatData: () => {
              return this.jsonData.consumerStatus == 1 ? "停用" : "启用";
            },
          },
          {
            prop: "roleId",
            showName: "用户类型",
            formatData: function (val) {
              let type = "";
              if (val == 12) {
                return (type = "管理商");
              } else if (val == 14) {
                return (type = "终端用户");
              } else if (val == 22) {
                return (type = "线上终端");
              } else {
                return (type = "子用户");
              }
              return type;
            },
          },
          { prop: "industryName", showName: "行业", },
          { prop: "createName", showName: "创建人", },
          {
            prop: "createTime",
            showName: "创建时间",
            width: "170",
          },
          { prop: "email", showName: "邮箱", },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "126", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [],
      },
      //近期重置信息查询条件
      totalSize: 0,
      searchInput: {
        //搜索条件改变
        time: "",
        beginTime: "",
        endTime: "",
        pageSize: 5,
        currentPage: 1,
      },
      //近期充值信息-列表
      recentTableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        tableLabel: [
          {
            prop: "rechargeNum",
            showName: "充值的条数",
          },
          {
            prop: "productId",
            showName: "产品名称",
            formatData: function (val) {
              let type = "";
              if (val == 1) {
                type = "短信";
              } else if (val == 2) {
                type = "彩信";
              } else if (val == 3) {
                type = "视频短信";
              } else if (val == 4) {
                type = "国际短信";
              } else if (val == 5) {
                type = "闪验";
              }
              return type;
            },
          },
          {
            prop: "orderNumber",
            showName: "订单号",
          },
          {
            prop: "rechargeTime",
            showName: "时间",
            width: "150",
            formatData: (val) => {
              return window.common.formatDate(val);
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "240", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      clinetroleIds: "",
    };
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      let formDatas = {};
      window.api.get(
        window.path.omcs + "operatinguser/" + this.clientId,
        formDatas,
        (res) => {
          this.jsonData = res.data;
          this.parentId = res.data.clientId;
          //基本信息表格数据
          this.basicInfoTable.tableData = [res.data];
          this.corporateName = res.data.compName;
          if (res.data.roleId == 12) {
            this.basicInfoTable.tableOptions = [];
            window.api.get(
              window.path.omcs +
                "operatinguser/consumerClientCount?clientId=" +
                res.data.userId,
              {},
              (ress) => {
                let option = {
                  optionName: "子用户( " + ress.data + " )",
                  type: "",
                  size: "mini",
                  optionMethod: "getSubPage",
                  icon: "el-icon-success",
                };
                this.basicInfoTable.tableOptions.push(option);
              }
            );
          } else {
            this.basicInfoTable.tableOptions = [];
          }
          // window.api.get(
          //   window.path.omcs + "operatinguser/userInfo/" + this.$route.query.id,
          //   {},
          //   (res) => {
          //     //   this.clinetroleIds = res.data.roleId;

          //     // this.channel = res.data;
          //   }
          // );

          this.getRecentData();
        }
      );
    },
    /**---------数据表格（近期充值信息）------ */
    getRecentData() {
      let formData = {};
      formData = this.searchInput;
      formData.userId = this.clientId;
      window.api.get(
        window.path.recharge + "manager/recharge/page",
        formData,
        (res) => {
          this.recentTableDataObj.tableData = res.data.records;
          this.totalSize = res.data.total;
          this.recentTableDataObj.loading2 = false;
        }
      );
    },
    handelOptionButton(val) {
      if (val.methods == "getSubPage") {
        this.$router.push({
          path: "ManagerSub",
          query: { Id: this.parentId, ids: this.clientId },
        });
      }
    },
    handeTime: function (val) {
      //获取查询时间框的值
      if (val) {
        this.searchInput.beginTime = this.moment(val[0]).format("YYYY-MM-DD ");
        this.searchInput.endTime = this.moment(val[1]).format("YYYY-MM-DD ");
      } else {
        this.searchInput.beginTime = "";
        this.searchInput.endTime = "";
      }
      this.searchInput.pageSize = 10;
      this.searchInput.currentPage = 1;
      this.getRecentData();
    },
    handleSizeChange(size) {
      this.searchInput.pageSize = size;
      this.getRecentData();
    },
    handleCurrentChange: function (currentPage) {
      this.searchInput.currentPage = currentPage;
      this.getRecentData();
    },
    getinfo() {
      window.api.get(
        window.path.omcs +
          "operatinguser/certificate/info?userId=" +
          this.clientId,
        {},
        (res) => {
          // console.log(res.data);
          this.infoList = res.data.certificateInfo;
          // console.log(this.infoList,'llll');
          if (res.data.certificateInfo) {
            this.srcList.push(
              window.path.imgU + res.data.certificateInfo.authorization
            );
            this.srcList1.push(
              window.path.imgU + res.data.certificateInfo.businessLicense
            );
          }else{
            this.srcList = [];
            this.srcList1 = [];
          }

          // console.log(window.path.imgU);
          // this.recentTableDataObj.tableData = res.data.records;
          // this.totalSize=res.data.total
          // this.recentTableDataObj.loading2 = false;
        }
      );
    },
    //    getInfo(){
    //       window.api.get(
    //         window.path.omcs + "operatinguser/userInfo/"+this.$route.query.id,
    //         {},
    //         (res) => {
    //           this.clinetroleIds = res.data.roleId
    //           // this.channel = res.data;
    //         }
    //       );
    //     }
  },
  created() {
    console.log(111);
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.clientId = this.$route.query.id;
      //   this.roleId = this.clinetroleIds;
      this.getBasicData();
      this.getinfo();
    });
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.clientId = this.$route.query.id;
        // this.roleId = this.clinetroleIds;
        this.getBasicData();
        this.getinfo();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  // computed: {
  //     clientId (val) {
  //     console.log(val,'llll');
  //     }
  // },

  watch: {
    // '$route.query.id': {
    //   handler(newVal, oldVal) {
    //     //判断newVal有没有值监听路由变化
    //     if (newVal) {
    //       this.clientId = newVal;
    //       this.getBasicData();
    //       this.getinfo();
    //     }
    //   },
    //   deep: true,
    //   immediate: true, // 初次监听即执行
    // }
    // clientId(val,oldVal){
    //     console.log(val,'xin');
    //     console.log(oldVal,'old');
    // }
  },
};
</script>

<style scoped>
.dateStyle {
  position: absolute;
  right: 0;
  bottom: 10px;
}
.mr-3 {
  margin-right: 3px;
}
.detailsList {
  line-height: 42px;
  font-size: 12px;
  position: relative;
}
.bs {
  position: absolute;
  top: 0px;
  left: 100px;
  color: red;
  font-size: 14px;
  z-index: 9999;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  border-bottom: 1px solid #eee;
}
.sensitive-fun {
  color: #555;
}
</style>

<style>
.account-info-w {
  width: 300px !important;
}

.productInfoTable1 td {
  padding: 2px 0 !important;
}

.el-input-number__decrease,
.el-input-number__increase {
  top: 2px !important;
}
</style>
