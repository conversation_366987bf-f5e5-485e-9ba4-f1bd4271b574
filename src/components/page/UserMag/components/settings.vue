<template>
  <div>
    <p style="margin-top: 30px; font-weight: bold">通知与告警</p>
    <div class="sensitive-fun" style="padding-top: 15px">
      <el-button
        @click="addAlarmContact"
        type="primary"
        style="margin-right: 5px"
        >添加告警联系人</el-button
      >
      <span style="font-weight: normal">最多可添加5个指定联系人</span>
    </div>
    <!-- 表格组件 -->
    <table-tem
      :tableDataObj="tableDataObj"
      @handelOptionButton="handelOptionButton"
    ></table-tem>
    <!-- 编辑紧急联系人弹窗 -->
    <el-dialog
      :title="addAlertTitle"
      v-model="addAlertContactDialog"
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="addAlertContactForm.formData"
        :inline="true"
        :rules="addAlertContactForm.formRule"
        ref="addAlertContactForm"
      >
        <el-form-item label="姓名" prop="linkmanName">
          <el-input
            class="input-w"
            v-model="addAlertContactForm.formData.linkmanName"
          ></el-input>
        </el-form-item>
        <el-form-item
          label-width="155px"
          label="接收人工审核通知"
          prop="auditRemind"
        >
          <el-radio v-model="addAlertContactForm.formData.auditRemind" label="2"
            >是</el-radio
          >
          <el-radio v-model="addAlertContactForm.formData.auditRemind" label="1"
            >否</el-radio
          >
        </el-form-item>
        <el-form-item label="手机" prop="linkmanPhone">
          <el-input
            class="input-w"
            v-model="addAlertContactForm.formData.linkmanPhone"
          ></el-input>
        </el-form-item>

        <!-- <el-form-item label="余额变更&不足通知" prop="balanceRemind" v-if="this.$route.query.ids == '14' && this.isOpenBalace == true">
                    <el-radio v-model="addAlertContactForm.formData.balanceRemind" label="2">是</el-radio>
                    <el-radio v-model="addAlertContactForm.formData.balanceRemind" label="1">否</el-radio>
                </el-form-item> -->
        <el-form-item
          label-width="155px"
          label="余额变更&不足通知"
          prop="balanceRemind"
        >
          <el-radio
            v-model="addAlertContactForm.formData.balanceRemind"
            label="2"
            >是</el-radio
          >
          <el-radio
            v-model="addAlertContactForm.formData.balanceRemind"
            label="1"
            >否</el-radio
          >
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button
          @click="cancelForm('addAlertContactForm')"
          style="width: 100px; padding: 9px 0"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="submitForm('addAlertContactForm')"
          style="width: 100px; padding: 9px 0"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <!-- 余额提醒通知     -->
    <!-- <div style="height: 250px;position: relative;">
                <div class="fillet bas-block" style="height:181px;">
                    <div class="basic-cfg-title">短信余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ SMScreateTime | fmtDate }} - 更新时间：{{
                            SMSupdateTime
                            | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder"
                        v-if="rebalanceReminder == false">启用</el-button>
                    <div v-if="rebalanceReminder == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders.SingleFrequencysub }} </span> 条时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div class="fillet bas-block" style="height:181px;left: 550px;">
                    <div class="basic-cfg-title">彩信余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ MMScreateTime | fmtDate }} - 更新时间：{{
                            MMSupdateTime
                            | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder1"
                        v-if="rebalanceReminder1 == false">启用</el-button>
                    <div v-if="rebalanceReminder1 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder1">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders1.SingleFrequencysub }} </span> 条时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance1">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla1()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder1">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders1.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace1">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace1">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div class="fillet bas-block" style="height:181px;left:1090px">
                    <div class="basic-cfg-title">视频短信余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ RMScreateTime | fmtDate }} - 更新时间：{{
                            RMSupdateTime
                            | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder3"
                        v-if="rebalanceReminder3 == false">启用</el-button>
                    <div v-if="rebalanceReminder3 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder3">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders3.SingleFrequencysub }} </span> 条时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance3">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla3()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder3">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders3.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace3">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace3">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
            </div>
            <div style="height: 250px;position: relative;">
                <div class="fillet bas-block" style="height:181px;">
                    <div class="basic-cfg-title">语音验证码余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ vCodecreateTime | fmtDate }} -
                            更新时间：{{ vCodeupdateTime | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder6"
                        v-if="rebalanceReminder6 == false">启用</el-button>
                    <div v-if="rebalanceReminder6 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder6">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders6.SingleFrequencysub }} </span> 条时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance6">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla6()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder6">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders6.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace6">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace6">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div class="fillet bas-block" style="height:181px;left: 550px;">
                    <div class="basic-cfg-title">语音余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ vNoticecreateTime | fmtDate }} -
                            更新时间：{{ vNoticeupdateTime | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder7"
                        v-if="rebalanceReminder7 == false">启用</el-button>
                    <div v-if="rebalanceReminder7 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder7">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders7.SingleFrequencysub }} </span> 条时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance7">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla7()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder7">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders7.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace7">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace7">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div class="fillet bas-block" style="height:181px;left: 1090px;">
                    <div class="basic-cfg-title">国际短信余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ IMScreateTime | fmtDate }} - 更新时间：{{
                            IMSupdateTime
                            | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder4"
                        v-if="rebalanceReminder4 == false">启用</el-button>
                    <div v-if="rebalanceReminder4 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder4">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders4.SingleFrequencysub }} </span> 元时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance4">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla4()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder4">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders4.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 元时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace4">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace4">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
            </div>
            <div style="height: 250px;position: relative;">
                <div class="fillet bas-block" style="height:181px;">
                    <div class="basic-cfg-title">闪验余额提醒
                        <div style="font-size: 12px;color: #ff00009e;">(创建时间：{{ FtestcreateTime | fmtDate }} -
                            更新时间：{{ FtestupdateTime | fmtDate }})</div>
                    </div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRebalanceReminder5"
                        v-if="rebalanceReminder5 == false">启用</el-button>
                    <div v-if="rebalanceReminder5 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="balanceReminder5">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <span class="bac-color-red">
                                        {{ balanceReminders5.SingleFrequencysub }} </span> 条时提醒</div>
                                <p style="color:red;font-size:12px;">注：请去添加告警联系人，以便正常接收余额变更&不足！</p>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="setSendBalance5">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="handleChangeBla5()">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!balanceReminder5">
                                <div style="padding-bottom:15px;">当前账号余额条数在不足 <el-input style="width:120px;"
                                        v-model="balanceReminders5.successRate"
                                        oninput="value=value.replace(/[^\d]/g,'')"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="sureSendbalace5">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="cancelSendbalace5">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>

            </div> -->
    <p style="margin: 20px 0; font-weight: bold">余额提醒</p>
    <el-table
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.6)"
      ref="multipleTable"
      border
      :data="this.tableData"
      style="width: 100%"
    >
      <el-table-column label="产品名称">
        <!-- 点击用户名 -->
        <template v-slot="scope">
          <div v-for="item in productIdList" :key="item.id">
            <span v-if="scope.row.productId == item.id">{{ item.name }}</span>
            <!-- <div>
                                {{ scope.row.productName }}
                            </div> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column label="不足条数提醒">
        <template v-slot="scope">
          {{ scope.row.num }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="200">
        <template v-slot="scope">
          <el-tag
:disable-transitions="true" v-if="scope.row.open == 0" type="danger" effect="dark">
            关闭
          </el-tag>
          <el-tag
:disable-transitions="true" v-if="scope.row.open == 1" type="success" effect="dark">
            开启
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间">
        <template v-slot="scope">
          <span v-if="scope.row.createTime">{{
            moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间">
        <template v-slot="scope">
          <span v-if="scope.row.updateTime">{{
            moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template v-slot="scope">
          <el-button
            v-if="scope.row.open == 1"
            link
            style="color: #409eff"
            @click="handelOpen('edit', scope.row)"
            >设置</el-button
          >
          <el-button
            v-if="scope.row.open == 0"
            link
            style="color: #67c23a"
            @click="handelOpen('start', scope.row)"
            >开启</el-button
          >
          <el-button
            v-if="scope.row.open == 1"
            link
            style="color: #f56c6c"
            @click="handleChangeBla(scope.row)"
            >关闭</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="余额提醒"
      v-model="dialogVisible"
      width="500px"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名称" prop="username">
          <el-input
            class="input-w"
            disabled
            v-model="ruleForm.username"
          ></el-input>
        </el-form-item>
        <el-form-item label="产品名称" prop="productId">
          <el-select
            class="input-w"
            disabled
            v-model="ruleForm.productId"
            placeholder="请选择"
          >
            <el-option
              v-for="item in productIdList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="条数" prop="num">
          <el-input-number
            class="input-w"
            v-model="ruleForm.num"
            :min="0"
            :max="10000000"
            label="描述文字"
          ></el-input-number>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmOpen('ruleForm')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import moment from 'moment'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  name: 'settings',
  components: {
    TableTem,
  },
  data() {
    // var checkPhone = (rule, value, callback) => {
    //   let reg =
    //     /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
    //   if (!value) {
    //     // return callback(new Error('请输入手机号'));
    //     callback()
    //   } else {
    //     var a = false
    //     for (var i = 0; i < this.tableDataObj.tableData.length; i++) {
    //       if (
    //         this.addAlertContactForm.formData.linkmanPhone ==
    //           this.tableDataObj.tableData[i].linkmanPhone &&
    //         this.editPhone != value
    //       ) {
    //         a = true
    //         return callback(new Error('手机号已存在'))
    //       }
    //     }
    //     if (!a) {
    //       if (reg.test(value)) {
    //         callback()
    //       }
    //       return callback(new Error('请输入正确的手机号'))
    //     }
    //   }
    // }
    return {
      isFirstEnter: false,
      clientId: '',
      username: '',
      dataList: [
        // {
        //     productName: "短信",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // },
        // {
        //     productName: "彩信",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // },
        // {
        //     productName: "视频短信",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // },
        // {
        //     productName: "语音验证码",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // },
        // {
        //     productName: "语音通知",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // },
        // {
        //     productName: "国际短信",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // },
        // {
        //     productName: "闪验",
        //     num: 0,
        //     open: 0,
        //     createTime: "",
        //     updateTime: ""
        // }
      ],
      productIdList: [
        {
          id: '1',
          name: '短信',
        },
        {
          id: '2',
          name: '彩信',
        },
        {
          id: '3',
          name: '视频短信',
        },
        {
          id: '4',
          name: '国际短信',
        },
        {
          id: '5',
          name: '闪验',
        },
        {
          id: '6',
          name: '语音验证码',
        },
        {
          id: '7',
          name: '语音通知',
        },
      ],
      tableData: [],
      dialogVisible: false,
      ruleForm: {
        username: '',
        productId: '',
        num: '',
      },
      rules: {
        num: [{ required: true, message: '条数不能为空', trigger: 'change' }],
      },
      // balanceReminder: true,
      // balanceReminder1: true,
      // balanceReminder3: true,
      // balanceReminder4: true,
      // balanceReminder5: true,
      // balanceReminder6: true,
      // balanceReminder7: true,
      // rebalanceReminder: false,
      // rebalanceReminder1: false,
      // rebalanceReminder3: false,
      // rebalanceReminder4: false,
      // rebalanceReminder5: false,
      // rebalanceReminder6: false,
      // rebalanceReminder7: false,
      // sendfrequency: true,
      // requencyEnable: false,
      // //用户预警
      // SendReminder: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // //余额预警通知
      // balanceReminders: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // balanceReminders1: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // balanceReminders3: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // balanceReminders4: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // balanceReminders5: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // balanceReminders6: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // balanceReminders7: {
      //     SingleFrequencysub: '',
      //     successRate: ''
      // },
      // editPhone: '',
      // clientId: '',
      // SMScreateTime: '',
      // SMSupdateTime: '',
      // MMScreateTime: '',
      // RMSupdateTime: '',
      // RMScreateTime: '',
      // MMSupdateTime: '',
      // IMSupdateTime: '',
      // IMScreateTime: '',
      // FtestupdateTime: '',
      // FtestcreateTime: '',
      // vCodecreateTime: '',
      // vCodeupdateTime: '',
      // vNoticecreateTime: '',
      // vNoticeupdateTime: '',
      // isOpenBalace: false,//是否开启余额预警
      // isOpenBalace1: false,//是否开启余额预警
      // isOpenBalace3: false,//是否开启余额预警
      // isOpenBalace4: false,//是否开启余额预警
      // isOpenBalace5: false,//是否开启余额预警
      // isOpenBalace6: false,//是否开启余额预警
      // isOpenBalace7: false,//是否开启余额预警
      addAlertStatus: 1,//添加联系人的弹窗 --状态1为新增，0为编辑
      // 添加告警联系人弹窗
      addAlertContactDialog: false,
      addAlertContactForm: {
        formData: {
          userId: '',
          conLinkmanId: '',
          linkmanName: '',
          linkmanPhone: '',
          auditRemind: '1',
          warningRemind: '1',
          balanceRemind: '2',
        },
        formRule: {
          linkmanName: [
            { required: true, message: '请输入姓名', trigger: 'blur' },
            // {pattern:/^[\u4e00-\u9fa5]{2,4}$/gi,message:'姓名为2-4个中文字符!'}
          ],
          linkmanEmail: [
            { required: false, message: '请输入邮箱地址', trigger: 'blur' },
            {
              type: 'email',
              message: '请输入正确的邮箱地址',
              trigger: ['blur', 'change'],
            },
          ],
          auditRemind: [
            {
              required: true,
              message: '请选择是否人工审核通知',
              trigger: 'change',
            },
          ],
          warningRemind: [
            {
              required: true,
              message: '请选择是否接收预警通知',
              trigger: 'change',
            },
          ],
          balanceRemind: [
            {
              required: true,
              message: '请选择是否接收余额预警通知',
              trigger: 'change',
            },
          ],
          // linkmanPhone: [
          //   { required: true, validator: checkPhone, trigger: 'change' },
          // ],
        },
      },
      //表格数据列表
      tableDataObj: {
        id:'settings',
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'linkmanName',
            showName: '姓名',
            width: '140',
          },
          {
            prop: 'linkmanPhone',
            showName: '手机',
          },
          {
            prop: 'auditRemind',
            showName: '审核通知',
            formatData: function (val) {
              return val == '2' ? 'Y' : 'N'
            },
          },
          {
            prop: 'balanceRemind',
            showName: '余额变更&不足通知',
            formatData: function (val) {
              return val == '2' ? 'Y' : 'N'
            },
          },
          { prop: 'createTime', showName: '创建时间', },
          { prop: 'updateTime', showName: '更新时间', },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'editContact',
            icon: 'el-icon-edit', //按钮图标
            color: '#16A589', //按钮颜色
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'delContact',
            icon: 'el-icon-delete', //按钮图标
            color: '#f56c6c', //按钮颜色
          },
        ],
      },
    }
  },
  methods: {
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      window.api.get(
        window.path.omcs + 'operatinguser/getuserwarn',
        { userId: this.clientId },
        (res) => {
          this.tableDataObj.tableData = res.data
          this.tableDataObj.loading2 = false
        }
      )
    },
    //表格数据转换
    formatData: function (row, column) {
      if (column.property == 'status') {
        return row[column.property] == 1 ? '停止' : '启用'
      } else if (column.property == 'userStatus') {
        return row[column.property] == 1 ? '停止' : '启用'
      } else if (column.property == 'date') {
        return formatDate(
          new Date(row[column.property] * 1000),
          'YYYY-MM-DD HH:mm:ss'
        )
      }
    },
    handelOptionButton(val) {
      if (val.methods == 'editContact') {
        console.log(111);
        
        // this.editPhone = val.row.linkmanPhone
        this.addAlertStatus = 0
        this.addAlertContactDialog = true
        this.addAlertContactForm.formData.conLinkmanId = val.row.conLinkmanId
        for (let k in this.addAlertContactForm.formData) {
          console.log(k, 'k')
          if (k == 'auditRemind' || k == 'balanceRemind') {
            this.addAlertContactForm.formData[k] = val.row[k] + ''
          } else {
            this.addAlertContactForm.formData[k] = val.row[k]
          }
          if(k == 'linkmanPhone'){
            this.addAlertContactForm.formData.linkmanPhone = ''
          }
        }
      } else if (val.methods == 'delContact') {
        let userId = val.row.conLinkmanId
        const mount = this.tableDataObj.tableData.length
        if (mount >= 2) {
          this.$confirms.confirmation(
            'delete',
            '确定删除告警联系人？',
            window.path.omcs + 'operatinguser/deleteuserwarn',
            { conLinkmanId: userId },
            (res) => {
              this.getTableData()
            }
          )
        } else {
          this.$message({
            message: '至少保留一条信息',
            type: 'warning',
          })
        }
      }
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (val == 'addAlertContactForm') {
          if (valid) {
            this.addAlertContactForm.formData.userId = this.clientId
            if (this.addAlertStatus == 1) {
              //新增
              // if (
              //   this.addAlertContactForm.formData.linkmanPhone == '' &&
              //   this.addAlertContactForm.formData.linkmanEmail == ''
              // ) {
              //   this.$message({
              //     message: '手机号或邮箱必须填一个',
              //     type: 'warning',
              //   })
              // } else {
               
              // }
              this.$confirms.confirmation(
                  'post',
                  '确定新增告警联系人',
                  window.path.omcs + 'operatinguser/adduserwarn',
                  this.addAlertContactForm.formData,
                  (res) => {
                    this.getTableData()
                    this.addAlertContactDialog = false //关闭弹窗
                  }
                )
            } else if (this.addAlertStatus == 0) {
              //编辑conLinkmanId
              // if (
              //   this.addAlertContactForm.formData.linkmanPhone == '' &&
              //   this.addAlertContactForm.formData.linkmanEmail == ''
              // ) {
              //   this.$message({
              //     message: '手机号或邮箱必须填一个',
              //     type: 'warning',
              //   })
              // } else {
                
              // }
              this.$confirms.confirmation(
                  'put',
                  '确定编辑告警联系人？',
                  window.path.omcs + 'operatinguser/updateuserwarn',
                  this.addAlertContactForm.formData,
                  (res) => {
                    this.getTableData()
                    this.addAlertContactDialog = false //关闭弹窗
                  }
                )
            }
          } else {
            return false
          }
        }
      })
    },
    addAlarmContact() {
      if (this.tableDataObj.tableData.length <= 4) {
        this.addAlertContactDialog = true
        this.addAlertStatus = 1
      } else {
        this.$message({
          type: 'warning',
          message: '最多添加5个告警联系人',
        })
      }
    },
    cancelForm(val) {
      //取消
      if (val == 'addAlertContactForm') {
        this.addAlertContactDialog = false
      }
    },
    /* --------------- 用户预警通知 ------------------*/
    //用户预警通知设置
    // setSendfrequency() {
    //     this.SendReminder.successRate = this.SendReminder.SingleFrequencysub;
    //     this.sendfrequency = !this.sendfrequency
    // },
    // //余额提醒设置
    // setSendBalance() {
    //     this.balanceReminders.successRate = this.balanceReminders.SingleFrequencysub;
    //     this.balanceReminder = !this.balanceReminder
    // },
    // setSendBalance1() {
    //     this.balanceReminders1.successRate = this.balanceReminders1.SingleFrequencysub;
    //     this.balanceReminder1 = !this.balanceReminder1
    // },
    // setSendBalance3() {
    //     this.balanceReminders3.successRate = this.balanceReminders3.SingleFrequencysub;
    //     this.balanceReminder3 = !this.balanceReminder3
    // },
    // setSendBalance4() {
    //     this.balanceReminders4.successRate = this.balanceReminders4.SingleFrequencysub;
    //     this.balanceReminder4 = !this.balanceReminder4
    // },
    // setSendBalance5() {
    //     this.balanceReminders5.successRate = this.balanceReminders5.SingleFrequencysub;
    //     this.balanceReminder5 = !this.balanceReminder5
    // },
    // setSendBalance6() {
    //     this.balanceReminders6.successRate = this.balanceReminders6.SingleFrequencysub;
    //     this.balanceReminder6 = !this.balanceReminder6
    // },
    // setSendBalance7() {
    //     this.balanceReminders7.successRate = this.balanceReminders7.SingleFrequencysub;
    //     this.balanceReminder7 = !this.balanceReminder7
    // },
    // //用户预警通知取消
    // cancelSendfrequency() {
    //     if (this.SendReminder.SingleFrequencysub == '' || this.SendReminder.SingleFrequencysub == 'undefined' || this.SendReminder.SingleFrequencysub == null) {
    //         this.requencyEnable = false;
    //     } else {
    //         this.sendfrequency = !this.sendfrequency
    //         this.SendReminder.successRate = this.SendReminder.SingleFrequencysub;
    //     }
    // },
    // //余额提醒通知取消
    // cancelSendbalace() {
    //     if (this.balanceReminders.SingleFrequencysub == '' || this.balanceReminders.SingleFrequencysub == 'undefined' || this.balanceReminders.SingleFrequencysub == null) {
    //         this.rebalanceReminder = false;
    //     } else {
    //         this.balanceReminder = !this.balanceReminder
    //         this.balanceReminders.successRate = this.balanceReminders.SingleFrequencysub;
    //     }
    // },
    // cancelSendbalace1() {
    //     if (this.balanceReminders1.SingleFrequencysub == '' || this.balanceReminders1.SingleFrequencysub == 'undefined' || this.balanceReminders1.SingleFrequencysub == null) {
    //         this.rebalanceReminder1 = false;
    //     } else {
    //         this.balanceReminder1 = !this.balanceReminder1
    //         this.balanceReminders1.successRate = this.balanceReminders1.SingleFrequencysub;
    //     }
    // },
    // cancelSendbalace3() {
    //     if (this.balanceReminders3.SingleFrequencysub == '' || this.balanceReminders3.SingleFrequencysub == 'undefined' || this.balanceReminders3.SingleFrequencysub == null) {
    //         this.rebalanceReminder3 = false;
    //     } else {
    //         this.balanceReminder3 = !this.balanceReminder3
    //         this.balanceReminders3.successRate = this.balanceReminders3.SingleFrequencysub;
    //     }
    // },
    // cancelSendbalace4() {
    //     if (this.balanceReminders4.SingleFrequencysub == '' || this.balanceReminders4.SingleFrequencysub == 'undefined' || this.balanceReminders4.SingleFrequencysub == null) {
    //         this.rebalanceReminder4 = false;
    //     } else {
    //         this.balanceReminder4 = !this.balanceReminder4
    //         this.balanceReminders4.successRate = this.balanceReminders4.SingleFrequencysub;
    //     }
    // },
    // cancelSendbalace5() {
    //     if (this.balanceReminders5.SingleFrequencysub == '' || this.balanceReminders5.SingleFrequencysub == 'undefined' || this.balanceReminders5.SingleFrequencysub == null) {
    //         this.rebalanceReminder5 = false;
    //     } else {
    //         this.balanceReminder5 = !this.balanceReminder5
    //         this.balanceReminders5.successRate = this.balanceReminders5.SingleFrequencysub;
    //     }
    // },
    // cancelSendbalace6() {
    //     if (this.balanceReminders6.SingleFrequencysub == '' || this.balanceReminders6.SingleFrequencysub == 'undefined' || this.balanceReminders6.SingleFrequencysub == null) {
    //         this.rebalanceReminder6 = false;
    //     } else {
    //         this.balanceReminder6 = !this.balanceReminder6
    //         this.balanceReminders6.successRate = this.balanceReminders6.SingleFrequencysub;
    //     }
    // },
    // cancelSendbalace7() {
    //     if (this.balanceReminders7.SingleFrequencysub == '' || this.balanceReminders7.SingleFrequencysub == 'undefined' || this.balanceReminders7.SingleFrequencysub == null) {
    //         this.rebalanceReminder7 = false;
    //     } else {
    //         this.balanceReminder7 = !this.balanceReminder7
    //         this.balanceReminders7.successRate = this.balanceReminders7.SingleFrequencysub;
    //     }
    // },
    //用户预警通知启用
    // handleRequencyEnable() {
    //     this.requencyEnable = true;
    //     this.sendfrequency = false;
    //     this.SendReminder.SingleFrequencysub = '';
    //     this.SendReminder.successRate = '';
    // },
    // //余额提醒通知启用
    // handleRebalanceReminder() {
    //     this.rebalanceReminder = true;
    //     this.balanceReminder = false;
    // },
    // //余额提醒通知启用
    // handleRebalanceReminder1() {
    //     this.rebalanceReminder1 = true;
    //     this.balanceReminder1 = false;
    // },
    // handleRebalanceReminder3() {
    //     this.rebalanceReminder3 = true;
    //     this.balanceReminder3 = false;
    // },
    // handleRebalanceReminder4() {
    //     this.rebalanceReminder4 = true;
    //     this.balanceReminder4 = false;
    // },
    // handleRebalanceReminder5() {
    //     this.rebalanceReminder5 = true;
    //     this.balanceReminder5 = false;
    // },
    // handleRebalanceReminder6() {
    //     this.rebalanceReminder6 = true;
    //     this.balanceReminder6 = false;
    // },
    // handleRebalanceReminder7() {
    //     this.rebalanceReminder7 = true;
    //     this.balanceReminder7 = false;
    // },
    //获取用户短信预警
    getsmsClient() {
      //获取余额预警
      window.api.get(
        window.path.omcs + 'operatinguser/' + this.$route.query.id,
        {},
        (res) => {
          let jsonData = res.data
          this.username = jsonData.consumerName
          try {
            let that = this
            // let productIdList = ['1', '2', '3', '4', '5', '6', '7']
            // that.url = that.shortLink
            // that.allist = []
            for (let i = 0; i < that.productIdList.length; i++) {
              let p = new Promise(function (resolve, reject) {
                that.$api.get(
                  that.API.recharge + 'manager/balance/notice/info',
                  {
                    productId: that.productIdList[i].id,
                    username: that.username,
                  },
                  (res) => {
                    if (res.code == 200) {
                      let obj = {
                        // productName: "短信",
                        productId: res.data.productId,
                        num: res.data.num,
                        open: res.data.open,
                        createTime: res.data.createTime,
                        updateTime: res.data.updateTime,
                      }
                      resolve(obj)
                    } else {
                      that.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  },
                  (err) => {
                    let obj = {
                      productId: '',
                      num: '',
                      open: '',
                      createTime: '',
                      updateTime: '',
                    }
                    resolve(obj)
                  }
                )
              })
              that.dataList.push(p)
            }
          } catch (error) {
            // this.flag = true
            this.$message({
              type: 'error',
              message: error,
            })
          }
          // window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '1', username: this.username }, res => {
          //     if (res.code == 200) {
          //         let list = {
          //             productName: "短信",
          //             productId: res.data.productId,
          //             num: res.data.num,
          //             open: res.data.open,
          //             createTime: res.data.createTime,
          //             updateTime: res.data.updateTime
          //         }
          //         this.dataList[0] = list

          //     }
          //     // console.log(this.dataList, 'this.dataList[0]')
          //     // this.SMScreateTime = res.data.createTime
          //     // this.SMSupdateTime = res.data.updateTime
          //     // if (res.data.open == 0) {
          //     //     this.rebalanceReminder = false;
          //     //     this.isOpenBalace = false;
          //     // } else {
          //     //     this.isOpenBalace = true;
          //     //     this.rebalanceReminder = true;
          //     //     this.balanceReminders.SingleFrequencysub = res.data.num
          //     // }
          // })
        }
      )
    },
    handelOpen(type, row) {
      console.log(row, 'row')
      this.ruleForm.username = this.username
      this.ruleForm.productId = row.productId + ''
      this.dialogVisible = true
      if (type == 'edit') {
        this.ruleForm.num = row.num
      } else {
        this.ruleForm.num = ''
      }
    },
    handleClose() {
      this.dialogVisible = false
    },
    confirmOpen(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.recharge + 'manager/balance/notice/open',
            this.ruleForm,
            (res) => {
              if (res.code == 200) {
                this.getsmsClient()
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.dialogVisible = false
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //获取用户彩信预警
    // getsmsClient1() {
    //     //获取余额预警
    //     window.api.get(window.path.omcs + 'operatinguser/' + this.$route.query.id, {}, res => {
    //         let jsonData = res.data;
    //         this.username = jsonData.consumerName
    //         window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '2', username: this.username }, res => {
    //             // this.MMScreateTime = res.data.createTime
    //             // this.MMSupdateTime = res.data.updateTime
    //             // if (res.data.open == 0) {
    //             //     this.rebalanceReminder1 = false;
    //             //     this.isOpenBalace1 = false;
    //             // } else {
    //             //     this.isOpenBalace1 = true;
    //             //     this.rebalanceReminder1 = true;
    //             //     this.balanceReminders1.SingleFrequencysub = res.data.num
    //             // }
    //             if (res.code == 200) {
    //                 let list = {
    //                     productName: "彩信",
    //                     productId: res.data.productId,
    //                     num: res.data.num,
    //                     open: res.data.open,
    //                     createTime: res.data.createTime,
    //                     updateTime: res.data.updateTime
    //                 }
    //                 this.dataList[1] = list
    //                 // this.$nextTick(()=>{

    //                 // })

    //             }
    //         })
    //     })
    // },
    // //获取视频短信预警
    // getrmsClient() {
    //     //获取余额预警
    //     window.api.get(window.path.omcs + 'operatinguser/' + this.$route.query.id, {}, res => {
    //         let jsonData = res.data;
    //         this.username = jsonData.consumerName
    //         window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '3', username: this.username }, res => {
    //             // this.RMScreateTime = res.data.createTime
    //             // this.RMSupdateTime = res.data.updateTime
    //             // if (res.data.open == 0) {
    //             //     this.rebalanceReminder3 = false;
    //             //     this.isOpenBalace3 = false;
    //             // } else {
    //             //     this.isOpenBalace3 = true;
    //             //     this.rebalanceReminder3 = true;
    //             //     this.balanceReminders3.SingleFrequencysub = res.data.num
    //             // }
    //             if (res.code == 200) {
    //                 let list = {
    //                     productName: "视频短信",
    //                     productId: res.data.productId,
    //                     num: res.data.num,
    //                     open: res.data.open,
    //                     createTime: res.data.createTime,
    //                     updateTime: res.data.updateTime
    //                 }
    //                 this.dataList[2] = list
    //                 // this.$nextTick(()=>{

    //                 // })

    //             }
    //         })
    //     })
    // },
    // //获取语音验证码预警
    // getvoiceCodeClient() {
    //     //获取余额预警
    //     window.api.get(window.path.omcs + 'operatinguser/' + this.$route.query.id, {}, res => {
    //         let jsonData = res.data;
    //         this.username = jsonData.consumerName
    //         window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '6', username: this.username }, res => {
    //             // this.vCodecreateTime = res.data.createTime
    //             // this.vCodeupdateTime = res.data.updateTime
    //             // if (res.data.open == 0) {
    //             //     this.rebalanceReminder6 = false;
    //             //     this.isOpenBalace6 = false;
    //             // } else {
    //             //     this.isOpenBalace6 = true;
    //             //     this.rebalanceReminder6 = true;
    //             //     this.balanceReminders6.SingleFrequencysub = res.data.num
    //             // }
    //             if (res.code == 200) {
    //                 let list = {
    //                     productName: "语音验证码",
    //                     productId: res.data.productId,
    //                     num: res.data.num,
    //                     open: res.data.open,
    //                     createTime: res.data.createTime,
    //                     updateTime: res.data.updateTime
    //                 }
    //                 this.dataList[3] = list
    //                 // this.$nextTick(()=>{

    //                 // })

    //             }
    //         })
    //     })
    // },
    // //获取语音通知预警
    // getvoiceNoticeClient() {
    //     //获取余额预警
    //     window.api.get(window.path.omcs + 'operatinguser/' + this.$route.query.id, {}, res => {
    //         let jsonData = res.data;
    //         this.username = jsonData.consumerName
    //         window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '7', username: this.username }, res => {
    //             // this.vNoticecreateTime = res.data.createTime
    //             // this.vNoticeupdateTime = res.data.updateTime
    //             // if (res.data.open == 0) {
    //             //     this.rebalanceReminder7 = false;
    //             //     this.isOpenBalace7 = false;
    //             // } else {
    //             //     this.isOpenBalace7 = true;
    //             //     this.rebalanceReminder7 = true;
    //             //     this.balanceReminders7.SingleFrequencysub = res.data.num
    //             // }
    //             if (res.code == 200) {
    //                 let list = {
    //                     productName: "语音通知",
    //                     productId: res.data.productId,
    //                     num: res.data.num,
    //                     open: res.data.open,
    //                     createTime: res.data.createTime,
    //                     updateTime: res.data.updateTime
    //                 }
    //                 this.dataList[4] = list
    //                 // this.$nextTick(()=>{

    //                 // })

    //             }
    //         })
    //     })
    // },
    // //获取国际短信预警
    // getimsClient() {
    //     //获取余额预警
    //     window.api.get(window.path.omcs + 'operatinguser/' + this.$route.query.id, {}, res => {
    //         let jsonData = res.data;
    //         this.username = jsonData.consumerName
    //         window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '4', username: this.username }, res => {
    //             // this.IMScreateTime = res.data.createTime
    //             // this.IMSupdateTime = res.data.updateTime
    //             // if (res.data.open == 0) {
    //             //     this.rebalanceReminder4 = false;
    //             //     this.isOpenBalace4 = false;
    //             // } else {
    //             //     this.isOpenBalace4 = true;
    //             //     this.rebalanceReminder4 = true;
    //             //     this.balanceReminders4.SingleFrequencysub = res.data.num
    //             // }
    //             if (res.code == 200) {
    //                 let list = {
    //                     productName: "国际短信",
    //                     productId: res.data.productId,
    //                     num: res.data.num,
    //                     open: res.data.open,
    //                     createTime: res.data.createTime,
    //                     updateTime: res.data.updateTime
    //                 }
    //                 this.dataList[5] = list
    //                 // this.$nextTick(()=>{

    //                 // })

    //             }
    //         })
    //     })
    // },
    // //获取闪验预警
    // getFtestClient() {
    //     //获取余额预警
    //     window.api.get(window.path.omcs + 'operatinguser/' + this.$route.query.id, {}, res => {
    //         let jsonData = res.data;
    //         this.username = jsonData.consumerName
    //         window.api.get(window.path.recharge + 'manager/balance/notice/info', { productId: '5', username: this.username }, res => {
    //             // this.FtestcreateTime = res.data.createTime
    //             // this.FtestupdateTime = res.data.updateTime
    //             // if (res.data.open == 0) {
    //             //     this.rebalanceReminder5 = false;
    //             //     this.isOpenBalace5 = false;
    //             // } else {
    //             //     this.isOpenBalace5 = true;
    //             //     this.rebalanceReminder5 = true;
    //             //     this.balanceReminders5.SingleFrequencysub = res.data.num
    //             // }
    //             if (res.code == 200) {
    //                 let list = {
    //                     productName: "闪验",
    //                     productId: res.data.productId,
    //                     num: res.data.num,
    //                     open: res.data.open,
    //                     createTime: res.data.createTime,
    //                     updateTime: res.data.updateTime
    //                 }
    //                 this.dataList[6] = list
    //                 // this.$nextTick(()=>{

    //                 // })

    //             }
    //         })
    //     })
    // },

    // //用户预警确定
    // sureSendfrequency() {
    //     if (this.SendReminder.successRate != '') {
    //         let reg = /^[1-9][0-9]{0,1}$/;
    //         if (reg.test(this.SendReminder.successRate)) {
    //             window.api.put(window.path.cpus + 'manager/balance/notice/open', {
    //                 userId: this.clientId,
    //                 warningRemind: 2,
    //                 successRate: this.SendReminder.successRate
    //             }, res => {
    //                 this.sendfrequency = !this.sendfrequency
    //                 // this.SendReminder.SingleFrequencysub = this.SendReminder.successRate;
    //                 this.getsmsClient();
    //                 this.getsmsClient1();
    //             })
    //         } else {
    //             this.$message({
    //                 message: '账号成功率设置范围在1-99之间！',
    //                 type: 'warning'
    //             });
    //         }

    //     } else {
    //         this.$message({
    //             message: '账号成功率不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    //短信余额预警确定
    // sureSendbalace() {
    //             if (this.balanceReminders.successRate != '') {
    //                 let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //                 if (reg.test(this.balanceReminders.successRate)) {
    //                     window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                         productId: 1,
    //                         username: this.username,
    //                         num: this.balanceReminders.successRate
    //                     }, res => {
    //                         // this.balanceReminder = !this.balanceReminder
    //                         // this.balanceReminders.SingleFrequencysub = this.balanceReminders.successRate;
    //                         this.getsmsClient();
    //                     })
    //                 } else {
    //                     this.$message({
    //                         message: '余额提醒条数设置范围在0-1000万之间！',
    //                         type: 'warning'
    //                     });
    //                 }
    //             } else {
    //                 this.$message({
    //                     message: '余额提醒条数不能为空！',
    //                     type: 'warning'
    //                 });
    //             }
    //         },
    // //彩信余额预警确定
    // sureSendbalace1() {
    //     if (this.balanceReminders1.successRate != '') {
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if (reg.test(this.balanceReminders1.successRate)) {
    //             window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                 productId: 2,
    //                 username: this.username,
    //                 num: this.balanceReminders1.successRate
    //             }, res => {
    //                 this.balanceReminder1 = !this.balanceReminder1
    //                 this.balanceReminders1.SingleFrequencysub = this.balanceReminders1.successRate;
    //                 this.getsmsClient1();
    //             })
    //         } else {
    //             this.$message({
    //                 message: '余额提醒条数设置范围在0-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }
    //     } else {
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    // sureSendbalace3() {
    //     if (this.balanceReminders3.successRate != '') {
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if (reg.test(this.balanceReminders3.successRate)) {
    //             window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                 productId: 3,
    //                 username: this.username,
    //                 num: this.balanceReminders3.successRate
    //             }, res => {
    //                 this.balanceReminder3 = !this.balanceReminder3
    //                 this.balanceReminders3.SingleFrequencysub = this.balanceReminders3.successRate;
    //                 this.getrmsClient();
    //             })
    //         } else {
    //             this.$message({
    //                 message: '余额提醒条数设置范围在0-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }
    //     } else {
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    // sureSendbalace4() {
    //     if (this.balanceReminders4.successRate != '') {
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if (reg.test(this.balanceReminders4.successRate)) {
    //             window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                 productId: 4,
    //                 username: this.username,
    //                 num: this.balanceReminders4.successRate
    //             }, res => {
    //                 this.balanceReminder4 = !this.balanceReminder4
    //                 this.balanceReminders4.SingleFrequencysub = this.balanceReminders4.successRate;
    //                 this.getimsClient();
    //             })
    //         } else {
    //             this.$message({
    //                 message: '余额提醒条数设置范围在0-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }
    //     } else {
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    // sureSendbalace5() {
    //     if (this.balanceReminders5.successRate != '') {
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if (reg.test(this.balanceReminders5.successRate)) {
    //             window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                 productId: 5,
    //                 username: this.username,
    //                 num: this.balanceReminders5.successRate
    //             }, res => {
    //                 this.balanceReminder5 = !this.balanceReminder5
    //                 this.balanceReminders5.SingleFrequencysub = this.balanceReminders5.successRate;
    //                 this.getFtestClient();
    //             })
    //         } else {
    //             this.$message({
    //                 message: '余额提醒条数设置范围在0-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }
    //     } else {
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    // sureSendbalace6() {
    //     if (this.balanceReminders6.successRate != '') {
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if (reg.test(this.balanceReminders6.successRate)) {
    //             window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                 productId: 6,
    //                 username: this.username,
    //                 num: this.balanceReminders6.successRate
    //             }, res => {
    //                 this.balanceReminder6 = !this.balanceReminder6
    //                 this.balanceReminders6.SingleFrequencysub = this.balanceReminders6.successRate;
    //                 this.getvoiceCodeClient()
    //             })
    //         } else {
    //             this.$message({
    //                 message: '余额提醒条数设置范围在0-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }
    //     } else {
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    // sureSendbalace7() {
    //     if (this.balanceReminders7.successRate != '') {
    //         let reg = /^([1-9][0-9]{0,6}|10000000)$/;
    //         if (reg.test(this.balanceReminders7.successRate)) {
    //             window.api.post(window.path.recharge + 'manager/balance/notice/open', {
    //                 productId: 7,
    //                 username: this.username,
    //                 num: this.balanceReminders7.successRate
    //             }, res => {
    //                 this.balanceReminder7 = !this.balanceReminder7
    //                 this.balanceReminders7.SingleFrequencysub = this.balanceReminders7.successRate;
    //                 this.getvoiceNoticeClient()
    //             })
    //         } else {
    //             this.$message({
    //                 message: '余额提醒条数设置范围在0-1000万之间！',
    //                 type: 'warning'
    //             });
    //         }
    //     } else {
    //         this.$message({
    //             message: '余额提醒条数不能为空！',
    //             type: 'warning'
    //         });
    //     }
    // },
    //关闭余额预警
    handleChangeBla(row) {
      this.$confirms.confirmation(
        'post',
        '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？',
        window.path.recharge + 'manager/balance/notice/close',
        {
          productId: row.productId,
          username: this.username,
        },
        (res) => {
          this.getsmsClient()
        }
      )
    },
    // handleChangeBla1() {
    //     this.$confirms.confirmation('post', '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？', window.path.recharge + 'manager/balance/notice/close', {
    //         productId: 2,
    //         username: this.username
    //     }, res => {
    //         this.rebalanceReminder1 = false;
    //         this.balanceReminders1.SingleFrequencysub = '';
    //         this.balanceReminders1.successRate = '';
    //         this.getsmsClient1();
    //         this.isOpenBalace1 = false;
    //     });
    // },
    // handleChangeBla3() {
    //     this.$confirms.confirmation('post', '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？', window.path.recharge + 'manager/balance/notice/close', {
    //         productId: 3,
    //         username: this.username
    //     }, res => {
    //         this.rebalanceReminder3 = false;
    //         this.balanceReminders3.SingleFrequencysub = '';
    //         this.balanceReminders3.successRate = '';
    //         this.getrmsClient();
    //         this.isOpenBalace3 = false;
    //     });
    // },
    // handleChangeBla4() {
    //     this.$confirms.confirmation('post', '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？', window.path.recharge + 'manager/balance/notice/close', {
    //         productId: 4,
    //         username: this.username
    //     }, res => {
    //         this.rebalanceReminder4 = false;
    //         this.balanceReminders4.SingleFrequencysub = '';
    //         this.balanceReminders4.successRate = '';
    //         this.getimsClient();
    //         this.isOpenBalace4 = false;
    //     });
    // },
    // handleChangeBla5() {
    //     this.$confirms.confirmation('post', '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？', window.path.recharge + 'manager/balance/notice/close', {
    //         productId: 5,
    //         username: this.username
    //     }, res => {
    //         this.rebalanceReminder5 = false;
    //         this.balanceReminders5.SingleFrequencysub = '';
    //         this.balanceReminders5.successRate = '';
    //         this.getFtestClient();
    //         this.isOpenBalace5 = false;
    //     });
    // },
    // handleChangeBla6() {
    //     this.$confirms.confirmation('post', '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？', window.path.recharge + 'manager/balance/notice/close', {
    //         productId: 6,
    //         username: this.username
    //     }, res => {
    //         this.rebalanceReminder6 = false;
    //         this.balanceReminders6.SingleFrequencysub = '';
    //         this.balanceReminders6.successRate = '';
    //         this.getvoiceCodeClient()
    //         this.isOpenBalace6 = false;
    //     });
    // },
    // handleChangeBla7() {
    //     this.$confirms.confirmation('post', '关闭余额提醒通知后，您将收不到此类提醒，您确认关闭吗？', window.path.recharge + 'manager/balance/notice/close', {
    //         productId: 7,
    //         username: this.username
    //     }, res => {
    //         this.rebalanceReminder7 = false;
    //         this.balanceReminders7.SingleFrequencysub = '';
    //         this.balanceReminders7.successRate = '';
    //         this.getvoiceNoticeClient()
    //         this.isOpenBalace7 = false;
    //     });
    // },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.clientId = this.$route.query.id
      this.getTableData()
      this.getsmsClient()
      // this.getsmsClient1();
      // this.getrmsClient()
      // this.getimsClient()
      // this.getFtestClient()
      // this.getvoiceCodeClient()
      // this.getvoiceNoticeClient()
      // if (this.SendReminder.SingleFrequencysub || this.SendReminder.SameleFrequencysub) {
      //     this.requencyEnable = true;
      // }
    })
  },
  activated() {
      if (this.$route.meta.isBack || !this.isFirstEnter) {
          this.$nextTick(() => {
              console.log(111)
              this.clientId = this.$route.query.id;
              this.getTableData();
              this.getsmsClient();
              // this.getsmsClient1();
              // this.getrmsClient()
              // this.getimsClient()
              // this.getFtestClient()
              // this.getvoiceCodeClient()
              // this.getvoiceNoticeClient()
              // if (this.SendReminder.SingleFrequencysub || this.SendReminder.SameleFrequencysub) {
              //     this.requencyEnable = true;
              // }
          });
      } else {
          this.$route.meta.isBack = false
          this.isFirstEnter = false;
      }

  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
    addAlertTitle() {
      return this.addAlertStatus == 1 ? '添加告警联系人' : '编辑告警联系人'
    },
  },
  watch: {
    // '$route.query.id': {
    //     handler(newVal, oldVal) {
    //         console.log(newVal);
    //         //判断newVal有没有值监听路由变化
    //         if (newVal) {
    //             this.clientId = this.$route.query.id;
    //             this.getTableData();
    //             this.getsmsClient();
    //         }

    //     },
    //     deep: true,
    //     immediate: true, // 初次监听即执行
    // },
    addAlertContactDialog(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.addAlertContactForm.resetFields() //表单验证清空
        this.isOpenBalace = false
        for (let key in this.addAlertContactForm.formData) {
          this.addAlertContactForm.formData[key] = ''
          this.addAlertContactForm.formData.auditRemind = '1'
          this.addAlertContactForm.formData.warningRemind = '1'
          this.addAlertContactForm.formData.balanceRemind = '2'
        }
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$refs['ruleForm'].resetFields()
        this.ruleForm.num = 0
      }
    },
    dataList: {
      handler(newValue, oldValue) {
        Promise.all(newValue)
          .then((res) => {
            this.tableData = Array.from(
              new Map(res.map((item) => [item.productId, item])).values()
            )
          })
          .catch((err) => {
            console.log(err)
          })
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.bas-block {
  margin-bottom: 10px;
  padding-top: 36px;
  position: absolute;
  display: inline-block;
  width: 550px;
  margin-right: 20px;
}
.basic-cfg-title {
  font-weight: bold;
  padding-bottom: 14px;
}
.basic-cfg-tit {
  width: 92px;
  display: inline-block;
}
.basic-cfg-cot {
  margin-bottom: 24px;
}
.basic-cfg-tips {
  padding-left: 96px;
  padding-top: 2px;
  font-size: 12px;
}
.basic-help {
  margin: 30px 0 20px 0;
  padding-bottom: 10px;
  width: 500px;
  border-bottom: 1px solid #e6e6e6;
}
.tips-box {
  position: absolute;
  background: #fff;
  padding: 20px;
  border: 1px solid #e6e6e6;
}
.fade-enter-active {
  animation: show-in 1s;
  transition: all 1s;
}
.fade-leave-active {
  animation: show-out 1s;
  transition: all 1s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
@keyframes show-in {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(360deg);
  }
}
@keyframes show-out {
  0% {
    transform: rotateX(360deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}
.bac-color-red {
  color: red;
}
</style>
