<template>
  <div class="container_left">
    <div style="padding: 10px">
      <div>
        <!-- <el-form inline ref="form" :model="form" label-width="80px">
                <el-form-item label="回执成功状态" prop="msgId">
                <el-checkbox-group v-model="form.checkList">
                    <el-checkbox v-for="(item,index) in tableData.data" :key = "index" :label="item.code"></el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="search">查询</el-button>
                <el-button @click="Reset('form')">重置</el-button>
              </el-form-item> 
            </el-form> -->
        <el-button
          type="primary"
          @click="handleAdopt1('0')"
          v-if="ids.length != 0"
          >批量成功</el-button
        >
        <el-button
          type="warning"
          @click="handleAdopt1('1')"
          v-if="ids.length != 0"
          >批量失败</el-button
        >
      </div>
      <div>
        <!-- <el-table
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          :data="tableData.data"
          @selection-change="handleSelectionChange"
          border
          stripe
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="5gSetting"
          border
          stripe
          show-overflow
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableData.data"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="55"></vxe-column>
          <vxe-column field="code" title="code">
            <template #default="scope">
              <div>{{ scope.row.code }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回执状态" title="回执状态">
            <template v-slot="scope">
              <div>
                <span style="color: #409eff" v-if="scope.row.value == '1'"
                  >成功</span
                >
                <span style="color: red" v-else>失败</span>
              </div>
            </template>
          </vxe-column>

          <vxe-column field="" title="操作">
            <template v-slot="scope">
              <el-button
                style="color: red"
                v-if="scope.row.value == '1'"
                @click="handleClick(scope.row, '0')"
                link
                size="default"
                >修改失败状态</el-button
              >
              <el-button
                style="color: #409eff"
                v-else
                @click="handleClick(scope.row, '1')"
                link
                size="default"
                >修改成功状态</el-button
              >
              <!-- <el-button type="text" size="default">编辑</el-button> -->
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="回执时间">
                <template #default="scope">
                  <span>{{
                    moment(scope.row.reportTime).format("YYYY-MM-DD HH:mm:ss")
                  }}</span>
                </template>
              </vxe-column> -->
        </vxe-table>
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableData.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      dialogVisible: false,
      value1: '',
      message: '群发记录',
      type: '',
      detail: {},
      form: {
        mobile: '',
        msgId: '',
        reportCode: '',
        pageSize: 10,
        currentPage: 1,
        checkList: [],
      },
      ids: [],
      tabelAlllist: {
        mobile: '',
        msgId: '',
        reportCode: '',
        pageSize: 10,
        currentPage: 1,
      },
      tableData: {
        loading2: false,
        data: [],
        total: 0,
      },
      title: '',
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  computed: {
    ...mapState({
      userId: (state) => state.userId,
    }),
  },
  methods: {
    getList() {
      this.tableData.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/dictionary/page',
        this.tabelAlllist,
        (res) => {
          this.tableData.data = res.data.records
          this.tableData.total = res.data.total
          this.tableData.loading2 = false
        }
      )
    },
    search() {
      Object.assign(this.tabelAlllist, this.form)
      this.getList()
    },
    Reset(formName) {
      this.$refs[formName].resetFields()
      Object.assign(this.tabelAlllist, this.form)
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getList()
    },
    handleCurrentChange: function (currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getList()
    },
    handleSelectionChange(row) {
      // console.log(row, 'row')
      // var idarr = []
      // idarr = row.map((item) => {
      //   return item.id
      // })
      // this.ids = idarr.join(',')
      // console.log(this.ids, 'ids')

      if (row.records.length > 0) {
        let arr = []
        row.records.forEach(item => {
          arr.push(item.id)
        })
        this.ids = arr.join(',')
      } else {
        this.ids = ""
      }
    },
    handleAdopt1(val) {
      this.$confirms.confirmation(
        'put',
        '确认修改回执成功状态',
        window.path.fiveWeb + '/operator/dictionary/batchUpdate',
        {
          id: this.ids,
          value: val,
        },
        (res) => {
          if (res.code == 200) {
            this.getList()
          }
        }
      )
    },
    handleClick(row, val) {
      this.$confirms.confirmation(
        'put',
        '确认修改回执成功状态',
        window.path.fiveWeb + '/operator/dictionary',
        {
          id: row.id,
          value: val,
        },
        (res) => {
          if (res.code == 200) {
            this.getList()
          }
        }
      )
    },
  },
}
</script>

<style scoped>
.send_foot {
  display: flex;
  justify-content: space-between;
}
</style>

<style>
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
