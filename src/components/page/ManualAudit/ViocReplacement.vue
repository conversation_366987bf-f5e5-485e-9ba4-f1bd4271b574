<template>
  <div class="container_left">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="待补发" name="first">
        <div class="passageWay-title" style="margin-top: 30px">
          <!-- 查询框开始 -->
          <el-form
            :inline="true"
            :model="form"
            label-width="82px"
            class="demo-form-inline"
            ref="sendjl"
          >
            <el-form-item label="用户名称" prop="clientName">
              <el-input v-model="form.clientName" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="通道ID" prop="channelId">
              <el-input
                v-model="form.channelId"
                class="input-w"
                onInput="value=value.replace(/[^\d]/g,'')"
                maxlength="10"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="form.mobile" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="内容" prop="content">
              <el-input v-model="form.content" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="msgId" prop="msgid">
              <el-input v-model="form.msgid" class="input-w"></el-input>
            </el-form-item>
            <!-- <el-form-item label="videoId" prop="videoId">
                  <el-input v-model="form.videoId" class="input-w"></el-input>
                </el-form-item> -->
            <!-- <el-form-item label="发送状态"  prop='smsStatus'>
                            <el-select v-model="form.smsStatus" class="input-w">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="成功" value="1"></el-option>
                                <el-option label="失败" value="2"></el-option>
                                <el-option label="待返回" value="3"></el-option>
                            </el-select>
                        </el-form-item> -->
            <el-form-item label="失败代码" prop="originalCode">
              <el-input v-model="form.originalCode" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" label-width="80px" prop="time">
              <el-date-picker
                :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate"
                :cell-class-name="pickerOptions && pickerOptions.cellClassName"
                v-model="form.time"
                type="datetimerange"
                @change="timeD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                align="right"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button
                type="primary"
                plain
                @click.prevent="Query()"
                @keyup.enter="Query()"
                >查询</el-button
              >
              <el-button type="primary" plain @click="reset('sendjl')"
                >重置</el-button
              >
            </div>
          </el-form>

          <!-- 查询框结束 -->
          <div class="passage-table" style="margin-top: 10px">
            <!-- 表格和分页开始 -->
            <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
            <!-- <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(0, 0, 0, 0.6)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              @selection-change="handelSelection"
              style="width: 100%"
            > -->

            <vxe-toolbar ref="toolbarRef" custom>
              <template #buttons>
                <el-button type="primary" @click="ConditionS('条件补发')"
                  >条件补发</el-button
                >
              </template>
            </vxe-toolbar>

            <vxe-table
              ref="tableRef"
              id="ViocReplacement"
              border
              stripe
              :custom-config="customConfig"
              :column-config="{ resizable: true }"
              :row-config="{ isHover: true }"
              min-height="1"
              v-loading="tableDataObj.loading2"
              element-loading-text="拼命加载中"
              element-loading-background="rgba(0, 0, 0, 0.6)"
              style="font-size: 12px;"
              :data="tableDataObj.tableData"
              @checkbox-all="handelSelection"
              @checkbox-change="handelSelection">

              <vxe-column type="checkbox" width="50"></vxe-column>
              <vxe-column prop="username" field="用户名称" title="用户名称" width="120px">
                <template v-slot="scope">
                  <div>
                    {{ scope.row.username }}
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="msgId" title="msgId" width="200px">
                <template v-slot="scope">
                  <div>
                    {{ scope.row.msgid }}
                    <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                          @click="handleCopy(scope.row.msgid, $event)"></i> -->
                    <CopyTemp :content="scope.row.msgid" />
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="手机号" title="手机号" width="110px">
                <template v-slot="{row, rowIndex}">
                  <div
                    style="color: #16a589; cursor: pointer"
                    @click="tableContent(row, rowIndex)"
                  >
                    <span>{{ row.mobile }}</span>
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="内容" title="内容" width="200px">
                <template v-slot="scope">
                  <Tooltip
                    v-if="scope.row.content"
                    :content="scope.row.content"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                </template>
              </vxe-column>
              <!-- <vxe-column field="预览" title="预览">
                    <template #default="scope">
                      <el-button type="text" @click="View(scope.row)"
                        ><i class="el-icon-picture"></i>&nbsp;预览</el-button
                      >
                    </template>
                  </vxe-column> -->
              <vxe-column field="计费数" title="计费数" width="80px">
                <template #default="scope">
                  <div>{{ scope.row.chargeNum }}</div>
                </template>
              </vxe-column>
              <vxe-column prop="sendTime" field="发送时间" title="发送时间" width="100px">
                <template v-slot="scope">
                  <p v-if="scope.row.sendTime">
                    {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
                  </p>
                  <p v-if="scope.row.sendTime">
                    {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
                  </p>
                </template>
              </vxe-column>
              <vxe-column prop="reportTime" field="回执时间" title="回执时间" width="100px">
                <template v-slot="scope">
                  <p v-if="scope.row.reportTime">
                    {{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}
                  </p>
                  <p v-if="scope.row.reportTime">
                    {{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}
                  </p>
                </template>
              </vxe-column>
              <vxe-column field="发送状态" title="发送状态" width="90px">
                <template v-slot="scope">
                  <el-tag
                    :disable-transitions="true"
                    v-if="scope.row.status == 1"
                    type="success"
                    effect="dark"
                  >
                    成功
                  </el-tag>
                  <el-tag
                    :disable-transitions="true"
                    v-else-if="scope.row.status == 2"
                    type="danger"
                    effect="dark"
                  >
                    失败
                  </el-tag>
                  <el-tag
                    :disable-transitions="true"
                    v-else-if="scope.row.status == 3"
                    type="info"
                    effect="dark"
                  >
                    待返回
                  </el-tag>
                  <!-- <span v-if="scope.row.status == 1">成功</span>
                      <span v-else-if="scope.row.status == 2">失败</span>
                      <span v-else-if="scope.row.status == 3">待返回</span> -->
                </template>
              </vxe-column>
              <vxe-column field="下载状态" title="下载状态" width="90px">
                <template v-slot="scope">
                  <div>
                    <span v-if="scope.row.downloadStatus == '1000'"
                      >下载成功</span
                    >
                    <span v-else-if="scope.row.downloadStatus == '1001'"
                      >下载失败</span
                    >
                    <span v-else>未知</span>
                  </div>
                </template>
              </vxe-column>
              <vxe-column field="备注" title="备注" width="90px">
                <template v-slot="scope">
                  <Tooltip
                    v-if="scope.row.originalCode"
                    :content="scope.row.originalCode"
                    className="wrapper-text"
                    effect="light"
                  ></Tooltip>
                </template>
              </vxe-column>
              <vxe-column field="扩展号" title="扩展号" width="90px">
                <template v-slot="scope">
                  <Tooltip
                    v-if="scope.row.ext"
                    :content="scope.row.ext"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                </template>
              </vxe-column>
              <vxe-column field="发送通道号" title="发送通道号" width="85px">
                <template v-slot="scope">
                  <div
                    @click="save(scope.row.channelId)"
                    style="color: rgb(22, 165, 137); cursor: pointer"
                    >{{ scope.row.channelId }}</div
                  >
                </template>
              </vxe-column>
              <!-- <vxe-column prop="videoId" field="" title="videoId" width="70px">
                  </vxe-column> -->
              <vxe-column field="提交Ip" title="提交Ip" width="110px">
                <template #default="scope">
                  <div>{{ scope.row.ip }}</div>
                </template>
              </vxe-column>
              <vxe-column field="来源" title="来源" width="140px">
                <template #default="scope">
                  <div>{{ scope.row.source }}</div>
                </template>
              </vxe-column>
              <vxe-column field="操作" title="操作" width="120" prop="">
                <template v-slot="scope">
                  <!-- <el-button type="text" @click="Dreissue(scope.row)"
                        >&nbsp;补发</el-button
                      > -->
                </template>
              </vxe-column>
            </vxe-table>
            <!--分页-->
            <!-- <template v-slot:pagination>
              <el-col
                :xs="24"
                :sm="24"
                :md="24"
                :lg="24"
                class="page"
                style="background: #fff; padding: 10px 0; text-align: right"
              > -->

            <div class="paginationBox">
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="form.currentPage"
                :page-size="form.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.totalRow"
              >
              </el-pagination>
            </div>

              <!-- </el-col>
            </template> -->
            <!-- 表格和分页结束 -->
          </div>
        </div>
        <!-- 预览手机弹框   -->
        <el-dialog title="预览" v-model="dialogVisible" width="40%">
          <div>
            <div class="send-mobel-box">
              <img src="../../../assets/images/sendmobel.png" alt="" />
              <div class="mms-content-exhibition">
                <el-scrollbar class="sms-content-exhibition">
                  <div style="width: 253px">
                    <span
                      style="
                        display: inline-block;
                        padding: 5px;
                        border-radius: 5px;
                        background: #e2e2e2;
                        margin-top: 5px;
                      "
                      >{{ title }}</span
                    >
                  </div>
                  <div
                    style="
                      overflow-wrap: break-word;
                      width: 234px;
                      background: #e2e2e2;
                      padding: 10px;
                      border-radius: 5px;
                      margin-top: 5px;
                    "
                    v-for="(item, index) in viewData"
                    :key="index"
                  >
                    <img
                      v-if="
                        item.media == 'jpg' ||
                        item.media == 'gif' ||
                        item.media == 'png' ||
                        item.media == 'jpeg'
                      "
                      :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                      style="width: 235px"
                      class="avatar video-avatar"
                      ref="avatar"
                    />
                    <video
                      v-bind:src="
                        API.imgU + item.mediaGroup + '/' + item.mediaPath
                      "
                      v-if="item.type == 'video'"
                      style="width: 235px"
                      class="avatar video-avatar"
                      controls="controls"
                    ></video>
                    <audio
                      v-bind:src="
                        API.imgU + item.mediaGroup + '/' + item.mediaPath
                      "
                      v-if="item.type == 'audio'"
                      style="width: 235px"
                      autoplay="autoplay"
                      controls="controls"
                      preload="auto"
                    ></audio>
                    <div style="white-space: pre-line">
                      {{ item.txt }}
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </div>
        </el-dialog>
        <!-- 条件补发 -->
        <el-dialog
          title="条件补发"
          v-model="ConditionalReplacement"
          width="50%"
          :close-on-click-modal="false"
          :before-close="handleClose1"
        >
          <!-- <div style="padding: 10px 0;font-weight: 600;">补发用户名:<span style="font-weight: 500;margin-left: 10px;">{{ReissueName}}</span></div> -->
          <!-- <el-table
                v-loading="tableDataObjCondition.loading2"
                border
                :data="tableDataObjCondition.tableData"
                style="width: 100%">
                    <el-table-column label="选择" width="50">
                        <template #default="scope" >
                            <el-radio v-model="radio" @change="radioChange(scope.row.failNum,scope.row.content,scope.row.md5Content)" :label="scope.row.failNum+scope.row.content">&nbsp;</el-radio>
                        </template>
                    </el-table-column> 
                    <el-table-column label="发送内容" >
                        <template #default="scope" >
                            {{ scope.row.content }}
                        </template>
                    </el-table-column>
                    <el-table-column label="失败数量" width="100">
                        <template #default="scope">
                            {{ scope.row.failNum}}
                        </template>
                    </el-table-column>
                </el-table> -->
          <div>
            <span style="padding: 10px 0; font-weight: 600">补发条件</span>
            <span>共:（</span
            ><span style="color: red; font-size: 13px">{{
              tablecurrentTotal
            }}</span
            >）条
          </div>
          <div>
            <div style="width: 49%; display: inline-block; float: left">
              <div>
                <el-input readonly v-model="form.clientName">
                  <template v-slot:prepend>用户名:</template>
                </el-input>
              </div>
              <div>
                <el-input readonly v-model="form.msgid">
                  <template v-slot:prepend>消息ID:</template>
                </el-input>
              </div>
              <div style="word-wrap: break-word">
                <el-input readonly v-model="form.title">
                  <template v-slot:prepend>发送标题:</template>
                </el-input>
              </div>
              <div style="word-wrap: break-word">
                <el-input readonly v-model="form.videoId">
                  <template v-slot:prepend>videoId:</template>
                </el-input>
              </div>
            </div>
            <div style="width: 49%; display: inline-block">
              <div>
                <el-input readonly v-model="form.channelId">
                  <template v-slot:prepend>通道ID:</template>
                </el-input>
              </div>
              <div>
                <el-input readonly v-model="form.originalCode">
                  <template v-slot:prepend>失败代码:</template>
                </el-input>
              </div>
              <div>
                <el-input readonly v-model="form.mobile">
                  <template v-slot:prepend>手机号码:</template>
                </el-input>
              </div>

              <div>
                <el-input readonly v-model="time1String">
                  <template v-slot:prepend>发送时间:</template>
                </el-input>
              </div>
            </div>
          </div>
          <template v-slot:footer>
            <span class="dialog-footer">
              <el-button @click="ConditionalReplacement = false"
                >取 消</el-button
              >
              <el-button type="primary" @click="determine">确 定</el-button>
            </span>
          </template>
        </el-dialog>
        <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
      </el-tab-pane>
      <el-tab-pane label="补发记录" name="second">
        <VIOCrecords v-if="activeName == 'second'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import UserLists from '@/components/publicComponents/userList.vue'
import VIOCrecords from './ReplacementRecord/viocRecord.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
// import clip from '../../../utils/clipboard'
import moment from 'moment'
export default {
  components: {
    DatePlugin,
    TableTem,
    ChannelView,
    UserLists,
    VIOCrecords,
    Tooltip,
    CopyTemp
  },
  name: 'RMSrecord',
  data() {
    return {
      customConfig: {
        storage: true
      },
      activeName: 'first',
      isFirstEnter: false,
      userFlag: false,
      nameover: '',
      // 预览
      viewData: '',
      indexVeew: 1,
      time1String: '',
      title: '',
      dialogVisible: false,
      //
      ChannelData: '', //传递通道值
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      idStr: '',
      // moreInfoDialog:false,
      //  dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: '',
      },
      // Times:{
      //     beginTime:monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      //     endTime:monment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      // },
      // datePluginValue:[monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),monment().endOf('day').format('YYYY-MM-DD HH:mm:ss')],
      // datePluginValueList: { //日期选择器
      //     type:"datetimerange",
      //     start:"",
      //     end:'',
      //     range:'-',
      //     clearable:false,
      // pickerOptions:{
      //     disabledDate(time) {
      //         return time.getTime() > Date.now()
      //     }
      // },
      //    datePluginValue:''
      // },
      //发送查询的值
      //   time: [
      //     moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
      //     moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      //   ],
      pickerOptions: {
        disabledDate(time) {
          const curDate = new Date().getTime()
          const six = 6 * 24 * 3600 * 1000
          const sixDay = curDate - six
          return time.getTime() > Date.now() || time.getTime() < sixDay - 8.64e7
        },
      },
      form: {
        mobile: '',
        channelId: '',
        clientName: '',
        content: '',
        msgid: '',
        // videoId: "",
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        ],
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        originalCode: '',
        currentPage: 1,
        pageSize: 10,
      },
      tablecurrentTotal: '',
      ReissueName: '',
      ConditionalReplacement: false,
      //复制发送查询的值
      formData: {
        mobile: '',
        channelId: '',
        clientName: '',
        content: '',
        msgid: '',
        // videoId: "",
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        ],
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        originalCode: '',
        currentPage: 1,
        pageSize: 10,
      },
      activeName2: 'first', //发送和回复选项卡
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        totalRow: 0,
        tableData: [],
      },
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      btime: '',
      etime: '',
    }
  },
  methods: {
    handleClick(tab, e) {
      if (tab.index == 0) {
        this.getChallDate()
      }
    },
    handelSelection(val) {
      //列表复选框的值
      console.log(val)
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id + '')
      }
      this.selectId = selectId
      // console.log(this.selectId, "ll");
    },
    // 预览
    View(val) {
      this.indexVeew = 1
      this.viewData = val.contents
      this.title = val.title
      this.dialogVisible = true
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 点击手机号
    tableContent(row, index) {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.decryptMobile,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile = res.data;
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      )
      //  window.api.get(window.path.omcs + "smsMessage/decrypt?smsInfoId="+val,{},res=>{
      //     if(res.code==200){
      //         this.tableDataObj.tableData[index].mobile=res.data[0]
      //     }else{
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }
      // })
    },
    //获取 发送记录数据
    getChallDate() {
      //   let aa = {};
      //   Object.assign(this.formData, this.form);
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'voiceResend/messages/page',
        this.form,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.totalRow = res.data.total
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
    },
    // timeClick(val){
    //     if(val){
    //         this.Times.beginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
    //         this.Times.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");

    //     }
    // },
    handleChangeTimeOptions: function () {
      this.datePluginValue = ''
    },
    handleClose1() {
      this.ConditionalReplacement = false
    },
    // handleCopy(name, event) {
    //   clip(name, event)
    // },
    //条件补发
    determine() {
      let Obj = {}
      Obj = Object.assign(Obj, this.form)
      Obj.productId = 7
      if (this.form.sendBeginTime == '' || this.form.sendEndTime == '') {
        Obj.sendBeginTime = this.btime
        Obj.sendEndTime = this.etime
      }

      window.api.post(
        window.path.omcs + 'v3/smsreplacement/batchSend',
        Obj,
        (res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '操作成功!',
            })
            // this.getChallDate();
            // this.form.time = [this.form.sendBeginTime,this.form.sendEndTime ]
            this.ConditionalReplacement = false
          } else {
            this.$message({
              type: 'error',
              duration: 2000,
              message: res.msg,
            })
          }
        }
      )
    },
    //条件补发
    ConditionS(val) {
      if (val == '条件补发') {
        if (this.form.clientName) {
          window.api.post(
            window.path.omcs + 'voiceResend/messages/page',
            this.form,
            (res) => {
              this.tablecurrentTotal = res.data.total
              this.ReissueName = this.form.clientName
              if (
                this.form.sendBeginTime == '' ||
                this.form.sendEndTime == ''
              ) {
                this.btime = moment()
                  .startOf('day')
                  .format('YYYY-MM-DD HH:mm:ss')
                this.etime = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                this.time1String = this.btime + '--' + this.etime
              } else {
                this.time1String =
                  this.form.sendBeginTime + '--' + this.form.sendEndTime
              }
              this.ConditionalReplacement = true
            }
          )
        } else {
          this.$message({
            type: 'error',
            duration: 2000,
            message: '条件补发需先填写用户名',
          })
        }
      }
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    //发送查询
    Query() {
      Object.assign(this.formData, this.form)
      this.getChallDate()
    },
    //发送重置
    reset() {
      this.$refs.sendjl.resetFields()
      this.form.pageSize = 10
      this.form.currentPage = 1
      this.form.title = ''
      this.form.sendBeginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.form.sendEndTime = moment()
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      Object.assign(this.formData, this.form)
      //   console.log();

      this.getChallDate()
    },
    handleSizeChange(size) {
      this.form.pageSize = size
      this.getChallDate()
    },
    handleCurrentChange: function (currentPage) {
      this.form.currentPage = currentPage
      this.getChallDate()
    },
    Reissue() {
      this.$confirms.confirmation(
        'post',
        '确定视频短信补发',
        window.path.omcs + 'videoResend/messages/batchreplacement',
        { ids: this.selectId },
        (res) => {
          this.getChallDate()
        }
      )
    },
    Dreissue(row) {
      console.log(row, 'row')
      let id = []
      id.push(row.id + '')
      // this.selectId.push(row.id+"")
      this.$confirms.confirmation(
        'post',
        '确定视频短信补发',
        window.path.omcs + 'videoResend/messages/batchreplacement',
        { ids: id },
        (res) => {
          if (res.code == 200) {
            this.getChallDate()
            id = []
          }
        }
      )
    },
    timeD(val) {
      if (val) {
        this.form.sendBeginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.form.sendEndTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.form.sendBeginTime = ''
        this.form.sendEndTime = ''
        this.form.time = []
      }

      //   console.log(this.form.sendBeginTime);
      // console.log(val);
    },
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    this.isFirstEnter = true
    this.$nextTick(() => {
      ;(this.form.time = [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
      ]),
        (this.form.sendBeginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'))
      this.form.sendEndTime = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')
      Object.assign(this.formData, this.form)
      this.getChallDate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        ;(this.form.time = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        ]),
          (this.form.sendBeginTime = moment()
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss'))
        this.form.sendEndTime = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')
        Object.assign(this.formData, this.form)
        this.getChallDate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听时间范围
    // flag:function(val){
    //     if(val != '5'){
    //         this.Times.beginTime = '';
    //         this.Times.endTime = '';
    //         this.datePluginValue='';
    //     }
    //     this.getChallDate();
    // },
    //监听时间是否改变
    // Times:{
    //     handler(val){
    //         if(val){
    //             this.getChallDate();
    //         }
    //     },
    //     deep:true
    // },
    // formData:{
    //     handler(val){
    //         this.getChallDate();
    //     },
    //     deep:true
    // },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
