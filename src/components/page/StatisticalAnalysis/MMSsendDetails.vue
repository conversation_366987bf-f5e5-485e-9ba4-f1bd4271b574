<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon>
          用户发送明细报表</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <el-tabs v-model="activeName2" type="card">
        <el-tab-pane label="用户发送明细报表" name="first">
          <component v-bind:is="currentTabComponent"></component>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import MMSrecord from './components/MMSrecord.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    MMSrecord,
    ElIconLxEmoji,
  },
  name: 'MMSsendDetails',
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  data() {
    return {
      currentTabComponent: 'MMSrecord',
      activeName2: 'first',
    }
  },
}
</script>
