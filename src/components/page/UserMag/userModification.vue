<template>
  <div
    class="user-modifi-cation"
    style="min-height: 800px; height: auto; background: #fff; padding: 15px"
  >
    <div class="Top_title" style="display: flex">
      <div
        style="
          display: flex;
          padding-right: 10px;
          cursor: pointer;
          align-items: center;
          color: #16a589;
        "
        @click="goBack()"
      >
        <el-icon><ArrowLeft /></el-icon>返回
      </div>
      |
      <div style="margin-left: 10px">{{ statusOf }}</div>
    </div>
    <!-- <div v-if="flagCreated" style="margin: 15px">
      <el-steps :active="active" finish-status="success">
        <el-step title="步骤 1"></el-step>
        <el-step title="步骤 2"></el-step>
      </el-steps>
    </div> -->
    <div class="fillet">
      <div>
        <div v-if="roleId != 7">
          <el-form
            :model="addUserStep1Form.formData"
            :inline="true"
            :rules="addUserStep1Form.formRule"
            ref="addUserStep1Form"
            label-width="144px"
          >
            <el-form-item label="用户类型" prop="roleId">
              <el-select
                :disabled="addUserDialog_status == 0"
                v-model="addUserStep1Form.formData.roleId"
                class="input-w"
                placeholder="请选择用户类别"
              >
                <el-option label="管理商" value="12"></el-option>
                <el-option
                  v-if="statusOf != '新增用户信息'"
                  label="子用户"
                  value="13"
                ></el-option>
                <el-option label="终端" value="14"></el-option>
                <el-option
                  v-if="statusOf != '新增用户信息'"
                  label="线上终端"
                  value="22"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="addUserDialog_status == 1"
              label="用户名称"
              prop="consumerName"
            >
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.consumerName"
              ></el-input>
            </el-form-item>
            <el-form-item v-else label="用户名称" prop="consumerNames">
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.consumerName"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="公司名称" prop="compName">
              <el-select
                :disabled="
                  addUserDialog_status != 1 &&
                  addUserStep1Form.formData.roleId == '13'
                    ? true
                    : false
                "
                ref="optionRef"
                class="input-w"
                v-model="addUserStep1Form.formData.compName"
                clearable
                filterable
                remote
                :remote-method="remoteMethod"
                :loading="loadingcomp"
                placeholder="请选择公司名称"
                @change="bindChange"
              >
                <el-option
                  v-for="(item, index) in compNamelist"
                  :key="index"
                  :label="item.company"
                  :value="item.company + '+' + item.id"
                >
                </el-option>
              </el-select>
              <!-- <el-input class="input-w" v-model="addUserStep1Form.formData.compName"></el-input> -->
            </el-form-item>
            <el-form-item
              v-if="addUserStep1Form.formData.roleId != '13'"
              label="企业ID"
              prop=""
            >
              <el-input
                class="input-w"
                disabled
                v-model="addUserStep1Form.formData.compId"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="行业类别" prop="industryId">
              <el-select class="input-w" v-model="addUserStep1Form.formData.industryId" placeholder="请选择行业类别">
                <el-option v-for="(item, index) in industrys" :label="item.industryName" :value="item.id"
                  :key="index"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="公司地址" prop="compAddres">
              <el-input class="input-w" v-model="addUserStep1Form.formData.compAddres"></el-input>
            </el-form-item> -->
            <el-form-item
              v-if="addUserDialog_status != 0"
              label="手机号码"
              prop="phone"
              :rules="
                filter_rules({
                  required: true,
                  type: 'fiveMobiles',
                  message: '请输入手机号码',
                })
              "
            >
              <template #label>
                <span>手机号码</span>
                <el-tooltip
                  effect="dark"
                  content="默认输入首个号码为管理员"
                  placement="top"
                >
                  <i
                    style="color: #409eff; margin-left: 5px"
                    class="el-icon-info"
                  ></i>
                </el-tooltip>
              </template>
              <!-- <span slot="label" style="display: inline-block">
                
              </span> -->
              <!-- <el-input placeholder="请输入手机号"
            class="input-w" 
            v-model="addUserStep1Form.formData.phone"
            @keyup.native="handleClick" 
            show-password></el-input> -->
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.phone"
                @keyup.native="handleClick"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="所属销售" prop="salesName">
                        <el-input class="input-w" v-model="addUserStep1Form.formData.salesName"></el-input>
                    </el-form-item> -->

            <el-form-item label="认证状态" prop="certificate">
              <template #label>
                <span style="color: red"> 认证状态 </span>
              </template>
              <el-select
                class="input-w"
                :disabled="addUserDialog_status == 0 && roleId != 19"
                v-model="addUserStep1Form.formData.certificate"
                placeholder="认证状态"
              >
                <el-option label="待认证" value="0"></el-option>
                <el-option
                  v-if="addUserDialog_status == 0"
                  label="认证中"
                  value="1"
                ></el-option>
                <el-option label="已认证" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="账号状态" prop="consumerStatus">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.consumerStatus"
                placeholder="账号状态"
              >
                <el-option label="启用" value="0"></el-option>
                <el-option label="停用" value="1"></el-option>
                <el-option
                  v-if="addUserDialog_status == 0"
                  disabled
                  label="冻结"
                  value="2"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="合作状态" prop="cooperateStatus">
              <template #label>
                <span style="color: red"> 合作状态 </span>
              </template>
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.cooperateStatus"
                placeholder="账号状态"
              >
                <el-option label="测试阶段" value="TEST"></el-option>
                <el-option label="正式合作" value="NORMAL"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="所属客服" prop="service">
              <el-select
                class="input-w"
                clearable
                v-model="addUserStep1Form.formData.service"
                placeholder="请选择客服"
              >
                <el-option
                  v-for="(item, index) in services"
                  :label="item.realName"
                  :value="item.userId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="电子邮箱" prop="email">
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.email"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="密码" prop="password" v-if="addUserDialog_status == 1">
              <el-input style="width: 264px" placeholder="此密码只显示一次" :readonly="true"
                v-model="addUserStep1Form.formData.password"></el-input>
              <el-button type="primary" :disabled="pwdIsDisable" @click="generatePass">生成密码</el-button>
            </el-form-item> -->
            <el-form-item label="号码保护" prop="mobileProtecte">
              <el-select
                class="input-w"
                v-model="mobileProtecte"
                @change="change"
              >
                <el-option label="开启" value="0"></el-option>
                <el-option label="关闭" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="商城功能" prop="shoppingCapability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.shoppingCapability"
              >
                <el-option label="开启" :value="true"></el-option>
                <el-option label="关闭" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="WEB功能" prop="webCapability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.webCapability"
              >
                <el-option label="开启" :value="true"></el-option>
                <el-option label="关闭" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="API功能" prop="apiCapability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.apiCapability"
              >
                <el-option label="开启" :value="true"></el-option>
                <el-option label="关闭" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="短信接口加密方式" prop="cipherMode">
              <div>
                <el-select
                  class="input-w"
                  v-model="addUserStep1Form.formData.cipherMode"
                  placeholder="加密方式"
                >
                  <el-option label="MD5" value="1"></el-option>
                  <el-option label="SM3" value="2"></el-option>
                  <el-option label="AES" value="3"></el-option>
                </el-select>
                <div
                  style="
                    width: 345px;
                    font-size: 12px;
                    color: red;
                    line-height: 20px;
                  "
                >
                  (注意：只支持融合短信接口（JSON）)
                </div>
              </div>
            </el-form-item>
            <el-form-item
              v-if="addUserStep1Form.formData.cipherMode == '2'"
              label="加密盐"
              prop="salt"
            >
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.salt"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="addUserStep1Form.formData.cipherMode == '3'"
              label="密钥"
              prop="cipherKey"
            >
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.cipherKey"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="账户密码" prop="loginPassword" v-if="addUserDialog_status == 0">
              <el-input style="width: 264px" :readonly="addUserDialog_status == 1 ? true : false"
                v-model="addUserStep1Form.formData.loginPassword"></el-input>
              <el-button type="primary" @click="resetPassword">重置密码</el-button>
            </el-form-item> -->
            <!-- <el-form-item label="接口密码" prop="interfacePassword" v-if="addUserDialog_status == 0">
              <el-input style="width: 264px" :readonly="addUserDialog_status == 1 ? true : false"
                v-model="addUserStep1Form.formData.interfacePassword"></el-input>
              <el-button type="primary" @click="resetInterPassword">重置密码</el-button>
            </el-form-item> -->
            <!-- <el-form-item v-if="addUserDialog_status == 1" label="默认签名" prop="signature">
              <el-input class="input-w" v-model="addUserStep1Form.formData.signature" placeholder="请输入默认签名"></el-input>
            </el-form-item> -->
            <!-- :rules="
                filter_rules({ required: false, type: 'muchIPs', message: '' })
              " -->
            <el-form-item label="接口IP地址" prop="ip">
              <!-- <span slot="label" style="display: inline-block">
                <span>接口IP地址</span>
                <el-tooltip
                  effect="dark"
                  content="默认输入首个号码为管理员"
                  placement="top"
                >
                <div slot="content">
                  <div>1.多个IP之间请用英文逗号(,)隔开；</div>
                  <div>2.最多支持30个IP；</div>
                  <div>3.IP地址格式：xxx.xxx.xxx.xxx</div>
                  <div>4.IP段格式：xxx.xxx.xxx.xxx/1-255 </div>
                  <div>4.通配（不建议）：xxx.xxx.xxx.* </div>
                </div>
                  <i
                    style="color: #409eff; margin-left: 5px"
                    class="el-icon-info"
                  ></i>
                </el-tooltip>
              </span> -->
              <el-input
                type="textarea"
                v-model="addUserStep1Form.formData.ip"
                placeholder="多个IP之间请用英文逗号(,)隔开；最多支持100个IP"
                resize="none"
                class="input-w"
                :rows="4"
                @paste.prevent="handlePaste"
              ></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="addUserStep1Form.formData.remark"
                resize="none"
                class="input-w-3"
                :rows="4"
              ></el-input>
            </el-form-item>

            <!-- <div v-if="flagCreated" style="text-align: right; clear: both">
              <el-button type="primary" @click="next('addUserStep1Form')"
                >下一步</el-button
              >
            </div> -->
            <div style="text-align: right; clear: both">
              <!-- <el-button @click="back()">上一步</el-button> -->
              <el-button @click="cancelForm()">取 消</el-button>
              <el-button
                v-if="!subLoading"
                type="primary"
                @click="submitForm('addUserStep1Form')"
                >提交</el-button
              >
              <el-button v-else type="primary" :loading="subLoading" disabled
                >提交中</el-button
              >
            </div>
            <div class="tips"></div>
            <el-form-item label="IP说明" prop="remark">
              <ul
                style="
                  list-style: disc;
                  margin-left: 20px;
                  color: #666;
                  line-height: 1.7;
                "
              >
                <li>多个IP之间请用英文逗号(,)隔开，最多支持100个IP；</li>
                <div>ip支持3种格式：</div>
                <li>1）静态IP格式：*************</li>
                <li>2）CIDR格式：***********/24</li>
                <li>3）通配符格式：101.87.66.* （不推荐）</li>
              </ul>
            </el-form-item>
          </el-form>
        </div>
        <div v-if="roleId == 7">
          <el-form
            :model="addUserStep1Form.formData"
            :inline="true"
            :rules="addUserStep1Form.formRule"
            ref="addUserStep1Form"
            label-width="144px"
          >
            <el-form-item label="用户类型" prop="roleId">
              <el-select
                disabled
                v-model="addUserStep1Form.formData.roleId"
                class="input-w"
                placeholder="请选择用户类别"
              >
                <el-option label="管理商" value="12"></el-option>
                <el-option
                  v-if="statusOf != '新增用户信息'"
                  label="子用户"
                  value="13"
                ></el-option>
                <el-option label="终端" value="14"></el-option>
                <el-option
                  v-if="statusOf != '新增用户信息'"
                  label="线上终端"
                  value="22"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="用户名称" prop="consumerNames">
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.consumerName"
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item label="公司名称" prop="compName">
              <el-select
                disabled
                ref="optionRef"
                class="input-w"
                v-model="addUserStep1Form.formData.compName"
                clearable
                filterable
                remote
                :remote-method="remoteMethod"
                :loading="loadingcomp"
                placeholder="请选择公司名称"
                @change="bindChange"
              >
                <el-option
                  v-for="(item, index) in compNamelist"
                  :key="index"
                  :label="item.company"
                  :value="item.company + '+' + item.id"
                >
                </el-option>
              </el-select>
              <!-- <el-input class="input-w" v-model="addUserStep1Form.formData.compName"></el-input> -->
            </el-form-item>
            <el-form-item
              v-if="addUserStep1Form.formData.roleId != '13'"
              label="企业ID"
              prop=""
            >
              <el-input
                class="input-w"
                disabled
                v-model="addUserStep1Form.formData.compId"
              ></el-input>
            </el-form-item>
            <el-form-item label="账号状态" prop="consumerStatus">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.consumerStatus"
                placeholder="账号状态"
              >
                <el-option label="启用" value="0"></el-option>
                <el-option label="停用" value="1"></el-option>
                <el-option
                  v-if="addUserDialog_status == 0"
                  disabled
                  label="冻结"
                  value="2"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="商城功能" prop="shoppingCapability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.shoppingCapability"
              >
                <el-option label="开启" :value="true"></el-option>
                <el-option label="关闭" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="WEB功能" prop="webCapability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.webCapability"
              >
                <el-option label="开启" :value="true"></el-option>
                <el-option label="关闭" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="API功能" prop="apiCapability">
              <el-select
                class="input-w"
                v-model="addUserStep1Form.formData.apiCapability"
              >
                <el-option label="开启" :value="true"></el-option>
                <el-option label="关闭" :value="false"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="短信接口加密方式" prop="cipherMode">
              <div>
                <el-select
                  class="input-w"
                  v-model="addUserStep1Form.formData.cipherMode"
                  placeholder="加密方式"
                >
                  <el-option label="MD5" value="1"></el-option>
                  <el-option label="SM3" value="2"></el-option>
                  <el-option label="AES" value="3"></el-option>
                </el-select>
                <div
                  style="
                    width: 345px;
                    font-size: 12px;
                    color: red;
                    line-height: 20px;
                  "
                >
                  (注意：只支持融合短信接口（JSON）)
                </div>
              </div>
            </el-form-item>
            <el-form-item
              v-if="addUserStep1Form.formData.cipherMode == '2'"
              label="加密盐"
              prop="salt"
            >
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.salt"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="addUserStep1Form.formData.cipherMode == '3'"
              label="密钥"
              prop="cipherKey"
            >
              <el-input
                class="input-w"
                v-model="addUserStep1Form.formData.cipherKey"
              ></el-input>
            </el-form-item>
            <div style="text-align: right; clear: both">
              <el-button @click="cancelForm()">取 消</el-button>
              <el-button
                v-if="!subLoading"
                type="primary"
                @click="submitForm('addUserStep1Form')"
                >提交</el-button
              >
              <el-button v-else type="primary" :loading="subLoading" disabled
                >提交中</el-button
              >
            </div>
          </el-form>
        </div>
        <!-- <div class="qualification" v-if="active == 1">
          <el-form
            :model="addUserStep1Form.formData"
            :rules="addUserStep1Form.formRule"
            ref="addUserStep1Form"
            label-width="144px"
          >
            <div style="display: flex">
              <div>
                <div>
                  <el-form-item label="营业执照" label-width="144px">
                    <div class="lege_upload">
                      <div
                        style="width：550px;display: flex"
                        @click.capture="fileType = 'fd_L'"
                      >
                        <el-upload
                          :action="action"
                          :headers="token"
                          :limit="1"
                          list-type="picture-card"
                          :on-preview="handlePictureCardPreview"
                          :on-remove="handleRemove"
                          :on-success="handlewqsSuccessY"
                          :file-list="PhotoLege"
                        >
                          <i class="el-icon-plus"></i>
                        </el-upload>
                        <div class="lege_upload_r">
                          <div>提示：</div>
                          <div>1.证件照应清晰可见容易识别，且边框完整</div>
                          <div>2.必须真实拍摄，不能使用复印件</div>
                          <div>3.大小不超过10M</div>
                          <div>4.仅支持.png、.jpg、.jpeg</div>
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                </div>
                <div  v-loadingcomp="loading2">
                  <el-form-item
                    label="企业名称"
                    label-width="144px"
                    prop="entName"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Form.formData.entName"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="企业资质编号类型"
                    label-width="144px"
                    prop="qualificationType"
                  >
                    <el-select
                      class="input-w"
                      v-model="addUserStep1Form.formData.qualificationType"
                      placeholder="请选择"
                    >
                      <el-option label="企业营业执照统一社会信用代码" value="1">
                      </el-option>
                      <el-option label="企业营业执照注册号" value="2">
                      </el-option>
                      <el-option
                        label="个体工商户营业执照统一社会信用代码 "
                        value="3"
                      >
                      </el-option>
                      <el-option label="个体工商户营业执照注册号" value="4">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="企业资质编号"
                    label-width="144px"
                    prop="qualificationNum"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Form.formData.qualificationNum"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label="企业法人姓名"
                    label-width="144px"
                    prop="qualificationNum"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Form.formData.corporateName"
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
              <div>
                <div>
                  <el-form-item label="身份证正面" label-width="150px">
                    <div @click.capture="fileType = 'fd_0'">
                      <el-upload
                        :action="action"
                        :headers="token"
                        :limit="1"
                        list-type="picture-card"
                        :on-preview="handlePictureCardPreview"
                        :on-remove="handleRemove"
                        :on-success="handlewqsSuccessZ"
                        :file-list="PhotoFront"
                      >
                        <i class="el-icon-plus"></i>
                      </el-upload>
                    </div>
                  </el-form-item>
                </div>
                <div v-loading="loading3">
                  <el-form-item
                    label-width="144px"
                    label="身份证"
                    prop="corporateIdCard"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Form.formData.personIdCard"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label-width="144px"
                    label="姓名"
                    prop="personName"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Form.formData.personName"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    label-width="144px"
                    label="手机号"
                    prop="personMobile"
                  >
                    <el-input
                      class="input-w"
                      v-model="addUserStep1Form.formData.personMobile"
                    ></el-input>
                  </el-form-item>
                  <el-form-item prop="type">
                    <span slot="label"> 身份类型 </span>
                    <el-radio-group v-model="addUserStep1Form.formData.type">
                      <el-radio label="1">企业法人</el-radio>
                      <el-radio style="margin-left:10px" label="2">企业代理人</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </div>
            </div>
            <div style="margin-top: 20px">
              <el-form-item label="核实备注" prop="verificateRemark">
                <el-input
                  type="textarea"
                  v-model="addUserStep1Form.formData.verificateRemark"
                  resize="none"
                  style="width: 600px"
                  rows="4"
                ></el-input>
              </el-form-item>
            </div>

            <div style="text-align: right; clear: both">
              <el-button type="primary" @click="rests()">重置</el-button>
              <el-button @click="back()">上一步</el-button>
              <el-button @click="cancelForm()">取 消</el-button>
              <el-button type="primary" @click="submitForm('addUserStep1Form')"
                >提交</el-button
              >
            </div>
          </el-form>
        </div> -->
      </div>
    </div>
    <!-- <el-dialog v-model="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog> -->
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  inject: ["reload"],
  data() {
    var checkStatus = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请选择状态"));
      } else {
        let selected = [];
        selected.push(parseInt(this.addUserStep1Form.formData.userId));
        if (this.checkuserClientId == 0) {
          callback();
        } else {
          window.api.post(
            window.path.omcs + "operatinguser/checkCanBeDisabled",
            { userIds: selected },
            (res) => {
              if (res.data != "0") {
                if (this.addUserStep1Form.formData.consumerStatus == 1) {
                  return callback(
                    new Error("该管理商用户名下仍有子用户正在使用，无法停用!")
                  );
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          );
        }
      }
    };
    var checkBlackWords = (rule, value, callback) => {
      if (value == "") {
        return callback(new Error("加黑关键字不能为空"));
      } else {
        console.log(value.length);
        if (value.length < 256) {
          var reg =
            /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,9}$/gi;
          if (!reg.test(value)) {
            return callback(
              new Error(
                "加黑关键词包括数字、汉字、字母、多个由英文逗号隔开最多10组"
              )
            );
          } else {
            var arr = value.split(",");
            var arr2 = [];
            for (var i = 0; i < arr.length; i++) {
              if (arr2.indexOf(arr[i]) < 0) {
                arr2.push(arr[i]);
              }
            }
            if (arr2.length != arr.length) {
              return callback(new Error("不允许重复"));
            } else {
              callback();
            }
          }
        }
        return callback(new Error("加黑关键字不能多于255个字"));
      }
    };
    var checkuserName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("用户名不能为空"));
      } else {
        if (!/[^0-9a-zA-Z_]/g.test(value)) {
          if (!/^[\da-zA-Z_]+$/.test(value)) {
            // }else if(!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,20}$/.test(value)){
            callback(new Error("由大小写字母数字_组成!"));
          } else if (/[\u4E00-\u9FA5\uF900-\uFA2D]/.test(value)) {
            callback(new Error("由大小写字母数字_组成!"));
          } else if (
            /[`~!@#$%^&*()+<>?:"{},.\/;'[\]]/im.test(value) ||
            /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
          ) {
            callback(new Error("由大小写字母数字_组成!"));
          } else {
            window.api.get(
              window.path.upms + "online/existUser/" + value.toLowerCase(),
              {
                // userId: this.checkuserClientId || "",
                // username: value.toLowerCase(),
              },
              (res) => {
                if (res.data == 1) {
                  callback(new Error("用户名已存在"));
                } else {
                  callback();
                }
              }
            );
          }
        } else {
          callback(new Error("由大小写字母数字_组成!"));
        }
      }
    };
    var checkSignature = (rule, value, callback) => {
      if (!/^\d{1,12}$/.test(value)) {
        callback(new Error("扩展号由1-12位数字组成"));
      } else {
        window.api.get(
          window.path.omcs + "operatingSignature/checkClientExt",
          { ext: value, userId: this.addUserStep1Form.formData.userId },
          (res) => {
            if (res.data == true) {
              callback(new Error("此扩展号已存在"));
            } else {
              callback();
            }
          }
        );
      }
      //  }
    };
    var sign = (rule, value, callback) => {
      if (value == "") {
        callback();
      } else {
        if (value[0] != "【" || value[value.length - 1] != "】") {
          callback(new Error("请正确填写签名格式【签名】"));
        } else if (value.length > 22) {
          callback(new Error("签名最多20位"));
        } else if (
          /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value) ||
          /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
        ) {
          callback(new Error("不能填写特殊字符"));
        } else {
          callback();
        }
      }
    };
    var splitRatio = (rule, value, callback) => {
      if (value == "") {
        callback();
      } else {
        if (!/^(?:[1-9]?\d|100)$/.test(value)) {
          callback(new Error("请填写0-100整数"));
        } else {
          callback();
        }
      }
    };
    var checkIp = (rule, value, callback) => {
      if (value) {
        // return callback(new Error("接口ip不能为空"));
        // 1.^ 和 $ 确保整个字符串符合模式。
        // 2.(\*|(\d{1,3})(\.(\*|\d{1,3})){3}) 匹配一个标准 IPv4 地址或带有通配符的 IPv4 地址。
        // 3.(/\\d{1,2})? 匹配可选的 CIDR 表示法。
        // 4.,(\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,2})?){0,29} 匹配 0 到 29 个额外的 IP 地址，逗号分隔，从而限制总共最多 30 个 IP
        const ipListRegex =
          /^((\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,3})?)(,(\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,3})?){0,99}$/;
        let ipvalue = value.replace(/\s+/g, ""); // 去除空格
        const maxLimit = 100; // 最大IP地址数量限制
        const ipArray = value.split(","); // 按逗号分隔
        if (ipArray.length > maxLimit) {
          return callback(new Error(`最多只能输入${maxLimit}个IP地址`));
        } else {
          // 数量超过限制，不做处理
          if (!ipListRegex.test(ipvalue)) {
            return callback(new Error("接口ip格式错误"));
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    };
    return {
      statusOf: "", //是新增还是编辑
      action: window.path.cpus + "v3/file/upload",
      token: {},
      active: 0,
      flagCreated: true,
      // PhotoLege: [],
      // PhotoFront: [],
      // PhotoReverse: [],
      // dialogVisible: false,
      // loading2: false,
      // loading3:false,
      // dialogImageUrl: "",
      subLoading: false,
      compNamelist: [],
      loadingcomp: false,
      fileType: "",
      checkuserClientId: 0,
      keywordsStatus: 0,
      radio: 2,
      reportStatusNum: "1",
      services: [], //客服
      // sales:[],//销售
      salesName: "", //销售
      channel: [], //通道
      replyGrabbing: [], //回复抓取
      receiptGrabbing: [], // 回执抓取
      receiptPush: [], //回执推送
      replyPush: [], //回复推送
      industrys: [], //行业类别
      pwdIsDisable: false, //生成密码是否禁止
      mobileProtecte: "0",
      addUserStep1Form: {
        //弹窗步骤1的数据和验证
        formData: {
          userId: "", //用户id
          roleId: "",
          // signature: "", //签名
          splitRatio: "",
          industryId: "",
          compAddres: "",
          remark: null,
          type: "",
          // entName: "",
          compId: "",
          webCapability: true,
          apiCapability: true,
          shoppingCapability:false,
          // corporateName: "",
          // qualificationType: "",
          // corporateIdCard: "",
          // personMobile: "",
          // personIdCard: "",
          // personName: "",
          verificateRemark: "",
          // sales:'',
          salesName: "",
          consumerStatus: "0",
          mobileProtect: true,
          email: "",
          certificate: "",
          consumerName: "",
          compName: "",
          phone: "",
          service: "",
          ip: null,
          // password: "",
          cooperateStatus: "",
          cipherMode: "", //加密方式
          salt: "", //密盐
          cipherKey: "", //AES密钥
          // interfacePassword: '', // 接口密码
          // loginPassword: '', // 账号密码
        },
        formRule: {
          roleId: [
            { required: true, message: "请选择用户类型", trigger: "change" },
          ],
          // signature: [
          //   { required: true, message: "请填写默认签名", trigger: 'change' },
          // ],
          cipherKey: [
            { required: true, message: "请填写密钥", trigger: "change" },
          ],
          // verificateRemark: [
          //   { required: true, message: "请输入核实备注", trigger: "change" },
          // ],
          cipherMode: [
            { required: true, message: "请选择加密方式", trigger: "change" },
          ],
          ip: [{ required: false, validator: checkIp, trigger: "change" }],
          // salt: [
          //   { required: true, message: "请输入密盐", trigger: "change" },
          // ],
          // entName : [
          //   { required: true, message: "请输入企业名称", trigger: "change" },
          // ],
          compId: [
            {
              required: true,
              message: "请输入企业资质编号",
              trigger: "change",
            },
          ],
          webCapability: [
            {
              required: true,
              message: "请选择是否开启web功能",
              trigger: "change",
            },
          ],
          shoppingCapability: [
            {
              required: true,
              message: "请选择是否开启商城功能",
              trigger: "change",
            },
          ],
          apiCapability: [
            {
              required: true,
              message: "请选择是否开启接口功能",
              trigger: "change",
            },
          ],
          // corporateName: [
          //   { required: true, message: "请输入企业法人名称", trigger: "change" },
          // ],
          // qualificationType: [
          //   { required: true, message: "请输入企业资质编号类型", trigger: "change" },
          // ],
          // corporateIdCard: [
          //   { required: true, message: "请输入法人身份证", trigger: "change" },
          // ],
          // personMobile: [
          //   { required: true, message: "请输入经办人手机号", trigger: "change" },
          // ],
          // personIdCard: [
          //   { required: true, message: "请输入经办人身份证", trigger: "change" },
          // ],
          // personName: [
          //   { required: true, message: "请输入经办人姓名", trigger: "change" },
          // ],
          cooperateStatus: [
            { required: true, message: "请选择合作状态", trigger: "change" },
          ],
          certificate: [
            { required: true, message: "请选择认证状态", trigger: "change" },
          ],
          consumerStatus: [
            { required: true, validator: checkStatus, trigger: "change" },
          ],
          consumerName: [
            {
              required: true,
              validator: checkuserName,
              trigger: ["change", "blur"],
            },
            {
              min: 3,
              max: 20,
              message: "长度在 3-20 个字符以内",
              trigger: ["blur", "change"],
            },
          ],
          compName: [
            { required: true, message: "请选择公司名", trigger: "change" },
            {
              min: 1,
              max: 200,
              message: "长度在 200 个字符以内",
              trigger: ["change", "change"],
            },
          ],
          compAddres: [
            { message: "请输入公司地址", trigger: "blur" },
            {
              min: 0,
              max: 40,
              message: "长度在 40 个字符以内",
              trigger: ["blur", "change"],
            },
          ],
          // email: [
          //   { message: "请输入邮箱地址", trigger: "blur" },
          //   {
          //     min: 1,
          //     max: 50,
          //     message: "长度在 50 个字符以内",
          //     trigger: ["blur", "change"],
          //   },
          //   {
          //     type: "email",
          //     message: "请输入正确的邮箱地址",
          //     trigger: ["blur", "change"],
          //   },
          // ],
          // password: [
          //   { required: true, message: "请传入密码", trigger: "change" },
          // ],
          // remark: [
          //   { required: false, trigger: ["change", "blur"] },
          //   {
          //     min: 1,
          //     max: 70,
          //     message: "长度在 70 个字符以内",
          //     trigger: ["blur", "change"],
          //   },
          // ],
        },
      },
      addUserDialog_status: 1, //用户信息是否是新增---1为新增，0为编辑
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  created() {
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
    };
    // function generateIPList(count) {
    //   const ipList = [];

    //   for (let i = 0; i < count; i++) {
    //     // 随机生成每个 IP 地址的四个段
    //     const ip = `${Math.floor(Math.random() * 256)}.${Math.floor(
    //       Math.random() * 256
    //     )}.${Math.floor(Math.random() * 256)}.${Math.floor(
    //       Math.random() * 256
    //     )}`;
    //     ipList.push(ip);
    //   }

    //   return ipList;
    // }
    // const ipArray = generateIPList(30);
    // console.log(ipArray.join(','),'ipArray');
    if (this.$route.query.i == "1") {
      this.statusOf = "新增用户信息";
      this.addUserDialog_status = 1;
      // window.location.reload()
    } else {
      this.statusOf = "编辑用户信息";
      this.addUserDialog_status = 0;
      this.handleEdit();
    }
    this.getServices(); /**获取客服列表 */
    this.getChannel(); /**获取通道列表 */
    this.getIndustrys(); /**获取行业类别列表*/
  },
  activated() {
    // window.location.reload()
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
    };
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
    };
    if (this.$route.query.i == "1") {
      this.statusOf = "新增用户信息";
      this.addUserDialog_status = 1;
      // window.location.reload()
    } else {
      this.statusOf = "编辑用户信息";
      this.addUserDialog_status = 0;
      this.handleEdit();
    }
    this.getServices(); /**获取客服列表 */
    this.getChannel(); /**获取通道列表 */
    this.getIndustrys(); /**获取行业类别列表*/
  },
  deactivated() {
    this.$refs["addUserStep1Form"].resetFields();
    this.addUserStep1Form.formData.consumerName = "";
    // this.addUserStep1Form.formData.password = ''
    this.flagCreated = true;
    // this.$router.replace('/UserMag')
    // this.reload()
    // this.getMag()
    // window.location.reload()
  },
  methods: {
    //返回
    goBack() {
      this.$router.push({ path: "/UserMag" });
    },
    rests() {
      // this.addUserStep1Form.formData.entName = ""
      // this.addUserStep1Form.formData.corporateName = ""
      // this.addUserStep1Form.formData.qualificationType = ""
      this.addUserStep1Form.formData.compId = "";
      // this.addUserStep1Form.formData.personIdCard = ""
      // this.addUserStep1Form.formData.personMobile = ""
      // this.addUserStep1Form.formData.personName = ""
      this.addUserStep1Form.formData.type = "";
      // this.addUserStep1Form.formData.verificateRemark = ""
      // this.PhotoFront = []
      // this.PhotoLege = []
      // this.loading2 = false
      // this.loading3 = false
    },
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      console.log(query, "query");
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.compNamelist = [];
        this.searchAccount();
      }
    },
    bindChange(val) {
      if (val) {
        this.addUserStep1Form.formData.compName = val.split("+")[0];
        if (val.split("+")[1] != "null") {
          this.addUserStep1Form.formData.compId = val.split("+")[1];
        } else {
          this.addUserStep1Form.formData.compId = "";
        }
      } else {
        this.searchAccount();
        this.addUserStep1Form.formData.compName = "";
        this.addUserStep1Form.formData.compId = "";
      }
    },
    getMag() {
      this.reload(); //直接使用该方法
    },
    //生成密码
    // generatePass() {
    //   this.$confirms.confirmation(
    //     "get",
    //     "确认要生成密码？",
    //     window.path.omcs + "operatinguser/getPassWord",
    //     {},
    //     (res) => {
    //       this.keywordsStatus = 1;
    //       this.addUserStep1Form.formData.password = res.data;
    //       this.passwordIsDisable = true;
    //     }
    //   );
    // },
    // 重置登录密码
    resetPassword() {
      this.$confirms.confirmation(
        "get",
        "确认要重置密码？",
        window.path.cpus + "consumerclientinfo/generatePassword",
        {},
        (res) => {
          this.addUserStep1Form.formData.loginPassword = res;
        }
      );
    },
    // 重置接口密码
    resetInterPassword() {
      this.$confirms.confirmation(
        "get",
        "确认要重置密码？",
        window.path.cpus + "consumerclientinfo/generatePassword",
        {},
        (res) => {
          this.addUserStep1Form.formData.interfacePassword = res;
        }
      );
    },
    change(val) {
      //   console.log(val, "val");
      if (val == 0) {
        this.addUserStep1Form.formData.mobileProtect = true;
      } else {
        this.addUserStep1Form.formData.mobileProtect = false;
      }
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          this.subLoading = true;
          //-----------新增-------------
          if (this.addUserDialog_status == 1) {
            //发送请求
            this.services.map((value, index) => {
              if (this.addUserStep1Form.formData.service == value.userId)
                this.addUserStep1Form.formData.serviceName = value.realName;
            });
            this.$confirms.confirmation(
              "post",
              "确认创建用户吗？",
              window.path.omcs + "operatinguser/userInfo",
              this.addUserStep1Form.formData,
              (res) => {
                if (res.code == 200) {
                  this.subLoading = false;
                  // console.log(11);
                  this.$router.push({ path: "/UserMag" });
                } else {
                  this.subLoading = false;
                }
                //返回用户管理页面
              }
            );
            //------------编辑-----------
          } else if (this.addUserDialog_status == 0) {
            //发送请求
            this.services.map((value, index) => {
              if (this.addUserStep1Form.formData.service == value.userId)
                this.addUserStep1Form.formData.serviceName = value.realName;
            });
            if (this.addUserStep1Form.formData.interfacePassword == "******") {
              //未修改接口密码,传空
              this.addUserStep1Form.formData.interfacePassword = "";
            }
            if (this.addUserStep1Form.formData.loginPassword == "******") {
              //未修改账户密码,传空
              this.addUserStep1Form.formData.loginPassword = "";
            }
            this.$confirms.confirmation(
              "put",
              "确认编辑该用户吗？",
              window.path.omcs + "operatinguser/userInfo",
              this.addUserStep1Form.formData,
              (res) => {
                console.log(res, "res");

                //返回用户管理页面
                if (res.code == 200) {
                  this.subLoading = false;
                  this.$router.push({ path: "/UserMag" });
                } else {
                  this.subLoading = false;
                }
              },
              (err) => {
                this.subLoading = false;
              }
            );
          }
        } else {
          return false;
        }
      });
    },
    handleClick() {
      this.addUserStep1Form.formData.phone =
        this.addUserStep1Form.formData.phone.replace(/[^\d\,]/g, "");
    },
    /**获取行业类别列表*/
    getIndustrys() {
      window.api.get(
        window.path.omcs + "operatingindustrycategories/selectAllIndustry",
        {},
        (res) => {
          this.industrys = res.data;
        }
      );
    },
    /**获取客服列表 */
    getServices() {
      window.api.get(
        window.path.upms + "user/selectAllSalesOrService",
        { flag: 2 },
        (res) => {
          this.services = res.data;
        }
      );
    },
    /**获取通道列表 */
    getChannel() {
      window.api.get(
        window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup",
        {},
        (res) => {
          this.channel = res.data;
        }
      );
    },
    next() {
      this.$refs["addUserStep1Form"].validate((valid, value) => {
        if (valid) {
          if (this.active++ > 1) this.active = 0;
        }
      });
    },
    back() {
      this.active--;
    },
    handlePaste(event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData("Text");
      if (pastedData) {
        let formattedIPs = "";
        // 使用正则表达式拆分内容，考虑多种分隔符
        const ipList = pastedData.split(/[\s,]+/);
        // 去重并过滤掉空字符串
        const uniqueIPs = Array.from(new Set(ipList)).filter(
          (ip) => ip.trim() !== ""
        );
        // 将处理后的IP列表转换为逗号分隔的字符串
        formattedIPs = uniqueIPs.join(",");
        // 阻止默认的粘贴行为并将结果手动插入
        event.target.value = formattedIPs;
        this.addUserStep1Form.formData.ip = formattedIPs;
      }
    },
    //取消 返回上一级
    cancelForm() {
      this.$router.push({ path: "/UserMag" });
    },
    //编辑的赋值
    handleEdit() {
      this.checkuserClientId = this.$route.query.d;
      this.flagCreated = false;
      window.api.get(
        window.path.omcs + "operatinguser/userInfo/" + this.checkuserClientId,
        {},
        (res) => {
          //   console.log(res.data, "res");
          for (let i in res.data) {
            if (!(res.data[i] == null || i == "service" || i == "industryId")) {
              res.data[i] += "";
            }
          }
          if (res.data.ip == null) {
            res.data.ip = "";
          }

          this.addUserStep1Form.formData = res.data;
          if (res.data.mobileProtect == "true") {
            this.mobileProtecte = "0";
          } else {
            this.mobileProtecte = "1";
            // console.log(this.mobileProtecte, "jj");
          }
          this.addUserStep1Form.formData.apiCapability = JSON.parse(
            this.addUserStep1Form.formData.apiCapability
          );
          this.addUserStep1Form.formData.webCapability = JSON.parse(
            this.addUserStep1Form.formData.webCapability
          );
          this.addUserStep1Form.formData.shoppingCapability = JSON.parse(
            this.addUserStep1Form.formData.shoppingCapability
          );
        }
      );
    },
    // handlePictureCardPreview(file) {
    //   this.dialogImageUrl = file.url;
    //   this.dialogVisible = true;
    // },
    // handleRemove() {
    //   let _this = this;
    //   if (_this.fileType == "fd_0") {
    //     _this.PhotoFront = [];
    //     this.loading2 = false;
    //   } else if (_this.fileType == "fd_1") {
    //     _this.PhotoReverse = [];
    //   } else if (_this.fileType == "fd_L") {
    //     console.log(_this.fileType, "ll");
    //     _this.PhotoLege = [];
    //     this.loading2 = false;
    //   }
    // },
    // handlewqsSuccessY(res, file, fileList) {
    //   // console.log(res,'ll');
    //    let _this = this
    //    _this.PhotoLege = fileList;
    //    _this.PhotoLege[0].url = window.path.imgU+res.data.fullpath
    //   this.loading2 = true;
    //   window.api.get(
    //     window.path.cpus + "consumerCertificate/entLicenseInfo",
    //     { filePath: res.data.fullpath },
    //     (ress) => {
    //       if (ress.code == 200) {
    //         // console.log(ress.data);
    //         this.addUserStep1Form.formData.corporateName =
    //           ress.data.corporateName;
    //         this.addUserStep1Form.formData.entName = ress.data.entName;
    //         this.addUserStep1Form.formData.qualificationNum =
    //           ress.data.entQualificationNum;
    //         // this.addUserStep1Form.formData.qualificationType =
    //         //   ress.data.entQualificationType + "";
    //         this.loading2 = false;
    //       } else {
    //         this.$message({
    //           type: "error",
    //           duration: "2000",
    //           message: ress.msg,
    //         });
    //       }
    //     }
    //   );
    // },
    // handlewqsSuccessZ(res, file, fileList) {
    //   let _this = this
    //   // console.log(res,'kk');
    //   _this.PhotoFront = fileList;
    //      _this.PhotoFront[0].url = window.path.imgU+res.data.fullpath
    //   this.loading3 = true;
    //   window.api.get(
    //     window.path.cpus + "consumerCertificate/idCardInfo",
    //     { filePath: res.data.fullpath },
    //     (ress) => {
    //       if (ress.code == 200) {
    //         // console.log(ress.data);
    //         // this.addUserStep1Form.formData.personName = ress.data.personName;
    //         // this.addUserStep1Form.formData.personIdCard =
    //         //   ress.data.personIdCard;
    //         // this.addUserStep1Form.formData.corporateName = ress.data.corporateName
    //         // this.addUserStep1Form.formData.entName = ress.data.entName
    //         // this.addUserStep1Form.formData.entQualificationNum = ress.data.entQualificationNum
    //         // this.addUserStep1Form.formData.entQualificationType = ress.data.entQualificationType+''
    //         this.loading3 = false;
    //       } else {
    //         this.$message({
    //           type: "error",
    //           duration: "2000",
    //           message: ress.msg,
    //         });
    //       }
    //     }
    //   );
    // },
    handlewqsSuccessF(res, file, fileList) {
      // console.log(res,'jj');
    },
    // handlewqsSuccess(res, file, fileList){
    //     let _this = this;
    //   if (_this.fileType == "fd_0") {
    //     _this.PhotoFront = fileList;
    //      _this.PhotoFront[0].url = window.path.imgU+res.data.fullpath
    //      this.loading2 = true
    //      this.addUserStep1Form.formData.idCardFront = res.data.fullpath
    //       window.api.get(window.path.cpus + 'consumerCertificate/idCardInfo',{filePath:res.data.fullpath},ress=>{
    //           if(ress.code ==200){
    //             this.addUserStep1Form.formData.name = ress.data.personName
    //             this.addUserStep1Form.formData.idCard = ress.data.personIdCard
    //             this.loading2 = false
    //           }else{
    //               this.$message({
    //                             type: "error",
    //                             duration: "2000",
    //                             message: ress.msg
    //                         });
    //           }

    //       })
    //   } else if (_this.fileType == "fd_1") {
    //     _this.PhotoReverse = fileList;
    //     _this.PhotoReverse[0].url = window.path.imgU+res.data.fullpath
    //     this.addUserStep1Form.formData.idCardBack = res.data.fullpath
    //   }else if(_this.fileType == "fd_L"){
    //       _this.PhotoLege = fileList;
    //       this.loading2 = true
    //      _this.PhotoLege[0].url = window.path.imgU+res.data.fullpath
    //      this.addUserStep1Form.formData.businessLicense = res.data.fullpath
    //        window.api.get(window.path.cpus + 'consumerCertificate/entLicenseInfo',{filePath:res.data.fullpath},ress=>{
    //            if(ress.code ==200){
    //                 this.addUserStep1Form.formData.corporateName = ress.data.corporateName
    //                 this.addUserStep1Form.formData.compName = ress.data.entName
    //                 this.addUserStep1Form.formData.number = ress.data.entQualificationNum
    //                 this.addUserStep1Form.formData.qualificationType = ress.data.entQualificationType+''
    //                 this.loading2 = false
    //            }else{
    //                this.$message({
    //                             type: "error",
    //                             duration: "2000",
    //                             message: ress.msg
    //                         });
    //            }
    //        })
    //   }
    // },
  },
  watch: {
    mobileProtecte(newVal, oldVal) {
      if (newVal == 0) {
        this.addUserStep1Form.formData.mobileProtect = true;
      } else {
        this.addUserStep1Form.formData.mobileProtect = false;
      }
    },
  },
};
</script>
<style scoped>
.tips {
  width: 100%;
  margin: 10px 0;
  border-bottom: 1px dashed #ccc;
}
.qualification {
  position: relative;
}

.rests {
  position: absolute;
  bottom: 30%;
  right: 40%;
  margin-bottom: 20px;
  margin-right: 10px;
}

.input-w {
  width: 345px !important;
}

.el-radio-group .el-radio {
  margin-right: 0px;
}

.fillet {
  padding: 20px;
}

.input-w-2 {
  width: 320px !important;
}

.input-w-3 {
  width: 320px !important;
}

.input-w-4 {
  width: 190px !important;
}

.input-w-sm {
  width: 126px !important;
}

.input-w-f {
  width: 80px !important;
}

.float-div-1 {
  float: left;
}

.float-div-2 {
  float: left;
}

.float-div-3 {
  float: left;
}

.clear-div {
  clear: both;
}

.red {
  color: red;
}

.lege_upload {
  width: 100%;
  /* padding: 20px; */
}

.lege_upload_r {
  margin-left: 5px;
  line-height: 25px;
  color: #c6c6c6;
  font-size: 12px;
}

.lege_upload_b {
  margin-top: 30px;
  margin-left: 10px;
  width: 95%;
  height: 50px;
  border-bottom: 1px dashed #ccc;
}

@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>
<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>




