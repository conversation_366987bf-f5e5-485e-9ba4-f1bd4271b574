<template>
  <div class="container_left">
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <el-tabs v-model="activeName2" type="card">
        <el-tab-pane label="用户发送明细报表" name="first">
          <keep-alive>
            <component
              v-bind:is="currentTabComponent"
              v-if="activeName2 == 'first'"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="实时查询" name="second">
          <keep-alive>
            <component
              v-bind:is="RealTimeQuery"
              v-if="activeName2 == 'second'"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <el-tab-pane label="回执信息" name="receipt">
          <keep-alive>
            <component
              v-bind:is="receiptInformation"
              v-if="activeName2 == 'receipt'"
            ></component>
          </keep-alive>
        </el-tab-pane>
        <!-- <el-tab-pane label="号码诊断" name="mobile">
                  <component v-bind:is="mobileDiagnose"></component>
                </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script>
import SendRecord from './components/SendRecord.vue'
import SendRealTimeQuery from './components/SendRealTimeQuery.vue'
import receiptInformation from './components/receiptInformation.vue'
// import MobileDiagnose from './components/MobileDiagnose.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    SendRecord,
    SendRealTimeQuery,
    // MobileDiagnose
    receiptInformation,
  },
  name: 'userSendNotes',
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  data() {
    return {
      receiptInformation: 'receiptInformation',
      currentTabComponent: 'SendRecord',
      RealTimeQuery: 'SendRealTimeQuery',
      mobileDiagnose: 'MobileDiagnose',
      activeName2: 'first',
    }
  },
}
</script>
