<template>
    <div style="background: #fff; padding: 15px">
        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="用户名">
                    <el-input class="input-w" v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
                </el-form-item>
                <el-form-item label="短信类型">
                    <el-select class="input-w" v-model="searchForm.smsType" placeholder="请选择短信类型" clearable>
                        <el-option :label="'验证码'" :value="1"></el-option>
                        <el-option :label="'行业'" :value="2"></el-option>
                        <el-option :label="'营销'" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 工具栏 -->
        <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
                <el-button type="primary" @click="handleAdd">添加预警</el-button>
            </template>
        </vxe-toolbar>

        <!-- 数据表格 -->
        <vxe-table ref="alertTableRef" id="alertTable" border stripe show-header-overflow :custom-config="customConfig"
            :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1" v-loading="loading"
            element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;"
            :data="tableData">
            <!-- <vxe-column field="id" title="ID" width="60"></vxe-column> -->
            <vxe-column field="username" title="用户名" min-width="120"></vxe-column>
            <vxe-column field="smsType" title="短信类型" width="100">
                <template #default="{ row }">
                    {{ getSmsTypeLabel(row.smsType) }}
                </template>
            </vxe-column>
            <vxe-column field="successRateAlert" title="成功率告警开关" width="120">
                <template #default="{ row }">
                    <el-tag :type="row.successRateAlert ? 'success' : 'danger'">
                        {{ row.successRateAlert ? '启用' : '禁用' }}
                    </el-tag>
                </template>
            </vxe-column>
            <vxe-column field="successRate" title="成功率触发阈值" width="120"></vxe-column>
            <vxe-column field="successRateRange" title="成功率告警时间" min-width="140"></vxe-column>
            <vxe-column field="receiptRateAlert" title="回执率告警开关" width="120">
                <template #default="{ row }">
                    <el-tag :type="row.receiptRateAlert ? 'success' : 'danger'">
                        {{ row.receiptRateAlert ? '启用' : '禁用' }}
                    </el-tag>
                </template>
            </vxe-column>
            <vxe-column field="receiptRate" title="回执率触发阈值" width="120"></vxe-column>
            <vxe-column field="receiptRateRange" title="回执率告警时间" min-width="140"></vxe-column>
            <vxe-column field="status" title="同步状态" width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status ? 'success' : 'warning'">
                        {{ row.status ? '已同步' : '待同步' }}
                    </el-tag>
                </template>
            </vxe-column>
            <vxe-column field="createName" title="创建人" min-width="100"></vxe-column>
            <vxe-column field="createTime" title="创建时间" min-width="160"></vxe-column>
            <vxe-column field="操作" title="操作" fixed="right" width="200">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
            </vxe-column>
        </vxe-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pagination.currentPage" :page-size="pagination.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
        </div>

        <!-- 弹窗表单 -->
        <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑预警配置' : '添加预警配置'" width="650px"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="140px">
                <el-form-item label="用户名" prop="username">
                    <el-input :disabled="isEdit" v-model="form.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="短信类型" prop="smsType">
                    <el-select :disabled="isEdit" v-model="form.smsType" placeholder="请选择短信类型" style="width: 100%">
                        <el-option :label="'验证码'" :value="1"></el-option>
                        <el-option :label="'行业'" :value="2"></el-option>
                        <el-option :label="'营销'" :value="3"></el-option>
                    </el-select>
                </el-form-item>

                <el-divider content-position="center">成功率告警设置</el-divider>

                <el-form-item label="成功率告警开关" prop="successRateAlert">
                    <el-switch v-model="form.successRateAlert" :active-value="1" :inactive-value="0" active-text="启用"
                        inactive-text="禁用"></el-switch>
                </el-form-item>
                <el-form-item label="成功率触发阈值" prop="successRate">
                    <el-input-number v-model="form.successRate" :max="100" controls-position="right"
                        style="width: 100%"></el-input-number>
                </el-form-item>
                <el-form-item label="成功率告警时间" prop="successRateRange">
                    <el-input-number v-model="form.successRateRange" :max="1440" controls-position="right"
                        style="width: 100%"></el-input-number>
                    <template #label>
                        <span>成功率告警时间 <el-tooltip content="单位（分钟）" placement="top">
                            <el-icon><QuestionFilled /></el-icon>
                        </el-tooltip></span>
                    </template>
                    <!-- <template #append>分钟</template> -->
                </el-form-item>

                <el-divider content-position="center">回执率告警设置</el-divider>

                <el-form-item label="回执率告警开关" prop="receiptRateAlert">
                    <el-switch v-model="form.receiptRateAlert" :active-value="1" :inactive-value="0" active-text="启用"
                        inactive-text="禁用"></el-switch>
                </el-form-item>
                <el-form-item label="回执率触发阈值" prop="receiptRate">
                    <el-input-number v-model="form.receiptRate" :max="100" controls-position="right"
                        style="width: 100%"></el-input-number>
                </el-form-item>
                <el-form-item label="回执率告警时间" prop="receiptRateRange">
                    <el-input-number v-model="form.receiptRateRange" :max="1440" controls-position="right"
                        style="width: 100%"></el-input-number>
                    <template #label>
                        <span>回执率告警时间 <el-tooltip content="单位（分钟）" placement="top">
                            <el-icon><QuestionFilled /></el-icon>
                        </el-tooltip></span>
                    </template>
                    <!-- <template #append>分钟</template> -->
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 表格引用
const alertTableRef = ref(null);
const toolbarRef = ref(null);

// 表格配置
const customConfig = {
    storage: true
};

// 搜索表单
const searchForm = reactive({
    username: '',
    smsType: '',
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
});

// 弹窗表单
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const form = reactive({
    id: '',
    username: '',
    smsType: '',
    successRateAlert: 1,
    successRate: null,
    successRateRange: null,
    receiptRateAlert: 1,
    receiptRate: null,
    receiptRateRange: null
});

// 表单验证规则
const rules = {
    username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
    ],
    smsType: [
        { required: true, message: '请选择短信类型', trigger: 'change' }
    ],
    successRate: [
        { required: true, message: '请输入成功率触发阈值', trigger: 'blur' }
    ],
    successRateRange: [
        { required: true, message: '请输入成功率告警时间', trigger: 'blur' }
    ],
    receiptRate: [
        { required: true, message: '请输入回执率触发阈值', trigger: 'blur' }
    ],
    receiptRateRange: [
        { required: true, message: '请输入回执率告警时间', trigger: 'blur' }
    ]
};

// 短信类型标签
const getSmsTypeLabel = (type) => {
    const smsTypeMap = {
        1: '验证码',
        2: '行业',
        3: '营销'
    };
    return smsTypeMap[type] || '未知类型';
};
// 获取预警列表数据
const getAlertList = async () => {
    loading.value = true;
    try {
        // 使用 axios 发送请求
        const params = {
            current: pagination.currentPage,
            size: pagination.pageSize,
            ...searchForm
        };

        // 调用实际API
        window.api.post(window.path.omcs + 'operatingclientsms/alertconfig/page', params, (res) => {
            // 设置表格数据和分页信息
            if (res.code == 200) {
                tableData.value = res.data.records;
                pagination.total = res.data.total;
                loading.value = false;
            } else {
                ElMessage.error(res.msg);
                loading.value = false;
            }
        }, (error) => {
            loading.value = false;
            ElMessage.error('获取预警列表失败，请稍后重试');
        });
    } catch (error) {
        console.error('获取预警列表失败:', error);
        ElMessage.error('获取预警列表失败，请稍后重试');
        loading.value = false;
    }
};

// 查询
const handleSearch = () => {
    pagination.currentPage = 1;
    getAlertList();
};

// 重置查询条件
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = '';
    });
    handleSearch();
};

// 处理页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    getAlertList();
};

// 处理每页显示数量变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    getAlertList();
};

// 添加预警
const handleAdd = () => {
    isEdit.value = false;
    // 重置表单
    resetForm();

    dialogVisible.value = true;
};

// 编辑预警
const handleEdit = (row) => {
    isEdit.value = true;
    // 填充表单数据，确保数值类型正确
    form.id = row.id;
    form.username = row.username;
    form.smsType = row.smsType;
    form.successRateAlert = Number(row.successRateAlert);
    form.successRate = row.successRate !== undefined ? Number(row.successRate) : null;
    form.successRateRange = row.successRateRange !== undefined ? Number(row.successRateRange) : null;
    form.receiptRateAlert = Number(row.receiptRateAlert);
    form.receiptRate = row.receiptRate !== undefined ? Number(row.receiptRate) : null;
    form.receiptRateRange = row.receiptRateRange !== undefined ? Number(row.receiptRateRange) : null;

    dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // 构建提交的数据
                const submitData = {
                    ...form
                };

                // 根据是编辑还是新增，调用不同的接口
                if (isEdit.value) {
                    // 编辑操作使用PUT方法
                    window.api.put(window.path.omcs + 'operatingclientsms/alertconfig', submitData, (res) => {
                        // 关闭弹窗
                        if (res.code == 200) {
                            // 刷新列表
                            getAlertList();
                            // 提示成功
                            ElMessage.success('修改成功');
                            dialogVisible.value = false;
                        } else {
                            ElMessage.error(res.msg);
                        }


                    }, (error) => {
                        console.error('修改预警失败:', error);
                        ElMessage.error('修改预警失败，请稍后重试');
                    });
                } else {
                    // 新增操作使用POST方法
                    window.api.post(window.path.omcs + 'operatingclientsms/alertconfig', submitData, (res) => {
                        // 关闭弹窗
                        if (res.code == 200) {
                            // 刷新列表
                            getAlertList();
                            // 提示成功
                            ElMessage.success('添加成功');
                            dialogVisible.value = false;
                        } else {
                            ElMessage.error(res.msg);
                        }


                    }, (error) => {
                        console.error('添加预警失败:', error);
                        ElMessage.error('添加预警失败，请稍后重试');
                    });
                }
            } catch (error) {
                console.error(isEdit.value ? '修改预警失败:' : '添加预警失败:', error);
                ElMessage.error(isEdit.value ? '修改预警失败，请稍后重试' : '添加预警失败，请稍后重试');
            }
        } else {
            return false;
        }
    });
};

// 删除预警配置（软删除）
const handleDelete = async (row) => {
    try {
        // 确认删除
        await ElMessageBox.confirm('确定要删除该预警配置吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        // 调用实际API
        window.api.delete(window.path.omcs + 'operatingclientsms/alertconfig/' + row.id, {}, () => {
            // 提示成功
            ElMessage.success('删除成功');

            // 刷新列表
            getAlertList();
        }, (error) => {
            console.error('删除预警配置失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        });
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除预警配置失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        }
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    // 重置表单数据到默认值
    form.id = '';
    form.username = '';
    form.smsType = '';
    form.successRateAlert = 1;
    form.successRate = null;
    form.successRateRange = null;
    form.receiptRateAlert = 1;
    form.receiptRate = null;
    form.receiptRateRange = null;
};

// 监听对话框关闭事件
watch(dialogVisible, (newValue) => {
    if (!newValue) {
        // 当对话框关闭时重置表单
        resetForm();
    }
});

// 页面加载时获取数据
onMounted(() => {
    const $table = alertTableRef.value
    const $toolbar = toolbarRef.value
    if ($table && $toolbar) {
        $table.connect($toolbar)
    }
    getAlertList();
});
</script>

<style scoped>
.alert-config-container {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-container {
    margin-bottom: 20px;
    padding: 15px;
    /* background-color: #f5f7fa; */
    border-radius: 4px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>
