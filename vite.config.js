import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';
import AutoImport from 'unplugin-auto-import/vite'
import svgLoader from "vite-svg-loader"
import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
const Timestamp= new Date().getTime();//随机时间戳
const resolve = (dir) => path.join(__dirname, dir)
export default defineConfig(({ mode }) => {
  const config = loadEnv(mode, '/')
  return {
    base: '/', // 基本路径
    build: {
			/** 消除打包大小超过 500kb 警告 */
			chunkSizeWarningLimit: 50000000,
			/** Vite 2.6.x 以上需要配置 minify: "terser", terserOptions 才能生效 */
			minify: "terser",
			/** 在打包代码时移除 console.log、debugger 和 注释 */
			terserOptions: {
				compress: {
					drop_console: true,
					drop_debugger: true,
					pure_funcs: ["console.log",'console.warn']
				},
				format: {
					/** 删除注释 */
					comments: false
				}
			},
			/** 打包后静态资源目录 */
			assetsDir: "static",
			rollupOptions: {
				output: {
					assetFileNames: assetInfo => {
						var info = assetInfo.name.split('.')
						var extType = info[info.length - 1]
						if (
							/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)
						) {
							extType = 'media'
						} else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
							extType = 'img'
						} else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
							extType = 'fonts'
						}
						return `static/${extType}/[name].[hash]${Timestamp}.[ext]`
					},
					chunkFileNames: `static/js/[name].[hash]${Timestamp}.js`,
          entryFileNames: `static/js/[name].[hash]${Timestamp}.js`,
				},
        onwarn(warning, warn) {
          // 忽略特定警告
          if (warning.code === 'SOME_SPECIFIC_WARNING_CODE') return;
  
          // 默认行为
          warn(warning);
        }
			},
		},
    // server: {
    //   proxy: {
    //     // 设置代理
    //     '/api': {
    //       target: 'http://192.168.1.5',
    //       changeOrigin: true,
    //     },
    //   },
    // },
    // resolve: {
    //   alias: {
    //     '@': path.resolve(__dirname, 'src-out'), // 配置路径别名
    //   },
    // },
    plugins: [
      vue({
        // 添加 Vue 的编译时标志
        reactivityTransform: true,
        include: [/\.vue$/, /\.md$/],
      }),
      AutoImport({
        imports: ["vue", "vue-router"], // 自动导入vue和vue-router相关函数
        }),
      /** 将 SVG 静态图转化为 Vue 组件 */
      svgLoader(),
      /** SVG */
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/icons")],
        symbolId: "icon-[dir]-[name]"
      }),
    ],
    define: {
      // 确保在生产环境中定义这些标志
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
    },
    resolve: {
      alias: {
        '@': resolve('src'),
        comps: resolve('src/components/publicComponents'),
        apis: resolve('src/utils'),
        views: resolve('src/components/page'),
        utils: resolve('src/utils'),
        routes: resolve('src/routes'),
        styles: resolve('src/styles')
      }
    },
    server: {
      /** 是否开启 HTTPS */
      https: false,
      /** 设置 host: true 才可以使用 Network 的形式，以 IP 访问项目 */
      host: '0.0.0.0', // host: "0.0.0.0"
      /** 端口号 */
      port: 668,
      /** 是否自动打开浏览器 */
      open: false,
      /** 跨域设置允许 */
      cors: true,
      /** 端口被占用时，是否直接退出 */
      strictPort: false,
      historyApiFallback: true, // 处理 SPA 路由问题
      proxy: {
        // '/api': {
        //   target: 'http://**********',
        //   changeOrigin: true
        // },
        '/gateway': {
          target: 'http://opt.zthysms.cn',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/gateway/, '/gateway')
        }
      }
    },
  }
});