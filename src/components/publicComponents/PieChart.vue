<template>
  <div :id="id" :style="style"></div>
</template>

<script>
// let echarts = require('echarts')
import * as echarts from 'echarts';
export default {
  name: 'PieChart',
  props: {
    id: {
      type: String,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '400px',
    },
    basicOption: {
      type: Object,
    },
  },
  computed: {
    style() {
      return {
        height: this.height,
        width: this.width,
      }
    },
  },
  methods: {
    handleSetEcharts: function () {
      let myChart = echarts.init(document.getElementById(this.id))
      let option = {
        title: this.basicOption.title,
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          position: 'top', //鼠标放上去提示显示位置
        },
        series: [
          {
            // name: '业务健康度',
            type: 'pie',
            hoverAnimation: false,
            labelLine: {
              normal: {
                show: this.basicOption.labelLine.normal.show,
                length: 20,
                length2: 20,
                lineStyle: {
                  type: 'dotted',
                  color: '#ccc',
                },
              },
            },
            radius: this.basicOption.radius,
            itemStyle: {
              // emphasis: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              // }
            },
            label: {
              normal: {
                show: this.basicOption.label.normal.show,
                formatter: '{b|{b}}\n{d|{d}%}',
                rich: {
                  b: {
                    fontSize: 12,
                    color: '#999',
                    align: 'left',
                    padding: 4,
                  },
                  d: {
                    fontSize: 14,
                    color: '#666',
                    align: 'center',
                    padding: 4,
                  },
                },
              },
            },
            color: this.basicOption.bgColor,
            data: this.basicOption.data,
          },
        ],
      }
      myChart.setOption(option)
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    },
  },
  watch: {
    basicOption: {
      handler(newValue, oldValue) {
        this.handleSetEcharts()
      },
      deep: true,
    },
  },
  mounted() {
    this.handleSetEcharts()
  },
}
</script>
