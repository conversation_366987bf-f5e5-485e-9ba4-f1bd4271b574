<template>
  <div :id="id" :style="style"></div>
</template>

<script>
let echarts = require('echarts')
export default {
  name: 'Chart',
  data() {
    return {
      chart: '',
    }
  },
  props: {
    id: {
      type: String,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '400px',
    },
    option: {
      type: Object,
    },
  },
  computed: {
    style() {
      return {
        height: this.height,
        width: this.width,
      }
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.chart = echarts.init(document.getElementById(this.id))
      this.chart.setOption(this.option)
      let _this = this
      window.addEventListener('resize', function () {
        _this.chart.resize()
      })
    },
  },
  watch: {
    //观察option的变化
    option: {
      handler(newVal, oldVal) {
        if (this.chart) {
          if (newVal) {
            this.chart.setOption(newVal)
          } else {
            this.chart.setOption(oldVal)
          }
        } else {
          this.init()
        }
      },
      deep: true, //对象内部属性的监听，关键。
    },
  },
}
</script>
