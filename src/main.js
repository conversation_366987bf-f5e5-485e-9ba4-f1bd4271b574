import * as Vue from 'vue'
import { createApp } from 'vue'
// import SocketIO from 'socket.io-client'
// import VueSocketIO  from 'vue-socket.io'
// Vue.use(new VueSocketIO({
//   debug:true,
//   connection: 'ws://192.168.1.6:9999',
//   options: { path: "/", transports: ['websocket', 'polling', 'flashsocket'] }
// }))
// import './plugins/axios'
import App from './App.vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
const app = createApp(App)
window.app = app;
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'
// import '@/style/theme-green/index.css';       // 浅绿色主题
// app.use(ElementUI, { size: 'small' })
import router from './router/index.js'
import store from './store/store.js'
import '@/utils/browser_patch'
import '@/style/normalize.css'
import '@/style/icon.css'
import 'default-passive-events'
// import '@/assets/theme/index.css'
import '@/assets/styles/iconfont/iconfont.css'
import common from './assets/js/common.js'
import validates from './assets/js/validationRule.js'
// import * as filters from './assets/js/filters.js'
import Moment from 'moment'
import global from './plugins/global.js'

import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'
import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'

app.config.globalProperties.global = global
import { parseTime } from '@/utils/index'
app.config.globalProperties.$parseTime = parseTime
//二次弹框
import confirms from './assets/js/confirm.js'
app.config.globalProperties.$confirms = confirms
app.config.globalProperties.$common = common
window.common = common
import api from './plugins/axios.js'
import Files from './plugins/exportFile.js'
app.config.globalProperties.$File = Files
// 绑定 axios 到全局（vue对象原型方法）
app.config.globalProperties.$api = api
window.api = api
//接口路径配置
import globalPath from '@/assets/js/globalPath.vue'
import { formatDate } from '@/assets/js/date.js'
// import {version } from '@/utils/version'
import _ from 'lodash' //引入lodash
app.config.globalProperties._ = _
app.config.globalProperties.API = globalPath.globalPath //接口路径配置
window.path = globalPath.globalPath
app.use(validates)

//引入全局指令消除element-ui单选组件el-radio-group 的报错问题
app.directive('removeHidden', {
  bind(el) {
    // 查找所有匹配的元素
    let ariaEl = el.querySelectorAll('.el-radio__original[aria-hidden]');
    ariaEl.forEach((item) => {
      // 移除 aria-hidden 属性
      item.removeAttribute('aria-hidden');
    });
  }
});

app.use(VxeUITable)
app.use(VxeUI)

// Object.keys(filters).forEach((key) => {
//   ;(
//     app.config.globalProperties.$filters ||
//     (app.config.globalProperties.$filters = {})
//   ).key = filters[key]
// })(
//   app.config.globalProperties.$filters ||
//     (app.config.globalProperties.$filters = {})
// ).fmtDate = function (time) {
//   let date = new Date(time * 1)
//   return formatDate(date, 'YYYY-MM-DD HH:mm:ss')
// }
app.config.globalProperties.moment = Moment
// app.config.globalProperties.$bus = new Vue()
// import '@/style/base.styl'
// import '@/style/utility.less'

// import './plugins/element.js'

//使用钩子函数对路由进行权限跳转
// router.beforeEach((to, from, next)=>{
//     console.log(to,'to');
//     if(to.matched.some(r => r.meta.requireAuth)){
//         const isLogin = getCookie('ZTADMIN_TOKEN')
//         if(isLogin){
//             next()
//         }else{
//             if(to.path === '/login'){
//                 next()
//             }else{
//                 next('/login')
//             }
//         }
//     }
// })
// const VUE_APP_VERSION = version;
// const vers = window.localStorage.getItem('version');
// console.log(VUE_APP_VERSION,' VUE_APP_VERSION')
// if(vers !== VUE_APP_VERSION){
//   localStorage.clear();
//   sessionStorage.clear();
//   window.localStorage.setItem('version', VUE_APP_VERSION);
//   window.location.reload();
// }
function tooltip() {
  try {
    let tooltipList = document.getElementsByClassName('el-tooltip__popper')
    for (let tip in tooltipList) {
      let tipItem = tooltipList[tip]
      if (typeof tipItem == 'object') {
        tipItem.style.display = 'none'
      }
    }
  } catch (error) {
    console.log('error--tooltip', error)
  }
}
router.beforeEach((to, from, next) => {
  if (to.matched.some((r) => r.meta.requireAuth)) {
    const isLogin = getCookie('ZTADMIN_TOKEN')
    if (isLogin) {
      tooltip()
      next()
    } else {
      if (to.path === '/login') {
        //跳出循环
        next()
      } else {
        next({ path: 'login' })
      }
    }
  } else {
    // console.log(11);
    tooltip()
    next()
  }
})
// router.beforeEach((to, from, next) => {
//   const isLogin = getCookie('ZTADMIN_TOKEN');
//   if(!isLogin && to.path !== '/login'){
//       next('/login');
//   }else if(to.meta.permission){
//       // 如果是管理员权限则可进入，这里只是简单的模拟管理员权限而已
//       role === 'ZTADMIN_TOKEN' ? next() : next('/403');
//   }else{
//       // 简单的判断IE10及以下不进入富文本编辑器，该组件不兼容
//       if(navigator.userAgent.indexOf('MSIE') > -1 && to.path === '/editor'){
//           Vue.prototype.$alert('vue-quill-editor组件不兼容IE10及以下浏览器，请使用更高版本的浏览器查看', '浏览器不兼容通知', {
//               confirmButtonText: '确定'
//           });
//       }else{
//           next();
//       }
//   }
// })
//捕捉懒加载报错重新加载页面
// router.onError((error) => {
//   const pattern = /Loading chunk (\d)+ failed/g
//   const isChunkLoadFailed = error.message.match(pattern)
//   const targetPath = router.history.pending.fullPath
//   if (isChunkLoadFailed) {
//     router.replace(targetPath)
//   }
// })
function getCookie(name) {
  var strcookie = document.cookie //获取cookie字符串
  var arrcookie = strcookie.split('; ') //分割 //遍历匹配
  for (var i = 0; i < arrcookie.length; i++) {
    var arr = arrcookie[i].split('=')
    if (arr[0] == name) {
      return arr[1]
    }
  }
  return ''
}

// window.app.config.globalProperties.config.productionTip = false
// new Vue({
//   router,
//   store,
//   render: h => h(App)
// }).$mount('#app')

// 防抖函数实现
function debounce(func, wait) {
  let timeout
  return function (...args) {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }
}

// 创建防抖处理函数
const cleanInvisibleCharacters = debounce(function (event) {
  const invisibleCharRegex =
    /[\u0000-\u001F\u007F-\u00A0\u00AD\u034F\u061C\u115F-\u1160\u17B4-\u17B5\u180E\u2000-\u200F\u2028-\u202F\u205F-\u206F\u3000\u2800\u3164\uFEFF\uFFA0\uFFF9-\uFFFB\u200B-\u200D\u202A-\u202E]/g

  if (event.target.tagName === 'INPUT') {
    event.target.value = event.target.value.replace(invisibleCharRegex, '')
    event.target.dispatchEvent(new Event('input'))
  }
}, 300) // 防抖延迟 300ms

// window.app.config.globalProperties = app = Vue.createApp(App)
app.config.globalProperties.routerAppend = (path, pathToAppend) => {
  return path + (path.endsWith('/') ? '' : '/') + pathToAppend
}
app.use(store)
app.use(router)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(ElementPlus,{
  locale: zhCn,//element默认英文将其转换为中文
})
app.mount('#app')

