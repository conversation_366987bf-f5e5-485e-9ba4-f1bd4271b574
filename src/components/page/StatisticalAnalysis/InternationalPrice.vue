<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet OuterFrameList" style="height: 100%">
        <el-form
          :model="form"
          @submit.prevent
          ref="sendjl1"
        >
          <div style="display: flex;">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="form.username"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-button style="margin-left: 16px;" type="primary" plain @click="Query">查询</el-button>
          </div>
          
        </el-form>
        <div class="Mail-table" style="padding-bottom: 40px">
          <el-table
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj"
            style="width: 100%"
          >
            <el-table-column label="地区">
              <template v-slot="scope">{{
                scope.row.area1 + ' (' + scope.row.type1 + ')'
              }}</template>
            </el-table-column>
            <el-table-column label="价格">
              <template v-slot="scope">{{ scope.row.price1 }}</template>
            </el-table-column>
            <el-table-column label="地区">
              <template v-slot="scope">{{
                scope.row.area2
                  ? scope.row.area2 + ' (' + scope.row.type2 + ')'
                  : ''
              }}</template>
            </el-table-column>
            <el-table-column label="价格">
              <template v-slot="scope">{{ scope.row.price2 }}</template>
            </el-table-column>
            <el-table-column label="地区">
              <template v-slot="scope">{{
                scope.row.area3
                  ? scope.row.area3 + ' (' + scope.row.type3 + ')'
                  : ''
              }}</template>
            </el-table-column>
            <el-table-column label="价格">
              <template v-slot="scope">{{ scope.row.price3 }}</template>
            </el-table-column>
            <el-table-column label="地区">
              <template v-slot="scope">{{
                scope.row.area4
                  ? scope.row.area4 + ' (' + scope.row.type4 + ')'
                  : ''
              }}</template>
            </el-table-column>
            <el-table-column label="价格">
              <template v-slot="scope">{{ scope.row.price4 }}</template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InternationalPrice',
  data() {
    return {
      isFirstEnter: false,
      tableDataObj: [],
      form: {
        username: '',
      },
    }
  },
  methods: {
    gettableLIst() {
      window.api.get(window.path.recharge + 'manager/gjPrice', this.form, (res) => {
        let obj = res.data
        var newObj = []
        var item = {}
        var j = 1
        obj.forEach(function (v, i) {
          var k = 'area' + j
          var q = 'price' + j
          var t = 'type' + j
          item[k] = v.area
          item[q] = v.price
          item[t] = v.type
          j++
          if (obj.length < 4 && obj.length - 1 == i) {
            newObj.push(item)
            return false
          }
          if ((i + 1) % 4 === 0) {
            newObj.push(item)
            item = {}
            j = 1
          }
        })
        this.tableDataObj = newObj
        console.log(this.tableDataObj)
      })
    },
    Query() {
      this.gettableLIst()
    },
  },
  watch: {},
  mounted() {
    // this.gettableLIst()
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
