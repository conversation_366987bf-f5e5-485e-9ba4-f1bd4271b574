/**
 * 路由管理工具函数
 * 提供路由模块的辅助功能
 */

/**
 * 动态导入模块路由
 * @param {string} moduleName - 模块名称
 * @returns {Promise<Array>} 路由数组
 */
export async function importModuleRoutes(moduleName) {
  try {
    const module = await import(`../modules/${moduleName}.js`)
    return module.default || []
  } catch (error) {
    console.warn(`Failed to import module routes: ${moduleName}`, error)
    return []
  }
}

/**
 * 批量导入多个模块路由
 * @param {Array<string>} moduleNames - 模块名称数组
 * @returns {Promise<Array>} 合并后的路由数组
 */
export async function importMultipleModules(moduleNames) {
  const promises = moduleNames.map(name => importModuleRoutes(name))
  const results = await Promise.all(promises)
  return results.flat()
}

/**
 * 验证路由配置
 * @param {Array} routes - 路由配置数组
 * @returns {Object} 验证结果
 */
export function validateRoutes(routes) {
  const errors = []
  const warnings = []
  const paths = new Set()
  
  routes.forEach((route, index) => {
    // 检查必需字段
    if (!route.path) {
      errors.push(`Route at index ${index} is missing 'path' property`)
    }
    
    if (!route.component && !route.redirect) {
      errors.push(`Route '${route.path}' is missing 'component' or 'redirect' property`)
    }
    
    // 检查路径重复
    if (paths.has(route.path)) {
      errors.push(`Duplicate path found: '${route.path}'`)
    } else {
      paths.add(route.path)
    }
    
    // 检查 meta 信息
    if (!route.meta) {
      warnings.push(`Route '${route.path}' is missing 'meta' property`)
    } else {
      if (!route.meta.title) {
        warnings.push(`Route '${route.path}' is missing 'meta.title'`)
      }
    }
  })
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    totalRoutes: routes.length,
    uniquePaths: paths.size
  }
}

/**
 * 根据路由路径查找对应的模块
 * @param {string} path - 路由路径
 * @param {Object} moduleRoutes - 所有模块路由的映射
 * @returns {string|null} 模块名称
 */
export function findModuleByPath(path, moduleRoutes) {
  for (const [moduleName, routes] of Object.entries(moduleRoutes)) {
    if (routes.some(route => route.path === path)) {
      return moduleName
    }
  }
  return null
}

/**
 * 获取模块路由统计信息
 * @param {Object} moduleRoutes - 所有模块路由的映射
 * @returns {Object} 统计信息
 */
export function getRouteStatistics(moduleRoutes) {
  const stats = {
    totalModules: 0,
    totalRoutes: 0,
    moduleDetails: {},
    routesByModule: {}
  }
  
  for (const [moduleName, routes] of Object.entries(moduleRoutes)) {
    stats.totalModules++
    stats.totalRoutes += routes.length
    stats.moduleDetails[moduleName] = {
      routeCount: routes.length,
      paths: routes.map(route => route.path)
    }
    stats.routesByModule[moduleName] = routes.length
  }
  
  return stats
}

/**
 * 格式化路由为树形结构（用于调试）
 * @param {Array} routes - 路由数组
 * @returns {string} 格式化的字符串
 */
export function formatRoutesTree(routes) {
  return routes.map(route => {
    const meta = route.meta || {}
    return `${route.path} (${meta.title || 'No title'})`
  }).join('\n')
}

/**
 * 检查路由模块的依赖关系
 * @param {Array} routes - 路由数组
 * @returns {Array} 依赖的组件列表
 */
export function getRouteDependencies(routes) {
  const dependencies = new Set()
  
  routes.forEach(route => {
    if (route.component && typeof route.component === 'function') {
      // 这里可以进一步分析动态导入的路径
      const componentString = route.component.toString()
      const importMatch = componentString.match(/import\(['"`](.+?)['"`]\)/)
      if (importMatch) {
        dependencies.add(importMatch[1])
      }
    }
  })
  
  return Array.from(dependencies)
}

/**
 * 路由性能分析
 * @param {Array} routes - 路由数组
 * @returns {Object} 性能分析结果
 */
export function analyzeRoutePerformance(routes) {
  const analysis = {
    lazyLoadedRoutes: 0,
    staticRoutes: 0,
    routesWithKeepAlive: 0,
    routesWithoutMeta: 0,
    recommendations: []
  }
  
  routes.forEach(route => {
    // 检查懒加载
    if (route.component && typeof route.component === 'function') {
      analysis.lazyLoadedRoutes++
    } else if (route.component) {
      analysis.staticRoutes++
      analysis.recommendations.push(`Consider lazy loading for route: ${route.path}`)
    }
    
    // 检查缓存配置
    if (route.meta && route.meta.keepAlive) {
      analysis.routesWithKeepAlive++
    }
    
    // 检查 meta 信息
    if (!route.meta) {
      analysis.routesWithoutMeta++
      analysis.recommendations.push(`Add meta information for route: ${route.path}`)
    }
  })
  
  return analysis
}

// 开发环境下的调试工具
if (process.env.NODE_ENV === 'development') {
  // 将工具函数暴露到全局，方便调试
  window.routeHelper = {
    validateRoutes,
    findModuleByPath,
    getRouteStatistics,
    formatRoutesTree,
    getRouteDependencies,
    analyzeRoutePerformance
  }
} 