<template>
  <div>
    <div class="heater">
      <div>短链地址<span class="span_r">（以http或https开头）</span>：</div>
      <el-input
        class="input_s"
        v-model="shortLink"
        placeholder="请输入短链"
      ></el-input>
      <el-button style="margin-left: 16px" type="primary" @click="handelClick"
        >提交</el-button
      >
    </div
      v-loading="lodding"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)">
    <div>
      <div class="item" v-for="(item, index) in list" :key="index + item.city">
        <div>{{ item.province + item.city }}</div>
        <div style="display: flex">
          <el-input
            class="input_s"
            v-model="item.htmlMd5"
            placeholder=""
          ></el-input>
          <div style="margin-left: 10px" v-if="item.ip">
            <span>代理ip:</span>
            <span style="color: red">{{ item.ip }}</span>
          </div>
          <div style="margin-left: 10px" v-if="item.expire">
            <span>有效期:</span>
            <span style="color: green">{{ item.expire }}</span>
          </div>
        </div>
      </div>
    </div>
    <div style="margin-top: 10px">
      <div>上海市：</div>
      <iframe
        v-if="url != ''"
        width="100%"
        height="700px"
        :src="shortLink"
        frameborder="0"
      ></iframe>
      <div
        style="width: 100%; height: 700px; border: 2px solid #ccc"
        v-else
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isFirstEnter: false,
      lodding: false,
      shortLink: '',
      url: '',
      a: '',
      cityList: [],
      list: [],
      flag: true,
      allist: [],
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getCity()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getCity()
    })
  },
  methods: {
    getCity() {
      window.api.get(window.path.omcs + 'link/index', this.tabelAlllist, (res) => {
        if (res.code == 200) {
          this.cityList = res.data
          let data = JSON.stringify(this.cityList)
          this.list = JSON.parse(data)
        }
        // console.log(res);
      })
    },
    async handelClick() {
      // try {
      //   // this.flag = false
      //   let that = this
      //   that.url = that.shortLink
      //   that.allist = []
      //   for (let i = 0; i < this.cityList.length; i++) {
      //     let p = new Promise(function (resolve, reject) {
      //       that.$api.post(
      //         that.API.omcs + 'link/proxy/' + that.cityList[i].city,
      //         {
      //           url: that.shortLink,
      //         },
      //         (res) => {
      //           if (res.code == 200) {
      //             let obj = {
      //               htmlMd5: res.data.htmlMd5,
      //               expire: res.data.expire,
      //               ip: res.data.ip,
      //               province: that.cityList[i].province,
      //               city: that.cityList[i].city,
      //             }
      //             resolve(obj)
      //           } else {
      //             that.$message({
      //               type: 'error',
      //               message: res.msg,
      //             })
      //           }
      //         },
      //         (err) => {
      //           let obj = {
      //             htmlMd5: '',
      //             expire: '',
      //             ip: '',
      //             province: '',
      //             city: '',
      //           }
      //           resolve(obj)
      //         }
      //       )
      //     })
      //     that.allist.push(p)
      //   }
      // } catch (error) {
      //   this.flag = true
      //   this.$message({
      //     type: 'error',
      //     message: error,
      //   })
      // }
      try {
        // this.flag = false
        this.lodding = true;
        let that = this;
        that.url = that.shortLink;
        that.allist = [];

        const promises = that.cityList.map((city) => {
          return new Promise((resolve) => {
            that.$api.post(
              `${that.API.omcs}link/proxy/${city.city}`,
              { url: that.shortLink },
              (res) => {
                if (res.code === 200) {
                  resolve({
                    htmlMd5: res.data.htmlMd5,
                    expire: res.data.expire,
                    ip: res.data.ip,
                    province: city.province,
                    city: city.city,
                  });
                } else {
                  that.$message({
                    type: "error",
                    message: res.msg,
                  });
                  resolve({
                    htmlMd5: "",
                    expire: "",
                    ip: "",
                    province: "",
                    city: "",
                  });
                }
              },
              (err) => {
                resolve({
                  htmlMd5: "",
                  expire: "",
                  ip: "",
                  province: "",
                  city: "",
                });
              }
            );
          });
        });

        that.list = await Promise.all(promises);
        this.lodding = false;
      } catch (error) {
        this.flag = true;
        this.lodding = false;
        this.$message({
          type: "error",
          message: error,
        });
      }
    },
  },
  watch: {
    visible: {
      deep: true,
      immediate: true,
      handler(val) {
        this.shortLink = ''
        this.url = ''
        this.list = this.cityList
      },
    },
    // allist(val) {
    //   if (val.length == this.cityList.length) {
    //     Promise.all(val)
    //       .then((res) => {
    //         this.list = res
    //       })
    //       .catch((err) => {
    //         console.log(err)
    //       })
    //   }
    // },
  },
}
</script>

<style lang="less" scoped>
.heater {
  display: flex;
  align-items: center;
}
.span_r {
  color: red;
}
.input_s {
  width: 600px;
}
.item {
  margin-top: 10px;
}
</style>
