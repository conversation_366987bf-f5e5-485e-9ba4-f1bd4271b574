<template>
  <div class="container_left">
    <!-- <div class="crumbs">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              ><i class="el-icon-lx-emoji"></i> 发送明细</el-breadcrumb-item
            >
          </el-breadcrumb>
        </div> -->
    <div class="Top_title" style="display: flex; align-items: center;">
      <span
        style="
          display: flex;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><el-icon><ArrowLeft /></el-icon> 返回</span
      >|
      <span style="margin-left: 10px;">发送明细</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <!-- <el-form-item label="用户名称" label-width="80px" prop="userName">
                <el-input
                  v-model="formInline.userName"
                  placeholder
                  class="input-w"
                ></el-input>
              </el-form-item> -->
          <!-- <el-form-item label="任务名称" label-width="80px" prop="groupName">
                <el-input
                  v-model="formInline.groupName"
                  placeholder
                  class="input-w"
                ></el-input>
              </el-form-item> -->
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query"
          >刷新列表</el-button
        >
        <!-- <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            > -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!--分页-->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->
          <el-table-column label="用户名称">
            <template #default="scope">
              <span>{{ scope.row.userName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发送类型">
            <template #default="scope">
              <span v-if="scope.row.isTiming == '1'">定时发送</span>
              <span v-else>立即发送</span>
            </template>
          </el-table-column>
          <el-table-column label="发送时间">
            <template #default="scope">
              <span>{{ scope.row.sendTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发送号码数">
            <template #default="scope">
              <span>{{ scope.row.mobileNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人">
            <template #default="scope">
              <span>{{ scope.row.createName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template #default="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" width="230" fixed="right">
                <template #default="scope">
                  <el-button
                    type="text"
                    @click="detailsRow(scope.$index, scope.row)"
                    ><i class="el-icon-edit"></i>&nbsp;修改</el-button
                  >
                  <el-button
                    type="text"
                    style="margin: 0px 10px; color: red"
                    @click="delState(scope.$index, scope.row)"
                    ><i class="el-icon-delete"></i>&nbsp;删除</el-button
                  >
                  <el-button
                    type="text"
                    @click="settingState(scope.$index, scope.row)"
                    ><i class="el-icon-setting"></i>&nbsp;号码池设置</el-button
                  >
                </template>
              </el-table-column> -->
        </el-table>
        <!-- <template #default:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
// import TableTem from "../../../../publicComponents/TableTem";
// import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import moment from 'moment'
export default {
  name: 'webtaskgroup',
  data() {
    return {
      isFirstEnter: false,
      //查询表单
      formInline: {
        poolId: '',
        // userName: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        poolId: '',
        // userName: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerwebtaskpool/record',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(val) {
      //   console.log(val);
      //   let selectId = [];
      //   // let wordsCount = [];
      //   for (let i = 0; i < val.length; i++) {
      //     selectId.push(val[i].id);
      //     // wordsCount.push(val[i].wordsCount);
      //   }
      //   this.selectId = selectId.join(","); //批量操作选中id
      //   // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    goBack() {
      this.$router.go(-1)
      //     this.$router.push({
      //     path: "/webtaskgroup",
      //   });
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        const { poolId } = this.$route.query
        this.tabelAlllist.poolId = poolId
        this.formInline.poolId = poolId
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    const { poolId } = this.$route.query
    this.tabelAlllist.poolId = poolId
    this.formInline.poolId = poolId
    this.gettableLIst()
  },
  watch: {},
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
