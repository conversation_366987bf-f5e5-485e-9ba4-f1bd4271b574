<template>
    <div class="container_left action">
        <div class="danger">
            <div class="dangerTitle">批量操作</div>
            <div style="padding: 20px;">
                <div class="actionList">
                    <div :class="item.code == activeName ? 'actionItem active' : 'actionItem'"
                        v-for="(item, index) in actionList" :key="item.code" @click="handleClick(item)">
                        {{ item.title }}
                    </div>
                </div>
            </div>
            <div v-if="itemList.length" class="dangerOperate">
                <div class="operate" v-for="row in itemList">
                    <div @click="goProcessing(row)">
                        <i style="font-size: 40px !important;" :class="'iconfont' + ' ' + row.icon"></i>
                        <div class="title">{{ row.title }}</div>
                    </div>
                </div>
            </div>
            <div v-else>
                <el-empty description="暂无数据"></el-empty>
            </div>

        </div>
    </div>
</template>

<script>
export default {
    name: 'batchaaAction',
    data() {
        return {
            isFirstEnter: false,
            activeName: 'USER',
            oprateList: [
                {
                    title: "用户关停",
                    icon: "icon-Z_guanting",
                    path: "/batchUserShop"
                }
            ],
            actionList: [
            ],
            itemList: [],
            roleId: null,
        }
    },
    created() {
        this.isFirstEnter = true;
        this.$nextTick(() => {
            // this.activeName = 'USER'
            // console.log(this.activeName,'this.activeName');

            this.getActionList();
        });

        // this.getSales()
        // this.getChannel()
    },
    // activated() {
    //     if (this.$route.meta.isBack || !this.isFirstEnter) {
    //         this.$nextTick(() => {

    //             // this.activeName = 'USER'
    //             // console.log(this.activeName,'this.activeName');
    //             this.getActionList();
    //         });
    //     } else {
    //         this.$route.meta.isBack = false;
    //         this.isFirstEnter = false;
    //     }
    // },
    // deactivated() {
    //     this.activeName = 'USER'
    //     this.itemList = this.actionList[0].actions
    // },
    methods: {
        // getRoule(){

        // },
        getActionList() {
            window.api.get(
                window.path.omcs + "operatingaccountinfo/account",
                null,
                (records) => {
                    if (records.code == 200) {
                        this.roleId = records.data.roleId;
                        window.api.get(
                            window.path.omcs + "operating/client/batch/targets",
                            {},
                            (res) => {
                                if (res.code == 200) {
                                    if (records.data.roleId == 3 || records.data.roleId == 4) {
                                        this.actionList = res.data;
                                        this.itemList = res.data[0].actions;
                                    } else {
                                        this.actionList = res.data;
                                        let data = res.data.filter(item => item.code == 'USER')[0];
                                        if (data) {
                                            this.itemList = data.actions.filter(item => item.code == 'USER_BLACKLIST' || item.code == 'USER_WHITELIST'|| item.code == 'USER_IP_RESET');
                                        } else {
                                            this.itemList = [];
                                        }
                                        // this.itemList = res.data.filter(item => item.code == 'USER')[0].actions;
                                    }

                                }
                            }
                        );
                    }
                })

        },
        goProcessing(row) {
            this.$router.push({
                path: row.path,
                // query: {
                //     action: row.code,
                //     title: row.title
                // }
            });
            localStorage.setItem("batchAction", JSON.stringify(row));
        },
        handleClick(tab, event) {
            console.log(tab, event);
        },
        handleClick(item) {
            if (this.roleId == 3) {
                this.itemList = item.actions
            } else {
                this.itemList = item.actions.filter(item => item.code == 'USER_BLACKLIST' || item.code == 'USER_WHITELIST'|| item.code == 'USER_IP_RESET');
            }
            this.activeName = item.code;
        },
    }
}
</script>

<style lang="less" scoped>
.action {
    height: 100%;
}

.danger {

    .dangerTitle {
        height: 60px;
        border: 1px solid #ddd;
        background-color: #f5f8fd;
        font-size: 16px;
        line-height: 60px;
        padding-left: 20px;
        font-weight: bold;
    }

    .dangerOperate {
        display: flex;
        flex-wrap: wrap;
        padding: 20px;

        .operate {
            width: calc(100% / 5 - 10px);
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 120px;
            border-right: 1px solid #eee;

            >div {
                width: 130px;
                display: flex;
                flex-direction: column;
                cursor: pointer;

                i {
                    font-size: 40px;
                    margin-bottom: 20px;
                }
            }

            >div:hover {
                color: #409EFF;
            }
        }

    }
}

.actionList {
    display: flex;
    // justify-content: space-between;
}

.actionItem {
    width: 15%;
    height: 60px;
    line-height: 60px;
    text-align: center;
    border: 1px solid #ddd;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #f5f8fd;

    &:hover {
        background-color: #409EFF;
        color: #fff;
    }
}

.active {
    background-color: #409EFF;
    color: #fff;
}
</style>