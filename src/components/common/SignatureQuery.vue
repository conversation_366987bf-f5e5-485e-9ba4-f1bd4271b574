<template>
  <el-dialog
    title="签名查询工具"
    v-model="dialogVisible"
    width="1200px"
    :before-close="handleClose"
    class="signature-query-dialog"
  >
    <!-- 查询表单 -->
    <div class="query-form-card">
      <el-card shadow="never" class="query-card">
        <template #header>
          <div class="card-header">
            <i class="el-icon-search"></i>
            <span>查询条件</span>
          </div>
        </template>
        <el-form :inline="true" :model="queryForm" class="query-form">
          <el-form-item label="签名" prop="signature">
            <el-input
              ref="signatureInput"
              v-model="queryForm.signature"
              placeholder="请输入签名进行查询"
              clearable
              style="width: 300px;"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery" :loading="loading">
              <i class="el-icon-search"></i>
              查询签名
            </el-button>
            <el-button @click="handleReset">
              <i class="el-icon-refresh"></i>
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <div class="query-tip">
          <i class="el-icon-info"></i>
          <span>请输入签名进行查询，支持模糊匹配</span>
        </div>
      </el-card>
    </div>

    <!-- 卡片式数据展示 -->
    <div class="cards-container" v-loading="loading">
      <!-- 查询提示 -->
      <div v-if="!loading && tableData.length === 0 && !hasSearched" class="search-prompt">
        <el-empty description="请输入签名进行查询">
          <el-button type="primary" @click="$refs.signatureInput?.focus()">开始查询</el-button>
        </el-empty>
      </div>

      <!-- 签名卡片列表 -->
      <div class="signature-cards" v-if="tableData.length > 0">
        <el-card
          v-for="item in tableData"
          :key="item.signatureId"
          shadow="hover"
          class="signature-card"
        >
          <!-- 卡片头部 - 签名和用户信息 -->
          <template #header>
            <div class="signature-header">
              <div class="signature-main-info">
                <div class="signature-title">
                  <i class="el-icon-edit signature-icon"></i>
                  <span class="signature-text">{{ item.signature }}</span>
                  <el-tag
                    :type="getSignatureAttrTag(item.signatureAttr).type"
                    size="small"
                    class="attr-tag"
                  >
                    {{ getSignatureAttrTag(item.signatureAttr).text }}
                  </el-tag>
                </div>
                <div class="user-subject-info">
                  <div class="user-info">
                    <i class="el-icon-user user-icon"></i>
                    <span class="user-label">用户：</span>
                    <span class="user-name">{{ item.consumerName }}</span>
                    <span class="user-id">(ID: {{ item.userId }})</span>
                  </div>
                  <div class="subject-info" v-if="item.companyName">
                    <i class="el-icon-office-building subject-icon"></i>
                    <span class="subject-label">主体：</span>
                    <span class="subject-name">{{ item.companyName }}</span>
                  </div>
                </div>
              </div>
              <div class="realname-status-header">
                <div class="status-title">实名状态</div>
                <div class="operators-status">
                  <div class="operator-status-item">
                    <i class="iconfont icon-yidong operator-icon yd"></i>
                    <el-tag
                      :type="item.ydRealNameStatus == 1 ? 'success' : 'info'"
                      size="small"
                    >
                      {{ item.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                    </el-tag>
                  </div>
                  <div class="operator-status-item">
                    <i class="iconfont icon-liantong operator-icon lt"></i>
                    <el-tag
                      :type="item.ltRealNameStatus == 1 ? 'success' : 'info'"
                      size="small"
                    >
                      {{ item.ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                    </el-tag>
                  </div>
                  <div class="operator-status-item">
                    <i class="iconfont icon-dianxin operator-icon dx"></i>
                    <el-tag
                      :type="item.dxRealNameStatus == 1 ? 'success' : 'info'"
                      size="small"
                    >
                      {{ item.dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 卡片内容 - 签名详细信息 -->
          <div class="signature-content">
            <!-- 基本信息行 -->
            <div class="basic-info-row">
              <div class="info-item">
                <div class="info-label">
                  <i class="el-icon-message"></i>
                  签名类型
                </div>
                <div class="info-value">
                  <el-tag
                    :type="getServiceTypeTag(item.serviceType).type"
                    size="small"
                  >
                    {{ getServiceTypeTag(item.serviceType).text }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item" v-if="item.source">
                <div class="info-label">
                  <i class="el-icon-link"></i>
                  签名来源
                </div>
                <div class="info-value">{{ getSignatureSourceText(item.source) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <i class="el-icon-time"></i>
                  创建时间
                </div>
                <div class="info-value">{{ formatDate(item.createTime) }}</div>
              </div>
            </div>

            <!-- 签名详细信息 - 第二级优先展示 -->
            <div class="signature-detail-info" v-loading="item.detailLoading">
              <div class="detail-title">
                <i class="el-icon-document"></i>
                签名详细信息
              </div>
              <div v-if="item.detailData" class="detail-content-grid">
                <div class="detail-row">
                  <div class="detail-item">
                    <div class="detail-label">企业名称</div>
                    <div class="detail-value">{{ item.detailData.enterpriseName || '-' }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">社会统一信用代码</div>
                    <div class="detail-value">{{ item.detailData.socialCreditCode || '-' }}</div>
                  </div>
                </div>
                <div class="detail-row">
                  <div class="detail-item">
                    <div class="detail-label">企业法人</div>
                    <div class="detail-value">{{ item.detailData.legalPerson || '-' }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">责任人姓名</div>
                    <div class="detail-value">{{ item.detailData.responsiblePersonName || '-' }}</div>
                  </div>
                </div>
                <div class="detail-row">
                  <div class="detail-item">
                    <div class="detail-label">责任人证件号码</div>
                    <div class="detail-value">{{ item.detailData.responsiblePersonId || '-' }}</div>
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">责任人手机号</div>
                    <div class="detail-value">{{ item.detailData.responsiblePersonPhone || '-' }}</div>
                  </div>
                </div>
                <div class="detail-row full-width" v-if="item.detailData.signatureSource">
                  <div class="detail-item">
                    <div class="detail-label">签名来源</div>
                    <div class="detail-value signature-source">
                      <div class="source-category">{{ item.detailData.signatureSource.category || '企业名称' }}</div>
                      <div class="source-description">{{ item.detailData.signatureSource.description || '事业单位、如机关、学校、科研单位、街道社区等' }}</div>
                      <div class="source-types" v-if="item.detailData.signatureSource.types">
                        <div class="source-type" v-for="type in item.detailData.signatureSource.types" :key="type.name">
                          <span class="type-name">{{ type.name }}</span>
                          <span class="type-desc">{{ type.description }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="detail-loading-placeholder">
                <el-button type="text" @click="loadSignatureDetail(item)" :loading="item.detailLoading">
                  <i class="el-icon-view"></i>
                  点击加载详细信息
                </el-button>
              </div>
            </div>

            <!-- 短信示例 -->
            <div class="content-example" v-if="item.contentExample">
              <div class="example-label">
                <i class="el-icon-chat-line-square"></i>
                短信示例
              </div>
              <div class="example-text">{{ item.contentExample }}</div>
            </div>

            <!-- 详细实名信息 -->
            <div class="detailed-realname">
              <div class="realname-title">
                <i class="el-icon-document-checked"></i>
                详细实名信息
              </div>
              <el-row :gutter="16" class="realname-cards">
                <!-- 移动实名详情 -->
                <el-col :span="8">
                  <div class="realname-detail-card yd-card">
                    <div class="realname-card-header">
                      <i class="iconfont icon-yidong"></i>
                      <span>中国移动</span>
                    </div>
                    <div class="realname-card-body">
                      <div class="status-main">
                        <el-tag
                          :type="item.ydRealNameStatus == 1 ? 'success' : 'info'"
                          size="large"
                        >
                          {{ item.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                        </el-tag>
                      </div>
                      <div v-if="item.ydAvalibleType" class="available-types">
                        <div class="types-label">可用类型：</div>
                        <div class="types-tags">
                          <el-tag
                            v-for="type in item.ydAvalibleType.split(',')"
                            :key="type"
                            size="small"
                            type="success"
                            class="type-tag"
                          >
                            {{ type.trim() }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <!-- 联通实名详情 -->
                <el-col :span="8">
                  <div class="realname-detail-card lt-card">
                    <div class="realname-card-header">
                      <i class="iconfont icon-liantong"></i>
                      <span>中国联通</span>
                    </div>
                    <div class="realname-card-body">
                      <div class="status-main">
                        <el-tag
                          :type="item.ltRealNameStatus == 1 ? 'success' : 'info'"
                          size="large"
                        >
                          {{ item.ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                        </el-tag>
                      </div>
                      <div v-if="item.ltAvalibleType" class="available-types">
                        <div class="types-label">可用类型：</div>
                        <div class="types-tags">
                          <el-tag
                            v-for="type in item.ltAvalibleType.split(',')"
                            :key="type"
                            size="small"
                            type="success"
                            class="type-tag"
                          >
                            {{ type.trim() }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <!-- 电信实名详情 -->
                <el-col :span="8">
                  <div class="realname-detail-card dx-card">
                    <div class="realname-card-header">
                      <i class="iconfont icon-dianxin"></i>
                      <span>中国电信</span>
                    </div>
                    <div class="realname-card-body">
                      <div class="status-main">
                        <el-tag
                          :type="item.dxRealNameStatus == 1 ? 'success' : 'info'"
                          size="large"
                        >
                          {{ item.dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                        </el-tag>
                      </div>
                      <div v-if="item.dxAvalibleType" class="available-types">
                        <div class="types-label">可用类型：</div>
                        <div class="types-tags">
                          <el-tag
                            v-for="type in item.dxAvalibleType.split(',')"
                            :key="type"
                            size="small"
                            type="success"
                            class="type-tag"
                          >
                            {{ type.trim() }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 资质图片 -->
            <div class="qualification-images" v-if="item.imgUrl">
              <div class="images-title">
                <i class="el-icon-picture"></i>
                资质图片
              </div>
              <div class="images-gallery">
                <el-image
                  v-for="(img, index) in getImageList(item.imgUrl)"
                  :key="index"
                  :src="img"
                  :preview-src-list="getImageList(item.imgUrl)"
                  fit="cover"
                  class="qualification-image"
                  :preview-teleported="true"
                />
              </div>
            </div>
          </div>

        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && tableData.length === 0 && hasSearched"
        description="未找到相关签名"
        class="empty-state"
      >
        <el-button type="primary" @click="handleReset">重新查询</el-button>
      </el-empty>
    </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="tableData.length > 0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
  </el-dialog>
</template>

<script>
import moment from 'moment'

export default {
  name: 'SignatureQuery',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  data() {
    return {
      loading: false,
      detailLoading: false,
      detailVisible: false,
      queryForm: {
        signature: ''
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      detailData: {},
      hasSearched: false // 是否已经搜索过
    }
  },
  watch: {
    visible(val) {
      if (val) {
        // 打开弹窗时不自动查询，等待用户输入
        this.$nextTick(() => {
          this.$refs.signatureInput?.focus()
        })
      } else {
        this.handleReset()
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      if (!this.queryForm.signature.trim()) {
        this.$message.warning('请输入签名进行查询')
        return
      }

      this.loading = true
      this.hasSearched = true
      const params = {
        current: this.pagination.current,
        size: this.pagination.size,
        signature: this.queryForm.signature.trim()
      }

      window.api.post(
        window.path.omcs + 'consumerSignature/page',
        params,
        (res) => {
          console.log(res,'res');
          this.loading = false
          // 为每个记录添加详情加载状态
          const records = (res.records || []).map(item => ({
            ...item,
            detailLoading: false,
            detailData: null
          }))
          this.tableData = records
          this.pagination.total = res.total || 0

          // 自动加载第一个记录的详细信息
          if (records.length > 0) {
            this.loadSignatureDetail(records[0])
          }
        },
        () => {
          this.loading = false
          this.$message.error('查询失败')
        }
      )
    },

    // 重置
    handleReset() {
      this.queryForm = {
        signature: ''
      }
      this.pagination.current = 1
      this.tableData = []
      this.pagination.total = 0
      this.hasSearched = false
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.handleQuery()
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.handleQuery()
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
    },

    // 获取服务类型标签
    getServiceTypeTag(type) {
      const typeMap = {
        1: { text: '验证码', type: 'primary' },
        2: { text: '通知', type: 'success' },
        3: { text: '营销', type: 'warning' }
      }
      return typeMap[type] || { text: '未知', type: 'info' }
    },

    // 获取签名属性标签
    getSignatureAttrTag(attr) {
      const attrMap = {
        1: { text: '报备签名', type: 'success' },
        2: { text: '自定义签名', type: 'warning' }
      }
      return attrMap[attr] || { text: '未知', type: 'info' }
    },

    // 获取签名来源文本
    getSignatureSourceText(source) {
      const sourceMap = {
        1: '企业名称',
        2: '商标',
        3: 'App',
        4: '小程序'
      }
      return sourceMap[source] || source || '-'
    },

    // 加载签名详细信息
    loadSignatureDetail(item) {
      if (item.detailLoading || item.detailData) return

      item.detailLoading = true

      window.api.get(
        window.path.omcs + "consumerSignature/realName?userId=" + item.userId + "&signatureId=" + item.signatureId,
        {},
        (res) => {
          item.detailLoading = false
          if (res.code === 200 && res.data) {
            // 处理详细信息数据
            item.detailData = {
              enterpriseName: res.data.enterpriseName || res.data.companyName,
              socialCreditCode: res.data.socialCreditCode || res.data.creditCode,
              legalPerson: res.data.legalPerson || res.data.legalRepresentative,
              responsiblePersonName: res.data.responsiblePersonName || res.data.contactName,
              responsiblePersonId: res.data.responsiblePersonId || res.data.contactIdCard,
              responsiblePersonPhone: res.data.responsiblePersonPhone || res.data.contactPhone,
              signatureSource: this.parseSignatureSource(res.data.signatureSource || res.data.source),
              // 保存原始数据以备其他用途
              rawData: res.data
            }
          } else {
            item.detailData = {
              enterpriseName: '-',
              socialCreditCode: '-',
              legalPerson: '-',
              responsiblePersonName: '-',
              responsiblePersonId: '-',
              responsiblePersonPhone: '-',
              signatureSource: null
            }
            this.$message.warning('获取签名详细信息失败')
          }
        },
        () => {
          item.detailLoading = false
          item.detailData = {
            enterpriseName: '-',
            socialCreditCode: '-',
            legalPerson: '-',
            responsiblePersonName: '-',
            responsiblePersonId: '-',
            responsiblePersonPhone: '-',
            signatureSource: null
          }
          this.$message.error('获取签名详细信息失败')
        }
      )
    },

    // 解析签名来源信息
    parseSignatureSource(sourceData) {
      if (!sourceData) return null

      const sourceTypes = [
        {
          name: '商标',
          description: '(须提供商标注册证书图片或在中国商标网的商标查询截图)'
        },
        {
          name: 'App',
          description: '(须提供app在ICP/IP/域名备案管理系统的截图)'
        },
        {
          name: '小程序',
          description: '(须提供小程序在ICP/IP/域名备案管理系统的截图)'
        }
      ]

      return {
        category: '企业名称',
        description: '事业单位、如机关、学校、科研单位、街道社区等',
        types: sourceTypes
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },

    // 获取图片列表
    getImageList(imgUrl) {
      if (!imgUrl) return []
      return imgUrl.split(',').filter(item => item.trim() !== '').map(item => {
        return item.startsWith('http') ? item : window.path.img + item
      })
    }
  }
}
</script>

<style lang="less" scoped>
.signature-query-dialog {
  /* 查询表单卡片样式 */
  .query-form-card {
    margin-bottom: 20px;

    .query-card {
      border: 1px solid #e4e7ed;

      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #303133;

        i {
          color: #409eff;
          font-size: 16px;
        }
      }

      .query-form {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }

    .query-tip {
      margin-top: 12px;
      padding: 8px 12px;
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 4px;
      font-size: 13px;
      color: #409eff;

      i {
        margin-right: 6px;
      }
    }
  }

  /* 卡片容器样式 */
  .cards-container {
    min-height: 400px;

    .search-prompt {
      margin-top: 60px;
      text-align: center;
    }

    .signature-cards {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .empty-state {
      margin-top: 60px;
    }
  }

  /* 签名卡片样式 */
  .signature-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    }

    .signature-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 0;

      .signature-main-info {
        flex: 1;

        .signature-title {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          .signature-icon {
            color: #409eff;
            font-size: 18px;
          }

          .signature-text {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
          }

          .attr-tag {
            margin-left: 8px;
          }
        }

        .user-subject-info {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .user-info, .subject-info {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;

            .user-icon, .subject-icon {
              color: #67c23a;
              font-size: 16px;
            }

            .user-label, .subject-label {
              color: #606266;
              font-weight: 500;
            }

            .user-name, .subject-name {
              color: #303133;
              font-weight: 600;
            }

            .user-id {
              color: #909399;
              font-size: 12px;
              margin-left: 4px;
            }
          }
        }
      }

      .realname-status-header {
        min-width: 300px;

        .status-title {
          font-size: 14px;
          font-weight: 600;
          color: #606266;
          margin-bottom: 8px;
          text-align: center;
        }

        .operators-status {
          display: flex;
          gap: 12px;
          justify-content: center;

          .operator-status-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .operator-icon {
              font-size: 18px;

              &.yd {
                color: #409eff;
              }

              &.lt {
                color: #f56c6c;
              }

              &.dx {
                color: #e6a23c;
              }
            }
          }
        }
      }
    }

    .signature-content {
      .basic-info-row {
        display: flex;
        gap: 24px;
        margin-bottom: 20px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;

        .info-item {
          flex: 1;

          .info-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 600;
            color: #606266;
            margin-bottom: 6px;

            i {
              color: #409eff;
            }
          }

          .info-value {
            font-size: 14px;
            color: #303133;
          }
        }
      }

      .content-example {
        margin-bottom: 20px;
        padding: 16px;
        background: #f0f9ff;
        border-radius: 6px;
        border-left: 4px solid #409eff;

        .example-label {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          font-weight: 600;
          color: #606266;
          margin-bottom: 10px;

          i {
            color: #409eff;
          }
        }

        .example-text {
          font-size: 14px;
          color: #303133;
          line-height: 1.6;
          background: white;
          padding: 12px;
          border-radius: 4px;
          border: 1px solid #e1ecff;
        }
      }

      .signature-detail-info {
        margin-bottom: 20px;

        .detail-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 16px;

          i {
            color: #409eff;
            font-size: 18px;
          }
        }

        .detail-content-grid {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          border: 1px solid #e4e7ed;

          .detail-row {
            display: flex;
            gap: 24px;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            &.full-width {
              .detail-item {
                flex: 1;
              }
            }

            .detail-item {
              flex: 1;

              .detail-label {
                font-size: 13px;
                font-weight: 600;
                color: #606266;
                margin-bottom: 6px;
              }

              .detail-value {
                font-size: 14px;
                color: #303133;
                min-height: 20px;

                &.signature-source {
                  .source-category {
                    font-weight: 600;
                    color: #409eff;
                    margin-bottom: 4px;
                  }

                  .source-description {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 8px;
                  }

                  .source-types {
                    .source-type {
                      display: flex;
                      align-items: center;
                      gap: 8px;
                      margin-bottom: 4px;

                      .type-name {
                        font-weight: 500;
                        color: #606266;
                        min-width: 60px;
                      }

                      .type-desc {
                        font-size: 12px;
                        color: #909399;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        .detail-loading-placeholder {
          text-align: center;
          padding: 20px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px dashed #d9d9d9;

          .el-button {
            color: #409eff;

            &:hover {
              background-color: #ecf5ff;
            }
          }
        }
      }

      .detailed-realname {
        margin-bottom: 20px;

        .realname-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 16px;

          i {
            color: #67c23a;
            font-size: 18px;
          }
        }

        .realname-cards {
          .realname-detail-card {
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            overflow: hidden;
            height: 160px;

            .realname-card-header {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 12px 16px;
              background: #f8f9fa;
              border-bottom: 1px solid #e4e7ed;
              font-weight: 600;
              font-size: 14px;

              i {
                font-size: 18px;
              }
            }

            &.yd-card .realname-card-header {
              background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
              color: #409eff;

              i {
                color: #409eff;
              }
            }

            &.lt-card .realname-card-header {
              background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
              color: #f56c6c;

              i {
                color: #f56c6c;
              }
            }

            &.dx-card .realname-card-header {
              background: linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%);
              color: #e6a23c;

              i {
                color: #e6a23c;
              }
            }

            .realname-card-body {
              padding: 16px;
              text-align: center;

              .status-main {
                margin-bottom: 12px;
              }

              .available-types {
                text-align: left;

                .types-label {
                  font-size: 12px;
                  color: #909399;
                  margin-bottom: 6px;
                }

                .types-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 4px;

                  .type-tag {
                    font-size: 11px;
                  }
                }
              }
            }
          }
        }
      }

      .qualification-images {
        .images-title {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 16px;

          i {
            color: #e6a23c;
            font-size: 18px;
          }
        }

        .images-gallery {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;

          .qualification-image {
            width: 120px;
            height: 120px;
            border-radius: 8px;
            cursor: pointer;
            border: 2px solid #e4e7ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409eff;
              transform: scale(1.05);
            }
          }
        }
      }

        .users-list {
          margin-bottom: 20px;

          .users-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 600;
            color: #606266;
            margin-bottom: 12px;

            i {
              color: #e6a23c;
            }
          }

          .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;

            .user-item {
              padding: 12px;
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                border-color: #409eff;
                background-color: #ecf5ff;
              }

              .user-name {
                font-size: 14px;
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
              }

              .user-meta {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #909399;

                .user-id {
                  color: #409eff;
                }
              }
            }
          }
        }

        .detailed-realname {
          margin-bottom: 20px;

          .realname-label {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            font-weight: 600;
            color: #606266;
            margin-bottom: 16px;

            i {
              color: #67c23a;
            }
          }

          .realname-cards {
            .realname-card {
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              overflow: hidden;

              .realname-card-header {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: #f8f9fa;
                border-bottom: 1px solid #e4e7ed;
                font-weight: 600;

                i {
                  font-size: 16px;
                }
              }

              &.yd-card .realname-card-header i {
                color: #409eff;
              }

              &.lt-card .realname-card-header i {
                color: #f56c6c;
              }

              &.dx-card .realname-card-header i {
                color: #e6a23c;
              }

              .realname-card-content {
                padding: 16px;
                text-align: center;

                .available-types {
                  margin-top: 12px;
                  text-align: left;

                  .types-label {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 6px;
                  }

                  .type-tag {
                    margin-right: 6px;
                    margin-bottom: 4px;
                  }
                }
              }
            }
          }
        }

        .card-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
          padding-top: 16px;
          border-top: 1px solid #e4e7ed;
        }
      }
    }
  }

  /* 分页容器 */
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

/* 响应式设计 */
@media (max-width: 768px) {
  .signature-query-dialog {
    .query-form-card {
      .query-form {
        .el-form-item {
          display: block;
          margin-bottom: 16px;

          .el-input {
            width: 100% !important;
          }
        }
      }
    }

    .signature-card {
      .signature-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .realname-status-header {
          min-width: auto;
          width: 100%;

          .operators-status {
            justify-content: space-around;
          }
        }
      }

      .signature-content {
        .basic-info-row {
          flex-direction: column;
          gap: 16px;
        }

        .signature-detail-info {
          .detail-content-grid {
            .detail-row {
              flex-direction: column;
              gap: 16px;

              .detail-item {
                width: 100%;
              }
            }
          }
        }

        .detailed-realname {
          .realname-cards {
            .el-col {
              width: 100%;
              margin-bottom: 16px;
            }
          }
        }

        .qualification-images {
          .images-gallery {
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
