// 运营管理相关路由
export default [
  {
    path: '/FailurecodeMag',
    component: () => import('@/components/page/OperationMag/FailurecodeMag/FailurecodeMag.vue'),
    meta: { title: '失败代码管理', keepAlive: true, isBack: false },
  },
  {
    path: '/IndustryCategoryMag',
    component: () => import('@/components/page/OperationMag/IndustryCategoryMag/IndustryCategoryMag.vue'),
    meta: { title: '行业类别管理', keepAlive: true, isBack: false },
  },
  {
    path: '/PhoneNumberLoc',
    component: () => import('@/components/page/OperationMag/PhoneNumberLoc/PhoneNumberLoc.vue'),
    meta: { title: '手机号码归属地', keepAlive: true, isBack: false },
  },
  {
    path: '/OperatorConfigurationMag',
    component: () => import('@/components/page/OperationMag/OperatorConfigurationMag/OperatorConfigurationMag.vue'),
    meta: { title: '运营商配置管理', keepAlive: true, isBack: false },
  },
  {
    path: '/alertconfig',
    component: () => import('@/components/page/OperationMag/alertconfig/alertconfig.vue'),
    meta: { title: '短信预警设置', keepAlive: true, isBack: false },
  },
  {
    path: '/FailurecodeMagNote',
    meta: { title: '失败代类别码管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/Operationalsetup/FailurecodeMagNote/FailurecodeMagNote.vue'),
  },
  {
    path: '/CMPPmanagement',
    meta: { title: 'cmpp账号协议管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/Operationalsetup/CMPPmanagement/CMPPmanagement.vue'),
  },
  {
    path: '/cmppConnect',
    meta: { title: 'cmpp连接信息', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/Operationalsetup/CMPPmanagement/cmppConnectList.vue'),
  },
  {
    path: '/KeyWords',
    meta: { title: '营销关键字管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/Words/KeyWords/KeyWords.vue'),
  },
  {
    path: '/SignatureReport',
    meta: { title: '签名报备', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/SignatureReport/SignatureReport.vue'),
  },
  {
    path: '/SignatureNumMag',
    meta: { title: ' 签名扩展号管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/SignatureNumMag/SignatureNumMag.vue'),
  },
  {
    path: '/TemplateMag',
    meta: { title: ' 模板管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/TemplateMag/TemplateMag.vue'),
  },

  {
    path: '/SignatureMag',
    meta: { title: ' 签名管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/SignatureMag/SignatureMag.vue'),
  },
  {
    path: '/SignatureExample',
    meta: { title: ' 签名示例管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/OtherMag/SignatureExample/SignatureExample.vue'),
  },
  {
    path: '/billSetting',
    meta: { title: '统计设置管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/bill/setting.vue'),
  },
  {
    path: '/billParam',
    meta: { title: '统计参数管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/bill/param.vue'),
  },
  {
    path: '/billList',
    meta: { title: '明码导出任务', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/bill/billList.vue'),
  },
  {
    path: '/userList',
    meta: { title: '  用户列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/userList.vue'),
  },
  {
    path: '/OperationManger',
    meta: { title: ' 运营监控管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/Operationalsetup/OperationManger/OperationManger.vue'),
  },
  {
    path: '/whitelist',
    meta: { title: '白名单设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/Operationalsetup/whitelist/whitelist.vue'),
  },
] 