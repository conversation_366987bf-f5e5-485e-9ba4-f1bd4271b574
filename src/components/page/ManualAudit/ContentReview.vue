<template>
  <div>
    <div class="Top_title">
      <span>自定义内容审核</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="82px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" label-width="82px" prop="userName">
            <el-input
              v-model="sensitiveCondition.userName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="排除用户名"
            label-width="82px"
            prop="exceptUserName"
          >
            <el-input
              v-model="sensitiveCondition.exceptUserName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="短信内容" label-width="82px" prop="content">
            <el-input
              v-model="sensitiveCondition.content"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="排除内容"
            label-width="82px"
            prop="exceptContent"
          >
            <el-input
              v-model="sensitiveCondition.exceptContent"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="消息ID" label-width="82px" prop="messageId">
            <el-input
              v-model="sensitiveCondition.messageId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="通道组"
            label-width="82px"
            prop="channelGroupIds"
          >
            <el-select
              v-model="sensitiveCondition.channelGroupIds"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelGroupName"
                :key="index"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提交时间" label-width="82px" prop="time1">
            <el-date-picker
              class="input-w"
              v-model="sensitiveCondition.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande1"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="审核人" label-width="82px"  prop="auditUserName">
                            <el-input v-model="sensitiveCondition.auditUserName" placeholder="" class="input-w"></el-input>
                        </el-form-item> -->
          <!-- <el-form-item label="审核时间" label-width="82px"  prop="time2">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time2"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande2"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div class="boderbottom" style="padding-left: 10px">
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-fun">
        <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">自定义内容审核列表</span> -->
        <el-button type="primary" @click="conditionBy()">条件通过</el-button>
        <el-button type="primary" @click="conditionTurnDown()"
          >条件驳回</el-button
        >
        <el-button type="primary" @click="conditionChannelGroup()"
          >条件切换通道</el-button
        >
        <el-button
          type="primary"
          @click="sensitiveBatchSet()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量通过</el-button
        >
        <el-button
          type="primary"
          @click="sensitiveBatchDel()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量驳回</el-button
        >
        <el-button
          type="primary"
          @click="batchChannelGroup()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量切换通道</el-button
        >
      </div>
      <div class="sensitive-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <template>
          <el-table
            v-loading="tableDataObj.loading2"
            border
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            :data="tableDataObj.tableData"
            @selection-change="handleSelectionChange"
            style="width: 100%"
            height="600"
            :default-expand-all="true"
          >
            <el-table-column type="selection" width="36"> </el-table-column>
            <el-table-column
              type="expand"
              width="50"
              :render-header="renderHeader"
            >
              <template v-slot="scope">
                <el-form
                  label-position="left"
                  class="demo-table-expand"
                  style="padding: 0px 18px 0px 36px"
                >
                  <div class="div_bottom">
                    <el-form-item label="内容 ：">
                      <template>
                        <span>{{
                          scope.row.content.replace(/ /g, '&nbsp;')
                        }}</span>
                        <span style="color: #f56c6c">{{
                          scope.row.prompt
                        }}</span>
                      </template>
                    </el-form-item>
                  </div>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column label="消息ID">
              <template v-slot="scope">
                <span>{{ scope.row.messageId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="用户名">
              <template v-slot="scope">{{ scope.row.userName }}</template>
            </el-table-column>
            <el-table-column label="手机号" width="85px">
              <template v-slot="scope">
                <span
                  style="cursor: pointer; color: #16a589"
                  @click="ViewPhone(scope.row.smsPhone)"
                  >查看手机号</span
                >
              </template>
            </el-table-column>
            <el-table-column label="提交号码数">
              <template v-slot="scope">
                <span>{{ scope.row.phoneCount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="提交时间">
              <template v-slot="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="通道组">
              <template v-slot="scope">
                <span>{{ scope.row.channelGroupName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审核状态" width="90px">
              <template v-slot="scope">
                <span v-if="scope.row.auditStatus == '1'">未审核</span>
                <span v-if="scope.row.auditStatus == '2'" style="color: #16a589"
                  >审核中信息处理中</span
                >
                <span v-if="scope.row.auditStatus == '3'" style="color: #16a589"
                  >审核通过</span
                >
                <span v-if="scope.row.auditStatus == '4'" style="color: #f56c6c"
                  >审核不通过</span
                >
                <span v-if="scope.row.auditStatus == '5'">待复审</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120px">
              <template v-slot="scope">
                <!-- <el-button type="text" v-if="(scope.row.auditStatus=='1'||scope.row.auditStatus=='5')" @click="handleReject(scope.$index, scope.row)"><i class="el-icon-success"></i>&nbsp;通过</el-button> -->
                <!-- <el-button type="text" style="color:orange"  v-if="(scope.row.auditStatus=='1'||scope.row.auditStatus=='5')" @click="handleAdopt(scope.$index, scope.row)"><i class="el-icon-error"></i>&nbsp;驳回</el-button> -->
                <el-button
                  type="text"
                  v-if="
                    (scope.row.auditStatus == '1' ||
                      scope.row.auditStatus == '5') &&
                    scope.row.sendType == 1 &&
                    scope.row.flag == true
                  "
                  @click="addTem(scope.$index, scope.row)"
                  ><el-icon><el-icon-circle-plus /></el-icon
                  >&nbsp;添加模板</el-button
                >
                <!-- <el-button type="text" @click="ChannelGroup(scope.$index, scope.row)"><i class="el-icon-circle-plus"></i>&nbsp;切换通道组</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="sensitiveConObj.currentPage"
              :page-size="sensitiveConObj.pageSize"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            >
            </el-pagination>
          </el-col>
        </template>
      </div>

      <!-- 弹框 （审核 通过 或 不通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="500px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="60px"
          style="padding: 0 34px 0 25px"
        >
          <el-form-item label="原因" prop="auditReason" ref="auditReason">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>

      <!-- 编辑弹框 -->
      <el-dialog
        title="新增短信模板"
        v-model="TemDialogVisible"
        width="620px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <el-form
          :model="form"
          :rules="temFormRules"
          label-width="80px"
          style="padding: 10px 30px"
          ref="temForm"
        >
          <el-form-item
            label="用户名"
            prop="temUserName"
            :rules="
              filter_rules({
                required: true,
                type: 'userName',
                message: '请输入用户名',
              })
            "
          >
            <el-input v-model="form.temUserName" disabled></el-input>
          </el-form-item>
          <el-form-item label="模板名称" prop="temName">
            <el-input v-model="form.temName"></el-input>
          </el-form-item>
          <el-form-item label="模板类型" prop="temType">
            <el-radio-group v-model="form.temType">
              <el-radio label="1">验证码</el-radio>
              <el-radio label="2">行业通知</el-radio>
              <el-radio label="3">会员营销</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="模板内容" prop="initialContent">
            <el-input
              v-model="form.initialContent"
              disabled
              type="textarea"
              resize="none"
              rows="4"
            ></el-input>
            <div class="tem-be-careful" style="">
              <div class="tem-font">
                已输入<span> {{ wordCount1 }} </span>个字，最多可输入450个字
              </div>
              <div class="tem-font">
                当前模板 预计发送条数约为<span> {{ numTextMsg1 }} </span>条短信
              </div>
            </div>
          </el-form-item>
          <el-form-item label="申请说明" prop="remark">
            <el-input
              placeholder="用于我公司app用户注册时发送验证码"
              type="textarea"
              rows="5"
              resize="none"
              v-model="form.remark"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="addTemOk()">确 定</el-button>
            <el-button @click="TemDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 编辑弹框 -->
      <el-dialog
        title="手机号码"
        v-model="ViewPhoneNumber"
        width="620px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <div
          style="
            width: 100%;
            white-space: normal;
            word-break: break-all;
            overflow: auto;
          "
        >
          {{ phoneNumber }}
        </div>
      </el-dialog>
      <!-- 切换通道 -->
      <el-dialog
        title="切换通道组"
        v-model="SwitchChannelGroup"
        width="530px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <el-form
          :model="SwitchChannelForm"
          :rules="SwitchChannelRules"
          label-width="100px"
          style="padding: 10px 30px"
          ref="refForm"
        >
          <el-form-item label="扩展号" prop="updateExt">
            <el-input
              v-model="SwitchChannelForm.updateExt"
              class="input-w"
            ></el-input>
            <span style="color: red; font-size: 12px">不可随意填写</span>
          </el-form-item>
          <el-form-item label="选择通道" prop="aisle">
            <el-select
              v-model="SwitchChannelForm.aisle"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="人工通道组" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道组名称" prop="channelGroupId">
            <el-select
              v-model="SwitchChannelForm.channelGroupId"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelGroupName"
                :key="index"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="移动" prop="channelYx">
            <el-select
              v-model="SwitchChannelForm.channelYx"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelYx"
                :key="index"
                :label="'移动通道:' + item[1] + ' 单价:' + item[2]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联通" prop="channelLt">
            <el-select
              v-model="SwitchChannelForm.channelLt"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelLt"
                :key="index"
                :label="'联通通道:' + item[1] + ' 单价:' + item[2]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="电信" prop="channelDx">
            <el-select
              v-model="SwitchChannelForm.channelDx"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in channelDx"
                :key="index"
                :label="'电信通道:' + item[1] + ' 单价:' + item[2]"
                :value="item[0]"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="confirmSwitchChannel()"
              >确 定</el-button
            >
            <el-button @click="SwitchChannelGroup = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import templateRule from './TemplateRule'
import editDiv from './editDiv'
export default {
  components: {
    TableTem,
    templateRule,
    editDiv,
    ElIconCirclePlus,
  },
  name: 'SensitiveWordsAudit',
  data() {
    var updateExt = (rule, value, callback) => {
      if (value == '' || value == null) {
        callback()
      } else {
        if (!/^\d{2,15}$/.test(value)) {
          return callback(new Error('请输入2-15位扩展号'))
        } else {
          callback()
        }
      }
    }
    return {
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      title: '',
      zk: 1,
      remarkshow: false,
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          idArr: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: '输入驳回原因', trigger: 'blur' },
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      // 查询所有通道组
      channelGroupName: [],
      // 储存手机号
      phoneNumber: '',
      ViewPhoneNumber: false,
      // 切换通道组
      SwitchChannelGroup: false,
      // 切换组状态
      SwitchingState: '',
      idArrs: [],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        userName: '',
        exceptUserName: '',
        time1: [],
        content: '',
        exceptContent: '',
        beginTime1: '',
        endTime1: '',
        messageId: '',
        channelGroupIds: '',
        currentPage: 1,
        pageSize: 20,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        userName: '',
        exceptUserName: '',
        time1: [],
        content: '',
        exceptContent: '',
        beginTime1: '',
        endTime1: '',
        messageId: '',
        channelGroupIds: '',
        currentPage: 1,
        pageSize: 20,
      },
      // 切换通道组数据
      SwitchChannelForm: {
        updateExt: '',
        channelGroupId: '',
        channelYx: '',
        channelLt: '',
        channelDx: '',
      },
      // 移动
      channelYx: [],
      // 联通
      channelLt: [],
      // 电信
      channelDx: [],
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      status: '展开',
      TemDialogVisible: false, //---添加模板的弹窗
      form: {
        temUserName: '',
        temName: '',
        temType: '',
        initialContent: '',
        remark: '',
        temFormat: '2',
      },
      temFormRules: {
        //添加模板内容的验证
        temName: [
          { required: true, message: '请输入模板名', trigger: 'blur' },
          {
            pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+$/,
            message: '不允许输入空格等特殊符号',
          },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        remark: [
          {
            min: 1,
            max: 200,
            message: '长度在 1 到 200 个字符',
            trigger: 'blur',
          },
        ],
        temType: [
          { required: true, message: '请选择模板类型', trigger: 'change' },
        ],
      },
      // 切换通道组验证
      SwitchChannelRules: {
        updateExt: [{ required: false, validator: updateExt, trigger: 'blur' }],
        aisle: [{ required: true, message: '请选择通道', trigger: 'change' }],
        channelGroupId: [
          { required: true, message: '请选择通道组名称', trigger: 'change' },
        ],
        channelYx: [
          { required: true, message: '请选择移动通道', trigger: 'change' },
        ],
        channelLt: [
          { required: true, message: '请选择联通通道', trigger: 'change' },
        ],
        channelDx: [
          { required: true, message: '请选择电信通道', trigger: 'change' },
        ],
      },
      myTemPageTotal: 0, //总条数
      tagColors: [
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
      ],
    }
  },
  computed: {
    numTextMsg1: function () {
      //1短信条数
      let numMsg = 0
      //字数和短信条数的显示
      if (this.wordCount1 == 0) {
        numMsg = 0
      } else if (
        parseInt(this.wordCount1) <= 70 &&
        parseInt(this.wordCount1) > 0
      ) {
        numMsg = 1
      } else {
        numMsg = Math.floor(parseInt(this.wordCount1) / 67) + 1
      }
      return numMsg
    },
    wordCount1: function () {
      //1字数
      console.log(this.form.initialContent)
      return this.form.initialContent.length
    },
  },
  methods: {
    // 遮罩层
    openFullScreen2() {
      this.loading = this.$loading({
        lock: true,
        text: '正在操作,请等待...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    renderHeader(h) {
      //表头事件
      return (
        <div style="margin:0;padding:0;line-height: 23px;width:inherit;height: 23px;">
          <el-button
            nativeOnClick={this.expandOpen}
            style="plain:false;background:none;border:none;margin:0;padding:0;list-style:none;"
          >
            {this.status}
          </el-button>
        </div>
      )
    },
    expandOpen() {
      var arr = document.querySelectorAll('.el-table__expand-icon')
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].className.length < 25) {
          arr[i].onclick = function () {}
        } else {
          arr[i].onclick = function () {}
        }
      }
      if (this.status == '展开') {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length < 25) {
            arr[i].click()
          }
        }
        this.status = '折叠'
      } else {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length > 25) {
            arr[i].click()
          }
        }
        this.status = '展开'
      }
    },
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'serviceAuditCustomSms/page',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.total = res.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    sensitiveQuery() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime1 = ''
      this.sensitiveCondition.endTime1 = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime1 = this.moment(val[0]).format(
          'YYYY-MM-DD'
        )
        this.sensitiveCondition.endTime1 = this.moment(val[1]).format(
          'YYYY-MM-DD'
        )
      } else {
        this.sensitiveCondition.beginTime1 = ''
        this.sensitiveCondition.endTime1 = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
    },
    //切换通道组
    ChannelGroup(index, row) {
      this.SwitchingState = '1'
      this.idArrs[0] = row.id
      this.SwitchChannelGroup = true
    },
    //批量切换通道组
    batchChannelGroup() {
      this.SwitchingState = '2'
      this.SwitchChannelGroup = true
    },
    // 条件切换通道
    conditionChannelGroup() {
      if (this.sensitiveCondition.userName) {
        this.SwitchingState = '3'
        this.SwitchChannelGroup = true
      } else {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '请先填写用户名条件',
        })
      }
    },
    // 确认切换通道
    confirmSwitchChannel() {
      this.$refs.refForm.validate((valid) => {
        if (valid) {
          if (this.SwitchingState == '1') {
            // 单条切换通道
            this.SwitchChannelForm.idArr = this.idArrs
            this.$confirms.confirmation(
              'post',
              '确认切换通道组？',
              window.path.omcs + 'serviceAuditCustomSms/batchAudit?type=1',
              this.SwitchChannelForm,
              (res) => {
                this.SwitchChannelGroup = false //关闭弹窗
                this.getTableDtate()
              }
            )
          } else if (this.SwitchingState == '2') {
            // 批量切换通道
            this.SwitchChannelForm.idArr = this.remarkObj.formData.idArr
            this.$confirm('确认切换通道组？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.openFullScreen2()
                window.api.post(
                  window.path.omcs + 'serviceAuditCustomSms/batchAudit?type=1',
                  this.SwitchChannelForm,
                  (res) => {
                    this.SwitchChannelGroup = false //关闭弹窗
                    this.getTableDtate()
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        duration: '2000',
                        message: res.msg,
                      })
                    }
                    this.loading.close()
                  }
                )
              })
              .catch(() => {
                // this.operationFlag = true
                this.$message({
                  type: 'info',
                  duration: '2000',
                  message: '已取消操作!',
                })
              })
          } else if (this.SwitchingState == '3') {
            // 条件切换通道
            let objAss = Object.assign(
              {},
              this.SwitchChannelForm,
              this.sensitiveCondition
            )
            objAss.idArr = null
            this.$confirms.confirmation(
              'post',
              '确认切换通道组？',
              window.path.omcs + 'serviceAuditCustomSms/conditionAudit?type=1',
              objAss,
              (res) => {
                this.SwitchChannelGroup = false //关闭弹窗
                this.getTableDtate()
              }
            )
          }
        }
      })
    },
    // 条件通过
    conditionBy() {
      if (this.sensitiveCondition.userName) {
        this.$confirms.confirmation(
          'post',
          '确认条件通过？',
          window.path.omcs + 'serviceAuditCustomSms/conditionAudit?type=1',
          this.sensitiveCondition,
          (res) => {
            this.getTableDtate()
          }
        )
      } else {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '请先填写用户名条件',
        })
      }
    },
    // 条件驳回
    conditionTurnDown() {
      if (this.sensitiveCondition.userName) {
        this.title = '条件驳回备注'
        this.remarkshow = true
      } else {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '请先填写用户名条件',
        })
      }
    },
    //查看手机号
    ViewPhone(row) {
      this.phoneNumber = row
      this.ViewPhoneNumber = true
    },
    //短信内容的签名格式是否正确，或者是否有签名
    // checkSignature(value) {
    //     let ret = value;
    //     let beg = ret.indexOf('【');
    //     let end = ret.indexOf('】');
    //     let lastbeg = ret.lastIndexOf('【');
    //     let lastend = ret.lastIndexOf('】');
    //     let valLength = ret.length;
    //     if(beg > -1 && end > -1 && end > beg && beg == 0 ){
    //         if( beg == 0 && beg < 50 && end >2){
    //             ret= ret.slice(end+1,ret.length);
    //         }else{
    //             ret = '';
    //         }
    //     }
    //     if(lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength -1){
    //         if (lastend == (valLength -1) && lastend - lastbeg < 49 && lastend - lastbeg > 2){
    //             ret= ret.slice(0,lastbeg);
    //         }else{
    //             ret = '';
    //         }
    //     }
    //     return ret;
    // },
    //添加模板
    addTem: function (index, row) {
      this.form.temUserName = row.userName
      // this.form.initialContent = this.checkSignature(row.content) ;
      window.api.post(
        window.path.omcs + 'serviceaduitsms/getTemplateContent',
        { temContent: row.content },
        (res) => {
          this.form.initialContent = res.msg
        }
      )
      this.TemDialogVisible = true
    },
    //添加模板--确定
    addTemOk(val) {
      this.$refs.temForm.validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + 'serviceaduitsms/validateUserTempExist',
            { temName: this.form.temName, temUserName: this.form.temUserName },
            (res) => {
              if (res.code == 400) {
                this.$message({
                  message: '模板名称已存在，需重新设置！',
                  type: 'warning',
                })
              } else {
                this.$confirms.confirmation(
                  'post',
                  '确认新增模板？',
                  window.path.omcs + 'serviceaduitsms/addTemplate',
                  this.form,
                  (res) => {
                    this.TemDialogVisible = false //关闭弹窗
                    this.getTableDtate()
                  }
                )
              }
            }
          )
        } else {
          return false
        }
      })
    },

    //驳回
    handleAdopt(index, row) {
      this.title = '驳回备注'
      this.remarkshow = true
      this.idArrs.push(row.id)
      this.$nextTick(() => {
        this.$refs.remarkObjs.resetFields()
        this.remarkObj.formData.auditReason = row.prompt
      })
    },
    //批量驳回
    sensitiveBatchDel() {
      this.title = '批量驳回备注'
      // let status = true;
      //判断选项中是否有审核过的状态
      // for(let i =0; i< this.auditStatus.length; i++){
      //     if(this.auditStatus[i] != '1'){
      //         status = false;
      //         break;
      //     }
      // }
      // if(status){
      this.remarkshow = true
      // }else{
      //     this.$message({
      //         message: '选项中包含已审核项，需重新选择待审核项！',
      //         type: 'warning'
      //     });
      // }
    },
    //通过
    handleReject(index, row) {
      this.$confirms.confirmation(
        'post',
        '此操作不可逆，是否继续?',
        window.path.omcs + 'serviceAuditCustomSms/batchAudit?type=1',
        { idArr: [row.id] },
        (res) => {
          this.idArrs = [] //置空ID
          this.remarkshow = false
          this.getTableDtate()
        }
      )
    },
    //批量通过
    sensitiveBatchSet() {
      // this.title = '批量审核通过备注';
      let status = true
      //判断选项中是否有审核过的状态
      // for(let i =0; i< this.auditStatus.length; i++){
      //     if(this.auditStatus[i] != '1'){
      //         status = false;
      //         break;
      //     }
      // }
      if (status) {
        // this.operationFlag = false; //操作进行中（按钮的显示）
        this.$confirm('此操作不可逆，是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning',
        })
          .then(() => {
            this.openFullScreen2()
            window.api.post(
              window.path.omcs + 'serviceAuditCustomSms/batchAudit?type=1',
              { idArr: this.remarkObj.formData.idArr },
              (res) => {
                this.remarkObj.formData.idArr = [] //置空ID
                this.remarkshow = false
                this.getTableDtate()
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    duration: '2000',
                    message: '操作成功!',
                  })
                } else {
                  this.$message({
                    type: 'error',
                    duration: '2000',
                    message: res.msg,
                  })
                }
                this.loading.close()
              }
            )
          })
          .catch(() => {
            // this.operationFlag = true
            this.$message({
              type: 'info',
              duration: '2000',
              message: '已取消操作!',
            })
          })
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //审核通过（或者驳回） 以及批量操作（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let aa = {}
          if (this.title == '驳回备注') {
            Object.assign(aa, this.remarkObj.formData)
            aa.idArr = this.idArrs
            this.$confirms.confirmation(
              'post',
              '此操作不可逆，是否继续?',
              window.path.omcs + 'serviceAuditCustomSms/batchAudit?type=2',
              aa,
              (res) => {
                this.idArrs = []
                this.remarkshow = false
                this.getTableDtate()
              }
            )
          } else if (this.title == '批量驳回备注') {
            this.operationFlag = false //操作进行中（按钮的显示）
            Object.assign(aa, this.remarkObj.formData)
            this.$confirm('此操作不可逆，是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                window.api.post(
                  window.path.omcs + 'serviceAuditCustomSms/batchAudit?type=2',
                  aa,
                  (res) => {
                    this.remarkObj.formData.idArr = [] //置空ID
                    this.remarkshow = false
                    this.getTableDtate()
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        duration: '2000',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.operationFlag = true
                this.$message({
                  type: 'info',
                  duration: '2000',
                  message: '已取消操作!',
                })
              })
            // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs+'serviceaduitsms/auditNotPass',aa,res =>{
            //     this.remarkObj.formData.idArr=[]; //置空ID
            //     this.remarkshow = false;
            //     this.getTableDtate();
            // });
          } else if (this.title == '条件驳回备注') {
            this.sensitiveCondition.auditReason =
              this.remarkObj.formData.auditReason
            this.$confirms.confirmation(
              'post',
              '确认条件驳回？',
              window.path.omcs + 'serviceAuditCustomSms/conditionAudit?type=2',
              this.sensitiveCondition,
              (res) => {
                this.remarkshow = false
              }
            )
          }
        } else {
          return false
        }
      })
    },

    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let auditStatus = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
        //批量操作列表选中项的审核状态
        auditStatus.push(val[i].auditStatus)
      }
      this.auditStatus = auditStatus //选中项的审核状态
      this.remarkObj.formData.idArr = selectId //批量通过，不通过的id数组
    },
  },
  created() {
    window.api.post(
      window.path.omcs + 'serviceaduitsms/getAllSmsChannelGroup',
      {},
      (res) => {
        this.channelGroupName = res.data
      }
    )
  },
  watch: {
    /**----------监听添加模板弹窗是否关闭------------- */
    TemDialogVisible(newVal, oldVal) {
      if (newVal == false) {
        Object.assign(this.form, this.formEmpty)
        this.$refs.temForm.resetFields()
      }
    },
    //监听查询框对象的变化
    sensitiveConObj: {
      handler(val) {
        this.getTableDtate()
      },
      deep: true,
      immediate: true,
    },
    //监听设置标签弹框是否关闭
    setLabelDialog(val) {
      if (this.$refs.setLabel) {
        if (val == false) {
          this.$refs.setLabel.resetFields()
        }
      }
    },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true
      } else {
        this.disabledinput = false
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.operationFlag = true
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.idArr = [] //置空ID
          this.idArrs = [] //置空ID
          this.getTableDtate()
        }
      }
    },
    // 监听切换通道组是否关闭
    SwitchChannelGroup(val) {
      if (this.$refs.refForm) {
        if (val == false) {
          this.$refs.refForm.resetFields()
          this.idArrs = []
          this.SwitchChannelForm.idArr = []
        }
      }
    },
    // 监听通道组改变
    'SwitchChannelForm.channelGroupId'(val) {
      this.SwitchChannelForm.channelYx = ''
      this.SwitchChannelForm.channelLt = ''
      this.SwitchChannelForm.channelDx = ''
      if (val) {
        window.api.post(
          window.path.omcs + 'serviceaduitsms/getAllSmsChannelGroup/' + val,
          {},
          (res) => {
            this.channelYx = res.listYD2
            this.channelLt = res.listLT2
            this.channelDx = res.listDX2
          }
        )
      } else {
        this.channelYx = []
        this.channelLt = []
        this.channelDx = []
      }
    },
  },
}
</script>

<style scoped>
.tem-font {
  display: inline-block;
  font-size: 12px;
}
.tem-font span {
  color: red;
}
.tem-be-careful {
  font-size: 12px;
  color: #999;
  line-height: 20px;
  margin: 10px 0;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.div_bottom > div {
  margin-bottom: 0px !important;
}
</style>

<style>
.myTems .el-dialog__body {
  padding-bottom: 50px !important;
}
.el-table--small th {
  background: #f5f5f5;
}

.text-adaNum {
  color: #16a589;
  cursor: pointer;
}
.TemDialog .el-tabs__item {
  height: 32px;
  line-height: 32px;
}
.TemDialog .el-tabs__header {
  margin: 0px;
}
.TemDialog .el-tabs__content {
  overflow: inherit;
}
</style>
