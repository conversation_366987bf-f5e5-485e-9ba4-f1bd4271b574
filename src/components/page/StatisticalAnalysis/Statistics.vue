<template>
  <div>
    <div class="Top_title">发送统计报表</div>
    <div class="fillet Statistics-box">
      <span>数据总览</span>
      <el-select v-model="flag" placeholder="请选择">
        <el-option label="今日" value="4"></el-option>
        <el-option label="近7天" value="1"></el-option>
        <el-option label="近30天" value="2"></el-option>
      </el-select>

      <div style="margin-top: 20px">
        <div
          v-for="(item, index) in StatisticsNum"
          :key="index"
          class="statistic-box"
        >
          <div class="statistics-number-box">
            <div
              :class="item.staClass"
              style="
                width: 60px;
                height: 60px;
                border-radius: 30px;
                text-align: center;
              "
            >
              <img :src="item.stasrc" alt="" />
            </div>
            <div class="statistics-numbers-box">
              <div style="padding-bottom: 5px; font-size: 14px">
                {{ item.statitle }}
              </div>
              <div style="padding-bottom: 5px; font-size: 26px; color: #333">
                {{ item.staNum }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <chart id="test" :option="option" height="300px"></chart>
      </div>
    </div>
    <!-- 通道发送，短信发送，单个用户发送 -->
    <!-- <div class="fillet Statistics-box" style="margin-top:10px;">
                <el-tabs v-model="activeName2" type="card" @tab-click="handleClick">
                    <el-tab-pane label="通道发送统计" name="PassagewaySend"></el-tab-pane>
                    <el-tab-pane label="用户发送统计" name="SendReportform"></el-tab-pane>
                    <el-tab-pane label="用户发送明细" name="SendRecord"></el-tab-pane>
                </el-tabs>
                <div>
                    <component v-bind:is="currentTabComponent"></component>
                </div>
            </div> -->
  </div>
</template>

<script>
import Chart from '@/components/publicComponents/Chart.vue'
import PassagewaySend from './components/PassagewaySend.vue'
import SendReportform from './components/SendReportform.vue'
import SendRecord from './components/SendRecord.vue'
export default {
  name: 'Statistics',
  components: {
    Chart,
    PassagewaySend,
    SendReportform,
    SendRecord,
  },
  data() {
    return {
      activeName2: 'PassagewaySend',
      currentTabComponent: 'PassagewaySend',
      flag: '4',
      loading: false,
      StatisticsNum: [
        {
          stasrc: require('@/assets/images/u1178.png'),
          statitle: '',
          staNum: '',
          staClass: 'bag-color1',
        },
        {
          stasrc: require('@/assets/images/u1198.png'),
          statitle: '',
          staNum: '',
          staClass: 'bag-color2',
        },
        {
          stasrc: require('@/assets/images/u99681.png'),
          statitle: '',
          staNum: '',
          staClass: 'bag-color3',
        },
      ],
      //折线图数据
      option: {
        color: ['#FF9900', '#49A9EE'],
        grid: {
          left: '1%',
          right: '3%',
          bottom: '1%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#EBEBEB', //坐标轴线的颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: '#666', //坐标值得具体的颜色
            },
          },
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#EBEBEB', //坐标轴线的颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: '#666', //坐标值得具体的颜色
            },
          },
        },
        series: [
          {
            name: '短信发送量',
            type: 'line',
            data: [],
            lineStyle: {
              normal: {
                width: 3,
                shadowColor: 'rgba(0,0,0,0.4)',
                shadowBlur: 6,
                shadowOffsetY: 10,
              },
            },
            markPoint: {
              //最大值 最小值
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' },
              ],
            },
            markLine: {
              //平均值
              data: [{ type: 'average', name: '平均值' }],
              label: {
                position: 'middle', //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
              },
            },
            smooth: true,
          },
        ],
      },
    }
  },
  methods: {
    //切换 通道发送
    handleClick(tab) {
      this.currentTabComponent = tab.name
    },
    //获取今天的统计图数据
    getStatisticsDate() {
      this.loading = true
      this.option.xAxis.data = []
      this.option.series[0].data = []
      window.api.post(
        window.path.omcs + 'operatingStatisticalAnalysis/sentOneDay',
        {},
        (res) => {
          this.setDate(res)
        }
      )
    },
    //获取本周，当月，当季度的统计数据
    getStatisticsDate1() {
      this.option.xAxis.data = []
      this.option.series[0].data = []
      window.api.post(
        window.path.omcs + 'operatingStatisticalAnalysis/sentOther/' + this.flag,
        {},
        (res) => {
          this.setDate(res)
        }
      )
    },
    setDate(res) {
      this.StatisticsNum[0].staNum = res.sendTotalCount
      this.StatisticsNum[1].staNum = res.sendSuccessCount
      this.StatisticsNum[2].staNum = res.sendFailCount
      this.option.xAxis.data = res.totalCountList[0]
      this.option.series[0].data = res.totalCountList[1]
    },
  },
  watch: {
    //监听时间的选择（今天4，本周1，当月2，当季度3）
    flag: {
      handler(val) {
        if (val == '4') {
          this.StatisticsNum[0].statitle = '今日总发送量'
          this.StatisticsNum[1].statitle = '今日发送成功量'
          this.StatisticsNum[2].statitle = '今日总发送失败量'
        } else if (val == '1') {
          this.StatisticsNum[0].statitle = '近7天总发送量'
          this.StatisticsNum[1].statitle = '近7天发送成功量'
          this.StatisticsNum[2].statitle = '近7天总发送失败量'
        } else if (val == '2') {
          this.StatisticsNum[0].statitle = '近30天总发送量'
          this.StatisticsNum[1].statitle = '近30天发送成功量'
          this.StatisticsNum[2].statitle = '近30天总发送失败量'
        }
        if (val != '4') {
          this.getStatisticsDate1()
        } else {
          this.getStatisticsDate()
        }
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.statistics-number-box {
  border: 1px solid #eee;
  border-radius: 3px;
  display: flex;
  padding: 18px;
  background: #f8f8f8;
}
.statistics-numbers-box {
  width: calc(100% - 80px);
  margin-left: 20px;
}
.bag-color1 {
  background: #49a9ee;
}
.bag-color2 {
  background: #98d87d;
}
.bag-color3 {
  background: #f3857c;
}
.bag-color4 {
  background: #ffd86e;
}
.bag-color1 > img {
  padding-top: 15px;
}
.bag-color2 > img {
  padding-top: 15px;
}
.bag-color3 > img {
  padding-top: 15px;
}
.bag-color4 > img {
  padding-top: 17px;
}
.statistic-box {
  display: inline-block;
  width: calc(33.33333333% - 14px);
  margin-right: 14px;
  font-size: 0px;
  overflow: hidden;
}
</style>
