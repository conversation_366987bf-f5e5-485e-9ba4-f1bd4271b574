<template>
  <div id="ChannelStyle">
    <el-dialog
      title="通道查看"
      v-model="ChannelDialog"
      :close-on-click-modal="false"
      width="880px"
      class="addUserDialog"
    >
      <el-form :model="formData" ref="addUserStep1Form" label-width="140px">
        <div style="width: 50%; float: left">
          <el-form-item label="通道编号">
            <el-input
              class="input-w"
              readonly
              v-model="formData.channelCode"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道名称" prop="channelName">
            <el-input
              class="input-w"
              readonly
              v-model="formData.channelName"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道号码" prop="channelNumber">
            <el-input
              class="input-w"
              readonly
              v-model="formData.channelNumber"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道单价" prop="channelUnitPrice">
            <el-input
              class="input-w"
              placeholder="单位：元"
              readonly
              v-model="formData.channelUnitPrice"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道区域" prop="channelFlag">
            <el-select
              v-model="formData.channelFlag"
              class="input-w"
              placeholder="请选择"
              disabled
            >
              <el-option label="全网" value="0"></el-option>
              <el-option label="省网" value="1"></el-option>
              <el-option label="全网+省网" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="接口方式" prop="interfaceMode">
            <el-select
              class="input-w"
              disabled
              v-model="formData.interfaceMode"
              placeholder="请选择"
            >
              <el-option label="http接口" value="1"></el-option>
              <el-option label="直连协议" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运营商名称" prop="operatorId">
            <el-select
              class="input-w"
              v-model="formData.operatorId"
              placeholder="请选择"
              disabled
            >
              <el-option
                v-for="(item, index) in operatorNames"
                :label="item.operatorName"
                :value="item.operatorId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="是否需要提前报备" prop="madeReported">
                  <el-select
                    class="input-w"
                    v-model="formData.madeReported"
                    placeholder="请选择"
                    disabled 
                  >
                    <el-option label="不报可发" value="1"></el-option>
                    <el-option label="签名报备可发" value="2"></el-option>
                    <el-option label="签名扩展报备可发" value="3"></el-option>
                  </el-select>
                </el-form-item> -->
          <!-- <el-form-item label="协议推送端口" prop="port">
                  <el-input class="input-w"  readonly v-model="formData.port"></el-input>
                </el-form-item> -->
          <el-form-item label="内容签名处理：" prop="removeSign">
            <el-select
              class="input-w"
              v-model="formData.removeSign"
              placeholder="请选择"
              disabled
            >
              <el-option label="抹除签名" value="true"></el-option>
              <el-option label="不抹除" value="false"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择新旧网关" prop="pactType">
            <el-radio-group disabled v-model="formData.pactType">
              <el-radio value="1">旧网关</el-radio>
              <el-radio value="2">新网关</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- <el-form-item
                  label="验证码流速占比"
                  prop="velocityRateYzm"
                  v-if="formData.channelType == '1'"
                >
                  <el-select
                    class="input-w"
                    v-model="formData.velocityRateYzm"
                    placeholder="请选择"
                    disabled 
                  >
                    <el-option label="0" value="0"></el-option>
                    <el-option label="0.1" value="0.1"></el-option>
                    <el-option label="0.2" value="0.2"></el-option>
                    <el-option label="0.3" value="0.3"></el-option>
                    <el-option label="0.4" value="0.4"></el-option>
                    <el-option label="0.5" value="0.5"></el-option>
                    <el-option label="0.6" value="0.6"></el-option>
                    <el-option label="0.7" value="0.7"></el-option>
                    <el-option label="0.8" value="0.8"></el-option>
                    <el-option label="0.9" value="0.9"></el-option>
                    <el-option label="1" value="1"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="营销流速占比"
                  prop="velocityRateYx"
                  v-if="formData.channelType == '1'"
                readonly
                >
                  <el-select
                    class="input-w"
                    v-model="formData.velocityRateYx"
                    placeholder="请选择"
                    disabled 
                  >
                    <el-option label="0" value="0"></el-option>
                    <el-option label="0.1" value="0.1"></el-option>
                    <el-option label="0.2" value="0.2"></el-option>
                    <el-option label="0.3" value="0.3"></el-option>
                    <el-option label="0.4" value="0.4"></el-option>
                    <el-option label="0.5" value="0.5"></el-option>
                    <el-option label="0.6" value="0.6"></el-option>
                    <el-option label="0.7" value="0.7"></el-option>
                    <el-option label="0.8" value="0.8"></el-option>
                    <el-option label="0.9" value="0.9"></el-option>
                    <el-option label="1" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
        </div>
        <div style="width: 50%; float: left; height: 660px">
          <el-form-item label="通道产品类型" prop="channelType">
            <el-select
              v-model="formData.productId"
              disabled
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="短信通道" value="1"></el-option>
              <el-option label="彩信通道" value="2"></el-option>
              <el-option label="视频短信" value="3"></el-option>
              <el-option label="国际短信" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="批次容量" prop="batch">
            <el-input
              class="input-w"
              readonly
              v-model="formData.batch"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道属性" prop="channelNature">
            <el-select
              class="input-w"
              v-model="formData.channelNature"
              placeholder="请选择通道属性"
              disabled
            >
              <el-option label="直连" value="1"></el-option>
              <el-option label="第三方" value="2"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="下行流速" prop="descendingVelocity">
                  <el-input class="input-w"  readonly v-model="formData.descendingVelocity"></el-input>
                </el-form-item> -->
          <el-form-item
            label="省份选择"
            prop="provincialId"
            v-if="formData.channelFlag == '1' || formData.channelFlag == '2'"
          >
            <el-select
              v-model="formData.provincialId"
              placeholder="请选择"
              class="input-w"
              disabled
            >
              <el-option
                v-for="(item, index) in ProvincesCities"
                :key="index"
                :label="item.provincial"
                :value="item.provincialId"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="是否专属" prop="isPrivate">
                  <el-select
                    class="input-w"
                    v-model="formData.isPrivate"
                    placeholder="请选择"
                    disabled 
                  >
                    <el-option label="是" value="2"></el-option>
                    <el-option label="否" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
          <!-- <el-form-item
                  label="专属客户名称"
                  prop="privateName"
                  v-if="formData.isPrivate == '2'"
                >
                  <el-input class="input-w"  readonly v-model="formData.privateName"></el-input>
                </el-form-item> -->
          <el-form-item label="运营商类型">
            <el-select
              v-model="formData.operatorsType"
              disabled
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
              <el-option label="三网" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道状态">
            <el-select
              class="input-w"
              disabled
              v-model="formData.channelStatus"
              placeholder="请选择"
            >
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="协议推送ip" prop="ip">
            <el-input class="input-w" readonly v-model="formData.ip"></el-input>
          </el-form-item>
          <!-- <el-form-item
                  label="行业流速占比"
                  prop="velocityRateHy"
                  v-if="formData.channelType == '1'"
                >
                  <el-select
                    class="input-w"
                    v-model="formData.velocityRateHy"
                    placeholder="请选择"
                    disabled 
                  >
                    <el-option label="0" value="0"></el-option>
                    <el-option label="0.1" value="0.1"></el-option>
                    <el-option label="0.2" value="0.2"></el-option>
                    <el-option label="0.3" value="0.3"></el-option>
                    <el-option label="0.4" value="0.4"></el-option>
                    <el-option label="0.5" value="0.5"></el-option>
                    <el-option label="0.6" value="0.6"></el-option>
                    <el-option label="0.7" value="0.7"></el-option>
                    <el-option label="0.8" value="0.8"></el-option>
                    <el-option label="0.9" value="0.9"></el-option>
                    <el-option label="1" value="1"></el-option>
                  </el-select>
                </el-form-item> -->
          <el-form-item label="备注" prop="remark">
            <el-input
              :autosize="{ minRows: 4, maxRows: 5 }"
              class="input-w"
              type="textarea"
              v-model="formData.remark"
              readonly
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ChannelView',
  props: ['ChannelData'],
  data() {
    return {
      ChannelDialog: false, //配置弹窗显示
      //弹窗的数据
      formData: {
        //通道数据
        channelCode: '', //编号
        channelName: '', //名称
        channelNumber: '', //号码
        channelUnitPrice: '', //单价
        channelFlag: '', //区域
        interfaceMode: '', //接口方式
        operatorId: '', //运营商id
        madeReported: '', //是否提前报备
        port: '', //协议推送
        pactType: '1', //新旧网关
        ip: '', //协议推送ip
        channelType: '', //通道产品类型
        batch: '', //批次容量
        channelNature: '', //通道属性
        descendingVelocity: '', //下行速度
        provincialId: '', //省份id
        isPrivate: '', //是否专属
        privateName: '', //客户专属名称
        operatorsType: '', //运营商类型
        channelStatus: '', //通道状态
        velocityRateYzm: '', //验证码流速
        velocityRateHy: '', //行业流速占比
        velocityRateYx: '', //营销流速占比
        removeSign: '', //抹除签名
        remark: '', //备注
      },
      operatorNames: [],
      ProvincesCities: [],
    }
  },
  methods: {
    ChannelClick() {
      this.ChannelDialog = true // 调用父组件的关闭方法
    },
  },
  watch: {
    ChannelData(val) {
      // 获取省市
      window.api.get(
        window.path.omcs + 'operatingchannelprovincial/list',
        {},
        (res) => {
          this.ProvincesCities = res.data
        }
      )
      //获取运营商
      window.api.get(
        window.path.omcs + 'operatingchannelinfo/operator',
        {
          pageSize: 100,
          currentPage: 1,
        },
        (res) => {
          this.operatorNames = res.data
        }
      )
      window.api.get(
        window.path.omcs + 'operatingchannelinfo/info?channelCode=' + val,
        {},
        (res) => {
          if (res) {
            this.formData = res
            this.formData.removeSign += ''
            this.formData.pactType += ''
          } else {
            this.formData = {}
            this.$message({
              message: '该通道ID未查出数据',
              type: 'warning',
            })
          }
        }
      )
    },
  },
}
</script>

<style lang="less" scoped>
#ChannelStyle .addUserDialog > .el-dialog {
  height: 750px;
}

:deep(.el-dialog__body) {
  height: 500px;
}
</style>
