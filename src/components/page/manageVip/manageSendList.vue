<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <el-form
        :inline="true"
        :model="formInline"
        class="demo-form-inline"
        ref="formInline"
      >
        <el-form-item label="用户名称" prop="customerName">
          <el-input
            v-model="formInline.customerName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="formInline.taskName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="发送类型" prop="taskType">
          <el-select v-model="formInline.taskType" clearable class="input-w">
            <el-option label="短信群发" value="1"> </el-option>
            <el-option label="短信触发" value="2"> </el-option>
            <el-option label="彩信群发" value="3"> </el-option>
            <el-option label="视频群发" value="4"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品" prop="productId">
          <el-select v-model="formInline.productId" clearable class="input-w">
            <el-option label="短信" value="1"> </el-option>
            <el-option label="彩信" value="2"> </el-option>
            <el-option label="视频短信" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click="onSubmit()">查询</el-button>
          <el-button type="primary" plain @click="resetForm('formInline')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="margin: 10px 0">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :stripe="true"
        :data="tableDataObj.tableData"
        style="width: 100%"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="manageSendList"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="用户名称" title="用户名称" prop="id" align="left">
          <template v-slot="{ row }">
            <Tooltip
              v-if="row.customerName"
              :content="row.customerName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <span>{{ row.customerName }}</span> -->
          </template>
        </vxe-column>
        <vxe-column field="ID" title="ID" prop="id" align="left">
          <template v-slot="{ row }">
            <div>{{ row.id }}</div>
          </template>
        </vxe-column>
        <vxe-column field="任务名称" title="任务名称" prop="id" align="left">
          <template v-slot="{ row }">
            <Tooltip
              v-if="row.taskName"
              :content="row.taskName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <span>{{ row.taskName }}</span> -->
          </template>
        </vxe-column>
        <vxe-column field="产品" title="产品" prop="productId" align="left">
          <template v-slot="{ row }">
            <div v-if="row.productId == 1">短信</div>
            <div v-else-if="row.productId == 2">彩信</div>
            <div v-else-if="row.productId == 3">视频短信</div>
          </template>
        </vxe-column>
        <vxe-column field="发送类型" title="发送类型" prop="id" align="left">
          <template v-slot="{ row }">
            <div v-if="row.taskType == 1">短信群发</div>
            <div v-else-if="row.taskType == 2">短信触发</div>
            <div v-else-if="row.taskType == 3">彩信群发</div>
            <div v-else>视频群发</div>
          </template>
        </vxe-column>
        <vxe-column field="模板Id" title="模板Id" prop="tempId" align="left">
          <template v-slot="{ row }">
            <div>{{ row.tempId }}</div>
          </template>
        </vxe-column>
        <vxe-column field="提交时间" title="提交时间" align="left" width="160px">
          <template v-slot="{ row }">
            <div v-if="row.createTime">{{
              moment(row.createTime).format('YYYY-MM-DD HH:mm:ss')
            }}</div>
          </template>
        </vxe-column>

        <vxe-column field="发送内容" title="发送内容" prop="id" align="left" width="400px">
          <template v-slot="{ row }">
            <div>{{ row.content }}</div>
          </template>
        </vxe-column>
        <vxe-column field="号码数量" title="号码数量" prop="id" align="left">
          <template v-slot="{ row }">
            <div>{{ row.mobileNumber }}</div>
          </template>
        </vxe-column>
        <vxe-column field="是否定时" title="是否定时" prop="id" align="left">
          <template v-slot="{ row }">
            <div v-if="row.isTiming == 0">立即发送</div>
            <div v-else>定时发送</div>
          </template>
        </vxe-column>
        <vxe-column field="发送时间" title="发送时间" align="left" width="160px">
          <template v-slot="{ row }">
            <div v-if="row.sendTime">{{
              moment(row.sendTime).format('YYYY-MM-DD HH:mm:ss')
            }}</div>
          </template>
        </vxe-column>
        <vxe-column field="任务状态" title="任务状态" prop="id" align="left">
          <template v-slot="{ row }">
            <div v-if="row.status == 0">待处理</div>
            <div style="color: #e6a23c" v-if="row.status == 1">处理中</div>
            <div style="color: #16a598" v-if="row.status == 2">已完成</div>
            <div style="color: #909399" v-if="row.status == 3">已取消</div>
            <div style="color: red" v-if="row.status == -1">处理异常</div>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="pagination">
        <div></div>
        <div>
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInlines.currentPage"
            :page-size="formInlines.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    Tooltip
  },
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      //查询条件的值
      formInline: {
        customerName: '', //用户名
        taskName: '', //触发名称
        taskType: '',
        productId: '',
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      formInlines: {
        customerName: '', //用户名
        taskName: '', //触发名称
        taskType: '',
        productId: '',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getList() {
      window.api.post(
        window.path.omcs + 'customercontactsendtask/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    onSubmit() {
      Object.assign(this.formInlines, this.formInline)
      this.getList()
    },
    resetForm(form) {
      this.$refs.formInline.resetFields()
      Object.assign(this.formInlines, this.formInline)
      this.getList()
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.getList()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.getList()
    },
  },
}
</script>

<style scoped>
.pagination {
  display: flex;
  justify-content: space-between;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>