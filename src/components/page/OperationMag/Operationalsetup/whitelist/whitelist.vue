<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="等级" prop="mobile">
            <el-select
              v-model="formInline.level"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="一级白名单" value="1"></el-option>
              <el-option label="二级白名单" value="2"></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="创建人" prop="createName">
            <el-input
              v-model="formInline.createName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <!-- <div class="boderbottom">
        
          </div> -->
      <div class="Signature-search-fun" style="margin: 10px 0">
        <!-- <span class="" style="margin-right:10px"> 白名单列表</span> -->
        <el-button type="primary" @click="addopt">新增</el-button>
        <el-button type="primary" style="margin-left: 15px" @click="updatefail"
          >白名单导入</el-button
        >
        <el-button
          type="danger"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delAll"
          >批量删除</el-button
        >
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        ></table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        title="新增白名单"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="500px"
      >
        <el-form
          :model="formop"
          :rules="ruless"
          label-width="80px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formop.mobile"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="等级" prop="level">
            <el-radio-group v-model="formop.level">
              <el-radio value="1">一级白名单</el-radio>
              <el-radio value="2" style="margin-left: 10px"
                >二级白名单</el-radio
              >
            </el-radio-group>
          </el-form-item> -->
          <el-form-item label="有效期" prop="days">
            <el-input
              style="width: 150px"
              v-model="formop.days"
              placeholder="请输入有效期天数"
              class="input-w"
            ></el-input
            ><span style="margin-left: 10px">天</span>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formop.remark"
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 编辑 -->
      <el-dialog
        title="编辑白名单"
        v-model="dialogEditVisible"
        :close-on-click-modal="false"
        width="500px"
      >
        <el-form
          :model="formopEdit"
          :rules="rulessEdit"
          label-width="80px"
          ref="formopEdit"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="手机号" prop="mobile">
            <el-input
              :disabled="true"
              v-model="formopEdit.mobile"
              autocomplete="off"
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="等级" prop="level">
            <el-radio-group v-model="formopEdit.level">
              <el-radio value="1">一级白名单</el-radio>
              <el-radio value="2" style="margin-left: 10px"
                >二级白名单</el-radio
              >
            </el-radio-group>
          </el-form-item> -->

          <el-form-item label="有效时长">
            <!-- <el-input v-model="formopEdit.days" placeholder='请输入有效期天数' class="input-w"></el-input> -->
            <el-date-picker
              disabled
              v-model="formopEdit.expireDate"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="有效期" prop="days">
            <el-input
              style="width: 150px"
              v-model="formopEdit.days"
              placeholder="请输入有效期天数"
              class="input-w"
            ></el-input
            ><span style="margin-left: 10px">天</span>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              v-model="formopEdit.remark"
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitEdit('formopEdit')"
              >提 交</el-button
            >
            <el-button @click="dialogEditVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增结束 -->
      <!-- 上传失败代码 -->
      <el-dialog
        title="白名单导入"
        v-model="dialogFormUpdate"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formupcode"
          :rules="rules"
          ref="formupcode"
          label-width="120px"
        >
          <el-form-item label="上传文件">
            <el-upload
              v-bind:data="{
                days: formupcode.days,
              }"
              class="upload-demo"
              ref="uploading"
              :action="actionUrl"
              :on-remove="handleRemove"
              multiple
              :limit="1"
              :headers="myHeader"
              :auto-upload="false"
              :on-success="handleAvatarSuccess"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              
            </el-upload>
            <a 
              style="margin-left: 10px"
                href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/f065834fd8cf9828612ddbc55cff100f"
                >模板下载</a
              >
          </el-form-item>
          <el-form-item label="有效期" prop="days">
            <el-input
              style="width: 150px"
              v-model="formupcode.days"
              placeholder="请输入有效期天数"
              class="input-w"
            ></el-input
            ><span style="margin-left: 10px">天</span>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForms('formupcode')"
              >提 交</el-button
            >
            <el-button @click="dialogFormUpdate = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 上传失败代码结束 -->
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import TableTem from '../../../../publicComponents/TableTem.vue'
export default {
  components: {
    TableTem,
  },
  name: 'whitelist',
  data() {
    var percent = (rule, value, callback) => {
      if (value != '') {
        let reg = /^[0-9]*[1-9][0-9]*$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入正确天数'))
        }
      } else {
        callback(new Error('有效期不能为空'))
      }
    }
    return {
      actionUrl: window.path.omcs + 'operatingmobilewhitelist/upload',
      isFirstEnter: false,
      dialogEditVisible: false,
      myHeader: '', //请求头
      id: '',
      editdays: '',
      tipFlag: true,
      flag: false,
      fileList: [],
      titleMap: {
        add: '新增白名单',
        edit: '编辑白名单',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      dialogFormUpdate: false, //导入失败代码隐藏
      selectId: '',
      formInline: {
        //失败代码查询
        // level: '',
        mobile: '',
        createName: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //失败注释代码查询储存
        // level: '',
        mobile: '',
        createName: '',
        currentPage: 1,
        pageSize: 10,
      },
      formop: {
        //新增失败代码类别
        mobile: '',
        // level: '',
        remark: '',
        days: '',
      },
      formopEdit: {
        //新增失败代码类别
        mobile: '',
        // level: '',
        remark: '',
        days: '',
        expireDate: '',
      },
      //提交失败代码
      formupcode: {
        yunyingshang: '',
        days: '',
      },
      ruless: {
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'change',
          },
        ],
        // level: [
        //   {
        //     required: true,
        //     message: '请选择等级',
        //     trigger: 'change',
        //   },
        // ],
        days: [
          {
            required: true,
            validator: percent,
            trigger: 'change',
          },
        ],
        remark: [
          {
            required: true,
            message: '备注不能为空',
            trigger: 'change',
          },
        ],
      },
      rulessEdit: {
        mobile: [
          {
            required: true,
            message: '请输入手机号',
            trigger: 'change',
          },
        ],
        // level: [
        //   {
        //     required: true,
        //     message: '请选择等级',
        //     trigger: 'change',
        //   },
        // ],
        remark: [
          {
            required: true,
            message: '备注不能为空',
            trigger: 'change',
          },
        ],
      },
      rules: {
        failureCodeNote: [
          { required: true, message: '请输入失败代码类别', trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '长度在 1 到 50 个字符',
            trigger: ['blur', 'change'],
          },
        ],
        uploadfail: [
          { required: true, message: '请选择文件上传', trigger: 'blur' },
        ],
      },
      tableDataObj: {
        //列表数据
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'id',
            showName: 'ID',
            width: '70',
            fixed: false,
          },
          {
            prop: 'mobile',
            showName: '手机号',
            fixed: false,
          },
          // {
          //   prop: 'level',
          //   showName: '等级',
          //   fixed: false,
          //   formatData: function (val) {
          //     if (val == 1) {
          //       return '一级白名单'
          //     }
          //     if (val == 2) {
          //       return '二级白名单'
          //     }
          //   },
          // },
          {
            prop: 'remark',
            showName: '备注',
            fixed: false,
          },
          {
            prop: 'expireDate',
            showName: '有效期',
            fixed: false,
            formatData: function (val) {
              if (val) {
                return moment(val).format('YYYY-MM-DD HH:mm:ss')
              } else {
                return '永久有效'
              }
            },
            width: '160',
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
            formatData: function (val) {
              return moment(val).format('YYYY-MM-DD HH:mm:ss')
            },
            width: '160',
          },
          {
            prop: 'updateTime',
            showName: '更新时间',
            fixed: false,
            formatData: function (val) {
              return moment(val).format('YYYY-MM-DD HH:mm:ss')
            },
            width: '160',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '150', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-edit',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'el-icon-delete',
            color: '#f56c6c',
          },
        ],
      },
    }
  },
  methods: {
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingmobilewhitelist/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //------------------------ 列表信息 ---------------------------------
    //------------------------ 操作列表 ---------------------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.begTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.begTime = ''
        this.formInline.endTime = ''
      }
    },
    // handDate(e){
    //   let reg = /^[0-9]*[1-9][0-9]*$/;
    //     if (reg.test(e)) {
    //       this.tipFlag = true
    //     } else {
    //       this.tipFlag = false
    //     }
    // },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
      }
      this.selectId = selectId.join(',')
    },
    Query() {
      //查询功能
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      //重置
      // this.formInline.beginTime = "";
      // this.formInline.endTime = "";
      // this.formInline.level = ''
      this.formInline.mobile = ''
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    // expireTime(e){
    //   this.formop.expireDate =  moment(e).format('YYYY-MM-DD HH:mm:ss')
    // },
    hangDays(e) {
      this.formop.days = e
    },

    submitForm(formop) {
      //提交新增失败代码
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '确认执行此操作吗？',
            window.path.omcs + 'operatingmobilewhitelist',
            this.formop,
            () => {
              this.dialogFormVisible = false
              this.gettableLIst()
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //编辑
    submitEdit(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.formopEdit.id = this.id
          this.$confirms.confirmation(
            'put',
            '确认执行此操作吗？',
            window.path.omcs + 'operatingmobilewhitelist',
            this.formopEdit,
            () => {
              this.dialogEditVisible = false
              this.gettableLIst()
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //新增
    addopt() {
      this.flag = false
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },

    delAll() {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingmobilewhitelist/' + this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    detailsRow(val) {
      //编辑id
      this.id = val.row.id
      this.tipFlag = false
      this.flag = true
      this.dialogEditVisible = true
      this.dialogStatus = 'edit'
      // this.editdays = val.row.days
      // this.tableRow = val.row;
      this.$nextTick(() => {
        this.$refs.formopEdit.resetFields()
        Object.assign(this.formopEdit, val.row)
        //省市区选中
        // if (val.row.level) {
        //   this.formopEdit.level = val.row.level + ''
        // }

        //  this.formop.remark = val.row.remark
        // this.formop.optionscity = [val.row.provincialId,val.row.cityId];
      })
    },
    delRow: function (val) {
      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingmobilewhitelist/' + val.row.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    //------------------上传文件开始--------------
    //上传失败代码弹窗
    updatefail() {
      this.dialogFormUpdate = true
    },
    //改变上传文件
    handleChange(file, fileList) {
      let endingCode = file.name //结尾字符
      this.endingName = endingCode.slice(
        endingCode.lastIndexOf('.') + 1,
        endingCode.length
      )
      let isStyle = false //文件格式
      let fileStyle = ['xls', 'xlsx']
      for (let i = 0; i < fileStyle.length; i++) {
        if (this.endingName === fileStyle[i]) {
          isStyle = true
          break
        }
      }
      if (isStyle == true) {
        this.fileList = fileList
      } else {
        this.fileList = []
        this.$message({
          message: '只能上传.xls .xlsx 结尾的文件！',
          type: 'warning',
        })
      }
    },
    //移除上传文件
    handleRemove(file, fileList) {
      this.fileList = []
    },
    //上传成功
    handleAvatarSuccess(res, file) {
      console.log(res)
      this.dialogFormUpdate = false
      this.gettableLIst()
      if (res.code == 200) {
        this.$message({
          message: '文件上传成功！此次共上传' + res.data + '条数据！',
          type: 'success',
        })
      } else {
        this.$message.error('文件上传失败！')
      }
    },
    submitForms(formupcode) {
      //上传失败代码
      this.$refs[formupcode].validate((valid) => {
        if (valid) {
          if (this.fileList.length <= 0) {
            this.$message.error('请至少上传一个文件！')
            return false
          } else {
            this.$confirm('上传文件, 是否继续?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                //上传成功
                this.$refs.uploading.submit()
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消上传',
                })
              })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //上传文件过多提示
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      )
    },
    //---------------------方法结束--------------

    handelOptionButton: function (val) {
      //操作栏
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
    //------------------------ 操作列表 ---------------------------------
  },
  mounted() {
    this.gettableLIst()
    this.myHeader = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  watch: {
    // tabelAlllist: {
    //   handler() {
    //     // this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    dialogFormVisible(val) {
      //监听弹框是否关闭
      if (val == false) {
        // this.tipFlag = true
        // this.formop.days = ""
        // this.editdays = ""
        this.$refs.formop.resetFields() //清空表单
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>

<style>
.el-upload--text {
  width: 80px;
  height: 32px;
  border: none;
}
</style>
