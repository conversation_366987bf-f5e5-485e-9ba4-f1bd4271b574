<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            label-width="82px"
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名称" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="消息ID" prop="msgid">
              <el-input
                v-model="formInline.msgid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="短信内容"  prop="content">
                                <el-input v-model="formInline.content" placeholder="" class="input-w"></el-input>
                            </el-form-item> -->
            <el-form-item label="任务状态" prop="timingStatus">
              <el-select
                v-model="formInline.timingStatus"
                clearable
                placeholder="请选择类型"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="未执行" value="1"></el-option>
                <el-option label="正在执行" value="2"></el-option>
                <el-option label="取消" value="3"></el-option>
                <el-option label="超时未执行" value="4"></el-option>
                <el-option label="执行完成" value="5"></el-option>
                <el-option label="执行失败" value="6"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="发送时间类型"  prop="isTiming">
                                <el-select v-model="formInline.isTiming" placeholder="请选择类型" class="input-w">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="定时发送" value="1"></el-option>
                                    <el-option label="立即发送" value="0"></el-option>
                                </el-select>
                            </el-form-item> -->
            <el-form-item label="发送时间" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="true"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click.prevent="Query()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>

        <div class="Mail-table" style="padding-bottom: 40px;">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handelSelection"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
              <el-button
                type="danger"
                style="margin-right: 10px"
                @click="batchDeletion"
                v-if="selectId.length"
                >批量取消</el-button
              >
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="IMSWebTask"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handelSelection"
            @checkbox-change="handelSelection">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="用户名" title="用户名" width="130">
              <template v-slot="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.userName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.userName}}
                                    </span>
                                </el-popover> -->
                <div>
                  {{ scope.row.userName }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="消息ID" title="消息ID" width="200">
              <template v-slot="scope">
                <div>
                  {{ scope.row.msgid }}
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                  <CopyTemp :content="scope.row.msgid" />
                </div>
              </template>
            </vxe-column>
            <vxe-column field="提交时间" title="提交时间" width="170">
              <template v-slot="scope">
                <div>{{ scope.row.createTime }}</div>
              </template>
            </vxe-column>
            <vxe-column field="短信内容" title="短信内容">
              <template v-slot="scope">
                <div>{{ scope.row.signature }}</div>
                <div>{{ scope.row.content }}</div>
              </template>
            </vxe-column>
            <vxe-column
              field="提交数量"
              title="提交数量"
              width="90"
            >
              <template v-slot="scope">
                <div>
                  {{ scope.row.mobileNumber }}
                </div>
              </template>
            </vxe-column>
            <vxe-column
              field="定时时间"
              title="定时时间"
              width="170"
            >
              <template v-slot="scope">
                <div>
                  {{ scope.row.sendTime }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="提交状态" title="提交状态" width="100">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 1"
                  type="info"
                  effect="dark"
                  
                >
                  未执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 2"
                  type="warning"
                  effect="dark"
                  
                >
                  正在执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 3"
                  type="info"
                  effect="dark"
                  
                >
                  取消
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 4"
                  type="danger"
                  effect="dark"
                  
                >
                  超时未执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 5"
                  type="success"
                  effect="dark"
                  
                >
                  执行完成
                </el-tag>
                <el-tag
                  :disable-transitions="true" v-if="scope.row.timingStatus == 6" type="danger" effect="dark">
                  执行失败
                </el-tag>
                <!-- <span v-if="scope.row.timingStatus=='1'">未执行</span>
                                    <span v-if="scope.row.timingStatus=='2'">正在执行</span>
                                    <span v-if="scope.row.timingStatus=='3'">取消</span>
                                    <span v-else-if="scope.row.timingStatus=='4'" >超时未执行</span>
                                    <span v-else-if="scope.row.timingStatus=='5'" >执行完成</span> -->
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="140">
              <template v-slot="scope">
                <el-button
                  link
                  style="color: orange"
                  v-if="scope.row.timingStatus == '1'"
                  @click="cancel(scope.row)"
                  ><el-icon><CircleCloseFilled /></el-icon>&nbsp;取消</el-button
                >
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
import UserLists from '@/components/publicComponents/userList.vue'
// import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    UserLists,
    CopyTemp
  },
  name: 'IMSWebTask',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      name: 'IMSWebTask',
      userFlag: false,
      nameover: '',
      //复选框值
      selectId: '',
      // 搜索数据
      formInline: {
        // content:'',
        msgid: '',
        // isTiming:'',
        userName: '',
        timingStatus: '',
        startTime: '',
        stopTime: '',
        beginTime: moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        // content:'',
        msgid: '',
        // isTiming:'',
        userName: '',
        timingStatus: '',
        startTime: '',
        stopTime: '',
        beginTime: moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        'post',
        '确定取消定时发送？',
        window.path.omcs + 'consumertimingims/cancel',
        { ids: this.selectId },
        (res) => {
          this.InquireList()
        }
      )
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].taskSmsId)
      }
      this.selectId = selectId //批量操作选中id
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumertimingims/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 编辑
    edit(val) {
      if (Date.parse(val.sendTime) - Date.now() < 1800000) {
        this.$message({
          message: '定时小于现在30分钟无法编辑',
          type: 'warning',
        })
      } else {
        if (val.sendType == 3) {
          this.$router.push({
            path: '/PersonalizedSendEditing?SmSId=' + val.taskSmsId,
          })
        } else {
          this.$router.push({ path: '/SendSMSEdit?SmSId=' + val.taskSmsId })
        }
      }
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        'post',
        '确定取消定时？',
        window.path.omcs + 'consumertimingims/cancel',
        { ids: [val.id] },
        (res) => {
          this.InquireList()
        }
      )
    },
    // 下载
    download(val) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          'M+': this.getMonth() + 1,
          'd+': this.getDate(),
          'h+': this.getHours(),
          'm+': this.getMinutes(),
          's+': this.getSeconds(),
          'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        }
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + '').substr(4 - RegExp.$1.length)
          )
        for (var i in args) {
          var n = args[i]
          if (new RegExp('(' + i + ')').test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
            )
        }
        return format
      }
      var that = this
      filedownload()
      function filedownload() {
        fetch(
          that.API.cpus +
            'v3/file/download?fileName=' +
            val.fileMobile +
            '&group=group1&path=' +
            val.filePath.slice(7),
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
            // body: JSON.stringify({
            //     batchNo: val.row.batchNo,
            // })
          }
        )
          .then((res) => res.blob())
          .then((data) => {
            let blobUrl = window.URL.createObjectURL(data)
            download(blobUrl)
          })
      }
      function download(blobUrl) {
        var a = document.createElement('a')
        a.style.display = 'none'
        a.download =
          '(' + new Date().format('YYYY-MM-DD hh:mm:ss') + ') ' + val.fileMobile
        a.href = blobUrl
        a.click()
      }
    },
    // 查询
    Query() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      ;(this.formInline.time = []),
        (this.formInline.time1 = []),
        (this.formInline.beginTime = '')
      this.formInline.endTime = ''
      this.formInline.startTime = ''
      this.formInline.stopTime = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 定时时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + ' 00:00:00'
        this.formInline.stopTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.startTime = ''
        this.formInline.stopTime = ''
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
    // handleCopy(name,event){
    //  clip(name, event)
    // },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
