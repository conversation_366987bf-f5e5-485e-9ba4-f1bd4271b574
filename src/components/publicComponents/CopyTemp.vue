<template>
  <div class="copy_temp" :style="propStyle" v-if="content">
    <el-tooltip :effect="effect" content="复制" placement="top" v-if="showTip">
      <i :style="{ fontSize, color }" class="iconfont icon-fuzhi"
        @click="handleCopy($event)"></i>
    </el-tooltip>
    <i v-else :style="{ fontSize, color }" class="iconfont icon-fuzhi"
      @click="handleCopy($event)"></i>
  </div>
</template>

<script>
import clip from '@/utils/clipboard'

export default {
  components: {
    // ElIconDocumentCopy,
  },
  props: {
    // 复制内容
    content: {
      type: String,
      default: '',
    },
    // 是否展示复制tip
    showTip: {
      type: Boolean,
      default: true,
    },
    // 图标大小
    fontSize: {
      type: String,
      default: '14px',
    },
    // 图标颜色
    color: {
      type: String,
      default: '#409eff',
    },
    // tip背景颜色
    effect: {
      type: String,
      default: 'dark',
    },
    // 样式
    propStyle: {
      type: Object,
      default: () => ({})
    },
  },
  methods: {
    handleCopy(event) {
      clip(this.content, event)
    },
  },
}
</script>

<style lang="less" scoped>
.copy_temp {
  display: inline-block;
  margin-left: 2px;
}
.icon-fuzhi {
  cursor: pointer;
}
</style>
