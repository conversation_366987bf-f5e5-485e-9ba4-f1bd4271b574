// 审核管理相关路由
export default [
  {
    path: '/AuditCertification',
    meta: { title: ' 客服认证审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/AuditCertification/AuditCertification.vue'),
  },
  {
    path: '/recmanagementSetting',
    meta: { title: ' 失败自动补发设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/recmanagementSetting.vue'),
  },
  {
    path: '/blackTemplate',
    meta: { title: '黑模板管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/blackTemplate/blackTemplate.vue'),
  },
  {
    path: '/ReCheck',
    meta: { title: '风控认证审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ReCheck/ReCheck.vue'),
  },
  {
    path: '/ReportLinkAudit',
    meta: { title: '短信备案链接审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ReportLinkAudit.vue'),
  },
  {
    path: '/ReportLinkProofAudit',
    meta: { title: '链接授权证明审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ReportLinkProofAudit.vue'),
  },
  {
    path: '/TemplateAudit',
    meta: { title: '短信模板审核管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/TemplateAudit/TemplateAudit.vue'),
  },
  {
    path: '/SignatureAudit',
    meta: { title: '短信签名审核管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/SignatureAudit.vue'),
  },
  {
    path: '/SMSContentReview',
    meta: { title: '短信发送内容审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/SMSContentReview.vue'),
  },
  {
    path: '/BlackWordReview',
    meta: { title: '短信敏感词审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/BlackWordReview.vue'),
  },
  {
    path: '/clientsmssimilar',
    meta: { title: '自动审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/clientsmssimilar.vue'),
  },
  {
    path: '/smsTestBlackWord',
    meta: { title: ' 短信测试账号敏感词审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/smsTestList.vue'),
  },
  {
    path: '/smsTestContent',
    meta: { title: ' 短信测试账号内容审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/smsTestContent.vue'),
  },
  {
    path: '/ConditionalAuditList',
    meta: { title: ' 短信条件审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ConditionalAuditList.vue'),
  },

  {
    path: '/shortChainAnalysis',
    meta: { title: '短链追踪统计', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ShortAudit/shortChainAnalysis.vue'),
  },
  {
    path: '/shortLinkApply',
    meta: { title: '短链开通审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ShortAudit/shortLinkApply.vue'),
  },
  {
    path: '/shortDomain',
    meta: { title: '短链白名单审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ShortAudit/shortDomain.vue'),
  },
] 