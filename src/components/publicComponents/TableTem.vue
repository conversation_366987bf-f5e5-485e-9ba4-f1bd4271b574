<template>
  <div class="tableTem">
    <!-- <el-table
      v-loading="tableDataObj.loading2"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.6)"
      element-loading-customClass="loadingStyle"
      :data="tableDataObj.tableData"
      :max-height="tableDataObj.tableStyle.height"
      :border="tableDataObj.tableStyle.border"
      :stripe="tableDataObj.tableStyle.stripe"
      :style="tableDataObj.tableStyle.style"
      :default-expand-all="tableDataObj.tableStyle.isDefaultExpand"
      @selection-change="handleSelectionChange"
    > -->
    <vxe-toolbar :ref="tableDataObj.id + 'tbRef'" :custom="tableDataObj.custom">
      <template #buttons>
        <slot name="tableButtons"></slot>
      </template>
    </vxe-toolbar>

    <vxe-table
      :ref="tableDataObj.id + 'Ref'"
      :id="tableDataObj.id"
      border
      stripe
      :custom-config="customConfig"
      :column-config="{ resizable: true }"
      :row-config="{ isHover: true }"
      :virtual-y-config="{enabled: false}"
      min-height="1"
      v-loading="tableDataObj.loading2"
      element-loading-text="拼命加载中"
      element-loading-background="rgba(0, 0, 0, 0.6)"
      :style="{ fontSize: (tableDataObj.tableFontSize || '12') + 'px' }"
      :data="tableDataObj.tableData"
      @checkbox-all="handleSelectionChange"
      @checkbox-change="handleSelectionChange"
    >
      <!-- 复选框一栏 -->
      <vxe-column
        type="checkbox"
        width="50"
        v-if="tableDataObj.tableStyle.isSelection"
      >
      </vxe-column>
      <!-- 折叠栏 -->
      <vxe-column
        type="expand"
        field="展开项"
        title="展开项"
        width="60px"
        v-if="tableDataObj.tableStyle.isExpand"
      >
        <template #default="props">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item
              :class="[
                tableDataObj.tableStyle.expandLine2 ? 'expand2Line' : '',
              ]"
              :label="item.showName"
              v-for="(item, index) in tableDataObj.tableLabelExpand"
              :key="index"
            >
              <!-- 有数据变化和条件的颜色变化 -->
              <span
                v-if="item.showCondition && item.formatData"
                :class="[
                  item.showCondition.condition == props.row[item.prop]
                    ? 'red'
                    : '',
                ]"
                >{{ props.row[item.prop] }}</span
              >
              <!-- 只有条件颜色变化 -->
              <span
                v-else-if="item.showCondition"
                :class="[
                  item.showCondition.condition.indexOf(props.row[item.prop]) >
                  -1
                    ? 'red'
                    : '',
                ]"
              >
                {{ props.row[item.prop] }}
              </span>
              <!-- 只有表格数据整理 -->
              <span
                v-else-if="item.formatData"
                :style="{
                  color: item.showColorTag ? item.showColorTag.color : '',
                }"
                >{{ props.row[item.prop] }}</span
              >
              <!-- 正常 -->
              <span
                v-else
                :style="{
                  color: item.showColorTag ? item.showColorTag.color : '',
                }"
                >{{ props.row[item.prop] }}</span
              >
            </el-form-item>
          </el-form>
        </template>
      </vxe-column>
      <!-- 数据表格 -->
      <vxe-column
        v-for="(item, index) in tableDataObj.tableLabel"
        :prop="item.prop"
        :key="index"
        :field="item.showName"
        :title="item.showName"
        :fixed="item.fixed"
        :sortable="item.sortable"
        :width="item.width"
      >
        <template #default="scope">
          <div v-if="item.showName === '运营商'">
            <div
              v-if="scope.row[item.prop] == 1"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-yidong"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">移动</span>
            </div>
            <div
              v-else-if="scope.row[item.prop] == 2"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-liantong"
                style="font-size: 16px; color: #f56c6c"
              ></i>
              <span style="margin-left: 5px">联通</span>
            </div>
            <div
              v-else-if="scope.row[item.prop] == 3"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-dianxin"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">电信</span>
            </div>
            <div
              v-else-if="scope.row[item.prop] == 4"
              style="display: flex; align-items: center"
            >
              <span>其他</span>
            </div>
          </div>
          <!-- 有数据变化和条件的颜色变化 -->
          <div
            v-else-if="item.showCondition && item.formatData"
            :class="[
              item.showCondition.condition == scope.row[item.prop] ? 'red' : '',
            ]"
          >
            {{ item.formatData(scope.row[item.prop]) }}
          </div>
          <!-- 只有条件颜色变化 -->
          <div
            v-else-if="item.showCondition"
            :class="[
              item.showCondition.condition.indexOf(scope.row[item.prop]) > -1
                ? 'red'
                : '',
            ]"
          >
            {{ scope.row[item.prop] }}
          </div>
          <!-- 只有表格数据整理 -->
          <div
            v-else-if="item.formatData"
            :style="{ color: item.showColorTag ? item.showColorTag.color : '' }"
          >
            {{ item.formatData(scope.row[item.prop]) }}
          </div>
          <!-- 不同颜色标签 -->
          <div v-else-if="item.showTags">
            <el-tag
              :disable-transitions="true"
              class="comTableTag"
              :style="{ background: item.showTags.bgColor[tagIndex] }"
              v-for="(tag, tagIndex) in scope.row[item.prop]"
              :key="tagIndex"
              >{{ tag }}</el-tag
            >
          </div>
          <!-- 当有通道方法时 -->
          <div v-else-if="item.Channel">
            <div
              v-for="(item, Index) in scope.row[item.prop]
                ? scope.row[item.prop].split(',')
                : scope.row[item.prop]"
              :key="Index"
              @click="ChannelRef(item)"
              style="
                color: rgb(22, 165, 137);
                cursor: pointer;
                display: inline-block;
                margin-right: 10px;
              "
            >
              {{ item + " " }}
            </div>
          </div>
          <!-- <el-popover v-else-if="item.showName=='用户名'|| item.showName=='用户名称' && scope.row.userId "
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row)'>
                                     <span slot="reference" @click="routers(scope.row)"  style="color:#16A589;cursor: pointer;">{{ scope.row[item.prop] }}</span>
                                </el-popover> -->
          <div
            @click="routers(scope.row)"
            v-else-if="item.showName == '用户名' || item.showName == '用户名称'"
            style="color: #16a589; cursor: pointer"
          >
            {{ scope.row[item.prop] }}
          </div>
          <!-- <span @click="handPhone(scope.row,scope.$index)" v-else-if="item.showName=='手机号码'||item.showName=='手机号'" style="color:#16A589;cursor: pointer;">{{ scope.row[item.prop] }}</span> -->
          <div
            @click="getIdx(scope.row)"
            v-else-if="item.showName == '序号'"
            style="color: #409eff; cursor: pointer"
          >
            {{ scope.row[item.prop] }}
          </div>
          <!-- <span v-else-if="item.Channel" @click="ChannelRef(scope.row[item.prop])" style="color:rgb(22, 165, 137);cursor: pointer;">{{ scope.row[item.prop] }}</span>   -->
          <!-- 正常 -->
          <div
            v-else
            :style="{ color: item.showColorTag ? item.showColorTag.color : '' }"
          >
            {{ scope.row[item.prop] }}
          </div>
        </template>
      </vxe-column>
      <!-- 操作栏 -->
      <vxe-column
        field="操作"
        title="操作"
        v-if="tableDataObj.tableOptions || tableDataObj.conditionOption"
        fixed="right"
        :width="tableDataObj.tableStyle.optionWidth"
      >
        <template #default="scope">
          <template v-for="(item, index) in tableDataObj.tableOptions">
            <div style="display: inline-block">
              <div
                v-if="tableDataObj.tableOptions"
                class="elButton"
                type="text"
                :style="{ color: item.color }"
                @click="handelOptionButton(item.optionMethod, scope.row)"
              >
                <!-- <i :class="item.icon"></i> -->
                <el-icon v-if="item.icon == 'EditPen'"><EditPen /></el-icon>
                <el-icon v-else-if="item.icon == 'CircleCloseFilled'"
                  ><CircleCloseFilled
                /></el-icon>
                <el-icon v-else-if="item.icon == 'Sort'"><Sort /></el-icon>
                <el-icon v-else-if="item.icon == 'Delete'"><Delete /></el-icon>
                <el-icon v-else-if="item.icon == 'SuccessFilled'"
                  ><SuccessFilled
                /></el-icon>
                <el-icon v-else-if="item.icon == 'WarningFilled'"
                  ><WarningFilled
                /></el-icon>
                <el-icon v-else-if="item.icon == 'InfoFilled'"
                  ><InfoFilled
                /></el-icon>
                <el-icon v-else-if="item.icon == 'RefreshRight'"
                  ><RefreshRight
                /></el-icon>
                <el-icon v-else-if="item.icon == 'Refresh'"
                  ><Refresh
                /></el-icon>
                <el-icon v-else-if="item.icon == 'Setting'"
                  ><Setting
                /></el-icon>
                <el-icon v-else-if="item.icon == 'Star'"><Star /></el-icon>
                <el-icon v-else-if="item.icon == 'CircleCheck'"
                  ><CircleCheck
                /></el-icon>
                <i v-else :class="item.icon"></i>
                <div style="height: 24px; line-height: 24px">
                  {{ item.optionName }}
                </div>
              </div>
            </div>
          </template>
          <!-- 特殊条件的按钮 -->
          <div style="display: flex">
            <template v-for="(item, index) in tableDataObj.conditionOption">
              <div
                v-if="
                  item.otherOptionName ||
                  (!item.otherOptionName &&
                    item.contactData == scope.row[item.contactCondition])
                "
                type="text"
              >
                <div
                  class="elButton"
                  :style="{ color: item.optionButtonColor }"
                  @click="handelOptionButton(item.optionMethod, scope.row)"
                  v-if="item.contactData == scope.row[item.contactCondition]"
                >
                  <!--  -->
                  <el-icon v-if="item.icon == 'EditPen'"><EditPen /></el-icon>
                  <el-icon v-else-if="item.icon == 'CircleCloseFilled'"
                    ><CircleCloseFilled
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'Sort'"><Sort /></el-icon>
                  <el-icon v-else-if="item.icon == 'Delete'"
                    ><Delete
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'SuccessFilled'"
                    ><SuccessFilled
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'WarningFilled'"
                    ><WarningFilled
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'InfoFilled'"
                    ><InfoFilled
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'RefreshRight'"
                    ><RefreshRight
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'Refresh'"
                    ><Refresh
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'Setting'"
                    ><Setting
                  /></el-icon>
                  <el-icon v-else-if="item.icon == 'Star'"><Star /></el-icon>
                  <el-icon v-else-if="item.icon == 'CircleCheck'"
                    ><CircleCheck
                  /></el-icon>
                  <i v-else :class="item.icon"></i>
                  <div style="height: 24px; line-height: 24px">
                    {{ item.optionName }}
                  </div>
                </div>
                <div
                  class="elButton"
                  :style="{ color: item.optionOtherButtonColor }"
                  @click="handelOptionButton(item.otherOptionMethod, scope.row)"
                  v-else-if="item.otherOptionName"
                >
                  <!-- <i :class="item.otherIcon"></i> -->
                  <el-icon v-if="item.otherIcon == 'EditPen'"
                    ><EditPen
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'CircleCloseFilled'"
                    ><CircleCloseFilled
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'Sort'"
                    ><Sort
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'Delete'"
                    ><Delete
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'SuccessFilled'"
                    ><SuccessFilled
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'WarningFilled'"
                    ><WarningFilled
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'InfoFilled'"
                    ><InfoFilled
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'RefreshRight'"
                    ><RefreshRight
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'Refresh'"
                    ><Refresh
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'Setting'"
                    ><Setting
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'Star'"
                    ><Star
                  /></el-icon>
                  <el-icon v-else-if="item.otherIcon == 'CircleCheck'"
                    ><CircleCheck
                  /></el-icon>
                  <i v-else :class="item.otherIcon"></i>
                  <div style="height: 24px; line-height: 24px">
                    {{ item.otherOptionName }}
                  </div>
                </div>
              </div>
            </template>
          </div>
        </template>
      </vxe-column>
    </vxe-table>
    <slot name="pagination"></slot>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
export default {
  name: "tableTemp",
  props: {
    tableDataObj: {
      type: Object,
      default: () => {},
    },
    //  tableData:Array,//表格数据
    //  tableLabel:Array,//表头
    //  tableStyle:Object,//表格样式
    //  tableOptions:Array,//表格操作栏
    //  tableLabelExpand:Array//折叠列
  },
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
    };
  },
  methods: {
    handelOptionButton(methods, row) {
      $emit(this, "handelOptionButton", { methods: methods, row: row });
    },
    handelColumnBtn() {
      $emit(this, "handelColumnBtn");
    },
    //将选中的行发送到父组件
    handleSelectionChange(val) {
      const selectionArr = [];
      val.records.forEach(function (el) {
        selectionArr.push(el);
      });
      $emit(this, "handelSelection", selectionArr);
    },
    userList(val) {
      console.log(val, "val");
    },
    routers(val) {
      // console.log(val);
      // const routerArr = [];
      //   val.forEach(function (el) {
      //       routerArr.push(el);
      //   });

      $emit(this, "routers", val);
    },
    getIdx(val) {
      $emit(this, "getIdx", val);
    },
    ChannelRef(val) {
      $emit(this, "save", val);
    },
    handPhone(val, index) {
      $emit(this, "handPhone", val, index);
    },
  },
  mounted() {
    const $table = this.$refs[this.tableDataObj.id + "Ref"];
    const $toolbar = this.$refs[this.tableDataObj.id + "tbRef"];
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  emits: [
    "handelOptionButton",
    "handelSelection",
    "routers",
    "getIdx",
    "save",
    "handPhone",
    "handelColumnBtn",
  ],
};
</script>

<style>
body .el-table th.gutter {
  display: table-cell !important;
}
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
.elButton i {
  margin-right: 3px;
}
.tableTem .el-form-item {
  margin-right: 0;
  margin-bottom: 0 !important;
}
.tableTem .el-table__expanded-cell {
  padding: 6px 18px !important;
}
.tableTem .el-form-item {
  width: 100%;
}
.tableTem .el-form-item__content {
  max-width: calc(100% - 100px);
}
.tableTem .demo-table-expand label {
  color: #99a9bf;
}
.tableTem .el-form-item--small .el-form-item__content,
.tableTem .el-form-item--small .el-form-item__label {
  line-height: 24px;
}
.comTableTag {
  display: inline-block;
  height: 30px !important;
  line-height: 27px !important;
  margin: 3px;
  padding: 3px;
  color: #fff !important;
  padding: 0 10px !important;
}
.red {
  color: red;
}
.tableTem .el-form-item.expand2Line {
  max-width: 33.33% !important;
}
.tableTem .expand2Line .el-form-item__content {
  max-width: calc(100% - 130px) !important;
  color: #b3b0b0 !important;
}
.elButton {
  cursor: pointer;
  color: #16a589;
  display: flex;
  align-items: center;
  padding: 0px 4px;
}
</style>

<style>
.el-loading-spinner i {
  margin-top: -10px;
  margin-bottom: 10px;
  font-size: 30px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>