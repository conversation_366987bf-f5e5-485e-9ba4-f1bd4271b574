<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon>
          用户发送统计</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <component v-bind:is="currentTabComponent"></component>
    </div>
  </div>
</template>

<script>
import SendReportform from './components/SendReportform.vue'
export default {
  components: {
    SendReportform,
    ElIconLxEmoji,
  },
  name: 'userSendData',
  data() {
    return {
      currentTabComponent: 'SendReportform',
    }
  },
}
</script>
