<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title" style="display: flex; align-items: center">
        <div
          @click="goBack()"
          style="
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #16a589;
          "
        >
          <el-icon><ArrowLeftBold /></el-icon>
          <span style="display: inline-block; padding-right: 10px"> 返回</span>
        </div>
        |
        <span style="margin-left: 10px;">阅信配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="状态设置" name="stateRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="stateRef"
            label-width="170px"
          >
            <el-form-item label="模板推送地址" prop="">
              <el-input
                type="textarea"
                style="width: 250px"
                placeholder=""
                v-model="addUserStep2Form.formData.templateUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：模板推送地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="链接申请推送地址" prop="">
              <el-input
                style="width: 250px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.linkApplyUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：链接申请推送地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="链接解析回执推送地址" prop="">
              <el-input
                style="width: 250px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.linkReportUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：链接解析回执推送地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="自定义域名" prop="domain">
              <el-input
                style="width: 250px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.domain"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：自定义域名需要线下申请</span>
              </p>
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(stateRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="addUserStep2Form"
        label-width="170px">
        <div class="float-div-1" style="padding-right: 20px">
          <div>
            <p style="padding: 0 0 18px 40px; font-weight: bold; font-size: 14px">
              状态设置
            </p>
            <el-form-item label="模板推送地址" prop="">
              <el-input type="textarea" style="width: 250px" placeholder=""
                v-model="addUserStep2Form.formData.templateUrl"></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：模板推送地址</span>(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="链接申请推送地址" prop="">
              <el-input style="width: 250px" placeholder="" type="textarea"
                v-model="addUserStep2Form.formData.linkApplyUrl"></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：链接申请推送地址</span>(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="链接解析回执推送地址" prop="">
              <el-input style="width: 250px" placeholder="" type="textarea"
                v-model="addUserStep2Form.formData.linkReportUrl"></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：链接解析回执推送地址</span>(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="自定义域名" prop="domain ">
              <el-input style="width: 250px" placeholder="" type="textarea"
                v-model="addUserStep2Form.formData.domain"></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：自定义域名需要线下申请</span>
              </p>
            </el-form-item>
          </div>
        </div>
        <div style="text-align: center" class="clear-div">
          <el-button style="width:120px" @click="goBack()">返回</el-button>
          <el-button type="primary" style="margin-left: 20px;width:120px" @click="submitForm('addUserStep2Form')">提
            交</el-button>
        </div>
      </el-form> -->
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const stateRef = ref(null);
const isFirstEnter = ref(false);
const roleId = ref("");
const activeName = ref("stateRef");
const jsonData = reactive({
  consumerName: "",
  compName: "",
});
const addUserStep2Form = reactive({
  formData: {
    userId: "",
    username: "",
    templateUrl: "", //模板审核推送服务地址
    linkApplyUrl: "", //阅信链接申请推送服务地址
    linkReportUrl: "", //阅信链接解析回执推送服务地址
    domain: "", //自定义域名
    pushSize: "500", //推送最大数据量
    id: "",
  },
  formRule: {
    templateUrl: [
      { required: true, message: "模板推送地址不能为空", trigger: "change" },
    ],
    linkApplyUrl: [
      {
        required: true,
        message: "阅信链接申请推送服务地址不能为空",
        trigger: "change",
      },
    ],
    linkReportUrl: [
      {
        required: true,
        message: "阅信链接解析回执推送服务地址不能为空",
        trigger: "change",
      },
    ],
  },
});
const channel = ref([]);
onMounted(() => {
  addUserStep2Form.formData.userId = $router.currentRoute.value.query.id;
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo) {
    roleId.value = userInfo.roleId;
  }
  getBasicData();
  handleEdit();
});
const getBasicData = () => {
  window.api.get(
    window.path.omcs + "operatinguser/" + addUserStep2Form.formData.userId,
    {},
    (res) => {
      jsonData.consumerName = res.data.consumerName;
      jsonData.compName = res.data.compName;
     addUserStep2Form.formData.username = res.data.consumerName;
     addUserStep2Form.formData.userId = res.data.userId;
    }
  );
};
const goBack = () => {
  $router.push({
    path: "UserDetail",
    query: {
      id: $router.currentRoute.value.query.id,
    },
  });
};
const handleEdit = () => {
  try {
    window.api.get(
      window.path.yuexin +
        "/operator/user/config/" +
        addUserStep2Form.formData.userId,
      {},
      (res) => {
        if (res.code == 200) {
          if (res.data.id) {
            addUserStep2Form.formData.id = res.data.id;
          }
          if (res.data.linkApplyUrl) {
            addUserStep2Form.formData.linkApplyUrl = res.data.linkApplyUrl;
          } else {
            addUserStep2Form.formData.linkApplyUrl = "";
          }
          if (res.data.linkReportUrl) {
            addUserStep2Form.formData.linkReportUrl = res.data.linkReportUrl;
          } else {
            addUserStep2Form.formData.linkReportUrl = "";
          }
          if (res.data.templateUrl) {
            addUserStep2Form.formData.templateUrl = res.data.templateUrl;
          } else {
            addUserStep2Form.formData.templateUrl = "";
          }
          if (res.data.pushSize) {
            addUserStep2Form.formData.pushSize = res.data.pushSize;
          } else {
            addUserStep2Form.formData.pushSize = "";
          }
          if (res.data.domain) {
            addUserStep2Form.formData.domain = res.data.domain;
          } else {
            addUserStep2Form.formData.domain = "";
          }
        }
      }
    );
  } catch (error) {
    console.error(error);
  }
};
const submitForm = (formEl) => {
  try {
    formEl.validate((valid, fields) => {
      if (valid) {
        ElMessageBox.confirm("确认修改阅信配置？", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            window.api.post(
              window.path.yuexin + "/operator/user/config",
              addUserStep2Form.formData,
              (res) => {
                if (res.code == 200) {
                  ElMessage({
                    type: "success",
                    message: res.msg,
                  });
                  goBack();
                } else {
                  ElMessage({
                    type: "error",
                    message: res.msg,
                  });
                }
              }
            );
          })
          .catch(() => {
            ElMessage({
              type: "info",
              message: "操作取消",
            });
          });
      } else {
        console.error("error submit!!", fields);
        return false;
      }
    });
  } catch (error) {
    console.error(error);
  }
};
</script>
<!-- <script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  name: "yuexinconfiguration",
  data() {
    return {
      isFirstEnter: false,
      activeName: "stateRef",
      jsonData: {
        consumerName: "",
        compName: "",
      }, //用户资料
      addUserStep2Form: {
        formData: {
          userId: "",
          username: "",
          templateUrl: "",//模板审核推送服务地址
          linkApplyUrl: "",//阅信链接申请推送服务地址
          linkReportUrl: "",//阅信链接解析回执推送服务地址
          domain: "",//自定义域名
          pushSize: '500',//推送最大数据量
          id: ""
        },
        formRule: {
          templateUrl: [
            { required: true, message: "模板推送地址不能为空", trigger: "change" },
          ],
          linkApplyUrl: [
            { required: true, message: "阅信链接申请推送服务地址不能为空", trigger: "change" },
          ],
          linkReportUrl: [
            { required: true, message: "阅信链接解析回执推送服务地址不能为空", trigger: "change" },
          ],
        },
      },
      channel: [], //通道
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs +
        "operatinguser/" +
        this.addUserStep2Form.formData.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName;
          this.jsonData.compName = res.data.compName;
          this.addUserStep2Form.formData.username = res.data.consumerName;
          this.addUserStep2Form.formData.userId = res.data.userId;
        }
      );
    },
    //返回
    goBack() {
      // this.$router.push({
      //   path: "UserDetail",
      //   query: { id: this.$route.query.id, ids: this.$route.query.ids },
      // });
      this.$router.push({ path: 'UserDetail', query: { id: this.$route.query.id } })
    },
    //编辑的赋值
    handleEdit() {
      window.api.get(
        window.path.yuexin + "/operator/user/config/" + this.$route.query.id,
        {},
        (res) => {
          if (res.code == 200) {
            if (res.data.id) {
              this.addUserStep2Form.formData.id = res.data.id
            }
            if (res.data.linkApplyUrl) {
              this.addUserStep2Form.formData.linkApplyUrl = res.data.linkApplyUrl
            } else {
              this.addUserStep2Form.formData.linkApplyUrl = ''
            }
            if (res.data.linkReportUrl) {
              this.addUserStep2Form.formData.linkReportUrl = res.data.linkReportUrl
            } else {
              this.addUserStep2Form.formData.linkReportUrl = ''
            }
            if (res.data.templateUrl) {
              this.addUserStep2Form.formData.templateUrl = res.data.templateUrl
            } else {
              this.addUserStep2Form.formData.templateUrl = ''
            }
            if (res.data.pushSize) {
              this.addUserStep2Form.formData.pushSize = res.data.pushSize
            } else {
              this.addUserStep2Form.formData.pushSize = ''
            }
            if (res.data.domain) {
              this.addUserStep2Form.formData.domain = res.data.domain
            } else {
              this.addUserStep2Form.formData.domain = ''
            }
          }
          // this.addUserStep2Form.formData = res.data
        }
      );
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "确认修改5G短信配置？",
            window.path.yuexin + "/operator/user/config",
            this.addUserStep2Form.formData,
            (res) => {
              //返回用户管理页面
              this.goBack();
            }
          );
        } else {
          return false;
        }
      });
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      //   this.addUserStep2Form.formData5g.userId = this.$route.query.id;
      this.addUserStep2Form.formData.userId = this.$route.query.id;
      this.handleEdit();
      this.getBasicData();
    });
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        // this.addUserStep2Form.formData5g.userId = this.$route.query.id;
        this.addUserStep2Form.formData.userId = this.$route.query.id;
        this.handleEdit();
        this.getBasicData();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
    // console.log(11111);
  },
  deactivated() {
    this.activeName = "stateRef";
  },
  watch: {
  },
};
</script> -->
<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}

.input-w {
  width: 345px !important;
}

.el-radio-group .el-radio {
  margin-right: 0px;
}

.fillet {
  padding: 20px;
}

.input-w-2 {
  width: 200px !important;
}

.input-w-3 {
  width: 200px !important;
}

.input-w-4 {
  width: 190px !important;
}

.input-w-sm {
  width: 126px !important;
}

.input-w-f {
  width: 80px !important;
}

.float-div-1 {
  float: left;
}

.float-div-2 {
  float: left;
}

.float-div-3 {
  float: left;
}

.clear-div {
  clear: both;
}

.red {
  color: red;
}

@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>
<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>