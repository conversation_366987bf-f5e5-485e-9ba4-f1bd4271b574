<template>
  <div class="container_left">
    <div class="OuterFrame fillet" style="height: 100%">
      <div>
        <el-form
          label-width="82px"
          ref="formInline"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item
            label-width="100px"
            label="操作人用户名"
            prop="createName"
          >
            <el-input
              v-model="formInline.createName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="封禁IP" prop="bannedIp">
            <el-input
              v-model="formInline.bannedIp"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="IP区域" prop="ipArea">
            <el-input
              v-model="formInline.ipArea"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="操作时间" prop="time1">
            <el-date-picker
              class="input-time"
              v-model="formInline.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              @change="getTimeOperating"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="更新时间" prop="time2">
            <el-date-picker
              class="input-time"
              v-model="formInline.time2"
              value-format="YYYY-MM-DD"
              type="daterange"
              @change="getTimeUpdate"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="ListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reset('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table" style="padding-bottom: 40px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button type="primary" @click="addCharacter">新增IP地址</el-button>
            <el-button
              type="danger"
              @click="batchDeletion"
              v-if="selectId.length > 0"
              >批量删除</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInlines.currentPage"
            :page-size="formInlines.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <!-- 添加IP地址 -->
    <el-dialog
      :title="IPaddDialog"
      v-model="addCharacterPopUps"
      width="380px"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <el-form
        :inline="true"
        ref="addIP"
        :model="addIP"
        :rules="rules"
        class="demo-form-inline IPForm"
      >
        <el-form-item label="IP地址" label-width="80px" prop="bannedIp">
          <el-input
            v-model="addIP.bannedIp"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="IP状态" label-width="80px" prop="ipStatus">
          <el-select
            v-model="addIP.ipStatus"
            selected
            disabled
            placeholder="封禁中"
            class="input-w"
          >
            <el-option label="封禁中" value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="addCharacterPopUps = false">取 消</el-button>
          <el-button type="primary" @click="addIPDialog">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    DatePlugin,
    TableTem
  },
  name: 'IPblacklist',
  data() {
    // 验证IP规则
    var verificationIP = (rule, value, callback) => {
      if (
        !/^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/.test(
          value
        )
      ) {
        return callback(new Error('请正确填写IP地址'))
      } else {
        callback()
      }
    }
    return {
      isFirstEnter: false,
      IPaddDialog: '',
      selectId: '', //批量操作选中id
      blackIpId: '', //列表ID
      rules: {
        bannedIp: [
          { required: true, validator: verificationIP, trigger: 'blur' },
        ],
        ipStatus: [
          { required: true, message: '请选择IP状态', trigger: 'change' },
        ],
      },
      // 搜索数据
      formInline: {
        createName: '',
        bannedIp: '',
        ipArea: '',
        createBegTime: '',
        createEndTime: '',
        updateBegTime: '',
        updateEndTime: '',
        time1: [],
        time2: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        createName: '',
        bannedIp: '',
        ipArea: '',
        createBegTime: '',
        createEndTime: '',
        updateBegTime: '',
        updateEndTime: '',
        time1: [],
        time2: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 新增ip数据
      addIP: {
        bannedIp: '',
        ipStatus: '2',
      },
      // 添加角色弹出窗
      addCharacterPopUps: false,
      datePluginValueList: {
        type: 'date',
        start: '',
        end: '',
        range: '-',
        defaultTime: '', //默认起始时刻
        datePluginValue: '',
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        id: "IPblacklist",
        custom: true,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'createName',
            showName: '操作人用户名',
          },
          {
            prop: 'bannedIp',
            showName: '封禁IP',
            width: '140px',
          },
          {
            prop: 'ipArea',
            showName: 'IP区域',
          },
          {
            prop: 'createTime',
            showName: '操作时间',
            width: '170px',
          },
          {
            prop: 'updateTime',
            showName: '更新时间',
            width: '170px',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '100', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: '', //关联的表格属性
            contactData: '', //关联的表格属性-值
            optionName: '删除', //按钮的显示文字
            optionMethod: 'Disable', //按钮的方法
            icon: 'el-icon-delete', //按钮图标
            optionButtonColor: '#f56c6c', //按钮颜色
            otherOptionName: '删除', //其他条件的按钮显示文字
            otherOptionMethod: 'Disable', //其他条件的按钮方法
            otherIcon: 'el-icon-delete', //其他条件按钮的图标
            optionOtherButtonColor: '#f56c6c', //其他条件按钮的颜色
          },
        ],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingblackip/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.createBegTime = ''
      this.formInline.createEndTime = ''
      this.formInline.updateBegTime = ''
      this.formInline.updateEndTime = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.createBegTime = val[0]
        this.formInline.createEndTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.createBegTime = ''
        this.formInline.createEndTime = ''
      }
    },
    // 更新时间
    getTimeUpdate(val) {
      if (val) {
        this.formInline.updateBegTime = val[0]
        this.formInline.updateEndTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.updateBegTime = ''
        this.formInline.updateEndTime = ''
      }
    },
    // 添加角色
    addCharacter() {
      this.IPaddDialog = '新增IP黑名单'
      this.addCharacterPopUps = true
    },
    // 操作
    handelOptionButton: function (val) {
      if (val.methods == 'edit') {
        this.blackIpId = val.row.blackIpId
        this.IPaddDialog = '编辑IP黑名单'
        this.addCharacterPopUps = true
        this.$nextTick(() => {
          this.$refs.addIP.resetFields()
          Object.assign(this.addIP, val.row)
        })
      } else if (val.methods == 'Disable') {
        this.$confirms.confirmation(
          'delete',
          '确认删除',
          window.path.omcs + 'operatingblackip/' + val.row.blackIpId,
          {},
          (res) => {
            this.InquireList()
            this.addCharacterPopUps = false
          }
        )
      }
    },
    // 新增IP地址
    addIPDialog() {
      this.$refs['addIP'].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + 'operatingblackip/existip',
            {
              bannedIp: this.addIP.bannedIp,
              blackIpId: this.blackIpId || null,
            },
            (res) => {
              if (res.data == 'true') {
                if (this.IPaddDialog == '新增IP黑名单') {
                  this.$confirms.confirmation(
                    'post',
                    '确认新增',
                    window.path.omcs + 'operatingblackip',
                    this.addIP,
                    (res) => {
                      this.InquireList()
                      this.addCharacterPopUps = false
                    }
                  )
                } else {
                  this.$confirms.confirmation(
                    'put',
                    '确认编辑',
                    window.path.omcs + 'operatingblackip',
                    this.addIP,
                    (res) => {
                      this.InquireList()
                      this.addCharacterPopUps = false
                    }
                  )
                }
              } else {
                this.$message({
                  type: 'error',
                  duration: '2000',
                  message: '当前IP地址已存在',
                })
              }
            }
          )
        } else {
          return false
        }
      })
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].blackIpId)
      }
      this.selectId = selectId.join(',')
    },
    //批量删除
    batchDeletion() {
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续?',
        window.path.omcs + 'operatingblackip/batch/' + this.selectId,
        {},
        (res) => {
          this.InquireList()
        }
      )
    },
    // 关闭弹出层
    handleClose(done) {
      this.addCharacterPopUps = false
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps: function (val) {
      if (val == false) {
        this.$refs.addIP.resetFields() //清空表单
        this.addIP = {
          bannedIp: '',
          ipStatus: '2',
        }
        this.blackIpId = null
      }
    },
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
