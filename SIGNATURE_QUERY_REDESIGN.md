# 🎨 SignatureQuery.vue 重新设计 - 按签名查询展示

## 🎯 重设计概述

根据用户需求，将SignatureQuery.vue重新设计为按签名查询的展示方式，突出显示签名的用户、主体和实名状态，默认不加载数据，通过签名查询获取结果，每个签名记录独立展示为卡片。

## ✨ 核心设计特性

### 📋 主要功能重设计
- **按签名查询**: 只支持签名输入查询，不再支持用户名等其他条件
- **默认不加载**: 打开弹窗时不自动加载数据，等待用户输入查询
- **独立卡片展示**: 每个签名记录独立显示为一个卡片，不再分组
- **突出用户主体**: 卡片头部突出显示用户名、主体信息
- **实名状态突出**: 头部右侧显示三大运营商实名状态
- **默认展开**: 所有信息默认展开显示，不需要二次弹窗

### 🎨 UI设计重点
- **简化查询**: 只保留签名输入框，简化查询条件
- **卡片式布局**: 每个签名记录一个独立卡片
- **信息层次**: 签名 → 用户/主体 → 实名状态 → 详细信息
- **默认展开**: 所有详细信息直接在卡片中展示
- **无二次弹窗**: 移除详情弹窗，所有信息在主卡片中展示

## 🔧 技术实现

### 1. 查询方式重设计

#### **简化查询表单**
```vue
<el-form :inline="true" :model="queryForm" class="query-form">
  <el-form-item label="签名" prop="signature">
    <el-input 
      ref="signatureInput"
      v-model="queryForm.signature" 
      placeholder="请输入签名进行查询" 
      clearable
      style="width: 300px;"
      @keyup.enter="handleQuery"
    />
  </el-form-item>
  <el-form-item>
    <el-button type="primary" @click="handleQuery" :loading="loading">
      <i class="el-icon-search"></i>
      查询签名
    </el-button>
    <el-button @click="handleReset">
      <i class="el-icon-refresh"></i>
      重置
    </el-button>
  </el-form-item>
</el-form>
```

#### **查询逻辑优化**
```javascript
// 查询
handleQuery() {
  if (!this.queryForm.signature.trim()) {
    this.$message.warning('请输入签名进行查询')
    return
  }
  
  this.loading = true
  this.hasSearched = true
  const params = {
    current: this.pagination.current,
    size: this.pagination.size,
    signature: this.queryForm.signature.trim()
  }
  
  window.api.post(
    window.path.omcs + 'consumerSignature/page',
    params,
    (res) => {
      this.loading = false
      this.tableData = res.records || []
      this.pagination.total = res.total || 0
    }
  )
}
```

### 2. 卡片布局重设计

#### **卡片头部 - 突出用户和主体**
```vue
<template #header>
  <div class="signature-header">
    <div class="signature-main-info">
      <div class="signature-title">
        <i class="el-icon-edit signature-icon"></i>
        <span class="signature-text">{{ item.signature }}</span>
        <el-tag :type="getSignatureAttrTag(item.signatureAttr).type" size="small">
          {{ getSignatureAttrTag(item.signatureAttr).text }}
        </el-tag>
      </div>
      <div class="user-subject-info">
        <div class="user-info">
          <i class="el-icon-user user-icon"></i>
          <span class="user-label">用户：</span>
          <span class="user-name">{{ item.consumerName }}</span>
          <span class="user-id">(ID: {{ item.userId }})</span>
        </div>
        <div class="subject-info" v-if="item.subject">
          <i class="el-icon-office-building subject-icon"></i>
          <span class="subject-label">主体：</span>
          <span class="subject-name">{{ item.subject }}</span>
        </div>
      </div>
    </div>
    <div class="realname-status-header">
      <div class="status-title">实名状态</div>
      <div class="operators-status">
        <div class="operator-status-item">
          <i class="iconfont icon-yidong operator-icon yd"></i>
          <el-tag :type="item.ydRealNameStatus == 1 ? 'success' : 'info'" size="small">
            {{ item.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
          </el-tag>
        </div>
        <!-- 联通、电信类似 -->
      </div>
    </div>
  </div>
</template>
```

#### **卡片内容 - 默认展开所有信息**
```vue
<div class="signature-content">
  <!-- 基本信息行 -->
  <div class="basic-info-row">
    <div class="info-item">
      <div class="info-label">
        <i class="el-icon-message"></i>
        签名类型
      </div>
      <div class="info-value">
        <el-tag :type="getServiceTypeTag(item.serviceType).type" size="small">
          {{ getServiceTypeTag(item.serviceType).text }}
        </el-tag>
      </div>
    </div>
    <div class="info-item" v-if="item.source">
      <div class="info-label">
        <i class="el-icon-link"></i>
        签名来源
      </div>
      <div class="info-value">{{ item.source }}</div>
    </div>
    <div class="info-item">
      <div class="info-label">
        <i class="el-icon-time"></i>
        创建时间
      </div>
      <div class="info-value">{{ formatDate(item.createTime) }}</div>
    </div>
  </div>

  <!-- 短信示例 -->
  <div class="content-example" v-if="item.contentExample">
    <div class="example-label">
      <i class="el-icon-chat-line-square"></i>
      短信示例
    </div>
    <div class="example-text">{{ item.contentExample }}</div>
  </div>

  <!-- 详细实名信息 -->
  <div class="detailed-realname">
    <!-- 三大运营商详细信息卡片 -->
  </div>

  <!-- 资质图片 -->
  <div class="qualification-images" v-if="item.imgUrl">
    <!-- 图片展示 -->
  </div>
</div>
```

### 3. 数据结构简化

#### **查询表单数据**
```javascript
queryForm: {
  signature: '' // 只保留签名查询
}
```

#### **状态管理**
```javascript
data() {
  return {
    loading: false,
    queryForm: {
      signature: ''
    },
    tableData: [],
    pagination: {
      current: 1,
      size: 20,
      total: 0
    },
    hasSearched: false // 是否已经搜索过
  }
}
```

### 4. 移除的功能

#### **移除的组件和功能**
- ❌ 详情弹窗 (`detailVisible`, `detailData`)
- ❌ 图片预览弹窗 (`imagePreviewVisible`, `previewImages`)
- ❌ 展开/收起功能 (`expandedCards`, `toggleExpand`)
- ❌ 分组功能 (`groupedData` 计算属性)
- ❌ 用户名查询条件
- ❌ 签名属性查询条件

#### **简化的方法**
- ❌ `handleViewDetail()` - 移除详情查看
- ❌ `handleViewImages()` - 移除图片预览
- ❌ `toggleExpand()` - 移除展开切换
- ❌ `groupedData` - 移除数据分组

## 🎨 样式设计重点

### 1. 卡片头部设计

#### **签名主信息区**
```css
.signature-main-info {
  flex: 1;
  
  .signature-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    .signature-text {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .user-subject-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .user-info, .subject-info {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 14px;
      
      .user-name, .subject-name {
        color: #303133;
        font-weight: 600;
      }
    }
  }
}
```

#### **实名状态头部区**
```css
.realname-status-header {
  min-width: 300px;
  
  .status-title {
    font-size: 14px;
    font-weight: 600;
    color: #606266;
    margin-bottom: 8px;
    text-align: center;
  }
  
  .operators-status {
    display: flex;
    gap: 12px;
    justify-content: center;
    
    .operator-status-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
    }
  }
}
```

### 2. 卡片内容设计

#### **基本信息行**
```css
.basic-info-row {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  
  .info-item {
    flex: 1;
    
    .info-label {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      font-weight: 600;
      color: #606266;
      margin-bottom: 6px;
    }
  }
}
```

#### **详细实名信息卡片**
```css
.realname-detail-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  height: 160px;
  
  .realname-card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f8f9fa;
    font-weight: 600;
  }
  
  &.yd-card .realname-card-header {
    background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
    color: #409eff;
  }
  
  &.lt-card .realname-card-header {
    background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
    color: #f56c6c;
  }
  
  &.dx-card .realname-card-header {
    background: linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%);
    color: #e6a23c;
  }
}
```

## 🔄 用户交互流程

### 1. 查询流程
```
用户打开弹窗 → 看到查询提示 → 输入签名 → 点击查询/回车 → 
显示查询结果 → 每个签名一个卡片 → 所有信息默认展开
```

### 2. 浏览流程
```
查看签名卡片 → 头部显示签名、用户、主体、实名状态 → 
内容区显示基本信息、短信示例、详细实名信息、资质图片
```

### 3. 分页流程
```
查看更多结果 → 使用底部分页组件 → 切换页码或页面大小 → 
重新查询 → 更新卡片列表
```

## 📊 数据展示重点

### 1. 头部信息优先级
1. **签名名称** - 最突出，18px字体
2. **用户信息** - 用户名 + ID，绿色图标
3. **主体信息** - 主体名称，建筑图标
4. **实名状态** - 三大运营商状态，右侧显示

### 2. 内容信息层次
1. **基本信息** - 签名类型、来源、创建时间
2. **短信示例** - 蓝色背景突出显示
3. **详细实名** - 三列运营商卡片
4. **资质图片** - 缩略图展示，支持预览

### 3. 状态可视化
- **实名状态**: 已实名(绿色)、未实名(灰色)
- **签名属性**: 报备签名(绿色)、自定义签名(橙色)
- **签名类型**: 验证码(蓝色)、通知(绿色)、营销(橙色)

## 🎯 使用场景优化

### 1. 快速签名查询
```
运营人员需要查询特定签名 → 输入签名关键词 → 
快速找到相关签名 → 查看用户和实名状态
```

### 2. 签名状态检查
```
客服需要检查签名实名状态 → 搜索签名 → 
直接在卡片头部查看三大运营商状态 → 无需额外操作
```

### 3. 签名信息核实
```
审核人员需要核实签名信息 → 查询签名 → 
查看用户主体、短信示例、资质图片 → 一屏完成核实
```

## 🚀 优化效果

### 1. 用户体验提升
- **查询简化**: 只需输入签名，操作更简单
- **信息突出**: 重要信息在头部突出显示
- **无需点击**: 所有信息默认展开，减少交互步骤

### 2. 界面优化
- **信息密度**: 合理的信息密度，不拥挤不稀疏
- **视觉层次**: 清晰的信息层次和重点突出
- **响应式**: 适配不同屏幕尺寸

### 3. 功能完整性
- **一屏展示**: 所有重要信息在一个卡片中展示
- **状态直观**: 实名状态一目了然
- **操作便捷**: 减少弹窗和点击操作

---

**重设计完成**: SignatureQuery.vue已成功重设计为按签名查询的展示方式，突出显示用户、主体和实名状态，提供了更直观和高效的签名信息查询体验。
