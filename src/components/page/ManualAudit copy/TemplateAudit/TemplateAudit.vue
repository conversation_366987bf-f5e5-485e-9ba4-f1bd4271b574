<template>
  <div>
    <div class="Top_title">
      <span>模板审核bbbbbbb</span>
    </div>
    <div class="fillet Templat-box">
      <el-form
        :inline="true"
        :model="formInline"
        class="demo-form-inline"
        ref="queryForm"
      >
        <div>
          <el-form-item
            label="用户名"
            label-width="82px"
            prop="clientName"
            style="float: left"
          >
            <el-input
              v-model="formInline.clientName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="模板名称"
            label-width="82px"
            prop="temName"
            style="float: left"
          >
            <el-input
              v-model="formInline.temName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="模板格式"
            label-width="82px"
            prop="temFormat"
            style="float: left"
          >
            <el-select
              v-model="formInline.temFormat"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="请选择" value="0"></el-option>
              <el-option label="变量模板" value="1"></el-option>
              <el-option label="普通模板" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="模板类型"
            label-width="82px"
            prop="temType"
            style="float: left"
          >
            <el-select
              v-model="formInline.temType"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="验证码" value="1"></el-option>
              <el-option label="通知" value="2"></el-option>
              <el-option label="营销推广" value="3"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div style="float: left">
          <el-form-item
            label="模板ID"
            label-width="82px"
            prop="temId"
            style="float: left"
          >
            <el-input
              v-model="formInline.temId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="审核状态"
            label-width="82px"
            prop="temStatus"
            style="float: left"
          >
            <el-select
              v-model="formInline.temStatus"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核未通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="模板内容"
            label-width="82px"
            prop="temContent"
            style="float: left"
          >
            <el-input
              v-model="formInline.temContent"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="审核时间"
            label-width="82px"
            prop="time2"
            style="float: left"
          >
            <el-date-picker
              class="input-w"
              v-model="time2"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handeTime2"
            >
            </el-date-picker>
          </el-form-item>
        </div>
      </el-form>
      <div class="boderbottom" style="padding-left: 25px; clear: both">
        <el-button
          type="primary"
          plain
          style=""
          @click.prevent="Query()"
          @keyup.enter="Query()"
          >查询</el-button
        >
        <el-button type="primary" plain style="" @click="Reload('queryForm')"
          >重置</el-button
        >
        <!-- <el-button type="primary" plain  @click="clickAddTem">创建正文模板</el-button> -->
      </div>
      <div style="margin: 10px 0">
        <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">模板审核列表</span>  -->
        <el-button type="primary" v-if="selected.length > 0" @click="muchPass"
          >批量通过</el-button
        >
        <el-button type="primary" v-if="selected.length > 0" @click="muchNoPass"
          >批量驳回</el-button
        >
        <el-button type="primary" v-if="selected.length > 0" @click="delAll"
          >批量删除</el-button
        >
        <!-- <el-button type="primary" v-if="selected.length>0" @click="muchEditTag">批量修改标签</el-button> -->
      </div>
      <div class="Templat-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <template>
          <el-table
            v-loading="tableDataObj.loading2"
            border
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            :data="tableDataObj.tableData"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          >
            <el-table-column type="selection" width="40"> </el-table-column>
            <el-table-column label="模板ID" width="100px">
              <template v-slot="scope">
                <span>{{ scope.row.temId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="用户名" width="130">
              <template v-slot="scope">
                <span>{{ scope.row.clientName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="模板名称" width="150px">
              <template v-slot="scope">
                <span>{{ scope.row.temName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="模板内容" min-width="500px">
              <template v-slot="scope">
                <span>{{ scope.row.temContent }}</span>
                <span style="color: red">{{ scope.row.prompt }}</span>
              </template>
            </el-table-column>
            <el-table-column label="模板类型" width="100px">
              <template v-slot="scope">
                <span v-if="scope.row.temType == 1">验证码</span>
                <span v-else-if="scope.row.temType == 2">通知</span>
                <span v-else-if="scope.row.temType == 3">营销推广</span>
              </template>
            </el-table-column>
            <el-table-column label="申请时间" width="150px">
              <template v-slot="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="审核状态" width="100px">
              <template v-slot="scope">
                <span v-if="scope.row.temStatus == 1">待审核</span>
                <span v-else-if="scope.row.temStatus == 2">审核通过</span>
                <span v-else-if="scope.row.temStatus == 3" style="color: red"
                  >审核未通过</span
                >
                <span v-else-if="scope.row.temStatus == 4">待复审</span>
              </template>
            </el-table-column>
            <el-table-column label="审核原因" width="200px">
              <template v-slot="scope">
                <span>{{ scope.row.checkReason }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
                            label="标签"
                        
                           >
                            <template #default="scope">
                                <div v-if="scope.row.labels">
                                     <el-tag
:disable-transitions="true" style="margin-right:5px;color:white;margin-bottom:5px" :style="{background:tagColors[index]}"  v-for="(item,index) in scope.row.labels.split(',')"  :key="index">{{ item }}</el-tag>
                                </div>   
                            </template>
                            </el-table-column> -->
            <el-table-column label="操作" width="165px" fixed="right">
              <template v-slot="scope">
                <el-button
                  v-if="
                    scope.row.temStatus == 1 ||
                    (scope.row.temStatus == 4 && roleName != 'ROLE_CSE')
                  "
                  style="color: #16a589"
                  type="text"
                  @click="handlePass(scope.$index, scope.row)"
                >
                  <el-icon><el-icon-circle-check-outline /></el-icon>
                  通过
                </el-button>
                <el-button
                  v-if="
                    scope.row.temStatus == 1 ||
                    (scope.row.temStatus == 4 && roleName != 'ROLE_CSE')
                  "
                  style="color: orange; margin-left: 5px"
                  type="text"
                  @click="handleNoPass(scope.$index, scope.row)"
                >
                  <el-icon><el-icon-circle-close-outline /></el-icon>
                  驳回
                </el-button>
                <el-button
                  style="color: red; margin-left: 5px"
                  type="text"
                  @click="delAllS(scope.row)"
                >
                  <el-icon><el-icon-delete /></el-icon>
                  删除
                </el-button>
                <!-- <el-button  
                                       v-if="scope.row.temStatus==1 || scope.row.temStatus==2 || scope.row.temStatus==4"
                                        style="color:#16A589;margin-left:5px;"
                                        type="text"
                                        @click="handleEditTag(scope.$index, scope.row)">
                                        <i class="el-icon-edit-outline"></i>
                                        修改标签
                                     </el-button>
                                     <el-button
                                        style="color:#f56c6c;margin-left:5px;"
                                        type="text"
                                        @click="handleDel(scope.$index, scope.row)">
                                        <i class="el-icon-delete"></i>
                                        删除
                                     </el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.pageTotal"
            >
            </el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>
      <!-- 编辑弹框 -->
      <el-dialog
        :title="editTemDialog_title"
        v-model="TemDialogVisible"
        width="620px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <!-- 模板规则弹窗(内层弹窗) -->
        <el-dialog v-model="dialogTemRule" append-to-body>
          <!-- 模板规则的模板内容 -->
          <templateRule></templateRule>
        </el-dialog>
        <!-- 我的模板库（内层弹窗） -->
        <el-dialog
          title="我的模板库"
          width="580px"
          style="padding-bottom: 40px"
          v-model="dialogMyTems"
          append-to-body
        >
          <el-input
            placeholder="模板名称、内容、ID"
            v-model="myTemSearch"
            style="width: 200px; margin-bottom: 10px"
          >
            <template v-slot:suffix>
              <el-icon class="el-input__icon"><el-icon-search /></el-icon>
            </template>
          </el-input>
          <!-- 表格和分页开始 -->
          <table-tem
            :tableDataObj="myTemTableDataObj"
            @handelOptionButton="handelOptionButton"
          >
            <!--分页-->
            <template v-slot:pagination>
              <el-col
                :xs="24"
                :sm="24"
                :md="24"
                :lg="24"
                class="page pageStyle"
              >
                <el-pagination
                  class="page_bottom"
                  @size-change="handleSizeChangeMyTem"
                  @current-change="handleCurrentChangeMyTem"
                  :current-page="myTemlist.currentPage"
                  :page-size="myTemlist.pageSize"
                  :page-sizes="[10, 20, 50, 100, 300]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="myTemPageTotal"
                >
                </el-pagination>
              </el-col>
            </template>
          </table-tem>
          <!-- 表格和分页结束 -->
        </el-dialog>
        <!-- 插入参数（内层弹窗） -->
        <el-dialog
          :title="dialogInsertValTitle"
          width="500px"
          v-model="dialogInsertVal"
          append-to-body
        >
          <el-form
            :model="insertVal.formData"
            :rules="insertVal.formRule"
            ref="insertVal"
            label-width="100px"
          >
            <el-form-item label="参数内容" prop="val">
              <el-select
                v-model="insertVal.formData.val"
                clearable
                style="width: 280px"
                placeholder="请选择参数内容"
              >
                <el-option label="纯数字（d）" value="d"></el-option>
                <el-option label="字母数字（w）" value="w"></el-option>
                <el-option label="金额（$）" value="$"></el-option>
                <el-option label="中文汉字（c）" value="c"></el-option>
                <el-option
                  label="日期（YYYY-MM-DD）"
                  value="YYYY-MM-DD"
                ></el-option>
                <el-option
                  label="日期（YYYY/MM/DD）"
                  value="YYYY/MM/DD"
                ></el-option>
                <el-option label="日期（MM-DD）" value="MM-DD"></el-option>
                <el-option label="日期（MM/DD）" value="MM/DD"></el-option>
                <el-option
                  label="日期时间（YYYY-MM-DD hh:mm:ss）"
                  value="YYYY-MM-DD hh:mm:ss"
                ></el-option>
                <el-option
                  label="日期时间（YYYY/MM/DD hh:mm:ss）"
                  value="MM/DD hh:mm:ss"
                ></el-option>
                <el-option
                  label="时间（hh:mm:ss）"
                  value="hh:mm:ss"
                ></el-option>
              </el-select>
            </el-form-item>
            <div
              v-if="
                insertVal.formData.val == 'd' ||
                insertVal.formData.val == 'w' ||
                insertVal.formData.val == '$' ||
                insertVal.formData.val == 'c'
              "
            >
              <el-form-item label="长度限制" prop="len">
                <el-radio-group v-model="insertVal.formData.len">
                  <el-radio label="1">可变长度</el-radio>
                  <el-radio label="2">固定长度</el-radio>
                </el-radio-group>
              </el-form-item>
              <template v-if="insertVal.formData.len == 1">
                <el-form-item label="最小长度" prop="min">
                  <el-input-number
                    v-model="insertVal.formData.min"
                    @change="handleChangeNum"
                    :min="0"
                    :max="31"
                  ></el-input-number>
                  <span>（大小范围在0-31）</span>
                </el-form-item>
                <el-form-item label="最大长度" prop="max">
                  <el-input-number
                    v-model="insertVal.formData.max"
                    @change="handleChangeNum"
                    :min="1"
                    :max="32"
                  ></el-input-number>
                  <span>（大小范围在1-32）</span>
                </el-form-item>
              </template>

              <template v-else>
                <el-form-item label="固定长度" prop="fixed">
                  <el-input-number
                    v-model="insertVal.formData.fixed"
                    @change="handleChangeNum"
                    :min="1"
                    :max="32"
                  ></el-input-number>
                  <span>（大小范围在1-32）</span>
                </el-form-item>
              </template>
            </div>
            <el-form-item style="margin-top: 50px">
              <el-button
                type="primary"
                @click="insertValOk(dialogInsertValStatus)"
                >提交</el-button
              >
              <el-button @click="dialogInsertVal = false">取消</el-button>
            </el-form-item>
          </el-form>
        </el-dialog>
        <el-form
          :model="form"
          :rules="temFormRules"
          label-width="80px"
          style="padding-right: 115px"
          ref="temForm"
        >
          <el-form-item
            label="用户名"
            prop="clientName"
            :rules="
              filter_rules({
                required: true,
                type: 'userName',
                min: 8,
                max: 15,
                message: '请输入用户名',
              })
            "
          >
            <el-input
              v-model="form.clientName"
              placeholder="输入平台客户用户名"
            ></el-input>
          </el-form-item>
          <el-form-item label="模板名称" prop="temName">
            <el-input
              v-model="form.temName"
              placeholder="10个汉字或20个数字、英文字符；只做备注识别"
            ></el-input>
          </el-form-item>
          <el-form-item label="模板类型" prop="temType">
            <el-radio-group v-model="form.temType">
              <el-radio label="1">验证码</el-radio>
              <el-radio label="2">行业通知</el-radio>
              <el-radio label="3">会员营销</el-radio>
            </el-radio-group>
          </el-form-item>
          <span class="tem-title">模板内容</span>
          <span class="tem-subTitle" @click="openTemRule">模板规则</span>
          <el-tabs
            v-model="form.temFormat"
            type="card"
            @tab-click="handleClick"
            style="padding-left: 80px"
          >
            <el-tab-pane label="普通模板" name="2" prop="temContent">
              <!-- 输入框组件 -->
              <edit-div
                ref="divInput1"
                v-model:value="form.input1.commonInputVal"
                id="input1"
              ></edit-div>
              <!-- <div class="common-template-val" ref="input1" contenteditable="true" id="input1" v-html="input1.commonInputVal" @keyup.ctrl.86="paste" @input="changeInput('input1')" @keyup="keyInput('input1',$event)"></div> -->
              <div class="tem-be-careful" style="">
                <div class="tem-font">
                  已输入<span> {{ wordCount1 }} </span>个字，最多可输入450个字
                </div>
                <div class="tem-font">
                  当前模板 预计发送条数约为<span> {{ numTextMsg1 }} </span
                  >条短信
                </div>
                <p>注意点：</p>
                <p>
                  1、模板内容中<span class="f-basic">不</span
                  >包含签名，首尾不可有【】。
                </p>
                <p>
                  2、模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。
                </p>
                <p>
                  3、如有链接，请将链接地址写于短信模板中，便于核实网站真实合法性
                </p>
              </div>
            </el-tab-pane>
            <el-tab-pane label="变量模板" name="1" prop="temContent">
              <el-button
                type="primary"
                @click="dialogMyTems = true"
                class="template-btn-1"
                plain
                >我的模板库</el-button
              >
              <el-button
                type="primary"
                @click="openValDialog"
                class="template-btn-2"
                plain
                >插入参数</el-button
              >
              <!-- 输入框组件 -->
              <edit-div
                @click="editVal"
                ref="divInput2"
                v-model:value="form.input2.commonInputVal"
                id="input2"
              ></edit-div>

              <!-- <div class="variable-template-val" ref="input2" contenteditable="true"  v-html="input2.commonInputVal" @input="changeInput('input2')" v-model="input2.commonInputVal"></div> -->

              <div class="tem-be-careful">
                <div class="tem-font">
                  已输入<span> {{ wordCount2 }} </span>个字，最多可输入450个字
                </div>
                <div class="tem-font">
                  当前模板 预计发送条数约为<span> {{ numTextMsg2 }} </span
                  >条短信
                </div>
                <p>注意点：</p>
                <p>1、模板内容不包含签名，首尾不可有【】</p>
                <p>
                  2、模板内容可手动填写或点击“我的模板库”在您已有的模板基础进行修改（重新创建，之前模板<span
                    class="f-basic"
                    >不会被替换</span
                  >）。
                </p>
                <p>
                  3、在模板中插入参数，请将鼠标光标移至填写参数处，点击“<span
                    class="f-basic"
                    >插入参数</span
                  >”，系统将根据您选择的参数内容进行<span class="f-basic"
                    >自动填写</span
                  >。
                </p>
                <p>
                  4、变量模板<span class="f-basic">最少</span>需添加<span
                    class="f-basic"
                    >1 个</span
                  >参数，<span class="f-basic">最多</span>可添加<span
                    class="f-basic"
                    >32 个</span
                  >参数，两个参数不可挨在一起，中间至少有一个字符隔开 。
                </p>
                <p>
                  5、模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。
                </p>
                <p>
                  6、如有链接，请将链接地址写于短信模板中，便于核实网站真实合法性
                </p>
              </div>
            </el-tab-pane>
          </el-tabs>
          <el-form-item label="模板标签" prop="labelId">
            <div>
              <span>① 类型</span>
              <el-checkbox-group
                v-model="form.labelId.type"
                style="margin-left: 8px; display: inline-block"
              >
                <el-checkbox
                  v-for="item in optionsTag1"
                  :key="item.labelId"
                  :label="item.labelId"
                  >{{ item.labelContent }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div>
              <span>② 退订格式</span>

              <el-radio-group v-model="form.labelId.type2">
                <el-radio
                  v-for="item in optionsTag3"
                  :key="item.labelId"
                  :label="item.labelId"
                  >{{ item.labelContent }}</el-radio
                >
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="申请说明" prop="desc">
            <el-input
              placeholder="用于我公司app用户注册时发送验证码"
              type="textarea"
              v-model="form.desc"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="addTemOk(editTemDialog_status)"
              >确 定</el-button
            >
            <el-button @click="TemDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 编辑弹框 -->
      <!-- 编辑标签弹窗 -->
      <el-dialog
        title="标签修改"
        width="550px"
        style="padding-bottom: 40px"
        v-model="dialogTag"
      >
        <el-form
          :model="tagForm"
          :rules="tagForm.formRule"
          label-width="80px"
          style="padding-right: 115px"
          ref="tagForm"
        >
          <el-form-item label="类型" prop="value1">
            <el-select
              style="width: 300px"
              v-model="tagForm.value1"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in optionsTag1"
                :key="item.labelId"
                :label="item.labelContent"
                :value="item.labelId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="行业" prop="value2">
            <el-select
              style="width: 300px"
              v-model="tagForm.value2"
              multiple
              placeholder="请选择"
            >
              <el-option
                v-for="item in optionsTag2"
                :key="item.labelId"
                :label="item.labelContent"
                :value="item.labelId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="退订格式" prop='value3'> 
                            <el-radio v-model="tagForm.value3"  v-for="item in optionsTag3" :key="item.labelId" :label="item.labelId" >{{item.labelContent}}</el-radio>
                        </el-form-item> -->
          <el-form-item label="退订格式" prop="value3">
            <el-select
              v-model="tagForm.value3"
              filterable
              placeholder="请选择标签"
              style="width: 300px"
            >
              <el-option label="请选择" value=""></el-option>
              <el-option
                v-for="(item, index) in optionsTag3"
                :key="index"
                :label="item.labelContent"
                :value="item.labelId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item style="margin-top: 30px">
            <el-button type="primary" @click="editTagOk('tagForm')"
              >提交</el-button
            >
            <el-button @click="dialogTag = false">取 消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 没有通过的弹窗 -->
      <el-dialog
        title="审核备注"
        width="450px"
        style="padding-bottom: 40px"
        v-model="dialogNoPass"
      >
        <el-form
          ref="noPassform"
          :model="formNoPass"
          :rules="formNoPass1.formRule"
          label-width="80px"
        >
          <el-form-item label="拒绝原因" prop="checkReason">
            <el-input
              type="textarea"
              v-model="formNoPass.checkReason"
              maxlength="70"
              placeholder="请输入70字以内的内容"
            ></el-input>
          </el-form-item>
          <el-form-item style="text-align: right; margin-top: 30px">
            <el-button @click="dialogNoPass = false">取消</el-button>
            <el-button type="primary" @click="noPassOk">确定</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import TableTem from '@/components/publicComponents/TableTem'
import templateRule from './components/TemplateRule'
import editDiv from './components/editDiv'
export default {
  components: {
    TableTem,
    templateRule,
    editDiv,
    ElIconCircleCheckOutline,
    ElIconCircleCloseOutline,
    ElIconDelete,
    ElIconSearch,
  },
  name: 'TemplateAudit',
  data() {
    var check = (rule, value, callback) => {
      console.log(this.tagForm.value1)
      if (value == '') {
        var status = true
        for (var i = 0; i < this.tagForm.value1.length; i++) {
          if (this.tagForm.value1[i] == 55) {
            status = false
            return callback(new Error('此处为必填项'))
          }
        }
        if (status) callback()
      } else {
        callback()
      }
    }
    return {
      roleName: '',
      time1: '',
      time2: '',
      labelName: [],
      status: '展开',
      formInline: {
        clientName: '',
        temName: '',
        temFormat: '',
        temType: '',
        temContent: '',
        temId: '',
        temStatus: '1',
        labels: '',
        checkName: '',
        beginTime1: '',
        endTime1: '',
        beginTime2: '',
        endTime2: '',
        labelIds: [],
        pageSize: 10,
        currentPage: 1,
      },
      searchInput: {
        clientName: '',
        temName: '',
        temFormat: '',
        labelIds: [],
        temType: '',
        temContent: '',
        temId: '',
        temStatus: '1',
        labels: '',
        checkName: '',
        beginTime1: '',
        endTime1: '',
        beginTime2: '',
        endTime2: '',
        pageSize: 10,
        currentPage: 1,
      },
      dialogTag: false, //修改标签弹窗(打开)
      tagForm: {
        //修改标签的弹窗表单
        value1: [],
        value2: [],
        value3: '',
        formRule: {
          // value1:[
          //     {required:true,message:"sss",trigger:'change'}
          // ],
          value3: [{ required: true, validator: check, trigger: 'change' }],
          // value2:[
          //     {required:true,message:"sss",trigger:'change'}
          // ]
        },
      },
      //所有的标签
      optionsTag1: [],
      optionsTag2: [],
      optionsTag3: [],
      editTagTemId: '', //点击表格里的 编辑标签的时候带的模板id
      editTagStatus: '1', //编辑标签的状态（1--操作栏中的编辑标签，2---批量编辑标签）

      divInputVal: '', //div输入框的内容
      tabelAlllist: {
        //------发送表格请求的对象
        param: '',
        currentPage: 1, //当前页
        pageSize: 10, //每一页条数
      },
      param: '', //搜索条件--改变监听
      searchPage: {
        //搜索的页数--改变监听
        currentPage: 1,
        pageSize: 10,
      },
      pageTotal: 0, //总共条数
      TemDialogVisible: false, //---添加模板的弹窗
      formAdd: {
        temFormat: '', //模板格式
        temContent: '', //内容
        temType: '', //模板类型
        remark: '', //备注
        temName: '', //模板名
      },
      form: {
        clientName: '',
        temName: '',
        temType: '',
        labelId: {
          type: [],
          type2: '',
        },
        remark: '',
        temId: '',

        temFormat: '2',

        input1: {
          //-----普通模板的输入框内容
          commonInputVal: '', //输入框内容
          numTextMsg: '0', //短信条数
          wordCount: '0', //字数
        },
        input2: {
          //-----变量模板的输入框内容
          commonInputVal: '', //输入框内容
          numTextMsg: '0', //短信条数
          wordCount: '0', //字数
        },
      },
      formEmpty: {
        //清空对象
        clientName: '',
        temName: '',
        temType: '',
        labelId: {
          type: [],
          type2: '',
        },
        remark: '',
        temId: '',

        temFormat: '2',
        input1: {
          //-----普通模板的输入框内容
          commonInputVal: '', //输入框内容
          numTextMsg: '0', //短信条数
          wordCount: '0', //字数
        },
        input2: {
          //-----变量模板的输入框内容
          commonInputVal: '', //输入框内容
          numTextMsg: '0', //短信条数
          wordCount: '0', //字数
        },
      },
      temFormRules: {
        //添加模板内容的验证
        temName: [
          { required: true, message: '请输入模板名', trigger: 'blur' },
          {
            pattern: /^[\u4e00-\u9fa5]{1,10}$|^[\dA-Za-z]{1,20}$/gi,
            message: '模板名为10个汉字或20个数字、英文字符!',
          },
        ],
        temType: [
          { required: true, message: '请选择模板类型', trigger: 'change' },
        ],
        temContent: [
          { required: true, message: '请输入模板内容', trigger: 'blur' },
        ],
      },
      //插入参数弹窗
      insertVal: {
        formData: {
          val: '',
          len: '1',
          min: '',
          max: '',
          fixed: '',
        },
        formRule: {
          val: [{ required: true, message: '请选择参数', trigger: 'change' }],
          min: [{ required: true, message: '请输入最小长度', trigger: 'blur' }],
          max: [{ required: true, message: '请输入最大长度', trigger: 'blur' }],
          fixed: [
            { required: true, message: '请输入固定长度', trigger: 'blur' },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        tableData: [],
        loading2: true,
        pageTotal: 0, //总共条数
      },
      myTemlist: {
        isDisable: 1,
        status: 2,
        param: '',
        currentPage: 1,
        pageSize: 10,
      },
      editVarCount: '',
      myTemSearch: '', //搜索我的模板
      myTemPageTotal: '', //总条数
      myTemTableDataObj: {
        //我的模板列表数据
        loading2: true,
        tableData: [],
        tableLabel: [
          {
            prop: 'temId',
            showName: 'ID',
            fixed: false,
          },
          {
            prop: 'temType',
            showName: '模板类型',
            fixed: false,
            width: '150',
            formatData: function (val) {
              let type = ''
              if (val == 1) {
                type = '验证码'
              } else if (val == 2) {
                type = '通知'
              } else if (val == 3) {
                type = '营销推广'
              }
              return type
            },
          },
          {
            prop: 'temName',
            showName: '模板名称',
            fixed: false,
          },
        ],
        // 表头--折叠的
        tableLabelExpand: [
          {
            prop: 'temContent',
            showName: '模板内容',
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          // isExpand:true,//是否是折叠的
          // isDefaultExpand:true,//默认打开折叠
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '选取',
            type: '',
            size: 'mini',
            optionMethod: 'getIt',
            icon: 'el-icon-success',
          },
        ],
      },
      dialogTemRule: false,
      dialogMyTems: false, //我的模板库弹窗
      dialogInsertVal: false, //插入参数弹窗
      dialogInsertValStatus: '1', //参数弹窗的状态---1：新增   0：编辑
      insertDom: '', //添加的dom
      getCurIndex: '', //光标位置
      editTemDialog_status: 1, //模板是否是新增---1为新增，0为编辑
      selected: '', //表格中的复选框
      NoPassIds: [], //没有通过的id
      dialogNoPass: false, //没有通过的弹窗--关闭
      formNoPass: {
        arr: [],
        checkReason: '',
      },
      formNoPass1: {
        formRule: {
          checkReason: [
            { required: true, message: '请输入驳回原因', trigger: 'change' },
          ],
        },
      },
      tagColors: [
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
        '#ff9900',
        '#e95d69',
        '#16a085',
      ],
    }
  },
  computed: {
    numTextMsg1: function () {
      //1短信条数
      let numMsg = 0
      //字数和短信条数的显示
      if (this.wordCount1 == 0) {
        numMsg = 0
      } else if (
        parseInt(this.wordCount1) <= 70 &&
        parseInt(this.wordCount1) > 0
      ) {
        numMsg = 1
      } else {
        numMsg = Math.floor((parseInt(this.wordCount1) - 70) / 67) + 1
      }
      return numMsg
    },
    wordCount1: function () {
      //1字数
      return this.countNum1(this.form.input1.commonInputVal)
    },
    numTextMsg2: function () {
      //2短信条数
      let numMsg = 0
      //字数和短信条数的显示
      if (this.wordCount2 == 0) {
        numMsg = 0
      } else if (
        parseInt(this.wordCount2) <= 70 &&
        parseInt(this.wordCount2) > 0
      ) {
        numMsg = 1
      } else {
        numMsg = Math.floor((parseInt(this.wordCount2) - 70) / 67) + 1
      }
      return numMsg
    },
    wordCount2: function () {
      //2字数
      return this.countNum1(this.form.input2.commonInputVal)
    },
    dialogInsertValTitle: function () {
      //插入参数弹窗的标题
      return this.dialogInsertValStatus == '1' ? '插入参数' : '编辑参数'
    },
    editTemDialog_title: function () {
      //模板内容弹窗的标题
      return this.editTemDialog_status == '1' ? '新增短信模板' : '编辑短信模板'
    },
  },
  watch: {
    searchInput: {
      //监听查询条件
      handler() {
        this.getTableData() //刷新表格
      },
      deep: true,
    },
    /**--------------------监听编辑标签弹窗是否关闭--------------- */
    dialogTag(newVal, oldVal) {
      if (newVal == false) {
        this.editTagTemId = ''
        this.tagForm.value1 = []
        this.tagForm.value2 = []
        this.tagForm.value3 = ''
      }
    },
    /**---------------监听参数弹窗是否开启关闭------------- */
    dialogInsertVal(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.insertVal.resetFields() //置空参数弹窗
        this.insertVal.formData.val = '' //初始参数弹窗
        this.insertVal.formData.len = '1'
        this.insertVal.formData.min = ''
        this.insertVal.formData.max = ''
        this.insertVal.formData.fixed = ''
      }
    },
    /**----------监听添加模板弹窗是否关闭------------- */
    TemDialogVisible(newVal, oldVal) {
      if (newVal == false) {
        Object.assign(this.form, this.formEmpty)
        // this.addVarCount=0;
        this.editVarCount = ''
        this.$refs.temForm.resetFields()
        // this.$refs.divInput2.$refs.input2.innerHTML='';
        // this.$refs.divInput1.$refs.input1.innerHTML='';
      }
    },
    /**---------监听模板的查询条件是否改变--------------- */
    param() {
      this.tabelAlllist.param = this.param
      this.tabelAlllist.currentPage = 1
      this.tabelAlllist.pageSize = 10
    },
    /**-------监听我的模板查询条件是否改变------------- */
    myTemSearch() {
      this.myTemlist.param = this.myTemSearch
      this.myTemlist.currentPage = 1
      this.myTemlist.pageSize = 10
    },
    /**-----------监听我的模板库 弹窗是否关闭(将内容清空和选择page置为初始值) */
    dialogMyTems() {
      this.myTemlist.param = ''
      this.myTemlist.currentPage = 1
      this.myTemlist.pageSize = 10
    },
  },
  methods: {
    renderHeader(h) {
      //表头事件
      return (
        <div style="margin:0;padding:0;line-height: 23px;width:inherit;height: 23px;">
          <el-button
            nativeOnClick={this.expandOpen}
            style="plain:false;background:none;border:none;margin:0;padding:0;list-style:none;"
          >
            {this.status}
          </el-button>
        </div>
      )
    },
    expandOpen() {
      console.log(1)
      var arr = document.querySelectorAll('.el-table__expand-icon')
      console.log(arr.length)
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].className.length < 25) {
          arr[i].onclick = function () {}
        } else {
          arr[i].onclick = function () {}
        }
      }
      if (this.status == '展开') {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length < 25) {
            console.log(i)
            arr[i].click()
          }
        }
        this.status = '折叠'
      } else {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length > 25) {
            arr[i].click()
          }
        }
        this.status = '展开'
      }
    },
    push(a, b, c) {
      if (this.tableDataObj.tableStyle.isDialog == true) {
        $emit(this, 'routePushDialog', {
          rowData: a,
          currentData: b,
          temName: c,
        })
      } else {
        $emit(this, 'routePush', { rowData: a, currentData: b, temName: c })
      }
    },
    handeTime: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime1 = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime1 = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime1 = ''
        this.formInline.endTime1 = ''
      }
    },
    handeTime2: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime2 = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime2 = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime2 = ''
        this.formInline.endTime2 = ''
      }
    },
    Query() {
      for (let key in this.formInline) {
        this.searchInput[key] = this.formInline[key]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
      // console.log(this.searchInput)
      // console.log(typeof this.searchInput.labelIds)
    },
    Reload(formName) {
      this.formInline.labelIds = []
      this.time1 = ''
      this.time2 = ''
      this.formInline.beginTime1 = ''
      this.formInline.beginTime2 = ''
      this.formInline.endTime1 = ''
      this.formInline.endTime2 = ''

      this.$refs[formName].resetFields() //清空查询表单
      for (let k in this.formInline) {
        this.searchInput[k] = this.formInline[k]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.tabelAlllist.pageSize = 10
      this.tabelAlllist.currentPage = 1
    },
    handleSelectionChange(val) {
      this.selected = val
    },
    muchEditTag() {
      //批量修改标签
      this.editTagStatus = 2
      let arr = []
      let flag = 1
      this.selected.forEach((item, index) => {
        if (item.temStatus == 3) {
          flag = 0
          this.$message({
            message: '"选择的模板中有已经驳回的，不能修改标签，请重新选择"',
            type: 'warning',
          })
          return false
        }
        arr.push(item.temId)
      })
      if (flag == 1) {
        this.dialogTag = true
      }
    },
    muchNoPass() {
      //批量驳回
      let arr = []
      let flag = 1
      this.selected.forEach((item, index) => {
        if (item.temStatus == 2) {
          flag = 0
          this.$message({
            message: '"选择的模板中有已经通过的，不能再次驳回，请重新选择"',
            type: 'warning',
          })
          return false
        }
        arr.push(item.temId)
      })
      //发送请求
      if (flag == 1) {
        this.formNoPass.arr = arr
        this.formNoPass.checkReason = ''
        this.formNoPass.checkReason = ''
        this.dialogNoPass = true //打开弹窗
      }
    },
    muchPass() {
      //批量通过
      let arr = []
      let flag = 1
      this.selected.forEach((item, index) => {
        if (item.temStatus == 3) {
          flag = 0
          this.$message({
            message: '"选择的模板中有已经驳回的，不能再次通过，请重新选择"',
            type: 'warning',
          })
          return false
        }
        if (item.temStatus == 2) {
          flag = 0
          this.$message({
            message: '"选择的模板中有已经通过的，不能再次通过，请重新选择"',
            type: 'warning',
          })
          return false
        }
        arr.push(item.temId)
      })

      //发送请求
      if (flag == 1) {
        this.$confirms.confirmation(
          'post',
          '确认审核通过模板吗？',
          window.path.smcs + 'templateaudit/auditPass',
          { arr: arr },
          (res) => {
            this.getTableData()
          }
        )
      }
    },
    //查询所有的标签
    searchTags() {
      window.api.post(
        window.path.smcs + 'templateaudit/selectAllLabel',
        {},
        (res) => {
          // console.log('tags',res)
          let tags = res
          for (let i = 0; i < tags.length; i++) {
            if (tags[i].attributeName == '类型') {
              this.optionsTag1 = tags[i].list
            } else if (tags[i].attributeName == '行业') {
              this.optionsTag2 = tags[i].list
            } else if (tags[i].attributeName == '退订格式') {
              this.optionsTag3 = tags[i].list
            }
          }
        }
      )
    },
    /**--------------------操作栏的操作-------------- */
    handleEdit(index, val) {
      this.TemDialogVisible = true //打开弹窗
      this.editTemDialog_status = 0 //模板状态
      //编辑框里赋值
      let rowData = val
      this.form.clientName = rowData.clientName
      this.form.temName = rowData.temName
      this.form.temType = rowData.temType
      this.form.remark = rowData.remark

      if (rowData.temFormat == 1) {
        //全文模板
        this.form.temFormat = '2'
        this.form.input1.commonInputVal = rowData.temContent
      } else if (rowData.temFormat == 2) {
        //变量模板
        this.form.temFormat = '1'
        if (rowData.initialContent) {
          this.form.input2.commonInputVal = rowData.initialContent
        } else {
          this.form.input2.commonInputVal = rowData.temContent
        }
      }
      this.form.temId = rowData.temId
    },
    handlePass(index, val) {
      let arr = []
      arr.push(val.temId)
      //发送请求
      this.$confirms.confirmation(
        'post',
        '确认审核通过模板吗？',
        window.path.smcs + 'templateaudit/auditPass',
        { arr: arr },
        (res) => {
          this.getTableData()
        }
      )
    },
    handleNoPass(index, val) {
      let arr = []
      arr.push(val.temId)
      this.formNoPass.arr = arr
      val.prompt != ''
        ? (this.formNoPass.checkReason = val.prompt)
        : (this.formNoPass.checkReason = '')
      this.dialogNoPass = true //打开弹窗
    },
    //确认不通过
    noPassOk() {
      //发送请求
      this.$confirms.confirmation(
        'post',
        '确认驳回模板吗？',
        window.path.smcs + 'templateaudit/auditNotPass',
        this.formNoPass,
        (res) => {
          this.getTableData()
          this.dialogNoPass = false
        }
      )
    },
    handleEditTag(index, val) {
      //编辑标签
      if (val.labelId) {
        let arrLabel = val.labelId.split(',')
        let option1 = []
        let option2 = []
        let option3 = []
        let labelValue1 = []
        let labelValue2 = []
        let labelValue3 = ''
        for (let k = 0; k < this.optionsTag1.length; k++) {
          option1.push(this.optionsTag1[k].labelId)
        }
        for (let k = 0; k < this.optionsTag2.length; k++) {
          option2.push(this.optionsTag2[k].labelId)
        }
        for (let k = 0; k < this.optionsTag3.length; k++) {
          option3.push(this.optionsTag3[k].labelId)
        }
        //回显类型 标签
        for (var k = 0; k < arrLabel.length; k++) {
          for (var i = 0; i < option1.length; i++) {
            if (arrLabel[k] == option1[i]) {
              let label = parseInt(arrLabel[k])
              labelValue1.push(label)
            }
          }
        }
        this.tagForm.value1 = labelValue1
        // this.tagForm.value1=[]
        //回显行业 标签
        for (let k = 0; k < arrLabel.length; k++) {
          for (var i = 0; i < option2.length; i++) {
            if (arrLabel[k] == option2[i]) {
              let label = parseInt(arrLabel[k])
              labelValue2.push(label)
            }
          }
        }
        this.tagForm.value2 = labelValue2
        // this.tagForm.value2=[]
        //回显退订格式
        for (let k = 0; k < arrLabel.length; k++) {
          if (option3.indexOf(parseInt(arrLabel[k])) > -1) {
            labelValue3 = parseInt(arrLabel[k])
          }
        }
        this.tagForm.value3 = labelValue3
        // this.tagForm.value3=''
      }
      this.editTagStatus = 1
      this.editTagTemId = val.temId
      this.dialogTag = true
    },
    handleDel(index, val) {
      let temId = val.temId
      //发送请求
      this.$confirms.confirmation(
        'post',
        '确认删除该模板吗？',
        window.path.smcs + 'templateaudit/delete',
        { temId: temId },
        (res) => {
          this.getTableData()
        }
      )
    },
    editTagOk(val) {
      //添加标签--提交
      this.$refs[val].validate((valid) => {
        if (val == 'tagForm') {
          //提交form1表单
          if (valid) {
            let arr = []
            let arrOk = ''
            let selectBox = []
            let selected = []
            console.log(this.tagForm.value1)
            arr = this.tagForm.value1.concat(this.tagForm.value2)
            if (this.tagForm.value3) {
              arr.push(this.tagForm.value3)
            }
            arrOk = arr.join(',')
            if (this.editTagStatus == 1) {
              //单个编辑标签
              selectBox.push(this.editTagTemId)
            } else if (this.editTagStatus == 2) {
              //批量修改标签
              selected = this.selected
              for (let k = 0; k < selected.length; k++) {
                selectBox.push(selected[k].temId)
              }
            }
            //发送请求
            if (arrOk == '') {
              this.$confirms.confirmation(
                'post',
                '确认修改标签吗？',
                window.path.smcs + 'templateaudit/updateLabel',
                { arr: selectBox, labelId: '' },
                (res) => {
                  this.getTableData()
                  this.dialogTag = false
                }
              )
            } else {
              console.log(selectBox, arrOk)
              this.$confirms.confirmation(
                'post',
                '确认修改标签吗？',
                window.path.smcs + 'templateaudit/updateLabel',
                { arr: selectBox, labelId: arrOk },
                (res) => {
                  this.getTableData()
                  this.dialogTag = false
                }
              )
            }
          } else {
            return false
          }
        }
      })
    },
    //置空表单--刷新表格===一般在“操作”后刷新表格使用
    emptySearch() {
      this.param = '' /** 查询条件置空,表单进行刷新**/
      this.tabelAlllist.param = this.param
      this.tabelAlllist.currentPage = 1
      this.tabelAlllist.pageSize = 10
      this.getTableData()
    },
    clickAddTem() {
      this.TemDialogVisible = true
      this.editTemDialog_status = 1
      //点击新增模板的时候-----清空模板内容
      this.form.input1.commonInputVal = ''
      this.form.input2.commonInputVal = ''
    },
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      let formDatas = this.searchInput
      console.log(formDatas)
      window.api.post(window.path.smcs + 'templateaudit/page', formDatas, (res) => {
        console.log(res.records)
        this.tableDataObj.tableData = res.records
        this.tableDataObj.loading2 = false
        this.tableDataObj.pageTotal = res.total
      })
    },
    /**-----------------列表展示我的模板------------- */
    getMyTemTableData() {
      //获取列表数据
      this.myTemTableDataObj.loading2 = true
      let formDatas = this.myTemlist
      // window.api.post(window.path.smcs+'templateaudit/page',formDatas,res=>{
      //     this.myTemTableDataObj.tableData = res.records;
      //     this.myTemTableDataObj.loading2 = false;
      //     this.myTemPageTotal=res.total;
      // })
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.searchInput.pageSize = size
      this.tabelAlllist.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.searchInput.currentPage = currentPage
      this.tabelAlllist.currentPage = currentPage
      this.getTableData()
    },
    handleSizeChangeMyTem(size) {
      //分页每一页的有几条
      this.searchInput.pageSize = size
      this.getTableData()
    },
    handleCurrentChangeMyTem: function (currentPage) {
      //分页的第几页
      this.searchInput.currentPage = currentPage
      console.log(this.myTemlist)
      this.getTableData()
    },
    openValDialog() {
      //点击“插入参数”按钮，打开弹窗--添加参数的弹窗
      this.dialogInsertVal = true
      this.dialogInsertValStatus = 1
      this.$refs.divInput2.$refs.input2.focus() //自动聚焦
      let selection = getSelection()
      // 设置最后光标对象
      this.getCurIndex = selection.getRangeAt(0)
    },
    insertValOk(val) {
      //提交参数--  val=0为编辑   val=1为新增
      this.$refs.insertVal.validate((valid) => {
        if (valid) {
          let showVal = '' //显示在输入框中的参数
          if (
            this.insertVal.formData.val == '$' ||
            this.insertVal.formData.val == 'c' ||
            this.insertVal.formData.val == 'd' ||
            this.insertVal.formData.val == 'w'
          ) {
            if (this.insertVal.formData.len == 1) {
              if (this.insertVal.formData.min > this.insertVal.formData.max) {
                this.$message({
                  message: '"最小长度"不能大于"最大长度"',
                  type: 'warning',
                })
                return false
              } else if (
                this.insertVal.formData.min == this.insertVal.formData.max
              ) {
                this.$message({
                  message: '"最小长度"不能等于"最大长度"',
                  type: 'warning',
                })
                return false
              } else {
                showVal =
                  this.insertVal.formData.val +
                  this.insertVal.formData.min +
                  '-' +
                  this.insertVal.formData.max
              }
            } else {
              showVal =
                this.insertVal.formData.val + this.insertVal.formData.fixed
            }
          } else {
            showVal = this.insertVal.formData.val
          }
          if (val == 1) {
            //新增--参数
            let regx = /<[^>]+>([^<]+)<\/[^>]+>/g
            let result1 = this.form.input2.commonInputVal.match(regx) //取出含标签的内容
            //判断参数个数--不能超过32
            let countNum = []
            let addVarCount = 1
            if (result1) {
              if (result1.length >= 32) {
                this.$message({
                  message: '"添加的参数个数不能超过32个"',
                  type: 'warning',
                })
                return false
              }
              result1.forEach(
                (item, index) =>
                  countNum.push(item.substr(item.indexOf('|') - 1, 1)) //获取所有的var变量后的数字
              )
            }
            if (countNum.length > 0) {
              let maxNum = Math.max.apply(Math, countNum)
              addVarCount = maxNum + 1
            } else {
              addVarCount = 1
            }
            let varCount = addVarCount
            let insertVal =
              '<span contenteditable="false" data=' +
              showVal +
              ' constVar=' +
              varCount +
              '  class="text-adaNum">{var' +
              varCount +
              '|' +
              showVal +
              '}</span>'
            let selection = window.getSelection
              ? window.getSelection()
              : document.selection
            if (this.getCurIndex) {
              // 存在最后光标对象，选定对象清除所有光标并添加最后光标还原之前的状态
              selection.removeAllRanges()
              selection.addRange(this.getCurIndex)
            }
            this.insertHtmlAtCaret(insertVal)
          } else {
            //编辑--参数
            if (this.insertDom) {
              this.insertDom.setAttribute('data', showVal)
              this.insertDom.innerHTML =
                '{var' + this.editVarCount + '|' + showVal + '}'
            }
          }
          this.form.input2.commonInputVal =
            this.$refs.divInput2.$refs.input2.innerHTML //值更改 --方便计数
          this.dialogInsertVal = false //关闭弹窗
        } else {
          return false
        }
      })
    },
    addTemOk(val) {
      //--------------添加编辑模板--确定
      this.$refs.temForm.validate((valid) => {
        if (valid) {
          let addForm = {} //接收传参的对象
          if (this.form.temFormat == '2') {
            //全文模板
            addForm.temContent = this.form.input1.commonInputVal
          } else if (this.form.temFormat == '1') {
            //变量模板
            addForm.temContent = this.form.input2.commonInputVal
          }
          let regx = /<[^>]+>([^<]+)<\/[^>]+>/g
          let result1 = []
          result1 = addForm.temContent.match(regx) //取出含标签的内容
          if (this.form.temFormat == '1') {
            //---------限制参数个数
            if (!result1) {
              this.$message({
                message: '"变量模板至少添加一个参数"',
                type: 'warning',
              })
              return false
            } else if (result1.length > 32) {
              this.$message({
                message: '"变量模板参数不能超过32个"',
                type: 'warning',
              })
              return false
            }
          }
          // let splitInVar= this.arrSplit(addForm.initialContent);
          // console.log(splitInVar)
          // if(splitInVar.length){
          //             for(var k=1;k<splitInVar.length;k++){
          //                 if(splitInVar[k].length==0){
          //                     this.$message({
          //                             message: '"变量模板内容 参数中间至少有一个字符"',
          //                             type: 'warning'
          //                     });
          //                     return false;
          //                 }
          //             }
          // }
          if (result1) {
            let result = []
            result1.forEach((item, index) =>
              result.push(
                item.match(/>([^<]+)</)[1].replace(/var[1-9]\d?[|]/gi, '')
              )
            ) //数组形式取出标签里面的内容
            addForm.text = result.join(',') //数组转换成字符串
          }
          addForm.temFormat = this.form.temFormat
          addForm.temName = this.form.temName
          addForm.clientName = this.form.clientName
          addForm.temType = this.form.temType
          addForm.remark = this.form.remark
          let labelArr = this.form.labelId.type
          if (this.form.labelId.type2) {
            labelArr.push(this.form.labelId.type2)
          }
          addForm.labelId = labelArr.join(',')
          if (val == 1) {
            //新增模板
            //发送请求
            this.$confirms.confirmation(
              'post',
              '确认新增模板？',
              window.path.smcs + 'templateaudit/addTemplate',
              addForm,
              (res) => {
                this.TemDialogVisible = false //关闭弹窗
              }
            )
          } else if (val == 0) {
            //编辑模板
            //发送请求
            addForm.temId = this.form.temId
            this.$confirms.confirmation(
              'post',
              '确认编辑模板？',
              window.path.smcs + 'templateaudit/updateTemplate',
              addForm,
              (res) => {
                this.TemDialogVisible = false //关闭弹窗
              }
            )
          }
        } else {
          return false
        }
      })
    },
    //编辑参数弹窗--打开
    editVal(e) {
      let dom = e.target
      let val = dom.getAttribute('data')
      this.editVarCount = dom.getAttribute('constVar')
      if (val) {
        this.insertDom = dom
        console.log(this.insertDom)
        let regVar1 = /[d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)/g //可变长度
        let regVar2 = /[d|w|$|c]([1-9]\d?)/g //固定长度
        let regFirstVar = /[d|w|$|c]/g //参数首字母匹配
        let regIsNum = /[^0-9]/g //验证数字
        let firstVar //变量的首字母
        let fixLen //固定长度的数字
        let variableLen1 //可变长度1
        let variableLen2 //可变长度2
        let formatDate //日期格式
        if (regVar2.test(val)) {
          //固定长度
          firstVar = val.match(regFirstVar)[0] //首字母
          fixLen = val.replace(/[^0-9]/gi, '') //固定位数的数字
          this.insertVal.formData.val = firstVar
          this.insertVal.formData.len = '2' //固定长度-单选
          this.insertVal.formData.fixed = fixLen //固定的长度
          this.dialogInsertVal = true //插入参数弹窗--打开
          this.dialogInsertValStatus = 0 //弹窗是编辑
        } else if (regVar1.test(val)) {
          //可变长度
          firstVar = val.match(regFirstVar)[0] //首字母
          variableLen1 = val.split('-')[0].replace(/[^0-9]/gi, '') //可变长度1
          variableLen2 = val.split('-')[1].replace(/[^0-9]/gi, '') //可变长度2
          this.insertVal.formData.val = firstVar
          this.insertVal.formData.len = '1' //可变长度-单选
          this.insertVal.formData.min = variableLen1 //可变长度的min
          this.insertVal.formData.max = variableLen2 //可变长度的max
          this.dialogInsertVal = true //插入参数弹窗--打开
          this.dialogInsertValStatus = 0 //弹窗是编辑
        } else {
          this.insertVal.formData.val = val
        }
        this.dialogInsertVal = true //插入参数弹窗--打开
        this.dialogInsertValStatus = 0 //弹窗是编辑
      }
    },
    /**---------插入数据----------- */
    insertHtmlAtCaret(html) {
      let sel, range
      if (window.getSelection) {
        // IE9 and non-IE
        sel = window.getSelection()
        if (sel.getRangeAt && sel.rangeCount) {
          range = sel.getRangeAt(0)
          range.deleteContents()
          // Range.createContextualFragment() would be useful here but is
          // non-standard and not supported in all browsers (IE9, for one)
          let el = document.createElement('div')
          el.innerHTML = html
          let frag = document.createDocumentFragment(),
            node,
            lastNode
          while ((node = el.firstChild)) {
            lastNode = frag.appendChild(node)
          }
          range.insertNode(frag) //设置选择范围的内容为插入的内容
          // Preserve the selection
          if (lastNode) {
            range = range.cloneRange()
            range.setStartAfter(lastNode) //设置光标位置为插入内容的末尾
            range.collapse(true) //移动光标位置到末尾
            sel.removeAllRanges() //移出所有选区
            sel.addRange(range) //添加修改后的选区
          }
        }
      } else if (document.selection && document.selection.type != 'Control') {
        // IE < 9
        document.selection.createRange().pasteHTML(html)
      }
    },
    getCaretPosition(oField) {
      //获取光标位置
      var iCaretPos = 0
      var doc = oField.ownerDocument || oField.document
      var win = doc.defaultView || doc.parentWindow
      var sel
      if (typeof win.getSelection != 'undefined') {
        sel = win.getSelection()
        if (sel.rangeCount > 0) {
          var range = win.getSelection().getRangeAt(0)
          var preCaretRange = range.cloneRange()
          preCaretRange.selectNodeContents(oField)
          preCaretRange.setEnd(range.endContainer, range.endOffset)
          iCaretPos = preCaretRange.toString().length
        }
      } else if ((sel = doc.selection) && sel.type !== 'Control') {
        var textRange = sel.createRange()
        var preCaretTextRange = doc.body.createTextRange()
        preCaretTextRange.moveToElementText(oField)
        preCaretTextRange.setEndPoint('EndToEnd', textRange)
        iCaretPos = preCaretTextRange.text.length
      }
      return iCaretPos
    },
    //监听字数
    countNum1(val) {
      let len = 0 //字数
      let regVar1 = /[{](var[1-9]\d?[|])[d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g //可变长度
      let regVar2 = /[{](var[1-9]\d?[|])[d|w|$|c]([1-9]\d?)[}]/g //固定长度
      if (regVar1.test(val) || regVar2.test(val)) {
        let regVar1Arr = val.match(regVar1)
        let regVar2Arr = val.match(regVar2)
        if (regVar1Arr) {
          //如果是可变长度类型的参数，要取出长度
          for (let i = 0; i < regVar1Arr.length; i++) {
            let variableLen = Number(
              regVar1Arr[i].split('-')[1].replace(/[^0-9]/gi, '')
            )
            len += variableLen
          }
        }
        if (regVar2Arr) {
          for (let i = 0; i < regVar2Arr.length; i++) {
            let fixedLen = Number(regVar2Arr[i].match(/\d+/g)[1]) //字符串中找出数字，组中的第二个数字为固定长度
            len += fixedLen
          }
        }
        val = val.replace(regVar1, '').replace(regVar2, '')
      }
      val = val
        .replace(/<br>/g, '')
        .replace(/\n/g, '')
        .replace(/\r/g, '')
        .replace(/\{/gi, '')
        .replace(/\}/gi, '')
        .replace(/<\/?[^>]*>/g, '')
        .replace(/&nbsp;/gi, ' ')
        .replace(/var[1-9]\d?[|]/gi, '')
      len += val.length
      return len
    },
    //分割模板内容的参数 验证参数间是否有间隔
    arrSplit(contentString) {
      var arrs = []
      var a = contentString.split(/<[^>]+>([^<]+)<\/[^>]+>/g)
      if (a) {
        for (var i = 0; i < a.length - 1; i++) {
          if (i != 0) {
            arrs[arrs.length] = a[i].split(/<[^>]+>([^<]+)<\/[^>]+>/g)[0]
          }
        }
      }
      return arrs
    },
    //监听字数
    countNum(dom) {
      let recentVal = '' //目前的内容
      let len = 0 //字数
      let smsNum = 0 //短信条数
      recentVal = this.$refs[dom].innerText
      let regVar1 = /[{][d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g //可变长度
      let regVar2 = /[{][d|w|$|c]([1-9]\d?)[}]/g //固定长度
      if (regVar1.test(recentVal) || regVar2.test(recentVal)) {
        let regVar1Arr = recentVal.match(regVar1)
        let regVar2Arr = recentVal.match(regVar2)
        if (regVar1Arr) {
          //如果是长度类型的参数，要取出长度
          for (let i = 0; i < regVar1Arr.length; i++) {
            let variableLen = Number(
              regVar1Arr[i].split('-')[1].replace(/[^0-9]/gi, '')
            )
            len += variableLen
          }
        }
        if (regVar2Arr) {
          for (let i = 0; i < regVar2Arr.length; i++) {
            let fixedLen = Number(regVar2Arr[i].replace(/[^0-9]/gi, ''))
            len += fixedLen
          }
        }
        recentVal = recentVal.replace(regVar1, '').replace(regVar2, '')
      }
      recentVal = recentVal
        .replace(/<br>/g, '')
        .replace(/\n/g, '')
        .replace(/\r/g, '')
        .replace(/\{/gi, '')
        .replace(/\}/gi, '')
        .replace(/<\/?[^>]*>/g, '')
        .replace(/&nbsp;/gi, ' ')
      if (recentVal.length >= 450) {
        //超过450个字字数截取
        this.$refs[dom].innerHTML = recentVal.substr(0, 450) //截取后文字内容放在里面--由于有插入的标签内容，所以使用innerHTML
        this.keepLastIndex(this.$refs[dom]) //光标放在最后
        len = 450
      } else {
        len += recentVal.length
      }
      //字数和短信条数的显示
      if (len == 0) {
        smsNum = 0
      } else if (parseInt(len) <= 70 && parseInt(len) > 0) {
        smsNum = 1
      } else {
        smsNum = Math.floor((parseInt(len) - 70) / 67) + 1
      }
      if (dom == 'input1') {
        this.form.input1.wordCount = len
        this.form.input1.numTextMsg = smsNum
      } else if (dom == 'input2') {
        this.form.input2.wordCount = len
        this.form.input2.numTextMsg = smsNum
      }
      let num = {}
      num.wordCount = len
      num.numTextMsg = smsNum
      return num
    },
    changeInput(input) {
      //监听输入框---手动输入内容变化
      // this.$refs[input].innerHTML
      event.target.innerText.replace(/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/gi, '')
      this.countNum(input) //计数
    },
    handleChangeNum(value) {
      //改变数字大小
      console.log(value)
    },
    //--------------操作栏的按钮--------------------
    handelOptionButton: function (val) {
      if (val.methods == 'editTem') {
        //----------------编辑模板
        this.TemDialogVisible = true //打开弹窗
        this.editTemDialog_status = 0 //模板状态
        //编辑框里赋值
        let rowData = val.row
        this.form.name = rowData.temName
        this.form.temFormat = rowData.temFormat
        if (rowData.temFormat == 2) {
          //全文模板
          this.form.input1.commonInputVal = rowData.temContent
        } else if (rowData.temFormat == 1) {
          //变量模板
          if (rowData.initialContent) {
            this.form.input2.commonInputVal = rowData.initialContent
          } else {
            this.form.input2.commonInputVal = rowData.temContent
          }
        }
        this.form.temType = rowData.temType
        this.form.desc = rowData.remark
        this.form.temId = rowData.temId
        console.log(this.form)
      } else if (val.methods == 'delTem') {
        //----------------删除模板
        let temId = val.row.temId
        this.$confirms.confirmation(
          'post',
          '此操作将永久删除该数据, 是否继续？',
          window.path.cpas + 'consumersmstemplate/delete',
          { temId: temId },
          (res) => {
            this.emptySearch()
          }
        )
      } else if (val.methods == 'getIt') {
        //----------------我的模板库里的“选取模板”
        let temContent = val.row.temContent //获取模板内容
        this.form.input2.commonInputVal = temContent
        this.dialogMyTems = false //关闭弹窗
      }
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    openTemRule() {
      //点击模板规则
      this.dialogTemRule = true
    },
    //提交
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (val == 'insertVal') {
          if (valid) {
            this.dialogInsertVal = false
          } else {
            console.log('error submit!!')
            return false
          }
        }
      })
    },
    //获取全部标签
    getpassLabels() {
      window.api.post(window.path.smcs + 'templateaudit/getAll', {}, (res) => {
        console.log(res.data.data)
        this.labelName = res.data.data
      })
    },
    //批量删除
    delAll() {
      let arr = []
      this.selected.forEach((item, index) => {
        arr.push(item.temId)
      })
      //批量删除
      this.$confirms.confirmation(
        'get',
        '此操作将永久删除该数据, 是否批量删除？',
        window.path.omcs + 'template/batchDelete/' + arr.join(','),
        {},
        () => {
          this.getTableData()
        }
      )
    },
    //单个批量删除
    delAllS(val) {
      this.$confirms.confirmation(
        'get',
        '此操作将永久删除该数据, 是否单个删除？',
        window.path.omcs + 'template/batchDelete/' + val.temId,
        {},
        () => {
          this.getTableData()
        }
      )
    },
  },
  mounted() {
    this.getTableData()
    this.getMyTemTableData()
    this.searchTags()
    this.getpassLabels()
    // window.api.post(/templateaudit/getRole
    window.api.post(window.path.smcs + 'templateaudit/getRole', {}, (res) => {
      this.roleName = res
    })
    console.log('formInline', this.formInline)
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
  },
  emits: ['routePushDialog', 'routePush'],
}
</script>

<style scoped>
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Templat-box {
  padding: 20px;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Templat-matter > div {
  height: 26px;
  line-height: 26px;
}
.Templat-set {
  color: #0066cc;
}
.Templat-creat {
  margin-top: 20px;
}
.Templat-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Templat-list-header {
  position: absolute;
  font-weight: bold;
  left: 0px;
  top: 12px;
}
.Templat-table {
  padding-bottom: 40px;
}
.Templat-search-fun {
  position: relative;
  height: 40px;
  margin-top: 20px;
  padding-bottom: 6px;
}
.Templat-search-box {
  position: absolute;
  right: 0px;
}
.tem-font {
  font-size: 12px;
}
.tem-font span {
  color: red;
}
.tem-be-careful {
  font-size: 12px;
  color: #999;
  line-height: 20px;
  margin: 10px 0;
}
.tem-title {
  position: absolute;
  left: 32px;
  top: 250px;
}
.tem-title:before {
  position: absolute;
  content: '*';
  color: #f56c6c;
  left: -10px;
}
.tem-subTitle {
  position: absolute;
  left: 32px;
  top: 285px;
  color: #16a589;
  cursor: pointer;
}
.template-btn-1 {
  position: absolute;
  right: -100px;
  top: 12px;
}
.template-btn-2 {
  position: absolute;
  right: -89px;
  top: 57px;
}
.common-template-val,
.variable-template-val {
  min-height: 100px;
  _height: 100px;
  border-right: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  outline: none;
  word-wrap: break-word;
  padding: 4px 10px;
  color: #000;
}
.f-basic {
  color: #16a589;
  font-weight: bold;
}
</style>

<style>
.text-adaNum {
  color: #16a589;
  cursor: pointer;
}
.TemDialog .el-tabs__item {
  height: 32px;
  line-height: 32px;
}
.TemDialog .el-tabs__header {
  margin: 0px;
}
.TemDialog .el-tabs__content {
  overflow: inherit;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.el-tag--mini {
  height: auto;
  word-wrap: break-all;
  white-space: normal;
}
.el-select__tags-text {
  word-wrap: break-all;
  white-space: normal;
}
.el-icon-close {
  display: inline-block;
}
.el-table--small th {
  background: #f5f5f5;
}
</style>
