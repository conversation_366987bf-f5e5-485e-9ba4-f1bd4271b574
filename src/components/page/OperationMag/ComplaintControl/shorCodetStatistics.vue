<template>
  <div class="container_left">
    <div class="Top_title" style="display: flex;">
      <span
        style="
          display: flex;
          align-items: center;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><el-icon><ArrowLeft /></el-icon> 返回</span
      >|
      <span style="margin-left: 5px;">短链点击详情</span>
    </div>
    <div class="short-message-recording-type">
      <el-form
        ref="shortFrom"
        :inline="true"
        :model="shortFrom"
        label-width="86px"
        class="query-frame"
      >
        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="shortFrom.mobile"
            placeholder="请输入手机号"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="打开时间" label-width="80px" prop="time">
          <el-date-picker
            class="input-w"
            v-model="shortFrom.time"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            :clearable="false"
            @change="getTimeOperating"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="querySending()"
            >查 询</el-button
          >
          <el-button type="primary" plain @click="resetSending('shortFrom')"
            >重 置</el-button
          >
          <!-- <el-button type="primary" @click="exportNums1()">导出数据</el-button> -->
          <!-- <el-button type="primary" v-if="specificTime==2" @click="exportNums()">导出数据</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <div style="padding:10px;">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
        style="width: 100%"
      >
        <el-table-column label="手机号">
          <template v-slot="scope">
            <div v-if="scope.row.mobile">
              <span v-if="pIndex == scope.$index">{{
                scope.row.mobile || "-"
              }}</span>
              <span
                v-else
                style="cursor: pointer; color: #16a589"
                @click="phoneClickTable(scope.$index, scope.row)"
                >{{ scope.row.maskMobile || "-" }}</span
              >
            </div>
            <div v-else>
              <span>未知</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="短链">
          <template v-slot="scope">{{ scope.row.shortCode }}</template>
        </el-table-column>
        <el-table-column label="UA">
          <template v-slot="scope">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row.userAgent"
              placement="top"
            >
              <div class="text">
                {{ scope.row.userAgent }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="ip">
          <template v-slot="scope">{{ scope.row.ip }}</template>
        </el-table-column>
        <el-table-column label="打开时时间">
          <template v-slot="scope">
            {{ moment(scope.row.clickTime).format('YYYY-MM-DD HH:mm:ss') }}
          </template>
        </el-table-column>
      </el-table>
      <div class="paginationBox">
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="1"
          :page-size="formData.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.totalRow"
        >
        </el-pagination>
      </div>
    </div>
    <!-- <el-dialog
                  title="手机验证"
                  v-model="exportFlag"
                  width="40%"
                  top="30vh"
              >  
                  <DownLoadExport ref="ExportChild" :formData1='formData' productType='11' :isDownload='formData.isDownload' :phoneList="phoneList"/>
              </el-dialog> -->
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'shorCodetStatistics',
  data() {
    return {
      isFirstEnter: false,
      phoneList: [],
      pIndex: -1,
      exportFlag: false,
      shortFrom: {
        mobile: '',
        time: [],
        beginTime: '',
        endTime: '',
        shortCode: '',
        currentPage: 1,
        pageSize: 10,
      },
      formData: {
        mobile: '',
        time: [],
        beginTime: '',
        endTime: '',
        shortCode: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
      },
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.formData.shortCode = this.$route.query.shortCode
      this.shortFrom.shortCode = this.$route.query.shortCode
      this.getList()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.formData.shortCode = this.$route.query.shortCode
        this.shortFrom.shortCode = this.$route.query.shortCode
        this.getList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  // activated() {
  //     this.formData.shortCode = this.$route.query.shortCode;
  //     this.shortFrom.shortCode = this.$route.query.shortCode;
  //     this.getList();
  // },
  // created() {
  // },
  methods: {
    goBack() {
      console.log(this.$route.query.id, 'this.$route.query.id')
      this.$router.push({
        path: '/shortStatistics',
        query: {
          id: this.$route.query.id,
        },
      })
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.shortFrom.beginTime = this.moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.shortFrom.endTime = this.moment(val[1]).format(
          'YYYY-MM-DD 23:59:59'
        )
      } else {
        this.shortFrom.beginTime = ''
        this.shortFrom.endTime = ''
      }
    },
    getList() {
      this.pIndex = -1;
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.slms + 'operator/shortLinkClick/page',
        this.formData,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.totalRow = res.data.total
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
        }
      )
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      window.api.post(
        window.path.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    querySending() {
      Object.assign(this.formData, this.shortFrom)
      this.getList()
    },
    resetSending(formName) {
      this.$refs[formName].resetFields()
      this.shortFrom.beginTime = ''
      this.shortFrom.endTime = ''
      Object.assign(this.formData, this.shortFrom)
      this.getList()
    },
    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size
      this.getList()
    },
    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage
      this.getList()
    },
    handleClose() {
      this.exportFlag = false
    },
    getLoginPhone() {
      window.api.post(
        window.path.cpus + 'consumerclientinfo/loginTelephoneManager/list',
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data
          }
        }
      )
    },
    exportNums1() {
      // this.exportFlag = true
      // this.getLoginPhone()
      let data = Object.assign({}, this.formData)
      data.productType = 11
      //  this.$File.export(window.path.cpus +'statistics/export', data,`彩信发送明显.zip`)
      window.api.post(window.path.cpus + 'statistics/export', data, (res) => {
        if (res.code == 200) {
          this.$message({
            type: 'success',
            duration: '2000',
            message: '已加入到文件下载中心!',
          })
        } else {
          this.$message({
            type: 'error',
            duration: '2000',
            message: res.msg,
          })
        }
      })
    },
  },
}
</script>

<style scoped>
.short-message-recording-type {
  margin-top: 40px;
}
.pagination {
  display: flex;
  justify-content: space-between;
}
.text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
