<template>
    <div class="container_left">
        <div class="OuterFrame fillet OuterFrameList">
            <!-- 查询条件 -->
            <div>
                <el-form :inline="true" :model="queryCondition" class="demo-form-inline" ref="queryConditionRef"
                    label-width="100px">
                    <el-form-item label="客户名称" prop="clientName">
                        <el-input v-model="queryCondition.clientName" placeholder="请输入客户名称" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="备案域名" prop="link">
                        <el-input v-model="queryCondition.link" placeholder="请输入备案域名" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="审核状态" prop="auditStatus">
                        <el-select v-model="queryCondition.auditStatus" placeholder="请选择审核状态" clearable class="input-w"
                            @change="handleAuditStatusChange">
                            <el-option label="待审核" value="1"></el-option>
                            <el-option label="审核通过" value="2"></el-option>
                            <el-option label="审核不通过" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <div>
                        <el-button type="primary" plain @click="handleQuery()">查询</el-button>
                        <el-button type="primary" plain @click="handleReset()">重置</el-button>
                    </div>
                </el-form>
            </div>

            <!-- 表格展示 -->
            <div class="sensitive-table">
                <div style="
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 12px;
          ">
                    <div></div>
                    <div style="display: flex; align-items: center">
                        <div style="margin-right: 10px; color: #606266; font-size: 14px">
                            <span>当前页</span><span style="margin: 0 5px">{{
                                tableDataObj.tableData.length
                            }}</span><span>条；</span>
                        </div>
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="queryParams.currentPage"
                            :page-size="queryParams.curPageSize" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                        </el-pagination>
                    </div>
                </div>

                <!-- 表格和分页开始 -->
                <vxe-toolbar ref="toolbarRef" custom>
                    <template #buttons>
                        <div class="sensitive">
                            <el-button type="primary" @click="batchPass()"
                                v-if="selectedAuditIds.length != 0">批量通过</el-button>
                            <el-button type="danger" @click="batchReject()"
                                v-if="selectedAuditIds.length != 0">批量驳回</el-button>
                        </div>
                    </template>
                    <template #tools>
                        <vxe-button style="margin-right: 10px" icon="vxe-icon-refresh"
                            @click="refreshTable()"></vxe-button>
                    </template>
                </vxe-toolbar>

                <vxe-table ref="tableRef" id="ReportLinkAudit" border :custom-config="{ storage: true }"
                    :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
                    v-loading="tableDataObj.loading" element-loading-text="拼命加载中"
                    element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px" :virtual-y-config="{
                        enabled: false,
                    }" :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange"
                    @checkbox-change="handleSelectionChange">

                    <vxe-column type="checkbox" width="45" :checkbox-config="{
                        checkMethod: ({ row }) => row.auditStatus == 1
                    }"></vxe-column>
                    <vxe-column field="clientName" title="客户名称" width="150">
                        <template #default="{ row }">
                            <div class="spanColor">
                                <span style="color: #16a589; cursor: pointer">{{ row.clientName }}</span>
                            </div>
                        </template>
                    </vxe-column>

                    <!-- <vxe-column field="companyName" title="公司名称" width="200">
            <template #default="{ row }">
              <div class="spanColor">{{ row.companyName }}</div>
            </template>
          </vxe-column> -->

                    <vxe-column field="link" title="备案域名" min-width="250">
                        <template #default="{ row }">
                            <div class="spanColor">
                                <el-tooltip class="item" effect="dark" :content="row.link" placement="top">
                                    <a :href="row.link" target="_blank"
                                        style="color: #409eff; text-decoration: underline;">
                                        {{ row.link || '-' }}
                                    </a>
                                </el-tooltip>
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="companyName" title="授权公司名称" width="180">
                        <template #default="{ row }">
                            <div class="spanColor">{{ row.companyName }}</div>
                        </template>
                    </vxe-column>
                    <!-- <vxe-column field="recordNumber" title="备案号" width="150">
                        <template #default="{ row }">
                            <div class="spanColor">{{ row.recordNumber }}</div>
                        </template>
                    </vxe-column> -->

                    <vxe-column field="creditCode" title="信用代码" width="180">
                        <template #default="{ row }">
                            <div class="spanColor">{{ row.creditCode }}</div>
                        </template>
                    </vxe-column>

                    <!-- <vxe-column field="reportImg" title="备案图片" width="100">
                        <template #default="{ row }">
                            <div>
                                <el-image v-if="row.reportImg" style="width: 50px; height: 50px" :z-index="9999"
                                    :src="getImageUrl(row.reportImg)" fit="cover"
                                    :preview-src-list="[getImageUrl(row.reportImg)]">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                                <span v-else>-</span>
                            </div>
                        </template>
                    </vxe-column> -->

                    <vxe-column field="sealImg" title="印章图片" width="100">
                        <template #default="{ row }">
                            <div>
                                <el-image v-if="row.sealImg" style="width: 50px; height: 50px" :z-index="9999"
                                    :src="getImageUrl(row.sealImg)" fit="cover"
                                    :preview-src-list="[getImageUrl(row.sealImg)]">
                                    <template #error>
                                        <div class="image-slot">
                                            <el-icon>
                                                <Picture />
                                            </el-icon>
                                        </div>
                                    </template>
                                </el-image>
                                <span v-else>-</span>
                            </div>
                        </template>
                    </vxe-column>

                    <vxe-column field="auditStatus" title="审核状态" width="120">
                        <template #default="{ row }">
                            <el-tag v-if="row.auditStatus === 1" type="warning" effect="dark">待审核</el-tag>
                            <el-tag v-else-if="row.auditStatus === 2" type="success" effect="dark">审核通过</el-tag>
                            <el-tag v-else-if="row.auditStatus === 3" type="danger" effect="dark">审核不通过</el-tag>
                        </template>
                    </vxe-column>

                    <vxe-column field="createTime" title="创建时间" width="100">
                        <template #default="{ row }">
                            <p v-if="row.createTime">
                                {{ moment(row.createTime).format('YYYY-MM-DD') }}
                            </p>
                            <p v-if="row.createTime">
                                {{ moment(row.createTime).format('HH:mm:ss') }}
                            </p>
                        </template>
                    </vxe-column>

                    <vxe-column v-if="queryParams.auditStatus != 0" field="auditTime" title="审核时间" width="100">
                        <template #default="{ row }">
                            <p v-if="row.auditTime">
                                {{ moment(row.auditTime).format('YYYY-MM-DD') }}
                            </p>
                            <p v-if="row.auditTime">
                                {{ moment(row.auditTime).format('HH:mm:ss') }}
                            </p>
                        </template>
                    </vxe-column>

                    <vxe-column v-if="queryParams.auditStatus != 0" field="auditName" title="审核人" width="90">
                        <template #default="{ row }">
                            <div class="spanColor">
                                {{ row.auditName }}
                            </div>
                        </template>
                    </vxe-column>

                    <vxe-column field="auditReason" title="审核原因" width="140">
                        <template #default="{ row }">
                            <div class="spanColor">
                                <el-tooltip v-if="row.auditReason" class="item" effect="dark" :content="row.auditReason"
                                    placement="top">
                                    <span>{{ row.auditReason && row.auditReason.length > 15 ?
                                        row.auditReason.substring(0, 15) + '...' : row.auditReason }}</span>
                                </el-tooltip>
                                <span v-else>-</span>
                            </div>
                        </template>
                    </vxe-column>

                    <vxe-column field="操作" title="操作" width="180" fixed="right">
                        <template #default="{ row, rowIndex }">
                            <el-tooltip v-if="row.auditStatus == 1" class="item" effect="dark" content="审核通过"
                                placement="bottom">
                                <span style="color: #409eff; font-size: 22px; cursor: pointer" @click="handlePass(row)">
                                    <el-icon>
                                        <SuccessFilled />
                                    </el-icon>
                                </span>
                            </el-tooltip>
                            <el-tooltip v-if="row.auditStatus == 1" class="item" effect="dark" content="审核驳回"
                                placement="bottom">
                                <span style="
                    color: #f56c6c;
                    font-size: 22px;
                    cursor: pointer;
                    margin-left: 18px;
                  " @click="handleReject(row)">
                                    <el-icon>
                                        <CircleCloseFilled />
                                    </el-icon>
                                </span>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="查看详情" placement="bottom">
                                <span style="
                    color: #409eff;
                    font-size: 22px;
                    cursor: pointer;
                    margin-left: 18px;
                  " @click="viewDetail(row)">
                                    <el-icon>
                                        <View />
                                    </el-icon>
                                </span>
                            </el-tooltip>
                        </template>
                    </vxe-column>
                </vxe-table>
            </div>

            <!-- 审核弹窗 -->
            <el-dialog :title="auditForm.title" v-model="auditDialogVisible" width="600px"
                :close-on-click-modal="false">
                <div v-if="auditForm.auditId && auditForm.auditId.includes(',')"
                    style="margin-bottom: 20px; padding: 10px; background-color: #f0f9ff; border: 1px solid #e1f5fe; border-radius: 4px;">
                    <p style="margin: 0; color: #333; font-size: 14px;">
                        <i class="el-icon-info" style="color: #409eff; margin-right: 5px;"></i>
                        批量操作提示：本次将对 <strong style="color: #409eff;">{{ auditForm.auditId.split(',').length }}</strong>
                        条记录进行{{
                            auditForm.type === 'pass' ? '审核通过' : '审核驳回' }}操作
                    </p>
                </div>
                <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" label-width="100px">
                    <el-form-item label="审核原因" prop="auditReason">
                        <el-input type="textarea" v-model="auditForm.auditReason" :rows="4"
                            :placeholder="auditForm.type === 'pass' ? '请输入通过原因（可选）' : '请输入驳回原因'"
                            resize="none"></el-input>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="auditDialogVisible = false">取 消</el-button>
                        <el-button type="primary" @click="confirmAudit">确 认</el-button>
                    </div>
                </template>
            </el-dialog>

            <!-- 详情弹窗 -->
            <el-dialog title="备案链接详情" v-model="detailDialogVisible" width="800px" :close-on-click-modal="false">
                <el-descriptions v-if="currentRow" :column="2" size="default" border>
                    <el-descriptions-item>
                        <template #label>客户名称</template>
                        {{ currentRow.clientName }}
                    </el-descriptions-item>
                    <!-- <el-descriptions-item>
            <template #label>公司名称</template>
            {{ currentRow.companyName }}
          </el-descriptions-item> -->
                    <el-descriptions-item>
                        <template #label>备案链接</template>
                        <a :href="currentRow.link" target="_blank" style="color: #409eff; text-decoration: underline;">
                            {{ currentRow.link }}
                        </a>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>授权公司名称</template>
                        {{ currentRow.companyName }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>信用代码</template>
                        {{ currentRow.creditCode }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>审核状态</template>
                        <!-- <el-tag v-if="currentRow.auditStatus === 0" type="warning" effect="dark">编辑中</el-tag> -->
                        <el-tag v-if="currentRow.auditStatus === 1" type="warning" effect="dark">待审核</el-tag>
                        <el-tag v-else-if="currentRow.auditStatus === 2" type="success" effect="dark">审核通过</el-tag>
                        <el-tag v-else-if="currentRow.auditStatus === 3" type="danger" effect="dark">审核不通过</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>创建时间</template>
                        {{ currentRow.createTime ? moment(currentRow.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>审核时间</template>
                        {{ currentRow.auditTime ? moment(currentRow.auditTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>审核人</template>
                        {{ currentRow.auditName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>审核原因</template>
                        {{ currentRow.auditReason || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>创建人</template>
                        {{ currentRow.createName || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>更新人</template>
                        {{ currentRow.updateName || '-' }}
                    </el-descriptions-item>
                </el-descriptions>

                <div v-if="currentRow && (currentRow.reportImg || currentRow.sealImg)" style="margin-top: 20px;">
                    <!-- <h4>相关图片</h4> -->
                    <div style="display: flex; gap: 20px; margin-top: 10px;">
                        <!-- <div v-if="currentRow.reportImg">
                            <p style="margin-bottom: 5px; font-weight: bold;">备案图片：</p>
                            <el-image style="width: 200px; height: 150px" :src="getImageUrl(currentRow.reportImg)"
                                fit="contain" :preview-src-list="[getImageUrl(currentRow.reportImg)]">
                            </el-image>
                        </div> -->
                        <div v-if="currentRow.sealImg">
                            <p style="margin-bottom: 5px; font-weight: bold;">印章图片：</p>
                            <el-image style="width: 200px; height: 150px" :src="getImageUrl(currentRow.sealImg)"
                                fit="contain" :preview-src-list="[getImageUrl(currentRow.sealImg)]">
                            </el-image>
                        </div>
                    </div>
                </div>

                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="detailDialogVisible = false">关 闭</el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { SuccessFilled, CircleCloseFilled, View, Picture } from '@element-plus/icons-vue'
import moment from 'moment'

// 定义组件名称
defineOptions({
    name: 'ProofLinkAudit'
})

// 引用
const queryConditionRef = ref(null)
const auditFormRef = ref(null)
const tableRef = ref(null)
const toolbarRef = ref(null)

// 响应式数据
const queryCondition = reactive({
    clientName: '',
    link: '',
    auditStatus: '1'
})

const queryParams = reactive({
    auditStatus: '1',
    clientName: '',
    curPageSize: 10,
    currentPage: 1,
    link: ''
})

const tableDataObj = reactive({
    loading: false,
    tableData: [],
    total: 0
})

const auditDialogVisible = ref(false)
const auditForm = reactive({
    title: '',
    type: '', // 'pass' 或 'reject'
    auditId: '',
    auditReason: ''
})

const auditRules = reactive({
    auditReason: [
        {
            validator: (rule, value, callback) => {
                if (auditForm.type === 'reject' && !value) {
                    callback(new Error('驳回原因不能为空'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ]
})

const detailDialogVisible = ref(false)
const currentRow = ref(null)

// 批量选择相关
const batchSelection = ref([])
const selectedAuditIds = ref([])

// 获取列表数据
const getTableData = () => {
    tableDataObj.loading = true
    const params = {
        ...queryParams
    }

    window.api.post(
        window.path.omcs + 'operator/smsTemplate/reportLinkProof/page',
        params,
        (res) => {
            tableDataObj.loading = false
            if (res.code == 200) {
                tableDataObj.tableData = res.data.records || []
                tableDataObj.total = res.data.total || 0
            } else {
                ElMessage.error(res.msg || '获取数据失败')
            }
        },
        (err) => {
            tableDataObj.loading = false
            ElMessage.error('网络请求失败')
        }
    )
}

// 查询
const handleQuery = () => {
    Object.assign(queryParams, queryCondition)
    queryParams.currentPage = 1
    getTableData()
}

const handleAuditStatusChange = (e) => {
    //   console.log(queryCondition.auditStatus)
    if (!e) {
        queryCondition.auditStatus = ''
    }
    // getTableData()
}

// 重置
const handleReset = () => {
    queryConditionRef.value?.resetFields()
    Object.assign(queryParams, {
        auditStatus: '1',
        clientName: '',
        curPageSize: 20,
        currentPage: 1,
        link: ''
    })
    getTableData()
}

// 分页 - 每页条数变化
const handleSizeChange = (size) => {
    queryParams.curPageSize = size
    getTableData()
}

// 分页 - 当前页变化
const handleCurrentChange = (currentPage) => {
    queryParams.currentPage = currentPage
    getTableData()
}

// 审核通过
const handlePass = (row) => {
    Object.assign(auditForm, {
        title: '审核通过',
        type: 'pass',
        auditId: row.id.toString(),
        auditReason: ''
    })
    auditDialogVisible.value = true
}

// 审核驳回
const handleReject = (row) => {
    Object.assign(auditForm, {
        title: '审核驳回',
        type: 'reject',
        auditId: row.id.toString(),
        auditReason: ''
    })
    auditDialogVisible.value = true
}

// 确认审核
const confirmAudit = () => {
    auditFormRef.value?.validate((valid) => {
        if (valid) {
            const params = {
                auditId: auditForm.auditId,
                auditReason: auditForm.auditReason
            }

            // 根据审核类型选择不同的接口
            const apiUrl = auditForm.type === 'pass'
                ? 'operator/smsTemplate/reportLinkProof/auditPass'
                : 'operator/smsTemplate/reportLinkProof/auditUnPass'

            window.api.post(
                window.path.omcs + apiUrl,
                params,
                (res) => {
                    if (res.code == 200) {
                        ElMessage.success(`${auditForm.type === 'pass' ? '审核通过' : '审核驳回'}成功`)
                        auditDialogVisible.value = false

                        // 清空选择状态
                        batchSelection.value = []
                        selectedAuditIds.value = []

                        // 刷新表格
                        getTableData()
                    } else {
                        ElMessage.error(res.msg || '审核操作失败')
                    }
                },
                (err) => {
                    ElMessage.error('网络请求失败')
                }
            )
        }
    })
}

// 查看详情
const viewDetail = (row) => {
    currentRow.value = row
    detailDialogVisible.value = true
}

// 预览图片
const previewImage = (imagePath) => {
    if (imagePath) {
        const imageUrl = getImageUrl(imagePath)
        // 可以在这里实现图片预览逻辑
        console.log('预览图片:', imageUrl)
    }
}

// 获取图片URL
const getImageUrl = (imagePath) => {
    console.log(imagePath);

    if (!imagePath) return ''
    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http')) {
        return imagePath
    }
    // 否则拼接图片服务器地址
    return window.path.imgU + imagePath
}

// 刷新表格
const refreshTable = () => {
    getTableData()
}

// 处理选择变化
const handleSelectionChange = (val) => {
    batchSelection.value = val.records || []

    // 只获取待审核状态的ID
    selectedAuditIds.value = batchSelection.value
        .filter(row => row.auditStatus == 1)
        .map(row => row.id.toString())
}

// 批量通过
const batchPass = () => {
    if (selectedAuditIds.value.length === 0) {
        ElMessage.warning('请选择待审核的记录')
        return
    }

    Object.assign(auditForm, {
        title: '批量审核通过',
        type: 'pass',
        auditId: selectedAuditIds.value.join(','),
        auditReason: ''
    })
    auditDialogVisible.value = true
}

// 批量驳回
const batchReject = () => {
    if (selectedAuditIds.value.length === 0) {
        ElMessage.warning('请选择待审核的记录')
        return
    }

    Object.assign(auditForm, {
        title: '批量审核驳回',
        type: 'reject',
        auditId: selectedAuditIds.value.join(','),
        auditReason: ''
    })
    auditDialogVisible.value = true
}

// 监听审核弹窗关闭
watch(auditDialogVisible, (val) => {
    if (!val) {
        // 重置表单
        Object.assign(auditForm, {
            title: '',
            type: '',
            auditId: '',
            auditReason: ''
        })

        // 重置表单验证
        auditFormRef.value?.resetFields()
    }
})

// 组件挂载时获取数据
onMounted(() => {
    getTableData()

    // 连接工具栏和表格
    nextTick(() => {
        const $table = tableRef.value
        const $toolbar = toolbarRef.value
        if ($table && $toolbar) {
            $table.connect($toolbar)
        }
    })
})
</script>

<style lang="less" scoped>
.OuterFrame {
    padding: 20px;
}

.demo-form-inline .el-form-item {
    margin-right: 50px;
}

.sensitive-table {
    padding-bottom: 40px;
}

.sensitive {
    margin-bottom: 10px;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 20px;
}

.demo-image__preview {
    width: 50px;
    height: 50px;
}

.spanColor {
    white-space: normal;
    word-break: break-all;
}

:deep(.el-descriptions__label) {
    font-weight: bold;
}

:deep(.el-descriptions__content) {
    word-break: break-all;
}

:deep(.el-tabs__nav-wrap) {
    overflow: visible !important;
}

:deep(.el-tabs__nav-scroll) {
    overflow: visible !important;
}
</style>