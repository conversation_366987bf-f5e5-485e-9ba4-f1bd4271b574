<template>
  <div class="container_left">
    <div class="Templat-matter">
      <p style="font-weight: bolder">注意：</p>
      <p>1、所有账号24小时内发送营销信息只会成功一次，白名单除外；</p>
      <p>2、生效时间以号码第一次发送时间往后推24小时；</p>
      <p>3、过期自动失效；</p>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInlineRef"
          label-width="60px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="启用" value="0"></el-option>
              <el-option label="停用" value="1"></el-option>
              <!-- <el-option label="删除" value="-1"></el-option> -->
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="formInline.createTime"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                @change="handleTimeChange"
                :clearable="false"
              />
            </el-form-item> -->
          <el-form-item>
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button type="primary" plain @click="Reload(formInlineRef)"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div>
        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addsimilarMobile">新增</el-button>
          </template>
        </vxe-toolbar>
        <vxe-table
          ref="tableRef"
          id="similarMobileTable"
          border
          stripe
          :custom-config="{ storage: true }"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
        >
          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="有效期" title="有效期">
            <template v-slot="scope">
              <div v-if="scope.row.expireTime">
                {{ moment(scope.row.expireTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.status == -1"
                type="danger"
                effect="dark"
              >
                删除
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.status == 0"
                type="success"
                effect="dark"
              >
                启用
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.status == 1"
                type="danger"
                effect="dark"
              >
                停用
              </el-tag>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div v-if="scope.row.createTime">
                {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>
                {{ scope.row.createName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="color: #409eff"
                @click="editSimilarMobile(scope.row)"
                >编 辑</el-button
              >
              <el-button
                link
                style="color: red"
                @click="deleteSimilarMobile(scope.row)"
                >删 除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
      </div>
      <el-dialog
        v-model="dialogVisible"
        :title="title"
        width="500"
        :before-close="handleClose"
      >
        <el-form
          ref="similarForm"
          :model="similarFormoptions"
          :rules="similarRules"
          label-width="80px"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              :disabled="title === '编辑'"
              v-model="similarFormoptions.username"
              placeholder="请输入用户名"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="有效期" prop="expireTime">
            <el-date-picker
              class="input-w"
              v-model="similarFormoptions.expireTime"
              type="datetime"
              placeholder="请选择有效期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="similarFormoptions.status">
              <el-radio label="启用" value="0"></el-radio>
              <el-radio label="停用" value="1"></el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit(similarForm)">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
  
  <script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import { ref } from "vue";
import { reactive } from "vue";

const formInlineRef = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const dialogVisible = ref(false);
const formInline = reactive({
  username: "",
  phone: "",
  source: "",
  status: "",
  currentPage: 1,
  pageSize: 10,
});
const statusList = ref([]);
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
  total: 0,
});
const title = ref("");
const similarForm = ref(null);
const similarFormoptions = reactive({
  username: "",
  expireTime: "",
  status: "0",
  id: "",
});
const similarRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
  ],
  expireTime: [{ required: true, message: "请选择有效期", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "blur" }],
});
const handleSelectionChange = () => {};
const handleSizeChange = (size) => {
  formInline.pageSize = size;
  gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  formInline.currentPage = currentPage;
  gettableLIst();
};
const gettableLIst = () => {
  //获取列表
  tableDataObj.loading2 = true;
  window.api.post(
    window.path.omcs + "operatingclientsimilarmobile/page",
    formInline,
    (res) => {
      tableDataObj.loading2 = false;
      tableDataObj.total = res.data.total;
      tableDataObj.tableData = res.data.records;
    }
  );
};
const Query = () => {
  gettableLIst();
};
const Reload = () => {
  formInlineRef.value.resetFields();
  statusList.value = [];
  gettableLIst();
};
const handleClose = () => {
  dialogVisible.value = false;
};
const addsimilarMobile = () => {
  dialogVisible.value = true;
  title.value = "新增";
};
const editSimilarMobile = (row) => {
  dialogVisible.value = true;
  title.value = "编辑";
  similarFormoptions.username = row.username;
  similarFormoptions.expireTime = moment(row.expireTime).format(
    "YYYY-MM-DD HH:mm:ss"
  );
  similarFormoptions.status = row.status + "";
  similarFormoptions.id = row.id;
};
const handleSubmit = (formEl) => {
  try {
    formEl.validate((valid) => {
      if (valid) {
        similarFormoptions.expireTime = moment(
          similarFormoptions.expireTime
        ).format("YYYY-MM-DD HH:mm:ss");
        if (similarFormoptions.id) {
          //编辑
          window.api.put(
            window.path.omcs + "operatingclientsimilarmobile",
            similarFormoptions,
            (res) => {
              if (res.code == 200) {
                dialogVisible.value = false;
                ElMessage.success("编辑成功");
                gettableLIst();
              } else {
                ElMessage.error(res.msg);
              }
            }
          );
        } else {
          //新增
          window.api.post(
            window.path.omcs + "operatingclientsimilarmobile",
            similarFormoptions,
            (res) => {
              if (res.code == 200) {
                dialogVisible.value = false;
                ElMessage.success("新增成功");
                gettableLIst();
              } else {
                ElMessage.error(res.msg);
              }
            }
          );
        }
      } else {
        console.log("error submit!!");
        return false;
      }
    });
  } catch (error) {
    console.error(error);
  }
};
const deleteSimilarMobile = (row) => {
  try {
    ElMessageBox.confirm(
      "确定删除吗？",
      "提醒",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(() => {
        window.api.delete(
          window.path.omcs + "operatingclientsimilarmobile/" + row.id,
          {},
          (res) => {
            if (res.code == 200) {
              ElMessage.success("删除成功");
              gettableLIst();
            } else {
              ElMessage.error(res.msg);
            }
          }
        );
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消",
        });
      });
  } catch (error) {
    console.error(error);
  }
};
//   const handleTimeChange = (val) => {
//     if (val) {
//       formInline.timeStart = moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
//       formInline.timeEnd = moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
//     } else {
//       formInline.timeStart = "";
//       formInline.timeEnd = "";
//     }
//     //   gettableLIst();
//   };
// const getStatusCodeList = (source) => {
//   window.api.get(
//     window.path.omcs + "operatingmobilecheck/source/status/" + source,
//     {},
//     (res) => {
//       if (res.code == 200) {
//         statusList.value = res.data;
//       }
//     }
//   );
// };
// const handleChange = (val) => {
//   if (val) {
//     getStatusCodeList(val);
//   } else {
//     statusList.value = [];
//   }
// };
onMounted(() => {
  const $table = tableRef.value;
  const $toolbar = toolbarRef.value;
  if ($table && $toolbar) {
    $table.connect($toolbar);
  }
  gettableLIst();
});

watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      similarForm.value.resetFields();
      similarFormoptions.username = "";
      similarFormoptions.expireTime = "";
      similarFormoptions.status = "0";
      similarFormoptions.id = "";
      title.value = "";
    }
  }
);
</script>
  
  <style lang="less" scoped>
.OuterFrame {
  padding: 20px;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Templat-matter > p {
  padding: 5px 0;
}
</style>