# 🔄 SignatureAudit.vue 审核操作合并功能

## 🎯 功能概述

根据用户需求，将SignatureAudit.vue中的"通过"和"驳回"操作合并到一个弹出窗中，使用el-radio-group组件根据用户选择来区分操作类型。通过时保持原有内容，驳回时需要拒绝原因，切换选择时避免内容混乱，接口使用原有接口。

## ✅ 主要修改内容

### 1. 操作按钮合并

#### **修改前**
```vue
<!-- 两个独立的操作按钮 -->
<el-button link @click="handleAdopt(scope.$index, scope.row)">
  <el-icon><SuccessFilled /></el-icon>&nbsp;通过
</el-button>
<el-button link @click="handleReject(scope.$index, scope.row)">
  <el-icon><CircleCloseFilled /></el-icon>&nbsp;驳回
</el-button>
```

#### **修改后**
```vue
<!-- 合并为一个审核按钮 -->
<el-button link @click="handleAudit(scope.$index, scope.row)">
  <el-icon><Edit /></el-icon>&nbsp;审核
</el-button>
```

### 2. 弹窗结构重设计

#### **新的审核弹窗结构**
```vue
<el-dialog title="签名审核" v-model="auditVisible" width="900px" 
  :close-on-click-modal="false" draggable :append-to-body="true" 
  :before-close="handleAuditClose">
  
  <!-- 实名信息展示 -->
  <el-descriptions v-if="reportId" title="实名信息" :column="3" border>
    <!-- 实名信息内容 -->
  </el-descriptions>
  
  <!-- 审核表单 -->
  <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" 
    label-width="90px" class="audit-form">
    
    <!-- 审核类型选择 -->
    <el-form-item label="审核结果" prop="auditType">
      <el-radio-group v-model="auditForm.auditType" @change="handleAuditTypeChange">
        <el-radio :value="2">
          <el-icon style="color: #67c23a;"><SuccessFilled /></el-icon>
          通过
        </el-radio>
        <el-radio :value="3">
          <el-icon style="color: #f56c6c;"><CircleCloseFilled /></el-icon>
          驳回
        </el-radio>
      </el-radio-group>
    </el-form-item>
    
    <!-- 通过时的表单内容 -->
    <template v-if="auditForm.auditType === 2">
      <!-- 短信类型、短信示例、签名来源等字段 -->
    </template>
    
    <!-- 驳回时的表单内容 -->
    <template v-if="auditForm.auditType === 3">
      <el-form-item label="拒绝原因" prop="auditReason">
        <el-input type="textarea" v-model="auditForm.auditReason" 
          placeholder="请输入拒绝原因" :rows="4" resize="none" />
      </el-form-item>
    </template>
  </el-form>
  
  <!-- 弹窗底部按钮 -->
  <template v-slot:footer>
    <el-button @click="auditVisible = false">取 消</el-button>
    <el-button type="primary" @click="handleAuditSubmit" :loading="auditSubmitting">
      {{ auditForm.auditType === 2 ? '通过' : '驳回' }}
    </el-button>
  </template>
</el-dialog>
```

### 3. 数据结构设计

#### **新增审核表单数据**
```javascript
data() {
  return {
    // 新的合并审核弹窗数据
    auditVisible: false,
    auditSubmitting: false,
    signatureIds: '', // 当前审核的签名ID
    auditForm: {
      auditType: 2, // 审核类型：2-通过，3-驳回
      serviceType: '', // 短信类型
      subServiceType: '', // 营销类型
      contentExample: '', // 短信示例
      signatureType: 1, // 签名来源
      signatureSubType: 0, // 签名类型
      appName: '', // APP名称
      imgUrl: '', // 文件上传
      auditReason: '' // 拒绝原因
    },
    auditRules: {
      auditType: [
        { required: true, message: '请选择审核结果', trigger: 'change' }
      ],
      serviceType: [
        { required: true, message: '请选择短信类型', trigger: 'change' }
      ],
      contentExample: [
        { required: true, message: '请输入短信示例', trigger: 'blur' }
      ],
      auditReason: [
        { required: true, message: '请输入拒绝原因', trigger: 'blur' },
        { min: 1, max: 70, message: '长度在 1 到 70 个字符', trigger: 'blur' }
      ]
    }
  }
}
```

### 4. 核心方法实现

#### **统一审核入口方法**
```javascript
// 新的合并审核方法
handleAudit(index, row) {
  this.auditVisible = true;
  this.signatureIds = row.signatureId;
  
  // 设置实名信息
  if (row.reportId) {
    this.reportId = row.reportId;
    // 设置实名相关信息...
  }
  
  // 重置表单数据
  this.auditForm = {
    auditType: 2, // 默认选择通过
    serviceType: row.serviceType ? row.serviceType + "" : '',
    contentExample: row.contentExample || '',
    signatureType: row.signatureType ? row.signatureType : 1,
    // 其他字段初始化...
    auditReason: ''
  };
  
  // 处理文件列表...
}
```

#### **审核类型切换处理**
```javascript
// 审核类型切换处理
handleAuditTypeChange(value) {
  // 切换审核类型时清空相关字段，避免内容混乱
  if (value === 2) {
    // 选择通过时，清空拒绝原因
    this.auditForm.auditReason = '';
  } else if (value === 3) {
    // 选择驳回时，清空通过相关字段
    this.auditForm.serviceType = '';
    this.auditForm.subServiceType = '';
    this.auditForm.contentExample = '';
  }
}
```

#### **统一提交处理**
```javascript
// 审核提交处理
handleAuditSubmit() {
  this.$refs.auditFormRef.validate((valid) => {
    if (valid) {
      this.auditSubmitting = true;
      
      if (this.auditForm.auditType === 2) {
        // 通过审核
        this.submitApproval();
      } else {
        // 驳回审核
        this.submitRejection();
      }
    }
  });
}
```

#### **分别调用原有接口**
```javascript
// 提交通过审核 - 使用原有通过接口
submitApproval() {
  const params = {
    signatureId: this.signatureIds,
    serviceType: this.auditForm.serviceType,
    contentExample: this.auditForm.contentExample,
    // 其他通过相关参数...
  };
  
  window.api.post(
    window.path.omcs + "signatureaudit/adopt", // 原有通过接口
    params,
    (res) => {
      this.auditSubmitting = false;
      this.$message.success("审核通过成功");
      this.auditVisible = false;
      this.getTableDtate();
    }
  );
}

// 提交驳回审核 - 使用原有驳回接口
submitRejection() {
  const params = {
    idArr: [this.signatureIds],
    auditReason: this.auditForm.auditReason
  };
  
  window.api.post(
    window.path.omcs + "signatureaudit/reject", // 原有驳回接口
    params,
    (res) => {
      this.auditSubmitting = false;
      this.$message.success("审核驳回成功");
      this.auditVisible = false;
      this.getTableDtate();
    }
  );
}
```

## 🎨 UI设计特点

### 1. 审核类型选择
- **视觉区分**: 通过使用绿色成功图标，驳回使用红色失败图标
- **默认选择**: 默认选中"通过"选项，符合常用操作习惯
- **清晰标识**: 图标 + 文字的组合，直观易懂

### 2. 条件显示内容
- **通过时显示**: 短信类型、营销类型、短信示例、签名来源、签名类型、APP名称、文件上传
- **驳回时显示**: 只显示拒绝原因输入框
- **动态切换**: 根据选择的审核类型动态显示对应的表单内容

### 3. 防混乱机制
- **字段清空**: 切换审核类型时自动清空不相关的字段
- **表单验证**: 不同审核类型有不同的验证规则
- **状态管理**: 统一的loading状态和错误处理

## 🔄 用户操作流程

### 1. 审核操作流程
```
点击"审核"按钮 → 打开审核弹窗 → 选择审核结果(通过/驳回) → 
填写对应表单内容 → 点击提交 → 调用对应接口 → 显示结果 → 关闭弹窗
```

### 2. 通过审核流程
```
选择"通过" → 填写短信类型 → 填写短信示例 → 选择签名来源 → 
选择签名类型 → 上传文件(可选) → 点击"通过" → 调用通过接口
```

### 3. 驳回审核流程
```
选择"驳回" → 填写拒绝原因 → 点击"驳回" → 调用驳回接口
```

### 4. 切换类型流程
```
选择通过 → 填写通过相关信息 → 切换到驳回 → 通过信息自动清空 → 
填写拒绝原因 → 再切换到通过 → 拒绝原因自动清空
```

## 🎯 优化效果

### 1. 用户体验提升
- **操作简化**: 从两个按钮减少到一个按钮
- **界面统一**: 所有审核操作在一个弹窗中完成
- **逻辑清晰**: 通过radio-group明确区分操作类型

### 2. 开发维护优化
- **代码复用**: 共用实名信息展示和基础弹窗结构
- **逻辑集中**: 审核相关逻辑集中在一个弹窗中
- **接口保持**: 使用原有接口，不影响后端逻辑

### 3. 防错机制
- **内容隔离**: 切换审核类型时自动清空不相关内容
- **表单验证**: 根据审核类型动态验证必填字段
- **状态管理**: 统一的提交状态和错误处理

## 🚀 技术实现亮点

### 1. 条件渲染
- 使用`v-if`根据审核类型条件显示不同的表单内容
- 避免了复杂的显示隐藏逻辑

### 2. 数据清理
- 在`handleAuditTypeChange`方法中实现智能的字段清空
- 确保切换时不会出现数据混乱

### 3. 接口复用
- 保持原有的通过和驳回接口不变
- 只是在前端层面进行了UI和交互的优化

### 4. 表单验证
- 动态的验证规则，根据审核类型应用不同的验证
- 确保数据完整性和用户体验

---

**功能完成**: SignatureAudit.vue的通过和驳回操作已成功合并到一个弹出窗中，使用el-radio-group进行操作区分，实现了内容隔离和防混乱机制，保持了原有接口的使用。
