<template>
  <div>
    <div class="passageWay-title">
      <!-- 查询框开始 -->
      <el-form
        :inline="true"
        :model="form"
        label-width="80px"
        class="demo-form-inline"
        ref="sendjl"
      >
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form.clientName" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="消息ID" prop="msgId">
          <el-input
            v-model="form.msgId"
            class="input-w"
            onInput="value=value.replace(/[^\d]/g,'')"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form.mobile" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="通道号" prop="channelId">
          <el-input v-model="form.channelId" class="input-w"></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click="Query1">查询</el-button>
          <el-button type="primary" plain @click="reset('sendjl')"
            >重置</el-button
          >
        </div>
      </el-form>
      <!-- <div class="boderbottom">
                    
                        <el-button type="primary" plain  @click="export1()">导出</el-button>
                </div> -->
      <!-- 查询框结束 -->
      <div class="passage-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="SendRealTimeQuery"
          border
          stripe
          :custom-config="{ storage: true }"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="100"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px;"
          :data="tableDataObj.tableData">

          <!-- <vxe-column type="checkbox" width="50px"></vxe-column> -->
          <vxe-column field="用户名称" title="用户名称" width="120px">
            <template v-slot="scope">
              <div>
                {{ scope.row.clientName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="msgId" title="msgId" width="200px">
            <template v-slot="scope">
              <div>
                {{ scope.row.msgid }}
                <!-- <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                <CopyTemp :content="scope.row.msgid" />
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="120px">
            <template #default="{row, rowIndex}">
              <div
                v-if="rowIndex == pindex"
                style="color: rgb(22, 165, 137); cursor: pointer"
              >
                {{ row.mobile }}
              </div>
              <div
                v-else
                style="color: rgb(22, 165, 137); cursor: pointer"
                @click="phoneClick(rowIndex, row)"
              >
                {{ row.maskMobile }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="短信内容" title="短信内容" width="500px">
            <template v-slot="scope">
              <el-tooltip class="items" effect="dark" placement="top-start">
                <template v-slot:content>
                  <div class="tooltip">
                    {{ scope.row.content.length }}个字符
                  </div>
                </template>
                <span class="span">{{ scope.row.content }}</span>
              </el-tooltip>
              <!-- <Tooltip v-if="scope.row.content" :content="scope.row.content" className="multiline-text"
                                    :count="scope.row.content.length" widthpx="400px" effect="light">
                                </Tooltip> -->
            </template>
          </vxe-column>
          <vxe-column field="通道号" title="通道号" width="130px">
            <template v-slot="scope">
              <div>
                {{ scope.row.channelId }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="扩展号" title="扩展号" width="130px">
            <template v-slot="scope">
              <div>
                {{ scope.row.ext }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="计费数" title="计费数" width="80px">
            <template v-slot="scope">
              <div>
                {{ scope.row.chargeNum }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="提交时间" title="提交时间" width="110px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="110px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间" width="110px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="发送状态" title="发送状态" width="90px">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                v-if="scope.row.smsStatus == 1"
                type="success"
                effect="dark"
              >
                成功
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.smsStatus == 2"
                type="danger"
                effect="dark"
              >
                失败
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.smsStatus == 3"
                type="info"
                effect="dark"
              >
                待返回
              </el-tag>
              <!-- <span v-if="scope.row.smsStatus == 1">成功</span>
                                <span v-else-if="scope.row.smsStatus == 2">失败</span>
                                <span v-else-if="scope.row.smsStatus == 3">待返回</span> -->
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="110px">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.originalCode"
                :content="scope.row.originalCode"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column
            field="错误码类别"
            title="错误码类别"
            width="100"
          >
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.originalCodeGroup"
                :content="scope.row.originalCodeGroup"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源" width="100px">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.source"
                :content="scope.row.source"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="提交Ip" title="提交Ip" width="110px">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.ip"
                :content="scope.row.ip"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formData.currentPage"
            :page-size="formData.pageSize"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  name: 'SendRealTimeQuery',
  components: { DatePlugin, TableTem, Tooltip, CopyTemp },
  data() {
    return {
      isFirstEnter: false,
      titleMap: {
        add: '批量加入黑名单',
        edit: '加入黑名单',
      },
      pindex: -1,
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      idStr: '',
      rules: {
        //验证规则
        remark: [
          { required: false, message: '请输入备注', trigger: 'blur' },
          {
            min: 1,
            max: 70,
            message: '长度在 1 到 70 个字符',
            trigger: ['blur', 'change'],
          },
        ],
      },
      dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: '',
      },
      datePluginValue: '',
      // datePluginValueList: { //日期选择器
      //     type:"datetimerange",
      //     start:"",
      //     end:'',
      //     range:'-',
      //     clearable:false,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime()
          if (maxDate) {
            this.pickerMinDate = ''
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
            return false
          }
          if (this.pickerMinDate !== '') {
            const day7 = 6 * 24 * 3600 * 1000
            let maxTime = this.pickerMinDate + day7
            if (maxTime > Date.now()) {
              maxTime = Date.now()
            }
            const minTime = this.pickerMinDate - day7
            return time.getTime() < minTime || time.getTime() > maxTime
          }
          return time.getTime() > Date.now()
        },
      },
      //    datePluginValue:''
      // },
      //发送查询的值
      form: {
        mobile: '',
        msgId: '',
        clientName: '',
        channelId: '',
        currentPage: 1,
        pageSize: 10,
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        msgId: '',
        clientName: '',
        channelId: '',
        currentPage: 1,
        pageSize: 10,
      },
      activeName2: 'first', //发送和回复选项卡
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
        // tableLabelExpand:[//折叠的列表表头
        //     { prop:"content",showName:'短信内容:'}
        // ],
      },
      tableDataObj1: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
        tableLabel: [
          //列表表头
          {
            prop: 'userName',
            showName: '用户名称',
            width: '120',
            fixed: false,
          },
          { prop: 'mobile', showName: '手机号码', width: '100', fixed: false },
          { prop: 'content', showName: '回复内容', fixed: false },
          {
            prop: 'createTime',
            showName: '回复时间',
            width: '150',
            fixed: false,
          },
          { prop: 'ext', showName: '扩展码', width: '150', fixed: false },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'isBlacknumber', //关联的表格属性
            contactData: '2', //关联的表格属性-值
            optionName: '加入黑名单', //按钮的显示文字
            optionMethod: 'details', //按钮的方法
            icon: 'el-icon-success', //按钮图标
            optionButtonColor: '#16A589', //按钮颜色
          },
        ],
        tableOptions: [],
      },
    }
  },
  methods: {
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsReplyId)
      }
      this.selectId = selectId.join(',')
    },
    detailsRow(val) {
      //加入黑名单
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.idStr = val.row.smsReplyId
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        // Object.assign(this.formop.remark,val.row.remark);
      })
    },
    //列表操作
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
    },
    // handleCopy(name,event){
    // clip(name, event)
    // },
    phoneClick(index, row) {
      this.pindex = index
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      )
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //批量//加入黑名单
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'statistics/blacklistBatch',
              {
                idStr: this.selectId,
                remark: this.formop.remark,
              },
              () => {
                this.dialogFormVisible = false
                this.getChallDate1()
              }
            )
          } else {
            //加入黑名单
            this.formop.idStr = this.idStr
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'statistics/blacklistBatch',
              this.formop,
              () => {
                this.dialogFormVisible = false
                this.getChallDate1()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    delAll() {
      //批量加入黑名单
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    //获取 发送记录数据
    getChallDate() {
      this.pindex = -1
      let aa = {}
      Object.assign(aa, this.formData)
      this.tableDataObj.loading2 = true
      window.api.post(window.path.omcs + 'smsMessageCommit/page', aa, (res) => {
        console.log(res.data)
        if (res.code == 200) {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.totalRow = res.data.total
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      })
    },
    handleChangeTimeOptions: function () {
      this.datePluginValue = ''
    },
    //发送查询
    Query1() {
      if(this.tableDataObj.loading2) return; //防止重复请求
      Object.assign(this.formData, this.form)
      this.getChallDate()
    },
    //发送重置
    reset() {
      this.$refs.sendjl.resetFields()
      Object.assign(this.formData, this.form)
    },
    // //回复重置
    // reset1(){
    //     this.$refs.sendjl1.resetFields();
    //     Object.assign(this.formData1,this.form1);
    // },
    handleSizeChange(size) {
      this.formData.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formData.currentPage = currentPage
    },
    handleSizeChange1(size) {
      this.formData1.pageSize = size
    },
    handleCurrentChange1: function (currentPage) {
      this.formData1.currentPage = currentPage
    },
  },
  // created(){
  //     this.getChallDate();
  // },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
    formData: {
      handler(val) {
        this.getChallDate()
      },
      deep: true,
    },
    formData1: {
      handler(val) {
        this.getChallDate1()
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
.span {
  white-space: pre-wrap;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>