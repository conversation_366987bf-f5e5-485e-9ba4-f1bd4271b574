<template>
  <div class="container_left">
    <div class="Templat-matter">
      <p>tips：默认回复时间为早上9点到晚上11点，可配置回复时间段。</p>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInlineRef" label-width="80px">
          <el-form-item label="用户名" prop="username">
            <el-input v-model.trim="formInline.username" placeholder class="input-w"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button type="primary" plain @click="Reload(formInlineRef)">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addsimilarMobile">添加设置</el-button>
          </template>
        </vxe-toolbar>
        <vxe-table ref="tableRef" id="blackTemplateTable" border stripe :custom-config="{ storage: true }"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px" :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">
          <!-- <vxe-column type="checkbox" width="50"></vxe-column> -->
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="百分比" title="百分比">
            <template v-slot="scope">
              <div>{{ scope.row.percent }}%</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="创建人" title="创建人">
              <template v-slot="scope">
                <div>
                  {{ scope.row.createName }}
                </div>
              </template>
            </vxe-column> -->
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button link style="color: #409eff" @click="editBlackTemplate(scope.row)">编 辑</el-button>
              <el-button link style="color: red" @click="deleteSimilarMobile(scope.row)">删 除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="formInline.currentPage" :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </div>
      </div>
      <el-dialog v-model="dialogVisible" :title="title" width="650" :before-close="handleClose">
        <el-form :model="similarFormop" :rules="similaRrules" label-width="120px" ref="similarFormopFef"
          style="padding: 0 28px 0 20px">
          <el-form-item label="用户名" prop="username">
            <el-input style="width: 300px" v-model="similarFormop.username" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="百分比" prop="percent">
            <el-input-number style="width: 300px" v-model="similarFormop.percent" :min="0" :max="100"
              label="相似度为正整数，请输入正确数字"></el-input-number>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit(similarFormopFef)">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import { ref } from "vue";
import { reactive } from "vue";
const percent = (rule, value, callback) => {
  if (value != "") {
    let reg = /^[0-9]*[1-9][0-9]*$/;
    if (reg.test(value)) {
      if (value >= 0 && value <= 100) {
        callback();
      } else {
        callback(new Error("百分比在于0-100之间"));
      }
    } else {
      callback(new Error("百分比为正整数，请输入正确数字"));
    }
  } else {
    callback(new Error("百分比不能为空"));
  }
};
const formInlineRef = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const similarFormopFef = ref(null);
const dialogVisible = ref(false);
const formInline = reactive({
  username: "",
  currentPage: 1,
  pageSize: 10,
});
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
  total: 0,
});
const title = ref("");
const similarFormop = reactive({
  id: "",
  username: "",
  percent: undefined,
});
const similaRrules = reactive({
  username: [
    { required: true, message: "用户名不能为空", trigger: "change" },
  ],
  content: [{ required: true, message: "内容不能为空", trigger: "change" }],
  percent: [{ required: true, validator: percent, trigger: "change" }],
});
const handleSelectionChange = () => { };
const handleSizeChange = (size) => {
  formInline.pageSize = size;
  gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  formInline.currentPage = currentPage;
  gettableLIst();
};
const gettableLIst = () => {
  //获取列表
  tableDataObj.loading2 = true;
  window.api.post(
    window.path.omcs + "operatingreplyuser/page",
    formInline,
    (res) => {
      tableDataObj.loading2 = false;
      tableDataObj.total = res.data.total;
      tableDataObj.tableData = res.data.records;
    }
  );
};
const Query = () => {
  gettableLIst();
};
const Reload = () => {
  formInlineRef.value.resetFields();
  gettableLIst();
};
const handleClose = () => {
  dialogVisible.value = false;
};
const addsimilarMobile = () => {
  dialogVisible.value = true;
  title.value = "添加设置";
};
const editBlackTemplate = (row) => {
  dialogVisible.value = true;
  title.value = "编辑设置";
  similarFormop.username = row.username;
  similarFormop.percent = row.percent;
  similarFormop.id = row.id;
};
const handleSubmit = (formEl) => {
  try {
    formEl.validate((valid) => {
      if (valid) {
        let data = JSON.parse(JSON.stringify(similarFormop)); //深拷贝数据
        if (similarFormop.id) {
          //编辑
          window.api.put(
            window.path.omcs + "operatingreplyuser",
            data,
            (res) => {
              if (res.code == 200) {
                dialogVisible.value = false;
                ElMessage.success("编辑成功");
                gettableLIst();
              } else {
                ElMessage.error(res.msg);
              }
            }
          );
        } else {
          //新增
          window.api.post(
            window.path.omcs + "operatingreplyuser",
            data,
            (res) => {
              if (res.code == 200) {
                dialogVisible.value = false;
                ElMessage.success("新增成功");
                gettableLIst();
              } else {
                ElMessage.error(res.msg);
              }
            }
          );
        }
      } else {
        console.log("error submit!!");
        return false;
      }
    });
  } catch (error) {
    console.error(error);
  }
};
const deleteSimilarMobile = (row) => {
  try {
    ElMessageBox.confirm("确定删除吗？", "提醒", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        window.api.delete(
          window.path.omcs + "operatingreplyuser/" + row.id,
          {},
          (res) => {
            if (res.code == 200) {
              ElMessage.success("删除成功");
              gettableLIst();
            } else {
              ElMessage.error(res.msg);
            }
          }
        );
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消",
        });
      });
  } catch (error) {
    console.error(error);
  }
};
onMounted(() => {
  const $table = tableRef.value;
  const $toolbar = toolbarRef.value;
  if ($table && $toolbar) {
    $table.connect($toolbar);
  }
  gettableLIst();
});

watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      similarFormopFef.value.resetFields();
      similarFormop.username = "";
      similarFormop.percent = undefined;
      similarFormop.id = "";
      title.value = "";
    }
  }
);
</script>

<style lang="less" scoped>
.OuterFrame {
  padding: 20px;
}

.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
  margin: 0 15px;
}

.Templat-matter>p {
  padding: 5px 0;
}
</style>