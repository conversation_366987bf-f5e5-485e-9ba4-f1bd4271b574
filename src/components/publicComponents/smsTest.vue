<template>
  <div>
    <el-dialog
      title="短信测试"
      v-model="dialogVisible"
      width="900px"
      :before-close="handleClose"
    >
      <el-form
        ref="smsform"
        label-width="120px"
        :model="formInline"
        :rules="rules"
      >
        <!-- <el-form-item label="客户账号" prop="username">
                        <el-input v-model="formInline.username"></el-input>
                    </el-form-item> -->
        <el-form-item label="发送内容" prop="content">
          <el-input
            type="textarea"
            v-model="formInline.content"
            placeholder="内容必须带签名，签名格式为【签名】"
            @input="handleContentInput"
          ></el-input>
          <div style="font-size: 12px; color: #999; margin-top: 5px;">
            注意：内容必须包含【】格式的签名，例如：您的验证码是123456【公司名】
          </div>
        </el-form-item>
        <el-form-item label="短信类型" prop="smsType">
          <el-select
            style="width: 100%"
            v-model="formInline.smsType"
            placeholder="请选择"
          >
            <el-option label="验证码类型" value="1"> </el-option>
            <el-option label="行业类型" value="2"> </el-option>
            <el-option label="营销类型" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发送手机号" prop="mobile">
          <el-input
            type="textarea"
            v-model="formInline.mobile"
            placeholder="多个号码之间用英文逗号隔开"
            @input="handleMobileInput"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item v-if="formInline.sendStatus == '1'" label="文件">
                        <el-upload ref="uploadingsms" :action="action" :headers="token" class="upload-demo"
                            v-bind:before-upload="beforeUploadVideo" :on-remove="handleRemove" :on-success="handleSuccess" multiple :limit="1"
                            :auto-upload="true" :file-list="fileList">
                            <el-button size="default" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item> -->
        <el-form-item label="移动通道" prop="channelYd">
          <el-input v-model="formInline.channelYd"></el-input>
        </el-form-item>
        <el-form-item label="联通通道" prop="channelLt">
          <el-input v-model="formInline.channelLt"></el-input>
        </el-form-item>
        <el-form-item label="电信通道" prop="channelDx">
          <el-input v-model="formInline.channelDx"></el-input>
        </el-form-item>
        <!-- <el-form-item label="发送类型" prop="sendType">
                        <el-radio-group v-model="formInline.sendType">
                            <el-radio label="1">立即发送</el-radio>
                            <el-radio label="2">定时发送</el-radio>
                        </el-radio-group>
                    </el-form-item> -->
        <!-- <el-form-item v-if="formInline.sendType == '2'" label="发送时间" prop="sendTime">
                        <el-date-picker v-model="formInline.sendTime" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item> -->
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="submitForm('smsform')"
            >测试发送</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import bus from '../common/bus'
import moment from 'moment'
import axios from 'axios'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    // 自定义签名验证函数
    const validateSignature = (rule, value, callback) => {
      if (!value) {
        callback(new Error('内容不能为空'))
      } else {
        // 检查是否包含【】格式的签名
        const signatureRegex = /【.+】/
        if (!signatureRegex.test(value)) {
          callback(new Error('内容必须包含【】格式的签名，例如：您的验证码是123456【公司名】'))
        } else {
          callback()
        }
      }
    }

    return {
      dialogVisible: false,
      token: '',
      action: window.path.cpus + 'v3/file/upload',
      fileList: [],
      formInline: {
        // username: "",//客户名称
        content: '', //内容
        mobile: '', //手机号
        smsType: '',
        // sendStatus: "0",
        filePath: '',
        channelDx: '', //电信通道
        channelLt: '', //联通通道
        channelYd: '', //移动通道
        // sendType: "1",//发送类型
        // sendTime: "",//发送类型
      },
      rules: {
        // username: [
        //     { required: true, message: '客户名称不能为空', trigger: 'change' },
        // ],
        content: [
          { required: true, validator: validateSignature, trigger: 'blur' },
        ],
        // sendTime: [
        //     { type: 'date', required: true, message: '请选择发送时间', trigger: 'change' }
        // ],
        mobile: [
          { required: true, message: '手机号不能为空', trigger: 'change' },
        ],
        smsType: [
          { required: true, message: '请选择短信类型', trigger: 'change' },
        ],
        // sendStatus: [
        //     { required: true, message: '请选择发送方式', trigger: 'change' }
        // ],
        // channelDx: [
        //     { required: true, message: '电信通道不能为空', trigger: 'change' }
        // ],
        // channelLt: [
        //     { required: true, message: '联通通道不能为空', trigger: 'blur' }
        // ],
        // channelYd: [
        //     { required: true, message: '移动通道不能为空', trigger: 'blur' }
        // ],
        // sendType: [
        //     { required: true, message: '发送不能为空', trigger: 'blur' }
        // ]
      },
    }
  },
  mounted() {
    this.token = {
      Authorization: 'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  methods: {
    // 处理内容输入，清理特殊字符
    handleContentInput(value) {
      // 清理 \t \r \n \f \v 等特殊字符
      const cleanedValue = value.replace(/[\t\r\n\f\v]/g, '').trim()
      this.formInline.content = cleanedValue
    },
    
    // 处理手机号输入，清理特殊字符
    handleMobileInput(value) {
      // 清理 \t \r \n \f \v 等特殊字符
      const cleanedValue = value.replace(/[\t\r\n\f\v]/g, '').trim()
      this.formInline.mobile = cleanedValue
    },
    
    // 清理所有表单内容
    cleanFormData() {
      // 清理发送内容
      if (this.formInline.content) {
        this.formInline.content = this.formInline.content.replace(/[\t\r\n\f\v]/g, '').trim()
      }
      // 清理手机号
      if (this.formInline.mobile) {
        this.formInline.mobile = this.formInline.mobile.replace(/[\t\r\n\f\v]/g, '').trim()
      }
    },
    
    handleClose() {
      this.dialogVisible = false
      $emit(bus, 'closeVideo', this.dialogVisible)
    },
    beforeUploadVideo(file) {
      // console.log(file.type, "file.type")
      // const isLt5M = file.size / 1024 / 1024 < 20;
      // if (
      //     ["video/mp4"].indexOf(file.type) == -1 &&
      //     ["video/WEBM"].indexOf(file.type) == -1
      // ) {
      //     this.$message({
      //         type: "warning",
      //         message: "请上传正确的视频格式",
      //     });
      //     return false;
      // }
      // if (!isLt5M) {
      //     this.$message({
      //         type: "warning",
      //         message: "视频大小不能超过20MB",
      //     });
      //     return false;
      // }
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.formInline.filePath = ''
    },
    handleSuccess(res) {
      if (res.code == 200) {
        console.log(res, 'res')
        // this.dialogVisible = false
        this.formInline.filePath = res.data.fullpath
        let obj = { name: res.data.fileName, url: res.data.fullpath }
        this.fileList.push(obj)

        // console.log(this.fileList,'this.fileList1');
        this.$message({
          message: res.msg,
          type: 'success',
        })
      } else {
        this.$message.error('文件上传失败！')
      }
    },
    submitForm(formupcode) {
      try {
        // 提交前清理表单数据
        this.cleanFormData()
        
        this.$refs[formupcode].validate((valid) => {
          if (valid) {
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'sms/message/send',
              this.formInline,
              (res) => {
                this.dialogVisible = false
                // this.gettableLIst();
              }
            )
          } else {
            console.log('error submit!!')
            return false
          }
        })
      } catch (error) {
        console.log(error, 'err')
      }
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        $emit(bus, 'closeVideo', this.dialogVisible)
        // this.fileList = [];
        // this.path = ''
        // this.formInline = {}
        // this.editFormInline = {}
        // this.videoPath = ""
      }
    },
  },
  emits: ['closeVideo'],
}
</script>
