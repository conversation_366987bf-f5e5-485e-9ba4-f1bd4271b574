<template>
  <div>
    <!-- 接口信息 -->
    <div class="fillet bas-block">
      <div class="basic-cfg-title">接口信息</div>
      <div class="basic-cfg-cot">
        <span class="basic-cfg-tit">SDK AppID</span>
        <span>1400132608</span>
        <div class="basic-cfg-tips">
          SDK AppID是短信应用的唯一标识，调用短信API接口时需要提供该参数
        </div>
      </div>
      <div class="basic-cfg-cot">
        <span class="basic-cfg-tit"
          >App Key <el-icon><el-icon-warning /></el-icon
        ></span>
        <span>**********显示</span>
        <div class="basic-cfg-tips">
          App Key是用来校验短信发送请求合法性的密码，与SDK
          AppID对应，需要业务方高度保密，切勿把密码存储在客户端。
        </div>
      </div>
      <div class="basic-cfg-cot">
        <span class="basic-cfg-tit">创建时间</span>
        <span>2018-08-29 11:35:44</span>
      </div>
    </div>
    <!-- 发送超量提醒 -->
    <div class="fillet bas-block">
      <div class="basic-cfg-title">发送超量提醒</div>
      <transition name="fade">
        <div class="tips-box" v-if="excessTips">
          <div>
            当前账号每个自然日
            <span class="bac-color-red">国内文本短信 </span> 请求量超过
            <span class="bac-color-red">{{ sendExcessTips.sendExcess }}</span>
            条时提醒
          </div>
          <div class="basic-help">超量提醒帮助</div>
          <el-row>
            <el-button
              type="success"
              class="set-excess-tips"
              @click="setExcessTips"
              >设置</el-button
            >
          </el-row>
        </div>
      </transition>
      <transition name="fade">
        <div class="tips-box" v-if="!excessTips">
          <div>
            当前账号每个自然日
            <span class="bac-color-red">国内文本短信 </span> 请求量超过
            <el-input
              placeholder="请输入条数"
              style="width: 100px"
              v-model="sendExcessTips.setSendExcess"
            ></el-input>
            条时提醒
          </div>
          <div class="basic-help">超量提醒帮助</div>
          <el-row>
            <el-button
              type="success"
              class="sure-excess-tips"
              @click="sureExcessTips"
              >确定</el-button
            >
            <el-button
              type="success"
              plain
              class="cancel-excess-tips"
              @click="cancelExcessTips"
              >取消</el-button
            >
          </el-row>
        </div>
      </transition>
    </div>
    <!-- 余额提醒 -->
    <div class="fillet bas-block">
      <div class="basic-cfg-title">余额提醒</div>
      <transition name="fade">
        <div class="tips-box" v-show="balanceTips">
          <span
            >当前账号余额条数在不足
            <span class="bac-color-red">{{ bacBalanceTips.insufficient }}</span>
            条时提醒</span
          >
          <div class="basic-help">余额提醒帮助</div>
          <el-row>
            <el-button
              type="success"
              class="set-balance-tips"
              @click="setbalanceTips"
              >设置</el-button
            >
          </el-row>
        </div>
      </transition>
      <transition name="fade">
        <div class="tips-box" v-show="!balanceTips">
          <span
            >当前账号余额条数 在不足
            <el-input
              placeholder="请输入条数"
              style="width: 100px"
              v-model="bacBalanceTips.setInsufficient"
            ></el-input>
            条时提醒</span
          >
          <div class="basic-help">余额提醒帮助</div>
          <el-row>
            <el-button
              type="success"
              class="sure-balance-tips"
              @click="surebalanceTips"
              >确定</el-button
            >
            <el-button
              type="success"
              plain
              class="cancel-balance-tips"
              @click="cancelbalanceTips"
              >取消</el-button
            >
          </el-row>
        </div>
      </transition>
    </div>
    <div class="fillet bas-block" style="height: 250px">
      <div class="basic-cfg-title">发送频率</div>
      <el-button
        type="success"
        class="set-balance-tips"
        @click="handleRequencyEnable"
        v-if="requencyEnable == false"
        >启用</el-button
      >
      <div v-if="requencyEnable == true">
        <transition name="fade">
          <div class="tips-box" v-show="sendfrequency">
            <div style="padding-bottom: 15px">
              当前账号单日提交数据频次超过<span class="bac-color-red">
                {{ SendReminder.SingleFrequencysub }} </span
              >次时提醒
            </div>
            <div style="padding-bottom: 20px">
              当前账号同一号码提交频次超过<span class="bac-color-red">
                {{ SendReminder.SameleFrequencysub }} </span
              >次时提醒
            </div>
            <div class="basic-help" style="margin: 20px 0 20px 0">
              余额提醒帮助
            </div>
            <el-row>
              <el-button
                type="success"
                class="set-balance-tips"
                @click="setSendfrequency"
                >设置</el-button
              >
              <el-button
                type="success"
                class="set-balance-tips"
                @click="sendfrDialogShow = true"
                >关闭设置</el-button
              >
            </el-row>
          </div>
        </transition>
        <transition name="fade">
          <div class="tips-box" v-show="!sendfrequency">
            <div style="padding-bottom: 15px">
              当前账号单日提交数据频次超过
              <el-input
                placeholder="请输入频次"
                style="width: 100px"
                v-model="SendReminder.setSingleFrequencysub"
              ></el-input>
              次时提醒
            </div>
            <div style="padding-bottom: 20px">
              当前账号同一号码提交频次超过
              <el-input
                placeholder="请输入频次"
                style="width: 100px"
                v-model="SendReminder.setSameleFrequencysub"
              ></el-input>
              次时提醒
            </div>
            <div class="basic-help" style="margin: 20px 0 20px 0">
              超次提醒帮助
            </div>
            <el-row>
              <el-button
                type="success"
                class="sure-balance-tips"
                @click="sureSendfrequency"
                >确定</el-button
              >
              <el-button
                type="success"
                plain
                class="cancel-balance-tips"
                @click="cancelSendfrequency"
                >取消</el-button
              >
            </el-row>
          </div>
        </transition>
      </div>
      <el-dialog title="注意" v-model="sendfrDialogShow" width="500px">
        <span
          >您将关闭发送频率设置，关闭后可能有被短信轰炸的风险，您是否确认该操作？</span
        >
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="sendfrDialogShow = false">取 消</el-button>
            <el-button type="primary" @click="handleChangeStatus"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    ElIconWarning,
  },
  name: 'BasicConfiguration',
  data() {
    return {
      excessTips: true,
      balanceTips: true,
      sendfrequency: true,
      requencyEnable: false,
      sendfrDialogShow: false,
      sendExcessTips: {
        sendExcess: 100,
        setSendExcess: 100,
      },
      bacBalanceTips: {
        insufficient: 100,
        setInsufficient: 100,
      },
      SendReminder: {
        SingleFrequencysub: '',
        setSingleFrequencysub: 1000,
        SameleFrequencysub: '',
        setSameleFrequencysub: 233,
      },
    }
  },
  methods: {
    setExcessTips() {
      this.excessTips = !this.excessTips
    },
    sureExcessTips() {
      this.excessTips = !this.excessTips
      this.sendExcessTips.sendExcess = this.sendExcessTips.setSendExcess
    },
    cancelExcessTips() {
      this.excessTips = !this.excessTips
      this.sendExcessTips.setSendExcess = this.sendExcessTips.sendExcess
    },
    setbalanceTips() {
      this.balanceTips = !this.balanceTips
    },
    surebalanceTips() {
      this.balanceTips = !this.balanceTips
      this.bacBalanceTips.insufficient = this.bacBalanceTips.setInsufficient
    },
    cancelbalanceTips() {
      this.balanceTips = !this.balanceTips
      this.bacBalanceTips.setInsufficient = this.bacBalanceTips.insufficient
    },
    setSendfrequency() {
      this.sendfrequency = !this.sendfrequency
    },
    sureSendfrequency() {
      this.sendfrequency = !this.sendfrequency
      this.SendReminder.SingleFrequencysub =
        this.SendReminder.setSingleFrequencysub
      this.SendReminder.SameleFrequencysub =
        this.SendReminder.setSameleFrequencysub
    },
    cancelSendfrequency() {
      this.sendfrequency = !this.sendfrequency
      this.SendReminder.setSingleFrequencysub =
        this.SendReminder.SingleFrequencysub
      this.SendReminder.setSameleFrequencysub =
        this.SendReminder.SameleFrequencysub
    },
    handleRequencyEnable() {
      this.requencyEnable = true
      this.sendfrequency = false

      this.SendReminder.SingleFrequencysub = ''
      this.SendReminder.setSingleFrequencysub = ''
      this.SendReminder.SameleFrequencysub = ''
      this.SendReminder.setSameleFrequencysub = ''
    },
    handleChangeStatus() {
      this.sendfrDialogShow = false
      this.requencyEnable = false
    },
  },
  created() {
    if (
      this.SendReminder.SingleFrequencysub ||
      this.SendReminder.SameleFrequencysub
    ) {
      this.requencyEnable = true
    }
  },
}
</script>

<style scoped>
.bas-block {
  margin-bottom: 10px;
  padding: 30px;
  position: relative;
  height: 220px;
}
.basic-cfg-title {
  font-weight: bold;
  padding-bottom: 32px;
  color: #333;
}
.basic-cfg-tit {
  width: 92px;
  display: inline-block;
}
.basic-cfg-cot {
  margin-bottom: 24px;
}
.basic-cfg-tips {
  padding-left: 96px;
  padding-top: 2px;
  font-size: 12px;
}
.basic-help {
  margin: 50px 0 20px 0;
  padding-bottom: 20px;
  width: 500px;
  border-bottom: 1px solid #e6e6e6;
}
.tips-box {
  position: absolute;
  background: #fff;
}
.fade-enter-active {
  animation: show-in 1s;
  transition: all 1s;
}
.fade-leave-active {
  animation: show-out 1s;
  transition: all 1s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
@keyframes show-in {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(360deg);
  }
}
@keyframes show-out {
  0% {
    transform: rotateX(360deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}
.bac-color-red {
  color: red;
}
</style>
