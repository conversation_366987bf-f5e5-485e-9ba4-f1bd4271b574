<template>
  <div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" label-width="82px" prop="consumerName">
            <el-input
              v-model="formInline.consumerName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属销售" label-width="82px" prop="sales">
            <el-select
              v-model="formInline.sales"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in sales"
                :label="item.realName"
                :value="item.userId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属客服" label-width="82px" prop="service">
            <el-select
              v-model="formInline.service"
              clearable
              placeholder="不限"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in services"
                :label="item.realName"
                :value="item.userId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建人" label-width="82px" prop="createName">
            <el-input
              v-model="formInline.createName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" label-width="82px" prop="dataCreat">
            <el-date-picker
              class="input-w"
              v-model="formInline.dataCreat"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
      </div>
      <div class="Signature-search-fun" style="margin: 10px 0">
        <!-- <span class="Signature-list-header">用户余额列表</span> -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        ></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>

      <!-- 充值 -->
      <el-dialog
        title="充值"
        v-model="LcpDialogVisible"
        width="560px"
        class="LoginCellPhoneDialog"
        :close-on-click-modal="false"
        :before-close="handelClose"
      >
        <el-steps
          :active="setPhoneSteps - 1"
          process-status="finish"
          finish-status="wait"
          simple
          style="margin-bottom: 26px"
        >
          <el-step title="线下充值确认" :icon="ElIconUpload"></el-step>
          <el-step title="获取手机号验证" :icon="ElIconEdit"></el-step>
        </el-steps>
        <div v-show="setPhoneSteps == 1">
          <el-form
            :model="setphoneFrom.ruleForm2"
            :rules="setphoneFrom.rules2"
            ref="ruleForm2"
            class="demo-ruleForm"
            label-width="130px"
          >
            <el-form-item label="用户名" prop="consumerName">
              <el-input
                v-model="setphoneFrom.ruleForm2.consumerName"
                style="width: 290px"
                :disabled="true"
              ></el-input>
            </el-form-item>
            <el-form-item label="公司名称" prop="compName">
              <el-input
                v-model="setphoneFrom.ruleForm2.compName"
                style="width: 290px"
                :disabled="true"
              ></el-input>
            </el-form-item>
            <el-form-item label="支付方式" prop="smsPayMethod">
              <el-input
                v-model="setphoneFrom.ruleForm2.smsPayMethod"
                style="width: 290px"
                :disabled="true"
              ></el-input>
            </el-form-item>

            <el-form-item label="产品类型" prop="rechargeCategory">
              <!-- <el-input v-model="setphoneFrom.ruleForm2.businessTypes" style="width:290px;" :disabled="true"></el-input> -->
              <el-select
                v-model="setphoneFrom.ruleForm2.rechargeCategory"
                clearable
                placeholder="不限"
                style="width: 290px"
              >
                <el-option label="短信" value="1"></el-option>
                <el-option label="空号检测" value="5"></el-option>
                <el-option label="反欺诈检测" value="6"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="充值方式">
              <el-radio-group v-model="setphoneFrom.ruleForm2.payStyle">
                <el-radio label="1">正式</el-radio>
                <el-radio label="2">测试</el-radio>
                <el-radio label="3">返条数</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="用户单价" prop="smsPrice">
              <el-input
                v-model="setphoneFrom.ruleForm2.smsPrice"
                style="width: 290px"
              ></el-input>
            </el-form-item>

            <template v-if="setphoneFrom.ruleForm2.payStyle == '1'">
              <el-form-item label="到款银行" prop="rechargeBank">
                <el-select
                  v-model="setphoneFrom.ruleForm2.rechargeBank"
                  clearable
                  placeholder="不限"
                  style="width: 290px"
                >
                  <el-option label="助通上海农商银行" value="1"></el-option>
                  <el-option label="助通上海建设银行" value="2"></el-option>
                  <el-option label="助通对公支付宝" value="3"></el-option>
                  <el-option label="助通对私支付宝" value="4"></el-option>
                  <el-option label="其他" value="5"></el-option>
                  <el-option label="测试" value="6"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="到款金额" prop="rechargeAmount">
                <el-input
                  v-model="setphoneFrom.ruleForm2.rechargeAmount"
                  style="width: 290px"
                ></el-input>
              </el-form-item>
              <el-form-item label="充值条数" prop="rechargeNum">
                <el-input
                  v-model="setphoneFrom.ruleForm2.rechargeNum"
                  style="width: 290px"
                  :disabled="true"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注" prop="rechargeNote">
                <el-input
                  type="textarea"
                  v-model="setphoneFrom.ruleForm2.rechargeNote"
                  style="width: 290px"
                ></el-input>
              </el-form-item>
            </template>

            <template v-if="setphoneFrom.ruleForm2.payStyle == '2'">
              <el-form-item label="测试条数" prop="rechargeNum">
                <el-input
                  v-model="setphoneFrom.ruleForm2.rechargeNum"
                  style="width: 290px"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注" prop="rechargeNote">
                <el-input
                  type="textarea"
                  v-model="setphoneFrom.ruleForm2.rechargeNote"
                  style="width: 290px"
                ></el-input>
              </el-form-item>
            </template>

            <template v-if="setphoneFrom.ruleForm2.payStyle == '3'">
              <el-form-item label="赠送条数" prop="rechargeNum">
                <el-input
                  v-model="setphoneFrom.ruleForm2.rechargeNum"
                  style="width: 290px"
                ></el-input>
              </el-form-item>
              <el-form-item label="备注" prop="rechargeNote">
                <el-input
                  type="textarea"
                  v-model="setphoneFrom.ruleForm2.rechargeNote"
                  style="width: 290px"
                ></el-input>
              </el-form-item>
            </template>

            <el-form-item>
              <el-button @click="cancel()" style="width: 100px; padding: 9px 0"
                >取消</el-button
              >
              <!-- <el-button type="primary" @click="beforeStep()" style="width:100px; padding:9px 0;">上一步</el-button> -->
              <el-button
                type="primary"
                @click="submitForm('ruleForm2')"
                style="width: 100px; padding: 9px 0"
                >提交</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div v-show="setPhoneSteps == 2">
          <el-table
            :data="tableD"
            class="Login-c-p-getPhone"
            border
            style="width: 100%"
          >
            <el-table-column align="center" width="80" label="选择">
              <template v-slot="scope">
                <el-radio
                  @change="getCurrentRow(scope.$index)"
                  :label="scope.$index"
                  v-model="radio"
                  class="textRadio"
                  >&nbsp;</el-radio
                >
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              prop="name"
              label="编号"
              width="120"
            >
            </el-table-column>
            <el-table-column prop="address" align="center" label="手机号">
            </el-table-column>
          </el-table>
          <el-form
            :model="setphoneFrom.ruleForm1"
            :rules="setphoneFrom.rules1"
            ref="ruleForm1"
            class="demo-ruleForm"
            label-width="140px"
          >
            <el-form-item
              label="手机验证码"
              prop="verCode"
              style="margin: 40px auto"
            >
              <el-input
                v-model="setphoneFrom.ruleForm1.verCode"
                style="display: inline-block; width: 180px"
              ></el-input>
              <el-button
                type="primary"
                plain
                style="width: 124px; padding: 9px 0px"
                @click="CountdownCode"
                v-if="nmb == 120"
                >获取验证码</el-button
              >
              <el-button
                type="primary"
                plain
                style="width: 124px; padding: 9px 0px"
                disabled
                v-else
                >重新获取({{ nmb }})</el-button
              >
            </el-form-item>
            <el-form-item style="">
              <el-button @click="cancel()" style="width: 100px; padding: 9px 0"
                >取消</el-button
              >
              <el-button
                type="primary"
                @click="submitForm('ruleForm1')"
                style="width: 100px; padding: 9px 0"
                >提交</el-button
              >
              <el-button
                type="primary"
                @click="beforeStep()"
                style="width: 100px; padding: 9px 0"
                >上一步</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-dialog>
      <!-- 充值结束 -->
      <!-- 扣款 -->
      <el-dialog
        title="扣款"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="560px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="140px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="用户名" prop="consumerName">
            <el-input
              v-model="formop.consumerName"
              style="width: 290px"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称" prop="compName">
            <el-input
              v-model="formop.compName"
              style="width: 290px"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="短信余额（条）" prop="restSumSms">
            <el-input
              v-model="formop.restSumSms"
              style="width: 290px"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="空号余额（条）" prop="emptyBalance">
            <el-input
              v-model="formop.emptyBalance"
              style="width: 290px"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="反欺诈余额（条）" prop="riskBalance">
            <el-input
              v-model="formop.riskBalance"
              style="width: 290px"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="产品类型" prop="rechargeCategory">
            <!-- <el-input v-model="setphoneFrom.ruleForm2.businessTypes" style="width:290px;" :disabled="true"></el-input> -->
            <el-select
              v-model="formop.rechargeCategory"
              clearable
              placeholder="不限"
              style="width: 290px"
            >
              <el-option label="短信" value="1"></el-option>
              <el-option label="空号检测" value="5"></el-option>
              <el-option label="反欺诈检测" value="6"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="产品类型" prop="businessTypes" >
                    <el-input v-model="formop.businessTypes" style="width:290px;" :disabled="true"></el-input>
                </el-form-item> -->
          <el-form-item label="扣除条数" prop="rechargeNum">
            <el-input
              v-model="formop.rechargeNum"
              style="width: 290px"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="rechargeNote">
            <el-input
              type="textarea"
              v-model="formop.rechargeNote"
              style="width: 290px"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button
              type="primary"
              v-if="payMoney"
              @click="submitForms('formop')"
              >提 交</el-button
            >
            <el-button type="primary" v-else :loading="true">加载中</el-button>
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 扣款结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
export default {
  data() {
    // 验证IP规则
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error('请选中手机号'))
      } else if (value == '') {
        return callback(new Error('请输入验证码'))
      } else {
        callback()
      }
    }
    return {
      titleMap: {
        add: '增加运营商',
        edit: '编辑运营商',
      },
      //新增编辑标题
      dialogStatus: '',
      nmb: 120,
      radio: '',
      //扣款提交按钮
      payMoney: true,
      // 设置手机号的步骤
      setPhoneSteps: 1,
      //新增弹出框显示隐藏
      dialogFormVisible: false,
      //弹出框显示隐藏
      LcpDialogVisible: false,
      //运营商id
      operatorId: '',
      //客服
      services: [],
      //销售
      sales: [],
      //当前行列表数据
      tableRow: '',
      selectId: '',
      // 存储个人信息
      roleInfo: {},
      formop: {
        //表单数据
        rechargeName: '',
        compName: '',
        restSumSms: '',
        emptyBalance: '',
        riskBalance: '',
        businessTypes: '',
        rechargeNum: '',
        rechargeNote: '',
        rechargeCategory: '',
        userId: '',
      },
      rules: {
        //验证规则
        rechargeNum: [
          { required: true, message: '请输入扣款条数', trigger: 'blur' },
          { min: 1, max: 8, message: '最多输入8位字符' },
          { pattern: /^[1-9]\d*$/, message: '含有非法字符（只能输入数字！）' },
        ],
        rechargeCategory: [
          {
            required: true,
            message: '请选择充值类型',
            trigger: ['blur', 'change'],
          },
        ],
        rechargeNote: [
          { required: false, message: '请输入备注', trigger: 'blur' },
          { min: 1, max: 50, message: '最多输入50位字符' },
        ],
      },
      tableD: [],
      setphoneFrom: {
        ruleForm1: {
          verCode: '',
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: 'blur' },
            { min: 6, max: 6, message: '请输入6位数字验证码' },
          ],
        },
        rules2: {
          rechargeBank: [
            {
              required: true,
              message: '请选择到款银行',
              trigger: ['blur', 'change'],
            },
          ],
          rechargeCategory: [
            {
              required: true,
              message: '请选择充值类型',
              trigger: ['blur', 'change'],
            },
          ],
          rechargeAmount: [
            { required: true, message: '请输入到款金额', trigger: 'blur' },
            { min: 1, max: 10, message: '最大100万' },
            {
              pattern:
                /^(([0-9][0-9]{0,9})|(([0]\.\d{1,5}|[0-9][0-9]{0,9}\.\d{1,2})))$/,
              message: '金额最多7位整数和2位小数！',
            },
          ],
          rechargeNum: [
            { required: true, message: '请输入充值条数', trigger: 'blur' },
            { min: 1, max: 8, message: '最大3000万条' },
            {
              pattern: /^[0-9]\d*$/,
              message: '含有非法字符（只能输入数字！）',
            },
          ],
          rechargeNote: [
            { required: false, message: '请输入备注', trigger: 'blur' },
            { min: 1, max: 50, message: '请输入50位字符' },
          ],
        },
        ruleForm2: {
          rechargeName: '',
          compName: '',
          smsPayMethod: '',
          businessTypes: '',
          smsPrice: '',
          payStyle: '1',
          rechargeBank: '',
          rechargeAmount: '',
          rechargeNum: '',
          rechargeNote: '',
          userId: '',
          rechargePrice: '',
          rechargeCategory: '',
        },
      },
      tabelAlllist: {
        //存储查询数据
        createName: '',
        consumerName: '',
        service: '',
        sales: '',
        dataCreat: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      formInline: {
        //查询数据
        createName: '',
        consumerName: '',
        service: '',
        sales: '',
        dataCreat: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        currentPage: '1',
        pageSize: '10',
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'consumerName',
            showName: '用户名',
            fixed: false,
          },
          {
            prop: 'roleId',
            showName: '用户角色',
            fixed: false,
            formatData: function (val) {
              if (val == '14') {
                return (val = '终端 ')
              } else if (val == '13') {
                return (val = '子用户')
              } else if (val == '12') {
                return (val = '管理商')
              }
            },
          },
          {
            prop: 'serviceName',
            showName: '所属客服',
            fixed: false,
          },
          {
            prop: 'salesName',
            showName: '所属销售',
            fixed: false,
          },
          {
            prop: 'smsPayMethod',
            showName: '充值类型',
            fixed: false,
            formatData: function (val) {
              if (val == '1') {
                return (val = '预付费 ')
              } else if (val == '2') {
                return (val = '后付费')
              }
            },
          },
          {
            prop: 'restSumSms',
            showName: '短信余额（条）',
            fixed: false,
          },
          // {
          //   prop: "emptyBalance",
          //   showName: "空号余额（条）",
          //   fixed: false
          // },
          // {
          //   prop: "riskBalance",
          //   showName: "反欺诈余额（条）",
          //   fixed: false
          // },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '充值',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-edit',
          },
          {
            optionName: '扣款',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'el-icon-success',
          },
        ],
      },
      pldel: false,
      ElIconUpload,
      ElIconEdit,
    }
  },
  name: 'SMSmsg',
  components: {
    TableTem,
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingRechargeConsumer/selectClientBalancePage',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    /**----------------获取客服列表---------- */
    getServices() {
      //    http://smsmixcloud-gateway/gateway/smcp-upms/user/selectAllSalesOrService?flag=2
      window.api.get(
        window.path.upms + 'user/selectAllSalesOrService',
        { flag: 2 },
        (res) => {
          this.services = res.data
        }
      )
    },
    /**----------------获取销售列表---------- */
    getSales() {
      window.api.get(
        window.path.upms + 'user/selectAllSalesOrService',
        { flag: 1 },
        (res) => {
          this.sales = res.data
        }
      )
    },
    beforeStep() {
      //上一步
      this.setPhoneSteps = 1
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    Query() {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].operatorId)
      }
      this.selectId = selectId.join(',')
    },
    Reload() {
      //重置
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
    },
    //--------------------------------------------------------
    //----------------充值-----------------------------
    // 发送请求方法
    InquireList() {
      window.api.post(window.path.omcs + 'operatinguser/getUserinfo', {}, (res) => {
        let resdata = []
        let tabledata = []
        let phonelength = res.data.phone.split(',')
        console.log(phonelength)
        this.phoneData = phonelength[0]
        this.phoneOriginal = phonelength
        for (var i = 0; i < phonelength.length; i++) {
          // 列表数据
          let a = {}
          a.Numbering = i + 1
          a.username = res.data.username
          a.phone = phonelength[i]
          resdata[resdata.length] = a
          // 登录手机号列表
          let b = {}
          b.index = i
          b.name = i + 1
          b.address = phonelength[i]
          tabledata[tabledata.length] = b
        }
        // this.tableDataObj.tableData=resdata
        this.tableD = tabledata
        // this.tableDataObj.loading2=false;
        // 存储个人信息
        this.roleInfo = res.data
      })
    },
    // 获取验证码倒计时
    CountdownCode() {
      if (this.phoneData) {
        --this.nmb
        const timer = setInterval((res) => {
          --this.nmb
          if (this.nmb < 1) {
            this.nmb = 120
            clearInterval(timer)
          }
        }, 1000)
        window.api.get(
          window.path.cpus +
            'code/sendVerificationCode?phone=' +
            this.phoneData +
            '&flag=3',
          {},
          (res) => {
            console.log(res)
            this.$message({
              type: 'success',
              duration: '2000',
              message: '验证码已发送至手机!',
            })
          }
        )
      } else {
        this.$message({
          message: '请先选中手机号码',
          type: 'warning',
        })
      }
    },
    //充值弹出层
    addphone(val) {
      this.tableRow = val.row
      window.api.get(
        window.path.cpus +
          'consumerclientinfo/selectClientAllInfoById/' +
          val.row.userId,
        {},
        (res) => {
          this.setphoneFrom.ruleForm2.consumerName = res.data.consumerName
          this.setphoneFrom.ruleForm2.compName = res.data.compName
          if (res.data.smsPayMethod == '1') {
            this.setphoneFrom.ruleForm2.smsPayMethod = '线下'
          } else if (res.data.smsPayMethod == '2') {
            this.setphoneFrom.ruleForm2.smsPayMethod = '支付宝'
          } else if (res.data.smsPayMethod == '3') {
            this.setphoneFrom.ruleForm2.smsPayMethod = '余额划扣'
          }

          // if (res.data.businessTypes =='1') {
          //   this.setphoneFrom.ruleForm2.businessTypes = "短信"
          // } else {
          //   this.setphoneFrom.ruleForm2.businessTypes = "空号检测"
          // }

          this.setphoneFrom.ruleForm2.smsPrice = res.data.smsPrice
        }
      )
      this.radio = 0
      this.LcpDialogVisible = true
    },
    //扣款弹出层
    detailsRow(val) {
      this.tableRow = val.row
      window.api.get(
        window.path.cpus +
          'consumerclientinfo/selectClientAllInfoById/' +
          val.row.userId,
        {},
        (res) => {
          this.formop.consumerName = res.data.consumerName
          this.formop.compName = res.data.compName
          this.formop.restSumSms = res.data.restSmsNum
          this.formop.emptyBalance = res.data.emptyBalance
          this.formop.riskBalance = res.data.riskBalance

          if (res.data.businessTypes == '1') {
            this.formop.businessTypes = '短信'
          } else {
            this.formop.businessTypes = '空号检测'
          }
        }
      )
      this.dialogFormVisible = true
    },
    showRow(row) {
      //赋值给radio
      this.radio = this.tableD.indexOf(row)
    },
    getCurrentRow(val) {
      console.log(val) //获取选中的index
      console.log(this.tableD[val].address) //获取对应的手机号
      this.phoneData = this.tableD[val].address //赋值手机号
    },
    cancel() {
      this.LcpDialogVisible = false //关闭弹出框
      this.setPhoneSteps = 1 //步进改为1
      this.setphoneFrom.ruleForm1.verCode = '' //验证码置空
      this.setphoneFrom.ruleForm2.setNewPhone = '' //手机号置空

      this.InquireList()
    },
    handelClose() {
      //×号关闭弹窗
      //点击关闭
      this.LcpDialogVisible = false //关闭弹出框
    },
    handelOptionButton(val) {
      //操作列表的点击
      if (val.methods == 'dele') {
        //点击删除
        console.log('dele')
        console.log(val)
        if (this.tableDataObj.tableData.length <= 1) {
          this.$message({
            type: 'error',
            duration: '2000',
            message: '最少存在一个手机号',
          })
        } else {
          let phoneF = []
          for (var i = 0; i < this.tableD.length; i++) {
            if (this.tableD[i].address != val.row.phone) {
              phoneF[phoneF.length] = this.tableD[i].address
            }
          }
          this.$confirms.confirmation(
            'put',
            '确认删除该手机号',
            window.path.omcs +
              'operatinguser/updatePhone?phone=' +
              phoneF.join(','),
            {},
            (res) => {
              this.InquireList()
            }
          )
        }
      }
    },
    // 验证手机号
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (val == 'ruleForm1') {
          if (valid) {
            window.api.get(
              window.path.cpus + 'code/checkVerificationCode',
              {
                code: this.setphoneFrom.ruleForm1.verCode,
                flag: '3',
                // phone:this.phoneData
              },
              (res) => {
                if (res.code == 200) {
                  window.api.post(
                    window.path.omcs +
                      'operatingRechargeConsumer/rechargeOrDeduction',
                    this.setphoneFrom.ruleForm2,
                    (res) => {
                      if (res.code == 200) {
                        this.$message({
                          type: 'success',
                          duration: '2000',
                          message: '充值成功',
                        })
                        this.cancel()
                        this.gettableLIst()
                      } else {
                        this.$message({
                          type: 'warning',
                          duration: '2000',
                          message: '充值失败',
                        })
                        this.cancel()
                        this.gettableLIst()
                      }
                    }
                  )
                } else {
                  this.$message({
                    type: 'error',
                    duration: '2000',
                    message: '验证码无效',
                  })
                }
              }
            )
          } else {
            console.log('error submit!!')
            return false
          }
        } else {
          if (valid) {
            this.setphoneFrom.ruleForm2.rechargeName =
              this.tableRow.consumerName
            this.setphoneFrom.ruleForm2.userId = this.tableRow.userId
            this.setphoneFrom.ruleForm2.rechargePrice =
              this.setphoneFrom.ruleForm2.smsPrice
            this.setphoneFrom.ruleForm2.type = '2'
            if (this.setphoneFrom.ruleForm2.rechargeAmount > 30000) {
              this.setPhoneSteps = 2
            } else if (this.setphoneFrom.ruleForm2.rechargeAmount > 1000000) {
              this.$message({
                type: 'warning',
                duration: '2000',
                message: '充值金额最多100万',
              })
            } else if (this.setphoneFrom.ruleForm2.rechargeNum > 30000000) {
              this.$message({
                type: 'warning',
                duration: '2000',
                message: '充值条数最多3000万',
              })
            } else {
              this.$confirms.confirmation(
                'post',
                '确认充值吗',
                window.path.omcs + 'operatingRechargeConsumer/rechargeOrDeduction',
                this.setphoneFrom.ruleForm2,
                (res) => {
                  if (res.code == 200) {
                    // this.$message({
                    //   type: 'success',
                    //   duration: '2000',
                    //   message: '充值成功',
                    // })
                    this.cancel()
                    this.gettableLIst()
                  } else {
                    // this.$message({
                    //   type: 'warning',
                    //   duration: '2000',
                    //   message: '充值失败',
                    // })
                    this.cancel()
                    this.gettableLIst()
                  }
                }
              )
              // window.api.post(window.path.omcs+"operatingRechargeConsumer/rechargeOrDeduction",this.setphoneFrom.ruleForm2,res=>{
              //     if (res.code == 200) {
              //       this.$message({
              //           type: 'success',
              //           duration:'2000',
              //           message:"充值成功"
              //       });
              //       this.cancel();
              //       this.gettableLIst();
              //       this.payMoneys = true;
              //     } else {
              //         this.$message({
              //             type: 'warning',
              //             duration:'2000',
              //             message:"充值失败"
              //         });
              //         this.cancel();
              //         this.gettableLIst();
              //         this.payMoneys = true;
              //     }
              // })
            }
            // let flag=true;
            // for(var i=0;i<this.phoneOriginal.length;i++){
            //     if(this.phoneOriginal[i]==this.setphoneFrom.ruleForm2.setNewPhone){
            //         flag=false;
            //         break;
            //     }
            // }
            // if(flag){
            //     this.phoneOriginal[this.phoneOriginal.length]=this.setphoneFrom.ruleForm2.setNewPhone
            //     window.api.put(window.path.omcs+"operatinguser/updatePhone?phone="+this.phoneOriginal.join(","),{},res=>{
            //         this.cancel();
            //     })
            // }else{
            //     this.$message({
            //         type: 'error',
            //         duration:'2000',
            //         message:"该手机号已存在,不可重复添加"
            //     });
            // }
            console.log('submit!!')
          } else {
            console.log('error submit!!')
            return false
          }
        }
      })
    },
    //------------------------扣款------------------
    submitForms(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.payMoney = false
          this.formop.rechargeName = this.tableRow.consumerName
          this.formop.userId = this.tableRow.userId
          this.formop.type = '1'
          window.api.get(
            window.path.omcs +
              'operatingRechargeConsumer/selectUserRestSmsSun?productId=' +
              this.formop.rechargeCategory +
              '&restSmsSum=' +
              this.formop.rechargeNum +
              '&username=' +
              this.formop.rechargeName,
            {},
            (res) => {
              if (res.code == 200) {
                window.api.post(
                  window.path.omcs +
                    'operatingRechargeConsumer/rechargeOrDeduction',
                  this.formop,
                  (res) => {
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '扣款成功',
                      })
                      this.dialogFormVisible = false
                      this.payMoney = true
                      this.gettableLIst()
                    } else {
                      this.$message({
                        type: 'warning',
                        duration: '2000',
                        message: '扣款失败',
                      })
                      this.dialogFormVisible = false
                      this.payMoney = true
                      this.gettableLIst()
                    }
                  }
                )
              } else {
                this.$message({
                  type: 'warning',
                  duration: '2000',
                  message: '扣款条数不足',
                })
                this.payMoney = true
              }
            }
          )
        } else {
          console.log('error submit!!')
          this.payMoney = true
          return false
        }
      })
    },
    //列表操作
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        //充值
        this.addphone(val)
      }

      if (val.methods == 'dellog') {
        //扣款
        this.detailsRow(val)
      }
    },
  },
  created() {
    this.getServices()
    this.getSales()
    this.InquireList()
  },
  activated() {
    this.getServices()
    this.getSales()
    this.InquireList()
  },
  mounted() {},
  watch: {
    tabelAlllist: {
      handler() {
        this.gettableLIst()
      },
      deep: true, //深度监听
      immediate: true,
    },
    //监听用户单价
    'setphoneFrom.ruleForm2.smsPrice': function (val) {
      if (val) {
        let a = this.setphoneFrom.ruleForm2.smsPrice
        let b = this.setphoneFrom.ruleForm2.rechargeAmount
        let c = b / a
        if (c.toString().indexOf('.') != -1) {
          this.setphoneFrom.ruleForm2.rechargeNum = c.toFixed(0).toString()
        } else {
          this.setphoneFrom.ruleForm2.rechargeNum = c.toString()
        }
      } else {
        this.setphoneFrom.ruleForm2.rechargeNum = ''
      }
    },
    //监听到款金额
    'setphoneFrom.ruleForm2.rechargeAmount': function (val) {
      if (val) {
        let a = this.setphoneFrom.ruleForm2.smsPrice
        let b = this.setphoneFrom.ruleForm2.rechargeAmount
        let c = b / a
        if (c.toString().indexOf('.') != -1) {
          this.setphoneFrom.ruleForm2.rechargeNum = c.toFixed(0).toString()
        } else {
          this.setphoneFrom.ruleForm2.rechargeNum = c.toString()
        }
      } else {
        this.setphoneFrom.ruleForm2.rechargeNum = ''
      }
    },
    LcpDialogVisible: function (val) {
      if (val == false) {
        this.setphoneFrom.ruleForm1.verCode = '' //验证码置空
        this.setphoneFrom.ruleForm2.setNewPhone = '' //手机号置空
        this.setphoneFrom.ruleForm2.rechargeNum = '' //充值条数置空
        this.setphoneFrom.ruleForm2.rechargeCategory = '' //充值类型置空
        this.setPhoneSteps = 1 //步进改为1
        this.radio = ''
        this.$refs.ruleForm1.resetFields()
        this.$refs.ruleForm2.resetFields()
        this.InquireList()
      }
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
