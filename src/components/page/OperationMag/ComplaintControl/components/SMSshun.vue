<template>
  <div>
    <div class="OuterFrame fillet">
      <div class="from">
        <el-form
          :inline="true"
          label-width="82px"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.userName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="省份" prop="province">
            <el-input
              v-model="formInline.province"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="城市" prop="city">
            <el-input
              v-model="formInline.city"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="运营商" prop="operator">
            <el-select
              class="input-w"
              v-model="formInline.operator"
              clearable
              placeholder="请选择"
            >
              <el-option label="全部" value=""> </el-option>
              <el-option label="移动" value="1"> </el-option>
              <el-option label="联通" value="2"> </el-option>
              <el-option label="电信" value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              class="input-w"
              v-model="formInline.status"
              clearable
              placeholder="请选择"
            >
              <el-option label="待处理" value="0"> </el-option>
              <el-option label="处理中" value="1"> </el-option>
              <el-option label="已成功" value="2"> </el-option>
              <el-option label="已下发" value="3"> </el-option>
              <el-option label="已失败" value="4"> </el-option>
              <el-option label="异常" value="-1"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="time">
            <el-date-picker
              :shortcuts="pickerOptions && pickerOptions.shortcuts"
              :disabled-date="pickerOptions && pickerOptions.disabledDate"
              :cell-class-name="pickerOptions && pickerOptions.cellClassName"
              v-model="formInline.time"
              type="datetimerange"
              @change="time"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          stripe
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">创建任务</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="SMSshun"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>
                {{ scope.row.userName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="110px">
            <template v-slot="scope">
              <div>
                {{ scope.row.mobile }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="省份" title="省份">
            <template v-slot="scope">
              <div>
                {{ scope.row.province }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="城市" title="城市">
            <template v-slot="scope">
              <div>
                {{ scope.row.city }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="运营商" title="运营商">
            <template v-slot="scope">
              <div
                v-if="scope.row.operator == 1"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yidong"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div
                v-else-if="scope.row.operator == 2"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-liantong"
                  style="font-size: 16px; color: #f56c6c"
                ></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div
                v-else-if="scope.row.operator == 3"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-dianxin"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">电信</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="通道ID" title="通道ID">
            <template v-slot="scope">
              <div>
                {{ scope.row.channelId }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="消息id" title="消息id">
            <template v-slot="scope">
              <div>
                {{ scope.row.msgId }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="短信内容" title="短信内容">
            <template v-slot="scope">
              <div>
                {{ scope.row.content }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态码" title="状态码">
            <template v-slot="scope">
              <div>
                {{ scope.row.code }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                待处理
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
              >
                处理中
              </el-tag>
              <el-tag
:disable-transitions="true" v-else-if="scope.row.status == 2" type="primary" effect="dark">
                已成功
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="success"
                effect="dark"
              >
                已下发
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 4"
                type="danger"
                effect="dark">
                已失败
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == -1"
                type="danger"
                effect="dark"
              >
                异常
              </el-tag>
              <el-tag
:disable-transitions="true" v-else type="info" effect="dark"> 未知 </el-tag>
              <!-- <span v-if="scope.row.status ==0"> 待处理</span>
                        <span style="color:red" v-else-if="scope.row.status == -1">异常</span>
                        <span v-else-if="scope.row.status ==1">处理中</span>
                        <span v-else-if="scope.row.status ==2">已成功</span>
                        <span v-else-if="scope.row.status ==3">已下发</span>
                        <span v-else-if="scope.row.status ==4">已失败</span>
                        <span v-else>未知</span> -->
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
              </div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <el-dialog
        :title="titleMap"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="800px"
      >
        <el-form
          :inline="true"
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <div>
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="formop.username"
                autocomplete="off"
                class="input-w"
                placeholder="用户名"
              ></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="时间段" prop="time">
              <el-date-picker
                v-model="formop.time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleTime"
              >
              </el-date-picker>
              <el-button
                style="margin-left: 10px"
                type="primary"
                circle
                @click="handleSearch('formop')"
              ><el-icon><Search /></el-icon></el-button>
              <div style="color: #909399">
                tips: 请根据用户名和时间段，查询提取数量！
              </div>
            </el-form-item>
          </div>
          <template v-if="formop.provinceData.length > 0">
            <div style="color: #606266; margin-left: 60px; margin-bottom: 10px">
              下发短信总条数为：{{ smsTotal }}条
            </div>
            <div
              v-for="(item, index) in formop.provinceData"
              :key="index + 'a'"
            >
              <el-form-item
                label="短链信息"
                :prop="'provinceData.' + index + '.province'"
              >
                <div
                  style="
                    display: flex;
                    border-radius: 5px;
                    background-color: #f5f7fa;
                    border-color: #e4e7ed;
                    color: #c0c4cc;
                    border: 1px solid;
                    padding: 0 10px;
                  "
                >
                  <div>
                    省份
                    <span style="margin-left: 8px">{{ item.province }}</span>
                  </div>
                  <div style="margin-left: 18px">
                    短链总数
                    <span style="margin-left: 8px">{{ item.countSum }}个</span>
                  </div>
                </div>
                <!-- <el-input disabled v-model="item.province" autocomplete="off" class="input-w" placeholder="省份"></el-input> -->
              </el-form-item>
              <el-form-item
                label="提取数量"
                :prop="'provinceData.' + index + '.count'"
                :rules="{
                  required: true,
                  message: '提取数量不能为空',
                  trigger: 'blur',
                }"
              >
                <el-input-number
                  class="input-w"
                  v-model="item.count"
                  :min="0"
                  :max="item.countSum"
                  label="提取数量"
                ></el-input-number>
                <!-- <el-input v-model="item.count" autocomplete="off" class="input-w" placeholder="数量"></el-input> -->
              </el-form-item>
            </div>
          </template>
          <div v-else>
            <el-empty :image-size="100"></el-empty>
          </div>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'
import moment from 'moment'
export default {
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      titleMap: '创建任务',
      isFirstEnter: false,
      //新增弹出框显示隐藏
      dialogFormVisible: false,
      formInline: {
        userName: '',
        status: '',
        createStartTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        createEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        operator: '',
        province: '',
        city: '',
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '最近三天',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              return [start, end]
            },
          },
          {
            text: '最近一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            },
          },
        ],
      },
      tabelAlllist: {
        //存储查询数据
        userName: '',
        status: '',
        createStartTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        createEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        operator: '',
        province: '',
        city: '',
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
      formop: {
        username: '',
        startTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ],
        provinceData: [],
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'change' },
        ],
        time: [{ required: true, message: '请选择时间', trigger: 'change' }],
      },
      smsTotal: 0
    }
  },
  methods: {
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingdivert/sms/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      this.formInline.userName = ''
      this.formInline.mobile = ''
      this.formInline.status = ''
      this.formInline.createStartTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline.createEndTime = moment(Date.now()).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      this.formInline.time = [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        Date.now(),
      ]
      Object.assign(this.tabelAlllist, this.formInline)
      // Object.assign(this.tabelAlllist,this.formInline);
      this.gettableLIst()
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    time(val) {
      if (val) {
        this.formInline.createStartTime = moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.formInline.createEndTime = moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline.createStartTime = ""
        this.formInline.createEndTime = ""
      }
    },
    addopt() {
      this.titleMap = '创建任务'
      this.dialogFormVisible = true
    },
    handleTime(val) {
      if (val) {
        this.formop.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formop.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formop.startTime = ''
        this.formop.endTime = ''
      }
    },
    handleSearch(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let data = {
            username: this.formop.username,
            startTime: this.formop.startTime,
            endTime: this.formop.endTime,
          }
          window.api.post(
            window.path.omcs + 'operatingdivert/message/statistics',
            data,
            (res) => {
              if (res.code == 200) {
                if (res.data.provinceData.length == 0) {
                  this.$message.warning('暂无数据!')
                }
                this.formop.provinceData = res.data.provinceData.map((item) => {
                  return {
                    province: item.province,
                    countSum: item.count,
                    count: '',
                  }
                })

                this.smsTotal = res.data.total
              } else {
                this.$message.error(res.msg)
              }
            }
          )
        } else {
          this.$message.error('请确认信息是否有遗漏！')
          return false
        }
      })
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let data = {
            username: this.formop.username,
            beginTime: this.formop.startTime,
            endTime: this.formop.endTime,
            // provinceNumList: this.formop.provinceData,
          }
          data.provinceNumList = this.formop.provinceData.map((item) => {
            return {
              province: item.province,
              count: item.count,
            }
          })
          if (this.formop.provinceData.length) {
            window.api.post(
              window.path.omcs + 'operatingshortlinkextractiontask',
              data,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false
                  this.$router.push({
                    path: '/shortTask',
                  })
                  // this.gettableLIst()
                  this.$message({
                    type: 'success',
                    duration: '2000',
                    message: res.msg,
                  })
                } else {
                  this.$message.error(res.msg)
                }
              }
            )
          } else {
            this.$message.error('该用户暂无提取数量，请确认！')
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
        this.formop.provinceData = []
      }
    },
  },
  emits: ['pick'],
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>