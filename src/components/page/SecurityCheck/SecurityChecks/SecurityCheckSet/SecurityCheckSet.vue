<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon>
          安全校验设置</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="security-box fillet">
      <div style="color: #f56c6c; padding-bottom: 14px">
        <el-icon><el-icon-warning /></el-icon>
        该页面为系统安全校验设置，请谨慎操作！
      </div>
      <el-form
        :model="ruleForm"
        label-position="left"
        ref="ruleForm"
        label-width="150px"
        class="demo-ruleForm"
      >
        <el-form-item
          label="黑名单验证等级"
          prop="type"
          style="padding: 30px 50px; border-bottom: 1px solid #f8f8f8"
        >
          <el-checkbox-group v-model="ruleForm.type">
            <el-checkbox label="1">一级</el-checkbox>
            <el-checkbox label="2">二级</el-checkbox>
          </el-checkbox-group>
          <div style="color: #f56c6c; font-size: 12px">
            <el-icon><el-icon-warning /></el-icon> 注意：不勾选系统将跳过该验证
          </div>
        </el-form-item>

        <el-form-item
          label="敏感词验证等级"
          prop="type1"
          style="padding: 30px 50px; border-bottom: 1px solid #f8f8f8"
        >
          <el-checkbox-group v-model="ruleForm.type1">
            <el-checkbox label="1">一级</el-checkbox>
            <el-checkbox label="2">二级</el-checkbox>
          </el-checkbox-group>
          <div style="color: #f56c6c; font-size: 12px">
            <el-icon><el-icon-warning /></el-icon> 注意：不勾选系统将跳过该验证
          </div>
        </el-form-item>

        <el-form-item
          label="是否启用黑词白名单"
          prop="resource"
          style="padding: 30px 50px"
        >
          <el-radio-group v-model="ruleForm.resource">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item style="padding: 30px 50px; text-align: right">
          <el-button
            type="primary"
            @click="setCheck"
            v-if="setShows"
            style="padding: 9px 24px"
            >设 置</el-button
          >
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
            v-if="!setShows"
            style="padding: 9px 24px"
            >保 存</el-button
          >
          <el-button
            @click="resetForm('ruleForm')"
            v-if="!setShows"
            style="padding: 9px 24px"
            >取 消</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  components: {
    ElIconLxEmoji,
    ElIconWarning,
  },
  name: 'UserMag',
  data() {
    return {
      setShows: true,
      ruleForm: {
        type: [],
        type1: [],
        resource: '2',
      },
    }
  },
  methods: {
    setCheck() {
      //设置
      this.setShows = false
    },
    submitForm(formName) {
      //保 存
      let formData = JSON.stringify(this.ruleForm) //表单数据
      this.$confirms.confirmation(
        'post',
        '是否要保存此设置?',
        'api/del',
        {},
        (res) => {}
      )
      this.setShows = true
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.setShows = true
    },
  },
}
</script>

<style scoped>
.security-box {
  padding: 20px;
}
</style>

<style>
.security-box .el-form-item--mini.el-form-item,
.security-box .el-form-item--small.el-form-item {
  margin-bottom: 0px;
}
</style>
