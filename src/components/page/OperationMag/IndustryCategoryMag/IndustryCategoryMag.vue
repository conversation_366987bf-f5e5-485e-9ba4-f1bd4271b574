<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="行业类别" prop="categoriesName">
            <el-input
              v-model="formInline.categoriesName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建人" prop="buildName">
            <el-input
              v-model="formInline.buildName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="dataCreat">
            <el-date-picker
              class="input-w"
              v-model="formInline.dataCreat"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button type="primary" @click="addopt">新增</el-button>
            <el-button
              type="danger"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delAll"
              >批量删除</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增行业类别 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="行业类别" prop="industryName">
            <el-input
              class="input-w"
              v-model="formop.industryName"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增行业类别结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
export default {
  components: {
    TableTem
  },
  name: 'IndustryCategoryMag',
  data() {
    return {
      isFirstEnter: false,
      titleMap: {
        add: '添加行业类别',
        edit: '编辑行业类别',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      id: '', //行业类别id
      tableRow: '', //列表行数据
      selectId: '',
      formop: {
        industryName: '',
      },
      rules: {
        industryName: [
          { required: true, message: '请输入行业类别名称', trigger: 'blur' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
          {
            pattern: /^[\u4e00-\u9fa5]+$/,
            message: '含有非法字符（只能输入汉字！）',
          },
        ],
      },
      formInline: {
        categoriesName: '',
        buildName: '',
        beginTime: '',
        endTime: '',
        dataCreat: [],
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        categoriesName: '',
        buildName: '',
        beginTime: '',
        endTime: '',
        dataCreat: [],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        custom: true,
        total: 0,
        id: 'IndustryCategoryMag',
        tableData: [],
        tableLabel: [
          {
            prop: 'id',
            showName: 'ID',
            width: '70',
          },
          {
            prop: 'industryName',
            showName: '行业类别',
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'EditPen',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'Delete',
            color: '#f56c6c',
          },
        ],
      },
    }
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingindustrycategories/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //---------------------列表操作-------------------------------
    handelSelection(val) {
      //列表复选框的值
      if (val.length > 0) {
        let arr = []
        val.forEach(item => {
          arr.push(item.id)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    hande: function (val) {
      if (val) {
        //获取查询时间框的值
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime =
          this.moment(val[1]).format('YYYY-MM-DD ') + '23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.get(
              window.path.omcs +
                'operatingindustrycategories/exists/' +
                this.formop.industryName,
              {},
              (res) => {
                if (res == false) {
                  this.$message({
                    message: '行业类型重复，重新输入！',
                    type: 'warning',
                  })
                } else {
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs + 'operatingindustrycategories',
                    this.formop,
                    () => {
                      this.dialogFormVisible = false
                      this.gettableLIst()
                    }
                  )
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            if (this.formop.industryName == this.tableRow.industryName) {
              this.$confirms.confirmation(
                'put',
                '确认执行此操作吗？',
                window.path.omcs + 'operatingindustrycategories',
                this.formop,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              window.api.get(
                window.path.omcs +
                  'operatingindustrycategories/exists/' +
                  this.formop.industryName,
                {},
                (res) => {
                  if (res == false) {
                    this.$message({
                      message: '行业类型重复，重新输入！',
                      type: 'warning',
                    })
                  } else {
                    this.$confirms.confirmation(
                      'put',
                      '确认执行此操作吗？',
                      window.path.omcs + 'operatingindustrycategories',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  }
                }
              )
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    //批量删除
    delAll() {
      window.api.get(
        window.path.omcs +
          'operatingindustrycategories/checkNoteInCodeCount/' +
          this.selectId,
        {},
        (res) => {
          if (res.code == 400) {
            this.$message({
              message: '行业类别已被使用，不可以删除！',
              type: 'warning',
            })
          } else {
            //批量删除
            this.$confirms.confirmation(
              'delete',
              '此操作将永久删除该数据, 是否继续？',
              window.path.omcs +
                'operatingindustrycategories/deleteBatch/' +
                this.selectId,
              {},
              () => {
                this.gettableLIst()
              }
            )
          }
        }
      )
    },
    detailsRow(val) {
      window.api.get(
        window.path.omcs +
          'operatingindustrycategories/checkNoteInCodeCount/' +
          val.row.id,
        {},
        (res) => {
          if (res.code == 400) {
            this.$message({
              message: '行业类别已被使用，不可以编辑！',
              type: 'warning',
            })
          } else {
            //编辑
            this.dialogFormVisible = true
            this.dialogStatus = 'edit'
            this.tableRow = val.row
            this.id = val.row.id
            //清除编辑默认值
            this.$nextTick(() => {
              this.$refs.formop.resetFields()
              Object.assign(this.formop, val.row)
            })
          }
        }
      )
    },
    delRow: function (val) {
      window.api.get(
        window.path.omcs +
          'operatingindustrycategories/checkNoteInCodeCount/' +
          val.row.id,
        {},
        (res) => {
          if (res.code == 400) {
            this.$message({
              message: '行业类别已被使用，不可以删除！',
              type: 'warning',
            })
          } else {
            //删除
            this.$confirms.confirmation(
              'delete',
              '此操作将永久删除该数据, 是否继续？',
              window.path.omcs + 'operatingindustrycategories/' + val.row.id,
              {},
              () => {
                this.gettableLIst()
              }
            )
          }
        }
      )
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }

      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
