<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="订单号" label-width="82px" prop="orderNum">
            <el-input
              v-model="formInline.orderNum"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="充值类型" label-width="80px" prop="productId">
            <el-select
              v-model="formInline.productId"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="请选择" value=""></el-option>
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="充值时间" label-width="82px" prop="dataCreat">
            <el-date-picker
              class="input-time"
              v-model="formInline.dataCreat"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
        <el-button type="primary" plain @click="export1()">导出</el-button>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem
              :tableDataObj="tableDataObj"
              @handelOptionButton="handelOptionButton"
              @handelSelection="handelSelection"
            ></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="rechargerecord"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="充值账号" title="充值账号" width="140">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="充值类型" title="充值类型">
            <template v-slot="scope">
              <div>{{ scope.row.productName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="充值条数" title="充值条数">
            <template v-slot="scope">
              <div>{{
                scope.row.rechargeNum + ' (' + scope.row.unit + ')'
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="订单号" title="订单号">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.orderNumber"
                :content="scope.row.orderNumber"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
              <!-- <span>{{ scope.row.orderNumber }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="充值日期" title="充值日期" width="170">
            <template v-slot="scope">
              <div>{{ scope.row.rechargeTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.rechargeNote"
                :content="scope.row.rechargeNote"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
              <!-- <span>{{ scope.row.rechargeNote }}</span> -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
        
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增运营商 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="100px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="运营商名称" prop="operatorName">
            <el-input
              v-model="formop.operatorName"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增运营商结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    TableTem,
    UserLists,
    Tooltip
  },
  name: 'rechargerecord',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      userFlag: false,
      nameover: '',
      titleMap: {
        add: '增加运营商',
        edit: '编辑运营商',
      },
      options: [],
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      operatorId: '', //运营商id

      tableRow: '', //当前行列表数据
      selectId: '',
      formop: {
        //表单数据
        operatorName: '',
      },
      rules: {
        //验证规则
        operatorName: [
          { required: true, message: '请输入运营商名称', trigger: 'blur' },
          {
            min: 1,
            max: 10,
            message: '长度在 1 到 10 个字符',
            trigger: ['blur', 'change'],
          },
          {
            pattern: /^[\u4e00-\u9fa5]+$/,
            message: '含有非法字符（只能输入汉字！）',
          },
        ],
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        productId: '',
        orderNum: '',
        dataCreat: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      formInline: {
        //查询数据
        username: '',
        productId: '',
        orderNum: '',
        dataCreat: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        currentPage: '1',
        pageSize: '10',
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'username',
            showName: '充值账号',
            fixed: false,
          },
          {
            prop: 'orderNumber',
            showName: '订单号',
            fixed: false,
            width: '180',
          },
          {
            prop: 'rechargeNum',
            showName: '充值条数（条）',
            fixed: false,
          },
          {
            prop: 'productId',
            showName: '充值类型',
            fixed: false,
            formatData: function (val) {
              if (val == '1') {
                return (val = '短信 ')
              } else if (val == '2') {
                return (val = '彩信')
              } else if (val == '3') {
                return (val = '视频短信')
              } else if (val == '4') {
                return (val = '国际短信')
              } else if (val == '5') {
                return (val = '闪验')
              } else if (val == '6') {
                return (val = '语音验证码')
              } else if (val == '7') {
                return (val = '语音通知')
              }
            },
          },
          {
            prop: 'rechargeName',
            showName: '充值人',
            fixed: false,
          },
          {
            prop: 'rechargeTime',
            showName: '充值时间',
            fixed: false,
            width: '150',
            formatData: (val) => window.common.formatDate(val),
          },
          {
            prop: 'rechargeNote',
            showName: '备注',
            width: '200',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      pldel: false,
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //充值记录列表
      this.tableDataObj.loading2 = true
      window.api.get(
        window.path.recharge + 'manager/recharge/page',
        this.tabelAlllist,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.total = res.data.total
            this.tableDataObj.tableData = res.data.records
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    Query() {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].operatorId)
      }
      this.selectId = selectId.join(',')
    },
    Reload() {
      //重置
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //导出 （发送记录）
    export1() {
      let aa = {}
      Object.assign(aa, this.tabelAlllist)
      // aa.flag = this.flag;
      // aa.sendBeginTime = this.Times.beginTime;
      // aa.sendEndTime = this.Times.endTime;
      // aa.isDownload = 1;
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingRechargeConsumer/export',
          aa,
          '充值记录报表.xlsx'
        )
      }
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //验证唯一性
            window.api.post(
              window.path.omcs + 'operatingoperatorinfo/validate',
              { operatorName: this.formop.operatorName },
              (res) => {
                if (res.code == 400) {
                  this.$message({
                    message: '运营商名称重复，重新输入！',
                    type: 'warning',
                  })
                } else {
                  //添加
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs + 'operatingoperatorinfo',
                    {
                      operatorName: this.formop.operatorName,
                    },
                    () => {
                      this.dialogFormVisible = false
                      this.gettableLIst()
                    }
                  )
                }
              }
            )
          } else {
            //编辑
            this.formop.operatorId = this.operatorId
            //验证唯一
            if (this.formop.operatorName == this.tableRow.operatorName) {
              this.$confirms.confirmation(
                'put',
                '确认执行此操作吗？',
                window.path.omcs + 'operatingoperatorinfo',
                this.formop,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              window.api.post(
                window.path.omcs + 'operatingoperatorinfo/validate',
                { operatorName: this.formop.operatorName },
                (res) => {
                  if (res.code == 400) {
                    this.$message({
                      message: '运营商名称重复，重新输入！',
                      type: 'warning',
                    })
                  } else {
                    this.$confirms.confirmation(
                      'put',
                      '确认执行此操作吗？',
                      window.path.omcs + 'operatingoperatorinfo',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  }
                }
              )
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    addopt() {
      //添加
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    delAll() {},
    detailsRow(val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.operatorId = val.row.operatorId
      this.tableRow = val.row
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val.row)
      })
    },
    //删除
    delRow: function (val) {
      //删除验证
      window.api.get(
        window.path.omcs +
          'operatingoperatorinfo/checkChannelIsUsed/' +
          val.row.operatorId,
        {},
        (res) => {
          if (res.code == 400) {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          } else {
            //删除
            this.$confirms.confirmation(
              'delete',
              '此操作将永久删除该数据, 是否继续？',
              window.path.omcs + 'operatingoperatorinfo/' + val.row.operatorId,
              {},
              () => {
                this.gettableLIst()
              }
            )
          }
        }
      )
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }

      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  // created(){
  //   // console.log(JSON.parse(localStorage.getItem('list')));
  //   this.options = JSON.parse(localStorage.getItem('list'))
  // },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  watch: {
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
