<template>
  <el-dialog
    title="签名查询工具"
    v-model="visible"
    width="1200px"
    :before-close="handleClose"
    class="signature-query-dialog"
  >
    <!-- 查询表单 -->
    <div class="query-form">
      <el-form :inline="true" :model="queryForm" class="demo-form-inline">
        <el-form-item label="用户名">
          <el-input 
            v-model="queryForm.consumerName" 
            placeholder="请输入用户名" 
            clearable
            style="width: 180px;"
          />
        </el-form-item>
        <el-form-item label="签名">
          <el-input 
            v-model="queryForm.signature" 
            placeholder="请输入签名" 
            clearable
            style="width: 180px;"
          />
        </el-form-item>
        <el-form-item label="签名属性">
          <el-select 
            v-model="queryForm.signatureAttr" 
            placeholder="请选择" 
            clearable
            style="width: 120px;"
          >
            <el-option label="全部" value="0" />
            <el-option label="报备签名" value="1" />
            <el-option label="自定义签名" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        max-height="400"
        @row-click="handleRowClick"
      >
        <el-table-column prop="consumerName" label="用户名称" width="140" show-overflow-tooltip />
        <el-table-column prop="signature" label="签名" width="150" show-overflow-tooltip />
        <el-table-column prop="contentExample" label="短信示例" width="200" show-overflow-tooltip />
        <el-table-column prop="serviceType" label="短信类型" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getServiceTypeTag(scope.row.serviceType).type" 
              size="small"
            >
              {{ getServiceTypeTag(scope.row.serviceType).text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="实名状态" width="300" align="center">
          <template #default="scope">
            <div class="realname-status">
              <!-- 移动 -->
              <div class="operator-item">
                <i class="iconfont icon-yidong operator-icon yd"></i>
                <el-tag 
                  :type="scope.row.ydRealNameStatus == 1 ? 'success' : 'info'" 
                  size="small"
                >
                  {{ scope.row.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                </el-tag>
              </div>
              <!-- 联通 -->
              <div class="operator-item">
                <i class="iconfont icon-liantong operator-icon lt"></i>
                <el-tag 
                  :type="scope.row.ltRealNameStatus == 1 ? 'success' : 'info'" 
                  size="small"
                >
                  {{ scope.row.ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                </el-tag>
              </div>
              <!-- 电信 -->
              <div class="operator-item">
                <i class="iconfont icon-dianxin operator-icon dx"></i>
                <el-tag 
                  :type="scope.row.dxRealNameStatus == 1 ? 'success' : 'info'" 
                  size="small"
                >
                  {{ scope.row.dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              link 
              size="small"
              @click.stop="handleViewDetail(scope.row)"
            >
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog
      title="签名详情"
      v-model="detailVisible"
      width="800px"
      append-to-body
      class="signature-detail-dialog"
    >
      <div v-loading="detailLoading" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="签名">
            {{ detailData.signature }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名称">
            {{ detailData.consumerName }}
          </el-descriptions-item>
          <el-descriptions-item label="短信示例">
            {{ detailData.contentExample }}
          </el-descriptions-item>
          <el-descriptions-item label="短信类型">
            <el-tag :type="getServiceTypeTag(detailData.serviceType).type" size="small">
              {{ getServiceTypeTag(detailData.serviceType).text }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="签名属性">
            <el-tag :type="detailData.signatureAttr == 1 ? 'success' : 'warning'" size="small">
              {{ detailData.signatureAttr == 1 ? '报备签名' : '自定义签名' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(detailData.createTime) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 实名状态详情 -->
        <div class="realname-detail" v-if="detailData.signatureId">
          <h4>实名状态详情</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="operator-card yd-card">
                <template #header>
                  <div class="card-header">
                    <i class="iconfont icon-yidong"></i>
                    <span>移动</span>
                  </div>
                </template>
                <div class="status-content">
                  <el-tag 
                    :type="detailData.ydRealNameStatus == 1 ? 'success' : 'info'" 
                    size="large"
                  >
                    {{ detailData.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                  <div v-if="detailData.ydAvalibleType" class="available-types">
                    <span class="label">可用类型：</span>
                    <el-tag 
                      v-for="type in detailData.ydAvalibleType.split(',')" 
                      :key="type"
                      size="small" 
                      type="success"
                      style="margin-right: 5px;"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="operator-card lt-card">
                <template #header>
                  <div class="card-header">
                    <i class="iconfont icon-liantong"></i>
                    <span>联通</span>
                  </div>
                </template>
                <div class="status-content">
                  <el-tag 
                    :type="detailData.ltRealNameStatus == 1 ? 'success' : 'info'" 
                    size="large"
                  >
                    {{ detailData.ltRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                  <div v-if="detailData.ltAvalibleType" class="available-types">
                    <span class="label">可用类型：</span>
                    <el-tag 
                      v-for="type in detailData.ltAvalibleType.split(',')" 
                      :key="type"
                      size="small" 
                      type="success"
                      style="margin-right: 5px;"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="operator-card dx-card">
                <template #header>
                  <div class="card-header">
                    <i class="iconfont icon-dianxin"></i>
                    <span>电信</span>
                  </div>
                </template>
                <div class="status-content">
                  <el-tag 
                    :type="detailData.dxRealNameStatus == 1 ? 'success' : 'info'" 
                    size="large"
                  >
                    {{ detailData.dxRealNameStatus == 1 ? '已实名' : '未实名' }}
                  </el-tag>
                  <div v-if="detailData.dxAvalibleType" class="available-types">
                    <span class="label">可用类型：</span>
                    <el-tag 
                      v-for="type in detailData.dxAvalibleType.split(',')" 
                      :key="type"
                      size="small" 
                      type="success"
                      style="margin-right: 5px;"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 资质图片 -->
        <div class="qualification-images" v-if="detailData.imgUrl">
          <h4>资质图片</h4>
          <div class="image-gallery">
            <el-image
              v-for="(img, index) in getImageList(detailData.imgUrl)"
              :key="index"
              :src="img"
              :preview-src-list="getImageList(detailData.imgUrl)"
              fit="cover"
              class="qualification-image"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { Search, Refresh, View } from '@element-plus/icons-vue'
import moment from 'moment'

export default {
  name: 'SignatureQuery',
  components: {
    Search,
    Refresh,
    View
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  data() {
    return {
      loading: false,
      detailLoading: false,
      detailVisible: false,
      queryForm: {
        consumerName: '',
        signature: '',
        signatureAttr: ''
      },
      tableData: [],
      pagination: {
        current: 1,
        size: 20,
        total: 0
      },
      detailData: {}
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.handleQuery()
      } else {
        this.handleReset()
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      this.loading = true
      const params = {
        current: this.pagination.current,
        size: this.pagination.size,
        ...this.queryForm
      }
      
      window.api.post(
        window.path.omcs + 'consumerSignature/page',
        params,
        (res) => {
          this.loading = false
          if (res.code === 200) {
            this.tableData = res.data.records || []
            this.pagination.total = res.data.total || 0
          } else {
            this.$message.error(res.msg || '查询失败')
          }
        },
        (error) => {
          this.loading = false
          this.$message.error('查询失败')
        }
      )
    },

    // 重置
    handleReset() {
      this.queryForm = {
        consumerName: '',
        signature: '',
        signatureAttr: ''
      }
      this.pagination.current = 1
      this.tableData = []
      this.pagination.total = 0
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.size = size
      this.pagination.current = 1
      this.handleQuery()
    },

    // 当前页改变
    handleCurrentChange(current) {
      this.pagination.current = current
      this.handleQuery()
    },

    // 行点击
    handleRowClick(row) {
      // 可以在这里添加行点击逻辑
    },

    // 查看详情
    handleViewDetail(row) {
      this.detailVisible = true
      this.detailLoading = true
      
      window.api.get(
        window.path.omcs + "consumerSignature/realName?userId=" + row.userId + "&signatureId=" + row.signatureId,
        {},
        (res) => {
          this.detailLoading = false
          if (res.code === 200) {
            this.detailData = res.data
          } else {
            this.$message.error(res.msg || '获取详情失败')
          }
        },
        (error) => {
          this.detailLoading = false
          this.$message.error('获取详情失败')
        }
      )
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false)
    },

    // 获取服务类型标签
    getServiceTypeTag(type) {
      const typeMap = {
        1: { text: '验证码', type: 'primary' },
        2: { text: '通知', type: 'success' },
        3: { text: '营销', type: 'warning' }
      }
      return typeMap[type] || { text: '未知', type: 'info' }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },

    // 获取图片列表
    getImageList(imgUrl) {
      if (!imgUrl) return []
      return imgUrl.split(',').filter(item => item.trim() !== '').map(item => {
        return item.startsWith('http') ? item : window.path.img + item
      })
    }
  }
}
</script>

<style scoped>
.signature-query-dialog {
  .query-form {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .table-container {
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }

  .realname-status {
    display: flex;
    justify-content: space-around;
    align-items: center;
    
    .operator-item {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .operator-icon {
        font-size: 14px;
        
        &.yd {
          color: #409eff;
        }
        
        &.lt {
          color: #f56c6c;
        }
        
        &.dx {
          color: #e6a23c;
        }
      }
    }
  }
}

.signature-detail-dialog {
  .detail-content {
    .realname-detail {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 15px;
        color: #303133;
        font-weight: 600;
      }
      
      .operator-card {
        height: 140px;
        
        .card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          
          i {
            font-size: 16px;
          }
        }
        
        &.yd-card .card-header i {
          color: #409eff;
        }
        
        &.lt-card .card-header i {
          color: #f56c6c;
        }
        
        &.dx-card .card-header i {
          color: #e6a23c;
        }
        
        .status-content {
          text-align: center;
          
          .available-types {
            margin-top: 10px;
            text-align: left;
            
            .label {
              font-size: 12px;
              color: #909399;
              margin-bottom: 5px;
              display: block;
            }
          }
        }
      }
    }
    
    .qualification-images {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 15px;
        color: #303133;
        font-weight: 600;
      }
      
      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .qualification-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          cursor: pointer;
          border: 2px solid #e4e7ed;
          transition: border-color 0.3s;
          
          &:hover {
            border-color: #409eff;
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .signature-query-dialog {
    .query-form {
      .el-form--inline .el-form-item {
        display: block;
        margin-bottom: 15px;
      }
    }
    
    .realname-status {
      flex-direction: column;
      gap: 8px;
    }
  }
}
</style>
