// 二次确认弹窗封装
import { ElMessage, ElMessageBox } from "element-plus";
function deleRows(type, prompt, url, params, callback,callback1,defaultType) {
  var _this = window.app.config.globalProperties
  _this
    .$confirm(prompt, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      type: 'warning',
      beforeClose: (action, instance, done) => {
        done()
      },
    })
    .then(() => {
      if (type == 'post') {
        window.api.post(url, params, (res) => {
          callback(res)
          if (res.code) {
            if (res.code == 200) {
              ElMessage({
                type: 'success',
                duration: 2000,
                message: '操作成功!',
              })
            } else {
              if(defaultType != 'default'){
                ElMessage({
                  type: 'error',
                  duration: 2000,
                  message: res.msg,
                })
              }
            }
          } else {
            ElMessage({
              type: 'success',
              duration: 2000,
              message: '操作成功!',
            })
          }
        })
      } else if (type == 'put') {
        window.api.put(url, params, (res) => {
          callback(res)
          if (res.code) {
            if (res.code == 200) {
              ElMessage({
                type: 'success',
                duration: 2000,
                message: '操作成功!',
              })
            } else {
              ElMessage({
                type: 'error',
                duration: 2000,
                message: res.msg,
              })
            }
          } else {
            ElMessage({
              type: 'success',
              duration: 2000,
              message: '操作成功!',
            })
          }
        })
      } else if (type == 'delete') {
        window.api.delete(url, params, (res) => {
          callback(res)
          if (res.code) {
            if (res.code == 200) {
              ElMessage({
                type: 'success',
                duration: 2000,
                message: '操作成功!',
              })
            } else {
              ElMessage({
                type: 'error',
                duration: 2000,
                message: res.msg,
              })
            }
          } else {
            ElMessage({
              type: 'success',
              duration: 2000,
              message: '操作成功!',
            })
          }
        })
      } else if (type == 'get') {
        window.api.get(url, params, (res) => {
          callback(res)
          if (res.code) {
            if (res.code == 200) {
              ElMessage({
                type: 'success',
                duration: 2000,
                message: '操作成功!',
              })
            } else {
              ElMessage({
                type: 'error',
                duration: 2000,
                message: res.msg,
              })
            }
          } else {
            ElMessage({
              type: 'success',
              duration: 2000,
              message: '操作成功!',
            })
          }
        })
      }
    })
    .catch((err) => {
      if (callback1) {
        callback1(err)
      }
      ElMessage({
        type: 'info',
        message: '已取消操作!',
      })
    })
}
export default {
  confirmation: function (type, prompt, url, params, callback,callback1) {
    return deleRows(type, prompt, url, params, callback,callback1)
  },
}
