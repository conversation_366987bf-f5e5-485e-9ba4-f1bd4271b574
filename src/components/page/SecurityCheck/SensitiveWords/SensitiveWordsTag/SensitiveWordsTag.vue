<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="sensiTag"
        class="demo-form-inline"
        ref="sensiTag"
      >
        <el-form-item label="创建时间" prop="time">
          <el-date-picker
            class="input-w"
            v-model="sensiTag.time"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="hande"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标签名称" prop="labelName">
          <el-input
            v-model="sensiTag.labelName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建人" prop="buildName">
          <el-input
            v-model="sensiTag.buildName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click="queryForm()">查询</el-button>
          <el-button type="primary" plain @click="resetForm('sensiTag')"
            >重置</el-button
          >
        </div>
      </el-form>

      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header" style="margin-right:10px" >敏感词标签库列表</span> -->
        <el-button type="primary" @click="handleAdd">新增敏感词标签</el-button>
        <!-- <el-button type="primary" @click="exportSensit()">导出</el-button> -->
        <!-- <el-button type="primary" @click="tagBatchDelet()" v-if="selectId.length>0">批量删除</el-button> -->
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem> -->
        <!--表格树内容栏-->
        <el-table
          :data="tableTreeDdata"
          border
          size="default"
          style="width: 100%"
          v-loading="loading"
        >
          <table-tree-column
            prop="labelName"
            treeKey="id"
            label="标签名称"
            @updateTreeData="updateTreeData"
          ></table-tree-column>
          <el-table-column prop="idx" label="标签权重"></el-table-column>
          <el-table-column prop="createName" label="创建人"></el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            width="170"
          ></el-table-column>
          <el-table-column
            fixed="right"
            header-align="center"
            align="center"
            width="140"
            label="操作"
          >
            <template v-slot="scope">
              <el-button style="color: #409eff;" @click="handleEdit(scope.row)" link
                ><el-icon><EditPen /></el-icon> 编辑
              </el-button>
              <el-button
                link
                @click="handleDelete(scope.row)"
                style="color: #f56c6c"
                ><el-icon><Delete /></el-icon>
                删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="sensiTagObj.currentPage" :page-size="sensiTagObj.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.totalRow">
                        </el-pagination>
                    </el-col> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增修改界面 -->
      <el-dialog
        :title="title"
        width="520px"
        v-model="dialogVisible"
        :close-on-click-modal="false"
      >
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataForm"
          @keyup.enter="submitForm()"
          label-width="140px"
          style="padding: 0 20px"
          class="addMenu"
        >
          <el-form-item label="敏感词标签" prop="selectedOptions">
            <el-cascader
              style="display: inline-block; width: 100%"
              :options="addMenu"
              v-model="dataForm.selectedOptions"
              :props="props"
              @change="handleChange"
              change-on-select
              :show-all-levels="false"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item label="标签名称" prop="labelName">
            <el-input
              v-model="dataForm.labelName"
              placeholder="请填写菜单名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="权重排序" prop="idx">
            <el-input-number
              v-model="dataForm.idx"
              :max="100"
            ></el-input-number>
            <!-- <el-input-number v-model="dataForm.idx"  :min="-999" :max="100"  placeholder="菜单排序"></el-input-number> -->
          </el-form-item>
          <!-- <el-form-item label="检测敏感词标签" prop="smsIsFail">
                            <el-radio-group v-model="dataForm.smsIsFail">
                                <el-radio label="2">进入审核</el-radio>
                                <el-radio label="1">直接失败</el-radio>
                            </el-radio-group>
                        </el-form-item> -->
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm()">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 弹窗（新增，编辑 标签） -->
      <!-- <el-dialog :title="tagLibrary.title" v-model="setsLabelDialog" width="520px" :close-on-click-modal = false> 
                    <el-form :model="tagLibrary.formData" :rules="tagLibrary.formRule" ref="tagLibrary" label-width="96px" style="padding:0 28px;">
                        <el-form-item label="敏感词标签" prop="labelName">
                            <el-input v-model="tagLibrary.formData.labelName"></el-input>
                        </el-form-item>
                        <el-form-item label="备注"  prop="remark">
                            <el-input type="textarea" :rows="5" resize='none' v-model="tagLibrary.formData.remark"></el-input>
                        </el-form-item>
                        <el-form-item style="margin-top: 40px;text-align: right;">
                            <el-button class="footer-center-button" @click="setsLabelDialog = false">取 消</el-button> 
                            <el-button class="footer-center-button" type="primary" @click="tag_ok_add('tagLibrary')">确 认</el-button>
                        </el-form-item>
                    </el-form>
                </el-dialog> -->
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import TableTreeColumn from '@/components/page/SystemSettings/TableTreeColumn.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
export default {
  components: {
    TableTreeColumn,
    TableTem
  },
  name: 'SensitiveWordsTag',
  data() {
    return {
      isFirstEnter: false,
      title: '新增敏感词',
      sensiTag: {
        //查询框的值
        labelName: '',
        time: [],
        buildName: '',
        beginTime: '',
        endTime: '',
      },
      sensiTagObj: {
        //储存查询框的值
        labelName: '',
        time: [],
        buildName: '',
        beginTime: '',
        endTime: '',
      },
      dialogVisible: false,
      dataForm: {
        remark: '',
        labelName: '',
        selectedOptions: [0],
        parentId: 0,
        idx: -999,
        // smsIsFail:"2"
      },
      dataRule: {
        labelName: [
          { required: true, message: '敏感词不能为空', trigger: 'blur' },
        ],
        selectedOptions: [
          { required: true, message: '父级敏感词不能为空', trigger: 'change' },
        ],
        idx: [{ required: true, message: '权重排序不能为空', trigger: 'blur' }],
        // smsIsFail: [
        //     { required: true, message: "请选择是否检测敏感词标签进审核", trigger: "blur" },
        // ],
      },
      props: {
        value: 'id',
        label: 'labelName',
        children: 'children',
        checkStrictly : true
      },
      addMenu: [
        {
          id: 0,
          labelName: '敏感词标签',
        },
      ],
      // setsLabelDialog:false,
      // selectId:'', //批量操作选中id
      // wordsCount:[],//批量操作选中行的包含敏感词个数
      // tagLibrary:{ //新增弹框数据
      //     title:'',
      //     labelName:'',
      //     formData:{
      //         labelName:"",
      //         remark:'',
      //         createName:'gaoyuan',
      //         wordsLabelId:''
      //     },
      //     formRule:{//验证规则
      //         labelName:[
      //             { required: true, message: '请输入敏感词标签', trigger: 'blur' },
      //             { min: 1, max: 15, message: '长度在 1 到 15 个字符', trigger: 'blur' }
      //         ],
      //         remark:[
      //             { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
      //         ]
      //     }
      // },
      tableTreeDdata: [],
      loading: false,
      // tableDataObj: { //列表数据
      //     tablecurrent:{ //分页参数
      //         totalRow:0,
      //     },
      //     loading2:false,
      //     tableData: [],
      //     tableLabel:[{
      //             prop:"labelName",
      //             showName:'标签名称',
      //             fixed:false
      //         },{
      //             prop:"wordsCount",
      //             showName:'敏感词个数',
      //             fixed:false,
      //             width:'100px'
      //         },{
      //             prop:"createName",
      //             showName:'创建人',
      //             fixed:false,
      //             width:'160px'
      //         }, {
      //             prop:"createTime",
      //             showName:'创建时间',
      //             fixed:false,
      //             width:'160px',

      //         },{
      //             prop:"remark",
      //             showName:'备注',
      //             fixed:false
      //         }
      //     ],
      //     tableStyle:{
      //         isSelection:true,//是否复选框
      //         // height:250,//是否固定表头
      //         isExpand:false,//是否是折叠的
      //         style: {//表格样式,表格宽度
      //             width:"100%"
      //         },
      //         optionWidth:'140',//操作栏宽度
      //         border:true,//是否边框
      //         stripe:false,//是否有条纹
      //     },
      //     conditionOption:[
      //             {
      //                 contactCondition:'wordsCount',//关联的表格属性
      //                 contactData:'0',//关联的表格属性-值
      //                 optionName:'删除',//按钮的显示文字
      //                 optionMethod:'delet',//按钮的方法
      //                 icon:'el-icon-error',//按钮图标
      //                 optionButtonColor:'#F56C6C',//按钮颜色
      //             }
      //     ],
      //     tableOptions:[
      //         {
      //             optionName:'编辑',
      //             type:'',
      //             size:'mini',
      //             optionMethod:'modify',
      //             icon:'el-icon-edit',
      //         }
      // ]}
    }
  },
  methods: {
    //-------------   列表信息   --------------
    // 获取列表数据
    findTreeDatas() {
      Object.assign(this.sensiTag, this.sensiTagObj)
      window.api.post(
        window.path.omcs + 'operatingblackwordslabel/page ',
        this.sensiTagObj,
        (res) => {
          this.tableTreeDdata = res.data
        }
      )
    },

    // 数据展开
    updateTreeData(data) {
      if (data) {
        this.tableTreeDdata = data
      }
    },
    // 表单提交
    submitForm() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dataForm.parentId == undefined) {
            this.dataForm.parentId = 0
          }
          if (this.title == '新增敏感词') {
            this.$confirms.confirmation(
              'put',
              '确认新增敏感词',
              window.path.omcs + 'operatingblackwordslabel',
              this.dataForm,
              (res) => {
                this.findTreeDatas()
                this.dialogVisible = false
              }
            )
          } else {
            this.$confirms.confirmation(
              'put',
              '确认编辑敏感词',
              window.path.omcs + '/operatingblackwordslabel/redact',
              this.dataForm,
              (res) => {
                this.findTreeDatas()
                this.dialogVisible = false
              }
            )
          }
        }
        // if (valid) {
        //     let dataForms = Object.assign({},this.dataForm);
        //     dataForms.type = this.dataForm1.type;//菜单还是按钮
        //     dataForms.sort = this.dataForm.selectedOptions.length; //菜单样式的级别
        //     if(this.title =='新增'){
        //         delete dataForms.menuId;
        //         delete dataForms.id;
        //         this.sendAjax('post','确定新增菜单',dataForms);
        //     }else{
        //         if(this.dataForm.type === '0'){
        //             delete dataForms.permission;
        //         }else{
        //             delete dataForms.component;
        //             delete dataForms.url;
        //         }
        //         if(this.menuName == this.dataForm.name){
        //             this.$confirms.confirmation('put','确定编辑菜单',window.path.upms+'menu',dataForms,res =>{
        //                 this.findTreeDatas();
        //                 this.findTreeData();
        //                 this.dialogVisible = false;//隐藏弹窗
        //             });
        //         }else{
        //             this.sendAjax('put','确定编辑菜单',dataForms);
        //         }

        //     }
        // }
      })
    },
    // 新增
    handleAdd() {
      let parent = []
      parent = JSON.parse(JSON.stringify(this.tableTreeDdata))
      for (let i = 0; i < parent.length; i++) {
        delete parent[i].children
      }
      this.addMenu[0].children = parent
      ;(this.title = '新增敏感词'), (this.dialogVisible = true)
    },
    // 编辑
    handleEdit(val) {
      this.title = '编辑敏感词'
      this.addMenu[0].children = this.tableTreeDdata
      this.dialogVisible = true
      this.dataForm.selectedOptions = this.getTreeDeepArr(val.id, this.addMenu)
      this.dataForm.parentId = val.parentId
      // this.dataForm.parentId = this.dataForm.parentId
      this.dataForm.id = val.id
      this.dataForm.labelName = val.labelName
      this.dataForm.idx = val.idx
      // if(val.smsIsFail){
      //     this.dataForm.smsIsFail = val.smsIsFail+'';
      // }else{
      //     this.dataForm.smsIsFail = '2'
      // }
    },
    // 删除
    handleDelete(val) {
      console.log(val)
      this.$confirms.confirmation(
        'delete',
        '确认删除敏感词',
        window.path.omcs + 'operatingblackwordslabel/' + val.id,
        {},
        (res) => {
          this.findTreeDatas()
          this.dialogVisible = false
        }
      )
    },
    getTreeDeepArr(key, treeData) {
      let arr = [] // 在递归时操作的数组
      let returnArr = [] // 存放结果的数组
      let depth = 0 // 定义全局层级
      // 定义递归函数
      function childrenEach(childrenData, depthN) {
        for (var j = 0; j < childrenData.length; j++) {
          depth = depthN // 将执行的层级赋值 到 全局层级
          arr[depthN] = childrenData[j].id
          if (childrenData[j].id == key) {
            returnArr = arr.slice(0, depthN + 1) //将目前匹配的数组，截断并保存到结果数组，
            break
          } else {
            if (childrenData[j].children) {
              depth++
              childrenEach(childrenData[j].children, depth)
            }
          }
        }
        return returnArr
      }
      return childrenEach(treeData, depth)
    },
    // 父级菜单选中的ID
    handleChange(value) {
      this.dataForm.parentId = value[1]
    },
    // //弹框父级菜单下拉框数据
    // findTreeData() {
    //     window.api.post(window.path.upms + 'menu/allTree',{
    //         platform:this.activeName2,
    //         flag:1
    //     },res=>{
    //         this.addMenu =[]; //置空
    //         let parent = {
    //             id: -1,
    //             name: "顶级菜单"
    //         };
    //         let datas = res;

    //         for(let i = 0; i<datas.length;i++){
    //             if(datas[i].children.length == 0){
    //                 delete datas[i].children
    //             }else{
    //                 for(let j = 0; j<datas[i].children.length;j++){
    //                     if(datas[i].children[j].children.length == 0){
    //                         delete datas[i].children[j].children
    //                     }else{
    //                         for(let k = 0; k<datas[i].children[j].children.length;k++){
    //                             if(datas[i].children[j].children[k].children.length == 0){
    //                                 delete datas[i].children[j].children[k].children
    //                             }else{
    //                                 for(let Q = 0; Q<datas[i].children[j].children[k].children.length;Q++){
    //                                     if(datas[i].children[j].children[k].children[Q].children.length == 0){
    //                                          delete datas[i].children[j].children[k].children[Q].children
    //                                     }else{
    //                                         for(let y = 0; y<datas[i].children[j].children[k].children[Q].children.length;y++){
    //                                             if(datas[i].children[j].children[k].children[Q].children[y].children.length == 0){
    //                                                 delete datas[i].children[j].children[k].children[Q].children[y].children
    //                                             }
    //                                         }
    //                                     }
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //         parent.children = datas;
    //         if(parent.children.length == 0){
    //             delete parent.children
    //         }
    //         this.addMenu =[parent];
    //     })
    // },
    //获取列表数据
    // getTableDtate(){
    //     this.tableDataObj.loading2 = true;
    //     window.api.post(window.path.omcs+'operatingblackwordslabel/page',this.sensiTagObj,res=>{
    //         this.tableDataObj.tableData = res.records;
    //         this.tableDataObj.tablecurrent.totalRow = res.total;
    //         this.tableDataObj.loading2 = false;
    //     })
    // },
    //改变每页数量触发事件
    // handleSizeChange(size) {
    //     this.sensiTagObj.pageSize = size;
    // },
    // //改变页数触发事件
    // handleCurrentChange: function(currentPage){
    //     this.sensiTagObj.currentPage = currentPage;
    // },
    //查询
    queryForm() {
      Object.assign(this.sensiTagObj, this.sensiTag)
      this.findTreeDatas()
      //    this.getTableDtate();
    },
    //重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.sensiTag.beginTime = ''
      this.sensiTag.endTime = ''
      Object.assign(this.sensiTagObj, this.sensiTag)
      this.findTreeDatas()
    },
    //获取查询时间的开始时间和结束时间
    hande: function (val) {
      if (val) {
        this.sensiTag.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensiTag.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensiTag.beginTime = ''
        this.sensiTag.endTime = ''
      }
    },
    //-------------   列表信息   --------------
    //列表复选框的值
    // handelSelection(val){
    //     let selectId = [];
    //     let wordsCount = [];
    //     for(let i=0;i<val.length;i++){
    //         selectId.push(val[i].wordsLabelId);
    //         wordsCount.push(val[i].wordsCount);
    //     }
    //     this.selectId = selectId.join(','); //批量操作选中id
    //     this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    // },
    // //操作按钮
    // handelOptionButton: function(val){
    //     this.tagLibrary.formData.wordsLabelId = val.row.wordsLabelId;  //获得所选行的id
    //     if(val.methods=='delet'){  //删除
    //         this.$confirms.confirmation('delete','此操作将永久删除该条数据, 是否继续?',window.path.omcs+'operatingblackwordslabel/'+val.row.wordsLabelId,{},res =>{
    //             // this.getTableDtate();
    //         })
    //     }else if (val.methods == 'modify'){ //编辑
    //         this.tagLibrary.title="编辑敏感词标签";
    //         this.setsLabelDialog = true;//弹框显示
    //         this.$nextTick(()=>{
    //             this.$refs.tagLibrary.resetFields();//清空表单
    //             Object.assign(this.tagLibrary.formData,val.row); //编辑赋值
    //         })
    //         this.tagLibrary.labelName = val.row.labelName;
    //     }
    // },
    // //新增
    // addTags(){
    //     this.tagLibrary.title = "设置敏感词标签";
    //     this.setsLabelDialog = true;
    // },
    //  //-------------   编辑和新增 标签功能开始   --------------
    // //弹框提交按钮
    // tag_ok_add(formName){
    //     //表单数据验证
    //     this.$refs[formName].validate((valid) => {
    //         if (valid) { //新增
    //             let tagLibraryDate =this.tagLibrary.formData;
    //             if(this.tagLibrary.title == "设置敏感词标签"){
    //                 tagLibraryDate.wordsLabelId='';
    //                 window.api.get(window.path.omcs+'operatingblackwordslabel/exists/'+this.tagLibrary.formData.labelName,{},res=>{
    //                     if(res.code == 200 && res.data == '0'){
    //                         this.$confirms.confirmation('post','确定新增?',window.path.omcs+'operatingblackwordslabel',tagLibraryDate,res =>{
    //                             // this.getTableDtate();
    //                             this.setsLabelDialog=false;//隐藏弹窗
    //                         })
    //                     }else{
    //                         this.$message({
    //                             message: '敏感词标签已存在，切勿重复添加！',
    //                             type: 'warning'
    //                         });
    //                     }

    //                 })
    //             }else{ //编辑
    //                 if( this.tagLibrary.formData.labelName ==  this.tagLibrary.labelName){
    //                     this.$confirms.confirmation('put','确定修改?',window.path.omcs+'operatingblackwordslabel',tagLibraryDate,res =>{
    //                         // this.getTableDtate();
    //                         this.setsLabelDialog=false;//隐藏弹窗
    //                     })
    //                 }else{
    //                     window.api.get(window.path.omcs+'operatingblackwordslabel/exists/'+this.tagLibrary.formData.labelName,{},res=>{
    //                         if(res.code == 200 && res.data == '0'){
    //                             this.$confirms.confirmation('put','确定修改?',window.path.omcs+'operatingblackwordslabel',this.tagLibrary.formData,res =>{
    //                                 // this.getTableDtate();
    //                                 this.setsLabelDialog=false;//隐藏弹窗
    //                             })
    //                         }else{
    //                             this.$message({
    //                                 message: '敏感词标签已存在，切勿重复！',
    //                                 type: 'warning'
    //                             });
    //                         }

    //                     })
    //                 }
    //             }
    //         } else {
    //             console.log('error submit!!');
    //             return false;
    //         }
    //     });
    // },
    //-------------  编辑和新增 标签功能结束  --------------
    //导出
    // exportSensit(){
    //     let aa = Object.assign({},this.sensiTagObj);
    //     aa.isDown = 1;
    //     if(this.tableDataObj.tableData.length == 0){
    //         this.$message({
    //             message: '列表无数据，不可导出！',
    //             type: 'warning'
    //         });
    //     }else{
    //         this.$File.export(window.path.omcs+'operatingblackwordslabel/page',aa,'敏感词标签报表.xlsx');
    //     }
    // },
    //批量删除功能
    // tagBatchDelet(){
    //     let flag = true;
    //     for(let i = 0;i<this.wordsCount.length;i++){
    //         if( this.wordsCount[i] != '0'){
    //             flag = false;
    //             break;
    //         }
    //     }
    //     if(flag){
    //         this.$confirms.confirmation('delete','此操作将永久删除该数据, 是否继续?',window.path.omcs+'operatingblackwordslabel/deleteBatchIds/'+this.selectId,{},res =>{
    //             // this.getTableDtate();
    //         })
    //     }else{
    //         this.$message({
    //             message: '选项中有不能删除项，需重新选择！',
    //             type: 'warning'
    //         });
    //     }
    // }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.findTreeDatas()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.findTreeDatas()
    })
  },
  watch: {
    //监听查询框对象的变化
    // sensiTagObj:{
    //     handler(){
    //         this.findTreeDatas();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听弹框是否关闭
    dialogVisible(val) {
      if (val == false) {
        this.dataForm.remark = ''
        this.dataForm.labelName = ''
        this.dataForm.selectedOptions = [0]
        this.dataForm.parentId = 0
        this.dataForm.idx = 1
      }
    },
  },
  emits: ['update:value'],
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
