<template>
  <div class="container_left">
    <div class="OuterFrame fillet" style="height: 100%">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          ref="formInline"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="标题" prop="ip">
            <el-input
              v-model="formInline.title"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="登录状态" prop="isSucced">
                            <el-select v-model="formInline.isSucced" placeholder="不限" class="input-w">
                                <el-option label="不限" value=""></el-option>
                                <el-option label="登录成功" value="1"></el-option>
                                <el-option label="登录失败" value="2"></el-option>
                            </el-select>
                        </el-form-item> -->
          <el-form-item label="操作时间" prop="time">
            <el-date-picker
              class="input-time"
              v-model="formInline.time"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              @change="getTimeOperating"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="ListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reset('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table" style="padding-bottom: 40px">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="OperationLog"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>{{ scope.row.createBy || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作手机号" title="操作手机号">
            <template v-slot="scope">
              <div>{{ scope.row.loginMobile || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="标题" title="标题">
            <template v-slot="scope">
              <div>{{ scope.row.title || '-' }}</div>
            </template>
          </vxe-column>

          <vxe-column field="IP" title="IP">
            <template v-slot="scope">
              <div>{{ scope.row.remoteAddr || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="地址" title="地址">
            <template v-slot="scope">
              <div>{{ scope.row.requestUri || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="请求方式" title="请求方式">
            <template v-slot="scope">
              <div>{{ scope.row.method || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作时间" title="操作时间" width="100">
            <template v-slot="scope">
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="参数" title="参数" width="300">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.params"
                :content="scope.row.params"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <!-- <vxe-column
                        field="登录状态" title="登录状态">
                        <template #default="scope">
                            <span v-if="scope.row.isSucced==1">成功</span>
                            <span v-else  style="color:red">失败</span>
                        </template>
                        </vxe-column> -->

          <!-- <vxe-column
                        field="详情描述"
                        title="详情描述"
                        width="300">
                        <template #default="scope" >{{ scope.row.logName }}:{{scope.row.remark}}{{scope.row.isSucced==1?'成功':'失败'}}</template>
                        </vxe-column> -->
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInlines.currentPage"
            :page-size="formInlines.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalSize"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
export default {
  components: {
    DatePlugin,
    TableTem,
    Tooltip
  },
  name: 'LoginLog',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      formInline: {
        userName: '',
        title: '',
        isSucced: '',
        begTime: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
        endTime: moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
        time: [
          moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
          moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formInlines: {
        userName: '',
        title: '',
        isSucced: '',
        begTime: moment().startOf('day').format('YYYY-MM-DD H00:00:00'),
        endTime: moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
        time: [
          moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
          moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formAdd: {
        name: '',
        state: '',
        menu: '',
        description: '',
      },
      datePluginValueList: {
        type: 'date',
        start: '',
        end: '',
        range: '-',
        defaultTime: '', //默认起始时刻
        datePluginValue: '',
      },
      totalSize: 0, //总条数
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(window.path.upms + 'log/logPage', this.formInlines, (res) => {
        this.tableDataObj.tableData = res.records
        this.totalSize = res.total
        this.tableDataObj.loading2 = false
      })
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.begTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.begTime = ''
        this.formInline.endTime = ''
      }
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.userName = ""
      this.formInline.title = ""
      this.formInline.begTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD 00:00:00')
      this.formInline.endTime = moment(Date.now()).format('YYYY-MM-DD 23:59:59')
      Object.assign(this.formInlines, this.formInline)
    },
    //分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
  },
  activated() {
    this.InquireList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tooltip {
  width: 300px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
