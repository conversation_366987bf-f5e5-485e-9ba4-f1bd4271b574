<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <!-- <div>
            <el-form
              :inline="true"
              :model="formInline"
              class="demo-form-inline"
              ref="formInline"
            >
              <el-form-item label="商品名称" label-width="80px" prop="signature">
                <el-input
                  v-model="formInline.goodName"
                  placeholder
                  class="input-w"
                ></el-input>
              </el-form-item>
              <el-form-item label="创建时间" label-width="80px" prop="time">
                <el-date-picker
                  class="input-w"
                  v-model="time1"
                  value-format="YYYY-MM-DD"
                  type="daterange"
                  range-separator="-"
                  @change="hande"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-form>
          </div> -->
      <!-- <div class="boderbottom">
            <el-button type="primary" plain style @click="Query">查询</el-button>
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div> -->
      <div class="Signature-search-fun" style="margin: 10px 0">
        <!-- <span class="Signature-list-header">商品列表</span> -->
        <el-button type="primary" @click="addopt">添加产品</el-button>
        <!-- <el-button
              type="primary"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delAll"
              >批量删除</el-button
            > -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->

        <!-- <table-tem
              :tableDataObj="tableDataObj"
              @handelOptionButton="handelOptionButton"
              @handelSelection="handelSelection"
              @save="save"
            ></table-tem> -->
        <!--分页-->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        >
          <el-table-column type="selection" width="46"></el-table-column>
          <el-table-column label="商品名称">
            <template v-slot="scope">
              <!-- <span >{{scope.row.productName}}</span> -->
              <span v-if="scope.row.productId == 1">短信</span>
              <span v-if="scope.row.productId == 2">彩信</span>
              <span v-if="scope.row.productId == 3">视频短信</span>
              <span v-if="scope.row.productId == 4">国际短信</span>
              <span v-if="scope.row.productId == 5">闪验</span>
              <span v-if="scope.row.productId == 6">语音验证码</span>
              <span v-if="scope.row.productId == 7">语音通知</span>
              <span v-if="scope.row.productId == 8">5G消息</span>
            </template>
          </el-table-column>
          <el-table-column label="条数">
            <template v-slot="scope">
              <span>{{ scope.row.amount }}条</span>
            </template>
          </el-table-column>
          <el-table-column label="单价">
            <template v-slot="scope">
              <span>{{ scope.row.unitPrice }}元</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">
              <span>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                @click="detailsRow(scope.$index, scope.row)"
                ><el-icon><EditPen /></el-icon>&nbsp;编辑</el-button
              >
              <el-button
                link
                style="margin-left: 0px; color: red"
                @click="delState(scope.$index, scope.row)"
                ><el-icon><Delete /></el-icon>&nbsp;删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增产品 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="产品" label-width="80px" prop="productId">
            <el-select v-model="formop.productId" placeholder="请选择">
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <!-- <el-input
                  v-model="formop.productId"
                  placeholder=""
                  autocomplete="off"
                ></el-input> -->
          </el-form-item>
          <el-form-item label="条数" label-width="80px" prop="amount">
            <el-input-number
              class="input-w"
              v-model="formop.amount"
              :min="0"
              :max="999999999"
              label="条数"
            ></el-input-number>
            <!-- <el-input v-model="formop.amount" placeholder="" autocomplete="off"></el-input> -->
          </el-form-item>
          <el-form-item label="单价" label-width="80px" prop="unitPrice">
            <el-input-number
              class="input-w"
              v-model="formop.unitPrice"
              :min="0"
              :max="999999999"
              label="单价"
            ></el-input-number>
            <!-- <el-input v-model="formop.unitPrice" placeholder="" autocomplete="off"></el-input> -->
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增标签商结束 -->
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
// import TableTem from "../../../../publicComponents/TableTem";
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
// import Bus from '@/assets/js/bus.js'
import moment from 'moment'
export default {
  components: {
    FileUpload,
    ChannelView
  },
  name: 'SignatureNumMag',
  data() {
    return {
      isFirstEnter: false,
      cateNames: '',
      ChannelData: '', //传递通道值
      titleMap: {
        add: '添加产品',
        edit: '编辑产品',
      },
      time1: '',
      signatureNum: '',
      status: 0,
      etxNum: '',
      flagPut: '', //用户名禁止状态 签名禁止状态
      dialogStatus: '', //新增编辑标题
      singName: [], //接受标签列表
      dialogFormVisible: false, //新增弹出框显示隐藏
      SignafileDialog: false,
      dialogBatchTest: false, //批量测试
      options: [
        // {
        //   value:'1',
        //   label:"短信"
        // },
        // {
        //   value:'2',
        //   label:"彩信"
        // },
        // {
        //   value:'3',
        //   label:"视频短信"
        // },
        // {
        //   value:'4',
        //   label:"国际短信"
        // },
        // {
        //   value:'5',
        //   label:"闪验"
        // },
        // {
        //   value:'6',
        //   label:"语音验证码"
        // },
        // {
        //   value:'7',
        //   label:"语音通知"
        // },
      ],
      //查询表单
      formInline: {
        beginTime: '',
        endtime: '',
        name: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        // beginTime: "",
        // endtime: "",
        // name: "",
        goodsId: '',
        currentPage: 1,
        pageSize: 10,
      },
      //批量测试
      formoTest: {
        channelId: '',
        mobile: '',
      },
      rulesTest: {
        productId: [
          { required: true, message: '请选择产品', trigger: 'change' },
        ],
        amount: [{ required: true, message: '请输入条数', trigger: 'change' }],
        unitPrice: [
          { required: true, message: '请输入价格', trigger: 'change' },
        ],
      },
      //添加列表
      formop: {
        amount: null,
        productId: '',
        unitPrice: null,
      },
      id: '', //列表行id
      selectId: '', //批量操作选中id
      tableRow: '', //当前行列表数据
      rules: {
        productId: [
          { required: true, message: '请选择产品', trigger: 'change' },
        ],
        amount: [{ required: true, message: '请输入条数', trigger: 'change' }],
        unitPrice: [
          { required: true, message: '请输入价格', trigger: 'change' },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
      Progressflag: false,
      failNum: '',
      failRow: '',
      fileStyle: {
        size: 245678943234,
        style: ['xlsx', 'xls'],
      },
      tip: '仅支持.xlsx .xls 等格式',
      del1: true, //关闭弹框时清空图片
    }
  },
  created() {
    const { goodsId, goodsname } = this.$route.query
    this.tabelAlllist.goodsId = goodsId
    this.cateNames = goodsname
    this.getList()
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
    // Bus.$on('cateName', target => {
    //       console.log(target,'sss');
    //       this.cateNames = target
    //       console.log(this.cateNames,'kkkk');
    //   });
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.shops + 'shopgoodsproduct/page',
        this.tabelAlllist,
        (res) => {
          // console.log(res);
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
          // this.cateName = res.data.records[0].cateName
        }
      )
    },
    goBack() {
      this.$router.go(-1)
    },
    getList() {
      window.api.get(window.path.recharge + 'products', {}, (res) => {
        // console.log(res);
        this.options = res.data
      })
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },

    hande: function (val) {
      if (val) {
        console.log(val, '11')
        //获取查询时间框的值
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endtime = this.moment(val[0]).format('YYYY-MM-DD ')
        // this.formInline.createEndTime = this.moment(val[1]).format("YYYY-MM-DD ")+"23:59:59";
      } else {
        this.formInline.beginTime = ''
        this.formInline.endtime = ''
        // this.formInline.createEndTime = '';
      }
    },
    // 监听子组件传递方法
    // save(val) {
    //   this.ChannelData = val;
    //   this.$refs.ChannelRef.ChannelClick();
    // },
    signaTure() {
      window.api.get(
        window.path.omcs + 'operatingsignature/sign/' + this.formop.signature,
        {},
        (res) => {
          this.signatureNum = res.data
        }
      )
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(val) {
      console.log(val)
      let selectId = []
      // let wordsCount = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
        // wordsCount.push(val[i].wordsCount);
      }
      this.selectId = selectId.join(',') //批量操作选中id
      // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },
    Reload(val) {
      console.log(val)
      //重置
      console.log(111)
      this.$refs.formInline.resetFields()
      this.formInline.beginTime = ''
      this.formInline.endtime = ''
      this.time1 = ''
      this.formInline.name = ''
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.formop.goodsId = this.tabelAlllist.goodsId
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.shops + 'shopgoodsproduct',
              this.formop,
              (res) => {
                if (res.code == 400) {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.shops + 'shopgoodsproduct',
              this.formop,
              (res) => {
                this.dialogFormVisible = false
                this.gettableLIst()
                // if (res.code == 400) {
                //   this.$message({
                //     message: res.msg,
                //     type: "warning",
                //   });
                // } else {
                //   this.$message({
                //     message: res.msg,
                //     type: "success",
                //   });
                // }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.flagPut = false
      this.dialogStatus = 'add'
    },
    delAll(val) {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingSignature',
        {
          ids: this.selectId,
        },
        () => {
          this.gettableLIst()
        }
      )
    },

    detailsRow(index, val) {
      console.log(val, 'll')
      //编辑
      this.dialogFormVisible = true
      this.flagPut = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.tableRow = val
      for (let i in val) {
        if (
          val[i] == null ||
          i == 'bestSell' ||
          i == 'status'
        ) {
          val[i] += ''
        }
      }
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确认执行此操作吗？',
        window.path.shops + 'shopgoodsproduct/' + val.id,
        {},
        (res) => {
          //   if (res.code == 400) {
          //     this.$message({
          //       message: res.msg,
          //       type: "warning",
          //     });
          //   } else {
          //     this.$message({
          //       message: res.msg,
          //       type: "success",
          //     });
          //   }
          this.gettableLIst()
        }
      )
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        const { goodsId, goodsname } = this.$route.query
        this.tabelAlllist.goodsId = goodsId
        this.cateNames = goodsname
        this.getList()
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep: true, //深度监听
    //   immediate: true,
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.formop.amount = null
        this.formop.productId = ''
        this.formop.unitPrice = null
        this.$refs.formop.resetFields()
      }
    },
    'formop.name'(val) {
      if (val == '') {
        this.formop.name = ''
      }
    },
    SignafileDialog(val) {
      this.del1 = true
      if (val == false) {
        this.del1 = false
        this.failNum = ''
        this.failRow = ''
      }
    },
    dialogBatchTest(val) {
      if (val == false) {
        this.$refs.formoTest.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  text-align: right;
  padding: 10px;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
