<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item
            label="原始通道编号"
            label-width="100px"
            prop="originalChannelId"
          >
            <el-input
              v-model="formInline.originalChannelId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="改写通道编号"
            label-width="100px"
            prop="channelId"
          >
            <el-input
              v-model="formInline.channelId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" label-width="82px" prop="status">
            <el-select
              v-model="formInline.status"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="启用" value="0"></el-option>
              <el-option label="禁用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份" label-width="82px" prop="provincial">
            <el-select
              v-model="formInline.provincial"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="(item, index) in ProvincesCities"
                :key="index"
                :label="item.provincial"
                :value="item.provincial"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="短信类型" prop="smsType">
                        <el-select class="input-w" v-model="formInline.smsType" placeholder="选择短信类型" clearable>
                            <el-option label="验证码" value="1"></el-option>
                            <el-option label="通知" value="2"></el-option>
                            <el-option label="营销推广" value="3"></el-option>
                        </el-select>
                        </el-form-item> -->
          <!-- <el-form-item>
                      
                        </el-form-item> -->
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload"
          >重置</el-button
        >
        <el-button
          type="primary"
          plain
          style
          @click="
            title = '新增改写通道';
            dialogFormVisible = true;
          "
          >添加通道</el-button
        >
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @save="save"></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="ChannelRouting"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="原始通道编号" title="原始通道编号" width="150px">
            <!-- 点击用户名 -->
            <template v-slot="scope">
              <div
                style="color: rgb(22, 165, 137); cursor: pointer"
                @click="save(scope.row.originalChannelId)"
                >{{ scope.row.originalChannelId }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="改写通道编号" title="改写通道编号" width="150px">
            <template v-slot="scope">
              <div
                style="color: rgb(22, 165, 137); cursor: pointer"
                @click="save(scope.row.channelId)"
                >{{ scope.row.channelId }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="短信类型" title="短信类型" width="120px">
            <template v-slot="scope">
              <div
                style="margin: 5px 0"
                v-for="(item, index) in scope.row.smsType"
                :key="index + 'a'"
              >
                <el-tag
:disable-transitions="true" v-if="item == '1'">验证码</el-tag>
                <el-tag
:disable-transitions="true" v-else-if="item == '2'">通知 </el-tag>
                <el-tag
:disable-transitions="true" v-else>营销推广</el-tag>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="省份" title="省份" width="110px">
            <template v-slot="scope">
              <div>{{ scope.row.provincial }}</div>
            </template>
          </vxe-column>
          <vxe-column field="量比(%)" title="量比(%)" width="110px">
            <template v-slot="scope">
              <div>{{ scope.row.percent }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态" width="120px">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="success" effect="dark">
                启用
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 1" type="danger" effect="dark">
                禁用
              </el-tag>
            </template>
          </vxe-column>
          <vxe-column field="生效开始时间" title="生效开始时间" width="120px">
            <template v-slot="scope">
              <p v-if="scope.row.beginTime">
                {{ $parseTime(scope.row.beginTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.beginTime">
                {{ $parseTime(scope.row.beginTime, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="生效结束时间" title="生效结束时间" width="120px">
            <template v-slot="scope">
              <p v-if="scope.row.endTime">
                {{ $parseTime(scope.row.endTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.endTime">
                {{ $parseTime(scope.row.endTime, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="120px">
            <template v-slot="scope">
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="更新时间" title="更新时间" width="120px">
            <template v-slot="scope">
              <p v-if="scope.row.updateTime">
                {{ $parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.updateTime">
                {{ $parseTime(scope.row.updateTime, "{h}:{i}:{s}") }}
              </p>
            </template>
          </vxe-column>

          <vxe-column field="创建人" title="创建人" width="120px">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="180px">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.remark"
                :content="scope.row.remark"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
              <!-- <span>{{ scope.row.remark }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180px" fixed="right">
            <template v-slot="scope">
              <el-button
                v-if="scope.row.status == 1"
                link
                style="color: #16a589"
                @click="handelOptionButton(scope.row, 'start')"
                >启用</el-button
              >
              <el-button
                v-if="scope.row.status == 0"
                link
                style="color: orange"
                @click="handelOptionButton(scope.row, 'start')"
                >停用</el-button
              >
              <el-button
                link
                style="color: #16a589"
                @click="handelOptionButton(scope.row, 'edit')"
                ><el-icon><EditPen /></el-icon>编辑</el-button
              >
              <!-- <el-button type="text" @click="ChannelData = scope.row; dialogFormVisible = true">查看</el-button> -->
              <el-button
                link
                style="color: #f56c6c"
                @click="handelOptionButton(scope.row, 'dle')"
                ><el-icon><CircleCloseFilled /></el-icon>删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogFormVisible"
      :close-on-click-modal="false"
      width="480px"
    >
      <el-form
        :model="formAddData"
        :rules="formRule"
        ref="formRules"
        label-width="120px"
        style="padding: 0 28px"
      >
        <el-form-item
          label="原始通道"
          label-width="110px"
          prop="originalChannelId"
        >
          <el-input
            v-model="formAddData.originalChannelId"
            :disabled="title == '编辑改写通道'"
            placeholder=""
            class="input-time"
          ></el-input>
        </el-form-item>
        <el-form-item label="改写通道编号" label-width="110px" prop="channelId">
          <el-input
            v-model="formAddData.channelId"
            placeholder=""
            class="input-time"
          ></el-input>
        </el-form-item>
        <el-form-item label="短信类型" prop="smsType">
          <el-select
            style="width: 250px"
            v-model="formAddData.smsType"
            multiple
            placeholder="请选择"
            @change="handelChange"
          >
            <el-option label="验证码" value="1"> </el-option>
            <el-option label="通知" value="2"> </el-option>
            <el-option label="营销推广" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="省份选择"
          prop="provincialId"
          label-width="110px"
          v-if="this.title != '编辑改写通道'"
        >
          <el-select
            v-model="formAddData.provincialId"
            placeholder="请选择"
            class="input-time"
          >
            <el-option
              v-for="(item, index) in ProvincesCities"
              :key="index"
              :label="item.provincial"
              :value="item.provincialId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="量比(%)" label-width="110px" prop="percent">
          <el-input-number
            class="input-time"
            v-model="formAddData.percent"
            :min="0"
            :max="999999999"
            label="量比"
          ></el-input-number>
          <!-- <el-input v-model="formAddData.percent" placeholder="" class="input-time"></el-input> -->
        </el-form-item>
        <el-form-item label="生效时间" label-width="110px" prop="time">
          <el-date-picker
            class="input-time"
            v-model="time"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="hande1"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" label-width="110px" prop="percent">
          <el-input
            type="textarea"
            v-model="formAddData.remark"
            placeholder=""
            class="input-time"
          ></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm('formRules')"
            >提 交</el-button
          >
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from "../../../../publicComponents/ChannelView.vue";
import TableTem from "../../../../publicComponents/TableTem.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
export default {
  components: {
    ChannelView,
    TableTem,
    Tooltip,
  },
  data() {
    var percents = (rule, value, callback) => {
      if (value == "" || value == null) {
        callback();
      } else {
        if (!/^([1-9]?\d|100)$/.test(value)) {
          return callback(new Error("请输入0-100正整数"));
        } else {
          callback();
        }
      }
    };
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      //传递通道值
      ChannelData: "",
      formInline: {
        originalChannelId: "",
        channelId: "",
        status: "",
        // smsType:"",
        provincial: "",
        currentPage: 1,
        pageSize: 10,
      },
      //查询赋值数据
      tabelAlllist: {
        originalChannelId: "",
        channelId: "",
        status: "",
        // smsType:"",
        provincial: "",
        currentPage: 1,
        pageSize: 10,
      },
      title: "新增改写通道",
      dialogFormVisible: false,
      //省份选择
      ProvincesCities: [],
      UserId: "",
      formAddData: {
        channelId: "",
        originalChannelId: "",
        percent: 0,
        provincialId: "",
        beginTime: "",
        endTime: "",
        remark: "",
        smsType: [],
      },
      time: [],
      text: "",
      // ProvincialList:[],
      formRule: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "change" },
        ],
        originalChannelId: [
          {
            required: true,
            message: "原始通道编号不能为空",
            trigger: "change",
          },
        ],
        channelId: [
          {
            required: true,
            message: "改写通道编号不能为空",
            trigger: "change",
          },
        ],
        percent: [{ required: false, validator: percents, trigger: "change" }],
        //省份
        provincialId: [
          { required: true, message: "请选择省份", trigger: "change" },
        ],
        smsType: [
          { required: true, message: "请选择短信行业类型", trigger: "blur" },
        ],
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
        tableLabel: [
          {
            prop: "originalChannelId",
            showName: "原始通道编号",
            Channel: true,
            fixed: false,
          },
          {
            prop: "channelId",
            showName: "改写通道编号",
            Channel: true,
            fixed: false,
          },
          {
            prop: "provincial",
            showName: "省份",
            fixed: false,
          },
          {
            prop: "percent",
            showName: "量比(%)",
            fixed: false,
          },
          {
            prop: "status",
            showName: "状态",
            fixed: false,
            formatData: function (val) {
              if (val == 0) {
                return (val = "启用");
              }
              if (val == 1) {
                return (val = "禁用");
              }
            },
            showCondition: {
              condition: "1",
            },
          },
          {
            prop: "beginTime",
            showName: "生效开始时间",
            width: 170,
            fixed: false,
          },
          {
            prop: "endTime",
            width: 170,
            showName: "生效结束时间",
            fixed: false,
          },
          {
            prop: "createTime",
            width: 170,
            showName: "创建时间",
            fixed: false,
          },
          {
            prop: "updateTime",
            width: 170,
            showName: "更新时间",
            fixed: false,
          },
          {
            prop: "createName",
            showName: "创建人",
            fixed: false,
          },
          {
            prop: "remark",
            showName: "备注",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "200", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: "status", //关联的表格属性
            contactData: "0", //关联的表格属性-值
            optionName: "停用", //按钮的显示文字
            optionMethod: "start", //按钮的方法
            icon: "el-icon-circle-close-outline", //按钮图标
            optionButtonColor: "orange", //按钮颜色
            otherOptionName: "启用", //其他条件的按钮显示文字
            otherOptionMethod: "start", //其他条件的按钮方法
            otherIcon: "CircleCheck", //其他条件按钮的图标
            optionOtherButtonColor: "#16A589", //其他条件按钮的颜色
          },
          {
            contactCondition: "", //关联的表格属性
            contactData: "", //关联的表格属性-值
            optionName: "", //按钮的显示文字
            optionMethod: "", //按钮的方法
            icon: "", //按钮图标
            optionButtonColor: "", //按钮颜色
            otherOptionName: "编辑", //其他条件的按钮显示文字
            otherOptionMethod: "edit", //其他条件的按钮方法
            otherIcon: "el-icon-edit", //其他条件按钮的图标
            optionOtherButtonColor: "#16A589", //其他条件按钮的颜色
          },
          {
            contactCondition: "", //关联的表格属性
            contactData: "", //关联的表格属性-值
            optionName: "", //按钮的显示文字
            optionMethod: "", //按钮的方法
            icon: "", //按钮图标
            optionButtonColor: "", //按钮颜色
            otherOptionName: "删除", //其他条件的按钮显示文字
            otherOptionMethod: "dle", //其他条件的按钮方法
            otherIcon: "el-icon-error", //其他条件按钮的图标
            optionOtherButtonColor: "#f56c6c", //其他条件按钮的颜色
          },
        ],
      },
    };
  },
  name: "ChannelRouting",
  methods: {
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingchannelroute/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tableData.forEach((item) => {
            if (item.smsType) {
              item.smsType = item.smsType.split(",");
            }
          });
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    // 监听子组件传递方法
    save(val) {
      console.log(val, "val");
      this.ChannelData = val;
      this.$refs.ChannelRef.ChannelClick();
    },
    // 查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    //重置
    Reload() {
      this.$refs.formInline.resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    //省份查询
    getProvincial() {
      window.api.get(
        window.path.omcs + "operatingchannelprovincial/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.ProvincesCities = res.data;
          }
        }
      );
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.formAddData.beginTime = val[0];
        this.formAddData.endTime = val[1];
      } else {
        this.formAddData.beginTime = "";
        this.formAddData.endTime = "";
      }
    },
    handelChange() {
      this.$forceUpdate();
    },
    handelOptionButton: function (val, methods) {
      this.UserId = val.id;
      if (methods == "edit") {
        //编辑
        this.title = "编辑改写通道";
        this.formAddData.channelId = val.channelId;
        this.formAddData.originalChannelId = val.originalChannelId;
        this.formAddData.percent = val.percent;
        this.formAddData.beginTime = val.beginTime;
        this.formAddData.endTime = val.endTime;
        this.formAddData.provincialId = val.provincialId;
        this.formAddData.remark = val.remark;
        if (val.beginTime && val.endTime) {
          this.time = [val.beginTime, val.endTime];
        }
        if (val.smsType) {
          this.formAddData.smsType = val.smsType;
        }
        this.dialogFormVisible = true;
      } else if (methods == "start") {
        this.$confirms.confirmation(
          "put",
          val.status == 0 ? "确认停用通道？" : "确认启用通道？",
          window.path.omcs + "operatingchannelroute/enable",
          { id: this.UserId, enable: val.status == 0 ? false : true },
          () => {
            this.gettableLIst();
          }
        );
      } else if (methods == "dle") {
        window.api.get(
          window.path.omcs + "operatingchannelroute/check/" + val.id,
          {},
          (res) => {
            if (res.code == 200) {
              this.text = `通道号：${res.data.channelId}${
                res.data.chanelStatus == 1 ? "已启用," : "已停用,"
              }${
                res.data.chanelGroup && res.data.chanelGroup.length > 0
                  ? " 且该通道被通道组：" +
                    res.data.chanelGroup.join(",") +
                    " 使用，"
                  : " 且该通道未被使用，"
              }${
                res.data.clientCount > 0
                  ? "且存在" + res.data.clientCount + "条个性化通道配置。"
                  : `且该通道未配置个性化通道。`
              }`;
              this.$confirms.confirmation(
                "delete",
                this.text + "确认删除通道？",
                window.path.omcs + "operatingchannelroute/" + this.UserId,
                {},
                () => {
                  this.gettableLIst();
                  this.text = "";
                }
              );
            }
          }
        );
      }
    },
    // 添加通道配置
    submitForm(formRules) {
      this.$refs.formRules.validate((valid) => {
        if (valid) {
          if (this.title == "新增改写通道") {
            let data = Object.assign({}, this.formAddData);
            (data.smsType = this.formAddData.smsType.join(",")), //短信行业类型
              this.$confirms.confirmation(
                "post",
                "确认新增通道？",
                window.path.omcs + "operatingchannelroute",
                data,
                () => {
                  this.dialogFormVisible = false;
                  this.gettableLIst();
                }
              );
          } else {
            this.formAddData.id = this.UserId;
            let data = Object.assign({}, this.formAddData);
            (data.smsType = this.formAddData.smsType.join(",")), //短信行业类型
              console.log(data, "tdata");
            this.$confirms.confirmation(
              "put",
              "确认编辑通道？",
              window.path.omcs + "operatingchannelroute",
              data,
              () => {
                this.dialogFormVisible = false;
                this.gettableLIst();
              }
            );
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  mounted() {
    // 获取省市
    // window.api.get(window.path.omcs + "operatingchannelprovincial/list",{},res => {
    //     this.ProvincesCities=res.data
    // })
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.gettableLIst();
      this.getProvincial();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst();
        this.getProvincial();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
    //  this.gettableLIst();
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //     handler() {
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate: true
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.formAddData = {
          originalChannelId: "",
          channelId: "",
          percent: 0,
          beginTime: "",
          endTime: "",
        };
        this.time = [];
        this.$refs.formRules.resetFields();
      }
    },
  },
};
</script>

<style>
.yunyingshang .el-form-item__content {
  margin-left: 10px !important;
}
</style>

<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 20px 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>