<template>
    <div class="container_left">
        <div class="OuterFrame fillet">
            <div>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInlineRef"
                    label-width="80px">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model.trim="formInline.username" placeholder class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号" prop="mobile">
                        <el-input v-model.trim="formInline.mobile" placeholder class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select class="input-w" clearable v-model="formInline.status" placeholder="请选择">
                            <!-- <el-option label="全部" value=""></el-option> -->
                            <el-option label="未处理" value="0"></el-option>
                            <el-option label="待回复" value="1"></el-option>
                            <el-option label="已回复" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain @click="Query">查询</el-button>
                        <el-button type="primary" plain @click="Reload(formInlineRef)">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div>
                <vxe-toolbar ref="toolbarRef" custom>
                    <template #buttons>
                        <!-- <el-button type="primary" @click="addsimilarMobile"
                >添加设置</el-button
              > -->
                    </template>
                </vxe-toolbar>
                <vxe-table ref="tableRef" id="blackWordTable" border v-loading="tableDataObj.loading2"
                    element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px"
                    :virtual-y-config="{ enabled: false }" :custom-config="{ storage: true }"
                    :column-config="{ resizable: true }" :row-config="{ isHover: true }" :data="tableDataObj.tableData"
                    @checkbox-all="handleSelectionChange" @checkbox-change="handleSelectionChange">
                    <vxe-column field="用户名" title="用户名">
                        <template v-slot="scope">
                            <div>
                                {{ scope.row.username }}
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="消息ID" title="消息ID" show-overflow>
                        <template v-slot="scope">
                            <span>{{ scope.row.msgId }}</span>
                        </template>
                    </vxe-column>
                    <vxe-column field="手机号" title="手机号">
                        <template v-slot="scope">
                            <div>
                                {{ scope.row.mobile }}
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="回复内容" title="回复内容" show-overflow>
                        <template v-slot="scope">
                            <span>
                                {{ scope.row.content }}
                            </span>
                        </template>
                    </vxe-column>
                    <vxe-column field="通道号" title="通道号" width="90">
                        <template v-slot="scope">
                            <div>
                                {{ scope.row.channelId }}
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="扩展" title="扩展" width="120">
                        <template v-slot="scope">
                            <div>
                                {{ scope.row.ext }}
                            </div>
                        </template>
                    </vxe-column>
                    <!-- <vxe-column field="错误码" title="错误码" width="100">
                        <template v-slot="scope">
                            <div>
                                {{ scope.row.code }}
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="回复状态" title="回复状态" width="100">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.replyStatus == 0" effect="dark" type="info">
                                未回复
                            </el-tag>
                            <el-tag v-if="scope.row.replyStatus == 1" effect="dark" type="success">
                                已回复
                            </el-tag>
                        </template>
                    </vxe-column> -->
                    <vxe-column field="状态" title="状态" width="100">
                        <template v-slot="scope">
                            <el-tag v-if="scope.row.status == 0" effect="dark" type="info">
                                未处理
                            </el-tag>
                            <el-tag v-if="scope.row.status == 1" effect="dark" type="warning">
                                待回复
                            </el-tag>
                            <el-tag v-if="scope.row.status == 2" effect="dark" type="success">
                                已回复
                            </el-tag>
                        </template>
                    </vxe-column>
                    <vxe-column field="回复时间" title="回复时间" width="180">
                        <template v-slot="scope">
                            <div v-if="scope.row.replyTime">
                                {{ moment(scope.row.replyTime).format("YYYY-MM-DD HH:mm:ss") }}
                            </div>
                        </template>
                    </vxe-column>
                    <vxe-column field="创建时间" title="创建时间" width="180">
                        <template v-slot="scope">
                            <div v-if="scope.row.createTime">
                                {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
                            </div>
                        </template>
                    </vxe-column>
                    <!-- <vxe-column field="操作" title="操作" width="180" fixed="right">
              <template v-slot="scope">
                <el-button
                  link
                  style="color: #409eff"
                  @click="editBlackTemplate(scope.row)"
                  >编 辑</el-button
                >
                <el-button
                  link
                  style="color: red"
                  @click="deleteSimilarMobile(scope.row)"
                  >删 除</el-button
                >
              </template>
            </vxe-column> -->
                </vxe-table>
                <div class="paginationBox">
                    <el-pagination class="page_bottom" @size-change="handleSizeChange"
                        @current-change="handleCurrentChange" :current-page="formInline.currentPage"
                        :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"
                        layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import { ref } from "vue";
import { reactive } from "vue";
const formInlineRef = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const formInline = reactive({
    username: "",
    mobile: "",
    status: "",
    currentPage: 1,
    pageSize: 10,
});
const tableDataObj = reactive({
    loading2: false,
    tableData: [],
    total: 0,
});
const handleSelectionChange = () => { };
const handleSizeChange = (size) => {
    formInline.pageSize = size;
    gettableLIst();
};
const handleCurrentChange = (currentPage) => {
    formInline.currentPage = currentPage;
    gettableLIst();
};
const gettableLIst = () => {
    //获取列表
    tableDataObj.loading2 = true;
    window.api.post(
        window.path.omcs + "operatingsmsmessagereply/page",
        formInline,
        (res) => {
            tableDataObj.loading2 = false;
            tableDataObj.total = res.data.total;
            tableDataObj.tableData = res.data.records;
        }
    );
};
const Query = () => {
    gettableLIst();
};
const Reload = () => {
    formInlineRef.value.resetFields();
    gettableLIst();
};
onMounted(() => {
    const $table = tableRef.value;
    const $toolbar = toolbarRef.value;
    if ($table && $toolbar) {
        $table.connect($toolbar);
    }
    gettableLIst();
});
</script>

<style lang="less" scoped>
.OuterFrame {
    padding: 20px;
}

.Templat-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
}

.Templat-matter>p {
    padding: 5px 0;
}
</style>