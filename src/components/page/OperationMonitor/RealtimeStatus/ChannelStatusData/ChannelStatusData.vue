<template>
  <div>
    <div class="Top_title">通道状态数据</div>
    <div class="fillet Statistics-box">
      <div style="display: flex">
        <!-- 通道数据大盘 -->
        <div
          style="
            width: calc(100% - 400px);
            padding-right: 20px;
            border-right: 1px solid #f0f0f0;
          "
        >
          <span>通道数据大盘</span>
          <div style="margin-top: 20px">
            <div
              v-for="(item, index) in StatisticsNum"
              :key="index"
              class="channel-box"
            >
              <div class="statistics-number-box">
                <div
                  :class="item.staClass"
                  style="
                    width: 60px;
                    height: 60px;
                    border-radius: 30px;
                    text-align: center;
                  "
                >
                  <img :src="item.stasrc" alt="" />
                </div>
                <div class="statistics-numbers-box">
                  <div style="padding-bottom: 5px; font-size: 14px">
                    {{ item.statitle }}
                  </div>
                  <div
                    style="
                      padding-bottom: 5px;
                      font-size: 24px;
                      color: #333;
                      padding-left: 3px;
                    "
                  >
                    {{ item.staNum }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--    折线图    -->
          <chart
            id="test"
            :option="option"
            height="300px"
            class="statistical-chart"
          ></chart>
        </div>
        <!-- 今日通道使用情况 -->
        <div style="width: 380px; padding-left: 30px; box-sizing: border-box">
          <span>今日通道使用情况</span>
          <!--   环图    -->
          <PieChart
            id="pie2"
            width="360px"
            height="336px"
            :basicOption="basicOption1"
            style="border-bottom: 1px solid #f0f0f0"
          ></PieChart>
          <!--   环图    -->
          <span style="line-height: 48px">通道使用分类</span>
          <div style="font-size: 0px">
            <div class="channel-type-box channel-type-box1">
              <p>
                <span style="font-size: 20px">{{ passagewayType.ydNum }}</span
                >条
              </p>
              <p>移动</p>
            </div>
            <div class="channel-type-box channel-type-box2">
              <p>
                <span style="font-size: 20px">{{ passagewayType.ltNum }}</span
                >条
              </p>
              <p>联通</p>
            </div>
            <div class="channel-type-box channel-type-box3">
              <p>
                <span style="font-size: 20px">{{ passagewayType.dxNum }}</span
                >条
              </p>
              <p>电信</p>
            </div>
            <div class="channel-type-box channel-type-box4">
              <p>
                <span style="font-size: 20px">{{
                  passagewayType.otherNum
                }}</span
                >条
              </p>
              <p>其他</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="fillet Statistics-box" style="margin-top: 10px">
      <span>通道队列列表（今日）</span>
      <el-tabs
        v-model="activeName2"
        type="card"
        @tab-click="handleClick"
        style="margin-top: 20px"
      >
        <el-tab-pane label="发送成功" name="first"></el-tab-pane>
        <!-- <el-tab-pane label="队列发送" name="second"></el-tab-pane> -->
        <div>
          <el-form
            :inline="true"
            :model="formBlackList"
            class="demo-form-inline"
            label-width="82px"
            ref="formBlackList"
          >
            <el-form-item label="通道号" label-width="82px" prop="channelCode">
              <el-input
                v-model="formBlackList.channelCode"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="运营商" label-width="82px" prop='operatorsType'>
                                <el-select v-model="formBlackList.operatorsType" class="input-w">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="移动" value="1"></el-option>
                                    <el-option label="联通" value="2"></el-option>
                                    <el-option label="电信" value="3"></el-option>
                                    <el-option label="三网" value="4"></el-option>
                                </el-select>
                            </el-form-item> -->
            <el-form-item style="padding-left: 10px">
              <el-button type="primary" plain @click="queryForm()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                @click="resetForm('formBlackList')"
                >重置</el-button
              >
              <el-button type="primary" plain @click="exportData()"
                >导出</el-button
              >
            </el-form-item>
          </el-form>
          <div class="sensitive-table">
            <!-- 表格和分页开始 -->
            <table-tem :tableDataObj="tableDataObj"></table-tem>
            <!--分页-->
            <template v-slot:pagination>
              <el-col
                :xs="24"
                :sm="24"
                :md="24"
                :lg="24"
                class="page"
                style="background: #fff; padding: 10px 0; text-align: right"
              >
                <el-pagination
                  class="page_bottom"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="formBlackList1.currentPage"
                  :page-size="formBlackList1.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="tableDataObj.total"
                >
                </el-pagination>
              </el-col>
            </template>
            <!-- 表格和分页结束 -->
          </div>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import Chart from '@/components/publicComponents/Chart.vue'
import PieChart from '@/components/publicComponents/PieChart' //饼图
import TableTem from '@/components/publicComponents/TableTem' //列表
export default {
  name: 'Statistics',
  components: { Chart, PieChart, TableTem },
  data() {
    return {
      activeName2: 'first',
      formBlackList: {
        channelCode: '',
        // operatorsType:'',
        currentPage: 1,
        pageSize: 10,
      },
      formBlackList1: {
        channelCode: '',
        // operatorsType:'',
        currentPage: 1,
        pageSize: 10,
      },
      StatisticsNum: [
        {
          stasrc: require('../../../../../assets/images/u1178.png'),
          statitle: '今日流入',
          staNum: '',
          staClass: 'bag-color1',
        },
        {
          stasrc: require('../../../../../assets/images/u1198.png'),
          statitle: '活跃通道(使用率超过80%)',
          staNum: '',
          staClass: 'bag-color2',
        },
        {
          stasrc: require('../../../../../assets/images/u99681.png'),
          statitle: '空闲通道(使用率不足20%)',
          staNum: '',
          staClass: 'bag-color3',
        },
        {
          stasrc: require('../../../../../assets/images/u99701.png'),
          statitle: '异常通道',
          staNum: '',
          staClass: 'bag-color4',
        },
      ],
      //折线图数据
      option: {
        color: ['#FF9900', '#49A9EE'],
        grid: {
          left: '1%',
          right: '5%',
          bottom: '1%',
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#EBEBEB', //坐标轴线的颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: '#666', //坐标值得具体的颜色
            },
          },
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#EBEBEB', //坐标轴线的颜色
            },
          },
          axisLabel: {
            textStyle: {
              color: '#666', //坐标值得具体的颜色
            },
          },
        },
        series: [
          {
            name: '发送量',
            type: 'line',
            data: [],
            lineStyle: {
              normal: {
                width: 3,
                shadowColor: 'rgba(0,0,0,0.4)',
                shadowBlur: 6,
                shadowOffsetY: 10,
              },
            },
            markPoint: {
              //最大值 最小值
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' },
              ],
            },
            markLine: {
              //平均值
              data: [{ type: 'average', name: '平均值' }],
            },
            smooth: true,
          },
          {
            name: '成功数',
            type: 'line',
            data: [],
            lineStyle: {
              normal: {
                width: 3,
                shadowColor: 'rgba(0,0,0,0.4)',
                shadowBlur: 6,
                shadowOffsetY: 10,
              },
            },
            markPoint: {
              //最大值 最小值
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' },
              ],
            },
            markLine: {
              //平均值
              data: [{ type: 'average', name: '平均值' }],
            },
            smooth: true,
          },
        ],
      },
      basicOption1: {
        //饼图1
        labelLine: {
          normal: {
            show: true,
          },
        },
        label: {
          normal: {
            show: true,
          },
        },
        data: [
          { value: '', name: '移动' },
          { value: '', name: '联通' },
          { value: '', name: '电信' },
          { value: '', name: '其他' },
        ],
        bgColor: ['#8996E6', '#49A9EE', '#98D87D', '#FFD86E'],
        radius: ['40%', '50%'],
        title: {
          textStyle: {
            color: '#333',
            //   fontWeight:'normal',
            fontSize: '14',
          },
          left: 20, //标题离左边的距离
        },
      },

      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0, //分页总量
        tableData: [],
        tableLabel: [
          { prop: 'channelCode', showName: '通道号', fixed: false },
          // {prop:"channelName",showName:'通道名称',fixed:false},
          { prop: 'channelUsedRate', showName: '通道使用占比', fixed: false },
          // {prop:"operatorsTypeName",showName:'运营商',fixed:false},
          { prop: 'date', showName: '日期', width: '150', fixed: false },
          { prop: 'submitNum', showName: '提交计费数', fixed: false },
          { prop: 'successNum', showName: '成功计费数', fixed: false },
          { prop: 'failNum', showName: '失败计费数', fixed: false },
          { prop: 'waitNum', showName: '待返回数', fixed: false },
          { prop: 'successRate', showName: '成功率', fixed: false },
          { prop: 'failRate', showName: '失败率', fixed: false },
          { prop: 'waitRate', showName: '待返回率', fixed: false },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '120', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      //今日通道使用情况
      passagewayType: {
        //通道类型数据
        ydNum: '',
        ltNum: '',
        dxNum: '',
        otherNum: '',
      },
    }
  },
  methods: {
    //导出
    exportData() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'channelStatus/download',
          this.formBlackList1,
          '发送成功报表.xlsx'
        )
      }
    },
    //获取今日通道使用情况数据
    getTodayPassage() {
      window.api.get(window.path.omcs + 'channelStatus/channelCount', {}, (res) => {
        //今日通道使用情况图表数据
        this.basicOption1.data[0].value = res.data.ydNum
        this.basicOption1.data[1].value = res.data.ltNum
        this.basicOption1.data[2].value = res.data.dxNum
        this.basicOption1.data[3].value = res.data.otherNum
        //通道使用分类数据
        this.passagewayType = res.data
        //获取通道数据大盘 4条 数据
        this.StatisticsNum[0].staNum = res.data.count
        this.StatisticsNum[1].staNum = res.data.active
        this.StatisticsNum[2].staNum = res.data.free
        this.StatisticsNum[3].staNum = res.data.abnormal
      })
    },
    //通道状态数据-今日发送成功列表
    getSendSuccess() {
      this.tableDataObj.loading2 = true
      Object.assign(this.formBlackList, this.formBlackList1)
      window.api.post(
        window.path.omcs + 'channelStatus/sentDetail',
        this.formBlackList1,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.total = res.total
        }
      )
    },
    //获取通道数据大盘统计图数据
    getPassagewayDate() {
      window.api.get(window.path.omcs + 'channelStatus/timeCount', {}, (res) => {
        this.option.xAxis.data = res.data[0]
        this.option.series[0].data = res.data[1]
        // this.option.series[1].data = res.data[1];
        // this.option.series[2].data = res.data[1];
      })
    },
    //获取通道数据大盘 4条 数据
    // getPagwDate(){
    //     window.api.get(window.path.omcs + 'channelStatus/channelCount',{},res=>{
    //        this.StatisticsNum[0].staNum = res.data.count;
    //        this.StatisticsNum[1].staNum = res.data.active;
    //        this.StatisticsNum[2].staNum = res.data.free;
    //        this.StatisticsNum[3].staNum = res.data.abnormal;
    //     })
    // },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    queryForm() {
      //查询
      Object.assign(this.formBlackList1, this.formBlackList)
      this.getSendSuccess()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      Object.assign(this.formBlackList1, this.formBlackList)
    },
    handleSizeChange(size) {
      console.log(size)
      this.formBlackList1.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formBlackList1.currentPage = currentPage
    },
  },
  mounted() {
    this.getTodayPassage()
    // this.getPagwDate();
    this.getSendSuccess()
    this.getPassagewayDate()
  },
  watch: {
    //监听查询框的变化
    formBlackList1: {
      handler(val) {
        this.getSendSuccess()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.statistics-number-box {
  border: 1px solid #eee;
  border-radius: 3px;
  display: flex;
  padding: 18px;
  background: #f8f8f8;
}
.statistics-numbers-box {
  width: calc(100% - 80px);
  margin-left: 20px;
}
.bag-color1 {
  background: #49a9ee;
}
.bag-color2 {
  background: #98d87d;
}
.bag-color3 {
  background: #f3857c;
}
.bag-color4 {
  background: #ffd86e;
}
.bag-color1 > img {
  padding-top: 15px;
}
.bag-color2 > img {
  padding-top: 15px;
}
.bag-color3 > img {
  padding-top: 15px;
}
.bag-color4 > img {
  padding-top: 17px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.channel-box {
  display: inline-block;
  width: calc(25% - 10px);
  margin-right: 10px;
  font-size: 0px;
  overflow: hidden;
}
.channel-type-box {
  display: inline-block;
  width: calc(50% - 5px);
  font-size: 14px;
  text-align: center;
  color: #fff;
}
.channel-type-box1 {
  background: #8996e6;
  padding: 5px 0;
  margin-bottom: 5px;
  margin-right: 5px;
}
.channel-type-box2 {
  background: #49a9ee;
  padding: 5px 0;
  margin-bottom: 5px;
}
.channel-type-box3 {
  background: #98d87d;
  padding: 5px 0;
  margin-right: 5px;
}
.channel-type-box4 {
  background: #ffd86e;
  padding: 5px 0;
}
@media screen and (max-width: 1895px) {
  .channel-box {
    width: calc(50% - 20px);
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .statistics-number-box {
    padding: 18px 36px;
  }
  .statistics-numbers-box {
    margin-left: 30px;
  }
  .channel-type-box {
    width: calc(50% - 5px);
    padding: 10px 0;
    margin: 10px 0;
  }
  .channel-type-box1 {
    border: none;
  }
  .channel-type-box2 {
    margin-left: 10px;
    border: none;
  }
  .channel-type-box3 {
    border: none;
  }
  .channel-type-box4 {
    margin-left: 10px;
    border: none;
  }
}
@media screen and (max-width: 1399px) {
  .channel-box {
    width: calc(100% - 30px);
    margin-right: 20px;
    margin-bottom: 10px;
  }
  .statistics-number-box {
    padding: 14px 36px;
  }
  .channel-type-box {
    width: calc(100% - 5px);
    padding: 12px 0;
    margin: 10px 0;
    color: #fff;
  }
}
</style>
