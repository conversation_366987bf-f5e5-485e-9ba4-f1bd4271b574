# 路由模块化迁移指南

## 概述

本指南将帮助您从原有的单一路由文件 `router.js` 迁移到新的模块化路由结构。

## 迁移步骤

### 1. 更新路由引入

将应用入口文件中的路由引入从：

```javascript
// 旧方式
import router from './router/router.js'
```

改为：

```javascript
// 新方式  
import router from './router/index.js'
// 或者
import router from './router'
```

### 2. 更新项目配置

如果项目中有任何直接引用 `router.js` 的地方，需要更新为 `index.js`。

### 3. 验证功能

迁移后请验证所有路由功能是否正常工作：

- [ ] 页面导航正常
- [ ] 路由守卫功能正常
- [ ] 动态路由加载正常
- [ ] 嵌套路由正常

## 新增路由的方法

### 添加到现有模块

1. 找到对应的业务模块文件（如 `modules/user.js`）
2. 在对应数组中添加新的路由配置：

```javascript
// 在 modules/user.js 中添加
{
  path: '/newUserFeature',
  meta: { title: '新用户功能', keepAlive: true, isBack: false },
  component: () => import('@/components/page/UserMag/NewFeature.vue'),
}
```

### 创建新模块

1. 在 `modules/` 目录下创建新文件：

```javascript
// modules/newModule.js
export default [
  {
    path: '/newModuleHome',
    meta: { title: '新模块首页', keepAlive: true, isBack: false },
    component: () => import('@/components/page/NewModule/Home.vue'),
  },
]
```

2. 在 `index.js` 中导入并添加：

```javascript
// 在 index.js 中添加
import newModuleRoutes from './modules/newModule'

const childRoutes = [
  // ... 其他模块
  ...newModuleRoutes,
]
```

## 模块拆分对照表

| 原路由位置 | 新模块位置 | 说明 |
|-----------|-----------|------|
| `/UserMag` | `modules/user.js` | 用户管理相关 |
| `/FailurecodeMag` | `modules/operation.js` | 运营管理相关 |
| `/AuditCertification` | `modules/audit.js` | 审核管理相关 |
| `/PassagewayMag` | `modules/channel.js` | 通道管理相关 |
| `/SensitiveWordMag` | `modules/security.js` | 安全检查相关 |
| `/MenuManagement` | `modules/system.js` | 系统设置相关 |
| `/userSendNotes` | `modules/statistics.js` | 统计分析相关 |
| `/5gAuditCertification` | `modules/fiveG.js` | 5G短信相关 |
| `/yuexinAudit` | `modules/yuexin.js` | 阅信相关 |
| `/MenuManage` | `modules/shop.js` | 商城相关 |
| `/MMSAudit` | `modules/message.js` | 消息业务相关 |

## 常见问题

### Q: 迁移后路由不工作怎么办？

A: 检查以下几点：
1. 确认已更新路由引入路径
2. 检查浏览器控制台是否有模块导入错误
3. 验证所有模块文件是否正确导出

### Q: 如何找到某个页面对应的路由模块？

A: 可以通过以下方式：
1. 查看页面路径的业务归属
2. 参考上面的模块拆分对照表
3. 在 `modules/` 目录下搜索路由路径

### Q: 原有的 router.js 文件可以删除吗？

A: 建议暂时保留作为备份，确认新结构完全正常后再删除。

## 性能优化建议

1. **路由懒加载**: 所有路由组件都已配置为懒加载，减少初始包大小
2. **模块按需加载**: 只有访问对应模块的路由时才会加载相关代码
3. **缓存策略**: 保持原有的 `keepAlive` 配置，确保页面缓存正常

## 团队协作建议

1. **分工明确**: 不同开发者可以专注于不同的路由模块
2. **减少冲突**: 模块化后减少了多人同时修改路由文件的冲突
3. **代码审查**: 更容易对特定业务模块的路由变更进行审查

## 后续维护

1. **定期整理**: 定期检查路由模块的合理性，必要时进行调整
2. **文档更新**: 新增路由时及时更新相关文档
3. **性能监控**: 关注路由加载性能，优化用户体验

## 回滚方案

如果迁移过程中遇到问题，可以临时回滚到原有结构：

1. 恢复原有的路由引入
2. 暂时使用 `router.js` 文件
3. 排查问题后重新迁移

## 技术支持

如果在迁移过程中遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查网络请求是否正常
3. 对比新旧路由配置差异
4. 联系项目技术负责人 