# 🎨 SignatureQuery.vue 卡片式风格优化

## 🎯 优化概述

将SignatureQuery.vue从传统表格展示优化为现代化卡片式风格，默认展示签名基本信息，支持展开查看详细实名信息，并合理处理同一签名多个用户存在的场景。

## ✨ 核心优化特性

### 📋 主要功能优化
- **卡片式展示**: 采用现代化卡片设计，信息层次清晰
- **按签名分组**: 同一签名的多个用户合并展示，避免重复
- **展开/收起**: 默认显示基本信息，可展开查看详细内容
- **实名状态可视化**: 三大运营商实名状态直观展示
- **图片预览**: 支持资质图片独立预览
- **响应式设计**: 适配不同屏幕尺寸

### 🎨 UI设计特色
- **现代化卡片**: Element Plus卡片组件，悬停效果
- **信息分层**: 基本信息 → 详细信息 → 操作按钮
- **运营商主题**: 移动蓝色、联通红色、电信橙色
- **状态标签**: 直观的颜色和图标标识
- **空状态处理**: 友好的空数据提示

## 🔧 技术实现

### 1. 数据分组处理

#### **计算属性 - groupedData**
```javascript
computed: {
  // 按签名分组数据
  groupedData() {
    const groups = {}
    this.tableData.forEach(item => {
      const signature = item.signature
      if (!groups[signature]) {
        groups[signature] = []
      }
      groups[signature].push(item)
    })
    // 按创建时间排序每个组内的数据
    Object.keys(groups).forEach(signature => {
      groups[signature].sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
    })
    return groups
  }
}
```

#### **多用户场景处理**
- 同一签名的多个用户合并为一个卡片
- 显示用户数量标签
- 展开后显示所有用户列表
- 支持点击单个用户查看详情

### 2. 卡片式布局设计

#### **卡片头部结构**
```vue
<template #header>
  <div class="signature-header">
    <div class="signature-info">
      <div class="signature-name">
        <i class="el-icon-edit"></i>
        <span class="signature-text">{{ signature }}</span>
        <el-tag :type="getSignatureAttrTag(group[0].signatureAttr).type">
          {{ getSignatureAttrTag(group[0].signatureAttr).text }}
        </el-tag>
        <el-tag v-if="group.length > 1" type="warning">
          {{ group.length }}个用户
        </el-tag>
      </div>
      <div class="signature-meta">
        <span class="service-type">
          <i class="el-icon-message"></i>
          {{ getServiceTypeTag(group[0].serviceType).text }}
        </span>
        <span class="create-time">
          <i class="el-icon-time"></i>
          {{ formatDate(group[0].createTime) }}
        </span>
      </div>
    </div>
    <div class="signature-actions">
      <el-button type="text" @click="toggleExpand(signature)">
        <i :class="expandedCards[signature] ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
        {{ expandedCards[signature] ? '收起' : '展开' }}
      </el-button>
    </div>
  </div>
</template>
```

#### **卡片内容结构**
- **短信示例**: 带图标的示例内容展示
- **实名状态概览**: 三大运营商状态横向展示
- **展开内容**: 用户列表 + 详细实名信息 + 操作按钮

### 3. 展开/收起功能

#### **状态管理**
```javascript
data() {
  return {
    expandedCards: {}, // 展开的卡片状态
    // ...其他数据
  }
}
```

#### **切换方法**
```javascript
methods: {
  // 切换卡片展开状态
  toggleExpand(signature) {
    this.$set(this.expandedCards, signature, !this.expandedCards[signature])
  }
}
```

#### **动画效果**
```vue
<el-collapse-transition>
  <div v-show="expandedCards[signature]" class="expanded-content">
    <!-- 展开内容 -->
  </div>
</el-collapse-transition>
```

### 4. 实名状态可视化

#### **概览展示**
```vue
<div class="operator-status-row">
  <div class="operator-item">
    <i class="iconfont icon-yidong operator-icon yd"></i>
    <span class="operator-name">移动</span>
    <el-tag :type="group[0].ydRealNameStatus == 1 ? 'success' : 'info'">
      {{ group[0].ydRealNameStatus == 1 ? '已实名' : '未实名' }}
    </el-tag>
  </div>
  <!-- 联通、电信类似 -->
</div>
```

#### **详细展示**
```vue
<el-row :gutter="16" class="realname-cards">
  <el-col :span="8">
    <div class="realname-card yd-card">
      <div class="realname-card-header">
        <i class="iconfont icon-yidong"></i>
        <span>移动</span>
      </div>
      <div class="realname-card-content">
        <el-tag :type="group[0].ydRealNameStatus == 1 ? 'success' : 'info'" size="large">
          {{ group[0].ydRealNameStatus == 1 ? '已实名' : '未实名' }}
        </el-tag>
        <div v-if="group[0].ydAvalibleType" class="available-types">
          <div class="types-label">可用类型：</div>
          <el-tag v-for="type in group[0].ydAvalibleType.split(',')" size="small" type="success">
            {{ type.trim() }}
          </el-tag>
        </div>
      </div>
    </div>
  </el-col>
  <!-- 联通、电信类似 -->
</el-row>
```

### 5. 多用户场景处理

#### **用户列表展示**
```vue
<div class="users-list" v-if="group.length > 1">
  <div class="users-label">
    <i class="el-icon-user"></i>
    使用该签名的用户 ({{ group.length }}个)
  </div>
  <div class="users-grid">
    <div 
      v-for="item in group" 
      :key="item.signatureId"
      class="user-item"
      @click="handleViewDetail(item)"
    >
      <div class="user-name">{{ item.consumerName }}</div>
      <div class="user-meta">
        <span class="user-id">ID: {{ item.userId }}</span>
        <span class="create-date">{{ formatDate(item.createTime) }}</span>
      </div>
    </div>
  </div>
</div>
```

#### **处理逻辑**
- 按签名分组，每组按创建时间倒序排列
- 显示用户数量标签提醒
- 网格布局展示所有用户
- 点击用户可查看该用户的详细信息

### 6. 图片预览功能

#### **独立预览弹窗**
```vue
<el-dialog title="资质图片预览" v-model="imagePreviewVisible" width="800px">
  <div class="image-preview-container">
    <el-image
      v-for="(img, index) in previewImages"
      :key="index"
      :src="img"
      :preview-src-list="previewImages"
      fit="contain"
      class="preview-image"
    />
  </div>
</el-dialog>
```

#### **预览方法**
```javascript
methods: {
  // 查看图片
  handleViewImages(row) {
    if (row.imgUrl) {
      this.previewImages = this.getImageList(row.imgUrl)
      this.imagePreviewVisible = true
    }
  }
}
```

## 🎨 样式设计

### 1. 卡片样式

#### **基础卡片**
```css
.signature-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  }
}
```

#### **运营商主题色**
```css
.operator-icon {
  &.yd { color: #409eff; } /* 移动 - 蓝色 */
  &.lt { color: #f56c6c; } /* 联通 - 红色 */
  &.dx { color: #e6a23c; } /* 电信 - 橙色 */
}
```

### 2. 响应式设计

#### **移动端适配**
```css
@media (max-width: 768px) {
  .signature-card {
    .signature-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
    
    .realname-overview {
      .operator-status-row {
        flex-direction: column;
        gap: 12px;
      }
    }
    
    .users-grid {
      grid-template-columns: 1fr;
    }
  }
}
```

## 🔄 功能流程

### 1. 数据展示流程
```
查询数据 → 按签名分组 → 生成卡片 → 默认收起状态 → 
显示基本信息 → 用户点击展开 → 显示详细信息
```

### 2. 多用户处理流程
```
检测同签名用户 → 显示用户数量标签 → 展开显示用户列表 → 
点击用户 → 查看该用户详情
```

### 3. 图片预览流程
```
点击查看资质按钮 → 解析图片URL → 打开预览弹窗 → 
支持轮播查看 → 点击图片放大
```

## 📊 数据结构

### 1. 分组数据结构
```javascript
groupedData: {
  "【签名1】": [
    { userId: 1, consumerName: "用户A", signatureId: 1, ... },
    { userId: 2, consumerName: "用户B", signatureId: 2, ... }
  ],
  "【签名2】": [
    { userId: 3, consumerName: "用户C", signatureId: 3, ... }
  ]
}
```

### 2. 展开状态管理
```javascript
expandedCards: {
  "【签名1】": true,  // 已展开
  "【签名2】": false  // 已收起
}
```

## 🎯 使用场景

### 1. 签名概览查看
```
运营人员需要快速了解签名状态 → 查看卡片基本信息 → 
获取签名类型、实名状态概览
```

### 2. 详细信息查看
```
需要了解详细实名信息 → 点击展开按钮 → 
查看三大运营商详细状态和可用类型
```

### 3. 多用户签名管理
```
发现同一签名多个用户 → 查看用户数量标签 → 
展开查看所有用户 → 点击特定用户查看详情
```

### 4. 资质材料查看
```
需要查看签名资质 → 点击查看资质按钮 → 
独立弹窗预览图片 → 支持轮播和放大
```

## 🚀 优化效果

### 1. 用户体验提升
- **信息密度**: 卡片式布局信息密度更高，一屏显示更多内容
- **视觉层次**: 清晰的信息层次，重要信息突出显示
- **交互友好**: 展开/收起操作直观，减少页面跳转

### 2. 功能增强
- **数据去重**: 同签名用户合并显示，避免重复信息
- **状态可视化**: 实名状态一目了然，运营商图标直观
- **快速预览**: 图片独立预览，不影响主界面

### 3. 性能优化
- **按需展开**: 详细信息按需加载，减少初始渲染压力
- **响应式设计**: 适配各种设备，提升移动端体验
- **动画效果**: 平滑的展开收起动画，提升交互体验

---

**优化完成**: SignatureQuery.vue已成功优化为现代化卡片式风格，提供了更好的用户体验和功能完整性。
