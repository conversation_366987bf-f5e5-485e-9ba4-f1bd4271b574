<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form :model="ruleForm" :inline="true" ref="ruleForm" class="demo-ruleForm" label-width="82px">
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="任务名称" prop="taskName">
          <el-input class="input-w" v-model="ruleForm.taskName"></el-input>
        </el-form-item>
        <el-form-item label="链接ID" prop="linkId">
          <el-input class="input-w" v-model="ruleForm.linkId"></el-input>
        </el-form-item>
        <el-form-item label="模板ID" prop="templateId">
          <el-input class="input-w" v-model="ruleForm.templateId"></el-input>
        </el-form-item>
        <el-form-item label="模板名称" prop="templateCondition">
          <el-input class="input-w" v-model="ruleForm.templateCondition"></el-input>
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input class="input-w" v-model="ruleForm.signature" placeholder="签名" />
        </el-form-item>
        <el-form-item label="发送类型" prop="sendType">
          <el-select class="input-w" v-model="ruleForm.sendType" clearable placeholder="请选择">
            <el-option label="全部 " value=""> </el-option>
            <el-option label="全量发送 " value="0"> </el-option>
            <el-option label="解析发送" value="1"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label-width="110px" label="发送时间类型" prop="timingSend">
          <el-select class="input-w" v-model="ruleForm.timingSend" clearable placeholder="请选择">
            <el-option label="立即发送" value="0"> </el-option>
            <el-option label="定时发送" value="1"> </el-option>
          </el-select>
        </el-form-item>

        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')">查询</el-button>
          <el-button plain type="primary" @click="resetForm('ruleForm')">重置</el-button>
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
        @selection-change="handleSelectionChange"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
          <el-button type="primary" v-if="ids.length > 0" @click="cancelSend(false)">批量取消定时发送</el-button>
        </template>
      </vxe-toolbar>

      <vxe-table ref="tableRef" id="yuexinSendTask" border stripe :custom-config="customConfig"
        :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
        v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;" :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange"
        @checkbox-change="handleSelectionChange">

        <vxe-column type="checkbox" width="45">
        </vxe-column>
        <vxe-column field="用户名称" title="用户名称" width="90">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.username" :content="scope.row.username" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">{{
                                     scope.row.username
                                }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="任务名称" title="任务名称" width="100">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.taskName" :content="scope.row.taskName" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <el-tooltip effect="dark" :content="scope.row.taskName"
                                placement="top-start">
                                <div class="wrapper-text">{{
                                     scope.row.taskName
                                }}</div>
                            </el-tooltip> -->
          </template>
        </vxe-column>
        <vxe-column field="链接ID" title="链接ID" width="90">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.linkId" :content="scope.row.linkId" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">{{
                                scope.row.linkId
                            }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="模版ID" title="模版ID" width="150">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.templateId" :content="scope.row.templateId" className="wrapper-text"
              effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">{{
                                scope.row.linkId
                            }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="模板名称" title="模板名称" width="110">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.templateName" :content="scope.row.templateName" className="wrapper-text"
              effect="light">
            </Tooltip>
            <!-- <el-tooltip effect="dark" :content="'模板名称：' + scope.row.templateName + '-模板ID：' + scope.row.templateId"
                                placement="top-start">
                                <div class="wrapper-text">{{
                                    scope.row.templateName
                                }}</div>
                            </el-tooltip> -->
          </template>
        </vxe-column>
        <vxe-column field="签名" title="签名" width="90">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.signature" :content="scope.row.signature" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">{{
                                scope.row.signature
                            }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="发送内容" title="发送内容" width="270">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.content" :content="scope.row.content" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <el-tooltip effect="dark" :content="scope.row.content"
                                placement="top-start">
                                <div class="wrapper-text">{{
                                    scope.row.content
                                }}</div>
                            </el-tooltip> -->
          </template>
        </vxe-column>

        <vxe-column field="链接类型" title="链接类型" width="80">
          <template v-slot="scope">
            <div class="wrapper-text" v-if="scope.row.linkType == '0'">
              群发
            </div>
            <div class="wrapper-text" v-else>个性化</div>
          </template>
        </vxe-column>
        <vxe-column field="发送类型" title="发送类型" width="80">
          <template v-slot="scope">
            <div class="wrapper-text" v-if="scope.row.sendType == '0'">
              全量发送
            </div>
            <div class="wrapper-text" v-else>解析发送</div>
          </template>
        </vxe-column>
        <vxe-column field="发送时间类型" title="发送时间类型" width="100">
          <template v-slot="scope">
            <div class="wrapper-text" v-if="scope.row.timingSend == '0'">
              立即发送
            </div>
            <div class="wrapper-text" v-else>定时发送</div>
          </template>
        </vxe-column>
        <vxe-column field="自定义短链" title="自定义短链" width="90">
          <template v-slot="scope">
            <!-- <Tooltip v-if="scope.row.shortLinkUrl" :content="scope.row.shortLinkUrl"  className="wrapper-text"
                                effect="light">
                            </Tooltip> -->
            <el-tooltip v-if="scope.row.shortLinkUrl" effect="light" :content="scope.row.shortLinkUrl"
              placement="top-start">
              <a :href="'https://' + scope.row.shortLinkUrl" target="_blank" rel="noopener noreferrer">
                <i class="iconfont icon-lianjie"></i>
              </a>
            </el-tooltip>
            <span v-else>-</span>
            <!-- <a :href="'https://' + scope.row.shortLinkUrl" target="_blank" rel="noopener noreferrer">{{
                                scope.row.shortLinkUrl }}</a> -->
          </template>
        </vxe-column>
        <vxe-column field="文件名/手机号" title="文件名/手机号" width="120">
          <template v-slot="scope">
            <div class="wrapper-text" v-if="scope.row.filePath">
              {{ scope.row.fileOriginalName }}
            </div>
            <div class="wrapper-text" v-else style="
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: block;
              ">
              {{ scope.row.mobile }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="提交号码数" title="提交号码数" width="90">
          <template v-slot="scope">
            <div class="wrapper-text">{{ scope.row.successCount }}</div>
          </template>
        </vxe-column>
        <vxe-column field="失败号码数" title="失败号码数" width="90">
          <template v-slot="scope">
            <div class="wrapper-text">{{ scope.row.invalidCount }}</div>
          </template>
        </vxe-column>
        <vxe-column field="发送时间" title="发送时间" width="100">
          <template v-slot="scope">
            <p class="wrapper-text" v-if="scope.row.sendTime">
              {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
            </p>
            <p class="wrapper-text" v-if="scope.row.sendTime">
              {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
            </p>
          </template>
        </vxe-column>
        <vxe-column field="状态" title="状态" width="180">
          <template v-slot="scope">
            <el-tag effect="dark" style="background: #e6a23c" v-if="scope.row.status == 'WAIT_MATCH'">
              待撞库
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'MATCH_PROCESSING'">
              撞库执行中
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'MATCH_ERROR'">
              撞库执行中
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'WAIT_LINK'">
              阅信链接待生成
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'LINK_PROCESSING'">
              阅信链接生成中
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'LINK_OK'">
              阅信链接生成成功
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'LINK_ERROR'">
              阅信链接生成失败
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'WAIT_SEND'">
              任务待处理
            </el-tag>
            <el-tag effect="dark" style="background: #e6a23c" v-else-if="scope.row.status == 'SEND_PROCESSING'">
              任务处理中
            </el-tag>
            <el-tag effect="dark" style="background: #67c23a" v-else-if="scope.row.status == 'SEND_OK'">
              处理成功
            </el-tag>
            <el-tooltip v-else-if="scope.row.status == 'SEND_ERROR'" effect="light" :content="scope.row.reason"
              placement="top-start">
              <el-tag effect="dark" style="background: #f56c6c">
                发送异常
              </el-tag>
            </el-tooltip>
            <el-tag effect="dark" style="background: #f56c6c" v-else-if="scope.row.status == 'CANCEL'">
              任务取消
            </el-tag>
          </template>
        </vxe-column>
        <vxe-column fixed="right" field="操作" title="操作" width="140">
          <template v-slot="scope">
            <el-button link style="color: #409eff;" @click="checkEcharts(scope.row)" size="default">统计</el-button>
            <el-button link style="color: #409eff;" v-if="scope.row.status === 'WAIT_SEND'"
              @click="cancelSend(scope.row.id)" size="default">取消定时发送</el-button>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="paginationBox">
        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
        </el-pagination>
      </div>
      <el-dialog v-model="echartsDialogVisible" :title="'报表统计__' + title" width="980px" :before-close="handleClose">
        <div>
          <div class="statistic_bar">
            <div class="bar-xsms">
              <TreeChart id="xsms" :treeData="treeData"></TreeChart>
            </div>
          </div>
          <div class="Templat-matter">
            <div>
              <p style="font-weight: bolder; font-size: 16px">名词解释：</p>
              <p>1、提交号码：本次任务提交的有效号码数。</p>
              <p>
                2、自定义链接：不支持阅信解析的号码数，目前苹果手机暂不支持阅信。
              </p>
              <p>
                3、阅信链接：支持阅信解析的号码数，目前支持厂商有华为/小米/VIVO/OPPO。
              </p>
              <p>4、发送成功：短信接收成功号码数。</p>
            </div>
            <div style="margin-left: 30px">
              <p>&nbsp;</p>
              <p>5、短链点击：点击自定义链接的号码数。</p>
              <p>6、未解析：阅信未解析的号码数。</p>
              <p>
                7、已解析：阅信已解析的号码数，根据运营商返回解析报文进行匹配。
              </p>
              <p>
                8、阅信点击：阅信点击号码数，跟踪的是点击阅信模板上的按钮/图片的号码。
              </p>
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="echartsDialogVisible = false">关闭</el-button>
            <!-- <el-button type="primary" @click="dialogVisible = false">
                                                                                                                    Confirm
                                                                                                                </el-button> -->
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import TreeChart from '@/components/publicComponents/treeChart.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    TreeChart,
    Tooltip,
  },
  name: 'xsmsRecord',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      status: '',
      title: '',
      reason: '',
      usedTimes: '',
      xsmsUrl: '',
      usedTimesIndex: 0,
      dialogVisible: false,
      dialogEditNum: false,
      codeFlag: true,
      value1: [],
      updateList: {},
      ruleForm: {
        taskName: '',
        username: '',
        linkId: '',
        signature: '',
        timingSend: '',
        templateId: '',
        templateCondition: '',
        sendType: '',
        currentPage: 1,
        pageSize: 10,
      },
      reasonFrom: {
        reason: '',
      },
      rules: {
        reason: [
          { required: true, message: '审核原因不能为空', trigger: 'blur' },
        ],
      },
      tabelAlllist: {
        taskName: '',
        username: '',
        linkId: '',
        signature: '',
        templateId: '',
        templateCondition: '',
        timingSend: '',
        sendType: '',
        currentPage: 1,
        pageSize: 10,
      },
      detail: {},
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      ids: [],
      echartsDialogVisible: false,
      treeData: {},
      title: '',
      taskId: '',
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getchatList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getchatList() {
      this.tableDataObj.loading2 = true
      // this.tabelAlllist.status = this.tabelAlllist.shortStatus.join(',')
      window.api.post(
        window.path.yuexin + '/operator/sendTask/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
          // console.log(res, "res");
        }
      )
    },
    // getRowClass({ rowIndex, columnIndex }) {
    //     if (columnIndex == 13||columnIndex == 14||columnIndex == 15||columnIndex == 16||columnIndex == 17||columnIndex == 18||columnIndex == 19) {
    //         return "background:#EEFBEE;";
    //     }else if(columnIndex == 6||columnIndex == 7||columnIndex == 8||columnIndex == 9||columnIndex == 10 ||columnIndex == 11||columnIndex == 12){
    //         return "background:#EDF4FF;";
    //     }
    // },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.ruleForm.startTime = ''
      this.ruleForm.endTime = ''
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.value1 = []
      this.getchatList()
    },
    // 是否启用勾选
    selectable(row, index) {
      return row.status === 'WAIT_SEND'
    },
    // 批量数据改变触发
    handleSelectionChange(row) {
      if (row.records.length > 0) {
        this.ids = row.records.map(item => {
          return item.id
        })
      } else {
        this.ids = []
      }
    },
    checkEcharts(row) {
      this.echartsDialogVisible = true
      this.title = row.taskName
      this.taskId = row.id
      if (row.sendType == '0') {
        this.treeData = {
          name:
            row.sendTotal != null
              ? '提交号码(' + row.sendTotal + ')'
              : '提交号码(未知)',
          children: [
            {
              name:
                row.notMatchCount != null
                  ? '自定义链接(' + row.notMatchCount + ')'
                  : '自定义链接(未知)',
              children: [
                {
                  name:
                    row.smsSuccessNum != null
                      ? '发送成功(' + row.smsSuccessNum + ')'
                      : '发送成功(未知)',
                  // value: row.smsSuccessNum,
                  children: [
                    {
                      name:
                        row.smsClickNum != null
                          ? '链接点击(' + row.smsClickNum + ')'
                          : '链接点击(未知)',
                      // value: row.smsClickNum
                    },
                  ],
                },
              ],
            },
            {
              name:
                row.matchCount != null
                  ? '阅信链接(' + row.matchCount + ')'
                  : '阅信链接(未知)',
              children: [
                {
                  name:
                    row.yxSuccessNum != null
                      ? '发送成功(' + row.yxSuccessNum + ')'
                      : '发送成功(未知)',
                  // value: row.yxSuccessNum,
                  children: [
                    {
                      name:
                        row.residueTimes != null
                          ? '未解析(' + row.residueTimes + ')'
                          : '未解析(未知)',
                      // value: row.residueTimes
                    },
                    {
                      name:
                        row.useTimes != null
                          ? '已解析(' + row.useTimes + ')'
                          : '已解析(未知)',
                      // value: row.useTimes,
                      children: [
                        {
                          name:
                            row.yxClickNum != null
                              ? '阅信点击(' + row.yxClickNum + ')'
                              : '阅信点击(未知)',
                          // value: row.yxClickNum
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        }
      } else {
        this.treeData = {
          name:
            row.sendTotal != null
              ? '提交号码(' + row.sendTotal + ')'
              : '提交号码(未知)',
          children: [
            {
              name:
                row.matchCount != null
                  ? '阅信链接(' + row.matchCount + ')'
                  : '阅信链接(未知)',
              children: [
                {
                  name:
                    row.yxSuccessNum != null
                      ? '发送成功(' + row.yxSuccessNum + ')'
                      : '发送成功(未知)',
                  // value: row.yxSuccessNum,
                  children: [
                    {
                      name:
                        row.residueTimes != null
                          ? '未解析(' + row.residueTimes + ')'
                          : '未解析(未知)',
                      // value: row.residueTimes
                    },
                    {
                      name:
                        row.useTimes != null
                          ? '已解析(' + row.useTimes + ')'
                          : '已解析(未知)',
                      // value: row.useTimes,
                      children: [
                        {
                          name:
                            row.yxClickNum != null
                              ? '阅信点击(' + row.yxClickNum + ')'
                              : '阅信点击(未知)',
                          // value: row.yxClickNum
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        }
      }
    },
    handleClose() {
      this.echartsDialogVisible = false
    },
    // 关闭发送
    cancelSend(id) {
      this.$confirm('是否取消定时发送?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.batchCancel(id)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作',
          })
        })
    },
    // 批量取消定时发送
    batchCancel(id) {
      let params = {
        ids: [],
      }
      if (id) {
        params.ids.push(id)
      } else {
        params.ids = this.ids
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      window.api.post(
        window.path.yuexin + '/operator/sendTask/cancel',
        params,
        (res) => {
          loading.close()
          this.$message({
            type: 'success',
            message: '操作成功!',
          })
          this.getchatList()
          console.log('操作成功', res)
        }
      )
      setTimeout(() => {
        if (loading) {
          loading.close()
          this.getchatList()
        }
      }, 5000)
    },
  },
  watch: {
    echartsDialogVisible(val) {
      if (!val) {
        this.treeData = {}
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}

.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}

.statistic_bar {
  width: 100%;
  height: 460px;
  background: #fff;
  margin-top: 10px;
  position: relative;
}

.bar-xsms {
  width: 100%;
  height: 400px;
  position: absolute;
  top: 0%;
  margin-top: 20px;
}

.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
  display: flex;
}

.Templat-matter>p {
  padding: 5px 0;
}
</style>

<style>
/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>