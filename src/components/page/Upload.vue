<template>
  <div>
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-calendar /></el-icon> 表单</el-breadcrumb-item
        >
        <el-breadcrumb-item>图片上传</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="container">
      <div class="content-title">支持拖拽</div>
      <div class="plugins-tips">
        Element UI自带上传组件。 访问地址：<a
          href="http://element.eleme.io/#/zh-CN/component/upload"
          target="_blank"
          >Element UI Upload</a
        >
      </div>
      <el-upload class="upload-demo" drag action="/api/posts/" multiple>
        <el-icon><el-icon-upload /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template v-slot:tip>
          <div class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
        </template>
      </el-upload>
      <div class="content-title">支持裁剪</div>
      <div class="plugins-tips">
        vue-cropperjs：一个封装了 cropperjs 的 Vue 组件。 访问地址：<a
          href="https://github.com/Agontuk/vue-cropperjs"
          target="_blank"
          >vue-cropperjs</a
        >
      </div>
      <div class="crop-demo">
        <img :src="cropImg" class="pre-img" />
        <div class="crop-demo-btn">
          选择图片
          <input
            class="crop-input"
            type="file"
            name="image"
            accept="image/*"
            @change="setImage"
          />
        </div>
      </div>

      <el-dialog title="裁剪图片" v-model="dialogVisible" width="30%">
        <vue-cropper
          ref="cropper"
          :src="imgSrc"
          :ready="cropImage"
          :zoom="cropImage"
          :cropmove="cropImage"
          style="width: 100%; height: 300px"
        ></vue-cropper>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="cancelCrop">取 消</el-button>
            <el-button type="primary" @click="dialogVisible = false"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import VueCropper from 'vue-cropperjs'
export default {
  components: {
    VueCropper,
    ElIconLxCalendar,
    ElIconUpload,
  },
  name: 'upload',
  data: function () {
    return {
      defaultSrc: '../../assets/images/img.jpg',
      fileList: [],
      imgSrc: '',
      cropImg: '',
      dialogVisible: false,
    }
  },
  methods: {
    setImage(e) {
      const file = e.target.files[0]
      if (!file.type.includes('image/')) {
        return
      }
      const reader = new FileReader()
      reader.onload = (event) => {
        this.dialogVisible = true
        this.imgSrc = event.target.result
        this.$refs.cropper && this.$refs.cropper.replace(event.target.result)
      }
      reader.readAsDataURL(file)
    },
    cropImage() {
      this.cropImg = this.$refs.cropper.getCroppedCanvas().toDataURL()
    },
    cancelCrop() {
      this.dialogVisible = false
      this.cropImg = this.defaultSrc
    },
    imageuploaded(res) {
      console.log(res)
    },
    handleError() {
      this.$notify.error({
        title: '上传失败',
        message: '图片上传接口上传失败，可更改为自己的服务器接口',
      })
    },
  },
  created() {
    this.cropImg = this.defaultSrc
  },
}
</script>

<style scoped>
.content-title {
  font-weight: 400;
  line-height: 50px;
  margin: 10px 0;
  font-size: 22px;
  color: #1f2f3d;
}
.pre-img {
  width: 100px;
  height: 100px;
  background: #f8f8f8;
  border: 1px solid #eee;
  border-radius: 5px;
}
.crop-demo {
  display: flex;
  align-items: flex-end;
}
.crop-demo-btn {
  position: relative;
  width: 100px;
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  margin-left: 30px;
  background-color: #409eff;
  color: #fff;
  font-size: 14px;
  border-radius: 4px;
  box-sizing: border-box;
}
.crop-input {
  position: absolute;
  width: 100px;
  height: 40px;
  left: 0;
  top: 0;
  opacity: 0;
  cursor: pointer;
}
</style>
