<template>
  <div class="bag" style="background: #fff; padding: 10px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-page-header
            style="color: #16a589"
            @back="goBack"
            :content="'5G注册' + '--' + username"
          >
          </el-page-header
        ></el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div style="padding: 20px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
      > -->
      <el-form :model="searchForm" ref="searchForm" inline>
        <el-form-item label="状态" prop="customerStatus" style="width: 300px">
          <el-select v-model="searchForm.customerStatus" placeholder="请选择状态">
            <el-option label="待注册" value="0"></el-option>
            <el-option label="通道注册中" value="10"></el-option>
            <el-option label="通道注册失败" value="20"></el-option>
            <el-option label="通道注册成功" value="30"></el-option>
            <el-option label="分配服务代码中" value="40"></el-option>
            <el-option label="分配服务代码失败" value="45"></el-option>
            <el-option label="分配服务代码成功" value="48"></el-option>
            <el-option label="正常" value="50"></el-option>
            <el-option label="变更中" value="52"></el-option>
            <el-option label="通道暂停运营" value="60"></el-option>
            <el-option label="变更失败" value="55"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button plain type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <vxe-toolbar ref="toolbarRef">
        <template #buttons>
          <el-button type="primary" class="el-icon-refresh" @click="refresh"
            ><el-icon><Refresh /></el-icon>刷新</el-button
          >
          <el-button type="primary" @click="addChannel">添加通道</el-button>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="channel5G"
        border
        stripe
        show-overflow
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px"
        :data="tableDataObj.tableData"
      >
        <vxe-column field="公司名称" title="公司名称" width="180px">
          <template v-slot="scope">
            <div>
              {{ scope.row.customerName }}
            </div>
          </template>
        </vxe-column>

        <vxe-column field="通道名称" title="通道名称">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelName }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="通道编号" title="通道编号">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelId }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="通道运营商" title="通道运营商" width="130px">
          <template v-slot="scope">
            <div
              v-if="scope.row.channelOperator == 1"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-yidong"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">移动</span>
            </div>
            <div
              v-else-if="scope.row.channelOperator == 2"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-liantong"
                style="font-size: 16px; color: #f56c6c"
              ></i>
              <span style="margin-left: 5px">联通</span>
            </div>
            <div
              v-else-if="scope.row.channelOperator == 3"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-dianxin"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">电信</span>
            </div>
            <div v-else></div>
          </template>
        </vxe-column>
        <vxe-column field="客户编码" title="客户编码">
          <template v-slot="scope">
            <div>
              {{ scope.row.customerNum }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="服务代码" title="服务代码">
          <template v-slot="scope">
            <div>
              {{ scope.row.spCode }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="状态" title="状态">
          <template v-slot="scope">
            <div v-if="scope.row.status == 0">待注册</div>
            <div style="color: #f56c6c" v-else-if="scope.row.status == 10"
              >通道注册中</div
            >
            <div style="color: #f56c6c" v-else-if="scope.row.status == 20"
              >通道注册失败</div
            >
            <div style="color: #16a589" v-else-if="scope.row.status == 30"
              >通道注册成功</div
            >
            <div style="color: #f56c6c" v-else-if="scope.row.status == 40"
              >分配服务代码中</div
            >
            <div style="color: red" v-else-if="scope.row.status == 45"
              >分配服务代码失败</div
            >
            <div style="color: #16a589" v-else-if="scope.row.status == 48"
              >分配服务代码成功</div
            >
            <div style="color: #16a589" v-else-if="scope.row.status == 50"
              >正常</div
            ><div style="color: red" v-else-if="scope.row.status == 52"
              >变更中</div
            >
            <div v-else-if="scope.row.status == 60">通道暂停运营</div>
            <div v-else-if="scope.row.status == 55" style="color: red"
              >变更失败</div
            >
          </template>
        </vxe-column>
        <vxe-column field="授权书" title="授权书">
          <template v-slot="scope">
            <div>
              <a
                v-if="scope.row.contractUrl"
                :href="scope.row.contractUrl"
                target="_blank"
                rel="noopener noreferrer"
                >立即下载</a
              >
              <!-- <img v-if="scope.row.contractUrl " :src="scope.row.contractUrl " @click="checkImg(scope.row.contractUrl)" alt="" width="50px" height="50px"> -->
            </div>
          </template>
        </vxe-column>
        <vxe-column field="原因" title="原因">
          <template v-slot="scope">
            <div>
              {{ scope.row.reason }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="160px">
          <template v-slot="scope">
            <div>{{
              moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss")
            }}</div>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" width="240">
          <template v-slot="scope">
            <el-button
              @click="declareChannel(scope.row)"
              link
              style="margin-left: 10px; color: #409eff"
              >修改通道信息</el-button
            >
            <el-button
              v-if="scope.row.status == 0 || scope.row.status == 20"
              @click="handleReport(scope.row)"
              link
              style="margin-left: 10px; color: #409eff"
              >报备</el-button
            >
            <el-button
              v-if="scope.row.status == 30 || scope.row.status == 45"
              @click="serviceCode(scope.row)"
              link
              style="margin-left: 10px; color: #409eff"
              >分配服务代码</el-button
            >
            <el-button
              v-if="scope.row.status == 50 || scope.row.status == 55"
              @click="handupload(scope.row)"
              link
              style="margin-left: 10px; color: #409eff"
              >更新</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="70%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <!-- <span>这是一段信息</span> -->
      <el-form
        :model="formInline"
        :rules="rules"
        :inline="true"
        label-width="170px"
        ref="formInlines"
        class="demo-form-inline"
      >
        <div>
          <el-form-item label="运营商：" prop="channelOperator">
            <el-select
              class="input-w"
              style="width: 220px"
              v-model="formInline.channelOperator"
              @change="handleOperatorChange"
              placeholder="选择运营商"
            >
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道：" prop="channelId">
            <el-select
              class="input-w"
              multiple
              :disabled="formInline.id ? true : false"
              style="width: 220px"
              v-model="formInline.channelId"
              @change="handChannel"
              placeholder="选择通道"
            >
              <el-option
                v-for="item in channel"
                :key="item.channelCode"
                :label="item.channelName"
                :value="item.channelCode"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>

        <!-- <div v-if="this.formInline.channelOperator == '1'">
              <el-form-item label="代理商名称：" prop="belongAgentName">
                <el-input
                  maxlength="64"
                  show-word-limit
                  style="width: 220px"
                  v-model="formInline.belongAgentName"
                ></el-input>
              </el-form-item>
              <el-form-item label="代理商编码：" prop="belongAgentCode">
                <el-input
                  maxlength="30"
                  show-word-limit
                  style="width: 220px"
                  v-model="formInline.belongAgentCode"
                ></el-input>
              </el-form-item>
              <el-form-item label="归属区域编码：" prop="belongRegionCode">
                <el-input
                  style="width: 220px"
                  v-model="formInline.belongRegionCode"
                  placeholder="大区，省分，城市用逗号间隔"
                ></el-input>
              </el-form-item>
            </div> -->
        <el-form-item
          v-if="formInline.channelOperator != ''"
          label="行业类型编码："
          :prop="formInline.channelOperator != '3' ? 'industryTypeCode' : ''"
        >
          <el-select
            v-if="formInline.channelOperator == '1'"
            style="width: 220px"
            class="input-w"
            filterable
            v-model="formInline.industryTypeCode"
            placeholder="请选择行业类别"
          >
            <el-option
              v-for="(item, index) in industrys"
              :label="item.subIndustryName"
              :value="item.subIndustryCode"
              :key="index"
            ></el-option>
          </el-select>
          <el-select
            v-if="formInline.channelOperator == '2'"
            style="width: 220px"
            class="input-w"
            v-model="formInline.industryTypeCode"
            placeholder="请选择行业类别"
          >
            <el-option
              v-for="(item, index) in ltOption"
              :label="item.lable"
              :value="item.value"
              :key="index"
            ></el-option>
          </el-select>
          <el-select
            v-if="formInline.channelOperator == '3'"
            style="width: 220px"
            class="input-w"
            filterable
            v-model="formInline.industryTypeCode"
            placeholder="请选择行业类别"
          >
            <el-option
              v-for="(item, index) in dxOption"
              :label="item.lable"
              :value="item.value"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="合同名称："
          :key="`contractName-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'contractName' : ''"
        >
          <el-input
            class="input-w"
            maxlength="50"
            show-word-limit
            style="width: 220px"
            v-model="formInline.contractName"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="合同编号："
          :key="`contractCode-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'contractCode' : ''"
        >
          <el-input
            class="input-w"
            maxlength="50"
            show-word-limit
            style="width: 220px"
            v-model="formInline.contractCode"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="合同生效日期："
          :key="`contractValidDate-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'contractValidDate' : ''"
        >
          <el-date-picker
            class="input-w"
            @change="time1"
            v-model="formInline.contractValidDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="合同失效日期："
          :key="`contractInvalidDate-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'contractInvalidDate' : ''"
        >
          <el-date-picker
            class="input-w"
            @change="time2"
            v-model="formInline.contractInvalidDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="合同是否自动续签："
          :key="`renewed-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'renewed' : ''"
        >
          <el-radio-group v-model="formInline.renewed">
            <el-radio value="1">是</el-radio>
            <el-radio value="0" style="margin-left: 10px">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="formInline.renewed == '1'"
          label="合同续签日期："
          :key="`contractRenewDate-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'contractRenewDate' : ''"
        >
          <el-date-picker
            class="input-time"
            @change="time3"
            v-model="formInline.contractRenewDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="电子合同附件上传："
          :key="`contractUrl-${formInline.channelOperator}`"
          :prop="formInline.channelOperator != '3' ? 'contractUrl' : ''"
        >
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
            :before-upload="beforeAvatarUpload"
            multiple
            :limit="1"
            :file-list="fileList"
          >
            <el-button size="default" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <div style="display: flex; justify-content: space-between">
          <div></div>
          <div style="padding: 0 10px">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitChannel('formInlines')"
              >提交</el-button
            >
          </div>
        </div>
        <div class="solid"></div>
        <div>基本信息</div>
        <div>
          <el-form-item label="用户名称：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.username"
            ></el-input>
          </el-form-item>
          <el-form-item label="客户名称：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.customerName"
            ></el-input>
          </el-form-item>
          <el-form-item label="客户扩展码：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.code"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="客户联系人：">
                <el-input
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.customerContactPerson"
                ></el-input>
              </el-form-item> -->
          <el-form-item label="联系人电话：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.contactPersonPhone"
            ></el-input>
          </el-form-item>
        </div>

        <div v-if="formInline.channelOperator == '2'">
          <el-form-item label="联系人身份证号：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.legalIdentification"
            ></el-input>
          </el-form-item>
          <el-form-item label="省份：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.province"
            ></el-input>
          </el-form-item>
          <el-form-item label="城市：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.city"
            ></el-input>
          </el-form-item>
          <el-form-item label="区：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.area"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司详情地址：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.businessAddress"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司介绍：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.introduce"
            ></el-input>
          </el-form-item>
          <el-form-item label="法人姓名：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.legalName"
            ></el-input>
          </el-form-item>
          <el-form-item label="法人身份证号：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.legalIdentification"
            ></el-input>
          </el-form-item>
          <el-form-item label="办公电话：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.workPhone"
            ></el-input>
          </el-form-item>
          <el-form-item label="联系人邮箱：">
            <el-input
              disabled
              show-word-limit
              style="width: 220px"
              v-model="customerList.contactPersonEmail"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司logo：">
            <div style="width: 220px">
              <img
                v-if="customerList.serviceIcon"
                :src="customerList.serviceIcon"
                width="100px"
              />
            </div>
          </el-form-item>

          <el-form-item label="营业执照：">
            <div style="width: 220px">
              <img
                v-if="customerList.businessLicense"
                :src="customerList.businessLicense"
                width="100px"
              />
            </div>
          </el-form-item>
          <el-form-item label="法人身份证正面照：">
            <div style="width: 220px">
              <img
                v-if="customerList.identificationFrontPic"
                :src="customerList.identificationFrontPic"
                width="100px"
              />
            </div>
          </el-form-item>
          <el-form-item label="法人身份证反面照：">
            <div style="width: 220px">
              <img
                v-if="customerList.identificationBackPic"
                :src="customerList.identificationBackPic"
                width="100px"
              />
            </div>
          </el-form-item>
          <el-form-item label="联系人身份证正面照：">
            <div style="width: 220px">
              <img
                v-if="customerList.contactPersonIdentityFron"
                :src="customerList.contactPersonIdentityFron"
                width="100px"
              />
            </div>
          </el-form-item>
          <el-form-item label="联系人身份证反面照：">
            <div style="width: 220px">
              <img
                v-if="customerList.contactPersonIdentityBack"
                :src="customerList.contactPersonIdentityBack"
                width="100px"
              />
            </div>
          </el-form-item>
        </div>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer"> </span>
      </template>
    </el-dialog>
    <el-dialog
      title="信息对比"
      v-model="contrastVisible"
      width="75%"
      :before-close="CloseFn"
    >
      <div>
        <el-form
          :model="customerList"
          :rules="rules"
          :inline="true"
          label-width="180px"
          ref="formInlines"
          class="demo-form-inline"
        >
          <div v-if="oldCustomerList">
            <el-form-item label="用户名称：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.username == customerList.username
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.username"
                ></el-input>
                <el-tooltip
                  v-if="oldCustomerList.username != customerList.username"
                  effect="dark"
                  :content="oldCustomerList.username"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="客户名称：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.customerName == customerList.customerName
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.customerName"
                ></el-input>
                <el-tooltip
                  v-if="
                    oldCustomerList.customerName != customerList.customerName
                  "
                  effect="dark"
                  :content="oldCustomerList.customerName"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <!-- <el-form-item label="客户扩展码：">
                  <div class="formItem">
                    <el-input
                      :class="oldCustomerList.code == customerList.code ? '' : 'te'"
                      disabled
                      show-word-limit
                      style="width: 220px"
                      v-model="customerList.code"
                    ></el-input>
                    <el-tooltip
                      v-if="oldCustomerList.code != customerList.code"
                      effect="dark"
                      :content="oldCustomerList.code"
                      placement="top-start"
                    >
                      <i
                        style="color: #e6a23c; margin: 0 10px"
                        class="el-icon-warning"
                      ></i>
                    </el-tooltip>
                  </div>
                </el-form-item> -->
            <!-- <el-form-item label="客户联系人：">
                  <div class="formItem">
                    <el-input
                      :class="
                        oldCustomerList.customerContactPerson ==
                        customerList.customerContactPerson
                          ? ''
                          : 'te'
                      "
                      disabled
                      show-word-limit
                      style="width: 220px"
                      v-model="customerList.customerContactPerson"
                    ></el-input>
                    <el-tooltip
                      v-if="
                        oldCustomerList.customerContactPerson !=
                        customerList.customerContactPerson
                      "
                      effect="dark"
                      :content="oldCustomerList.customerContactPerson"
                      placement="top-start"
                    >
                      <i
                        style="color: #e6a23c; margin: 0 10px"
                        class="el-icon-warning"
                      ></i>
                    </el-tooltip>
                  </div>
                </el-form-item> -->
            <el-form-item label="联系人电话：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.contactPersonPhone ==
                    customerList.contactPersonPhone
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.contactPersonPhone"
                ></el-input>
                <el-tooltip
                  v-if="
                    oldCustomerList.contactPersonPhone !=
                    customerList.contactPersonPhone
                  "
                  effect="dark"
                  :content="oldCustomerList.contactPersonPhone"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </div>
          <div v-if="updataChannelOperator == '2' && oldCustomerList">
            <el-form-item label="联系人身份证号：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.contactPersonCard ==
                    customerList.contactPersonCard
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.contactPersonCard"
                ></el-input>
                <el-tooltip
                  v-if="
                    oldCustomerList.contactPersonCard !=
                    customerList.contactPersonCard
                  "
                  effect="dark"
                  :content="oldCustomerList.contactPersonCard"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="省份：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.province == customerList.province
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.province"
                ></el-input>
                <el-tooltip
                  v-if="oldCustomerList.province != customerList.province"
                  effect="dark"
                  :content="oldCustomerList.province"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="城市：">
              <div class="formItem">
                <el-input
                  :class="oldCustomerList.city == customerList.city ? '' : 'te'"
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.city"
                ></el-input>
                <el-tooltip
                  v-if="oldCustomerList.city != customerList.city"
                  effect="dark"
                  :content="oldCustomerList.city"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="区：">
              <div class="formItem">
                <el-input
                  :class="oldCustomerList.area == customerList.area ? '' : 'te'"
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.area"
                ></el-input>
                <el-tooltip
                  v-if="oldCustomerList.area != customerList.area"
                  effect="dark"
                  :content="oldCustomerList.area"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="公司详情地址：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.businessAddress ==
                    customerList.businessAddress
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.businessAddress"
                ></el-input>
                <el-tooltip
                  v-if="
                    oldCustomerList.businessAddress !=
                    customerList.businessAddress
                  "
                  effect="dark"
                  :content="oldCustomerList.businessAddress"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="公司介绍：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.introduce == customerList.introduce
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.introduce"
                ></el-input>
                <el-tooltip
                  v-if="oldCustomerList.introduce != customerList.introduce"
                  effect="dark"
                  :content="oldCustomerList.introduce"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="法人姓名：">
              <div class="formItem">
                <el-input
                  :class="
                    legalPerson.legalName == customerList.legalName ? '' : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.legalName"
                ></el-input>
                <el-tooltip
                  v-if="legalPerson.legalName != customerList.legalName"
                  effect="dark"
                  :content="legalPerson.legalName"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="法人身份证号：">
              <div class="formItem">
                <el-input
                  :class="
                    legalPerson.legalIdentification ==
                    customerList.legalIdentification
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.legalIdentification"
                ></el-input>
                <el-tooltip
                  v-if="
                    legalPerson.legalIdentification !=
                    customerList.legalIdentification
                  "
                  effect="dark"
                  :content="legalPerson.legalIdentification"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="办公电话：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.workPhone == customerList.workPhone
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.workPhone"
                ></el-input>
                <el-tooltip
                  v-if="oldCustomerList.workPhone != customerList.workPhone"
                  effect="dark"
                  :content="oldCustomerList.workPhone"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人邮箱：">
              <div class="formItem">
                <el-input
                  :class="
                    oldCustomerList.contactPersonEmail ==
                    customerList.contactPersonEmail
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="customerList.contactPersonEmail"
                ></el-input>
                <el-tooltip
                  v-if="
                    oldCustomerList.contactPersonEmail !=
                    customerList.contactPersonEmail
                  "
                  effect="dark"
                  :content="oldCustomerList.contactPersonEmail"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="公司logo：">
              <div class="formItem">
                <img
                  v-if="customerList.serviceIcon"
                  :src="customerList.serviceIcon"
                  width="100px"
                />
                <el-tooltip
                  v-if="oldCustomerList.serviceIcon != customerList.serviceIcon"
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    <div>
                      <img
                        v-if="oldCustomerList.serviceIcon"
                        :src="oldCustomerList.serviceIcon"
                        width="100px"
                      />
                    </div>
                  </template>
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="营业执照：">
              <div class="formItem">
                <img
                  v-if="customerList.businessLicense"
                  :src="customerList.businessLicense"
                  width="100px"
                />
                <el-tooltip
                  v-if="
                    oldCustomerList.businessLicense !=
                    customerList.businessLicense
                  "
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    <div>
                      <img
                        v-if="oldCustomerList.businessLicense"
                        :src="oldCustomerList.businessLicense"
                        width="100px"
                      />
                    </div>
                  </template>
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="法人身份证正面照：">
              <div class="formItem">
                <img
                  v-if="customerList.identificationFrontPic"
                  :src="customerList.identificationFrontPic"
                  width="100px"
                />
                <el-tooltip
                  v-if="
                    legalPerson.identificationFrontPic !=
                    customerList.identificationFrontPic
                  "
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    <div>
                      <img
                        v-if="legalPerson.identificationFrontPic"
                        :src="legalPerson.identificationFrontPic"
                        width="100px"
                      />
                    </div>
                  </template>
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="法人身份证反面照：">
              <div class="formItem">
                <img
                  v-if="customerList.identificationBackPic"
                  :src="customerList.identificationBackPic"
                  width="100px"
                />
                <el-tooltip
                  v-if="
                    legalPerson.identificationBackPic !=
                    customerList.identificationBackPic
                  "
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    <div>
                      <img
                        v-if="legalPerson.identificationBackPic"
                        :src="legalPerson.identificationBackPic"
                        width="100px"
                      />
                    </div>
                  </template>
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人身份证正面照：">
              <div class="formItem">
                <img
                  v-if="customerList.contactPersonIdentityFron"
                  :src="customerList.contactPersonIdentityFron"
                  width="100px"
                />
                <el-tooltip
                  v-if="
                    oldCustomerList.contactPersonIdentityFron !=
                    customerList.contactPersonIdentityFron
                  "
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    <div>
                      <img
                        v-if="oldCustomerList.contactPersonIdentityFron"
                        :src="oldCustomerList.contactPersonIdentityFron"
                        width="100px"
                      />
                    </div>
                  </template>
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人身份证反面照：">
              <div class="formItem">
                <img
                  v-if="customerList.contactPersonIdentityBack"
                  :src="customerList.contactPersonIdentityBack"
                  width="100px"
                />
                <el-tooltip
                  v-if="
                    oldCustomerList.contactPersonIdentityBack !=
                    customerList.contactPersonIdentityBack
                  "
                  effect="dark"
                  placement="top-start"
                >
                  <template v-slot:content>
                    <div>
                      <img
                        v-if="oldCustomerList.contactPersonIdentityBack"
                        :src="oldCustomerList.contactPersonIdentityBack"
                        width="100px"
                      />
                    </div>
                  </template>
                  <el-icon style="color: #e6a23c; margin: 0 10px"
                    ><WarningFilled
                  /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="contrastVisible = false">取 消</el-button>
          <el-button type="primary" @click="quiteUpdata">更新</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="授权书"
      v-model="contractUrlFlag"
      width="30%"
      :before-close="handleClose"
    >
      <el-image :src="imgUrl" style="width: 100%"></el-image>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="contractUrlFlag = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import industrysCode from "../../../../utils/industrysCode";
import getNoce from "@/plugins/getNoce";
// console.log(getNoce,'getNoce');
export default {
  components: {
    // ElIconWarning,
  },
  name: "Channel5G",
  data() {
    return {
      isFirstEnter:false,
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      id: "",
      fileList: [],
      title: "",
      updataChannelOperator: "",
      updateId: "",
      submitTag: null,
      action: window.path.cpus + "v3/file/uploadFile",
      token: {},
      dialogVisible: false,
      contractUrlFlag: false,
      contrastVisible: false,
      industrys: [],
      ltOption: industrysCode.ltOption,
      dxOption: industrysCode.dxOption,
      searchForm: {
        customerStatus: '',
      },
      // ltOption: [
      //   {
      //     value: "1",
      //     lable: "IT 服务",
      //   },
      //   {
      //     value: "2",
      //     lable: "制造业",
      //   },
      //   {
      //     value: "3",
      //     lable: "批发/零售",
      //   },
      //   {
      //     value: "4",
      //     lable: "生活服务",
      //   },
      //   {
      //     value: "5",
      //     lable: "文化/体育/娱乐业",
      //   },
      //   {
      //     value: "6",
      //     lable: "建筑/房地产",
      //   },
      //   {
      //     value: "7",
      //     lable: "教育",
      //   },
      //   {
      //     value: "8",
      //     lable: "运输/物流/仓储",
      //   },
      //   {
      //     value: "9",
      //     lable: "医疗",
      //   },
      //   {
      //     value: "10",
      //     lable: "政府",
      //   },
      //   {
      //     value: "11",
      //     lable: "金融",
      //   },
      //   {
      //     value: "12",
      //     lable: "能源/采矿",
      //   },
      //   {
      //     value: "13",
      //     lable: "农、林、牧、渔业",
      //   },
      //   {
      //     value: "14",
      //     lable: "电力、热力、燃气及水生产和供应业",
      //   },
      //   {
      //     value: "15",
      //     lable: "计算机软件/硬件/信息服务",
      //   },
      //   {
      //     value: "16",
      //     lable: "互联网和相关服务",
      //   },
      //   {
      //     value: "17",
      //     lable: "机械/电子",
      //   },
      //   {
      //     value: "18",
      //     lable: "服装/纺织",
      //   },
      //   {
      //     value: "19",
      //     lable: "汽车",
      //   },
      //   {
      //     value: "20",
      //     lable: "金属制品",
      //   },
      //   {
      //     value: "21",
      //     lable: "食品/饮料",
      //   },
      //   {
      //     value: "22",
      //     lable: "家具/家纺",
      //   },
      //   {
      //     value: "23",
      //     lable: "重工制造",
      //   },
      //   {
      //     value: "24",
      //     lable: "家电/数码",
      //   },
      //   {
      //     value: "25",
      //     lable: "橡胶/塑料",
      //   },
      //   {
      //     value: "26",
      //     lable: "日用品/化妆品",
      //   },
      //   {
      //     value: "27",
      //     lable: "其他行业",
      //   },
      // ],
      customerList: {},
      newCustomerList: {},
      oldCustomerList: {},
      legalPerson: {},
      editChannelId: "",
      formInline: {
        id: "",
        // channelId: "",
        // channelName: "",
        channelOperator: "",
        // customerId: "",
        // channelExtCode: "", //通道扩展码
        // belongAgentName: "",
        // belongAgentCode: "", //代理商编码
        // belongRegionCode: "", //归属区域编码
        // channelIds: [], //通道
        channel5GList: [], //通道
        contractCode: "", //合同编号
        contractInvalidDate: "", //合同失效日期YYYY-MM-DD
        contractName: "", //合同名称
        contractRenewDate: "", //合同续签日期YYYY-MM-DD,自动续签为是时必填
        contractValidDate: "", //合同生效日期YYYY-MM-DD
        contractUrl: "", //附件url
        industryTypeCode: "", //行业类型编码
        renewed: "", //合同是否自动续签：1：是、0：否
      },
      rules: {
        channelId: [
          { required: true, message: "请选择通道", trigger: "change" },
        ],
        // belongAgentName: [
        //   { required: true, message: "代理商名称不可为空", trigger: "change" },
        // ],
        // belongAgentCode: [
        //   { required: true, message: "代理商编码不可为空", trigger: "change" },
        // ],
        // belongRegionCode: [
        //   {
        //     required: true,
        //     message: "归属区域编码不可为空",
        //     trigger: "change",
        //   },
        // ],
        channelOperator: [
          { required: true, message: "请选择运营商", trigger: "change" },
        ],
        industryTypeCode: [
          { required: true, message: "请选择行业类型编码", trigger: "change" },
        ],
        contractName: [
          { required: true, message: "合同名称不可为空", trigger: "change" },
        ],
        contractCode: [
          { required: true, message: "合同编号不可为空", trigger: "change" },
        ],
        contractInvalidDate: [
          {
            required: true,
            message: "合同失效日期不可为空",
            trigger: "change",
          },
        ],
        contractRenewDate: [
          {
            required: true,
            message: "合同续签日期Y不可为空",
            trigger: "change",
          },
        ],
        renewed: [
          {
            required: true,
            message: "请选择合同是否自动续签",
            trigger: "change",
          },
        ],
        contractValidDate: [
          {
            required: true,
            message: "合同生效日期不可为空",
            trigger: "change",
          },
        ],
        contractUrl: [
          { required: true, message: "请上传电子合同附件", trigger: "change" },
        ],
      },
      channel: [],
      channelList: [],
      selectedChannelsInfo: [], // 存储选择的通道信息数组
      tableDataObj: {
        tableData: [],
        loading2: false,
      },
      imgUrl: "",
      username: "",
    };
  },
  created() {
    this.isFirstEnter = true
    this.id = this.$route.query.customerId;
    this.username = this.$route.query.username;
    this.customerList = JSON.parse(localStorage.getItem("customerList"));
    if (this.customerList.contactPersonIdentity) {
      let list = this.customerList.contactPersonIdentity.split(",");
      this.customerList.contactPersonIdentityFron = list[0];
      this.customerList.contactPersonIdentityBack = list[1];
    }
    this.getList();
    this.channel5GList();
    this.getIndustrys();
    this.token = {
      Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
    };
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.id = this.$route.query.customerId;
        this.username = this.$route.query.username;
        this.customerList = JSON.parse(localStorage.getItem("customerList"));
        this.getList();
        this.channel5GList();
        this.getIndustrys();
        this.token = {
          Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
        };
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  methods: {
    goBack() {
      this.$router.push({ path: "/5gAuditCertification" });
    },
    search() {
      this.getList();
    },
    getList() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.fiveWeb +
          "/operator/customer/customerChannelList",
          {
            customerId: this.id,
            customerStatus: this.searchForm.customerStatus,
          },
        {},
        (res) => {
          this.tableDataObj.tableData = res.data;
          this.tableDataObj.loading2 = false;
          // console.log(res, "res");
        }
      );
    },
    /**获取通道*/
    channel5GList() {
      window.api.get(
        window.path.omcs + "/operatingchannelinfo/channel5GList",
        {},
        (res) => {
          this.channelList = res.data;
        }
      );
    },
    /**获取行业类别列表*/
    getIndustrys() {
      window.api.get(
        window.path.fiveWeb + "/industry/secondIndustry",
        {},
        (res) => {
          this.industrys = res.data;
        }
      );
    },
    time1(val) {
      this.formInline.contractValidDate = moment(
        this.formInline.contractValidDate
      ).format("YYYY-MM-DD");
    },
    time2(val) {
      this.formInline.contractInvalidDate = moment(
        this.formInline.contractInvalidDate
      ).format("YYYY-MM-DD");
    },
    time3(val) {
      this.formInline.contractRenewDate = moment(
        this.formInline.contractRenewDate
      ).format("YYYY-MM-DD");
    },
    checkImg(url) {
      this.imgUrl = url;
      this.contractUrlFlag = true;
    },
    handleOperatorChange(val) {
      if (val == "1") {
        this.channel = this.channelList.filter((item) => item.operatorsType == "1");
        this.formInline.industryTypeCode = "";
      } else if (val == "2") {
        this.channel = this.channelList.filter((item) => item.operatorsType == "2");
        this.formInline.industryTypeCode = "1";
      } else {
        this.channel = this.channelList.filter((item) => item.operatorsType == "3");
        this.formInline.industryTypeCode = "";
      }
      this.formInline.channel5GList = []
      // this.formInline.channelId = "";
      // this.formInline.channelName = "";
      // this.formInline.channelExtCode = "";
    },
    handChannel(selectedChannelCodes) {
      console.log(selectedChannelCodes, 'selectedChannelCodes');

      // 根据选择的多个channelCode在channel数组中匹配并组合成新数组
      const selectedChannels = this.getSelectedChannelsInfo(selectedChannelCodes);
      console.log(selectedChannels, 'selectedChannels');

      // 将选择的通道信息存储到data中，供其他地方使用
      this.formInline.channel5GList = selectedChannels;

      // // 如果只选择了一个通道，设置相关的单个通道信息
      // if (selectedChannelCodes && selectedChannelCodes.length === 1) {
      //   const channelInfo = selectedChannels[0];
      //   if (channelInfo) {
      //     this.formInline.channelName = channelInfo.channelName;
      //     this.formInline.channelExtCode = channelInfo.channelExtCode;
      //     this.formInline.channelOperator = channelInfo.operatorsType;

      //     // 根据运营商类型设置行业类型编码
      //     if (this.formInline.channelOperator == "2") {
      //       this.formInline.industryTypeCode = "1";
      //     } else {
      //       this.formInline.industryTypeCode = "";
      //     }
      //   }
      // } else if (selectedChannelCodes && selectedChannelCodes.length > 1) {
      //   // 多选时清空单个通道的相关信息，因为可能有多个不同的值
      //   this.formInline.channelName = "";
      //   this.formInline.channelExtCode = "";
      //   // 可以根据需要设置多选时的处理逻辑
      //   console.log('选择了多个通道:', this.selectedChannelsInfo);
      // } else {
      //   // 未选择时清空相关信息
      //   this.formInline.channelName = "";
      //   this.formInline.channelExtCode = "";
      //   this.formInline.channelOperator = "";
      //   this.formInline.industryTypeCode = "";
      // }
    },

    // 根据选择的channelCode数组获取对应的通道信息
    getSelectedChannelsInfo(selectedChannelCodes) {
      if (!selectedChannelCodes || !Array.isArray(selectedChannelCodes)) {
        return [];
      }

      // 在channel数组中匹配选择的channelCode，组合成新数组
      const selectedChannels = selectedChannelCodes.map(channelCode => {
        const channelInfo = this.channelList.find(item => item.channelCode === channelCode);
        if (channelInfo) {
          return {
            channelName: channelInfo.channelName,
            channelId: channelInfo.channelCode, // 使用channelCode作为channelId
            channelExtCode: channelInfo.spPortExtend,
          };
        }
        return null;
      }).filter(item => item !== null); // 过滤掉null值

      return selectedChannels;
    },
    addChannel() {
      this.title = "添加通道资料";
      this.submitTag = "1";
      this.dialogVisible = true;
    },
    refresh() {
      this.getList();
    },
    handleClose() {
      this.dialogVisible = false;
      this.contractUrlFlag = false;
    },
    submitChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formInline.customerId = this.$route.query.customerId;
          const formData = {
            customerId: this.$route.query.customerId,
            channel5GList: this.formInline.channel5GList,
            channelOperator: this.formInline.channelOperator,
            industryTypeCode: this.formInline.industryTypeCode,
            contractCode: this.formInline.contractCode,
            contractName: this.formInline.contractName,
            contractRenewDate: this.formInline.contractRenewDate,
            contractValidDate: this.formInline.contractValidDate,
            contractInvalidDate: this.formInline.contractInvalidDate,
            contractUrl: this.formInline.contractUrl,
            renewed: this.formInline.renewed,
          }
          if (this.submitTag == 1) {
            window.api.post(
              window.path.fiveWeb + "/operator/customer/customerAddChannel",
              formData,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.dialogVisible = false;
                  this.getList();
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
                //   this.channel = res.data;
              }
            );
          } else {
            formData.id = this.formInline.id;
            window.api.post(
              window.path.fiveWeb + "/operator/customer/customerUpdateChannel",
              formData,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.dialogVisible = false;
                  this.getList();
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
                //   this.channel = res.data;
              }
            );
          }
        }
      });
    },
    //修改通道
    declareChannel(row) {
      this.title = "修改通道资料";
      this.formInline.id = row.id;
      this.formInline.channelId = row.channelId;
      this.formInline.channelName = row.channelName;
      this.formInline.channelOperator = row.channelOperator;
      this.formInline.customerId = row.customerId;
      // this.formInline.belongAgentName = row.belongAgentName;
      // this.formInline.belongAgentCode = row.belongAgentCode;
      // this.formInline.belongRegionCode = row.belongRegionCode;
      this.formInline.contractCode = row.contractCode;
      this.formInline.contractInvalidDate = row.contractInvalidDate;
      this.formInline.contractName = row.contractName;
      this.formInline.contractRenewDate = row.contractRenewDate;
      this.formInline.contractValidDate = row.contractValidDate;
      this.formInline.contractUrl = row.contractUrl;
      this.formInline.industryTypeCode = row.industryTypeCode;
      // if (this.formInline.channelOperator == "1") {
      //   this.formInline.industryTypeCode = row.industryTypeCode ;
      // } else {
      //   this.formInline.industryTypeCode = row.industryTypeCode;
      // }
      this.formInline.renewed = row.renewed;
      this.fileList.push({ url: row.contractUrl });
      this.dialogVisible = true;
      this.submitTag = "2";
    },
    //报备通道
    handleReport(row) {
      this.$confirms.confirmation(
        "get",
        "报备通道",
        window.path.fiveWeb + "/operator/customer/declareChannel/" + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: "success",
            // });
            this.getList();
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: "error",
            // });
          }
        }
      );
    },
    //更新客户信息
    quiteUpdata() {
      this.$confirms.confirmation(
        "get",
        "更新客户通道信息",
        window.path.fiveWeb +
          "/operator/customer/updateCustomerInfo/" +
          this.updateId,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: "success",
            // });
            this.getList();
            this.contrastVisible = false;
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: "error",
            // });
          }
        }
      );
    },
    handupload(row) {
      this.contrastVisible = true;
      this.updataChannelOperator = row.channelOperator;
      this.updateId = row.id;
      if (row.snapshot) {
        this.newCustomerList = JSON.parse(row.snapshot);
        let { customer, legalPerson } = this.newCustomerList;
        this.oldCustomerList = customer;
        this.legalPerson = legalPerson;
        if (customer.contactPersonIdentity) {
          let list = customer.contactPersonIdentity.split(",");
          this.oldCustomerList.contactPersonIdentityFron = list[0];
          this.oldCustomerList.contactPersonIdentityBack = list[1];
        }
      }
    },
    CloseFn() {
      this.contrastVisible = false;
    },
    //分配服务代码
    serviceCode(row) {
      this.$confirms.confirmation(
        "post",
        "分配服务代码",
        window.path.fiveWeb + "/operator/customer/allocate",
        {
          id: row.id,
        },
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: "success",
            // });
            // this.getList();
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: "error",
            // });
          }
        }
      );
    },
    beforeAvatarUpload(file) {
      // console.log(file,'file');
      const siJPGGIF = file.name.split(".")[1];
      const isLt5M = file.size / 1024 / 1024 < 10;
      if (
        siJPGGIF != "jpg" &&
        siJPGGIF != "jpeg" &&
        siJPGGIF != "gif" &&
        siJPGGIF != "pdf" &&
        siJPGGIF != "doc" &&
        siJPGGIF != "docx" &&
        siJPGGIF != "rar"
      ) {
        this.$message.warning(
          "电子合同附件上传只能是 jpg、jpeg、gif 、pdf、doc、docx、rar格式!"
        );
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传文件大小不能超过 10MB!");
        return false;
      }

      // return isJPG && isLt2M;
    },
    handleRemove(file, fileList) {
      this.formInline.contractUrl = "";
    },
    handleSuccess(res, fileList) {
      this.formInline.contractUrl = res.data;
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        // console.log(111);
        this.$refs["formInlines"].resetFields();
        this.formInline.channelId = "";
        this.formInline.id = "";
        this.formInline.channelName = "";
        this.formInline.channelOperator = "";
        this.formInline.channelCode = "";
        this.formInline.contractUrl = "";
        this.formInline.channelExtCode = "";
        // this.formInline.belongAgentName = "";
        // this.formInline.belongAgentCode = "";
        // this.formInline.belongRegionCode = "";
        this.formInline.contractCode = "";
        this.formInline.contractInvalidDate = "";
        this.formInline.contractName = "";
        this.formInline.contractRenewDate = "";
        this.formInline.contractValidDate = "";
        this.formInline.contractUrl = "";
        this.formInline.industryTypeCode = "";
        this.formInline.renewed = "";
        this.fileList = [];
        this.channel = [];
        // debugger

        // console.log(this.formInline, "this.formInline");
      }
    },
    contractUrlFlag(val) {
      if (!val) {
        this.imgUrl = "";
      }
    },
  },
};
</script>

<style scoped>
.solid {
  width: 100%;
  height: 1px;
  border-bottom: 1px dashed #ccc;
  margin: 10px 0;
}
.te :deep(.el-input__inner) {
  color: red;
}
.formItem {
  width: 260px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>