<template>
  <div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="通道号" label-width="82px" prop="channelId">
            <el-input
              v-model="formInline.channelId"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="运营商" label-width="80px" prop="operator">
            <el-select
              v-model="formInline.operator"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
        <!-- <el-button type="primary" plain @click="export1()">导出</el-button> -->
      </div>
      <div class="Signature-search-fun" style="margin-bottom: 10px">
        <!-- <span class="Signature-list-header">携号转网补发列表</span> -->
        <el-button type="primary" style="margin-left: 15px" @click="addopt"
          >新增</el-button
        >
        <el-button
          type="danger"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delAll"
          >批量删除</el-button
        >
        <el-button
          type="primary"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="edit"
          >批量修改通道</el-button
        >
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
          @save="save"
        ></table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增运营商 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="560px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="140px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="通道号码" prop="channelId">
            <el-input
              v-model="formop.channelId"
              autocomplete="off"
              placeholder=""
              :disabled="disabledS"
            ></el-input>
          </el-form-item>
          <el-form-item label="携号转网失败代码" prop="reissueCode">
            <el-input
              v-model="formop.reissueCode"
              type="textarea"
              :rows="3"
              autocomplete="off"
              placeholder=""
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="不携号转网失败代码" prop="ignoreCode">
                <el-input v-model="formop.ignoreCode" type='textarea' :rows='3' autocomplete="off" placeholder=""></el-input>
              </el-form-item> -->
          <el-form-item label="移动补发通道：" prop="ydChannel">
            <el-select
              v-model="formop.ydChannel"
              clearable
              placeholder="不限"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in ydChannel"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联通补发通道：" prop="ltChannel">
            <el-select
              v-model="formop.ltChannel"
              clearable
              placeholder="不限"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in ltChannel"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="电信补发通道：" prop="dxChannel">
            <el-select
              v-model="formop.dxChannel"
              clearable
              placeholder="不限"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in dxChannel"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增运营商结束 -->
      <!-- 通道编辑 -->
      <el-dialog
        title="批量编辑通道"
        v-model="dialogFormVisibleChannel"
        :close-on-click-modal="false"
        width="560px"
      >
        <el-form
          :model="formopChannel"
          ref="formopChannel"
          label-width="140px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="移动补发通道：" prop="ydChannel">
            <el-select
              v-model="formopChannel.ydChannel"
              clearable
              placeholder="不限"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in ydChannel"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联通补发通道：" prop="ltChannel">
            <el-select
              v-model="formopChannel.ltChannel"
              clearable
              placeholder="不限"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in ltChannel"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="电信补发通道：" prop="dxChannel">
            <el-select
              v-model="formopChannel.dxChannel"
              clearable
              placeholder="不限"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in dxChannel"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button
              type="primary"
              @click="submitFormChannel('formopChannel')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisibleChannel = false"
              >取 消</el-button
            >
          </div>
        </template>
      </el-dialog>
      <!-- 通道编辑结束 -->
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '../../../../publicComponents/ChannelView.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  name: 'changePhone',
  components: {
    TableTem,
    ChannelView,
  },
  data() {
    return {
      isFirstEnter: false,
      ChannelData: '', //传递通道值
      titleMap: {
        add: '添加携号转网补发代码',
        edit: '编辑携号转网补发代码',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      dialogFormVisibleChannel: false,
      id: '', //失败代码补发id
      disabledS: false,
      ltChannel: [],
      dxChannel: [],
      ydChannel: [],
      tableRow: '', //当前行列表数据
      selectId: '',
      formop: {
        //表单数据
        channelId: '',
        // ignoreCode: "",
        reissueCode: '',
        ltChannel: '',
        ydChannel: '',
        dxChannel: '',
      },
      formopChannel: {
        ltChannel: '',
        ydChannel: '',
        dxChannel: '',
      },
      rules: {
        //验证规则
        ydChannel: [
          { required: true, message: '请选择移动通道', trigger: 'blur' },
        ],
        ltChannel: [
          { required: true, message: '请选择联通通道', trigger: 'blur' },
        ],
        dxChannel: [
          { required: true, message: '请选择电信通道', trigger: 'blur' },
        ],
        channelId: [
          { required: true, message: '请输入通道号', trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '长度在 1 到 50 个字符',
            trigger: ['blur', 'change'],
          },
          // {
          //   pattern: /^[\u4e00-\u9fa5]+$/,
          //   message: "含有非法字符（只能输入汉字！）"
          // }
        ],
        failureCode: [
          { required: true, message: '请输入失败代码', trigger: 'blur' },
          {
            // min: 1,
            // max: 50,
            // message: "长度在 1 到 50 个字符",
            trigger: ['blur', 'change'],
          },
          // {
          //   pattern: /^[\u4e00-\u9fa5]+$/,
          //   message: "含有非法字符（只能输入汉字！）"
          // }
        ],
      },
      tabelAlllist: {
        //存储查询数据
        channelId: '',
        operator: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      formInline: {
        //查询数据
        channelId: '',
        operator: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        currentPage: '1',
        pageSize: '10',
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'id',
            showName: 'ID',
            fixed: false,
            width: 50,
          },
          // {
          //   prop: "ignoreCode",
          //   showName: "不携号转网的失败代码",
          //   fixed: false,
          //   width:150
          // },
          {
            prop: 'reissueCode',
            showName: '需要携号转网的失败代码',
            fixed: false,
            width: 200,
          },
          {
            prop: 'channelId',
            showName: '通道号',
            fixed: false,
            Channel: true,
          },
          {
            prop: 'operator',
            showName: '所属运营商',
            fixed: false,
            formatData: function (val) {
              if (val == '1') {
                return (val = '移动')
              } else if (val == '2') {
                return (val = '联通')
              } else if (val == '3') {
                return (val = '电信')
              } else if (val == '4') {
                return (val = '未知')
              }
            },
          },
          {
            prop: 'ydChannel',
            showName: '移动补发通道',
            Channel: true,
            fixed: false,
          },
          {
            prop: 'ltChannel',
            showName: '联通补发通道',
            Channel: true,
            fixed: false,
          },
          {
            prop: 'dxChannel',
            showName: '电信补发通道',
            Channel: true,
            fixed: false,
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
            width: 140,
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-edit',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'el-icon-delete',
            color: '#f56c6c',
          },
        ],
      },
      pldel: false,
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingNumberPortability/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    getallchannel() {
      window.api.get(
        window.path.omcs + '/operatingchannelinfo/channels/1',
        {},
        (res) => {
          this.ydChannel = res
        }
      )
      window.api.get(
        window.path.omcs + '/operatingchannelinfo/channels/2',
        {},
        (res) => {
          this.ltChannel = res
        }
      )
      window.api.get(
        window.path.omcs + '/operatingchannelinfo/channels/3',
        {},
        (res) => {
          this.dxChannel = res
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    Query() {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
      }
      this.selectId = selectId.join(',')
    },
    Reload() {
      //重置
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //导出 （发送记录）
    export1() {
      let aa = {}
      Object.assign(aa, this.tabelAlllist)
      // aa.flag = this.flag;
      // aa.sendBeginTime = this.Times.beginTime;
      // aa.sendEndTime = this.Times.endTime;
      // aa.isDownload = 1;
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingRechargeConsumer/export',
          aa,
          '充值记录报表.xlsx'
        )
      }
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //验证唯一性
            // window.api.get(window.path.omcs+'v3/operatingusercodereissue/checkUserName/'+this.formop.userName,{},res=>{
            //     if (res.code!=200) {
            //         this.$message({
            //             message: res.msg,
            //             type: 'warning'
            //         });
            //     }else{

            //添加
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingNumberPortability',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              }
            )

            // }
            // })
          } else {
            //编辑
            this.formop.id = this.id
            //验证唯一
            // if(this.formop.userName == this.tableRow.userName ){
            //     this.$confirms.confirmation(
            //         "put",
            //         "确认执行此操作吗？",
            //         window.path.omcs+"v3/operatingusercodereissue",
            //         this.formop,
            //         () => {
            //             this.dialogFormVisible = false;
            //             this.gettableLIst();
            //         }
            //     );
            // }else{

            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingNumberPortability',
              this.formop,
              () => {
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            )

            // }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 批量编辑通道提交
    submitFormChannel() {
      this.formopChannel.batchId = this.selectId
      this.$confirms.confirmation(
        'put',
        '确认执行此操作吗？',
        window.path.omcs + '/operatingNumberPortability/batch',
        this.formopChannel,
        () => {
          this.dialogFormVisibleChannel = false
          this.gettableLIst()
        }
      )
    },
    addopt() {
      //添加
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
      this.disabledS = false
    },
    delAll() {
      // window.api.get(
      //   window.path.omcs+"operatingindustrycategories/checkNoteInCodeCount/" +
      //     this.selectId,
      //   {},
      //   res => {
      //     if (res.code == 400) {
      //       this.$message({
      //         message: "行业类别已被使用，不可以删除！",
      //         type: "warning"
      //       });
      //     } else {
      //批量删除

      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingNumberPortability/' + this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )

      //     }
      //   }
      // );
    },
    detailsRow(val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.disabledS = true
      this.id = val.row.id
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val.row)
      })
    },
    //删除
    delRow: function (val) {
      //删除验证
      // window.api.get(window.path.omcs+'operatingoperatorinfo/checkChannelIsUsed/'+val.row.id,
      // {},
      // res=>{
      //     if (res.code==400) {
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }else{

      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatingNumberPortability/' + val.row.id,
        {},
        () => {
          this.gettableLIst()
        }
      )

      //     }
      // })
    },
    // 批量编辑
    edit() {
      this.dialogFormVisibleChannel = true
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  mounted() {
    this.getallchannel()
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  watch: {
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
    dialogFormVisibleChannel(val) {
      if (val == false) {
        this.$refs.formopChannel.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.sensitive {
}
</style>
