<template>
  <div>
    <div class="Top_title">今日失败代码统计</div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item
              label="失败代码"
              label-width="80px"
              prop="failureCode"
            >
              <el-input
                v-model="formInline.failureCode"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道ID" label-width="80px" prop="channelId">
              <el-input
                v-model="formInline.channelId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="时间筛选" label-width="80px" prop="channelId">
              <el-date-picker
                v-model="formInline.time"
                type="datetimerange"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                range-separator="至"
                @change="timeClick"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>
        <div class="sensitive-fun">
          <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">失败代码统计列表 <span style="color:red;">(失败代码仅统计今天的数据，新增的失败代码会在列表中自动增加一条数据，10分钟更新一次）</span></span> -->
        </div>
        <div class="Mail-table" style="padding-bottom: 40px; margin-top: 10px">
          <!-- 表格和分页开始 -->
          <table-tem
            :tableDataObj="tableDataObj"
            @handelOptionButton="handelOptionButton"
            @handelSelection="handelSelection"
          ></table-tem>
          <!--分页-->
          <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            >
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[50, 100, 300]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              >
              </el-pagination>
            </el-col>
          </template>
          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
export default {
  name: 'FailcodeSend',
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      rules: {
        aisle: [{ required: true, message: '通道不能为空', trigger: 'change' }],
        ChannelSelection: [
          { required: true, message: '通道组不能为空', trigger: 'change' },
        ],
      },
      formAdd: {
        ChannelSelection: '',
        // 选择通道类型
        aisle: '',
      },
      roleDialog: '',
      // 查询列表数据
      formInline: {
        failureCode: '',
        channelId: '',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 50,
        currentPage: 1,
      },
      //储存查询列表数据
      formInlines: {
        failureCode: '',
        channelId: '',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 50,
        currentPage: 1,
      },
      // 存储二次弹出框状态
      StatusT: true,
      // 存储通道
      options: [],
      // 存储iD
      valueReissueId: '',
      addCharacterPopUps: false,
      //角色列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'failureCode',
            showName: '失败代码名称',
            fixed: false,
          },
          {
            prop: 'channelId',
            showName: '通道ID',
            fixed: false,
          },
          {
            prop: 'sum',
            showName: '发生次数',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          height: 600, //是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '80', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'failurecodestatisticsday/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    //日期选择
    timeClick(val) {
      this.formInline.beginTime = this.moment(val[0]).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      this.formInline.endTime = this.moment(val[1]).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      // console.log(this.moment(val[0]).format("YYYY-MM-DD hh:mm:ss"))
      // console.log(this.moment(val[1]).format("YYYY-MM-DD hh:mm:ss"))
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      Object.assign(this.formInlines, this.formInline)
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsInfoId)
      }
      this.valueReissueId = selectId.join(',')
    },
    // 补发
    Reissue(val) {
      if (val == '条件补发' && !this.formInline.clientName) {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '条件补发需先填写用户名',
        })
      } else {
        this.roleDialog = val
        this.addCharacterPopUps = true
      }
    },
    // 关闭弹出层
    handleClose1(done) {
      this.addCharacterPopUps = false
    },
    // 提交
    RoleADD() {
      this.$refs['formAdd'].validate((valid) => {
        if (valid) {
          if (this.roleDialog == '短信补发') {
            if (this.formAdd.aisle == '0') {
              this.formAdd.ChannelSelection = '0'
            }
            this.$confirm('确定短信补发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.StatusT = false
                window.api.post(
                  window.path.omcs +
                    'smsreplacement/batchreplacement?ids=' +
                    this.valueReissueId +
                    '&channelGroupId=' +
                    this.formAdd.ChannelSelection,
                  {},
                  (res) => {
                    this.StatusT = true
                    this.addCharacterPopUps = false
                    this.InquireList()
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除',
                })
              })
            // this.$confirms.confirmation('post','确定短信补发?',window.path.omcs+'smsreplacement/batchreplacement?ids='+this.valueReissueId+"&channelGroupId="+this.formAdd.ChannelSelection,{},res =>{
            //     this.addCharacterPopUps=false
            //     this.InquireList();
            // })
          } else if (this.roleDialog == '条件补发') {
            let objform = {}
            Object.assign(objform, this.formInline)
            if (this.formAdd.aisle == '0') {
              this.formAdd.ChannelSelection = '0'
            }
            objform.pageSize = 5000
            objform.channelGroupId = this.formAdd.ChannelSelection + ''

            this.$confirm('确定以当前条件补发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.StatusT = false
                window.api.post(
                  window.path.omcs + 'smsreplacement/conditionsreplacement',
                  objform,
                  (res) => {
                    this.StatusT = true
                    this.addCharacterPopUps = false
                    this.InquireList()
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除',
                })
              })
            // this.$confirms.confirmation('post','确定以当前条件补发?',window.path.omcs+'smsreplacement/conditionsreplacement',objform
            // ,res =>{
            //     this.addCharacterPopUps=false
            //     // this.InquireList();
            // })
          }
        } else {
          return false
        }
      })
    },
    // 用户操作
    handelOptionButton: function (val) {
      if (val.methods == 'Reissue') {
        this.roleDialog = '短信补发'
        this.valueReissueId = val.row.smsInfoId
        this.addCharacterPopUps = true
      }
    },
    // 发送时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.sendBeginTime = val[0] + ' 00:00:00'
        this.formInline.sendEndTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.sendBeginTime =
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00'
        this.formInline.sendEndTime =
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59'
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
  },
  created() {
    window.api.get(
      window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
      {},
      (res) => {
        this.options = res.data
      }
    )
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps: function (val) {
      if (val == false) {
        this.valueReissueId = ''
        this.formAdd.ChannelSelection = ''
        this.formAdd.aisle = ''
        this.InquireList()
        this.$refs.formAdd.resetFields() //清空表单
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
