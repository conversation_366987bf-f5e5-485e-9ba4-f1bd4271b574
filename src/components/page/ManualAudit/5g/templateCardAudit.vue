<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form
        label-width="80px"
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="模板名称" prop="templateName">
          <el-input class="input-w" v-model="ruleForm.templateName"></el-input>
        </el-form-item>
        <el-form-item label="模板ID" prop="templateId">
          <el-input class="input-w" v-model="ruleForm.templateId"></el-input>
        </el-form-item>
        <el-form-item label="chatbotId" prop="chatbotId">
          <el-input class="input-w" v-model="ruleForm.chatbotId"></el-input>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="templateCardAudit"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="用户名称" title="用户名称" width="140">
          <template v-slot="scope">
            <div>
              {{ scope.row.username }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="chatbotId" title="chatbotId" width="200">
          <template v-slot="scope">
            <div>
              <!-- <Tooltip v-if="scope.row.chatbotId" :content="scope.row.chatbotId" className="wrapper-text" effect="light">
                  </Tooltip> -->
              {{ scope.row.chatbotId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.chatbotId,$event )"></i> -->
              <CopyTemp :content="scope.row.chatbotId" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="chatbot名称" title="chatbot名称" width="140">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.chatbotName"
              :content="scope.row.chatbotName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- {{ scope.row.chatbotName }} -->
          </template>
        </vxe-column>
        <vxe-column field="模板名称" title="模板名称" width="140">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.chatbotName"
              :content="scope.row.chatbotName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- {{ scope.row.templateName }} -->
          </template>
        </vxe-column>
        <vxe-column field="模板ID" title="模板ID" width="200">
          <template v-slot="scope">
            <!-- <Tooltip v-if="scope.row.templateId" :content="scope.row.templateId" className="wrapper-text" effect="light">
                </Tooltip> -->
            {{ scope.row.templateId }}
            <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.templateId,$event )"></i> -->
            <CopyTemp :content="scope.row.templateId" />
          </template>
        </vxe-column>
        <vxe-column field="模板类型" title="模板类型" width="100">
          <template v-slot="scope">
            <div>
              <span v-if="scope.row.templateType == 'card'">卡片</span>
              <span v-if="scope.row.templateType == 'text'">文本</span>
              <span v-if="scope.row.templateType == 'media'">多媒体</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="阅信链接" title="阅信链接" width="140">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.rscContent"
              :content="scope.row.rscContent"
              typeLable="a"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <a
                  :href="'https://' + scope.row.rscContent"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                {{scope.row.rscContent}}
                </a> -->
          </template>
        </vxe-column>
        <vxe-column field="AIM链接" title="AIM链接" width="140">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.shortLink"
              :content="scope.row.shortLink"
              typeLable="a"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- <a
                  :href="'https://' + scope.row.shortLink"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                {{scope.row.shortLink}}
                </a> -->
          </template>
        </vxe-column>
        <vxe-column field="审核状态" title="审核状态" width="120">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f6f6f6;
                color: #999999;
                border: 1px solid #dddddd;
              "
              v-if="scope.row.status == 0"
            >
              未审核
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.status == 5"
            >
              审核中
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.status == 10"
            >
              运营商审核中
            </div>
            <div
              class="item_status"
              style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              "
              v-if="scope.row.status == 20"
            >
              审核通过
            </div>
            <div
              class="item_status"
              style="
                background: #ffe7e7;
                color: #ff6666;
                border: 1px solid #fac2c2;
              "
              v-if="scope.row.status == 30"
            >
              审核失败
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time">{{
                  moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss")
                }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" min-width="200" fixed="right">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.status == 20"
              link
              style="color: #16a589; margin-left: 0px"
              @click="checkContent(scope.row, '1')"
              ><el-icon><SuccessFilled /></el-icon> 查看模板</el-button
            >
            <el-button
              v-else
              link
              style="color: #e6a23c; margin-left: 0px"
              @click="checkContent(scope.row, '2')"
              ><el-icon><WarningFilled /></el-icon> 模板审核</el-button
            >
            <el-button
              link
              style="margin-left: 10px; color: #409eff;"
              @click="editLink(scope.row)"
              ><el-icon><EditPen /></el-icon> 修改AIM链接</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>
      <div class="paginationBox">
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :title="'模板审核' + '-' + title"
      v-model="dialogVisible"
      width="60%"
      :before-close="handleClose"
    >
      <div v-if="detail.templateType == 'card'">
        <Card :list="detail" />
      </div>
      <div v-if="detail.templateType == 'media'">
        <Media :list="detail" />
      </div>
      <div v-if="detail.templateType == 'text'">
        <Texts :list="detail" />
      </div>
      <div
        style="display: flex; margin: 10px 0"
        v-if="detail.status == 0 || detail.status == 5"
      >
        <!-- <span style="width: 80px;display: inline-block;margin: 10px 0;">审核原因：</span>
            <el-input
              :maxlength="20"
              show-word-limit
              type="textarea"
              placeholder="不能超过20个字符"
              style="width: 500px"
              v-model="reason"
            ></el-input> -->
        <el-form
          :model="reasonFrom"
          :inline="true"
          :rules="rules"
          ref="reasonFrom"
          class="demo-reasonFrom"
        >
          <el-form-item label="审核原因：" prop="reason">
            <el-input
              :maxlength="20"
              show-word-limit
              type="textarea"
              placeholder="不能超过20个字符"
              style="width: 500px"
              v-model="reasonFrom.reason"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div
        v-else
        style="
          width: 100%;
          background: #f2f5fa;
          line-height: 30px;
          display: flex;
          margin-top: 10px;
        "
      >
        <div style="padding: 5px">
          <div>
            <span>审核状态：</span>
            <span v-if="detail.status == 0">未审核</span>
            <span style="color: red" v-if="detail.status == 5">审核中</span>
            <span style="color: red" v-if="detail.status == 10"
              >运营商审核中</span
            >
            <span style="color: #409eff" v-if="detail.status == 20"
              >审核通过</span
            >
            <span style="color: red" v-if="detail.status == 30">审核失败</span>
          </div>
          <div>
            <span>审核人：</span>
            <span>{{ detail.auditUser }}</span>
          </div>
        </div>
        <div style="padding: 5px; margin-left: 30px">
          <div>
            <span>审核原因：</span>
            <span>{{ detail.auditReason }}</span>
          </div>
          <div>
            <span>审核时间：</span>
            <span v-if="detail.auditTime">{{
              moment(detail.auditTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            v-if="
              detail.status == 0 ||
              (detail.status == 5 && detail.templateType != 'text')
            "
            type="primary"
            @click="audit(detail, 1, '通过', 'reasonFrom')"
          >
            提交运营商</el-button
          >
          <el-button
            v-if="
              detail.status == 0 ||
              (detail.status == 5 && detail.templateType == 'text')
            "
            type="primary"
            @click="audit(detail, 1, '通过', 'reasonFrom')"
          >
            通过</el-button
          >
          <el-button
            v-if="detail.status == 0 || detail.status == 5"
            type="danger"
            style="margin-left: 10px"
            @click="audit(detail, 2, '不通过', 'reasonFrom')"
            >不通过</el-button
          >
          <!-- <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="修改AIM链接"
      v-model="dialogVisibleLink"
      width="30%"
      :before-close="handleClose"
    >
      <el-form label-width="80px" ref="formLabelAlign" :model="formLabelAlign">
        <el-form-item label="链接">
          <el-input show-word-limit class="input-w" v-model="formLabelAlign.shortLink"></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisibleLink = false">取 消</el-button>
          <el-button type="primary" @click="quiteLink('formLabelAlign')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Card from './components/cardKeyword.vue'
import Media from './components/mediakey.vue'
import Texts from './components/textkey.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    Card,
    Media,
    Texts,
    Tooltip,
    CopyTemp,
    // ElIconLxEmoji,
    // ElIconSuccess,
    // ElIconWarning,
    // ElIconEdit,
  },
  name: 'templateCardAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      status: '',
      title: '',
      reason: '',
      dialogVisible: false,
      codeFlag: true,
      ruleForm: {
        templateName: '',
        templateId: '',
        username: '',
        chatbotId: '',
        currentPage: 1,
        pageSize: 10,
      },
      reasonFrom: {
        reason: '',
      },
      rules: {
        reason: [
          { required: true, message: '审核原因不能为空', trigger: 'blur' },
        ],
      },
      formLabelAlign: {
        shortLink: '',
        templateId: '',
      },
      tabelAlllist: {
        templateName: '',
        username: '',
        templateId: '',
        currentPage: 1,
        pageSize: 10,
      },
      detail: {},
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      dialogVisibleLink: false,
      resetFields: '',
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getchatList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/template/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tableData.forEach((item, index) => {
            if (item.rscContent) {
              let pattern = new RegExp('[\u4E00-\u9FA5：]', 'gi')
              let str = item.rscContent.replaceAll(pattern, '')
              item.rscContent = str
              // if(str.indexOf('：') != -1){
              //   // item.xsmstitle = str.slice(0,3)
              //   // item.rscContent = str.slice(3)
              //   // item.xsmstitle = str.substring(0,str.indexOf('：')+1)
              //   // item.rscContent = str.substring(str.indexOf('：')+1, str.length)
              // }
            }
            if (item.shortLink) {
              // let str = item.shortLink
              let pattern = new RegExp('[\u4E00-\u9FA5：]', 'gi')
              let str = item.shortLink.replaceAll(pattern, '')
              item.shortLink = str
              // if(str.indexOf('：') != -1){
              //   // item.shortitle = str.slice(0,3)
              //   // item.shortLink = str.slice(3)
              //   item.shortitle = str.substring(0,str.indexOf('：')+1)
              //   item.shortLink = str.substring(str.indexOf('：')+1, str.length)
              // }
            }
          })
          this.tableDataObj.total = res.data.total

          // console.log(res, "res");
        }
      )
    },
    checkContent(row, count) {
      // console.log(JSON.parse(row.templateContent));
      if (row.status == 10 || row.status == 20 || row.status == 30) {
        this.resetFields = '1'
      } else {
        this.resetFields = '2'
      }

      this.title = row.templateName
      // console.log(row,'row');
      this.dialogVisible = true
      // console.log(row);
      this.detail = row
    },
    editLink(row) {
      this.dialogVisibleLink = true
      // console.log(row);
      this.formLabelAlign.shortLink = row.shortLink
      this.formLabelAlign.templateId = row.templateId
    },
    quiteLink() {
      this.$confirms.confirmation(
        'post',
        `确认是否修改阅信链接`,
        window.path.fiveWeb + '/operator/template/updateFallback',
        this.formLabelAlign,
        (res) => {
          if (res.code == 200) {
            this.dialogVisibleLink = false
            this.getchatList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    handleClose() {
      this.dialogVisible = false
      this.dialogVisibleLink = false
    },
    // handleCopy(name,event){
    //   clip(name, event)
    // },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    checked(row) {
      this.dialogVisible = true
      this.registerFrom = row
      this.registerFrom.renewed = row.renewed + ''
      this.registerFrom.industryTypeCode = row.industryTypeCode * 1
      this.status = row.customerStatus
      //   console.log(this.registerFrom );
      if (row.code) {
        this.codeFlag = true
      } else {
        this.codeFlag = false
      }
    },
    audit(row, val, tit, from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            `确认审核${tit}模板吗？`,
            window.path.fiveWeb + '/operator/template/audit',
            {
              audit: val,
              reason: this.reasonFrom.reason,
              templateId: row.templateId,
            },
            (res) => {
              if (res.code == 200) {
                this.dialogVisible = false
                this.getchatList()
              } else {
                // this.$message({
                //   message: res.msg,
                //   type: 'error',
                // })
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.reason = ''
        this.detail = {}
        if (this.resetFields == '2') {
          this.$refs['reasonFrom'].resetFields()
        }
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>


<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>