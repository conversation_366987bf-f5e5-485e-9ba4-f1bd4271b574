<template>
  <div class="container_left">
    <div class="OuterFrame">
      <div class="ChangeAvatar">
        <el-upload
          class="avatar-uploader"
          :auto-upload="true"
          :action="actionHttp"
          :headers="token"
          name="file"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
        >
          <!-- <img :src="imageUrl" alt=""> -->
          <img
            v-if="imageUrl"
            :src="imageUrl"
            class="avatar"
            ref="avatar"
            width="100px"
          />
          <el-icon class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <span @click="avatarClick" class="avatarClick">修改头像</span>
      </div>
      <div class="information">
        <div>
          <span>用户名</span>
          <span
            >{{ information.consumerName || '-' }}({{
              information.roleDesc
            }})</span
          >
        </div>
        <div>
          <span>真实姓名</span>
          <span>{{ information.realName || '-' }}</span>
        </div>
        <div>
          <span>手机号码</span>
          <span>{{ information.phone || '-' }}</span>
        </div>
        <div>
          <span>电子邮箱</span>
          <span>{{ information.email || '-' }}</span>
        </div>
        <!-- <div>
                        <span>登录密码</span>
                        <span>******</span>
                        <span class="changePassword" @click="changePassword=true">修改密码</span>
                    </div> -->
        <div>
          <span>创建时间</span>
          <span>{{ information.createTime || '-' }}</span>
        </div>
        <!-- <div>
                        <img v-if="imageUrl" :src="imageUrl" alt="">
                    </div> -->
      </div>
    </div>
    <!-- 修改密码 -->
    <el-dialog
      title="修改密码"
      v-model="changePassword"
      :close-on-click-modal="false"
      width="420px"
    >
      <el-form
        :inline="true"
        :model="formPassword"
        :rules="formRules"
        ref="formRule"
        class="demo-form-inline IPForm"
      >
        <!-- <el-form-item label="原密码" label-width="80px" prop="oldPassword">
                        <el-input type="password" v-model="formPassword.oldPassword" placeholder="请输入原密码进行身份验证" class="input-w"></el-input>
                    </el-form-item> -->
        <el-form-item label="新密码" label-width="80px" prop="newPassword">
          <el-input
            type="password"
            v-model="formPassword.newPassword"
            placeholder="非纯数字的8-16位的密码"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" label-width="80px" prop="passWord">
          <el-input
            type="password"
            v-model="formPassword.passWord"
            placeholder="请确认新密码"
            class="input-w"
          ></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="changePassword = false">取 消</el-button>
          <el-button type="primary" @click="changePwd('formRule')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  name: '',
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('新密码不能为空'))
      } else if (
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(
          value
        )
      ) {
        callback(new Error('请输入正确的密码格式'))
      } else {
        if (this.formPassword.passWord !== '') {
          this.$refs.formRule.validateField('passWord')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入确认新密码'))
      } else if (
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(
          value
        )
      ) {
        callback(new Error('请输入正确的密码格式'))
      } else if (value !== this.formPassword.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      imageUrl: '',
      changePassword: false,
      actionHttp: window.path.omcs + 'operatingaccountinfo/portrait',
      //  actionHttp:window.path.omcs+"operatingaccountinfo/upload",
      token: {},
      formPassword: {
        //  oldPassword:"",
        newPassword: '',
        passWord: '',
      },
      formRules: {
        oldPassword: [
          { required: true, message: '原密码不能为空', trigger: 'blur' },
          // ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$
          {
            pattern:
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/gi,
            message: '密码必须包含数字以及大小写字母8-16位',
          },
        ],
        newPassword: [
          { required: true, validator: validatePass, trigger: 'blur' },
        ],
        passWord: [
          { required: true, validator: validatePass2, trigger: 'blur' },
        ],
      },
      information: {
        consumerName: '',
        roleDesc: '',
        realName: '',
        phone: '',
        email: '',
        createTime: '',
      },
    }
  },
  watch: {
    changePassword(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.formRule.resetFields()
      }
    },
  },
  methods: {
    getInfomation() {
      window.api.get(
        window.path.omcs + 'operatingaccountinfo/account',
        {},
        (res) => {
          let data = res.data
          this.information.consumerName = data.username
          this.information.roleDesc = data.roleDesc
          this.information.realName = data.realName
          this.information.phone = data.phone
          this.information.email = data.mailbox
          this.information.createTime = data.createLocalDateTime
          this.imageUrl = data.portraitUrl
          // console.log(this.imageUrl);

          // //存在vuex里
          // let SET_PORTRAITURL ='http://'+data.portraitUrl;
          // let ROLE_ID = data.roleId;
          // let param={
          //     portraitUrl:SET_PORTRAITURL,
          //     roleId:ROLE_ID
          // }
          //  this.saveInfo(param)
        }
      )
    },
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      'saveInfo',
    ]),
    //上传成功
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw)
      this.getInfomation()
    },
    beforeAvatarUpload(file) {
      const isJPEG = file.type === 'image/jpeg'
      const isJPG = file.type === 'image/jpg'
      const isJPG1 = file.type === 'image/png'
      const isJPG2 = file.type === 'image/gif'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isJPG1 && !isJPG2 && !isJPEG) {
        this.$message.error('上传头像图片只能是 jpg、png、gif、jpeg 格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
        return false
      }
    },
    avatarClick() {
      this.$refs.avatar.click()
    },
    //确定修改密码
    changePwd(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let param = {}
          // param.oldPassword=this.formPassword.oldPassword;
          param.newPassword = this.formPassword.newPassword
          this.$confirms.confirmation(
            'post',
            '确定修改密码？',
            window.path.omcs + 'operatingaccountinfo/updatePWD',
            param,
            (res) => {
              this.changePassword = false //关闭弹出框
            }
          )
        } else {
          return false
        }
      })
    },
  },
  created() {
    this.getInfomation()
    this.token = {
      Authorization: 'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  activated() {
    this.getInfomation()
    this.token = {
      Authorization: 'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 65px;
  background: #fff;
  height: 600px;
}
.ChangeAvatar {
  padding-right: 100px;
  text-align: center;
}
.ChangeAvatar,
.information {
  display: inline-block;
}
.avatarClick {
  cursor: pointer;
  margin-top: 20px;
  display: inline-block;
  width: 100%;
}
.information > div {
  padding: 10px 0;
}
.information > div > span {
  font-size: 14px;
  margin: 0 15px;
}
.information > div:nth-child(1) > span {
  font-size: 18px;
  color: #232323cf;
  font-weight: 600;
}
.avatarClick:hover {
  color: rgb(22, 160, 133);
}
.changePassword {
  color: rgb(22, 160, 133);
  cursor: pointer;
}
.changePassword:hover {
  color: red;
}
.avatar-uploader {
  width: 180px;
  height: 180px;
  border: 1px solid;
  border-radius: 50%;
  overflow: hidden;
  border-color: #55555561;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 182px;
  height: 182px;
  line-height: 182px;
  text-align: center;
}
.avatar {
  width: 180px;
  height: 180px;
  display: block;
}
</style>
