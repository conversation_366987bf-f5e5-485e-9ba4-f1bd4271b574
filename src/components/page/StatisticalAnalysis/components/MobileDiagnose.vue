<template>
  <div>
    <div class="passageWay-title">
      <!-- 查询框开始 -->
      <el-form
        :inline="true"
        :model="form"
        :rules="rules"
        label-width="80px"
        class="demo-form-inline"
        ref="sendjl"
      >
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form.clientName" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form.mobile" class="input-w"></el-input>
        </el-form-item>
      </el-form>
      <div class="boderbottom">
        <el-button
          type="primary"
          plain
          @click.prevent="Query('sendjl')"
          @keyup.enter="Query('sendjl')"
          >查询</el-button
        >
        <el-button type="primary" plain @click="reset('sendjl')"
          >重置</el-button
        >
        <!-- <el-button
              type="primary"
              v-if="selectId.length != 0"
              @click="handeExtract()"
              >提取模板</el-button
            > -->
      </div>
      <!-- 查询框结束 -->
      <div class="passage-table" style="margin-top: 10px">
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection"> </el-table-column> -->
          <el-table-column label="用户名称" width="120px">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.clientName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                     {{ scope.row.clientName}}
                                    </span>
                                </el-popover> -->
              <span
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
              >
                {{ scope.row.clientName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="msgid" label="msgId" width="180px">
          </el-table-column>
          <el-table-column label="手机号" width="100px">
            <template v-slot="scope">
              <div
                style="color: #16a589; cursor: pointer"
                @click="tableContent(scope.row.smsInfoId)"
              >
                <span>{{ scope.row.mobile }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="短信内容" width="500px">
            <template v-slot="scope">
              <el-tooltip class="items" effect="dark" placement="top-start">
                <template v-slot:content>
                  <div class="tooltip">
                    {{ scope.row.content.length }}个字符
                  </div>
                </template>
                <span class="span">{{ scope.row.content }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="chargeNum" label="计费数" width="80px">
          </el-table-column>
          <el-table-column prop="createTime" label="提交时间" width="140px">
          </el-table-column>
          <el-table-column prop="sendTime" label="发送时间" width="140px">
          </el-table-column>
          <el-table-column prop="reportTime" label="回执时间" width="140px">
          </el-table-column>
          <el-table-column label="发送状态" width="90px">
            <template v-slot="scope">
              <span v-if="scope.row.smsStatus == 1">成功</span>
              <span v-else-if="scope.row.smsStatus == 2">失败</span>
              <span v-else-if="scope.row.smsStatus == 3">待返回</span>
            </template>
          </el-table-column>
          <el-table-column prop="originalCode" label="备注"> </el-table-column>
          <el-table-column prop="ext" label="扩展号" width="90px">
          </el-table-column>
          <el-table-column label="发送通道号" width="85px">
            <template v-slot="scope">
              <span
                @click="save(scope.row.channelId)"
                style="color: rgb(22, 165, 137); cursor: pointer"
                >{{ scope.row.channelId }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="SP码号">
            <template v-slot="scope">
              <div>
                <span>{{ scope.row.spPort }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="提交Ip" width="110px">
          </el-table-column>
          <el-table-column label="归属地" width="100px">
            <template v-slot="scope">
              <div>
                <span>{{
                  (scope.row.provincial || '未知') +
                  '/' +
                  (scope.row.city || '未知')
                }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="运营商" width="100px">
            <template v-slot="scope">
              <div
                v-if="scope.row.operator == 1"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yidong"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div
                v-else-if="scope.row.operator == 2"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-liantong"
                  style="font-size: 16px; color: #f56c6c"
                ></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div
                v-else-if="scope.row.operator == 3"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-dianxin"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">电信</span>
              </div>
              <div v-else>未知</div>
            </template>
          </el-table-column>
          <el-table-column prop="source" label="发送类型" width="100px">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.smsType == 1">验证码</span>
                <span v-if="scope.row.smsType == 2">通知</span>
                <span v-if="scope.row.smsType == 3">营销推广</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="source" label="来源" width="100px">
          </el-table-column>
          <el-table-column label="成功计费数" width="100px">
            <template v-slot="scope">
              <div>
                <span>{{ scope.row.chargeSuccessNum }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="100">
            <template v-slot="scope">
              <el-button
                @click="handleClick(scope.row)"
                type="text"
                size="default"
                >号码诊断</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formData.currentPage"
              :page-size="formData.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.totalRow"
            >
            </el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <!-- 点手机号查询补发 -->
    <el-dialog
      title="补发详情"
      v-model="moreInfoDialog"
      width="810px"
      class="pro-style"
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="tableDataObj1.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj1.tableData"
        style="width: 100%"
      >
        <el-table-column label="发送类型" width="80px">
          <template v-slot="scope">
            <span v-if="scope.row.sendType == 1">自动补发</span>
            <span v-else-if="scope.row.sendType == 2">首次发送</span>
            <span v-else-if="scope.row.sendType == 3">手动补发</span>
            <span v-else-if="scope.row.sendType == 4">携号转网</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" width="100px">
          <template v-slot="scope">
            <span
              style="cursor: pointer; color: #16a589"
              @click="phoneClick(scope.$index, scope.row)"
              >{{ scope.row.mobile }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="通道ID" width="70px">
          <template v-slot="scope">
            <span
              @click="save(scope.row.channelId)"
              style="color: rgb(22, 165, 137); cursor: pointer"
              >{{ scope.row.channelId }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="smsStatusStr" label="状态" width="80px">
        </el-table-column>
        <el-table-column prop="originalCode" label="失败代码" width="150px">
        </el-table-column>
        <el-table-column prop="ext" label="扩展码" width="150px">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="140px">
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="140px">
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 20px">
        <el-button type="primary" @click="moreInfoDialog = false"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 相似度 -->
    <!-- <SimilarTemlpate
          :dialogVisibles="dialogVisible"
          :tableDatas="extractObj.tableData"
          :similarTemplateLists="extractObj.similarTemplateList"
          @handleClose="handleCloses"
        >
        </SimilarTemlpate> -->
    <!-- 诊断 -->
    <el-dialog
      :title="'号码诊断（' + username + '）'"
      v-model="dialogVisibleImp"
      width="30%"
      :before-close="handleCloseImp"
    >
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          :color="activity.colour"
        >
          <div style="display: flex">
            <div style="height: 35px">{{ activity.msg }}</div>
            <div style="margin: 0 10px">
              <el-button
                v-if="
                  activity.event == 'zt_1005_event' ||
                  activity.event == 'zt_1006_event' ||
                  activity.event == 'zt_1007_event'
                "
                type="primary"
                @click="handelBlack(activity.event, false)"
                >加白</el-button
              >
              <el-button
                v-if="activity.event == 'zt_divert_event'"
                type="primary"
                @click="handelBlack(activity.event, false)"
                >加白解除黑名单</el-button
              >
              <el-button
                v-if="activity.event == 'zt_divert_event'"
                type="danger"
                @click="handelBlack('zt_divert_event', true)"
                >加白解除黑名单补发</el-button
              >
            </div>
          </div>
          <div
            v-if="activity.label.length"
            style="font-size: 12px; color: #5e6d82; margin: 10px 0"
          >
            <el-tag
:disable-transitions="true"
              style="margin: 0px 5px"
              v-for="(item, index) in activity.label"
              :key="index"
              type="info"
              effect="dark"
            >
              {{ item }}
            </el-tag>
          </div>
        </el-timeline-item>
      </el-timeline>
      <template v-slot:footer>
        <span class="dialog-footer">
          <!-- <el-button @click="dialogVisibleImp = false">取 消</el-button> -->
          <el-button @click="dialogVisibleImp = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView'
import DatePlugin from '@/components/publicComponents/DatePlugin' //时间
import TableTem from '@/components/publicComponents/TableTem' //列表
import UserLists from '@/components/publicComponents/userList'
import SimilarTemlpate from '@/components/publicComponents/similarTemlpate' //相似度
import moment from 'moment'
export default {
  name: 'MobileDiagnose',
  components: { DatePlugin, TableTem, ChannelView, UserLists, SimilarTemlpate },
  data() {
    return {
      isFirstEnter: false,
      userFlag: false,
      dialogVisibleImp: false,
      ChannelData: '', //传递通道值
      selectId: [], //列表id
      idStr: '',
      username: '',
      moreInfoDialog: false,
      //发送查询的值
      form: {
        mobile: '',
        clientName: '',
        flag: '2',
        currentPage: 1,
        pageSize: 10,
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        flag: '2',
        clientName: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        totalRow: 0,
        tableData: [],
      },
      rules: {
        clientName: [
          { required: true, message: '请输入用户名称', trigger: 'blur' },
        ],
      },
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      //相似度
      //   contentFlag: false,
      //   dialogVisible: false,
      //   contentList: [],
      //   extractObj: {
      //     loading2: false,
      //     tableData: [],
      //     similarTemplateList: [],
      //   },
      path: ' ws://opt.zthysms.cn/websocket/diagnosis/',
      socket: '',
      token: '',
      ws: '',
      timeout: 50000, //50秒一次心跳
      timeoutObj: null, //心跳心跳倒计时
      serverTimeoutObj: null, //心跳倒计时
      timeoutnum: null, //断开 重连倒计时
      activities: [],
      smsInfoId: '',
    }
  },
  methods: {
    //创建socket链接
    // init: function () {
    //   let that = this;
    //   if (typeof WebSocket === "undefined") {
    //     this.$notify({
    //       title: "您的浏览器不支持socket",
    //     });
    //     // alert("您的浏览器不支持socket")
    //   } else {
    //     // // 实例化socket
    //     that.ws = new WebSocket(window.path.socket+'diagnosis/' + this.token);
    //     that.global.setWs(that.ws);
    //     that.ws.onopen = function () {
    //       console.log("连接成功");
    //       that.start();
    //     };
    //     // 监听socket消息
    //     that.ws.onmessage = (msg) => {
    //       console.log(msg, "msg");
    //       let data = JSON.parse(msg.data);
    //       if (data.show) {
    //         that.activities.push(data);
    //       }
    //       //  console.log(Object.prototype.toString.call(msg.data),'data');
    //       //   if (msg.data != "连接成功！" ) {

    //       //   }
    //       that.resetStart();
    //     };
    //     //连接错误
    //     that.ws.onerror = function () {
    //       console.log("WebSocket连接发生错误");
    //       //重连
    //       that.reconnect();
    //     };
    //     that.ws.onclose = this.websocketclose;
    //   }
    // },
    // websocketclose(e) {
    //   //关闭
    //   console.log("连接已关闭....");
    //   let that = this;
    //   // that.global.ws.close();
    //   // that.global.ws.onclose();
    //   //   // 断线重新连接
    //     setTimeout(() => {
    //       that.init();
    //     }, 2000);
    // },

    // //重新连接
    // reconnect() {
    //   let that = this;
    //   if (that.global.ws && that.global.ws.readyState == 1) {
    //     clearInterval(that.timeoutnum);
    //     that.timeoutnum = null;
    //     that.timeNum = 0;
    //     return;
    //   }
    //   if (!that.timeoutnum) {
    //     that.timeoutnum = setInterval(function () {
    //       if (that.global.ws && that.global.ws.readyState == 1) {
    //         clearInterval(that.timeoutnum);
    //         that.timeoutnum = null;
    //         that.timeNum = 0;
    //         return;
    //       }
    //       //新连接
    //       that.init();
    //       that.timeNum++;
    //       if (that.timeNum >= 3) {
    //         clearInterval(that.timeoutnum);
    //         that.timeoutnum = null;
    //         that.timeNum = 0;
    //       }
    //     }, 1000);
    //   }
    // },
    // //重置心跳
    // //清除时间
    // resetStart() {
    //   clearTimeout(this.timeoutObj);
    //   clearTimeout(this.serverTimeoutObj);
    //   //重启心跳
    //   this.start();
    // },
    // //开启心跳
    // start() {
    //   let that = this;
    //   let date = new Date();
    //   that.timeoutObj && clearTimeout(that.timeoutObj);
    //   that.serverTimeoutObj && clearTimeout(that.serverTimeoutObj);
    //   that.timeoutObj = setTimeout(function () {
    //     //这里发送一个心跳，后端收到后，返回一个心跳消息，
    //     if (that.global.ws && that.global.ws.readyState == 1) {
    //       //如果连接正常
    //       that.global.ws.send(`发送心跳给后端${date}`);
    //     } else {
    //       //否则重连
    //       that.reconnect();
    //     }
    //   }, that.timeout);
    // },
    // isAllEqual(array) {
    //   if (array.length > 0) {
    //     return !array.some(function (value, index) {
    //       return value.clientName !== array[0].clientName;
    //     });
    //   } else {
    //     return true;
    //   }
    // },
    // handelSelection(val) {
    //   //列表复选框的值
    //   this.selectId = val.map((item) => {
    //     return item.smsReplyId;
    //   });
    //   this.contentFlag = this.isAllEqual(val);
    //   if (this.contentFlag) {
    //     this.contentList = val.map((item) => {
    //       return {
    //         username: item.clientName,
    //         content: item.content,
    //       };
    //     });
    //   }
    // },
    //提取相似度
    // handeExtract() {
    //   if (this.contentFlag) {
    //     window.api.post(
    //       window.path.omcs + "operatingclientsmssimilar/content/percent",
    //       this.contentList,
    //       (res) => {
    //         if (res.code == 200) {
    //           this.dialogVisible = true;
    //           this.extractObj.tableData = res.data.similarList;
    //           this.extractObj.similarTemplateList =
    //             res.data.similarTemplateList;
    //         }
    //       }
    //     );
    //   } else {
    //     this.$message({
    //       message: "请选择同一用户名！",
    //       type: "warning",
    //     });
    //   }
    // },
    handleCloses(val) {
      this.dialogVisible = val
    },
    handleCloseImp() {
      this.dialogVisibleImp = false
    },
    // 点击手机号
    tableContent(smsInfoId) {
      // console.log(11111);
      window.api.get(
        window.path.omcs + 'smsMessage/details?smsInfoId=' + smsInfoId,
        {},
        (res) => {
          if (res.code == 200) {
            this.tableDataObj1.loading2 = false
            this.tableDataObj1.tableData = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
      this.moreInfoDialog = true
    },
    //获取 发送记录数据
    getTableDtate() {
      let aa = {}
      Object.assign(aa, this.formData)
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingStatisticalAnalysis/sendSmsInfo/page',
        aa,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.totalRow = res.data.total
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
    },
    phoneClick(index, row) {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj1.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
    },
    handleChangeTimeOptions: function () {
      // this.datePluginValue = ''
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    //发送查询
    Query(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          Object.assign(this.formData, this.form)
          this.getTableDtate()
        }
      })
    },
    //发送重置
    reset() {
      this.$refs.sendjl.resetFields()
      Object.assign(this.formData, this.form)
      this.getTableDtate()
      // this.flag='1'
      // this.$refs.sendjl.resetFields();
      // Object.assign(this.formData,this.form);
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    handleSizeChange(size) {
      this.formData.pageSize = size
      this.getTableDtate()
    },
    handleCurrentChange: function (currentPage) {
      this.formData.currentPage = currentPage
      this.getTableDtate()
    },
    //号码诊断
    handleClick(row) {
      let that = this
      this.smsInfoId = row.smsInfoId
      this.username = row.clientName
      let obj = {
        clientName: row.clientName,
        cipherMobile: row.cipherMobile,
        keyId: row.keyId,
        smsType: row.smsType,
        provincial: row.provincial,
        signature: row.signature,
        msgid: row.msgid,
        batchNo: row.batchNo,
        createTime: row.createTime,
      }
      window.api.post(
        window.path.omcs + 'smsMessage/diagnosis/check',
        obj,
        (res) => {
          that.dialogVisibleImp = true
          if (res.code == 200) {
            this.activities = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
          // that.global.ws.onmessage = function (msg) {
          //   console.log(msg, "msg");
          // };
          // if (res.code == 200) {
          //   console.log(res,'res');
          // }
        }
      )
    },
    handelBlack(event, resend) {
      let obj = {
        event,
        resend,
        smsInfoId: this.smsInfoId,
      }
      let tit = ''
      if (resend) {
        tit = '确定加白解除黑名单补发？'
      } else {
        tit = '确定加白解除黑名单？'
      }
      this.$confirms.confirmation(
        'post',
        tit,
        window.path.omcs + 'smsMessage/diagnosis',
        obj,
        (res) => {
          console.log(res, 'res')
        }
      )
    },
  },
  created() {
    this.isFirstEnter = true
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    this.token = window.common.getCookie('ZTADMIN_TOKEN')
    // this.getTableDtate();
    // this.init();
    // this.$nextTick(() => {

    // });
  },
  activated() {
    // this.init();
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.token = window.common.getCookie('ZTADMIN_TOKEN')
        // this.getTableDtate();
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },

  // deactivated() {
  //   this.websocketclose();
  //   // this.over()
  //   // that.close()
  //   // that.global.ws.onclose();
  //   // that.ws.close();
  // },
  watch: {
    moreInfoDialog(val) {
      if (val == false) {
        this.tableDataObj1.loading2 = false
        this.tableDataObj1.tableData = []
      }
    },
    dialogVisibleImp(val) {
      if (!val) {
        this.activities = []
        this.username = ''
      }
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
</style>
