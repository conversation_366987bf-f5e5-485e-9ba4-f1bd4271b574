<template>
  <div>
    <el-dialog
      title="相似度"
      v-model="dialogVisible"
      width="1100px"
      :before-close="handleClose"
    >
      <div style="display: flex">
        <div>
          <div style="margin: 16px 0" class="title">模板列表</div>
          <el-table
            v-loading="extractObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="extractObj.tableData"
            style="width: 550px"
            max-height="350"
          >
            <el-table-column label="用户名" width="120">
              <template v-slot="scope">
                <span>{{ scope.row.username }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="签名" width="120">
                  <template #default="scope">
                    <span>{{ scope.row.signature }}</span>
                  </template>
                </el-table-column> -->
            <el-table-column label="模板内容" min-width="300">
              <template v-slot="scope">
                <div style="display: flex">
                  <div>
                    <span class="spanColor" v-html="scope.row.content"></span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="相似度">
              <template v-slot="scope">
                <span>{{ scope.row.percent }}%</span>
              </template>
            </el-table-column>
          </el-table>
          <div style="margin: 16px 0" class="title">相似模板列表</div>
          <el-table
            v-loading="extractObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="extractObj.similarTemplateList"
            style="width: 550px"
            max-height="350"
          >
            <el-table-column label="用户名" width="120">
              <template v-slot="scope">
                <span>{{ scope.row.username }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="签名" width="120">
                  <template #default="scope">
                    <span>{{ scope.row.signature }}</span>
                  </template>
                </el-table-column> -->
            <el-table-column label="模板内容" min-width="300">
              <template v-slot="scope">
                <div style="display: flex">
                  <div>
                    <span class="spanColor" v-html="scope.row.content"></span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="相似度">
              <template v-slot="scope">
                <span>{{ scope.row.percent }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-left: 30px">
          <div style="margin: 16px 0" class="title">自定义相似度</div>
          <el-form
            :model="similarFormop"
            :rules="similaRrules"
            label-width="140px"
            ref="similarFormop"
            style="padding: 0 28px 0 20px"
          >
            <el-form-item label="用户名" prop="username">
              <!-- <el-input
                style="width: 200px"
                v-model="similarFormop.username"
                autocomplete="off"
                class="input-w"
              ></el-input> -->
              <el-select
                style="width: 280px"
                v-model="similarFormop.username"
                placeholder="请选择"
              >
                <template v-if="usernameList.length > 1">
                  <el-option
                    v-for="(item, index) in usernameList"
                    :key="item"
                    :label="
                      index === usernameList.length - 1
                        ? `${item}（管理商）`
                        : item
                    "
                    :value="item"
                  >
                  </el-option>
                </template>
                <template v-else>
                  <el-option
                    v-for="item in usernameList"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </template>

                <!-- <el-option label="审核不通过" value="2"> </el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item label="模板内容" prop="content">
              <el-input
                type="textarea"
                style="height: 110px; width: 280px"
                v-model="similarFormop.content"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="关键字" prop="keywords">
              <el-input
                style="width: 280px"
                v-model="similarFormop.keywords"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select
                style="width: 280px"
                v-model="similarFormop.auditStatus"
                placeholder="请选择"
              >
                <el-option label="审核通过" value="1"> </el-option>
                <el-option label="审核不通过" value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="相似度" prop="percent">
              <el-input
                style="width: 280px"
                placeholder="相似度为正整数，请输入正确数字"
                v-model="similarFormop.percent"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item label="是否校验签名" prop="checkSign">
              <el-radio-group v-model="similarFormop.checkSign">
                <el-radio value="0">是</el-radio>
                <el-radio value="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否支持公司主体" prop="supportCompany">
              <el-radio-group v-model="similarFormop.supportCompany">
                <el-radio :value="true">是</el-radio>
                <el-radio :value="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm('similarFormop')"
            >提交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../utils/gogocodeTransfer";
export default {
  props: ["dialogVisibles", "tableDatas", "similarTemplateLists"],
  data() {
    var percent = (rule, value, callback) => {
      if (value != "") {
        let reg = /^[0-9]*[1-9][0-9]*$/;
        if (reg.test(value)) {
          if (value >= 50 && value <= 100) {
            callback();
          } else {
            callback(new Error("相似度在于50-100之间"));
          }
        } else {
          callback(new Error("相似度为正整数，请输入正确数字"));
        }
      } else {
        callback(new Error("相似度不能为空"));
      }
    };
    return {
      dialogVisible: false,
      contentList: [],
      usernameList: [],
      similarFormop: {
        //相似度
        username: "",
        content: "",
        percent: "",
        keywords: "",
        auditStatus: "1",
        snapshot: "",
        checkSign: "0",
        supportCompany:false
      },
      similaRrules: {
        username: [
          { required: true, message: "用户名不能为空", trigger: "change" },
        ],
        content: [
          { required: true, message: "内容不能为空", trigger: "change" },
        ],
        keywords: [
          { required: true, message: "关键词不能为空", trigger: "change" },
        ],
        auditStatus: [
          { required: true, message: "请选择审核状态", trigger: "change" },
        ],
        percent: [{ required: true, validator: percent, trigger: "change" }],
        checkSign: [
          { required: true, message: "请选择是否检验签名", trigger: "change" },
        ],
        supportCompany: [
          {
            required: true,
            message: "请选择是否支持公司主体",
            trigger: "change",
          },
        ]
      },
      extractObj: {
        loading2: false,
        tableData: [],
        similarTemplateList: [],
      },
    };
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
      $emit(this, "handleClose", this.dialogVisible);
    },
    //提交相似度
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + "operatingclientsmssimilar",
            this.similarFormop,
            (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: "success",
                });
                this.dialogVisible = false;
                this.$parent.getTableDtate();
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            }
          );
        }
      });
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        $emit(this, "handleClose", val);
        this.$refs["similarFormop"].resetFields();
        this.usernameList = [];
      }
    },
    dialogVisibles: {
      handler: function (val) {
        this.dialogVisible = val;
      },
      deep: true,
    },
    tableDatas: {
      handler: function (val) {
        this.extractObj.tableData = val;
        // this.similarFormop.content = val[0].content
        //   .replace(/<span class='prompt'>/g, "")
        //   .replace(/<\/span>/g, "");
        this.similarFormop.content = val[0].content.replace(/<[^>]+>/g, "");
        this.similarFormop.username = val[0].username;
        this.usernameList = val[0].usernames;
        // if(val[0].usernames.length>1){

        // }
        this.similarFormop.snapshot = JSON.stringify(val);
      },
      deep: true,
    },
    similarTemplateLists: {
      handler: function (val) {
        this.extractObj.similarTemplateList = val;
      },
      deep: true,
    },
  },
  emits: ["handleClose"],
};
</script>

<style scoped>
.title {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  margin: 10px 0;
}
</style>
