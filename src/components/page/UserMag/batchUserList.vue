<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-page-header @back="goBack" content="批量任务详情"> </el-page-header>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="queryForm"
        >
          <el-form-item :label="actionName" prop="targetId">
            <el-input
              v-model="formInline.targetId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="任务类型" prop="taskType">
                            <el-select v-model="formInline.taskType" clearable placeholder="不限" class="input-w">
                                <el-option label="不限" value=""></el-option>
                                <el-option label="管理商" value="12"></el-option>
                                <el-option label="子用户" value="13"></el-option>
                                <el-option label="终端" value="14"></el-option>
                                <el-option label="线上终端" value="22"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间" prop="time">
                            <el-date-picker class="input-time" v-model="formInline.time" value-format="YYYY-MM-DD"
                                type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                                @change="handeTime">
                            </el-date-picker>
                        </el-form-item> -->
          <el-form-item>
            <el-button type="primary" plain style="" @click="Query"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reload('queryForm')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="Mail-table Mail-table1">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection"> </el-table-column> -->
          <el-table-column label="批量任务ID">
            <template v-slot="scope">
              <span sty>{{ scope.row.taskId }}</span>
            </template>
          </el-table-column>

          <el-table-column :label="actionName">
            <template v-slot="scope">
              {{ scope.row.targetId }}
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                type="success"
                effect="dark"
                v-if="scope.row.status == '1'"
                >成功</el-tag
              >
              <el-tag
:disable-transitions="true"
                type="danger"
                effect="dark"
                v-else-if="scope.row.status == '2'"
                >失败</el-tag
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="statusReason" label="状态说明">
          </el-table-column>
        </el-table>
        <!--分页-->
        <div
          style="
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
          "
        >
          <div></div>
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import Tooltip from '@/components/publicComponents/tooltip.vue'
import clip from '../../../utils/clipboard'

export default {
  name: 'batchUserShop',
  components: {
    Tooltip,
  },
  data() {
    return {
      isFirstEnter: false,
      actionName: '',
      formInline: {
        targetId: '', //用户名称
        pageSize: 10, //每页显示条数
        currentPage: 1, //当前页数
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        total: 0, //总条数
      },
    }
  },
  watch: {},
  methods: {
    //返回上一页
    goBack() {
      this.$router.push('/batchUserShop')
    },
    //获取列表数据
    getTableData() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operating/client/batch/task/record/page',
        this.formInline,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    Query() {
      this.getTableData()
    },
    //重置
    Reload(formName) {
      this.$refs[formName].resetFields() //清空查询表单
      this.getTableData()
    },
    //-----复制功能
    handleCopy(name, event) {
      clip(name, event)
    },
    //-----翻页操作
    handleSizeChange(size) {
      this.formInline.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      this.formInline.currentPage = currentPage
      this.getTableData()
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      if (this.$route.query.id) {
        this.formInline.taskId = this.$route.query.id
        if (this.$route.query.action == 'USER') {
          this.actionName = '用户名称'
        }
        this.getTableData()
      }
    })

    // this.getSales()
    // this.getChannel()
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        if (this.$route.query.id) {
          this.formInline.taskId = this.$route.query.id
          if (this.$route.query.action == 'USER') {
            this.actionName = '用户名称'
          }
          this.getTableData()
        }
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style lang="less" scoped>
.fillet {
  margin-top: 18px;
}
:deep(.el-table__row) {
  height: 48px;
}
</style>
