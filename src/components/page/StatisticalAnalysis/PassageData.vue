<template>
  <div>
    <div class="Top_title">通道发送统计</div>
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <component v-bind:is="currentTabComponent"></component>
    </div>
  </div>
</template>

<script>
import PassagewaySend from './components/PassagewaySend.vue'
export default {
  name: 'PassageData',
  components: {
    PassagewaySend,
  },
  data() {
    return {
      currentTabComponent: 'PassagewaySend',
    }
  },
}
</script>
