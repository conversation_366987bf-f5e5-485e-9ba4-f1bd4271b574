<template>
  <div>
    <div class="Top_title">
      <span
        style="
          display: inline-block;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><el-icon><el-icon-arrow-left /></el-icon> 返回</span
      >|
      <span>短信配置</span>
    </div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-form
        :model="addUserStep2Form.formData"
        :rules="addUserStep2Form.formRule"
        ref="addUserStep2Form"
        label-width="180px"
      >
        <div class="float-div-1" style="padding-right: 20px">
          <div>
            <p
              style="padding: 0 0 18px 40px; font-weight: bold; font-size: 14px"
            >
              通道设置
            </p>
            <!-- <el-form-item label="选择通道" prop="smsSelect"  >
                                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.smsSelect">
                                        <el-option label="人工通道组" value="1"></el-option>
                                </el-select>
                            </el-form-item> -->
            <el-form-item label="发送签名限制" prop="smsIsSignatureReport">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsSignatureReport"
              >
                <el-radio label="0">自定义签名</el-radio>
                <el-radio label="1" style="margin-left: 10px"
                  >报备签名</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="运营商下发" prop="passOperator">
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.passOperator"
              >
                <el-checkbox label="1">移动</el-checkbox>
                <el-checkbox label="2">联通</el-checkbox>
                <el-checkbox label="3">电信</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="行业通道组名称" prop="smsProductId">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.smsProductId"
                filterable
                placeholder="请选择通道名称"
              >
                <el-option
                  v-for="(item, index) in channel"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="验证码专用通道" prop="yzmProductId">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.yzmProductId"
                filterable
                placeholder="请选择通道名称"
              >
                <el-option
                  v-for="(item, index) in channel"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="营销专用通道" prop="yxProductId">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.yxProductId"
                filterable
                placeholder="请选择通道名称"
              >
                <el-option
                  v-for="(item, index) in channel"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="开启机型匹配" prop="smsIsMobileAttribute">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsMobileAttribute"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <p
              style="padding: 0 0 18px 40px; font-weight: bold; font-size: 14px"
            >
              计费设置
            </p>
            <el-form-item label="计费方式" prop="smsChargeback">
              <el-radio-group v-model="addUserStep2Form.formData.smsChargeback">
                <el-radio disabled label="1">提交计费</el-radio>
                <el-radio disabled label="2" style="margin-left: 10px"
                  >成功计费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="付费方式" prop="smsPayMethod">
              <el-radio-group v-model="addUserStep2Form.formData.smsPayMethod">
                <el-radio disabled label="1">预付费</el-radio>
                <el-radio disabled label="2" style="margin-left: 10px"
                  >后付费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="允许超发"
              v-if="addUserStep2Form.formData.smsPayMethod == 1"
              prop="smsIsRepliesBlack"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsOvershoot"
              >
                <el-radio :disabled="roleId != '3'" label="0">是</el-radio>
                <el-radio
                  :disabled="roleId != '3'"
                  label="1"
                  style="margin-left: 10px"
                  >否</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="用户单价"   prop="smsPrice">
                                <el-input class="input-w-2" v-model="addUserStep2Form.formData.smsPrice"></el-input>
                            </el-form-item> -->
          </div>
          <div>
            <p
              style="padding: 0 0 18px 40px; font-weight: bold; font-size: 14px"
            >
              状态设置
            </p>
            <el-form-item label="返回通道错误码" prop="smsIsChannelCode">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsChannelCode"
              >
                <el-radio label="1">原始错误码</el-radio>
                <el-radio label="2" style="margin-left: 10px"
                  >错误码分类</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="消息报告" prop="smsReportLevel">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsReportLevel"
                class="input-w"
              >
                <el-radio label="1">不接收报告</el-radio>
                <el-radio label="2" style="margin-left: 10px"
                  >批量推送</el-radio
                >
                <el-radio label="3" style="margin-left: 10px"
                  >主动抓取</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="推送方式"
              prop="smsPushType"
              v-if="addUserStep2Form.formData.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-radio-group
                v-model="addUserStep2Form.formData.smsPushType"
                class="input-w"
              >
                <el-radio label="1">V5平台推送方式</el-radio>
                <el-radio label="2" style="margin-left: 10px"
                  >融合平台推送方式</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="推送请求头"
              prop=""
              v-if="addUserStep2Form.formData.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-radio-group v-model="addUserStep2Form.formData.pushHeader">
                <el-radio label="-1">无</el-radio>
                <el-radio style="margin-left: 10px" label="0">1个</el-radio>
                <el-radio style="margin-left: 10px" label="1">2个</el-radio>
                <el-radio style="margin-left: 10px" label="2">3个</el-radio>
                <el-radio style="margin-left: 10px" label="3">4个</el-radio>
                <el-radio style="margin-left: 10px" label="4">5个</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <div v-for="(item,index) in addUserStep2Form.formData.smsPushHeader" :key="index">
              
                </div> -->
            <div v-if="addUserStep2Form.formData.pushHeader != '-1'">
              <div style="display: flex; margin-left: 70px">
                <el-form-item
                  label="headKey"
                  prop="headKey"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headKey"
                    @change="handelChange"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="headValue"
                  prop="headValue"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headValue"
                    @change="handelChangeValue"
                  ></el-input>
                </el-form-item>
              </div>
              <div
                v-if="
                  addUserStep2Form.formData.pushHeader == '1' ||
                  addUserStep2Form.formData.pushHeader == '2' ||
                  addUserStep2Form.formData.pushHeader == '3' ||
                  addUserStep2Form.formData.pushHeader == '4'
                "
                style="display: flex; margin-left: 70px"
              >
                <el-form-item
                  label="headKey"
                  prop="headKey1"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headKey1"
                    @change="handelChange"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="headValue"
                  prop="headValue1"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headValue1"
                    @change="handelChangeValue"
                  ></el-input>
                </el-form-item>
              </div>
              <div
                v-if="
                  addUserStep2Form.formData.pushHeader == '2' ||
                  addUserStep2Form.formData.pushHeader == '3' ||
                  addUserStep2Form.formData.pushHeader == '4'
                "
                style="display: flex; margin-left: 70px"
              >
                <el-form-item
                  label="headKey"
                  prop="headKey2"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headKey2"
                    @change="handelChange"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="headValue"
                  prop="headValue2"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headValue2"
                    @change="handelChangeValue"
                  ></el-input>
                </el-form-item>
              </div>
              <div
                v-if="
                  addUserStep2Form.formData.pushHeader == '3' ||
                  addUserStep2Form.formData.pushHeader == '4'
                "
                style="display: flex; margin-left: 70px"
              >
                <el-form-item
                  label="headKey"
                  prop="headKey3"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headKey3"
                    @change="handelChange"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="headValue"
                  prop="headValue3"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headValue3"
                    @change="handelChangeValue"
                  ></el-input>
                </el-form-item>
              </div>
              <div
                v-if="addUserStep2Form.formData.pushHeader == '4'"
                style="display: flex; margin-left: 70px"
              >
                <el-form-item
                  label="headKey"
                  prop="headKey4"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headKey4"
                    @change="handelChange"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="headValue"
                  prop="headValue4"
                  label-width="100px"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"
                >
                  <el-input
                    style="width: 150px"
                    v-model="addUserStep2Form.formData.headValue4"
                    @change="handelChangeValue"
                  ></el-input>
                </el-form-item>
              </div>
            </div>

            <el-form-item
              v-if="addUserStep2Form.formData.smsReportLevel != 1"
              label="仅接收最后一条回执"
              prop="isReceiptDetails"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.isReceiptDetails"
              >
                <el-radio label="2">是</el-radio>
                <el-radio label="1" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="上行地址"
              prop="smsRepliesUrl"
              v-if="addUserStep2Form.formData.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                type="textarea"
                v-model="addUserStep2Form.formData.smsRepliesUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取回复短信的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="下行地址"
              prop="smsReportUrl"
              v-if="addUserStep2Form.formData.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                type="textarea"
                v-model="addUserStep2Form.formData.smsReportUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取状态报告的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="回复推送数量"
              prop="smsRepliesPackageSize"
              v-if="addUserStep2Form.formData.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder="v5默认150，融合默认400"
                v-model="addUserStep2Form.formData.smsRepliesPackageSize"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="回复抓取数量"
              prop="smsRepliesPackageSize"
              v-if="addUserStep2Form.formData.smsReportLevel == 3"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder="v5默认150，融合默认400"
                v-model="addUserStep2Form.formData.smsRepliesPackageSize"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="状态推送数量"
              prop="smsStrepliesPackageSize"
              v-if="addUserStep2Form.formData.smsReportLevel == 2"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder="v5默认150，融合默认400"
                v-model="addUserStep2Form.formData.smsStrepliesPackageSize"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="状态抓取数量"
              prop="smsStrepliesPackageSize"
              v-if="addUserStep2Form.formData.smsReportLevel == 3"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder="v5默认150，融合默认400"
                v-model="addUserStep2Form.formData.smsStrepliesPackageSize"
              ></el-input>
            </el-form-item>
            <el-form-item label="模板审核推送地址" prop="smsTemplateUrl"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.smsTemplateUrl"
              ></el-input>
            </el-form-item>
            <el-form-item label="签名审核推送地址" prop="smsSignatureUrl"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.smsSignatureUrl"
              ></el-input>
            </el-form-item>
            <el-form-item label="短信进审核推送地址" prop="smsAuditUrl"
              ><!-- 勾选批量推送时出现 -->
              <el-input
                style="width: 200px"
                placeholder=""
                type="textarea"
                v-model="addUserStep2Form.formData.smsAuditUrl"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="float-div-2" style="padding-right: 20px">
          <div>
            <p
              style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px"
            >
              审核设置
            </p>
            <el-form-item label="计费条数免审设置" prop="smsIsAudit">
              <el-radio-group v-model="addUserStep2Form.formData.smsIsAudit">
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="免审计费条数"
              prop="smsAuditNum"
              v-if="addUserStep2Form.formData.smsIsAudit == 1"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.smsAuditNum"
                :min="1"
                :max="50000"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="敏感词审核" prop="smsIsBlackWords">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsBlackWords"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="敏感词是否进审核"
              style="width: 360px"
              prop="smsIsBlackWordsFail"
            >
              <el-radio-group
                :disabled="
                  addUserStep2Form.formData.smsIsBlackWords == '1'
                    ? false
                    : true
                "
                v-model="addUserStep2Form.formData.smsIsBlackWordsFail"
              >
                <el-radio label="0">进入审核</el-radio>
                <el-radio label="1" style="margin-left: 10px"
                  >直接失败</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="用户上行加黑" prop="smsIsRepliesBlack">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsRepliesBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="加黑关键词"
              prop="smsRepliesBlackWords"
              v-if="addUserStep2Form.formData.smsIsRepliesBlack == 1"
            >
              <el-input
                type="textarea"
                :rows="4"
                show-word-limit
                resize="none"
                class="input-w-3"
                placeholder=" 多个用逗号(,)隔开"
                v-model="addUserStep2Form.formData.smsRepliesBlackWords"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="验证用户黑号"
              style="width: 360px"
              prop="smsIsPlatformBlack"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsPlatformBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 验证码 -->
            <el-form-item
              label="验证 验证码黑号"
              style="width: 360px"
              prop="smsIsUserYzmBlack"
            >
              <el-radio-group
                v-if="roleId == 3 || roleId == 4"
                v-model="addUserStep2Form.formData.smsIsUserYzmBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
              <el-radio-group
                v-else
                v-model="addUserStep2Form.formData.smsIsUserYzmBlack"
                disabled
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="黑号级别"
              prop="smsUserYzmBlackLevel1"
              v-if="addUserStep2Form.formData.smsIsUserYzmBlack == 1"
            >
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.smsUserYzmBlackLevel1"
                @change="handelYzmBlack"
                v-if="roleId == 3"
              >
                <el-checkbox
                  v-for="(item, index) in level"
                  :key="index"
                  style="margin-left: 0"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
                <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
              </el-checkbox-group>
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-else
                v-model="addUserStep2Form.formData.smsUserYzmBlackLevel1"
                disabled
              >
                <el-checkbox
                  v-for="(item, index) in level"
                  :key="index"
                  style="margin-left: 0"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <!-- 行业 -->
            <el-form-item
              label="验证行业黑号"
              style="width: 360px"
              prop="smsIsUserHyBlack"
            >
              <el-radio-group
                v-if="roleId == 3 || roleId == 4"
                v-model="addUserStep2Form.formData.smsIsUserHyBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
              <el-radio-group
                v-else
                v-model="addUserStep2Form.formData.smsIsUserHyBlack"
                disabled
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="黑号级别"
              prop="smsUserHyBlackLevel1"
              v-if="addUserStep2Form.formData.smsIsUserHyBlack == 1"
            >
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.smsUserHyBlackLevel1"
                @change="handelHymBlack"
                v-if="roleId == 3"
              >
                <!-- <el-checkbox v-for="(item,index) in level" :key="index" style="margin-left: 0" 
                  :disabled='addUserStep2Form.formData.smsUserYzmBlackLevel1.includes(item.value)' 
                  :label="item.value">
                    {{item.label}}
                  </el-checkbox> -->
                <el-checkbox
                  v-for="(item, index) in level"
                  :key="index"
                  style="margin-left: 0"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
                <!-- <el-checkbox :disabled='disabled' style="margin-left: 0" label="1">一级</el-checkbox>
                    <el-checkbox :disabled='disabled' style="margin-left: 0" label="2">二级</el-checkbox>
                    <el-checkbox :disabled='disabled' style="margin-left: 0" label="3">三级</el-checkbox>
                    <el-checkbox :disabled='disabled' style="margin-left: 0" label="4">四级</el-checkbox>
                    <el-checkbox :disabled='disabled' style="margin-left: 0" label="5">五级</el-checkbox> -->
              </el-checkbox-group>
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-else
                v-model="addUserStep2Form.formData.smsUserHyBlackLevel1"
                disabled
              >
                <el-checkbox
                  v-for="(item, index) in level"
                  :key="index"
                  style="margin-left: 0"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
                <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
              </el-checkbox-group>
            </el-form-item>
            <!-- 营销 -->
            <el-form-item
              label="验证营销黑号"
              style="width: 360px"
              prop="smsIsUserBlack"
            >
              <el-radio-group
                v-if="roleId == 3 || roleId == 4"
                v-model="addUserStep2Form.formData.smsIsUserBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
              <el-radio-group
                v-else
                v-model="addUserStep2Form.formData.smsIsUserBlack"
                disabled
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="黑号级别"
              prop="smsUserHyBlackLevel1"
              v-if="addUserStep2Form.formData.smsIsUserBlack == 1"
            >
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.smsUserBlackLevel1"
                v-if="roleId == 3"
              >
                <el-checkbox
                  v-for="(item, index) in level"
                  :key="index"
                  style="margin-left: 0"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
                <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
              </el-checkbox-group>
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-else
                v-model="addUserStep2Form.formData.smsUserBlackLevel1"
                disabled
              >
                <el-checkbox
                  v-for="(item, index) in level"
                  :key="index"
                  style="margin-left: 0"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
                <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                    <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
              </el-checkbox-group>
            </el-form-item>
            <el-form-item
              label="验证外部黑号"
              style="width: 360px"
              prop="smsIsExternalBlack"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsExternalBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="
                addUserStep2Form.formData.smsIsExternalBlack == '1' &&
                roleId == 3
              "
              label="外部黑号渠道"
              prop="smsExternalBlackSource"
            >
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.smsExternalBlackSource"
              >
                <el-checkbox
                  v-for="(item, index) in externalList"
                  :key="index"
                  :label="item.value"
                  >{{ item.name }}</el-checkbox
                >
              </el-checkbox-group>
            </el-form-item>
          </div>
        </div>
        <div class="float-div-3">
          <div
            v-if="
              clinetroleIds == '12' ||
              clinetroleIds == '14' ||
              clinetroleIds == '22'
            "
          >
            <p
              style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px"
            >
              其他设置
            </p>
            <el-form-item
              label="回执推送账户"
              prop="reportTarget"
              v-if="clinetroleIds == '12'"
            >
              <el-radio-group v-model="addUserStep2Form.formData.reportTarget">
                <el-radio label="0">本账户</el-radio>
                <el-radio label="1" style="margin-left: 10px">子账户</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="扣费账户"
              prop="balanceTarget"
              v-if="clinetroleIds == '12'"
            >
              <el-radio-group v-model="addUserStep2Form.formData.balanceTarget">
                <el-radio label="0">本账户</el-radio>
                <el-radio label="1" style="margin-left: 10px">子账户</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="加密方式"
              prop="encryptType"
              v-if="
                clinetroleIds == '12' ||
                clinetroleIds == '14' ||
                clinetroleIds == '22'
              "
            >
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.encryptType"
                placeholder="请选择加密方式"
              >
                <el-option label="oracle加密" value="1"></el-option>
                <el-option label="AES加密" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="秘钥"
              prop="secretKey"
              v-if="
                secretKeyShow &&
                (clinetroleIds == '12' ||
                  clinetroleIds == '14' ||
                  clinetroleIds == '22')
              "
            >
              <el-input
                class="input-w-3"
                placeholder="请输入秘钥"
                v-model="addUserStep2Form.formData.secretKey"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="偏移量"
              prop="secretIv"
              v-if="
                secretIvShow &&
                (clinetroleIds == '12' ||
                  clinetroleIds == '14' ||
                  clinetroleIds == '22')
              "
            >
              <el-input
                class="input-w-3"
                placeholder="请输入偏移量"
                v-model="addUserStep2Form.formData.secretIv"
              ></el-input>
            </el-form-item>
          </div>
          <div>
            <p
              style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px"
            >
              参数设置
            </p>
            <el-form-item label="扩展号" prop="ext">
              <el-input
                style="width: 224px"
                v-model="addUserStep2Form.formData.ext"
              ></el-input>
            </el-form-item>
            <el-form-item label="支持个性化设置" prop="isIndividualization">
              <el-radio-group
                v-model="addUserStep2Form.formData.isIndividualization"
                class="input-w"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="开启携号转网" prop="isNumberPortability">
                  <el-radio-group v-model="addUserStep2Form.formData.isNumberPortability">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item> -->

            <!-- <el-form-item label="是否掩码展示" prop="mobileMask">
                  <el-radio-group v-model="addUserStep2Form.formData.mobileMask">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item> -->
            <el-form-item label="短信签名位置" prop="signSetting">
              <el-radio-group v-model="addUserStep2Form.formData.signSetting">
                <el-radio label="1">签名前置</el-radio>
                <el-radio label="2" style="margin-left: 10px"
                  >签名后置</el-radio
                >
                <el-radio label="3" style="margin-left: 10px">无限制</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="抹除自定义扩展" prop="smsIsSignatureExtension">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsSignatureExtension"
              >
                <el-radio label="1" style="margin-left: 10px">是</el-radio>
                <el-radio label="0" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="签名扩展设置" prop="smsIsSignatureIdx">
              <el-radio-group
                v-model="addUserStep2Form.formData.smsIsSignatureIdx"
              >
                <el-radio label="0" style="margin-left: 10px"
                  >自动添加序号</el-radio
                >
                <el-radio label="1" style="margin-left: 10px"
                  >不自动添加序号</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="默认签名" prop="sign">
              <el-input
                class="input-w-3"
                placeholder="签名格式为【签名】"
                v-model="addUserStep2Form.formData.sign"
              ></el-input>
            </el-form-item>

            <!-- <el-form-item label="是否抹自定义扩展字段" prop="smsIsSignatureExtension">
                  <el-select
                    clearable
                    class="input-w-2"
                    v-model="addUserStep2Form.formData.smsIsSignatureExtension"
                    filterable
                    placeholder="是否抹自定义扩展字段"
                  >
                    <el-option
                      label="是"
                      value="1"
                    ></el-option>
                    <el-option
                      label="否"
                      value="0"
                    ></el-option>
                  </el-select>
                </el-form-item> -->
          </div>
          <div>
            <p
              style="padding: 0 0 18px 0px; font-weight: bold; font-size: 14px"
            >
              防轰炸设置
            </p>
            <el-form-item label="账号每日成功发送限制" prop="dailyLimited">
              <el-input
                class="input-w-3"
                v-model="addUserStep2Form.formData.dailyLimited"
              ></el-input>
            </el-form-item>
            <el-form-item label="24h号码内容去重" prop="smsIsContentSingle">
              <el-radio-group v-model="smsIsContentSingleflag" @change="change">
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="验证码24h限制（次）" prop="overrunCode">
              <el-input
                class="input-w-3"
                v-model="addUserStep2Form.formData.overrunCode"
              ></el-input>
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item label="行业24h限制（次）" prop="overrunIndustry">
              <el-input
                class="input-w-3"
                v-model="addUserStep2Form.formData.overrunIndustry"
              ></el-input
              ><span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item
              v-if="
                roleId == 3 ||
                roleId == 4 ||
                roleId == 6 ||
                roleId == 7 ||
                roleId == 8
              "
              label="营销24h限制（次）"
              prop="overrunMarket"
            >
              <el-input
                :disabled="
                  roleId == 6 || roleId == 7 || roleId == 8 ? true : false
                "
                class="input-w-3"
                v-model="addUserStep2Form.formData.overrunMarket"
              ></el-input
              ><span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item
              v-if="
                roleId == 3 ||
                roleId == 4 ||
                roleId == 6 ||
                roleId == 7 ||
                roleId == 8
              "
              label="营销7*24h限制（次）"
              prop="overrunTotalMarket"
            >
              <el-input
                class="input-w-3"
                :disabled="
                  roleId == 6 || roleId == 7 || roleId == 8 ? true : false
                "
                v-model="addUserStep2Form.formData.overrunTotalMarket"
              ></el-input
              ><span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
          </div>
        </div>
        <div style="text-align: center" class="clear-div">
          <el-button style="width: 120px" @click="goBack()">返回</el-button>
          <el-button
            type="primary"
            style="margin-left: 20px; width: 120px"
            @click="submitForm('addUserStep2Form')"
            >提 交</el-button
          >
        </div>
      </el-form>
      <el-dialog
        title="短信配置"
        v-model="dialogVisible"
        width="30%"
        :before-close="handleClose"
      >
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="70px"
          class="demo-ruleForm"
        >
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.remark"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitSetting('ruleForm')"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    ElIconArrowLeft,
  },
  name: 'SMSconfiguration',
  data() {
    var checkBlackWords = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('加黑关键字不能为空'))
      } else {
        console.log(value.length)
        if (value.length <= 200) {
          var reg =
            /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,200}$/gi
          if (!reg.test(value)) {
            return callback(
              new Error('加黑关键词包括数字、汉字、字母、多个由英文逗号隔开')
            )
          } else {
            var arr = value.split(',')
            var arr2 = []
            for (var i = 0; i < arr.length; i++) {
              if (arr2.indexOf(arr[i]) < 0) {
                arr2.push(arr[i])
              }
            }
            if (arr2.length != arr.length) {
              return callback(new Error('不允许重复'))
            } else {
              callback()
            }
          }
        }
        return callback(new Error('加黑关键字不能多于200个字'))
      }
    }
    var checkSignature = (rule, value, callback) => {
      var reg = /^(\d+,?)+$/
      if (value) {
        if (!reg.test(value)) {
          callback(new Error('扩展号由1-12位数字组成'))
        } else {
          window.api.get(
            window.path.omcs + 'operatingSignature/checkClientExt',
            { ext: value, userId: this.addUserStep2Form.formData.userId },
            (res) => {
              if (res.data == true) {
                callback(new Error('此扩展号已存在'))
              } else {
                callback()
              }
            }
          )
        }
      } else {
        return callback(new Error('请输入扩展号'))
      }
      // if (!/^\d{1,12}$/.test(value)) {
      //   callback(new Error("扩展号由1-12位数字组成"));
      // } else {

      // }
    }
    var sign = (rule, value, callback) => {
      if (value == '') {
        callback()
      } else {
        if (value[0] != '【' || value[value.length - 1] != '】') {
          callback(new Error('请正确填写签名格式【签名】'))
        } else if (value.length > 22) {
          callback(new Error('签名最多20位'))
        } else if (
          /[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value) ||
          /[·！#￥（——）：；“”‘、，|《。》？、[\]]/im.test(value)
        ) {
          callback(new Error('不能填写特殊字符'))
        } else {
          callback()
        }
      }
    }
    // var splitRatio=(rule,value,callback)=>{
    //      if(value==""){
    //         callback()
    //      }else{
    //          if(!/^(?:[1-9]?\d|100)$/.test(value)){
    //             callback(new Error('请填写0-100整数'))
    //          }else{
    //             callback()
    //          }
    //      }
    // };
    return {
      isFirstEnter: false,
      dialogVisible: false,
      HeaderFlag: true,
      HeaderValueFlag: true,
      count: null,
      countValue: null,
      clinetroleIds: '',
      ruleForm: {
        remark: '',
      },
      rules: {
        remark: [{ required: true, message: '请填写备注', trigger: 'change' }],
      },
      disabled: null,
      level: [
        {
          label: '一级',
          value: '1',
          disabled: false,
        },
        {
          label: '二级',
          value: '2',
          disabled: false,
        },
        {
          label: '三级',
          value: '3',
          disabled: false,
        },
        {
          label: '四级',
          value: '4',
          disabled: false,
        },
        {
          label: '五级',
          value: '5',
          disabled: false,
        },
      ],
      jsonData: {
        consumerName: '',
        compName: '',
      }, //用户资料
      secretKeyShow: false, //判断是否加密
      secretIvShow: false,
      smsIsContentSingleflag: '2',
      externalList: [
        {
          name: '先锋',
          value: 'XF',
        },
        {
          name: '棱镜',
          value: 'LJ',
        },
        {
          name: '河南利加',
          value: 'HNLJ',
        },
      ],
      addUserStep2Form: {
        //弹窗步骤2的数据和验证
        formData: {
          userId: '',
          smsPayMethod: '1',
          smsPrice: '',
          smsProductId: '',
          yzmProductId: '',
          smsChargeback: '1',
          smsIsMobileAttribute: '2',
          yxProductId: '',
          // flag: '5',
          ext: '',
          smsReportLevel: '1',
          smsRepliesUrl: '',
          smsReportUrl: '',
          smsRepliesPackageSize: '',
          smsStrepliesPackageSize: '',
          smsTemplateUrl: '',
          smsSignatureUrl: '',
          smsAuditUrl: '', //短信进审核推送地址
          // 防轰炸
          overrunCode: '0',
          overrunIndustry: '0',
          overrunMarket: '0',
          overrunTotalMarket: '0',
          smsIsContentSingle: false,
          // smsSelect:'',
          businessTypes: '1',
          interfacePassword: '******', // 接口密码
          loginPassWord: '******', // 账号密码
          // ifExt: '1', // 生成扩展码：1，手动输入扩展码：2
          custom: '1', //是否显示自定义发送operatinguse
          isIndividualization: '2', //是否个性化设置
          smsIsOvershoot: '0', //预付费是否允许超发
          smsIsAudit: '1', //是否审核
          sign: '',
          smsAuditNum: 1, //审核号码数
          smsIsRepliesBlack: '2', //是否加黑
          smsRepliesBlackWords: 'TD,t,T,td', //加黑关键词
          smsIsUserBlack: '1', //是否验证黑号
          smsIsExternalBlack: '2',
          smsIsBlackWordsFail: '0',
          smsIsPlatformBlack: '',
          smsUserBlackLevel1: ['1', '2', '3'], //黑号验证级别存储
          smsExternalBlackSource: [], //外部黑名单渠道
          passOperator: [], //下发运营商
          smsIsSignatureReport: '0',
          smsUserBlackLevel: '', //黑号验证级别存储
          // isNumberPortability: "0", //携号转网
          smsIsChannelCode: '1', //返回错误通道码
          isReceiptDetails: '1', //长短信回执
          encryptType: '', //加密方式
          secretKey: '', //秘钥
          secretIv: '', //偏移量
          mobileMask: '1', //掩码展示
          smsIsBlackWords: '1', //敏感词是否审核
          reportTarget: '1', //回执推送账户
          // splitDb:'1',
          balanceTarget: '1', //扣费账户
          smsPushType: '2', //推送方式
          signSetting: '1', //短信签名位置
          reportPushSetting: '0', //回执推送
          replyPushSetting: '0', //回复推送
          reportCrawlSetting: '0', //回执抓取
          replyCrawlSetting: '0', //回复抓取
          smsIsSignatureExtension: '',
          smsIsSignatureIdx: '0', //账号签名序号设置
          smsIsUserYzmBlack: '1', //验证验证码系统黑号
          smsUserYzmBlackLevel: '', //验证验证码黑号级别
          smsUserYzmBlackLevel1: ['1', '2', '3'],
          smsIsUserHyBlack: '1', //验证行业系统黑号
          smsUserHyBlackLevel: '', //验证行业黑号级别
          smsUserHyBlackLevel1: ['1', '2', '3'],
          smsPushHeader: [
            {
              headKey: '',
              headValue: '',
            },
          ],
          pushHeader: '0',
          headKey: '',
          headValue: '',
          headKey1: '',
          headValue1: '',
          headKey2: '',
          headValue2: '',
          headKey3: '',
          headValue3: '',
          headKey4: '',
          headValue4: '',
          dailyLimited: '', //每日限量
          // pushHeader:{
          //   headKey:"",
          //   headValue:""
          // },
          // pushHeader2:{
          //   headKey:"",
          //   headValue:""
          // },
          // pushHeader3:{
          //   headKey:"",
          //   headValue:""
          // },
          // pushHeader4:{
          //   headKey:"",
          //   headValue:""
          // },
          // pushHeader5:{
          //   headKey:"",
          //   headValue:""
          // },
          // splitRatio:'',
          // provinceRatio:'',
          // remark:'', //备注
        },
        formRule: {
          sign: [{ required: false, validator: sign, trigger: 'change' }],
          smsPayMethod: [
            { required: true, message: '请选择付费方式', trigger: 'change' },
          ],
          smsPrice: [
            { required: false, message: '请输入用户单价', trigger: 'change' },
            {
              pattern:
                /^(([1-9][0-9]{0,1})|(([0]\.\d{1,5}|[1-9][0-9]{0,1}\.\d{1,5})))$/gi,
              message: '用户单价为2位整数或5位以内小数',
            },
          ],
          smsIsMobileAttribute: [
            { required: true, message: '请选择机型', trigger: 'change' },
          ],
          smsIsSignatureReport: [
            { required: true, message: '发送签名限制必填', trigger: 'blur' },
          ],
          smsPushType: [
            { required: true, message: '推送方式必填！', trigger: 'blur' },
          ],

          // headKey: [
          //   { required: true, message: "headKey不能为空！", trigger: "blur" },
          // ],
          // headValue: [
          //   { required: true, message: "headValue不能为空！", trigger: "blur" },
          // ],
          reportTarget: [
            { required: true, message: '回执推送账户必填！', trigger: 'blur' },
          ],
          balanceTarget: [
            { required: true, message: '扣费账户必填！', trigger: 'blur' },
          ],
          smsIsBlackWords: [
            {
              required: true,
              message: '请选择敏感词是否审核',
              trigger: 'blur',
            },
          ],
          smsIsRepliesBlack: [
            { required: true, message: '请选择是否加黑', trigger: 'blur' },
          ],
          smsIsUserBlack: [
            {
              required: true,
              message: '请选择是否验证营销系统黑号',
              trigger: 'change',
            },
          ],
          smsIsBlackWordsFail: [
            {
              required: true,
              message: '请选择敏感词是否进审核',
              trigger: 'change',
            },
          ],
          smsIsUserYzmBlack: [
            {
              required: true,
              message: '请选择验证码系统黑号是否验证',
              trigger: 'change',
            },
          ],
          smsIsUserHyBlack: [
            {
              required: true,
              message: '请选择是否验证行业系统黑号',
              trigger: 'change',
            },
          ],
          smsUserBlackLevel1: [
            {
              required: true,
              message: '请选择营销黑号验证级别',
              trigger: 'change',
            },
          ],
          smsUserYzmBlackLevel1: [
            {
              required: true,
              message: '请选择验证码黑号验证级别',
              trigger: 'change',
            },
          ],
          smsUserHyBlackLevel1: [
            {
              required: true,
              message: '请选择行业黑号验证级别',
              trigger: 'change',
            },
          ],
          smsIsExternalBlack: [
            {
              required: true,
              message: '请选择是否验证外部黑名单',
              trigger: 'change',
            },
          ],
          smsIsPlatformBlack: [
            {
              required: true,
              message: '请选择是否验证用户黑号',
              trigger: 'change',
            },
          ],
          isIndividualization: [
            {
              required: true,
              message: '请选择是否个性化设置',
              trigger: 'blur',
            },
          ],
          ext: [
            { required: true, validator: checkSignature, trigger: ['blur'] },
          ],
          smsRepliesUrl: [
            {
              required: false,
              message: '请输入上行地址',
              trigger: ['change', 'blur'],
            },
            {
              min: 1,
              max: 255,
              message: '长度在 255 个字符以内',
              trigger: ['blur', 'change'],
            },
          ],
          smsReportUrl: [
            {
              required: false,
              message: '请输入下行地址',
              trigger: ['change', 'blur'],
            },
            {
              min: 1,
              max: 255,
              message: '长度在 255 个字符以内',
              trigger: ['blur', 'change'],
            },
          ],
          smsProductId: [
            { required: true, message: '请选择通道', trigger: 'change' },
          ],
          yzmProductId: [
            { required: true, message: '请选择通道', trigger: 'change' },
          ],
          yxProductId: [
            { required: true, message: '请选择通道', trigger: 'change' },
          ],
          smsChargeback: [
            { required: true, message: '请选择计费方式', trigger: 'change' },
          ],
          // smsSelect:[
          //     {required:true,message:'请选择通道名称',trigger:'change'}
          // ],
          smsRepliesBlackWords: [
            { required: true, validator: checkBlackWords, trigger: 'change' },
          ],
          smsIsAudit: [
            { required: true, message: '请选择是否审核', trigger: 'blur' },
          ],
          // isNumberPortability: [
          //   { required: true, message: "请选择是否携号转网", trigger: "blur" },
          // ],
          smsIsChannelCode: [
            {
              required: true,
              message: '请选择是否返回通道错误码',
              trigger: 'blur',
            },
          ],
          isReceiptDetails: [
            {
              required: true,
              message: '请选择是否接收长短信回执',
              trigger: 'blur',
            },
          ],
          secretKey: [
            { required: true, message: '请输入秘钥', trigger: 'blur' },
          ],
          secretIv: [
            { required: true, message: '请输入偏移量', trigger: 'blur' },
          ],
          mobileMask: [
            { required: true, message: '请选择是否掩码展示', trigger: 'blur' },
          ],
          smsAuditNum: [
            { required: true, message: '请输入审核条数', trigger: 'change' },
          ],
          signSetting: [
            { required: true, message: '请选择签名位置', trigger: 'blur' },
          ],
          reportPushSetting: [
            {
              required: true,
              message: '请选择回执推送方式',
              trigger: 'change',
            },
          ],
          replyPushSetting: [
            {
              required: true,
              message: '请选择回复推送方式',
              trigger: 'change',
            },
          ],
          reportCrawlSetting: [
            {
              required: true,
              message: '请选择回执抓取方式',
              trigger: 'change',
            },
          ],
          replyCrawlSetting: [
            {
              required: true,
              message: '请选择回复抓取方式',
              trigger: 'change',
            },
          ],
          smsIsSignatureIdx: [
            {
              required: true,
              message: '请选择签名扩展设置',
              trigger: 'change',
            },
          ],
          // splitRatio:[
          //     {required:false,validator:splitRatio,trigger:'change'},
          // ],
          // provinceRatio:[
          //     {required:false,trigger:['change','blur']},
          //     { min: 1, max: 200, message: '长度在 200 个字符以内', trigger: ['blur','change'] },
          // ],
          // remark:[
          //     {required:false,trigger:['change','blur']},
          //     { min: 1, max: 70, message: '长度在 70 个字符以内', trigger: ['blur','change'] },
          // ],
        },
      },
      channel: [], //通道
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs +
          'operatinguser/' +
          this.addUserStep2Form.formData.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName
          this.jsonData.compName = res.data.compName
        }
      )
    },
    change(val) {
      console.log(val)
      if (val == 1) {
        this.addUserStep2Form.formData.smsIsContentSingle = true
      } else {
        this.addUserStep2Form.formData.smsIsContentSingle = false
      }
    },
    /**获取通道列表 */
    getChannel() {
      window.api.get(
        window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
        {},
        (res) => {
          this.channel = res.data
        }
      )
    },
    //返回
    goBack() {
      this.$router.push({
        path: 'UserDetail',
        query: { id: this.$route.query.id },
      })
    },
    //编辑的赋值
    handleEdit() {
      window.api.get(
        window.path.omcs + 'operatinguser/userSmsInfo/' + this.$route.query.id,
        {},
        (res) => {
          for (let i in res.data) {
            if (
              !(
                res.data[i] == null ||
                i == 'smsProductId' ||
                i == 'yzmProductId' ||
                i == 'yxProductId' ||
                i == 'smsUserBlackLevel'
              )
            ) {
              res.data[i] += ''
            }
          }
          res.data['smsUserBlackLevel1'] = res.data.smsUserBlackLevel.split(',')
          res.data['smsUserYzmBlackLevel1'] =
            res.data.smsUserYzmBlackLevel.split(',')
          res.data['smsUserHyBlackLevel1'] =
            res.data.smsUserHyBlackLevel.split(',')
          let obj = res.data
          obj.pushHeader = '0'
          obj.headKey = ''
          obj.headValue = ''
          obj.headKey1 = ''
          obj.headValue1 = ''
          obj.headKey2 = ''
          obj.headValue2 = ''
          obj.headKey3 = ''
          obj.headValue3 = ''
          obj.headKey4 = ''
          obj.headValue4 = ''
          this.addUserStep2Form.formData = obj
          if (obj.smsPushHeader) {
            this.addUserStep2Form.formData.smsPushHeader = JSON.parse(
              res.data.smsPushHeader
            )
            if (this.addUserStep2Form.formData.smsPushHeader.length == 1) {
              this.addUserStep2Form.formData.pushHeader = '0'
              this.addUserStep2Form.formData.headKey =
                this.addUserStep2Form.formData.smsPushHeader[0].headKey
              this.addUserStep2Form.formData.headValue =
                this.addUserStep2Form.formData.smsPushHeader[0].headValue
            } else if (
              this.addUserStep2Form.formData.smsPushHeader.length == 2
            ) {
              this.addUserStep2Form.formData.pushHeader = '1'
              this.addUserStep2Form.formData.headKey =
                this.addUserStep2Form.formData.smsPushHeader[0].headKey
              this.addUserStep2Form.formData.headValue =
                this.addUserStep2Form.formData.smsPushHeader[0].headValue
              this.addUserStep2Form.formData.headKey1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headKey
              this.addUserStep2Form.formData.headValue1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headValue
            } else if (
              this.addUserStep2Form.formData.smsPushHeader.length == 3
            ) {
              this.addUserStep2Form.formData.pushHeader = '2'
              this.addUserStep2Form.formData.headKey =
                this.addUserStep2Form.formData.smsPushHeader[0].headKey
              this.addUserStep2Form.formData.headValue =
                this.addUserStep2Form.formData.smsPushHeader[0].headValue
              this.addUserStep2Form.formData.headKey1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headKey
              this.addUserStep2Form.formData.headValue1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headValue
              this.addUserStep2Form.formData.headKey2 =
                this.addUserStep2Form.formData.smsPushHeader[2].headKey
              this.addUserStep2Form.formData.headValue2 =
                this.addUserStep2Form.formData.smsPushHeader[2].headValue
            } else if (
              this.addUserStep2Form.formData.smsPushHeader.length == 4
            ) {
              this.addUserStep2Form.formData.pushHeader = '3'
              this.addUserStep2Form.formData.headKey =
                this.addUserStep2Form.formData.smsPushHeader[0].headKey
              this.addUserStep2Form.formData.headValue =
                this.addUserStep2Form.formData.smsPushHeader[0].headValue
              this.addUserStep2Form.formData.headKey1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headKey
              this.addUserStep2Form.formData.headValue1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headValue
              this.addUserStep2Form.formData.headKey2 =
                this.addUserStep2Form.formData.smsPushHeader[2].headKey
              this.addUserStep2Form.formData.headValue2 =
                this.addUserStep2Form.formData.smsPushHeader[2].headValue
              this.addUserStep2Form.formData.headKey3 =
                this.addUserStep2Form.formData.smsPushHeader[3].headKey
              this.addUserStep2Form.formData.headValue3 =
                this.addUserStep2Form.formData.smsPushHeader[3].headValue
            } else if (
              this.addUserStep2Form.formData.smsPushHeader.length == 5
            ) {
              this.addUserStep2Form.formData.pushHeader = '4'
              this.addUserStep2Form.formData.headKey =
                this.addUserStep2Form.formData.smsPushHeader[0].headKey
              this.addUserStep2Form.formData.headValue =
                this.addUserStep2Form.formData.smsPushHeader[0].headValue
              this.addUserStep2Form.formData.headKey1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headKey
              this.addUserStep2Form.formData.headValue1 =
                this.addUserStep2Form.formData.smsPushHeader[1].headValue
              this.addUserStep2Form.formData.headKey2 =
                this.addUserStep2Form.formData.smsPushHeader[2].headKey
              this.addUserStep2Form.formData.headValue2 =
                this.addUserStep2Form.formData.smsPushHeader[2].headValue
              this.addUserStep2Form.formData.headKey3 =
                this.addUserStep2Form.formData.smsPushHeader[3].headKey
              this.addUserStep2Form.formData.headValue3 =
                this.addUserStep2Form.formData.smsPushHeader[3].headValue
              this.addUserStep2Form.formData.headKey4 =
                this.addUserStep2Form.formData.smsPushHeader[4].headKey
              this.addUserStep2Form.formData.headValue4 =
                this.addUserStep2Form.formData.smsPushHeader[4].headValue
            } else if (
              this.addUserStep2Form.formData.smsPushHeader.length == 0
            ) {
              this.addUserStep2Form.formData.pushHeader = '-1'
              this.addUserStep2Form.formData.headKey = ''
              this.addUserStep2Form.formData.headValue = ''
              this.addUserStep2Form.formData.headKey1 = ''
              this.addUserStep2Form.formData.headValue1 = ''
              this.addUserStep2Form.formData.headKey2 = ''
              this.addUserStep2Form.formData.headValue2 = ''
              this.addUserStep2Form.formData.headKey3 = ''
              this.addUserStep2Form.formData.headValue3 = ''
              this.addUserStep2Form.formData.headKey4 = ''
              this.addUserStep2Form.formData.headValue4 = ''
            }
          } else {
            console.log(
              this.addUserStep2Form.formData.smsPushHeader,
              'this.addUserStep2Form.formData.smsPushHeader'
            )
            this.addUserStep2Form.formData.pushHeader = '0'
            this.addUserStep2Form.formData.smsPushHeader = [
              {
                headKey: '',
                headValue: '',
              },
            ]
            this.addUserStep2Form.formData.headKey =
              this.addUserStep2Form.formData.smsPushHeader[0].headKey
            this.addUserStep2Form.formData.headValue =
              this.addUserStep2Form.formData.smsPushHeader[0].headValue
          }
          if (res.data.smsIsExternalBlack) {
            this.addUserStep2Form.formData.smsIsExternalBlack =
              res.data.smsIsExternalBlack
          } else {
            this.addUserStep2Form.formData.smsIsExternalBlack = '2'
          }
          if (res.data.smsIsContentSingle == 'false') {
            this.smsIsContentSingleflag = '2'
          } else {
            this.smsIsContentSingleflag = '1'
          }
          if (res.data.smsRepliesBlackWords == null) {
            this.addUserStep2Form.formData.smsRepliesBlackWords = 'TD,t,T,td'
          }
          if (res.data.isReceiptDetails == null) {
            this.addUserStep2Form.formData.isReceiptDetails = '1'
          }
          if (res.data.smsIsSignatureIdx) {
            this.addUserStep2Form.formData.smsIsSignatureIdx =
              res.data.smsIsSignatureIdx
          } else {
            this.addUserStep2Form.formData.smsIsSignatureIdx = '0'
          }
          if (res.data.smsIsMobileAttribute) {
            this.addUserStep2Form.formData.smsIsMobileAttribute =
              res.data.smsIsMobileAttribute
          } else {
            this.addUserStep2Form.formData.smsIsMobileAttribute = '2'
          }
          if (res.data.smsIsChannelCode) {
            this.addUserStep2Form.formData.smsIsChannelCode =
              res.data.smsIsChannelCode
          } else {
            this.addUserStep2Form.formData.smsIsChannelCode = '1'
          }
          if (res.data.smsIsBlackWordsFail) {
            this.addUserStep2Form.formData.smsIsBlackWordsFail =
              res.data.smsIsBlackWordsFail
          } else {
            this.addUserStep2Form.formData.smsIsBlackWordsFail = '0'
          }
          if (res.data.smsExternalBlackSource) {
            this.addUserStep2Form.formData.smsExternalBlackSource =
              res.data.smsExternalBlackSource.split(',')
            let list = ['XF', 'LJ', 'HNLJ']
            let sortList =
              this.addUserStep2Form.formData.smsExternalBlackSource.map(
                (item) => {
                  return item
                }
              )
            let smsExternalBlackSource = sortList.concat(
              list.filter(function (v) {
                return !(sortList.indexOf(v) > -1)
              })
            )
            this.externalList = smsExternalBlackSource.map((item) => {
              return {
                name:
                  item == 'XF'
                    ? '先锋'
                    : item == 'LJ'
                    ? '棱镜'
                    : item == 'HNLJ'
                    ? '河南利加'
                    : '',
                value: item,
              }
            })
          } else {
            this.addUserStep2Form.formData.smsExternalBlackSource = []
            this.externalList = [
              {
                name: '先锋',
                value: 'XF',
              },
              {
                name: '棱镜',
                value: 'LJ',
              },
              {
                name: '河南利加',
                value: 'HNLJ',
              },
            ]
          }
          if (res.data.passOperator) {
            this.addUserStep2Form.formData.passOperator =
              res.data.passOperator.split(',')
          } else {
            this.addUserStep2Form.formData.passOperator = []
          }
        }
      )

      // this.checkuserNameID = this.$route.query.d;
      // this.checkuserClientId = this.$route.query.d;
      // window.api.get(window.path.omcs+'operatinguser/'+ this.checkuserNameID,{},res=>{
      //     // console.log(JSON.parse(res.data))//
      //     let userData= JSON.parse(res.data)[0];
      //     console.log(userData);//用户编辑赋值前的转换
      //     if(!userData.smsPrice){
      //         userData.smsPrice='0'
      //     }
      // this.addUserStep2Form.formData.userId = userData.userId;
      // this.addUserStep1Form.formData.salesName = '';
      // for(let k in this.addUserStep1Form.formData){
      //     if(k=='industryId' || k== 'salesName' || k=='service'){
      //         if(userData[k]==null){
      //             this.addUserStep1Form.formData[k]=''
      //         }
      //         else {
      //             this.addUserStep1Form.formData[k]=parseInt(userData[k])
      //             this.addUserStep1Form.formData.salesName= userData.salesName
      //         }
      //     }else {
      //         this.addUserStep1Form.formData[k]=String(userData[k]).replace('null','').replace('undefined','');
      //     }
      // }
      // for(let i in this.addUserStep2Form.formData){
      //     if(i=='smsProductId'){
      //         this.addUserStep2Form.formData[i]=parseInt(userData[i])
      //     }else if(i=='reportPushSetting' || i=='replyPushSetting' || i=='reportCrawlSetting' || i=='replyCrawlSetting'){
      //         if(userData[i]==''){
      //             this.addUserStep2Form.formData[i]='0'
      //         }else {
      //             this.addUserStep2Form.formData[i]=userData[i]
      //         }
      //     }else {
      //         this.addUserStep2Form.formData[i]=String(userData[i]).replace('null','').replace('undefined','')
      //     }
      // }
      // this.addUserStep2Form.formData.smsBlackWordLevel = userData.smsBlackWordLevel;
      // this.addUserStep2Form.formData.ifExt = '1';//默认选中“生成扩展码”
      // this.oldOditCode = this.addUserStep2Form.formData.ext
      // if(userData.smsUserBlackLevel){
      //     this.addUserStep2Form.formData.smsUserBlackLevel1 = userData.smsUserBlackLevel.split(',');
      // }else{
      //     this.addUserStep2Form.formData.smsUserBlackLevel1=[]
      // }
      // this.addUserStep2Form.formData.interfacePassword = userData.password;
      // this.addUserStep2Form.formData.loginPassWord = userData.loginPassword;

      // if(userData.flag == undefined || userData.flag == ''){//若原本无扩展码，则手动附'5';
      //     console.log(this.addUserStep2Form.formData.flag);
      //     this.addUserStep2Form.formData.flag = '5';
      // }
      // })
    },
    sortByFixedOrder(array, order) {
      const map = new Map()
      // 创建一个映射，将数组元素与其在order数组中的索引关联起来
      for (let i = 0; i < order.length; i++) {
        map.set(order[i], i)
      }
      // 使用映射中元素的索引进行排序
      array.sort((a, b) => map.get(a) - map.get(b))
      return array
    },
    handelYzmBlack(e) {
      // this.addUserStep2Form.formData.smsUserHyBlackLevel1 = e
      // this.addUserStep2Form.formData.smsUserBlackLevel1 = e
      // let arr1 = e
      // let arr2 = this.addUserStep2Form.formData.smsUserHyBlackLevel1
      // let arr3 = this.addUserStep2Form.formData.smsUserBlackLevel1
      // let newArr = [...new Set(arr1.concat(arr2,arr3))]
      // this.addUserStep2Form.formData.smsUserHyBlackLevel1 = newArr
      // this.addUserStep2Form.formData.smsUserBlackLevel1 = newArr
    },
    handelHymBlack(val) {
      // let arr1 = val
      // let arr2 = this.addUserStep2Form.formData.smsUserBlackLevel1
      // let newArr = [...new Set(arr1.concat(arr2))]
      // this.addUserStep2Form.formData.smsUserBlackLevel1 = newArr
    },
    handelChange(e) {
      // if(e){
      //   this.HeaderFlag = true
      // }else{
      //   this.HeaderFlag = false
      // }
    },
    handelChangeValue(e) {
      // if(e){
      //   this.HeaderValueFlag = true
      // }else{
      //   this.HeaderValueFlag = false
      // }
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          if (this.addUserStep2Form.formData.smsReportLevel != 2) {
            //消息报告如果不选批量推送时，重置批量推送下面的推送方式，上下行地址
            this.addUserStep2Form.formData.smsPushType = '2'
            this.addUserStep2Form.formData.smsRepliesUrl = ''
            this.addUserStep2Form.formData.smsReportUrl = ''
          }
          if (this.addUserStep2Form.formData.smsReportLevel == 1) {
            this.addUserStep2Form.formData.smsRepliesPackageSize = ''
            this.addUserStep2Form.formData.smsStrepliesPackageSize = ''
          }
          this.addUserStep2Form.formData.smsUserBlackLevel =
            this.addUserStep2Form.formData.smsUserBlackLevel1.join(',')
          this.addUserStep2Form.formData.smsUserYzmBlackLevel =
            this.addUserStep2Form.formData.smsUserYzmBlackLevel1.join(',')
          this.addUserStep2Form.formData.smsUserHyBlackLevel =
            this.addUserStep2Form.formData.smsUserHyBlackLevel1.join(',')
          let header = {
            headKey: this.addUserStep2Form.formData.headKey,
            headValue: this.addUserStep2Form.formData.headValue,
          }
          let header1 = {
            headKey: this.addUserStep2Form.formData.headKey1,
            headValue: this.addUserStep2Form.formData.headValue1,
          }
          let header2 = {
            headKey: this.addUserStep2Form.formData.headKey2,
            headValue: this.addUserStep2Form.formData.headValue3,
          }
          let header3 = {
            headKey: this.addUserStep2Form.formData.headKey3,
            headValue: this.addUserStep2Form.formData.headValue3,
          }

          let header4 = {
            headKey: this.addUserStep2Form.formData.headKey3,
            headValue: this.addUserStep2Form.formData.headValue3,
          }
          let arr = [header, header1, header2, header3, header4]
          if (this.addUserStep2Form.formData.pushHeader == '0') {
            this.addUserStep2Form.formData.smsPushHeader = arr.slice(0, 1)
          } else if (this.addUserStep2Form.formData.pushHeader == '1') {
            this.addUserStep2Form.formData.smsPushHeader = arr.slice(0, 2)
          } else if (this.addUserStep2Form.formData.pushHeader == '2') {
            this.addUserStep2Form.formData.smsPushHeader = arr.slice(0, 3)
          } else if (this.addUserStep2Form.formData.pushHeader == '3') {
            this.addUserStep2Form.formData.smsPushHeader = arr.slice(0, 4)
          } else if (this.addUserStep2Form.formData.pushHeader == '4') {
            this.addUserStep2Form.formData.smsPushHeader = arr.slice(0, 5)
          } else if (this.addUserStep2Form.formData.pushHeader == '-1') {
            this.addUserStep2Form.formData.smsPushHeader = []
          }
          this.dialogVisible = true

          // this.$confirms.confirmation(
          //   "put",
          //   "确认修改短信配置？",
          //   window.path.omcs + "operatinguser/userSmsInfo",
          //   this.addUserStep2Form.formData,
          //   (res) => {
          //     //返回用户管理页面
          //     this.goBack();
          //   }
          // );
        } else {
          return false
        }
      })
    },

    submitSetting(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addUserStep2Form.formData.remark = this.ruleForm.remark
          // let arr = this.sortByFixedOrder(this.addUserStep2Form.formData.smsExternalBlackSource,this.externalList)
          this.addUserStep2Form.formData.smsExternalBlackSource =
            this.addUserStep2Form.formData.smsExternalBlackSource.join(',')
          this.addUserStep2Form.formData.passOperator =
            this.addUserStep2Form.formData.passOperator.join(',')
          window.api.put(
            window.path.omcs + 'operatinguser/userSmsInfo',
            this.addUserStep2Form.formData,
            (res) => {
              if (res.code == 200) {
                //返回用户管理页面
                this.dialogVisible = false
                this.goBack()
              } else {
                this.$message.error(res.msg)
              }
            }
          )
          //   this.$confirms.confirmation(
          //   "put",
          //   "确认修改短信配置？",
          //   window.path.omcs + "operatinguser/userSmsInfo",
          //   this.addUserStep2Form.formData,
          //   (res) => {
          //     this.dialogVisible = false
          //     this.goBack();
          //   }
          // );
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    getInfo() {
      window.api.get(
        window.path.omcs + 'operatinguser/userInfo/' + this.$route.query.id,
        {},
        (res) => {
          this.clinetroleIds = res.data.roleId
          // this.channel = res.data;
        }
      )
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.addUserStep2Form.formData.userId = this.$route.query.id
      // this.addUserStep2Form.formData.smsPushHeader.forEach((item,index)=>{
      //   if(item.headKey == ''){
      //     this.HeaderFlag = true
      //   }else{
      //     this.HeaderFlag = false
      //   }
      //   if(item.headValue == ''){
      //     this.HeaderValueFlag = true
      //   }else{
      //     this.HeaderValueFlag = false
      //   }
      // })
      this.getInfo()
      this.getChannel() /**获取通道列表 */
      this.handleEdit()
      this.getBasicData()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.addUserStep2Form.formData.userId = this.$route.query.id
        //   this.addUserStep2Form.formData.smsPushHeader.forEach((item,index)=>{
        //   if(item.headKey == ''){
        //     this.HeaderFlag = true
        //   }else{
        //     this.HeaderFlag = false
        //   }
        //   if(item.headValue == ''){
        //     this.HeaderValueFlag = true
        //   }else{
        //     this.HeaderValueFlag = false
        //   }
        // })
        this.getInfo()
        this.getChannel() /**获取通道列表 */
        this.handleEdit()
        this.getBasicData()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    // console.log(11111);
  },
  watch: {
    'addUserStep2Form.formData.encryptType'(val) {
      if (val == 1) {
        this.secretKeyShow = true
        this.secretIvShow = false
      } else if (val == 2) {
        this.secretKeyShow = true
        this.secretIvShow = true
      } else {
        this.secretKeyShow = false
        this.secretIvShow = false
        this.addUserStep2Form.formData.secretKey = ''
        this.addUserStep2Form.formData.secretIv = ''
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$refs['ruleForm'].resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.input-w {
  width: 345px !important;
}
.el-radio-group .el-radio {
  margin-right: 0px;
}
.fillet {
  padding: 20px;
}
.input-w-2 {
  width: 200px !important;
}
.input-w-3 {
  width: 200px !important;
}
.input-w-4 {
  width: 190px !important;
}
.input-w-sm {
  width: 126px !important;
}
.input-w-f {
  width: 80px !important;
}
.float-div-1 {
  float: left;
}
.float-div-2 {
  float: left;
}
.float-div-3 {
  float: left;
}
.clear-div {
  clear: both;
}
.red {
  color: red;
}
@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>

<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>
