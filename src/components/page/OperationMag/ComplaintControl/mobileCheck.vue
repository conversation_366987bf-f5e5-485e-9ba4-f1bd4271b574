<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInlineRef"
          label-width="100px"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="渠道" prop="source">
            <el-select
              v-model="formInline.source"
              placeholder="请选择"
              clearable
              class="input-w"
              @change="handleChange"
            >
              <el-option label="先丰" value="XF"></el-option>
              <el-option label="棱镜" value="LJ"></el-option>
              <el-option label="流信云" value="LXY"></el-option>
              <el-option label="联麓" value="LL"></el-option>
              <el-option label="棱镜联电" value="LJ_LD"></el-option>
              <el-option label="贰伍叁" value="EWS"></el-option>
              <el-option label="正和顺" value="ZHS"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option
                v-for="item in statusList"
                :label="item.description"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <el-date-picker
              v-model="formInline.createTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              @change="handleTimeChange"
              :clearable="false"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button type="primary" plain @click="Reload(formInlineRef)"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div>
        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons></template>
        </vxe-toolbar>
        <vxe-table
          ref="tableRef"
          id="mobileCheckTable"
          border
          stripe
          :custom-config="{ storage: true }"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
        >
          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="110px">
            <template v-slot="scope">
              <div>
                {{ scope.row.phone }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="渠道" title="渠道">
            <template v-slot="scope">
              <div v-if="scope.row.source == 'XF'">先丰</div>
              <div v-if="scope.row.source == 'LJ'">棱镜</div>
              <div v-if="scope.row.source == 'LXY'">流信云</div>
              <div v-if="scope.row.source == 'LL'">联麓</div>
              <div v-if="scope.row.source == 'LJ_LD'">棱镜联电</div>
              <div v-if="scope.row.source == 'EWS'">贰伍叁</div>
              <div v-if="scope.row.source == 'ZHS'">正和顺 </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                type="primary"
                effect="light"
              >
                {{scope.row.statusDesc}}
              </el-tag>
              <!-- <el-tag
:disable-transitions="true"
                v-if="scope.row.status == 0"
                type="primary"
                effect="plain"
              >
                空号
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 1" type="primary" effect="dark">
                实号
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 2" type="danger" effect="dark">
                停机
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 3" type="info" effect="dark">
                未知
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 4" type="info" effect="light">
                沉默号
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 5" type="warning" effect="dark">
                风险号
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-if="scope.row.status == 6"
                type="warning"
                effect="light"
              >
                疑似空号
              </el-tag> -->
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
              }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button link style="color: #409eff" @click="edit(scope.row)"
                >编 辑</el-button
              >
              <el-button link style="color: red" @click="cancel(scope.row)"
                >删 除</el-button
              >
            </template>
          </vxe-column> -->
        </vxe-table>
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import moment from "moment";
import { ref } from "vue";
import { reactive } from "vue";

const formInlineRef = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const formInline = reactive({
  userName: "",
  phone: "",
  source: "",
  status: "",
  timeEnd: moment().format("YYYY-MM-DD HH:mm:ss"),
  timeStart: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
  createTime: [
    moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
    moment().format("YYYY-MM-DD HH:mm:ss"),
  ],
  currentPage: 1,
  pageSize: 10,
});
const statusList = ref([]);
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
  total: 0,
});
const handleSelectionChange = () => {};
const handleSizeChange = (size) => {
  formInline.pageSize = size;
  gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  formInline.currentPage = currentPage;
  gettableLIst();
};
const gettableLIst = () => {
  //获取行业类型归属列表
  tableDataObj.loading2 = true;
  window.api.post(
    window.path.omcs + "operatingmobilecheck/page",
    formInline,
    (res) => {
      tableDataObj.loading2 = false;
      tableDataObj.total = res.data.total;
      tableDataObj.tableData = res.data.records;
    }
  );
};
const Query = () => {
  gettableLIst();
};
const Reload = () => {
  formInlineRef.value.resetFields();
  statusList.value = [];
  formInline.timeStart = moment().startOf("day").format("YYYY-MM-DD 00:00:00");
  formInline.timeEnd = moment().format("YYYY-MM-DD HH:mm:ss");
  gettableLIst();
};
const handleTimeChange = (val) => {
  if (val) {
    formInline.timeStart = moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
    formInline.timeEnd = moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
  } else {
    formInline.timeStart = "";
    formInline.timeEnd = "";
  }
  //   gettableLIst();
};
const getStatusCodeList = (source) => {
  window.api.get(
    window.path.omcs + "operatingmobilecheck/source/status/" + source,
    {},
    (res) => {
        if(res.code == 200){
            statusList.value = res.data;
        }
    }
  );
};
const handleChange = (val) => {
  if (val) {
    getStatusCodeList(val);
  } else {
    statusList.value = [];
  }
};
onMounted(() => {
  const $table = tableRef.value;
  const $toolbar = toolbarRef.value;
  if ($table && $toolbar) {
    $table.connect($toolbar);
  }
  gettableLIst();
});
</script>

<style lang="less" scoped>
.OuterFrame {
  padding: 20px;
}
</style>