<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="类型" prop="type">
              <el-select v-model="formInline.type" clearable class="input-w">
                <el-option label="全部" value=""></el-option>
                <el-option label="一键登录" value="onelogin"></el-option>
                <el-option label="本机号码校验" value="onepass"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="账号名称" prop="username">
              <el-input
                v-model="formInline.username"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="APPID" prop="appid">
              <el-input
                v-model="formInline.appid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click="ListSearch"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>

        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem> -->
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            stripe
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="FlashTestApp"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData">

            <vxe-column field="账号名称" title="账号名称">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.username"
                  :content="scope.row.username"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </template>
            </vxe-column>
            <vxe-column field="APP名称" title="APP名称">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.appName"
                  :content="scope.row.appName"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </template>
            </vxe-column>
            <vxe-column field="APPID" title="APPID">
              <template v-slot="scope">
                <div style="display: flex">
                  <Tooltip
                    v-if="scope.row.appid"
                    :content="scope.row.appid"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- <i v-if="scope.row.appid" style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.appid,$event )"></i> -->
                  <CopyTemp :content="scope.row.appid" />
                </div>
              </template>
            </vxe-column>
            <vxe-column field="类型" title="类型">
              <template v-slot="scope">
                <div v-if="scope.row.type == 'onelogin'">一键登录</div>
                <div v-else>本机号码校验</div>
              </template>
            </vxe-column>
            <vxe-column field="提交时间" title="提交时间" width="170">
              <template v-slot="scope">
                <div v-if="scope.row.createTime">{{
                  moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
                }}</div>
              </template>
            </vxe-column>
            <vxe-column field="状态" title="状态">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.status == 'illegal'"
                  type="danger"
                  effect="dark"
                  
                >
                  审核不通过
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 'pass'"
                  type="success"
                  effect="dark"
                  
                >
                  审核通过
                </el-tag>
                <el-tag
                  :disable-transitions="true" v-else type="info" effect="dark" > 未审核 </el-tag>
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../utils/clipboard'
import moment from 'moment'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    Tooltip,
    CopyTemp
  },
  name: 'FlashTestApp',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      // 搜索数据
      formInline: {
        type: '',
        appid: '',
        username: '',
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        type: '',
        appid: '',
        username: '',
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          { prop: 'username', showName: '账号名称', fixed: false },
          { prop: 'appName', showName: 'APP名称', fixed: false },
          { prop: 'appid', showName: 'appid', fixed: false },
          {
            prop: 'type',
            showName: '类型',
            fixed: false,
            formatData: function (val) {
              if (val == 'onelogin') {
                return '一键登录'
              } else {
                return '本机号码校验'
              }
            },
          },
          {
            prop: 'createTime',
            showName: '提交时间',
            fixed: false,
            formatData: function (val) {
              return moment(val).format('YYYY-MM-DD HH:mm:ss')
            },
          },
          {
            prop: 'status',
            showName: '状态',
            fixed: false,
            formatData: function (val) {
              if (val == 'illegal') {
                return '审核不通过'
              } else if (val == 'pass') {
                return '审核通过'
              } else {
                return '未审核'
              }
            },
            showCondition: {
              condition: 'illegal',
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '120', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerflash/app/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    //查看详情跳转
    handelOptionButton(val) {
      if (val.methods == 'exportNums') {
        this.$router.push({
          path:
            '/detailShort?code=' + val.row.shortCode + '&id=' + val.row.userId,
        })
      }
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs.formInline.resetFields()
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // handleCopy(name,event){
    //     clip(name, event)
    // },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
  },
  // activated() {
  //     if (this.$route.meta.isBack || !this.isFirstEnter) {
  //         this.$nextTick(() => {
  //             this.InquireList();
  //         });
  //     } else {
  //         this.$route.meta.isBack = false
  //         this.isFirstEnter = false;
  //     }
  // },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
