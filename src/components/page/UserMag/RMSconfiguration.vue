<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title" style="display: flex; align-items: center">
        <div
          @click="goBack()"
          style="
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #16a589;
          "
        >
          <el-icon><ArrowLeftBold /></el-icon>
          <span style="display: inline-block; padding-right: 10px"> 返回</span>
        </div>
        |
        <span style="margin-left: 10px;">视频短信配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="参数设置" name="parameterRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="parameterRef"
            label-width="120px"
          >
            <el-form-item label="通道组名称" prop="videoChannelGroupId">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.videoChannelGroupId"
                filterable
                placeholder="请选择通道名称"
              >
                <el-option
                  v-for="(item, index) in channel"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="计费方式" prop="videoChargeback">
              <el-radio-group
                v-model="addUserStep2Form.formData.videoChargeback"
              >
                <el-radio disabled value="1">提交计费</el-radio>
                <el-radio disabled value="2" style="margin-left: 10px"
                  >成功计费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="付费方式" prop="videoPayMethod">
              <el-radio-group
                v-model="addUserStep2Form.formData.videoPayMethod"
              >
                <el-radio disabled value="1">预付费</el-radio>
                <el-radio disabled value="2" style="margin-left: 10px"
                  >后付费</el-radio
                >
              </el-radio-group>
            </el-form-item>

            <el-form-item label="扩展号" prop="ext">
              <el-input
                style="width: 224px"
                v-model="addUserStep2Form.formData.ext"
              ></el-input>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(parameterRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="状态设置" name="stateRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="stateRef"
            label-width="100px"
          >
            <el-form-item label="消息报告" prop="videoReportLevel">
              <el-radio-group
                v-model="addUserStep2Form.formData.videoReportLevel"
                class="input-w"
              >
                <el-radio value="1">不接收报告</el-radio>
                <el-radio value="2" style="margin-left: 10px"
                  >批量推送</el-radio
                >
                <el-radio value="3" style="margin-left: 10px"
                  >主动抓取</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="上行地址"
              prop="voiceRepliesUrl"
              v-if="addUserStep2Form.formData.videoReportLevel == 2"
            >
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.videoRepliesUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取回复短信的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="下行地址"
              prop="voiceReportUrl"
              v-if="addUserStep2Form.formData.videoReportLevel == 2"
            >
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.videoReportUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取状态报告的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item label="审核信息推送" prop="voiceReportUrl">
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.videoTemplateUrl"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="回复推送数量"
              prop="videoRepliesPackageSize"
              v-if="addUserStep2Form.formData.videoReportLevel == 2"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.videoRepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoRepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="状态推送数量"
              prop="videoStrepliesPackageSize"
              v-if="addUserStep2Form.formData.videoReportLevel == 2"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.videoStrepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoStrepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="回复抓取数量"
              prop="videoRepliesPackageSize"
              v-if="addUserStep2Form.formData.videoReportLevel == 3"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.videoRepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoRepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="状态抓取数量"
              prop="videoStrepliesPackageSize"
              v-if="addUserStep2Form.formData.videoReportLevel == 3"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.videoStrepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoStrepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(stateRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          v-if="roleId == 3 || roleId == 4"
          label="黑号设置"
          name="blackauditRef"
        >
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="blackauditRef"
            label-width="180px"
            key="audit"
          >
            <div style="display: flex">
              <div>
                <el-form-item label="黑词是否加黑" prop="videoIsRepliesBlack">
                  <el-radio-group
                    v-model="addUserStep2Form.formData.videoIsRepliesBlack"
                  >
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="加黑关键词"
                  prop="videoRepliesBlackWords"
                  v-if="addUserStep2Form.formData.videoIsRepliesBlack == 1"
                >
                  <el-input
                    type="textarea"
                    :rows="4"
                    show-word-limit
                    resize="none"
                    class="input-w-3"
                    placeholder=" 多个用逗号(,)隔开"
                    v-model="addUserStep2Form.formData.videoRepliesBlackWords"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="是否验证黑号"
                  style="width: 360px"
                  prop="videoIsUserBlack"
                >
                  <el-radio-group
                    v-model="addUserStep2Form.formData.videoIsUserBlack"
                  >
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="黑号验证级别"
                  prop="videoUserBlackLevel1"
                  v-if="addUserStep2Form.formData.videoIsUserBlack == 1"
                >
                  <el-checkbox-group
                    style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    "
                    v-model="addUserStep2Form.formData.videoUserBlackLevel1"
                  >
                    <el-checkbox label="一级" value="1" />
                    <el-checkbox label="二级" value="2" />
                    <el-checkbox label="三级" value="3" />
                    <el-checkbox label="四级" value="4" />
                    <el-checkbox label="五级" value="5" />
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item
                  label="检测外部黑号"
                  style="width: 360px"
                  prop="videoIsExternalBlack"
                >
                  <el-radio-group
                    v-model="addUserStep2Form.formData.videoIsExternalBlack"
                  >
                    <el-radio label="是" value="1"></el-radio>
                    <el-radio
                      label="否"
                      value="2"
                      style="margin-left: 10px"
                    ></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  v-if="
                    addUserStep2Form.formData.videoIsExternalBlack == '1' &&
                    roleId == 3
                  "
                  label="外部黑号渠道"
                  prop="videoExternalBlackSource"
                >
                  <el-checkbox-group
                    style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    "
                    v-model="videoExternalBlackSourceData"
                  >
                    <el-checkbox
                      v-for="(item, index) in externalList"
                      :key="index"
                      :label="item.name"
                      :value="item.value"
                    />
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </div>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(blackauditRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="防轰炸设置" name="bombRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="bombRef"
            label-width="160px"
          >
            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.dailyLimited"
                :min="0"
                :max="999999999"
                label="账号每日提交发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input> -->
            </el-form-item>
            <el-form-item label="视频24h发送限制" prop="videoOverrunMarket">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.videoOverrunMarket"
                :min="0"
                :max="999999999"
                label="视频24h发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.videoOverrunMarket"></el-input> -->
            </el-form-item>
            <el-form-item
              label="视频七天发送限制"
              prop="videoOverrunTotalMarket"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.videoOverrunTotalMarket"
                :min="0"
                :max="999999999"
                label="视频七天发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3"
                                    v-model="addUserStep2Form.formData.videoOverrunTotalMarket"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm(bombRef)"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="addUserStep2Form"
                    label-width="160px">
                    <div class="float-div-1" style="padding-right:20px;">
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">参数设置</p>
                            <el-form-item label="通道组名称" prop="videoChannelGroupId">
                                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.videoChannelGroupId"
                                    filterable placeholder="请选择通道名称">
                                    <el-option v-for="(item, index) in channel" :label="item.channelGroupName"
                                        :value="item.channelGroupId" :key="index"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="计费方式" prop="videoChargeback">
                                <el-radio-group v-model="addUserStep2Form.formData.videoChargeback">
                                    <el-radio disabled label="1">提交计费</el-radio>
                                    <el-radio disabled label="2" style="margin-left:10px">成功计费</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="付费方式" prop="videoPayMethod">
                                <el-radio-group v-model="addUserStep2Form.formData.videoPayMethod">
                                    <el-radio disabled label="1">预付费</el-radio>
                                    <el-radio disabled label="2" style="margin-left:10px">后付费</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="黑词是否加黑" prop="videoIsRepliesBlack">
                                <el-radio-group v-model="addUserStep2Form.formData.videoIsRepliesBlack">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="加黑关键词" prop="videoRepliesBlackWords"
                                v-if="addUserStep2Form.formData.videoIsRepliesBlack == 1">
                                <el-input type="textarea" :rows="4" show-word-limit resize='none' class="input-w-3"
                                    placeholder=" 多个用逗号(,)隔开"
                                    v-model="addUserStep2Form.formData.videoRepliesBlackWords"></el-input>
                            </el-form-item>
                            <el-form-item label="是否验证黑号" style="width:360px;" prop='videoIsUserBlack'>
                                <el-radio-group v-model="addUserStep2Form.formData.videoIsUserBlack">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="黑号验证级别" prop="videoUserBlackLevel1"
                                v-if="addUserStep2Form.formData.videoIsUserBlack == 1">
                                <el-checkbox-group style="width:250px;display: flex;flex-shrink: 0;flex-wrap: wrap;"
                                    v-model="addUserStep2Form.formData.videoUserBlackLevel1">
                                    <el-checkbox style="margin-left:0" label="1">一级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="2">二级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="3">三级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="4">四级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="5">五级</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                            <el-form-item label="扩展号" prop="ext">
                                <el-input style="width:224px;" v-model="addUserStep2Form.formData.ext"></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">防轰炸设置</p>
                            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
                                <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input>
                            </el-form-item>
                            <el-form-item label="视频24h发送限制" prop="overrunCode">
                                <el-input class="input-w-3" v-model="addUserStep2Form.formData.videoOverrunMarket"></el-input>
                            </el-form-item>
                            <el-form-item label="视频七天发送限制" prop="videoOverrunTotalMarket">
                                <el-input class="input-w-3"
                                    v-model="addUserStep2Form.formData.videoOverrunTotalMarket"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="float-div-2" style="padding-right:20px;">
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">状态设置</p>
                            <el-form-item label="消息报告" prop="videoReportLevel">
                                <el-radio-group v-model="addUserStep2Form.formData.videoReportLevel" class="input-w">
                                    <el-radio label="1">不接收报告</el-radio>
                                    <el-radio label="2" style="margin-left:10px">批量推送</el-radio>
                                    <el-radio label="3" style="margin-left:10px">主动抓取</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="上行地址" prop="voiceRepliesUrl"
                                v-if="addUserStep2Form.formData.videoReportLevel == 2">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.videoRepliesUrl"></el-input>
                                <p style="width: 345px;"><span style="font-size:12px;">注：获取回复短信的地址</span>(以http/https开头)</p>
                            </el-form-item>
                            <el-form-item label="下行地址" prop="voiceReportUrl"
                                v-if="addUserStep2Form.formData.videoReportLevel == 2">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.videoReportUrl"></el-input>
                                <p style="width: 345px;"><span style="font-size:12px;">注：获取状态报告的地址</span>(以http/https开头)</p>
                            </el-form-item>
                            <el-form-item label="审核信息推送" prop="voiceReportUrl">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.videoTemplateUrl"></el-input>
                            </el-form-item>
                            <el-form-item label="回复推送数量" prop="voiceRepliesPackageSize"
                                v-if="addUserStep2Form.formData.videoReportLevel == 2">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoRepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="状态推送数量" prop="voiceStrepliesPackageSize"
                                v-if="addUserStep2Form.formData.videoReportLevel == 2">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoStrepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="回复抓取数量" prop="videoRepliesPackageSize"
                                v-if="addUserStep2Form.formData.videoReportLevel == 3">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoRepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="状态抓取数量" prop="videoStrepliesPackageSize"
                                v-if="addUserStep2Form.formData.videoReportLevel == 3">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.videoStrepliesPackageSize"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div style="text-align: center" class="clear-div">
                        <el-button style="width:120px" @click="goBack()">返回</el-button>
                        <el-button type="primary" style="margin-left: 20px;width:120px"
                            @click="submitForm('addUserStep2Form')">提 交</el-button>
                    </div>
                </el-form> -->
      <el-dialog
        title="视频短信配置"
        v-model="dialogVisible"
        width="30%"
        :before-close="handleClose"
      >
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleFormRef"
          label-width="70px"
          class="demo-ruleForm"
        >
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.remark"></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button
              v-if="!subLoding"
              type="primary"
              @click="submitSetting(ruleFormRef)"
              >确 定</el-button
            >
            <el-button v-else type="primary" :loading="subLoding" disabled
              >稍 等</el-button
            >
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const parameterRef = ref(null);
const stateRef = ref(null);
const bombRef = ref(null);
const blackauditRef = ref(null);
const ruleFormRef = ref(null);
var checkBlackWords = (rule, value, callback) => {
  if (value == "") {
    return callback(new Error("加黑关键字不能为空"));
  } else {
    console.log(value.length);
    if (value.length <= 200) {
      var reg =
        /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,200}$/gi;
      if (!reg.test(value)) {
        return callback(
          new Error("加黑关键词包括数字、汉字、字母、多个由英文逗号隔开")
        );
      } else {
        var arr = value.split(",");
        var arr2 = [];
        for (var i = 0; i < arr.length; i++) {
          if (arr2.indexOf(arr[i]) < 0) {
            arr2.push(arr[i]);
          }
        }
        if (arr2.length != arr.length) {
          return callback(new Error("不允许重复"));
        } else {
          callback();
        }
      }
    }
    return callback(new Error("加黑关键字不能多于200个字"));
  }
};
var checkSignature = (rule, value, callback) => {
  var reg = /^(\d+,?)+$/;
  if (value) {
    if (!reg.test(value)) {
      callback(new Error("扩展号由1-12位数字组成"));
    } else {
      window.api.get(
        window.path.omcs + "operatingSignature/checkClientExt",
        { ext: value, userId: addUserStep2Form.formData.userId },
        (res) => {
          if (res.data == true) {
            callback(new Error("此扩展号已存在"));
          } else {
            callback();
          }
        }
      );
    }
  } else {
    return callback(new Error("请输入扩展号"));
  }
};
const isFirstEnter = ref(false);
const roleId = ref("");
const jsonData = reactive({
  consumerName: "",
  compName: "",
});
const dialogVisible = ref(false);
const subLoding = ref(false);
const activeName = ref("parameterRef");
const ruleForm = reactive({
  remark: "",
});
const rules = reactive({
  remark: [{ required: true, message: "请填写备注", trigger: "change" }],
});
const addUserStep2Form = reactive({
  //弹窗步骤2的数据和验证
  formData: {
    userId: "", //用户id
    videoPayMethod: "1",
    // smsPrice:'',
    videoChannelGroupId: "",
    videoChargeback: "1",
    // flag: '5',
    ext: "",
    videoOverrunMarket: 0, //防轰炸
    videoOverrunTotalMarket: 0,
    videoReportLevel: "1",
    videoRepliesUrl: "",
    videoReportUrl: "",
    videoTemplateUrl: "",
    videoRepliesPackageSize: 0,
    videoStrepliesPackageSize: 0,
    videoIsRepliesBlack: "2", //是否加黑
    videoRepliesBlackWords: "", //加黑关键词
    videoIsUserBlack: "1", //是否验证黑号
    videoUserBlackLevel1: ["1", "2", "3"], //黑号验证级别存储
    videoUserBlackLevel: "", //黑号验证级别存储
    dailyLimited: 0, //每日限量
    videoIsExternalBlack: "2",
    videoExternalBlackSource: "",
    // videoProvinceRatio:'',
    // videoSplitDb:'',
    // videoSplitRatio:''
  },
  formRule: {
    videoPayMethod: [
      { required: true, message: "请选择付费方式", trigger: "change" },
    ],
    videoIsRepliesBlack: [
      { required: true, message: "请选择是否加黑", trigger: "blur" },
    ],
    videoIsUserBlack: [
      {
        required: true,
        message: "请选择是否验证黑号",
        trigger: "change",
      },
    ],
    videoUserBlackLevel1: [
      {
        required: true,
        message: "请选择黑号验证级别",
        trigger: "change",
      },
    ],
    ext: [{ required: true, validator: checkSignature, trigger: ["blur"] }],
    videoRepliesUrl: [
      {
        required: false,
        message: "请输入上行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    videoReportUrl: [
      {
        required: false,
        message: "请输入下行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    videoChannelGroupId: [
      { required: true, message: "请选择通道", trigger: "change" },
    ],
    videoChargeback: [
      { required: true, message: "请选择计费方式", trigger: "change" },
    ],
    videoRepliesBlackWords: [
      { required: true, validator: checkBlackWords, trigger: "change" },
    ],
  },
});
const channel = ref([]);
const videoExternalBlackSourceData = ref([]);
const externalList = ref([
  {
    name: "先锋",
    value: "XF",
  },
  // {
  //   name: "棱镜",
  //   value: "LJ",
  // },
  {
    name: "河南利加",
    value: "HNLJ",
  },
  {
    name: "棱镜实时",
    value: "LJ_NEW",
  },
]);

onMounted(() => {
  addUserStep2Form.formData.userId = $router.currentRoute.value.query.id;
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo) {
    roleId.value = userInfo.roleId;
  }
  getBasicData();
  getChannel();
  handleEdit();
});
const getBasicData = () => {
  window.api.get(
    window.path.omcs + "operatinguser/" + addUserStep2Form.formData.userId,
    {},
    (res) => {
      jsonData.consumerName = res.data.consumerName;
      jsonData.compName = res.data.compName;
    }
  );
};
const getChannel = () => {
  window.api.get(
    window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup?productId=3",
    {},
    (res) => {
      if (res.code == 200) {
        channel.value = res.data;
      }
    }
  );
};
const goBack = () => {
  $router.push({
    path: "UserDetail",
    query: {
      id: $router.currentRoute.value.query.id,
    },
  });
};
//赋值
const handleEdit = (index, row) => {
  try {
    window.api.get(
      window.path.omcs +
        "operatinguser/userVideoInfo/" +
        $router.currentRoute.value.query.id,
      {},
      (res) => {
        if (res.code == 200) {
          for (let i in res.data) {
            if (!(res.data[i] == null)) {
              res.data[i] += "";
            }
          }
          res.data["videoUserBlackLevel1"] =
            res.data.videoUserBlackLevel.split(",");
          // console.error(res);
          Object.assign(addUserStep2Form.formData, res.data);
          for (let i in addUserStep2Form.formData) {
            if (
              i == "videoOverrunMarket" ||
              i == "videoOverrunTotalMarket" ||
              i == "videoChannelGroupId" ||
              i == "videoRepliesPackageSize" ||
              i == "videoStrepliesPackageSize" ||
              i == "dailyLimited"
            ) {
              if (addUserStep2Form.formData[i] != null) {
                addUserStep2Form.formData[i] = parseInt(res.data[i]);
              }
            }
          }
          if(res.data.videoIsExternalBlack){
            addUserStep2Form.formData.videoIsExternalBlack = res.data.videoIsExternalBlack;
          }else{
            addUserStep2Form.formData.videoIsExternalBlack = "2";
          }
          if (res.data.videoExternalBlackSource) {
            videoExternalBlackSourceData.value =
              res.data.videoExternalBlackSource.split(",");
            let list = ["XF", "HNLJ", "LJ_NEW"];
            let sortList = videoExternalBlackSourceData.value.map((item) => {
              return item;
            });
            let smsExternalBlackSources = sortList.concat(
              list.filter(function (v) {
                return !(sortList.indexOf(v) > -1);
              })
            );
            externalList.value = smsExternalBlackSources.map((item) => {
              return {
                name:
                  item == "XF"
                    ? "先锋"
                    : // : item == "LJ"
                    // ? "棱镜"
                    item == "HNLJ"
                    ? "河南利加"
                    : item == "LJ_NEW"
                    ? "棱镜实时"
                    : "",
                value: item,
              };
            });
          } else {
            videoExternalBlackSourceData.value = [];
            externalList.value = [
              {
                name: "先锋",
                value: "XF",
              },
              // {
              //   name: "棱镜",
              //   value: "LJ",
              // },
              {
                name: "河南利加",
                value: "HNLJ",
              },
              {
                name: "棱镜实时",
                value: "LJ_NEW",
              },
            ];
          }
          // console.log(addUserStep2Form.formData);
        }
      }
    );
  } catch (error) {
    console.warn(error);
  }
};
const submitForm = (formEl) => {
  console.log(formEl, "formEl");

  formEl.validate((valid, fields) => {
    if (valid) {
      if (addUserStep2Form.formData.videoReportLevel != 2) {
        //消息报告如果不选批量推送时，重置批量推送下面的推送方式，上下行地址
        addUserStep2Form.formData.videoRepliesUrl = "";
        addUserStep2Form.formData.videoReportUrl = "";
      }
      if (addUserStep2Form.formData.videoReportLevel == 1) {
        addUserStep2Form.formData.videoRepliesPackageSize = "";
        addUserStep2Form.formData.videoStrepliesPackageSize = "";
      }
      addUserStep2Form.formData.videoUserBlackLevel =
        addUserStep2Form.formData.videoUserBlackLevel1.join(",");
      dialogVisible.value = true;
    } else {
      console.error("error submit!!", fields);
      return false;
    }
  });
};
const submitSetting = (formEl) => {
  formEl.validate((valid, fields) => {
    if (valid) {
      subLoding.value = true;
      addUserStep2Form.formData.remark = ruleForm.remark;
      if (videoExternalBlackSourceData.value.length) {
        addUserStep2Form.formData.videoExternalBlackSource =
          videoExternalBlackSourceData.value.join(",");
      }
      window.api.put(
        window.path.omcs + "operatinguser/userVideoInfo",
        addUserStep2Form.formData,
        (res) => {
          if (res.code == 200) {
            //返回用户管理页面
            subLoding.value = false;
            dialogVisible.value = false;
            goBack();
          } else {
            subLoding.value = false;
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      console.error("error submit!!", fields);
      return false;
    }
  });
};
const handleClose = () => {
  dialogVisible.value = false;
};
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      ruleFormRef.value.resetFields();
    }
  }
);
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}
.input-w {
  width: 345px !important;
}
.el-radio-group .el-radio {
  margin-right: 0px;
}
.fillet {
  padding: 20px;
}
.input-w-2 {
  width: 200px !important;
}
.input-w-3 {
  width: 200px !important;
}
.input-w-4 {
  width: 190px !important;
}
.input-w-sm {
  width: 126px !important;
}
.input-w-f {
  width: 80px !important;
}
.float-div-1 {
  float: left;
}
.float-div-2 {
  float: left;
}
.float-div-3 {
  float: left;
}
.clear-div {
  clear: both;
}
.red {
  color: red;
}
@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>

<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>
