import { createRouter, createWebHashHistory } from 'vue-router'

// 解决报错
// const originalPush = Router.prototype.push
// const originalReplace = Router.prototype.replace
// push
// Router.prototype.push = function push(location, onResolve, onReject) {
//   if (onResolve || onReject)
//     return originalPush.call(this, location, onResolve, onReject)
//   return originalPush.call(this, location).catch((err) => err)
// }
// // replace
// Router.prototype.replace = function push(location, onResolve, onReject) {
//   if (onResolve || onReject)
//     return originalReplace.call(this, location, onResolve, onReject)
//   return originalReplace.call(this, location).catch((err) => err)
// }

export default createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: '/home',
      meta: {
        requireAuth: true,
      },
    },
    {
      path: '/',
      component:
        () => import('@/components/common/Home.vue'),
      // component: resolve => require(['@/components/common/Home.vue'], resolve),
      meta: { title: '自述文件' },
      children: [
        {
          path: '/home',
          component:
            () => import('@/components/page/Dashboard.vue'),
          // component: resolve => require(['@/components/page/Dashboard.vue'], resolve),
          meta: { title: '系统首页', keepAlive: true, isBack: false },
        },
        {
          path: '/FailurecodeMag',
          component:
            () =>
              import(
                '@/components/page/OperationMag/FailurecodeMag/FailurecodeMag.vue'
              ),
          // component: resolve => require(['@/components/page/OperationMag/FailurecodeMag/FailurecodeMag.vue'], resolve),
          meta: { title: '失败代码管理', keepAlive: true, isBack: false },
        },
        {
          path: '/IndustryCategoryMag',
          component: () =>
            import('@/components/page/OperationMag/IndustryCategoryMag/IndustryCategoryMag.vue'),
          meta: { title: '行业类别管理', keepAlive: true, isBack: false },
        },
        {
          path: '/PhoneNumberLoc',
          component:
            () =>
              import(
                '@/components/page/OperationMag/PhoneNumberLoc/PhoneNumberLoc.vue'
              ),
          // component: resolve => require(['@/components/page/OperationMag/PhoneNumberLoc/PhoneNumberLoc.vue'], resolve),
          meta: { title: '手机号码归属地', keepAlive: true, isBack: false },
        },
        {
          path: '/OperatorConfigurationMag',
          component:
            () =>
              import(
                '@/components/page/OperationMag/OperatorConfigurationMag/OperatorConfigurationMag.vue'
              ),
          // component: resolve => require(['@/components/page/OperationMag/OperatorConfigurationMag/OperatorConfigurationMag.vue'], resolve),
          meta: { title: '运营商配置管理', keepAlive: true, isBack: false },
        },
        {
          path: '/alertconfig',
          component:
            () =>
              import(
                '@/components/page/OperationMag/alertconfig/alertconfig.vue'
              ),
          // component: resolve => require(['@/components/page/OperationMag/OperatorConfigurationMag/OperatorConfigurationMag.vue'], resolve),
          meta: { title: '短信预警设置', keepAlive: true, isBack: false },
        },
        {
          path: '/FailurecodeMagNote',
          meta: { title: '失败代类别码管理', keepAlive: true, isBack: false },
          component:
            () =>
              import(
                '@/components/page/OperationMag/Operationalsetup/FailurecodeMagNote/FailurecodeMagNote.vue'
              ),
        },
        // {
        //   path: '/tagManagement',
        //   component: resolve => require(['@/components/page/OperationMag/tagMag/tagManagement.vue'], resolve),
        //   meta: { title: '标签管理',keepAlive:true,isBack:false, }
        // },
        {
          path: '/CMPPmanagement',
          meta: { title: 'cmpp账号协议管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/Operationalsetup/CMPPmanagement/CMPPmanagement.vue'
              ),
        },
        {
          path: '/cmppConnect',
          meta: { title: 'cmpp连接信息', keepAlive: true, isBack: false },
          component:
            () =>
              import(
                '@/components/page/OperationMag/Operationalsetup/CMPPmanagement/cmppConnectList.vue'
              ),
        },
        {
          path: '/KeyWords',
          meta: { title: '营销关键字管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/Words/KeyWords/KeyWords.vue'
              ),
        },
        {
          path: '/SignatureReport',
          meta: { title: '签名报备', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/SignatureReport/SignatureReport.vue'
              ),
        },
        {
          path: '/SignatureNumMag',
          meta: { title: ' 签名扩展号管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/SignatureNumMag/SignatureNumMag.vue'
              ),
        },
        {
          path: '/TemplateMag',
          meta: { title: ' 模板管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/TemplateMag/TemplateMag.vue'
              ),
        },
        {
          path: '/blackTemplate',
          meta: { title: '黑模板管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/blackTemplate/blackTemplate.vue'
              ),
        },
        {
          path: '/pushManagement',
          meta: { title: ' 状态推送管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/pushManagement/pushManagement.vue'
              ),
        },
        {
          path: '/RMSPushManagement',
          meta: { title: ' 视频短信补推', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/RmsPushManagement/pushManagement.vue'
              ),
        },
        {
          path: '/IMSPushManagement',
          meta: { title: ' 国际短信补推', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/ImsPushManagement/pushManagement.vue'
              ),
        },
        {
          path: '/voiceCodePushManagement',
          meta: { title: ' 语音验证码补推', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/voiceCodePushManagement/pushManagement.vue'
              ),
        },
        {
          path: '/voicePushManagement',
          meta: { title: ' 语言通知补推', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/voicePushManagement/pushManagement.vue'
              ),
        },
        {
          path: '/SignatureMag',
          meta: { title: ' 签名管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/SignatureMag/SignatureMag.vue'
              ),
        },
        {
          path: '/SignatureExample',
          meta: { title: ' 签名示例管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/OtherMag/SignatureExample/SignatureExample.vue'
              ),
        },
        {
          path: '/billSetting',
          meta: { title: '统计设置管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/OperationMag/bill/setting.vue'),
        },
        {
          path: '/billParam',
          meta: { title: '统计参数管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/OperationMag/bill/param.vue'),
        },
        {
          path: '/billList',
          meta: { title: '明码导出任务', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/OperationMag/bill/billList.vue'),
        },
        {
          path: '/PassagewayMag',
          meta: { title: ' 通道设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/PassagewayMag/PassagewayMag.vue'
              ),
        },
        {
          path: '/channelPool',
          meta: { title: ' 通道切换设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/ChannelPool/ChannelPool.vue'
              ),
        },
        {
          path: '/ChannelGroupLog',
          meta: { title: ' 通道组日志', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/ChannelGroupLog/ChannelGroupLog.vue'
              ),
        },
        {
          path: '/ChannelRouting',
          meta: { title: ' 通道组改写设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/ChannelRouting/ChannelRouting.vue'
              ),
        },
        {
          path: '/channelGroupRule',
          meta: { title: ' 人工通道组规则', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/channelGroupRule/channelGroupRule.vue'
              ),
        },
        {
          path: '/ArtificialMag',
          meta: { title: '旧人工通道组管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/ArtificialMag/ArtificialMag.vue'
              ),
        },
        {
          path: '/ArtificialMagNew',
          meta: { title: '新人工通道组管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/ArtificialMag/ArtificialMagNew.vue'
              ),
        },
        {
          path: '/SpecialChannel',
          meta: { title: ' 个性化通道设置', keepAlive: true, isBack: false },
          component:
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/SpecialChannel/SpecialChannel.vue'
              ),
        },
        {
          path: '/ChannelRestartPlan',
          meta: { title: ' 通道重启计划', keepAlive: true, isBack: false },
          component:
            () =>
              import(
                '@/components/page/OperationMag/PassageWay/ChannelRestartPlan/ChannelRestartPlan.vue'
              ),
        },
        {
          path: '/routerSetting',
          meta: { title: ' 路由设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/ShuntSetting.vue'
              ),
        },
        {
          path: '/signatureFailurcode',
          meta: { title: ' 签名失败代码', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/signatureFailurcode.vue'
              ),
        },
        {
          path: '/shortTask',
          meta: { title: '提取短链任务', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/shortTask.vue'
              ),
        },
        {
          path: '/successRateMonitor',
          meta: { title: '后置路由明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/successRateMonitor.vue'
              ),
        },
        {
          path: '/mobileCheck',
          meta: { title: '空号检测明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/mobileCheck.vue'
              ),
        },
        {
          path: '/shortStatistics',
          meta: { title: '短链任务明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/shortStatistics.vue'
              ),
        },
        {
          path: '/shorCodetStatistics',
          meta: { title: '短链点击详情', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/shorCodetStatistics.vue'
              ),
        },
        {
          path: '/routerdDetail',
          meta: { title: '前置路由明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/ShuntDetailed.vue'
              ),
        },
        {
          path: '/similarMobile',
          meta: { title: '号码频次限制', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/similarMobile.vue'
              ),
        },
        {
          path: '/recoveryDataSetting',
          meta: { title: '回复数据设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/recoveryDataSetting.vue'
              ),
        },
        {
          path: '/recoveryDataDetail',
          meta: { title: '回复数据明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/recoveryDataDetail.vue'
              ),
        },
        {
          path: '/Marketing',
          meta: { title: ' 营销黑号', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/Marketing.vue'
              ),
        },
        {
          path: '/receiptList',
          meta: { title: '回执补偿设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/ComplaintControl/receiptList.vue'
              ),
        },
        {
          path: '/AuditCertification',
          meta: { title: ' 客服认证审核', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/AuditCertification/AuditCertification.vue'
              ),
        }, //风控复核
        {
          path: '/recmanagementSetting',
          meta: { title: ' 失败自动补发设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/recmanagementSetting.vue'),
        },
        {
          path: '/5gAuditCertification',
          meta: { title: '5G资质', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/5g/5gAuditCertification.vue'
              ),
        },
        {
          path: '/5gChannel',
          meta: { title: '5G通道', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/channel5G.vue'),
        },
        {
          path: '/chatbotChannel',
          meta: { title: 'chatbot通道', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/chatbotChannel.vue'),
        },
        {
          path: '/5gkeywords',
          meta: { title: '关键词回复', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/5g/keyword/keywords.vue'),
        },
        {
          path: '/chatbotAudit',
          meta: { title: 'chatbot审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/chatbot.vue'),
        },
        {
          path: '/pictureAudit',
          meta: { title: '素材审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/pictureAudit.vue'),
        },
        {
          path: '/pictureChannel',
          meta: { title: '素材通道', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/pictureChannel.vue'),
        },
        {
          path: '/templateCardAudit',
          meta: { title: '模板审核', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/5g/templateCardAudit.vue'),
        },
        {
          path: '/MenuAudit',
          meta: { title: '固定菜单审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/MenuAudit.vue'),
        },
        {
          path: '/MenuChannel',
          meta: { title: '菜单报备', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/5g/MenuChannel/menuChannel.vue'
              ),
        },
        {
          path: '/xsmsTemplate',
          meta: { title: '5G阅信模板', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/xsmstemplate.vue'),
        },
        {
          path: '/xsmsSetting',
          meta: { title: '5G阅信解析配置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/5g/xsmsSetting/xsmsSetting.vue'
              ),
        },
        {
          path: '/xsmsRecord',
          meta: { title: '5G阅信费用返还', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/5g/xsmsSetting/xsmsRecord.vue'
              ),
        },
        {
          path: '/5gSendRecord',
          meta: { title: '发送记录', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/sendRecord.vue'),
        },
        {
          path: '/reportRecord',
          meta: { title: '回执记录', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/reportRecord.vue'),
        },
        {
          path: '/5gSetting',
          meta: { title: '5g配置', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/5g/5gSetting.vue'),
        },
        {
          path: '/5gSmsChannelSetting',
          meta: { title: '5g通道计费数配置', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/5g/5gSmsChannelSetting.vue'),
        },
        {
          path: '/ReCheck',
          meta: { title: '风控认证审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/ReCheck/ReCheck.vue'),
        },
        {
          path: '/TemplateAudit',
          meta: { title: '短信模板审核管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/TemplateAudit/TemplateAudit.vue'
              ),
        },
        {
          path: '/SignatureAudit',
          meta: { title: '短信签名审核管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/SignatureAudit.vue'),
        },
        {
          path: '/SMSContentReview',
          meta: { title: '短信发送内容审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/SMSContentReview.vue'),
        },
        {
          path: '/BlackWordReview',
          meta: { title: '短信敏感词审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/BlackWordReview.vue'),
        },
        {
          path: '/clientsmssimilar',
          meta: { title: '自动审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/clientsmssimilar.vue'),
        },
        {
          path: '/smsTestBlackWord',
          meta: {
            title: ' 短信测试账号敏感词审核',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () => import('@/components/page/ManualAudit/smsTestList.vue'),
        },
        {
          path: '/smsTestContent',
          meta: {
            title: ' 短信测试账号内容审核',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () => import('@/components/page/ManualAudit/smsTestContent.vue'),
        },
        {
          path: '/ConditionalAuditList',
          meta: { title: ' 短信条件审核', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/ConditionalAuditList.vue'),
        },
        // {
        //   path: '/ConditionalAuditList',
        //   meta: { title: ' 短信条件审核' },
        //   component: () => import ('@/components/page/ManualAudit/ConditionalAuditList.vue'),
        // },
        {
          path: '/MMSAudit',
          meta: { title: ' 彩信发送审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/MMSAudit.vue'),
        },
        {
          path: '/MMSinterfaceSend',
          meta: { title: ' 彩信接口发送审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/MMSinterfaceSend.vue'),
        },
        {
          path: '/RMSAudit',
          meta: { title: ' 视频短信模板审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/RMSAudit.vue'),
        },
        {
          path: '/voiceAudit',
          meta: { title: ' 语音短信审核', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/voiceAudit.vue'),
        },
        {
          path: '/OperationManger',
          meta: { title: ' 运营监控管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/Operationalsetup/OperationManger/OperationManger.vue'
              ),
        },
        {
          path: '/SMSReplacement',
          meta: { title: ' 短信补发管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/SMSReplacement.vue'),
        },
        {
          path: '/RMSReplacement',
          meta: { title: ' 视频短信补发管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/RMSReplacement.vue'),
        },
        {
          path: '/MMSReplacement',
          meta: { title: ' 彩信补发管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/MMSReplacement.vue'),
        },
        {
          path: '/ViocReplacement',
          meta: { title: '语言通知补发管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/ViocReplacement.vue'),
        },
        {
          path: '/ReissueDetails',
          meta: { title: ' 短信补发记录详情', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/ReissueDetails.vue'),
        },
        {
          path: '/ManagerSub',
          meta: { title: ' 子用户列表', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/UserMag/components/ManagerSub.vue'),
        },
        {
          path: '/userList',
          meta: { title: '  用户列表', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/OperationMag/userList.vue'),
        },
        {
          path: '/timing',
          meta: { title: ' 接口定时管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/timingMag/timing.vue'),
        },
        {
          path: '/ProcessingQueue',
          meta: { title: ' web发送任务管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/ProcessingQueue.vue'),
        },
        {
          path: '/similarUsers',
          meta: { title: ' 定时账号管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/similarUsers.vue'),
        },
        {
          path: '/MMSWebTask',
          meta: { title: '彩信web发送管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/MMSWebTask.vue'),
        },
        {
          path: '/MMSinterfaceTask',
          meta: { title: '彩信接口发送管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/MMSinterfaceTask.vue'),
        },
        {
          path: '/videoTemplate',
          meta: { title: '视频短信模板管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/videoTemplate.vue'),
        },
        {
          path: '/RMSWebTask',
          meta: {
            title: '视频短信web发送管理',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () => import('@/components/page/ManualAudit/RMSWebTask.vue'),
        },
        {
          path: '/RMSinterfaceTask',
          meta: {
            title: '视频短信接口发送任务管理',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () => import('@/components/page/ManualAudit/RMSinterfaceTask.vue'),
        },
        {
          path: '/IMSWebTask',
          meta: {
            title: '国际短信接口发送管理',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () => import('@/components/page/ManualAudit/IMSWebTask.vue'),
        },
        {
          path: '/voiceWebTask',
          meta: { title: '语音web发送管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/voiceWebTask.vue'),
        },
        {
          path: '/VoiceInterfaceTask',
          meta: { title: '语音接口发送管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/ManualAudit/VoiceInterfaceTask.vue'),
        },
        {
          path: '/FlashTestApp',
          meta: { title: '闪验App应用列表', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/FlashTestApp.vue'),
        },
        {
          path: '/webtaskgroup',
          meta: { title: '发送活动', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/sendGroup/webtaskgroup.vue'),
        },
        {
          path: '/webtaskpool',
          meta: { title: '号码池管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/sendGroup/webtaskpool.vue'),
        },
        {
          path: '/webSendRecord',
          meta: { title: '发送计划明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/sendGroup/sendRecord.vue'),
        },
        {
          path: '/SensitiveWordMag',
          meta: { title: '敏感词设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/SensitiveWords/SensitiveWordMag/SensitiveWordMag.vue'
              ),
        },
        {
          path: '/SensitiveWordsTag',
          meta: { title: '敏感词标签库', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/SensitiveWords/SensitiveWordsTag/SensitiveWordsTag.vue'
              ),
        },
        {
          path: '/IPWhiteList',
          meta: { title: 'IP白名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/whiteList/IPWhiteList.vue'
              ),
        },
        {
          path: '/AccountWhiteList',
          meta: { title: '手机号白名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/whiteList/PhoneWhiteList.vue'
              ),
        },
        {
          path: '/userWhitelist',
          meta: { title: '账号白名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/whiteList/userWhitelist.vue'
              ),
        },
        {
          path: '/BlackLIst',
          meta: { title: '系统黑名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/ListManagement/BlackLIst/BlackLIst.vue'
              ),
        },
        {
          path: '/UserBlackLIst',
          meta: { title: '账号黑名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/ListManagement/UserBlackLIst/UserBlackLIst.vue'
              ),
        },
        {
          path: '/MarketBlackLIst',
          meta: { title: '营销黑名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/ListManagement/MarketBlackLIst/MarketBlackLIst.vue'
              ),
        },
        {
          path: '/signatureBlack',
          meta: { title: '签名黑名单', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/ListManagement/signatureBlack/signatureBlack.vue'
              ),
        },
        {
          path: '/BlockList',
          meta: { title: '屏蔽签名', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/ListManagement/BlockList/BlockList.vue'
              ),
        },
        {
          path: '/whitelist',
          meta: { title: '白名单设置', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/OperationMag/Operationalsetup/whitelist/whitelist.vue'
              ),
        },
        {
          path: '/IPblacklist',
          meta: { title: 'IP黑名单', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/IPblacklist.vue'),
        },
        // {
        //   path: '/userIPWhiteList',
        //   meta: { title: '账号IP白名单',keepAlive:true,isBack:false, },
        //   component: () => import ('@/components/page/SecurityCheck/whiteList/userIPWhiteList.vue'),
        // },
        {
          path: '/MMSreplyDetails',
          meta: { title: '彩信用户回复明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/StatisticalAnalysis/MMSreplyDetails.vue'
              ),
        },
        {
          path: '/MMSsendDetails',
          meta: { title: '彩信发送明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/MMSsendDetails.vue'),
        },
        {
          path: '/userSendNotes',
          meta: { title: '短信发送明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/userSendNotes.vue'),
        },
        {
          path: '/userReplyData',
          meta: { title: '短信用户回复明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/userReplyData.vue'),
        },
        {
          path: '/RMSsendDetails',
          meta: { title: '视频短信发送明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/RMSsendDetails.vue'),
        },
        {
          path: '/RMSreplyDetails',
          meta: {
            title: '视频短信用户回复明细',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () =>
              import(
                '@/components/page/StatisticalAnalysis/RMSreplyDetails.vue'
              ),
        },
        {
          path: '/ImsSendNotes',
          meta: { title: '国际短信发送明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/ImsSendNotes.vue'),
        },
        {
          path: '/ImsReplyDetails',
          meta: {
            title: '国际短信用户回复明细',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () =>
              import(
                '@/components/page/StatisticalAnalysis/ImsReplyDetails.vue'
              ),
        },
        {
          path: '/FlashTestSendNotes',
          meta: { title: '闪验发送明细', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/StatisticalAnalysis/FlashTestSendNotes.vue'
              ),
        },
        {
          path: '/InternationalPrice',
          meta: { title: '国际价格表', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/StatisticalAnalysis/InternationalPrice.vue'
              ),
        },
        {
          path: '/PortNumberTransfer',
          meta: { title: '携号转网列表', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/StatisticalAnalysis/PortNumberTransfer.vue'
              ),
        },
        {
          path: '/shortChainAnalysis',
          meta: { title: '短链追踪统计', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/ShortAudit/shortChainAnalysis.vue'
              ),
        },
        {
          path: '/shortLinkApply',
          meta: { title: '短链开通审核', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/ManualAudit/ShortAudit/shortLinkApply.vue'
              ),
        },
        {
          path: '/shortDomain',
          meta: { title: '短链白名单审核', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/ManualAudit/ShortAudit/shortDomain.vue'),
        },
        {
          path: '/errorCode',
          meta: { title: '自动补发错误代码', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/StatisticalAnalysis/errorCode.vue'),
        },
        {
          path: '/exportproof',
          meta: { title: '导出授权明细 ', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/exportproof.vue'),
        },
        {
          path: '/MenuManage',
          meta: { title: '栏目管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/shop/MenuManage.vue'),
        },
        {
          path: '/MenuSetting',
          meta: {
            title: '栏目-关联商品',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/shop/MenuSetting.vue'),
        },
        {
          path: '/OrderManage',
          meta: { title: '订单管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/shop/OrderManage.vue'),
        },
        {
          path: '/ShopManage',
          meta: { title: '商品管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/shop/ShopManage.vue'),
        },
        {
          path: '/ShopSetting',
          meta: { title: '栏目关联商品', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/shop/ShopSetting.vue'),
        },
        {
          path: '/ShopUser',
          meta: { title: '线上终端客户列表', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/shop/ShopUserList.vue'),
        },
        {
          path: '/ShopMarketUser',
          meta: { title: '线上终端客户列表', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/shop/market/ShopUserList.vue'),
        },
        {
          path: '/voiceSendNotes',
          meta: { title: '语音短信计划', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/StatisticalAnalysis/voiceSendNotes.vue'),
        },
        {
          path: '/UserMag',
          component: 
            () => import('@/components/page/UserMag/UserMag.vue'),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '用户管理', keepAlive: true, isBack: false },
        },
        {
          path: '/subAccountRealInfo',
          component: 
            () => import('@/components/page/UserMag/subAccountRealInfo.vue'),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '子用户实名信息管理', keepAlive: true, isBack: false },
        },
        // {
        //   path: '/settingPhone',
        //   component: ()=> import('@/components/page/UserMag/settingPhone.vue'),
        //   // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
        //   meta: { title: '用户登录手机号管理',keepAlive:true,isBack:false,}
        // },
        {
          path: '/scompanyrisk',
          component: 
            () => import('@/components/page/UserMag/scompanyrisk.vue'),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '风险账号管理', keepAlive: true, isBack: false },
        },
        {
          path: '/templateSkipChecks',
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/SkipChecks/templateSkipChecks.vue'
              ),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: {
            title: '模板参数不检测授权证明',
            keepAlive: true,
            isBack: false,
          },
        },
        {
          path: '/crawlerLibrary',
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/Crawler/crawlerLibrary.vue'
              ),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '爬虫IP管理', keepAlive: true, isBack: false },
        },
        // {
        //   path: '/shortLinkCheck',
        //   component: ()=> import('@/components/page/SecurityCheck/shortLinkCheck/shortLinkCheck.vue'),
        //   // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
        //   meta: { title: '短链检索',keepAlive:true,isBack:false,}
        // },
        // {
        //   path: '/antibombing',
        //   component: ()=> import('@/components/page/SecurityCheck/Antibombing/antibombing.vue'),
        //   // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
        //   meta: { title: '防轰炸设置',keepAlive:true,isBack:false,}
        // },
        {
          path: '/numbPortability',
          component: 
            () =>
              import(
                '@/components/page/SecurityCheck/NumberPortability/index.vue'
              ),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '携号转网库', keepAlive: true, isBack: false },
        },
        {
          path: '/userModification',
          component: 
            () => import('@/components/page/UserMag/userModification.vue'),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '新增编辑用户管理', keepAlive: true, isBack: false },
        },
        {
          path: '/UserDetail',
          component: 
            () => import('@/components/page/UserMag/UserDetail.vue'),
          // component: resolve => require(['@/components/page/UserMag/UserMag.vue'], resolve),
          meta: { title: '用户详情', keepAlive: true, isBack: false },
        },
        {
          path: '/SMSconfiguration',
          meta: {
            title: '短信配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/SMSconfiguration.vue'),
        },
        {
          path: '/SMS5Gconfiguration',
          meta: {
            title: '5G短信配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/SMS5gConfiguration.vue'),
        },
        {
          path: '/yuexinconfiguration',
          meta: {
            title: '阅信配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/yuexinConfiguration.vue'),
        },
        {
          path: '/MMSconfiguration',
          meta: {
            title: '彩信配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/MMSconfiguration.vue'),
        },
        {
          path: '/RMSconfiguration',
          meta: {
            title: '视频短信配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/RMSconfiguration.vue'),
        },
        {
          path: '/ImsInternational',
          meta: {
            title: '国际短信配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/ImsInternational.vue'),
        },
        {
          path: '/FlashTest',
          meta: {
            title: '闪验配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/FlashTest.vue'),
        },
        {
          path: '/voiceCode',
          meta: {
            title: '语音验证码配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/voiceCode.vue'),
        },
        {
          path: '/voiceNotice',
          meta: {
            title: '语音通知配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/voiceNotice.vue'),
        },
        {
          path: '/productSetUp',
          meta: {
            title: '产品配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/productSetUp.vue'),
        },
        {
          path: '/batchaaAction',
          meta: {
            title: '批量操作',
            keepAlive: false,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/batchaaAction.vue'),
        },
        {
          path: '/batchUserShop',
          meta: {
            title: '用户关停',
            keepAlive: false,
            isBack: false,
          },
          component: 
            () => import('@/components/page/UserMag/batchUserShop.vue'),
        },
        {
          path: '/batchUserLists',
          meta: {
            title: '批量添加黑/白名单',
            keepAlive: false,
            isBack: false,
          },
          component: 
            () => import('@/components/page/UserMag/batchUserLists.vue'),
        },
        {
          path: '/batchUserOperators',
          meta: {
            title: '批量操作运营商下发 ',
            keepAlive: false,
            isBack: false,
          },
          component: 
            () => import('@/components/page/UserMag/batchUserOperators.vue'),
        },
        {
          path: '/batchShortlinkReset',
          meta: {
            title: '批量短链续期 ',
            keepAlive: false,
            isBack: false,
          },
          component: 
            () => import('@/components/page/UserMag/batchShortlinkReset.vue'),
        },
        {
          path: '/batchDynamicIPReset',
          meta: {
            title: '批量动态IP续期 ',
            keepAlive: false,
            isBack: false,
          },
          component: 
            () => import('@/components/page/UserMag/batchDynamicIPReset.vue'),
        },
        {
          path: '/batchUserList',
          meta: {
            title: '任务详情',
            keepAlive: false,
            isBack: false,
          },
          component: 
            () => import('@/components/page/UserMag/batchUserList.vue'),
        },
        //自动补发管理
        {
          path: '/AutoRecmanagement',
          meta: {
            title: '自动补发管理',
            keepAlive: true,
            isBack: false,
          },
          component: 
            () =>
              import(
                '@/components/page/SystemSettings/AutoRecmanagement/AutoRecmanagement.vue'
              ),
        },
        {
          path: '/Rechargerecord',
          meta: { title: '充值记录', keepAlive: true, isBack: false },
          component: 
            () =>
              import(
                '@/components/page/Rechargeconsumption/Rechargerecord/rechargerecord.vue'
              ),
        },
        // {
        //   path: '/Balancemanagement',
        //   meta: { title: '充值记录',keepAlive:true,isBack:false, },
        //   component: () => import ('@/components/page/Rechargeconsumption/Balancemanagement/components/SMSmsg.vue'),
        // },
        {
          path: '/salesManagement',
          meta: { title: '销售管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/salesManagement.vue'),
        },
        {
          path: '/MenuManagement',
          meta: { title: '菜单管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/MenuManagement.vue'),
        },
        {
          path: '/RoleManagement',
          meta: { title: '角色管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/RoleManagement.vue'),
        },
        {
          path: '/authorityManagement',
          meta: { title: '后台用户管理', keepAlive: true, isBack: false },
          component: 
            () =>
              import('@/components/page/SystemSettings/authorityManagement.vue'),
        },
        {
          path: '/LoginLog',
          meta: { title: '登录日志', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/LoginLog.vue'),
        },
        {
          path: '/LoginCellPhone',
          meta: { title: '手机号登录管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/LoginCellPhone.vue'),
        },
        {
          path: '/OperationLog',
          meta: { title: '平台操作日志', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/OperationLog.vue'),
        },
        {
          path: '/PersonalInformation',
          meta: { title: '个人信息', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/MyAccount/PersonalInformation.vue'),
        },
        {
          path: '/Personalization',
          meta: { title: '个性化设置', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/SystemSettings/Personalization.vue'),
        },
        {
          path: '/sendAssignment',
          meta: { title: '触发设置列表', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/manageVip/sendAssignment.vue'),
        },
        {
          path: '/manageSendList',
          meta: { title: '发送任务列表', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/manageVip/manageSendList.vue'),
        },
        {
          path: '/ShortchainSetting',
          meta: {
            title: '短链用户配置',
            keepAlive: true,
            isBack: false,
            hideTag: true,
          },
          component: 
            () => import('@/components/page/UserMag/ShortchainSetting.vue'),
        },
        {
          path: '/yuexinAudit',
          meta: { title: '阅信模板', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/yuexin/yuexinTemplateAudit.vue'),
        },
        {
          path: '/yuexinTemplateChannel',
          meta: { title: '模板渠道管理', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/yuexin/yuexinTemplateChannel.vue'),
        },
        {
          path: '/GeneratingRecords',
          meta: { title: '阅信短链', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/yuexin/yuexinLink.vue'),
        },
        {
          path: '/linkRefund',
          meta: { title: '链接返还', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/yuexin/yuexinLinkRefund.vue'),
        },
        {
          path: '/yuexinSendTask',
          meta: { title: '阅信发送任务', keepAlive: true, isBack: false },
          component: 
            () => import('@/components/page/yuexin/yuexinSendTask.vue'),
        },
        // {
        //   path: '/icone',
        //   component: resolve => require(['@/components/page/Icon.vue'], resolve),
        //   meta: { title: '基础表格' }
        // },
        // {
        //   path: '/tabs',
        //   component: resolve => require(['@/components/page/Tabs.vue'], resolve),
        //   meta: { title: 'tab选项卡' }
        // },
        // {
        //   path: '/form',
        //   component: resolve => require(['@/components/page/BaseForm.vue'], resolve),
        //   meta: { title: '基本表单' }
        // },
        // {
        //   // 富文本编辑器组件
        //   path: '/editor',
        //   component: resolve => require(['@/components/page/VueEditor.vue'], resolve),
        //   meta: { title: '富文本编辑器' }
        // },
        // {
        //   // markdown组件
        //   path: '/markdown',
        //   component: resolve => require(['@/components/page/Markdown.vue'], resolve),
        //   meta: { title: 'markdown编辑器' }
        // },
        // {
        //   // 图片上传组件
        //   path: '/upload',
        //   component: resolve => require(['@/components/page/Upload.vue'], resolve),
        //   meta: { title: '文件上传' }
        // },
        // {
        //   // vue-schart组件
        //   path: '/charts',
        //   component: resolve => require(['@/components/page/BaseCharts.vue'], resolve),
        //   meta: { title: 'schart图表' }
        // },
        // {
        //   // 拖拽列表组件
        //   path: '/drag',
        //   component: resolve => require(['@/components/page/DragList.vue'], resolve),
        //   meta: { title: '拖拽列表' }
        // },
        // {
        //   // 权限页面
        //   path: '/permission',
        //   component: resolve => require(['@/components/page/Permission.vue'], resolve),
        //   meta: { title: '权限测试', }
        // },
        {
          path: '/404',
          component: () =>
            import('@/components/page/404.vue'),
          meta: { title: '404' },
        },
        {
          path: '/403',
          component: () =>
            import('@/components/page/403.vue'),
          meta: { title: '403' },
        },
      ],
    },
    {
      path: '/login',
      component: () => import('@/components/page/Login.vue'),
    },
    {
      path: '/signatureList',
      component: () => import('@/components/page/signature/signature.vue'),
    },
    {
      path: '/:catchAll(.*)',
      name: "NotFound",
      redirect: '/404',
    },
    // {
    //   path: '/about',
    //   name: 'about',
    //   // route level code-splitting
    //   // this generates a separate chunk (about.[hash].js) for this route
    //   // which is lazy-loaded when the route is visited.
    //   component: () => import(/* webpackChunkName: "about" */ '@/pages/About.vue')
    // }
  ],
})
