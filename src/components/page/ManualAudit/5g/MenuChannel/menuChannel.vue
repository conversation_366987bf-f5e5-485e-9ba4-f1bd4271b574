<template>
  <div class="bag" style="background: #fff; padding: 10px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-page-header
            style="color: #16a589"
            @back="goBack"
            content="菜单报备"
          >
          </el-page-header
        ></el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div style="padding: 0 20px">
      <el-button type="primary" @click="refresh"
        ><el-icon><RefreshRight /></el-icon>刷新</el-button
      >
      <el-button type="primary" @click="addChannel">添加通道</el-button>
    </div>
    <div style="padding: 20px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
      >
        <el-table-column label="菜单ID">
          <template v-slot="scope">{{ scope.row.menuId }}</template>
        </el-table-column>
        <el-table-column label="通道名称">
          <template v-slot="scope">{{ scope.row.channelName }}</template>
        </el-table-column>
        <el-table-column label="通道ID">
          <template v-slot="scope">{{ scope.row.channelId }}</template>
        </el-table-column>
        <el-table-column label="通道运营商">
          <template v-slot="scope">
            <div
              v-if="scope.row.channelOperator == 1"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-yidong"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">移动</span>
            </div>
            <div
              v-else-if="scope.row.channelOperator == 2"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-liantong"
                style="font-size: 16px; color: #f56c6c"
              ></i>
              <span style="margin-left: 5px">联通</span>
            </div>
            <div
              v-else-if="scope.row.channelOperator == 3"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-dianxin"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">电信</span>
            </div>
            <div v-else></div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="服务代码">
              <template #default="scope">{{ scope.row.smsPort }}</template>
            </el-table-column> -->
        <el-table-column label="状态">
          <template v-slot="scope">
            <span style="color: red" v-if="scope.row.status == 10">审核中</span>
            <span style="color: #16a589" v-else-if="scope.row.status == 30"
              >审核通过</span
            >
            <span style="color: #f56c6c" v-else-if="scope.row.status == 20"
              >审核失败</span
            >
          </template>
        </el-table-column>
        <el-table-column label="原因">
          <template v-slot="scope">{{ scope.row.reason }}</template>
        </el-table-column>
        <el-table-column label="创建时间">
          <template v-slot="scope">
            <span>{{
              moment(scope.row.createdTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.status == 20 || scope.row.status == 30"
              @click="declareChannel(scope.row)"
              type="text"
              style="margin-left: 10px"
              >通道报备</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      title="添加通道"
      v-model="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <!-- <span>这是一段信息</span> -->
      <el-form
        :model="formInline"
        :rules="rules"
        label-width="90px"
        ref="formInline"
        class="demo-form-inline"
      >
        <el-form-item label="通道" prop="channelId">
          <el-select
            v-model="formInline.channelId"
            @change="handChannel"
            placeholder="选择通道"
            class="input-w"
          >
            <el-option
              v-for="item in channel"
              :key="item.channelId"
              :label="item.channelName"
              :value="item.channelId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitChannel('formInline')"
            >提交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      chatbotId: '',
      isFirstEnter: false,
      dialogVisible: false,
      tableDataObj: {
        tableData: [],
        loading2: false,
      },
      channel: [],
      formInline: {
        channelId: '',
        channelName: '',
        channelOperator: '',
        menuId: '',
        id: '',
      },
      rules: {
        channelId: [
          { required: true, message: '请选择通道', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.id = this.$route.query.menuId
    // this.chatbotId = this.$route.query.chatbotId
    this.token = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    this.getList()
    this.channel5GList()
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.id = this.$route.query.menuId
        // this.chatbotId = this.$route.query.chatbotId
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
        this.getList()
        this.channel5GList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    goBack() {
      this.$router.push({ path: '/MenuAudit' })
    },
    getList() {
      this.tableDataObj.loading2 = true
      window.api.get(
        window.path.fiveWeb + '/operator/menu/selectChannelList/' + this.id,
        {},
        (res) => {
          this.tableDataObj.tableData = res.data
          this.tableDataObj.loading2 = false
        }
      )
    },
    /**获取通道*/
    channel5GList() {
      window.api.get(
        window.path.fiveWeb + '/operator/menu/getAvailableChannels/' + this.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.channel = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        }
      )
    },
    handChannel(e) {
      this.channel.forEach((item) => {
        if (e == item.channelId) {
          this.formInline.channelName = item.channelName
          this.formInline.channelOperator = item.channelOperator
        }
      })
    },
    addChannel() {
      this.dialogVisible = true
      // this.channel5GList()
    },
    refresh() {
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    declareChannel(row) {
      this.formInline.channelId = row.channelId + ''
      this.formInline.channelName = row.channelName
      this.formInline.channelOperator = row.channelOperator
      this.formInline.id = row.id
      this.formInline.menuId = row.menuId
      this.$confirms.confirmation(
        'post',
        `重新报备通道!`,
        window.path.fiveWeb + '/operator/menu/addChannel',
        this.formInline,
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.formInline.channelId = ''
            this.formInline.channelName = ''
            this.formInline.channelOperator = ''
            this.formInline.id = ''
            this.formInline.menuId = ''
            this.getList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
      // console.log(row);
    },
    submitChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formInline.menuId = this.$route.query.menuId
          window.api.post(
            window.path.fiveWeb + '/operator/menu/addChannel',
            this.formInline,
            (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.dialogVisible = false
                this.getList()
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.formInline.channelId = ''
        this.formInline.channelName = ''
        this.formInline.channelOperator = ''
        this.formInline.id = ''
        this.formInline.menuId = ''
      }
    },
  },
}
</script>
