<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="手机号" prop="mobile">
              <el-input
                v-model="formInline.mobile"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain style="" @click="ListSearch"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
                  
                    </div> -->
        <div class="sensitive-fun" style="margin-top: 10px">
          <!-- <span class="sensitive-list-header">携号转网列表</span> -->
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <table-tem :tableDataObj="tableDataObj"></table-tem>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    DatePlugin,
    TableTem
  },
  name: 'PortNumberTransfer',
  data() {
    return {
      isFirstEnter: false,
      // 搜索数据
      formInline: {
        mobile: '',
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        mobile: '',
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          { prop: 'mobile', showName: '手机号', fixed: false },
          { prop: 'province', showName: '省份', fixed: false },
          { prop: 'originalOperator', showName: '原始运营商', fixed: false },
          { prop: 'forwardOperator', showName: '转网运营商', fixed: false },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: 180,
            fixed: false,
            formatData: function (val) {
              return formatDate(new Date(val * 1), 'YYYY-MM-DD HH:mm:ss')
            },
          },
          {
            prop: 'updateTime',
            showName: '更新时间',
            width: 180,
            fixed: false,
            formatData: function (val) {
              return formatDate(new Date(val * 1), 'YYYY-MM-DD HH:mm:ss')
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '120', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingnumberportabilityrecord/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs.formInline.resetFields()
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
