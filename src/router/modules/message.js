// 消息业务相关路由（短信、彩信、视频短信等）
export default [
  // 彩信相关
  {
    path: '/MMSAudit',
    meta: { title: ' 彩信发送审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/MMSAudit.vue'),
  },
  {
    path: '/MMSinterfaceSend',
    meta: { title: ' 彩信接口发送审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/MMSinterfaceSend.vue'),
  },
  {
    path: '/MMSReplacement',
    meta: { title: ' 彩信补发管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/MMSReplacement.vue'),
  },
  {
    path: '/MMSWebTask',
    meta: { title: '彩信web发送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/MMSWebTask.vue'),
  },
  {
    path: '/MMSinterfaceTask',
    meta: { title: '彩信接口发送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/MMSinterfaceTask.vue'),
  },
  {
    path: '/MMSreplyDetails',
    meta: { title: '彩信用户回复明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/MMSreplyDetails.vue'),
  },
  {
    path: '/MMSsendDetails',
    meta: { title: '彩信发送明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/MMSsendDetails.vue'),
  },

  // 视频短信相关
  {
    path: '/RMSAudit',
    meta: { title: ' 视频短信模板审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/RMSAudit.vue'),
  },
  {
    path: '/RMSReplacement',
    meta: { title: ' 视频短信补发管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/RMSReplacement.vue'),
  },
  {
    path: '/videoTemplate',
    meta: { title: '视频短信模板管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/videoTemplate.vue'),
  },
  {
    path: '/RMSWebTask',
    meta: { title: '视频短信web发送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/RMSWebTask.vue'),
  },
  {
    path: '/RMSinterfaceTask',
    meta: { title: '视频短信接口发送任务管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/RMSinterfaceTask.vue'),
  },
  {
    path: '/RMSsendDetails',
    meta: { title: '视频短信发送明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/RMSsendDetails.vue'),
  },
  {
    path: '/RMSreplyDetails',
    meta: { title: '视频短信用户回复明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/RMSreplyDetails.vue'),
  },

  // 短信相关
  {
    path: '/userSendNotes',
    meta: { title: '短信发送明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/userSendNotes.vue'),
  },
  {
    path: '/userReplyData',
    meta: { title: '短信用户回复明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/userReplyData.vue'),
  },

  // 国际短信相关
  {
    path: '/IMSWebTask',
    meta: { title: '国际短信接口发送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/IMSWebTask.vue'),
  },
  {
    path: '/ImsSendNotes',
    meta: { title: '国际短信发送明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/ImsSendNotes.vue'),
  },
  {
    path: '/ImsReplyDetails',
    meta: { title: '国际短信用户回复明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/ImsReplyDetails.vue'),
  },
  {
    path: '/InternationalPrice',
    meta: { title: '国际价格表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/InternationalPrice.vue'),
  },

  // 语音相关
  {
    path: '/voiceAudit',
    meta: { title: ' 语音短信审核', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/voiceAudit.vue'),
  },
  {
    path: '/ViocReplacement',
    meta: { title: '语言通知补发管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/ViocReplacement.vue'),
  },
  {
    path: '/voiceWebTask',
    meta: { title: '语音web发送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/voiceWebTask.vue'),
  },
  {
    path: '/VoiceInterfaceTask',
    meta: { title: '语音接口发送管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/VoiceInterfaceTask.vue'),
  },
  {
    path: '/voiceSendNotes',
    meta: { title: '语音短信计划', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/voiceSendNotes.vue'),
  },

  // 闪验相关
  {
    path: '/FlashTestApp',
    meta: { title: '闪验App应用列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/FlashTestApp.vue'),
  },
  {
    path: '/FlashTestSendNotes',
    meta: { title: '闪验发送明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/StatisticalAnalysis/FlashTestSendNotes.vue'),
  },
] 