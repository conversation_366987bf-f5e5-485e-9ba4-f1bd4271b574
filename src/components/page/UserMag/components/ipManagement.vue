<template>
  <div>
    <div class="LoginCellPhone-matter">
      <div>温馨提示</div>
      <div>1.多个IP之间请用英文逗号(,)隔开，最多支持100个IP；</div>
      <div>2.ip支持3种格式：</div>
      <div>1）静态IP格式：*************</div>
      <div>2）CIDR格式：***********/24</div>
      <div>3）通配符格式：101.87.66.* （不推荐）</div>
      <div>3. 不支持内网IP</div>
    </div>
    <div class="LoginCellPhone-creat">
      <el-button type="primary" @click="addphone()">添加IP</el-button>
    </div>
    <div>
      <el-table
        v-loading="tableDataObj.loading2"
        :data="tableDataObj.tableData"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        border
        :stripe="true"
        style="width: 100%"
      >
        <el-table-column label="用户名">
          <template #default="scope">
            <span>{{ scope.row.consumerName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="ip">
          <template #default="scope">
            <div>
              {{ scope.row.ip }}
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="备注">
          <template #default="scope">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作">
          <template #default="scope">
            <el-button
              style="color: #f56c6c"
              link
              @click="deletephone(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      title="添加接口IP"
      v-model="Ipvisible"
      width="560px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-form
        :model="addIpForm"
        :rules="addPhoneRules"
        ref="ruleForm3"
        class="demo-ruleForm"
        label-width="80px"
      >
        <el-form-item label="IP" prop="ip">
          <el-input
            @paste.prevent="handlePaste"
            type="textarea"
            v-model.trim="addIpForm.ip"
            placeholder="多个IP之间请用英文逗号(,)隔开；最多支持100个IP"
            style="height: 100px;"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="Ipvisible = false"
            style="width: 100px; padding: 9px 0"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submitForm(ruleForm3)"
            style="width: 100px; padding: 9px 0"
            >提交</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
  
  <script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const isFirstEnter = ref(false);
const roleId = ref("");
isFirstEnter.value = true;
onActivated(() => {
  if (meta.isBack || isFirstEnter.value) {
    nextTick(() => {
      let userInfo = JSON.parse(localStorage.getItem("userInfo"));
      if (userInfo) {
        roleId.value = userInfo.roleId;
      }
      InquireList();
      // getApiinfo();
    });
  } else {
    meta.isBack = false;
    isFirstEnter.value = false;
  }
});
const ruleForm3 = ref(null);
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
});
const Ipvisible = ref(false);
const addIpForm = reactive({
  ip: "",
});
const checkIp = (rule, value, callback) => {
  if (value) {
    // return callback(new Error("接口ip不能为空"));
    // 1.^ 和 $ 确保整个字符串符合模式。
    // 2.(\*|(\d{1,3})(\.(\*|\d{1,3})){3}) 匹配一个标准 IPv4 地址或带有通配符的 IPv4 地址。
    // 3.(/\\d{1,2})? 匹配可选的 CIDR 表示法。
    // 4.,(\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,2})?){0,29} 匹配 0 到 29 个额外的 IP 地址，逗号分隔，从而限制总共最多 30 个 IP

    const ipListRegex =
      /^((\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,3})?)(,(\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,3})?){0,99}$/;
    let ipvalue = value.replace(/\s+/g, ""); // 去除空格
    const maxLimit = 100; // 最大IP地址数量限制
    const ipArray = value.split(","); // 按逗号分隔
    if (ipArray.length > maxLimit) {
      return callback(new Error(`最多只能输入${maxLimit}个IP地址`));
    } else {
      // 数量超过限制，不做处理
      if (!ipListRegex.test(ipvalue)) {
        return callback(new Error("接口ip格式错误"));
      } else {
        callback();
      }
    }
  } else {
    return callback(new Error("ip不能为空"));
  }
};
const addPhoneRules = reactive({
  ip: [
    { required: true, validator: checkIp, trigger: "blur" },
    // { min: 6, max: 6, message: '请输入6位数字验证码' }
  ],
});

const handlePaste = (event) => {
  const clipboardData = event.clipboardData || window.clipboardData;
  const pastedData = clipboardData.getData('Text');
  if (pastedData) {
    let formattedIPs = ''
    // 使用正则表达式拆分内容，考虑多种分隔符
    const ipList = pastedData.split(/[\s,]+/);
    // 去重并过滤掉空字符串
    const uniqueIPs = Array.from(new Set(ipList)).filter(ip => ip.trim() !== '');
    // 将处理后的IP列表转换为逗号分隔的字符串
    formattedIPs = uniqueIPs.join(',');
    // 阻止默认的粘贴行为并将结果手动插入
    event.target.value = formattedIPs;
    addIpForm.ip = formattedIPs;
  }
}

//打开弹窗
const addphone = () => {
  try {
    if (tableDataObj.tableData.length >= 100) {
      ElMessage({
        type: "error",
        message: "最多只能添加30个IP！",
      });
      return;
    } else {
      Ipvisible.value = true;
    }
  } catch (error) {
    console.warn(error);
  }
};
//关闭弹窗
const handelClose = () => {
  Ipvisible.value = false;
};
//提交表单
const submitForm = (formName) => {
  ruleForm3.value.validate((valid) => {
    if (valid) {
      let data = {
        userId: $router.currentRoute.value.query.id,
        ip: addIpForm.ip,
      };
      window.api.post(
        window.path.omcs + "operatinguser/userInterfaceIp",
        data,
        (res) => {
          if (res.code == 200) {
            Ipvisible.value = false;
            InquireList();
            ElMessage({
              type: "success",
              message: "添加成功",
            });
          } else {
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      ElMessage({
        type: "error",
        message: "请检查输入项",
      });
    }
  });
};

//获取列表
const InquireList = () => {
  tableDataObj.loading2 = true;
  window.api.get(
    window.path.omcs +
      "operatinguser/userInterfaceIp/" +
      $router.currentRoute.value.query.id,
    {},
    (res) => {
      if (res.code == 200) {
        tableDataObj.loading2 = false;
        tableDataObj.tableData = res.data;
      }
    }
  );
};

//删除ip
const deletephone = (row) => {
  ElMessageBox.confirm("确认删除该IP？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      window.api.put(
        window.path.omcs + "operatinguser/userInterfaceIp",
        { ip: row.ip, userId: $router.currentRoute.value.query.id },
        (res) => {
          if (res.code == 200) {
            InquireList();
            ElMessage({
              type: "success",
              message: "删除成功",
            });
          } else {
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作取消",
      });
    });
};

watch(
  () => Ipvisible.value,
  (val) => {
    if (!val) {
      ruleForm3.value.resetFields();
    }
  }
);
</script>
  
  <style scoped>
.LoginCellPhone-box {
  padding: 20px;
}

.LoginCellPhone-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.LoginCellPhone-matter > div {
  /* height:26px; */
  line-height: 26px;
}

.LoginCellPhone-creat {
  margin: 20px 0px;
}

.add-white-list {
  width: 650px;
  height: 50px;
  line-height: 50px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.add-white {
  width: 640px;
  height: 30px;
  line-height: 30px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.input-t {
  width: 250px;
}
</style>