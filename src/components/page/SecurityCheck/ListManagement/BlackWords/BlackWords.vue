<template>
  <div>
    <div class="Top_title">
      <span>黑词白名单管理</span>
    </div>
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        label-width="82px"
        ref="formBlackList"
      >
        <el-form-item label="公司名称" label-width="82px" prop="area">
          <el-input
            v-model="formBlackList.area"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="免审词汇" label-width="82px" prop="area">
          <el-input
            v-model="formBlackList.area"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户名" label-width="82px" prop="mobile">
          <el-input
            v-model="formBlackList.mobile"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建时间" label-width="82px" prop="time">
          <el-date-picker
            class="input-w"
            v-model="formBlackList.time"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建人" label-width="82px" prop="creater">
          <el-input
            v-model="formBlackList.creater"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" label-width="82px" prop="mobile">
          <el-input
            v-model="formBlackList.mobile"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <div class="boderbottom" style="padding-left: 10px">
          <el-button type="primary" plain @click="queryForm()">查询</el-button>
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
          <el-button type="primary" plain>导出</el-button>
        </div>
      </el-form>

      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header" >黑名单列表</span> -->
        <el-button type="primary">新增黑词白名单</el-button>
        <el-button
          type="danger"
          @click="batchDeletion"
          v-if="selectId.length > 1"
          >批量删除</el-button
        >
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        ></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="1"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tableData.length"
            >
            </el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
export default {
  name: 'BlackWords',
  components: {
    TableTem,
  },
  data() {
    return {
      formBlackList: {
        //查询框的值
        complaintType: '',
        area: '',
        mobile: '',
        blackLevl: '',
        time: '',
        creater: '',
      },
      selectId: [], //批量操作选中id
      tableDataObj: {
        //列表数据
        tablecurrent: {
          current: '2',
          size: '10',
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'blacklistPhone',
            showName: '手机号码',
            fixed: false,
          },
          {
            prop: 'blacklistType',
            showName: '投诉类别',
            fixed: false,
            formatData: function (val) {
              if (val == 1) {
                return '运营商投诉'
              } else if (val == 2) {
                return '工信部投诉'
              }
            },
          },
          {
            prop: 'blacklistLevel',
            showName: '黑号等级',
            fixed: false,
            formatData: function (val) {
              return val == '1' ? '一级' : '二级'
            },
          },
          {
            prop: 'blacklistCity',
            showName: '归属',
            fixed: false,
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
            width: '140px',
            formatData: function (val) {
              return formatDate(new Date(val * 1000), 'YYYY-MM-DD HH:mm:ss')
            },
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '130', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'delet',
            icon: 'el-icon-edit',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'delet',
            icon: 'el-icon-error',
            color: '#F56C6C',
          },
        ],
      },
    }
  },
  methods: {
    getTableDtate() {
      debugger
      window.api.post(
        'api/operatingblacklist/page',
        {
          current: this.tableDataObj.tablecurrent.current,
          size: this.tableDataObj.tablecurrent.size,
        },
        (res) => {
          debugger
          this.tableDataObj.tableData = res.records
          console.log(res.records)
          // this.loading = false
        }
      )
    },
    queryForm() {
      //查询
      let formData = JSON.stringify(this.formBlackList) //查询条件的值
      // this.loading = true
      // window.api.post('api/login/do_login_code',formData,res=>{
      //     this.tableDataObj.tableData = res.data
      //     this.loading = false
      // })
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
    },
    handelSelection(val) {
      //列表复选框的值
      JSON.stringify(val)
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
      }
      this.selectId = selectId
      console.log(this.selectId)
    },
    handelOptionButton: function (val) {
      //操作按钮
      if (val.methods == 'delet') {
        //删除
      }
    },
    batchDeletion() {
      //批量删除
      if (this.selectId.length > 0) {
        console.log('有值')
      }
    },
    handleSizeChange(size) {
      this.pagesize = size
    },
    handleCurrentChange: function (currentPage) {
      this.currentPage = currentPage
    },
  },
  mounted() {
    this.getTableDtate()
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
