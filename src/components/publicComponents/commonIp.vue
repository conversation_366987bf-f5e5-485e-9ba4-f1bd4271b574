<template>
  <div>
    <!-- <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="1000"
      :before-close="handleClose"
    >
      <div class="commonIpMangForm">
        <div class="commonIpMangFormBox">
          <el-form
            ref="commonIpMangForm"
            :model="commonIpMangFormoptions"
            :rules="similarRules"
            label-width="80px"
          >
            <el-form-item label="IP" prop="ip">
              <el-input
                style="height: 100px"
                @paste.prevent="handlePaste"
                type="textarea"
                v-model.trim="commonIpMangFormoptions.ip"
                placeholder="多个IP之间请用英文逗号(,)隔开；最多支持100个IP"
              ></el-input>
            </el-form-item>
            <el-form-item label="有效期" prop="effectTime">
              <el-date-picker
                style="width: 100%"
                v-model="commonIpMangFormoptions.effectTime"
                type="datetime"
                :disabled-date="disabledDate"
                placeholder="请选择有效期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="commonIpMangFormoptions.remark"
                placeholder="请输入备注"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                @click="addSimilarMobile(commonIpMangForm)"
                >添加</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="commonIpMangTableBox"></div>
        <div class="commonIpMangTable">
          <vxe-table
            ref="tableRef"
            id="commonIpMangTableVisible"
            border
            stripe
            show-footer
            :custom-config="{ storage: true }"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px"
            :data="tableDataObj.tableData"
          >
            <vxe-column
              field="用户名"
              title="用户名"
              width="100"
              show-header-overflow show-overflow="title" show-footer-overflow
            >
              <template v-slot="scope">
                <span>
                  {{ scope.row.username }}
                </span>
              </template>
            </vxe-column>
            <vxe-column
              field="IP"
              title="IP"
              width="120"
              show-header-overflow show-overflow="title" show-footer-overflow
            >
              <template v-slot="scope">
                <span>
                  {{ scope.row.ip }}
                </span>
              </template>
            </vxe-column>
            <vxe-column
              field="有效期"
              title="有效期"
              width="160"
              show-overflow
            >
              <template v-slot="scope">
                <div v-if="scope.row.effectTime">
                  <div
                    v-if="
                      scope.row.remainingDays > 0 && scope.row.remainingDays < 7
                    "
                    style="display: flex; align-items: center"
                  >
                    <div>
                      {{
                        moment(scope.row.effectTime).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )
                      }}
                    </div>
                    <el-tooltip placement="top">
                      <template #content>
                        <div style="font-size: 12px">
                          即将到期，剩余{{ scope.row.remainingDays }}天
                        </div>
                      </template>
                      <el-icon
                        style="
                          color: #e6a23c;
                          margin-left: 5px;
                          font-size: 14px;
                        "
                        ><InfoFilled
                      /></el-icon>
                    </el-tooltip>
                  </div>
                  <div
                    v-else-if="scope.row.remainingDays <= 0"
                    style="display: flex; align-items: center"
                  >
                    <div style="cursor: pointer">
                      {{
                        moment(scope.row.effectTime).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )
                      }}
                    </div>
                    <el-tooltip placement="top">
                      <template #content>
                        <div style="font-size: 12px">已到期</div>
                      </template>
                      <el-icon
                        style="
                          color: #f56c6c;
                          margin-left: 5px;
                          font-size: 14px;
                        "
                        ><InfoFilled
                      /></el-icon>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    {{
                      moment(scope.row.effectTime).format("YYYY-MM-DD HH:mm:ss")
                    }}
                  </div>
                </div>
              </template>
            </vxe-column>
            <vxe-column
              field="备注"
              title="备注"
              width="90"
              show-header-overflow show-overflow="title" show-footer-overflow
            >
              <template v-slot="scope">
                <span>
                  {{ scope.row.remark }}
                </span>
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="90" fixed="right">
              <template v-slot="scope">
                <el-button
                  link
                  style="color: red"
                  @click="deleteSimilarMobile(scope.row)"
                  >删 除</el-button
                >
              </template>
            </vxe-column>
          </vxe-table>
          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.currentPage"
              :page-size="formInline.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
// import { defineEmits } from 'vue';
import moment from "moment";
const props = defineProps({
  showVisible: {
    type: Boolean,
    default: false,
  },
  userId: {
    type: String,
    default: "",
  },
});
const emit = defineEmits();
const tableRef = ref(null);
const commonIpMangForm = ref(null);
const dialogVisible = ref(props.showVisible);
const title = ref("添加动态IP");
const formInline = reactive({
  userId: "",
  ip: "",
  currentPage: 1,
  pageSize: 10,
});
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
  total: 0,
});
const commonIpMangFormoptions = reactive({
  userId: "",
  ip: "",
  remark: "",
  effectTime: "",
  status: "",
  id: "",
});
const disabledDate = (time) => {
  const currentDate = new Date(); // 获取当前日期
  const nextMonth = new Date(currentDate); // 复制当前日期
  nextMonth.setMonth(currentDate.getMonth() + 1); // 当前日期加一个月
  // 如果选择的日期早于当前日期，则禁用
  return (
    time.getTime() > nextMonth.getTime() ||
    time.getTime() + 86400000 < currentDate.getTime()
  );
};
const checkIp = (rule, value, callback) => {
  if (value) {
    // return callback(new Error("接口ip不能为空"));
    // 1.^ 和 $ 确保整个字符串符合模式。
    // 2.(\*|(\d{1,3})(\.(\*|\d{1,3})){3}) 匹配一个标准 IPv4 地址或带有通配符的 IPv4 地址。
    // 3.(/\\d{1,2})? 匹配可选的 CIDR 表示法。
    // 4.,(\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,2})?){0,29} 匹配 0 到 29 个额外的 IP 地址，逗号分隔，从而限制总共最多 30 个 IP
    const ipListRegex =
      /^((\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,3})?)(,(\*|(\d{1,3})(\.(\*|\d{1,3})){3})(\/\d{1,3})?){0,99}$/;
    let ipvalue = value.replace(/\s+/g, ""); // 去除空格
    const maxLimit = 100; // 最大IP地址数量限制
    const ipArray = value.split(","); // 按逗号分隔
    if (ipArray.length > maxLimit) {
      return callback(new Error(`最多只能输入${maxLimit}个IP地址`));
    } else {
      // 数量超过限制，不做处理
      if (!ipListRegex.test(ipvalue)) {
        return callback(new Error("接口ip格式错误"));
      } else {
        callback();
      }
    }
  } else {
    return callback(new Error("ip不能为空"));
  }
};
const similarRules = reactive({
  // username: [
  //   { required: true, message: "请输入用户名", trigger: "blur" },
  //   { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
  // ],
  effectTime: [{ required: true, message: "请选择有效期", trigger: "blur" }],
  ip: [
    { required: true, validator: checkIp, trigger: "blur" },
    // { min: 6, max: 6, message: '请输入6位数字验证码' }
  ],
});
const gettableLIst = () => {
  //获取列表
  const currentDate = new Date();
  formInline.userId = props.userId;
  tableDataObj.loading2 = true;
  window.api.post(
    window.path.omcs + "operatorClientIp/page",
    formInline,
    (res) => {
      tableDataObj.loading2 = false;
      tableDataObj.total = res.data.total;
      tableDataObj.tableData = res.data.records;
      tableDataObj.tableData.forEach((item) => {
        if (item.effectTime) {
          item.expirationDate = new Date(item.effectTime);
          item.remainingTime = item.expirationDate - currentDate;
          item.remainingDays = Math.ceil(
            item.remainingTime / (1000 * 60 * 60 * 24)
          );
        }
      });
    }
  );
};
gettableLIst()
const handleSizeChange = (size) => {
  formInline.pageSize = size;
    gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  formInline.currentPage = currentPage;
    gettableLIst();
};
const handleClose = () => {
  dialogVisible.value = false;
  emit("handleClose",dialogVisible.value);
};
const addSimilarMobile = (formEl) => {
  try {
    formEl.validate((valid) => {
      if (valid) {
        commonIpMangFormoptions.userId = props.userId;
        commonIpMangFormoptions.effectTime = moment(
          commonIpMangFormoptions.effectTime
        ).format("YYYY-MM-DD HH:mm:ss");
        // commonIpMangFormoptions.ip = commonIpMangFormoptions.ip;
        window.api.post(
          window.path.omcs + "operatorClientIp",
          commonIpMangFormoptions,
          (res) => {
            if (res.code == 200) {
            //   dialogVisible.value = false;
              ElMessage.success("新增成功");
            //   emit("handleClose",dialogVisible.value);
              gettableLIst();
            } else {
              ElMessage.error(res.msg);
            }
          }
        );
      } else {
        console.log("error submit!!");
        return false;
      }
    });
  } catch (error) {
    console.error(error);
  }
};
const deleteSimilarMobile = (row) => {
  try {
    ElMessageBox.confirm("确定删除吗？", "提醒", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        window.api.delete(
          window.path.omcs + "operatorClientIp/" + row.id,
          {},
          (res) => {
            if (res.code == 200) {
              ElMessage.success("删除成功");
              gettableLIst();
            } else {
              ElMessage.error(res.msg);
            }
          }
        );
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消",
        });
      });
  } catch (error) {
    console.error(error);
  }
};
const handlePaste = (event) => {
  const clipboardData = event.clipboardData || window.clipboardData;
  const pastedData = clipboardData.getData('Text');
  if (pastedData) {
    let formattedIPs = ''
    // 使用正则表达式拆分内容，考虑多种分隔符
    const ipList = pastedData.split(/[\s,]+/);
    // 去重并过滤掉空字符串
    const uniqueIPs = Array.from(new Set(ipList)).filter(ip => ip.trim() !== '');
    // 将处理后的IP列表转换为逗号分隔的字符串
    formattedIPs = uniqueIPs.join(',');
    // 阻止默认的粘贴行为并将结果手动插入
    event.target.value = formattedIPs;
    commonIpMangFormoptions.ip = formattedIPs;
  }
}
watch(
  () => props.showVisible,
  (val) => {
    // console.log("val", val);

    // if (!val) {
    //   ruleFormRef.value.resetFields();
    // }
  }
);
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      commonIpMangForm.value.resetFields();
      commonIpMangFormoptions.userId = "";
      commonIpMangFormoptions.effectTime = "";
      commonIpMangFormoptions.ip = "";
      title.value = "";
      emit("handleClose",false);
    }
  }
);
</script>

<style lang="less" scoped>
.commonIpMangForm {
  width: 100%;
  display: flex;
}
.commonIpMangFormBox {
  width: 40%;
}
.commonIpMangTableBox {
  border-right: 1px dashed #e6e6e6;
  margin: 0 10px;
}
.commonIpMangTable {
  flex: 1;
}
</style>