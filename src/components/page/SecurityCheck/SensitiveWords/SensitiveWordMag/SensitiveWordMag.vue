<template>
  <div class="container_left">
    <div class="Templat-matter">
      <p>
        <span style="font-weight: bolder">tips：</span>
        <span>敏感词设置完成5分钟后生效</span>
      </p>
      <!-- <p>敏感词设置完成5分钟后生效</p> -->
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          ref="sensitiveCondition"
        >
          <el-form-item label="敏感词" prop="words">
            <el-input
              v-model="sensitiveCondition.words"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建人" prop="createName">
            <el-input
              v-model="sensitiveCondition.createName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="标签" prop="labelName">
            <el-input
              v-model="sensitiveCondition.labelName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" prop="time">
            <el-date-picker
              class="input-time"
              v-model="sensitiveCondition.time"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button
              type="primary"
              plain
              @click.prevent="Query()"
              @keyup.enter="Query()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
        >
          <template #tableButtons>
            <el-button
              type="primary"
              @click="addFunction"
              >新增敏感词</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 弹窗（新增） -->
      <el-dialog
        :title="title"
        v-model="sensitiveAddedDialog"
        width="800px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="sensitiveAdded.formData"
          :rules="sensitiveAdded.formRule"
          ref="sensitiveAdded"
          label-width="96px"
          style="padding: 0 20px"
        >
          <el-form-item label="敏感词" prop="words">
            <el-input
              v-model="sensitiveAdded.formData.words"
              placeholder="不允许带特殊符号"
            ></el-input>
          </el-form-item>
          <el-form-item label="敏感词标签">
            <el-select
              class="sensitiveClass input-w"
              v-model="sensitiveValue[item.id]"
              v-for="item in sensitiveOptions"
              :key="item.id"
              clearable
              :placeholder="item.labelName"
            >
              <el-option
                v-for="items in item.children"
                :key="items.id"
                :label="items.labelName"
                :value="items.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="5"
              resize="none"
              v-model="sensitiveAdded.formData.remark"
              @input="updateView($event)"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              @click="sensitiveAddedDialog = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              type="primary"
              @click="handelAlert_ok_add('sensitiveAdded')"
              >确 认</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    TableTem
  },
  name: 'SensitiveWordMag',
  data() {
    return {
      isFirstEnter: false,
      sensitiveOptions: '', // 敏感词标签
      sensitiveValue: {},
      header: '', //请求头
      sensitiveAdded: {
        //新增弹框数据
        formData: {
          words: '',
          labelId: [],
          remark: '',
        },
        formRule: {
          //验证规则
          words: [
            { required: true, message: '请输入敏感词', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
            // { pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+$/, message: '含非法字符（由字母，数字，中文组成）！' }
          ],
          remark: [
            { required: true, message: '请输入敏感词', trigger: 'blur' },
            {
              min: 1,
              max: 100,
              message: '长度在 1 到 100 个字符',
              trigger: 'blur',
            },
          ],
          // labelId:[
          //     { required: true, message: '请选择敏感词等级', trigger: 'change' },
          // ]
        },
      },
      title: '添加敏感词',
      rowId: '', //操作行的id
      sensitiveAddedDialog: false, //新增弹窗显示状态
      //查询条件的值
      sensitiveCondition: {
        level: '',
        words: '',
        createName: '',
        time: [],
        labelName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        level: '',
        words: '',
        createName: '',
        time: [],
        labelName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        custom: true,
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        id: "SensitiveWordMag",
        loading2: false,
        tableData: [],
        tableLabel: [
          //列表标题
          {
            prop: 'words',
            showName: '敏感词',
            width: '130px',
          },
          {
            prop: 'wordsLabelName',
            showName: '标签',
            showTags: {
              bgColor: [
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
                '#16a085',
                '#ff9900',
                '#e95d69',
              ],
            },
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '160px',
          },
          {
            prop: 'createName',
            showName: '创建人',
            width: '120px',
          },
          {
            prop: 'remark',
            showName: '备注',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '150', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          //操作按钮
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'setLabel',
            icon: 'Setting',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'delLabel',
            icon: 'CircleCloseFilled',
            color: '#F56C6C',
          },
        ],
      },
    }
  },
  methods: {
    // 获取敏感词标签
    findTreeDatas() {
      window.api.post(
        window.path.omcs + 'operatingblackwordslabel/page ',
        {},
        (res) => {
          this.sensitiveOptions = res.data
          this.sensitiveOptions.forEach((item) => {
            this.sensitiveValue[item.id] = '' // 父、  子、  子的赋值
          })
        }
      )
    },
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingblackwords/page',
        this.sensitiveConObj,
        (res) => {
          for (let i = 0; i < res.data.records.length; i++) {
            if (res.data.records[i].wordsLabelName) {
              res.data.records[i].wordsLabelName =
                res.data.records[i].wordsLabelName.split(',')
            }
          }
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.totalRow = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    updateView(e) {
      this.$forceUpdate()
    },
    //查询
    Query() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition)
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
      this.getTableDtate()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
      this.getTableDtate()
    },
    //操作框各个按钮功能（编辑标签，删除）
    handelOptionButton: function (val) {
      //获得所选行的id
      this.rowId = val.row.wordsId
      //设置标签功能
      if (val.methods == 'setLabel') {
        this.title = '编辑敏感词'
        for (let i in val.row.labelParent) {
          this.sensitiveValue[val.row.labelParent[i]] = parseInt(i)
        }
        this.sensitiveAdded.formData.words = val.row.words
        this.sensitiveAdded.formData.remark = val.row.remark
        this.sensitiveAddedDialog = true
      } else if (val.methods == 'delLabel') {
        //删除功能
        this.$confirms.confirmation(
          'delete',
          '此操作将永久删除该条数据, 是否继续?',
          window.path.omcs + 'operatingblackwords/' + val.row.wordsId,
          {},
          (res) => {
            this.getTableDtate()
          }
        )
      }
    },
    //新增
    handelAlert_ok_add(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.sensitiveAdded.formData.labelId = []
          for (let i in this.sensitiveValue) {
            if (this.sensitiveValue[i] != '') {
              this.sensitiveAdded.formData.labelId.push(this.sensitiveValue[i])
            }
          }
          if (this.title == '添加敏感词') {
            this.$confirms.confirmation(
              'put',
              '是否新增敏感词?',
              window.path.omcs + 'operatingblackwords',
              this.sensitiveAdded.formData,
              (res) => {
                // window.api.put(window.path.omcs+'operatingblackwords',this.sensitiveAdded.formData,res=>{
                this.sensitiveAddedDialog = false
                this.getTableDtate()
              }
            )
          } else {
            // for(let i in this.sensitiveValue){
            //     if(this.sensitiveValue[i]!=''){
            //         this.sensitiveAdded.formData.labelId.push(this.sensitiveValue[i])
            //     }
            // }
            this.sensitiveAdded.formData.wordsId = this.rowId
            this.$confirms.confirmation(
              'post',
              '是否编辑敏感词?',
              window.path.omcs + 'operatingblackwords/redact',
              this.sensitiveAdded.formData,
              (res) => {
                // window.api.post(window.path.omcs+'operatingblackwords/redact',this.sensitiveAdded.formData,res=>{
                this.sensitiveAddedDialog = false
                delete this.sensitiveAdded.formData.wordsId
                this.getTableDtate()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addFunction() {
      this.sensitiveAddedDialog = true
      this.title = '添加敏感词'
    }
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    this.findTreeDatas()
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  watch: {
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听设置标签弹框是否关闭
    // setLabelDialog(val){
    //     if(this.$refs.setLabel){
    //         if(val == false){
    //            this.$refs.setLabel.resetFields();
    //         }
    //     }
    // },
    //监听导入弹框是否关闭
    // SigDialogVisible(val){
    //     if(val == false){
    //         this.fileList=[];
    //     }
    // },
    //监听新增弹框是否关闭
    sensitiveAddedDialog(val) {
      if (this.$refs.sensitiveAdded) {
        if (val == false) {
          this.sensitiveAdded.formData.words = ''
          this.sensitiveAdded.formData.labelId = []
          this.sensitiveAdded.formData.remark = ''
          this.sensitiveOptions.forEach((item) => {
            this.sensitiveValue[item.id] = '' // 父、  子、  子的赋值
          })
        }
      }
    },
  },
}
</script>

<style scoped>
.sensitiveClass {
  margin: 0px 5px 5px 0px;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
  margin-left: 20px;
}
.Templat-matter > p {
  padding: 5px 0;
}
</style>
