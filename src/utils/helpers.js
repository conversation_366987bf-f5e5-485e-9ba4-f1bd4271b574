/**
 * 常用工具函数集合
 */

/**
 * 格式化日期
 * @param {Date|number|string} date 日期对象或时间戳
 * @param {string} format 格式化模式
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  date = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date
  
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  
  const padZero = (num) => String(num).padStart(2, '0')
  
  return format
    .replace(/YYYY/g, year)
    .replace(/YY/g, String(year).slice(2))
    .replace(/MM/g, padZero(month))
    .replace(/M/g, month)
    .replace(/DD/g, padZero(day))
    .replace(/D/g, day)
    .replace(/HH/g, padZero(hour))
    .replace(/H/g, hour)
    .replace(/mm/g, padZero(minute))
    .replace(/m/g, minute)
    .replace(/ss/g, padZero(second))
    .replace(/s/g, second)
}

/**
 * 防抖函数
 * @param {Function} fn 需要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖处理后的函数
 */
export function debounce(fn, delay = 300) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param {Function} fn 需要节流的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 节流处理后的函数
 */
export function throttle(fn, wait = 300) {
  let timer = null
  let lastTime = 0
  return function(...args) {
    const now = Date.now()
    if (now - lastTime >= wait) {
      fn.apply(this, args)
      lastTime = now
    } else if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, args)
        lastTime = Date.now()
        timer = null
      }, wait - (now - lastTime))
    }
  }
}

/**
 * 深拷贝
 * @param {*} obj 需要深拷贝的对象
 * @returns {*} 深拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof RegExp) return new RegExp(obj)
  
  const clone = Array.isArray(obj) ? [] : {}
  
  Object.keys(obj).forEach(key => {
    clone[key] = deepClone(obj[key])
  })
  
  return clone
}

/**
 * 获取Cookie值
 * @param {string} name Cookie名称
 * @returns {string} Cookie值
 */
export function getCookie(name) {
  const cookies = document.cookie.split('; ')
  for (let i = 0; i < cookies.length; i++) {
    const parts = cookies[i].split('=')
    if (parts[0] === name) {
      return decodeURIComponent(parts[1] || '')
    }
  }
  return ''
}

/**
 * 设置Cookie
 * @param {string} name Cookie名称
 * @param {string} value Cookie值
 * @param {object} options Cookie选项
 */
export function setCookie(name, value, options = {}) {
  options = {
    path: '/',
    ...options
  }
  
  if (options.expires instanceof Date) {
    options.expires = options.expires.toUTCString()
  }
  
  let cookie = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`
  
  for (const optionKey in options) {
    cookie += `; ${optionKey}`
    const optionValue = options[optionKey]
    if (optionValue !== true) {
      cookie += `=${optionValue}`
    }
  }
  
  document.cookie = cookie
}

/**
 * 删除Cookie
 * @param {string} name Cookie名称
 */
export function deleteCookie(name) {
  setCookie(name, '', { 'max-age': -1 })
}

/**
 * 获取URL参数
 * @param {string} param 参数名
 * @param {string} url URL
 * @returns {string|null} 参数值
 */
export function getUrlParam(param, url = window.location.href) {
  const searchParams = new URL(url).searchParams
  return searchParams.get(param)
}

/**
 * 将对象转为URL查询字符串
 * @param {object} params 参数对象
 * @returns {string} URL查询字符串
 */
export function objectToQueryString(params) {
  return Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&')
}

/**
 * 检测是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 生成随机ID
 * @param {number} length ID长度
 * @returns {string} 随机ID
 */
export function generateRandomId(length = 8) {
  return Math.random().toString(36).substring(2, 2 + length)
}

/**
 * 格式化文件大小
 * @param {number} bytes 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${units[i]}`
} 
/**
 * 删除对象中的指定属性
 * @param {Object} obj 需要删除属性的对象
 * @param {string} prop 需要删除的属性名
 * @returns {Object} 删除指定属性后的对象
 */
export function deleteObjectProperty(obj, prop) {
  const newObj = { ...obj }
  delete newObj[prop]
  return newObj
}
