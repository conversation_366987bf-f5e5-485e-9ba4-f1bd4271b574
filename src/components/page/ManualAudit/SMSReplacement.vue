<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <el-tabs v-model="activeName2" type="card">
        <el-tab-pane label="待补发" name="first">
        </el-tab-pane>
        <el-tab-pane label="补发记录" name="second">
        </el-tab-pane>
        <el-tab-pane label="用户发送状况" name="situation">
        </el-tab-pane>
        <el-tab-pane label="失败代码统计" name="fail">
        </el-tab-pane>
        <el-tab-pane label="失败代码账号统计" name="failUser">
        </el-tab-pane>
      </el-tabs>

      <div v-show="activeName2 == 'first'" class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form label-width="82px" :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
            <el-form-item label="用户名" prop="clientName">
              <el-input v-model="formInline.clientName" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="消息ID" prop="msgid">
              <el-input v-model="formInline.msgid" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送内容" prop="content">
              <el-input v-model="formInline.content" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="formInline.mobile" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="通道ID" prop="channelId">
              <el-input v-model="formInline.channelId" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="smsStatus">
              <el-select v-model="formInline.smsStatus" clearable class="input-w">
                <el-option label="待返回" value="3"></el-option>
                <el-option label="发送失败" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="失败代码" prop="originalCode">
              <el-input v-model="formInline.originalCode" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" prop="time1">
              <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate" :cell-class-name="pickerOptions && pickerOptions.cellClassName
                  " v-model="formInline.time1" type="datetimerange" @change="timeD" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
              <!-- <el-date-picker class="input-w"
                                    v-model="formInline.time1"
                                    value-format="YYYY-MM-DD"
                                    type="daterange"
                                    range-separator="-"
                                    :picker-options="pickerOptions"
                                    :clearable="false"
                                    @change="getTimeOperating"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                    </el-date-picker> -->
            </el-form-item>
            <div style="margin-bottom: 18px">
              <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
              <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
            </div>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
                        
                        </div> -->

        <div class="Mail-table" style="padding-bottom: 40px">
          <div style="
              display: flex;
              justify-content: flex-end;
              margin: 10px 0;
            ">
            <!-- 表格和分页开始 -->
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[50, 100, 300]"
              layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
            </el-pagination>
          </div>

          <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem> -->
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handelSelection"
          > -->

          <vxe-toolbar ref="toolbarRef1" custom>
            <template #buttons>
              <el-button type="primary" @click="ConditionS('条件补发')">条件补发</el-button>
              <el-button v-if="valueReissueId.length > 0" type="primary" @click="Reissue('短信补发')">批量补发</el-button>
            </template>
          </vxe-toolbar>

          <vxe-table ref="tableRef1" id="SMSReplacementFirst" border stripe :custom-config="customConfig"
            :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
            v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;" :data="tableDataObj.tableData"
            @checkbox-all="handelSelection" @checkbox-change="handelSelection">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="用户名" title="用户名" width="120">
              <template v-slot="scope">
                <!-- <el-popover
                                placement="top-start"
                                width="700px"
                                trigger="hover"
                                @show='userList(scope.row,scope.$index)'>
                                <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.clientName"/>
                                <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                  {{ scope.row.clientName}}
                                </span>
                            </el-popover> -->
                <div style="color: #16a589; cursor: pointer" @click="rouTz(scope.row)">
                  {{ scope.row.clientName }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="手机号" title="手机号" width="110">
              <template #default="{ row, rowIndex }">
                <!-- <div
                  @click="phoneClick(rowIndex, row)"
                  style="color: rgb(22, 165, 137); cursor: pointer"
                >
                  <span>{{ row.mobile }}</span>
                </div> -->
                <div v-if="rowIndex == pIndex">
                  <span>{{ row.mobile }}</span>
                </div>
                <div v-else style="color: #16a589; cursor: pointer" @click="phoneClick(rowIndex, row)">
                  <span>{{ row.maskMobile }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="短信内容" title="短信内容" width="500">
              <template v-slot="scope">
                <div>
                  {{ scope.row.content }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="计费条数" title="计费条数" width="80">
              <template v-slot="scope">
                <div>
                  {{ scope.row.chargeNum }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="消息ID" title="消息ID" width="200">
              <template v-slot="scope">
                <div>
                  {{ scope.row.msgid }}
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                              class="el-icon-document-copy" @click="handleCopy(scope.row.msgid, $event)"></i> -->
                  <CopyTemp :content="scope.row.msgid" />
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送通道号" title="发送通道号" width="85">
              <template v-slot="scope">
                <div>
                  {{ scope.row.channelId }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="扩展号" title="扩展号" width="100">
              <template v-slot="scope">
                <div>
                  {{ scope.row.ext }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送时间" title="发送时间" width="170">
              <template v-slot="scope">
                <div>
                  {{ scope.row.sendTime }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="" title="状态" width="80">
              <template v-slot="scope">
                <div>
                  <span v-if="scope.row.smsStatus == 3">待返回</span>
                  <span style="color: #f56c6c" v-else>发送失败</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="状态代码" title="状态代码" width="90">
              <template v-slot="scope">
                <div>
                  {{ scope.row.originalCode }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="回执时间" title="回执时间" width="170">
              <template v-slot="scope">
                <div>
                  {{ scope.row.reportTime }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" fixed="right" width="120">
              <template v-slot="scope">
                <el-button style="color: #16a589" link @click="ReissueOperation(scope.row)"><el-icon>
                    <CircleCheck />
                  </el-icon>补发</el-button>
              </template>
            </vxe-column>
          </vxe-table>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[50, 100, 300]"
              layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
            </el-pagination>
          </div>

          <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>

      <div v-show="activeName2 == 'second'" class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form label-width="82px" :inline="true" ref="formInline1" :model="formInline1" class="demo-form-inline">
            <el-form-item label="用户名" prop="clientName">
              <el-input v-model="formInline1.clientName" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送内容" prop="content">
              <el-input v-model="formInline1.content" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <!-- <el-form-item label="通道ID" prop="channelId">
                                    <el-input v-model="formInline1.channelId" placeholder="" class="input-w"></el-input>
                                </el-form-item>
                                <el-form-item label="失败代码" prop="originalCode">
                                    <el-input v-model="formInline1.originalCode" placeholder="" class="input-w"></el-input>
                                </el-form-item> -->
            <el-form-item label="发送时间" prop="time1">
              <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate" :cell-class-name="pickerOptions && pickerOptions.cellClassName
                  " v-model="formInline1.time1" type="datetimerange" @change="timeB" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
              <!-- <el-date-picker class="input-w"
                                    v-model="formInline1.time1"
                                    value-format="YYYY-MM-DD"
                                    type="daterange"
                                    range-separator="-"
                                    :clearable="false"
                                    @change="getTimeOperating1"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                    </el-date-picker> -->
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click="ListSearch1">查询</el-button>
              <el-button type="primary" plain style="" @click="Reset1('formInline1')">重置</el-button>
            </div>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
                        
                        </div> -->
        <!-- <div class="sensitive-fun">
                            <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">补发记录列表</span>
                        </div> -->
        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- 表格和分页开始 -->
          <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"  style="background:#fff;padding:10px 0;text-align:right;">
                                <el-pagination class="page_bottom" @size-change="handleSizeChange1" @current-change="handleCurrentChange1" :current-page="formInline1s.currentPage" :page-size="formInline1s.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj1.tablecurrent.total">
                                </el-pagination>
                            </el-col> -->
          <!-- <table-tem :tableDataObj="tableDataObj1" @handelOptionButton="handelOptionButton1"></table-tem> -->
          <!-- <el-table
            :data="tableDataObj1.tableData"
            :border="true"
            :default-expand-all="false"
            style="width: 100%"
          > -->

          <vxe-toolbar ref="toolbarRef2" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table ref="tableRef2" id="SMSReplacementSecond" border stripe :custom-config="customConfig"
            :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1" style="font-size: 12px;"
            :data="tableDataObj1.tableData" v-loading="tableDataObj1.loading2" element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)">

            <vxe-column type="expand" align="center" width="60">
              <template #content="{ row }">
                <div style="padding: 0 20px">
                  <el-form label-position="left" inline class="demo-table-expand">
                    <el-form-item label="用户名:">
                      <span>{{ row.clientName }}</span>
                    </el-form-item>
                    <el-form-item label="消息ID:">
                      <span>{{ row.msgid }}</span>
                    </el-form-item>
                    <el-form-item label="发送内容:">
                      <span>{{ row.content }}</span>
                    </el-form-item>
                    <el-form-item label="手机号码:">
                      <span>{{ row.mobile }}</span>
                    </el-form-item>
                    <el-form-item label="通道ID:">
                      <span>{{ row.channelId }}</span>
                    </el-form-item>
                    <el-form-item label="失败代码:">
                      <span>{{ row.originalCode }}</span>
                    </el-form-item>
                    <el-form-item label="状态:">
                      <span v-if="row.smsStatus == 3">待返回</span>
                      <span v-else-if="row.smsStatus == 2">发送失败</span>
                      <!-- <span v-if="tableDataObj1.tableData.smsStatus==3">待返回</span>
                                          <span v-else-if="tableDataObj1.tableData.smsStatus==2">发送失败</span>
                                          <span v-else-if="tableDataObj1.tableData.smsStatus==2">补发完成</span>
                                          <span v-else>补发失败</span> -->
                    </el-form-item>
                    <el-form-item label="发送时间:">
                      <span>{{
                        row.sendBeginTime +
                        ' - ' +
                        row.sendEndTime
                        }}</span>
                    </el-form-item>
                  </el-form>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="用户名" title="用户名">
              <template v-slot="scope">
                <div>
                  {{ scope.row.clientName }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="补发条数" title="补发条数">
              <template v-slot="scope">
                <div>
                  {{ scope.row.number }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="应补发数" title="应补发数">
              <template v-slot="scope">
                <div>
                  {{ scope.row.reissueNumber }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="补发类型" title="补发类型">
              <template v-slot="scope">
                <div>
                  {{ scope.row.reissueType == 1 ? '指定通道补发' : scope.row.reissueType == 2 ? '原通道补发' : '指定通道组补发' }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="补发通道号" title="补发通道号">
              <template v-slot="scope">
                <div>
                  {{ scope.row.reissueChannelId }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="补发次数" title="补发次数">
              <template v-slot="scope">
                <div>
                  {{ scope.row.process }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="补发状态" title="补发状态" prop="">
              <template v-slot="scope">
                <div v-if="scope.row.status == 0">待补发</div>
                <div v-else-if="scope.row.status == 1">补发中</div>
                <div v-else-if="scope.row.status == 2">补发完成</div>
                <div v-else>补发失败</div>
              </template>
            </vxe-column>
            <vxe-column field="补发人" title="补发人">
              <template v-slot="scope">
                <div>
                  {{ scope.row.operatorName }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="补发时间" title="补发时间" width="170">
              <template v-slot="scope">
                <div>
                  {{ scope.row.createTime }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="120" prop="">
              <template v-slot="scope">
                <el-button link style="color: #409eff;" @click="DetailsS(scope.row)">&nbsp;详 情</el-button>
                <el-button link style="color: #409eff;" @click="Retry(scope.row)">&nbsp;重 试</el-button>
              </template>
            </vxe-column>
          </vxe-table>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination class="page_bottom" @size-change="handleSizeChange1" @current-change="handleCurrentChange1"
              :current-page="formInline1s.currentPage" :page-size="formInline1s.pageSize"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj1.tablecurrent.total">
            </el-pagination>
          </div>

          <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>

      <div v-show="activeName2 == 'situation'" class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form label-width="82px" :inline="true" ref="formInline2" :model="formInline2" class="demo-form-inline">
            <el-form-item label="用户名" prop="clientName">
              <el-input v-model="formInline2.clientName" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" prop="time1">
              <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate" :cell-class-name="pickerOptions && pickerOptions.cellClassName
                  " v-model="formInline2.time1" type="datetimerange" @change="timeY" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" align="right" :clearable="false">
              </el-date-picker>
              <!-- <el-date-picker class="input-w"
                                    v-model="formInline2.time1"
                                    value-format="YYYY-MM-DD"
                                    type="daterange"
                                    range-separator="-"
                                    :clearable="false"
                                    @change="getTimeOperating2"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                    </el-date-picker> -->
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click="ListSearch2">查询</el-button>
              <el-button type="primary" plain style="" @click="Reset2('formInline2')">重置</el-button>
            </div>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
                        
                        </div> -->
        <!-- <div class="sensitive-fun">
                            <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">用户发送状况列表</span>
                        </div> -->
        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- 表格和分页开始 -->
          <!-- <table-tem :tableDataObj="tableDataObj2"></table-tem> -->
          <!-- 表格和分页开始 -->
          <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"  style="background:#fff;padding:10px 0;text-align:right;">
                                <el-pagination class="page_bottom" @size-change="handleSizeChange2" @current-change="handleCurrentChange2" :current-page="formInline2s.currentPage" :page-size="formInline2s.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObsituation.total">
                                </el-pagination>
                            </el-col> -->
          <!-- <el-table
            v-loading="tableDataObsituation.loading2"
            border
            :data="tableDataObsituation.tableData"
            style="width: 100%"
          > -->

          <vxe-toolbar ref="toolbarRef3" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table ref="tableRef3" id="SMSReplacementSituation" border stripe :custom-config="customConfig"
            :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
            v-loading="tableDataObsituation.loading2" element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;"
            :data="tableDataObsituation.tableData">

            <vxe-column field="用户名" title="用户名">
              <template v-slot="scope">
                <div style="cursor: pointer; color: #16a589" @click="ConditionS(scope.row.userName)">{{
                  scope.row.userName }}</div>
                <!-- <div>{{ scope.row.userName }}</div> -->
              </template>
            </vxe-column>
            <vxe-column field="发送数" title="发送数">
              <template v-slot="scope">
                <div>
                  {{ scope.row.sendNum }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送成功数" title="发送成功数">
              <template v-slot="scope">
                <div>
                  {{ scope.row.sendSuccessNum }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="等待数" title="等待数">
              <template v-slot="scope">
                <div>
                  {{ scope.row.waitNum }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="失败数量" title="失败数量">
              <template v-slot="scope">
                <div>
                  {{ scope.row.failNum }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送时间" title="发送时间" width="170">
              <template v-slot="scope">
                <div>
                  {{ scope.row.sendTime }}
                </div>
              </template>
            </vxe-column>
          </vxe-table>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination class="page_bottom" @size-change="handleSizeChange2" @current-change="handleCurrentChange2"
              :current-page="formInline2s.currentPage" :page-size="formInline2s.pageSize"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObsituation.total">
            </el-pagination>
          </div>

          <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>

      <div v-show="activeName2 == 'fail'" class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form label-width="82px" :inline="true" ref="orifinalFormInline" :model="orifinalFormInline"
            :rules="orifinalRules" class="demo-form-inline">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="orifinalFormInline.username" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="错误码" prop="originalCode">
              <el-input v-model="orifinalFormInline.originalCode" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" prop="time1">
              <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate" :cell-class-name="pickerOptions && pickerOptions.cellClassName
                  " v-model="orifinalFormInline.time1" type="datetimerange" @change="timeFail" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
            <div style="margin-bottom: 18px">
              <el-button type="primary" plain style="" @click="failSearch('orifinalFormInline')">查询</el-button>
              <el-button type="primary" plain style="" @click="failReset('orifinalFormInline')">重置</el-button>
            </div>
          </el-form>
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <el-table v-loading="FailureData.loading2" border :data="FailureData.tableData" style="width: 100%">
            <el-table-column label="错误码">
              <template v-slot="scope">
                {{ scope.row.originalCode }}
              </template>
            </el-table-column>
            <el-table-column label="数量">
              <template v-slot="scope">
                {{ scope.row.num }}
              </template>
            </el-table-column>
            <el-table-column label="比例（%）">
              <template v-slot="scope">
                {{ scope.row.failRate }}
              </template>
            </el-table-column>
          </el-table>
          <!-- 表格和分页结束 -->
        </div>
      </div>

      <div v-show="activeName2 == 'failUser'" class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form label-width="82px" :inline="true" ref="failUserFormInline" :model="failUserFormInline"
            :rules="failRules" class="demo-form-inline">
            <el-form-item label="错误码" prop="originalCode">
              <el-input v-model="failUserFormInline.originalCode" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" prop="time1">
              <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
                :disabled-date="pickerOptions && pickerOptions.disabledDate" :cell-class-name="pickerOptions && pickerOptions.cellClassName
                  " v-model="failUserFormInline.time1" type="datetimerange" @change="timeFailUser" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" align="right">
              </el-date-picker>
            </el-form-item>
            <div style="margin-bottom: 18px">
              <el-button type="primary" plain style="" @click="failUserSearch('failUserFormInline')">查询</el-button>
              <el-button type="primary" plain style="" @click="failUserReset('failUserFormInline')">重置</el-button>
            </div>
          </el-form>
        </div>
        <div style="margin: 10px 0;">
          <el-button type="primary" style="" @click="batchReissueByCode"
            :disabled="selectedClientNames.length === 0">批量补发</el-button>
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <el-table v-loading="failUserData.loading2" border :data="failUserData.tableData" style="width: 100%"
            @selection-change="handleSelectionChangeCode">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column label="用户名">
              <template v-slot="scope">
                {{ scope.row.username }}
              </template>
            </el-table-column>
            <el-table-column label="数量">
              <template v-slot="scope">
                {{ scope.row.num }}
              </template>
            </el-table-column>
          </el-table>
          <!-- 表格和分页结束 -->
        </div>
      </div>

    </div>
    <!-- 补发 -->
    <el-dialog :title="roleDialog" v-model="addCharacterPopUps" width="30%" :close-on-click-modal="false"
      :before-close="handleClose">
      <div class="addC" style="padding-right: 50px">
        <el-form :inline="true" ref="formAdd" :rules="rules" :model="formAdd" class="demo-form-inline">
          <!-- <el-form-item label="选择通道" label-width="100px" prop="aisle">
                            <el-select v-model="formAdd.aisle" placeholder="请选择" style="width:280px">
                                <el-option label="人工通道组" value="1"></el-option>
                            </el-select>
                        </el-form-item> -->
          <!-- <el-form-item v-if='formAdd.aisle=="1"' label="通道组名称"  label-width="100px" prop="ChannelSelection">
                            <el-select v-model="formAdd.ChannelSelection"  style="width:280px" clearable filterable placeholder="请选择">
                                <el-option
                                v-for="item in options"
                                :key="item.channelGroupId"
                                :label="item.channelGroupName"
                                :value="item.channelGroupId">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
          <!-- 补发类型 -->
          <el-form-item label="补发类型" label-width="100px" prop="reissueType">
            <el-radio-group v-model="formAdd.reissueType">
              <el-radio value="1">指定通道补发</el-radio>
              <el-radio value="2">原通道补发</el-radio>
              <el-radio value="3">指定通道组补发</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="formAdd.reissueType === '3'">
            <el-form-item label="通道组名称" label-width="100px" prop="channelGroupId">
              <el-select v-model="formAdd.channelGroupId" @change="GroupIdChange" style="width: 280px" clearable
                filterable placeholder="请选择">
                <el-option v-for="(item, index) in options" :key="index + 'a'" :label="item.channelGroupName"
                  :value="item.channelGroupId">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="formAdd.reissueType === '1'">
            <el-form-item label="通道组名称" label-width="100px" prop="channelGroupId">
              <el-select v-model="formAdd.channelGroupId" @change="GroupIdChange" style="width: 280px" clearable
                filterable placeholder="请选择">
                <el-option v-for="(item, index) in options" :key="index + 'a'" :label="item.channelGroupName"
                  :value="item.channelGroupId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="移动通道号" label-width="100px" prop="ydChannelId">
              <el-select v-model="formAdd.ydChannelId" style="width: 280px" clearable filterable placeholder="请选择">
                <el-option v-for="(item, index) in GroupArry.listYD" :key="index + 'b'" :label="item.channelName"
                  :value="item.channelId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="联通通道号" label-width="100px" prop="ltChannelId">
              <el-select v-model="formAdd.ltChannelId" style="width: 280px" clearable filterable placeholder="请选择">
                <el-option v-for="(item, index) in GroupArry.listLT" :key="index + 'c'" :label="item.channelName"
                  :value="item.channelId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="电信通道号" label-width="100px" prop="dxChannelId">
              <el-select v-model="formAdd.dxChannelId" style="width: 280px" clearable filterable placeholder="请选择">
                <el-option v-for="(item, index) in GroupArry.listDX" :key="index + 'z'" :label="item.channelName"
                  :value="item.channelId">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="addCharacterPopUps = false" v-if="StatusT == true">取 消</el-button>
          <el-button type="primary" v-if="StatusT == true" @click="RoleADD()">确 定</el-button>
          <el-button type="primary" v-if="StatusT == false" :loading="true">操作进行中，请稍等</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 条件补发 -->
    <el-dialog title="条件补发" v-model="ConditionalReplacement" width="50%" :close-on-click-modal="false"
      :before-close="handleClose1">
      <!-- <div style="padding: 10px 0;font-weight: 600;">补发用户名:<span style="font-weight: 500;margin-left: 10px;">{{ReissueName}}</span></div> -->
      <!-- <el-table
                v-loading="tableDataObjCondition.loading2"
                border
                :data="tableDataObjCondition.tableData"
                style="width: 100%">
                    <el-table-column label="选择" width="50">
                        <template #default="scope" >
                            <el-radio v-model="radio" @change="radioChange(scope.row.failNum,scope.row.content,scope.row.md5Content)" :label="scope.row.failNum+scope.row.content">&nbsp;</el-radio>
                        </template>
                    </el-table-column> 
                    <el-table-column label="发送内容" >
                        <template #default="scope" >
                            {{ scope.row.content }}
                        </template>
                    </el-table-column>
                    <el-table-column label="失败数量" width="100">
                        <template #default="scope">
                            {{ scope.row.failNum}}
                        </template>
                    </el-table-column>
                </el-table> -->
      <div>
        <span style="padding: 10px 0; font-weight: 600">补发条件</span>
        <span>共:（</span><span style="color: red; font-size: 13px">{{ tablecurrentTotal }}</span>）条
      </div>
      <div>
        <div style="width: 49%; display: inline-block; float: left">
          <div>
            <el-input readonly v-model="formInline.clientName">
              <template v-slot:prepend>用户名:</template>
            </el-input>
          </div>
          <div>
            <el-input readonly v-model="formInline.msgid">
              <template v-slot:prepend>消息ID:</template>
            </el-input>
          </div>
          <div style="word-wrap: break-word">
            <el-input readonly v-model="formInline.content">
              <template v-slot:prepend>发送内容:</template>
            </el-input>
          </div>
          <div>
            <el-input readonly v-model="formInline.mobile">
              <template v-slot:prepend>手机号码:</template>
            </el-input>
          </div>
        </div>
        <div style="width: 49%; display: inline-block">
          <div>
            <el-input readonly v-model="formInline.channelId">
              <template v-slot:prepend>通道ID:</template>
            </el-input>
          </div>
          <div>
            <el-input v-if="formInline.smsStatus == 2" readonly model-value="发送失败">
              <template v-slot:prepend>状态:</template>
            </el-input>
            <el-input v-else readonly model-value="待返回">
              <template v-slot:prepend>状态:</template>
            </el-input>
          </div>
          <div style="word-wrap: break-word">
            <el-input readonly v-model="formInline.originalCode">
              <template v-slot:prepend>失败代码:</template>
            </el-input>
          </div>
          <div>
            <el-input readonly v-model="time1String">
              <template v-slot:prepend>发送时间:</template>
            </el-input>
          </div>
        </div>
      </div>
      <!-- <el-table
                    v-show="radio"
                    v-loading="tableDataObjsendRecord.loading2"
                    border
                    :data="tableDataObjsendRecord.tableData"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
                    <el-table-column label="选择" width="50" type="selection">
                    </el-table-column>
                    <el-table-column label="通道号" >
                        <template #default="scope" >
                            {{ scope.row.channelId }}
                        </template>
                    </el-table-column>
                    <el-table-column label="失败代码">
                        <template #default="scope" >
                            {{ scope.row.originalCode}}
                        </template>
                    </el-table-column>
                    <el-table-column label="次数" width="100">
                        <template #default="scope" >
                            {{ scope.row.total}}
                        </template>
                    </el-table-column>
                </el-table> -->
      <el-form :model="ConditionForm" :rules="ConditionFormRulef" ref="ConditionFormRulef" label-width="124px"
        :inline="true">
        <!-- <el-form-item label="" prop="isIndividualization">
                        <span>已选补发:</span><span style="color: red;margin: 0 5px;">{{ReissueNum}}</span>
                    </el-form-item> -->
        <!-- <div style="padding: 10px 0;font-weight: 600;"><span>补发次数</span></div>
                    <div>
                        <el-form-item label="" prop="process">
                            <el-select clearable v-model="ConditionForm.process" class="accountinfo-w" placeholder="请选择补发次数">
                                    <el-option label="0" value="0"></el-option>
                                    <el-option label="1" value="1"></el-option>
                                    <el-option label="2" value="2"></el-option>
                                    <el-option label="3" value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </div> -->
        <div style="padding: 10px 0; font-weight: 600">
          <span>补发通道</span>
        </div>
        <div>
          <!-- <el-form-item label="" prop="reissueChannelId">
                        <el-input v-model="ConditionForm.reissueChannelId" placeholder="请填写补发通道" class="input-w"></el-input>
                    </el-form-item> -->
          <div>

          </div>
          <!-- 补发类型 -->
          <el-form-item label="补发类型" label-width="100px" prop="reissueType">
            <el-radio-group v-model="ConditionForm.reissueType">
              <el-radio value="1">指定通道补发</el-radio>
              <el-radio value="2">原通道补发</el-radio>
              <el-radio value="3">指定通道组补发</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="ConditionForm.reissueType === '3'">
            <el-form-item label="通道组名称" label-width="100px" prop="channelGroupId">
              <el-select v-model="ConditionForm.channelGroupId" @change="GroupIdChange1" style="width: 280px" clearable
                filterable placeholder="请选择">
                <el-option v-for="(item, index) in options" :key="index + 'd'" :label="item.channelGroupName"
                  :value="item.channelGroupId">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="ConditionForm.reissueType === '1'">
            <el-form-item label="通道组名称" label-width="100px" prop="channelGroupId">
              <el-select v-model="ConditionForm.channelGroupId" @change="GroupIdChange1" style="width: 280px" clearable
                filterable placeholder="请选择">
                <el-option v-for="(item, index) in options" :key="index + 'd'" :label="item.channelGroupName"
                  :value="item.channelGroupId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="移动通道号" label-width="100px" prop="ydChannelId">
              <el-select v-model="ConditionForm.ydChannelId" style="width: 280px" clearable filterable
                placeholder="请选择">
                <el-option v-for="(item, index) in GroupArry.listYD" :key="index + 'e'" :label="item.channelName"
                  :value="item.channelId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="联通通道号" label-width="100px" prop="ltChannelId">
              <el-select v-model="ConditionForm.ltChannelId" style="width: 280px" clearable filterable
                placeholder="请选择">
                <el-option v-for="(item, index) in GroupArry.listLT" :key="index + 'f'" :label="item.channelName"
                  :value="item.channelId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="电信通道号" label-width="100px" prop="dxChannelId">
              <el-select v-model="ConditionForm.dxChannelId" style="width: 280px" clearable filterable
                placeholder="请选择">
                <el-option v-for="(item, index) in GroupArry.listDX" :key="index + 'g'" :label="item.channelName"
                  :value="item.channelId">
                </el-option>
              </el-select>
            </el-form-item>
          </div>

        </div>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="ConditionalReplacement = false">取 消</el-button>
          <el-button type="primary" @click="determine">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 条件补发 -->
    <el-dialog title="失败统计" v-model="ConditionalReplacementS" width="50%" :close-on-click-modal="false"
      :before-close="handleClose1S">
      <div style="padding: 10px 0; font-weight: 600">
        用户名:<span style="font-weight: 500; margin-left: 10px">{{
          ReissueName
          }}</span>
      </div>
      <el-table v-loading="tableDataObjCondition.loading2" border :data="tableDataObjCondition.tableData"
        style="width: 100%">
        <el-table-column label="选择" width="50">
          <template v-slot="scope">
            <el-radio v-model="radio" @change="
              radioChange(
                scope.row.failNum,
                scope.row.content,
                scope.row.md5Content
              )
              " :label="scope.row.failNum + scope.row.content">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="发送内容">
          <template v-slot="scope">
            {{ scope.row.content }}
          </template>
        </el-table-column>
        <el-table-column label="失败数量" width="100">
          <template v-slot="scope">
            {{ scope.row.failNum }}
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"  style="background:#fff;padding:10px 0;text-align:right;">
                    <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                    </el-pagination>
                </el-col> -->
      <div v-show="radio" style="padding: 10px 0; font-weight: 600">
        <span>补发条件</span>
      </div>
      <el-table v-show="radio" v-loading="tableDataObjsendRecord.loading2" border
        :data="tableDataObjsendRecord.tableData" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column label="通道号">
          <template v-slot="scope">
            {{ scope.row.channelId }}
          </template>
        </el-table-column>
        <el-table-column label="失败代码">
          <template v-slot="scope">
            {{ scope.row.originalCode }}
          </template>
        </el-table-column>
        <el-table-column label="次数" width="100">
          <template v-slot="scope">
            {{ scope.row.total }}
          </template>
        </el-table-column>
      </el-table>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="ConditionalReplacementS = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量补发错误码对话框 -->
    <el-dialog title="批量补发错误码" v-model="batchReissueByCodeDialog" width="25%" :close-on-click-modal="false"
      :before-close="handleCloseBatchReissueByCode">
      <div class="addC" style="padding-right: 50px">
        <el-form :inline="true" ref="formBatchReissueByCode" :rules="rules" :model="formBatchReissueByCode"
          class="demo-form-inline">
          <el-form-item label="通道组名称" label-width="100px" prop="channelGroupId">
            <el-select v-model="formBatchReissueByCode.channelGroupId" @change="GroupIdChangeBatchReissue"
              style="width: 280px" clearable filterable placeholder="请选择">
              <el-option v-for="(item, index) in options" :key="index + 'a'" :label="item.channelGroupName"
                :value="item.channelGroupId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="移动通道号" label-width="100px" prop="ydChannelId">
            <el-select v-model="formBatchReissueByCode.ydChannelId" style="width: 280px" clearable filterable
              placeholder="请选择">
              <el-option v-for="(item, index) in GroupArry.listYD" :key="index + 'b'" :label="item.channelName"
                :value="item.channelId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联通通道号" label-width="100px" prop="ltChannelId">
            <el-select v-model="formBatchReissueByCode.ltChannelId" style="width: 280px" clearable filterable
              placeholder="请选择">
              <el-option v-for="(item, index) in GroupArry.listLT" :key="index + 'c'" :label="item.channelName"
                :value="item.channelId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="电信通道号" label-width="100px" prop="dxChannelId">
            <el-select v-model="formBatchReissueByCode.dxChannelId" style="width: 280px" clearable filterable
              placeholder="请选择">
              <el-option v-for="(item, index) in GroupArry.listDX" :key="index + 'z'" :label="item.channelName"
                :value="item.channelId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="batchReissueByCodeDialog = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchReissueByCode">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
// import clip from '../../../utils/clipboard'
import moment from 'moment'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    UserLists,
    CopyTemp
  },
  name: 'SmSReplacement',
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      activeName2: 'first',
      userFlag: false,
      pIndex: -1,
      nameover: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        // onPick: ({ maxDate, minDate }) => {
        //     this.pickerMinDate = minDate.getTime();
        //     if (maxDate) {
        //         this.pickerMinDate = ''
        //     }
        // },
        // disabledDate: (time) => {
        //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
        //         return false
        //     }
        //     if (this.pickerMinDate !=='') {
        //         const day3 = 3 * 24 * 3600 * 1000
        //         let maxTime = this.pickerMinDate + day3
        //         if (maxTime >  Date.now()) {
        //             maxTime =  Date.now()
        //         }
        //         const minTime = this.pickerMinDate - day3
        //         return time.getTime() < minTime || time.getTime() > maxTime
        //     }
        //     return time.getTime() > Date.now();
        // }
        // disabledDate(time) {
        //     return time.getTime() < Date.now() - 4 * 24 * 3600 * 1000 || time.getTime() > Date.now();
        // }
      },
      rules: {
        channelGroupId: [
          { required: true, message: '通道组不能为空', trigger: 'change' },
        ],
        ydChannelId: [
          { required: true, message: '移动通道不能为空', trigger: 'change' },
        ],
        ltChannelId: [
          { required: true, message: '联通通道不能为空', trigger: 'change' },
        ],
        dxChannelId: [
          { required: true, message: '电信通道不能为空', trigger: 'change' },
        ],
        reissueType: [
          { required: true, message: '补发类型不能为空', trigger: 'change' },
        ],
      },
      formAdd: {
        reissueType: '1',
        smsInfoIds: '',
        channelGroupId: '',
        // 选择通道类型
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
      },
      roleDialog: '',
      // 查询列表数据
      formInline: {
        clientName: '',
        msgid: '',
        content: '',
        mobile: '',
        channelId: '',
        originalCode: '',
        smsStatus: '2',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now() - 300 * 1000).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now() - 300 * 1000,
        ],
        pageSize: 50,
        currentPage: 1,
      },
      //储存查询列表数据
      formInlines: {
        clientName: '',
        msgid: '',
        content: '',
        mobile: '',
        channelId: '',
        originalCode: '',
        smsStatus: '2',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now() - 300 * 1000).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now() - 300 * 1000,
        ],
        pageSize: 50,
        currentPage: 1,
      },
      // 查询列表数据
      formInline1: {
        clientName: '',
        content: '',
        channelId: '',
        originalCode: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      //储存查询列表数据
      formInline1s: {
        clientName: '',
        content: '',
        channelId: '',
        originalCode: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      // 查询列表数据
      formInline2: {
        clientName: '',
        content: '',
        channelId: '',
        originalCode: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      //储存查询列表数据
      formInline2s: {
        clientName: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      orifinalFormInline: {
        username: '',
        originalCode: '',
        beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ],
      },
      failUserFormInline: {
        originalCode: '',
        beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        time1: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ],
      },
      orifinalRules: {
        username: [
          { required: true, message: '用户名不能为空', trigger: 'change' },
        ],
        // originalCode: [
        //     { required: true, message: '失败代码不能为空', trigger: 'change' },
        // ],
        time1: [
          {
            type: 'array',
            required: true,
            message: '时间不能为空',
            trigger: 'change',
          },
        ],
      },
      failRules: {
        originalCode: [
          { required: true, message: '失败代码不能为空', trigger: 'change' },
        ],
      },
      time1String: '',
      tablecurrentTotal: '',
      status: '展开',
      // 存储二次弹出框状态
      StatusT: true,
      // 存储通道
      options: [],
      //储存查询通道
      GroupArry: [],
      // 存储iD
      valueReissueId: '',
      addCharacterPopUps: false,
      ConditionalReplacement: false,
      ConditionalReplacementS: false,
      // 条件补发多选存储
      multipleSelection: [],
      //待补发列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        // tableLabelExpand:[{ prop:"content",showName:'短信内容'}],
      },
      //待补发列表数据
      tableDataObj1: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        // tableLabelExpand:[{ prop:"content",showName:'短信内容'}],
        // tableLabel:[{
        //     prop:"clientName",
        //     showName:'用户名',
        //     width:"90",
        //     fixed:false
        //     },{
        //     prop:"content",
        //     showName:'短信内容',
        //     width:"650",
        //     fixed:false
        //     },{
        //     prop:"number",
        //     showName:'补发条数',
        //     width:"70",
        //     fixed:false
        //     },{
        //     prop:"originalCode",
        //     showName:'失败代码',
        //     width:"95",
        //     fixed:false
        //     },{
        //     prop:"channelId",
        //     showName:'发送通道号',
        //     width:"85",
        //     fixed:false,
        //     },{
        //     prop:"reissueChannelId",
        //     showName:'补发通道号',
        //     width:"150",
        //     fixed:false
        //     },{
        //     prop:"process",
        //     showName:'补发次数',
        //     width:"80",
        //     fixed:false
        //     },{
        //     prop:"status",
        //     showName:'补发状态',
        //     fixed:false,
        //     width:"80",
        //     formatData:function(val){
        //         if(val==0){
        //             return "待补发"
        //         }else if(val==1){
        //             return "补发中"
        //         }else if(val==2){
        //             return "补发完成"
        //         }else{
        //             return "补发失败"
        //         }
        //     }
        //     },{
        //     prop:"operatorName",
        //     showName:'补发人',
        //     width:"90",
        //     fixed:false
        //     },{
        //     prop:"createTime",
        //     showName:'补发时间',
        //     width:"150",
        //     fixed:false
        //     }
        // ],
        // tableStyle:{
        //     isSelection:false,//是否复选框
        //     isExpand:false,//是否是折叠的
        //     isDefaultExpand:false,
        //     style: {//表格样式,表格宽度
        //         width:"100%"
        //         },
        //     optionWidth:'80',//操作栏宽度
        //     border:true,//是否边框
        //     stripe:false,//是否有条纹
        // },
        // conditionOption:[
        //     {
        //         contactCondition:'',//关联的表格属性
        //         contactData:'',//关联的表格属性-值
        //         optionName:'详情',//按钮的显示文字
        //         optionMethod:'DetailsS',//按钮的方法
        //         icon:'',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //         otherOptionName:'详情',//其他条件的按钮显示文字
        //         otherOptionMethod:'DetailsS',//其他条件的按钮方法
        //         otherIcon:'',//其他条件按钮的图标
        //         optionOtherButtonColor:''//其他条件按钮的颜色
        //     },
        // ]
      },

      FailureData: {
        loading2: false,
        tableData: [],
      },
      failUserData: {
        loading2: false,
        tableData: [],
      },
      //条件补发列表数据
      // ConditionGroup:{
      //     clientName: "testzd2,
      //     currentPage: 1,
      //     pageSize: 10,
      //     sendBeginTime: "2019-01-01 00:00:00",
      //     sendEndTime: "2019-09-06 23:59:59",
      //     smsStatus: 2
      // },
      tableDataObjCondition: {
        loading2: false,
        tableData: [],
      },
      tableDataObsituation: {
        //列表数据
        loading2: false, //loading动画
        total: 0, //分页参数
        tableData: [],
      },
      tableDataObjsendRecord: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      totalSize: 0, //总条数
      totalSize1: 0, //总条数
      ConditionForm: {
        reissueType: '1',
        channelGroupId: '',
        // 选择通道类型
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
        // originalCode:'',
        // process:'0',
        // reissueChannelId:'',
      },
      ConditionFormRulef: {
        reissueType: [
          { required: true, message: '补发类型不能为空', trigger: 'change' },
        ],
        channelGroupId: [
          { required: true, message: '通道组不能为空', trigger: 'change' },
        ],
        ydChannelId: [
          { required: true, message: '移动通道不能为空', trigger: 'change' },
        ],
        ltChannelId: [
          { required: true, message: '联通通道不能为空', trigger: 'change' },
        ],
        dxChannelId: [
          { required: true, message: '电信通道不能为空', trigger: 'change' },
        ],
        channelId: [
          { required: true, message: '请选择通道组', trigger: 'change' },
        ],
        channelGroupId: [
          { required: true, message: '请选择通道号', trigger: 'change' },
        ],
        originalCode: [
          { required: true, message: '请选择失败代码', trigger: 'change' },
        ],
        process: [
          { required: true, message: '请选择补发次数', trigger: 'change' },
        ],
        reissueChannelId: [
          { required: true, message: '请填写补发通道', trigger: 'change' },
        ],
      },
      radio: '',
      radio1: '',
      radioNumber: '',
      radioContent: '', //发送内容
      radioMd5Content: '',
      CarrierChannel: {},
      FailureCode: '', //失败代码
      aisleCode: '', //通道号
      ReissueNum: '', //补发条数
      ReissueName: '',
      selectedClientNames: [], // 存储选中的用户名
      batchReissueByCodeDialog: false, // 批量补发错误码对话框
      formBatchReissueByCode: {
        channelGroupId: '',
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.pIndex = -1;
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'v3/smsreplacement/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    InquireList1() {
      this.tableDataObj1.loading2 = true
      window.api.post(
        window.path.omcs + 'servicereissuerecord/page',
        this.formInline1s,
        (res) => {
          this.tableDataObj1.loading2 = false
          this.tableDataObj1.tableData = res.records
          this.tableDataObj1.tablecurrent.total = res.total
        }
      )
    },
    InquireList2() {
      this.tableDataObj1.loading2 = true
      window.api.post(
        window.path.omcs + 'v3/smsreplacement/sendResultByUserId',
        this.formInline2s,
        (res) => {
          this.tableDataObsituation.loading2 = false
          if (res.data?.records) {
            this.tableDataObsituation.tableData = res.data.records
            this.tableDataObsituation.total = res.data.total
          }
        }
      )
    },
    // 条件补发多选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 补发条件
    radioChange(val, val2, val3) {
      if (this.activeName2 == 'first') {
        this.ConditionForm.originalCode = ''
        this.ConditionForm.channelId = ''
        this.radioNumber = val
        this.radioContent = val2
        this.radioMd5Content = val3
        let Obj = {}
        Object.assign(Obj, this.formInline)
        Obj.content = val2
        Obj.md5Content = val3
        window.api.post(
          window.path.omcs +
          'v3/smsreplacement/conditionsreplacement/originalCodes',
          Obj,
          (res) => {
            this.tableDataObjsendRecord.tableData = res.data
          }
        )
      } else {
        this.radioNumber = val
        this.radioContent = val2
        this.radioMd5Content = val3
        window.api.post(
          window.path.omcs +
          'v3/smsreplacement/conditionsreplacement/originalCodes',
          {
            clientName: this.ReissueName,
            sendBeginTime: this.formInline2.sendBeginTime,
            sendEndTime: this.formInline2.sendEndTime,
            content: val2,
            md5Content: val3,
          },
          (res) => {
            this.tableDataObjsendRecord.tableData = res.data
          }
        )
      }
    },
    codeChange(val, val2) {
      this.ConditionForm.channelId = val
      this.ConditionForm.originalCode = val2
    },
    // 选择通道组改变
    GroupIdChange() {
      this.formAdd.dxChannelId = ''
      this.formAdd.ltChannelId = ''
      this.formAdd.ydChannelId = ''
      window.api.get(
        window.path.omcs +
        'v3/smsreplacement/allsmschannelgroup/' +
        this.formAdd.channelGroupId,
        {},
        (res) => {
          this.GroupArry = res.data
        }
      )
    },
    // handleCopy(name, event) {
    //     clip(name, event)
    // },
    GroupIdChange1() {
      this.ConditionForm.dxChannelId = ''
      this.ConditionForm.ltChannelId = ''
      this.ConditionForm.ydChannelId = ''
      window.api.get(
        window.path.omcs +
        'v3/smsreplacement/allsmschannelgroup/' +
        this.ConditionForm.channelGroupId,
        {},
        (res) => {
          this.GroupArry = res.data
        }
      )
    },
    // renderHeader(h) {
    //   //表头事件
    //   return (
    //     <div style="margin:0;padding:0;line-height: 23px;width:inherit;height: 23px;">
    //       <el-button
    //         nativeOnClick={this.expandOpen}
    //         style="plain:false;background:none;border:none;margin:0;padding:0;list-style:none;"
    //       >
    //         {this.status}
    //       </el-button>
    //     </div>
    //   )
    // },
    expandOpen() {
      var arr = document.querySelectorAll('.el-table__expand-icon')
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].className.length < 25) {
          arr[i].onclick = function () { }
        } else {
          arr[i].onclick = function () { }
        }
      }
      if (this.status == '展开') {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length < 25) {
            arr[i].click()
          }
        }
        this.status = '折叠'
      } else {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length > 25) {
            arr[i].click()
          }
        }
        this.status = '展开'
      }
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    // 确认条件补发
    determine() {
      // if(this.radio){
      // if(this.multipleSelection.length!=0){
      this.$refs['ConditionFormRulef'].validate((valid) => {
        if (valid) {
          let arrySelection = []
          for (let i = 0; i < this.multipleSelection.length; i++) {
            arrySelection.push(
              this.multipleSelection[i].originalCode +
              '&' +
              this.multipleSelection[i].channelId
            )
          }
          let codeChannels = arrySelection.join(',')
          if (this.activeName2 == 'first') {
            let Obj = {}
            Obj = Object.assign(Obj, this.formInline, this.ConditionForm)
            // Obj.originalCode=this.ConditionForm.originalCode
            // Obj.content=this.radioContent
            // Obj.clientName=this.ReissueName
            // Obj.md5Content=this.radioMd5Content
            // Obj.codeChannels=codeChannels
            window.api.post(
              window.path.omcs + 'v3/smsreplacement/batchSend',
              Obj,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '操作成功!',
                  })
                  this.ConditionalReplacement = false
                } else {
                  this.$message({
                    type: 'error',
                    duration: 2000,
                    message: res.msg,
                  })
                }
              }
            )
          } else {
            let Obj = {}
            Object.assign(Obj, this.ConditionForm)
            Obj.clientName = this.ReissueName
            Obj.content = this.radioContent
            Obj.md5Content = this.radioMd5Content;
            Obj.sendBeginTime = this.formInline2.sendBeginTime
            Obj.sendEndTime = this.formInline2.sendEndTime
            Obj.smsStatus = '2'
            Obj.codeChannels = codeChannels
            window.api.post(
              window.path.omcs + 'v3/smsreplacement/groupReplacement',
              Obj,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '操作成功!',
                  })
                  this.ConditionalReplacement = false
                } else {
                  this.$message({
                    type: 'error',
                    duration: 2000,
                    message: res.msg,
                  })
                }
              }
            )
          }
        }
      })
      // }else{
      //     this.$message({
      //         type: 'error',
      //         duration:2000,
      //         message:'请先选择补发条件!'
      //     });
      // }
      // }else{
      //     this.$message({
      //         type: 'error',
      //         duration:2000,
      //         message:'请先选择发送内容!'
      //     });
      // }
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    ListSearch1() {
      Object.assign(this.formInline1s, this.formInline1)
      this.InquireList1()
    },
    ListSearch2() {
      Object.assign(this.formInline2s, this.formInline2)
      this.InquireList2()
    },
    failSearch(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.getFailList()
        }
      })
    },
    failReset(formName) {
      this.$refs[formName].resetFields()
      this.orifinalFormInline.beginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.orifinalFormInline.endTime = moment()
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.FailureData.tableData = []
      // this.getFailList()
    },
    getFailList() {
      this.FailureData.loading2 = true
      window.api.post(
        window.path.omcs + '/operatingStatisticalAnalysis/searchData',
        this.orifinalFormInline,
        (res) => {
          if (res.code == 200) {
            this.FailureData.tableData = res.data
            this.FailureData.loading2 = false
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
              duration: 2000,
            })
          }
        }
      )
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.sendBeginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline.sendEndTime = moment(Date.now() - 300 * 1000).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    Reset1(formName) {
      this.$refs[formName].resetFields()
      this.formInline1.sendBeginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline1.sendEndTime = moment(Date.now() - 300 * 1000).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      Object.assign(this.formInline1s, this.formInline1)
      this.InquireList1()
    },
    Reset2(formName) {
      this.$refs[formName].resetFields()
      this.formInline2.sendBeginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline2.sendEndTime = moment(Date.now() - 300 * 1000).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      Object.assign(this.formInline2s, this.formInline2)
      this.InquireList2()
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].smsInfoId)
      }
      this.valueReissueId = selectId.join(',')
    },
    // 补发
    Reissue(val) {
      if (val == '条件补发' && !this.formInline.clientName) {
        this.$message({
          type: 'error',
          duration: 2000,
          message: '条件补发需先填写用户名',
        })
      } else {
        this.roleDialog = val
        this.addCharacterPopUps = true
      }
    },
    // 条件补发
    ConditionS(val) {
      if (val == '条件补发') {
        if (this.formInline.clientName) {
          window.api.post(
            window.path.omcs + 'v3/smsreplacement/page',
            this.formInline,
            (res) => {
              this.tablecurrentTotal = res.data.total
              this.ReissueName = this.formInline.clientName
              this.ConditionalReplacement = true
            }
          )
        } else {
          this.$message({
            type: 'error',
            duration: 2000,
            message: '条件补发需先填写用户名',
          })
        }
      } else {
        this.ReissueName = val
        window.api.post(
          window.path.omcs + 'v3/smsreplacement/conditionsreplacement/group',
          {
            clientName: val,
            sendBeginTime: this.formInline2.sendBeginTime,
            sendEndTime: this.formInline2.sendEndTime,
          },
          (res) => {
            this.tableDataObjCondition.loading2 = false
            this.tableDataObjCondition.tableData = res.data.records
            this.tableDataObjCondition.tablecurrent.total = res.data.total
          }
        )
        this.ConditionalReplacementS = true
      }
    },
    // 条件去除
    Reissues(val) {
      if (val == '条件去除' && !this.formInline.clientName) {
        this.$message({
          type: 'error',
          duration: 2000,
          message: '条件去除需先填写用户名',
        })
      } else if (val == '批量去除') {
        this.$confirms.confirmation(
          'post',
          '确定以当前条件去除?',
          window.path.omcs + 'v3/smsreplacement/batchreplacement',
          {
            smsInfoIds: this.valueReissueId,
            isRealSend: '2',
          },
          (res) => {
            this.InquireList()
          }
        )
      } else {
        this.formInline.isRealSend = '2'
        // this.formInline.pageSize=null
        this.$confirms.confirmation(
          'post',
          '确定以当前条件去除?',
          window.path.omcs + 'v3/smsreplacement/conditionsreplacement',
          this.formInline,
          (res) => {
            this.InquireList()
          }
        )
      }
    },
    // 关闭弹出层
    handleClose(done) {
      this.addCharacterPopUps = false
      // 清除formAdd表单数据
      this.formAdd = {
        reissueType: '1',
        smsInfoIds: '',
        channelGroupId: '',
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
      }
      // 清除GroupArry数据
      this.GroupArry = []
      // 清除选中的ID
      this.valueReissueId = ''
    },
    handleClose1(done) {
      this.ConditionalReplacement = false
      // 清除ConditionForm表单数据
      this.ConditionForm = {
        reissueType: '1',
        channelGroupId: '',
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
      }
      // 清除GroupArry数据
      this.GroupArry = []
      // 清除相关数据
      this.multipleSelection = []
      this.radio = ''
      this.radioNumber = ''
      this.radioContent = ''
      this.radioMd5Content = ''
    },
    handleClose1S(done) {
      this.ConditionalReplacementS = false
      // 清除相关数据
      this.radio = ''
      this.radioNumber = ''
      this.radioContent = ''
      this.radioMd5Content = ''
    },
    // 提交
    RoleADD() {
      this.$refs['formAdd'].validate((valid) => {
        if (valid) {
          if (this.roleDialog == '短信补发') {
            this.$confirm('确定短信补发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.StatusT = false
                this.formAdd.smsInfoIds = this.valueReissueId
                window.api.post(
                  window.path.omcs + 'v3/smsreplacement/batchreplacement',
                  this.formAdd,
                  (res) => {
                    this.StatusT = true
                    this.addCharacterPopUps = false
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除',
                })
              })
            // this.$confirms.confirmation('post','确定短信补发?',window.path.sirs+'smsreplacement/batchreplacement?ids='+this.valueReissueId+"&channelGroupId="+this.formAdd.ChannelSelection,{},res =>{
            //     this.addCharacterPopUps=false
            //     this.InquireList();
            // })
          } else if (this.roleDialog == '条件补发') {
            let objform = {}
            Object.assign(objform, this.formInline)
            // objform.pageSize = null
            objform.isRealSend = '1'
            this.$confirm('确定以当前条件补发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.StatusT = false
                window.api.post(
                  window.path.omcs + 'v3/smsreplacement/conditionsreplacement',
                  objform,
                  (res) => {
                    this.StatusT = true
                    this.addCharacterPopUps = false
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除',
                })
              })
            // this.$confirms.confirmation('post','确定以当前条件补发?',window.path.sirs+'smsreplacement/conditionsreplacement',objform
            // ,res =>{
            //     this.addCharacterPopUps=false
            //     // this.InquireList();
            // })
          }
        } else {
          return false
        }
      })
    },
    // 用户操作
    // handelOptionButton: function(val){
    //     if(val.methods=='Reissue'){
    //         this.roleDialog="短信补发"
    //         this.valueReissueId=val.row.smsInfoId
    //         this.addCharacterPopUps=true
    //     }
    // },
    ReissueOperation(val) {
      this.roleDialog = '短信补发'
      this.valueReissueId = val.smsInfoId
      this.addCharacterPopUps = true
    },
    phoneClick(index, row) {
      this.pIndex = index;
      // console.log(row);
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          // console.log(res);
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      )
    },
    // 详情
    handelOptionButton1: function (val) {
      if (val.methods == 'DetailsS') {
        this.$router.push('/ReissueDetails?id=' + val.row.id)
      }
    },
    DetailsS(val) {
      this.$router.push('/ReissueDetails?id=' + val.id)
    },
    Retry(val) {
      this.$confirms.confirmation(
        'post',
        '确定执行重试操作？',
        window.path.omcs + 'servicereissuerecord/reissue/' + val.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   type: 'success',
            //   message: '操作成功!',
            // })
          }
        }
      )
    },
    // 发送时间
    timeD(val) {
      // console.log(this.formInline.time1,'lllllllllll');
      // console.log(val,'val');
      if (this.formInline.time1) {
        this.formInline.sendBeginTime = moment(this.formInline.time1[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.formInline.sendEndTime = moment(this.formInline.time1[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline.sendBeginTime = ''
        this.formInline.sendEndTime = ''
      }
    },
    // 发送时间
    timeB() {
      // console.log(11111);
      // console.log(this.formInline1.time1,'time');
      if (this.formInline1.time1) {
        this.formInline1.sendBeginTime = moment(
          this.formInline1.time1[0]
        ).format('YYYY-MM-DD HH:mm:ss')
        this.formInline1.sendEndTime = moment(this.formInline1.time1[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline1.sendBeginTime = ''
        this.formInline1.sendEndTime = ''
      }
    },
    // 发送时间
    timeY() {
      // console.log(this.formInline.time1,'val');
      if (this.formInline2.time1) {
        this.formInline2.sendBeginTime = moment(
          this.formInline2.time1[0]
        ).format('YYYY-MM-DD HH:mm:ss')
        this.formInline2.sendEndTime = moment(this.formInline2.time1[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline2.sendBeginTime = ''
        this.formInline2.sendEndTime = ''
      }
      // if(val){
      //     this.formInline2.sendBeginTime=val[0]+" 00:00:00"
      //     this.formInline2.sendEndTime=val[1]+" 23:59:59"
      // }else{
      //     this.formInline2.sendBeginTime=moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
      //     this.formInline2.sendEndTime=Date.now() - 300 * 1000
      // }
    },
    timeFail(val) {
      if (val) {
        this.orifinalFormInline.beginTime = moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.orifinalFormInline.endTime = moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.orifinalFormInline.beginTime = ''
        this.orifinalFormInline.endTime = ''
      }
    },
    timeFailUser(val) {
      if (val) {
        this.failUserFormInline.beginTime = moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.failUserFormInline.endTime = moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.failUserFormInline.beginTime = ''
        this.failUserFormInline.endTime = ''
      }
    },
    getFailUserList() {
      this.failUserData.loading2 = true
      window.api.post(
        window.path.omcs + '/operatingStatisticalAnalysis/searchUserData',
        this.failUserFormInline,
        (res) => {
          if (res.code == 200) {
            this.failUserData.tableData = res.data
            this.failUserData.loading2 = false
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
              duration: 2000,
            })
          }
        }
      )
    },
    failUserSearch(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.getFailUserList()
        }
      })
    },
    failUserReset(formName) {
      this.$refs[formName].resetFields()
      this.failUserFormInline.beginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.failUserFormInline.endTime = moment()
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.failUserData.tableData = []
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleSizeChange1(size) {
      this.formInline1s.pageSize = size
      this.InquireList1()
    },
    handleSizeChange2(size) {
      this.formInline2s.pageSize = size
      this.InquireList2()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
    handleCurrentChange1: function (currentPage) {
      this.formInline1s.currentPage = currentPage
      this.InquireList1()
    },
    handleCurrentChange2: function (currentPage) {
      this.formInline2s.currentPage = currentPage
      this.InquireList2()
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 处理失败代码账号统计多选
    handleSelectionChangeCode(val) {
      this.selectedClientNames = val.map(item => item.username);
    },

    // 打开批量补发错误码对话框
    batchReissueByCode() {
      if (this.selectedClientNames.length === 0) {
        this.$message({
          type: 'warning',
          message: '请先选择需要补发的用户!'
        });
        return;
      }

      this.batchReissueByCodeDialog = true;
    },

    // 关闭批量补发错误码对话框
    handleCloseBatchReissueByCode() {
      this.batchReissueByCodeDialog = false;
      // 清除formBatchReissueByCode表单数据
      this.formBatchReissueByCode = {
        channelGroupId: '',
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
      }
      // 清除GroupArry数据
      this.GroupArry = []
    },

    // 批量补发错误码通道组变更
    GroupIdChangeBatchReissue() {
      this.formBatchReissueByCode.dxChannelId = '';
      this.formBatchReissueByCode.ltChannelId = '';
      this.formBatchReissueByCode.ydChannelId = '';

      window.api.get(
        window.path.omcs + 'v3/smsreplacement/allsmschannelgroup/' +
        this.formBatchReissueByCode.channelGroupId,
        {},
        (res) => {
          this.GroupArry = res.data;
        }
      );
    },

    // 提交批量补发错误码
    submitBatchReissueByCode() {
      this.$refs['formBatchReissueByCode'].validate((valid) => {
        if (valid) {
          const params = {
            clientNames: this.selectedClientNames.join(','),
            originalCode: this.failUserFormInline.originalCode,
            sendBeginTime: this.failUserFormInline.beginTime,
            sendEndTime: this.failUserFormInline.endTime,
            channelGroupId: this.formBatchReissueByCode.channelGroupId,
            ydChannelId: this.formBatchReissueByCode.ydChannelId,
            ltChannelId: this.formBatchReissueByCode.ltChannelId,
            dxChannelId: this.formBatchReissueByCode.dxChannelId,
          };

          this.$confirm('确定批量补发所选用户的错误短信?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
          }).then(() => {
            window.api.post(
              window.path.omcs + 'v3/smsreplacement/batchSendByCode',
              params,
              (res) => {
                this.batchReissueByCodeDialog = false;
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '操作成功!'
                  });
                  this.getFailUserList();
                } else {
                  this.$message({
                    type: 'error',
                    message: res.msg
                  });
                }
              }
            );
          }).catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作'
            });
          });
        }
      });
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      ; (this.formInline.time1 = [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment(Date.now() - 300 * 1000).format('YYYY-MM-DD HH:mm:ss'),
      ]),
        (this.formInline.sendBeginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'))
      this.formInline.sendEndTime = moment(Date.now() - 300 * 1000).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
      //   this.GroupIdChange()
    })
    // this.InquireList();
    // // this.InquireList1();
    // // this.InquireList2();
    window.api.get(
      window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
      {},
      (res) => {
        this.options = res.data
      }
    )
    // this.time1String=this.formInline.sendBeginTime+"-"+this.formInline.sendEndTime
  },
  mounted() {
    const $table1 = this.$refs.tableRef1
    const $table2 = this.$refs.tableRef2
    const $table3 = this.$refs.tableRef3
    const $toolbar1 = this.$refs.toolbarRef1
    const $toolbar2 = this.$refs.toolbarRef2
    const $toolbar3 = this.$refs.toolbarRef3
    if ($table1 && $toolbar1) {
      $table1.connect($toolbar1)
    }
    if ($table2 && $toolbar2) {
      $table2.connect($toolbar2)
    }
    if ($table3 && $toolbar3) {
      $table3.connect($toolbar3)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        ; (this.formInline.time1 = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(Date.now() - 300 * 1000).format('YYYY-MM-DD HH:mm:ss'),
        ]),
          (this.formInline.sendBeginTime = moment()
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss'))
        this.formInline.sendEndTime = moment(Date.now() - 300 * 1000).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        Object.assign(this.formInlines, this.formInline)
        this.InquireList()
        //    this.GroupIdChange()
        window.api.get(
          window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
          {},
          (res) => {
            this.options = res.data
          }
        )
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }

    // this.InquireList1();
    // this.InquireList2();

    this.time1String =
      this.formInline.sendBeginTime + '-' + this.formInline.sendEndTime
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps: function (val) {
      if (val == false) {
        this.valueReissueId = ''
        this.InquireList()
        // 清除formAdd表单数据
        this.formAdd = {
          reissueType: '1',
          smsInfoIds: '',
          channelGroupId: '',
          dxChannelId: '',
          ltChannelId: '',
          ydChannelId: '',
        }
        // 清除GroupArry数据
        this.GroupArry = []
        // 重置表单验证状态
        this.$nextTick(() => {
          this.$refs.formAdd && this.$refs.formAdd.resetFields()
        })
      }
    },
    activeName2(val) {
      if (val == 'second') {
        this.InquireList1()
      } else if (val == 'situation') {
        this.InquireList2()
      } else if (val == 'first') {
        this.InquireList()
      }
    },
    // // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList();
    //     },
    //     deep: true,
    //     immediate: true,
    // },
    // 监听搜索/分页数据
    // formInline1s:{
    //     handler() {
    //         this.InquireList1();
    //     },
    //     deep: true,
    //     immediate: true,
    // },
    // // 监听搜索/分页数据
    // formInline2s:{
    //     handler() {
    //         this.InquireList2();
    //     },
    //     deep: true,
    //     immediate: true,
    // },
    // 'ConditionForm.channelGroupId'(val){
    //     this.ConditionForm.ydChannelId=''
    //     this.ConditionForm.dxChannelId=''
    //     this.ConditionForm.ltChannelId=''
    //     if(val){
    //         window.api.get(window.path.omcs+'v3/operatingchannelgroup/getAllSMSGroup/'+val,{},res=>{
    //             this.CarrierChannel=res.data
    //         })
    //     }
    // },
    ConditionalReplacement(val) {
      if (val == false) {
        // 清除ConditionForm表单数据
        this.ConditionForm = {
          reissueType: '1',
          channelGroupId: '',
          dxChannelId: '',
          ltChannelId: '',
          ydChannelId: '',
        }
        // 清除GroupArry数据
        this.GroupArry = []
        // 清除相关数据
        this.tableDataObjCondition.tableData = []
        this.tableDataObjsendRecord.tableData = []
        this.multipleSelection = []
        this.radio = ''
        this.radio1 = ''
        this.radioNumber = ''
        this.radioContent = '' //发送内容
        this.radioMd5Content = ''
        this.CarrierChannel = {}
        this.FailureCode = '' //失败代码
        this.aisleCode = '' //通道号
        this.ReissueNum = '' //补发条数
        // 重置表单验证状态
        this.$nextTick(() => {
          this.$refs.ConditionFormRulef && this.$refs.ConditionFormRulef.resetFields()
        })
      }
    },
    ConditionalReplacementS(val) {
      if (val == false) {
        // 清除相关数据
        this.tableDataObjCondition.tableData = []
        this.tableDataObjsendRecord.tableData = []
        this.radio = ''
        this.radio1 = ''
        this.radioNumber = ''
        this.radioContent = '' //发送内容
        this.radioMd5Content = ''
        this.CarrierChannel = {}
        this.FailureCode = '' //失败代码
        this.aisleCode = '' //通道号
        this.ReissueNum = '' //补发条数
      }
    },
    'formInline.time1'() {
      this.time1String =
        this.formInline.sendBeginTime + '-' + this.formInline.sendEndTime
    },
    // 监听批量补发错误码对话框关闭
    batchReissueByCodeDialog(val) {
      if (val === false) {
        // 清除formBatchReissueByCode表单数据
        this.formBatchReissueByCode = {
          channelGroupId: '',
          dxChannelId: '',
          ltChannelId: '',
          ydChannelId: '',
        }
        // 清除GroupArry数据
        this.GroupArry = []
        // 重置表单验证状态
        this.$nextTick(() => {
          this.$refs.formBatchReissueByCode && this.$refs.formBatchReissueByCode.resetFields();
        })
      }
    },
    // 监听补发类型切换 - formAdd表单
    'formAdd.reissueType'(newVal, oldVal) {
      if (newVal !== oldVal) {
        // 清除通道相关字段
        this.formAdd.channelGroupId = '';
        this.formAdd.dxChannelId = '';
        this.formAdd.ltChannelId = '';
        this.formAdd.ydChannelId = '';
        // 清除GroupArry数据
        this.GroupArry = [];
      }
    },
    // 监听补发类型切换 - ConditionForm表单
    'ConditionForm.reissueType'(newVal, oldVal) {
      if (newVal !== oldVal) {
        // 清除通道相关字段
        this.ConditionForm.channelGroupId = '';
        this.ConditionForm.dxChannelId = '';
        this.ConditionForm.ltChannelId = '';
        this.ConditionForm.ydChannelId = '';
        // 清除GroupArry数据
        this.GroupArry = [];
      }
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}

.addC .el-select {
  width: 100%;
}

.addC .el-cascader {
  width: 100%;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.demo-table-expand {
  padding: 0 10px;
}

.el-input-group--prepend>.el-input-group__append,
.el-input-group__prepend {
  width: 80px !important;
}

.sensitive {}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style>
#SMSReplacement .el-input-group--prepend>.el-input-group__append,
.el-input-group__prepend {
  width: 56px !important;
}
</style>
<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
