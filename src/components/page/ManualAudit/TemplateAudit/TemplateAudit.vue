<template>
  <div class="container_left">
    <div class="Templat-matter">
      <p style="font-weight: bolder">tips：变量类型</p>
      <p>1、验证码：{valid_code}，4-6位数字英文混合，支持英文大小写。</p>
      <p>2、手机号：{ mobile_number}，1-15位纯数字。</p>
      <p>3、其他号码：{other_number}，1-20位英文+数字组合,支持中划线-。</p>
      <p>
        4、金额：{amount}，支持数字（含英文小数点.）或数字的中文（壹贰叁肆伍陆柒捌玖拾佰仟万亿 圆元整角分厘毫），最多15位。
      </p>
      <p>
        5、日期：{date}，符合时间的表达方式，也支持中文：2019年9月3日16时24分35秒，最多20位。
      </p>
      <p>6、中文：{chinese}，1-15中文，支持中文圆括号（）。</p>
      <p>7、其他：{others}， 1-30个中文数字字母组合,支持中文符号和空格。</p>
      <p>8、数字：{int_number}，支持1-10位数字。</p>
      <p>9、变量长度规范:您填写的变量参数（如姓名、金额等）合计长度上限不能超过短信全文的70%，具体是：变量长度上限相加后≤ 短信模板总长度 × 70%；例如：模板原文："尊敬的{name}，您的订单{other_number}已发货，运费{amount}元"{other_number}1-20位英文+数字组合,支持中划线-，取20字模板总长50字 → 变量{name}+{other_number}+{amount}总字数需≤35字提交前请自行核对，超长将自动驳回。</p>
    </div>
    <div class="fillet Templat-box">
      <el-form label-width="82px" :inline="true" :model="formInline" class="demo-form-inline" ref="queryForm">
        <el-form-item label="用户名" prop="clientName">
          <el-input v-model="formInline.clientName" placeholder="" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="排除用户" prop="excludeUsername">
          <el-input v-model="formInline.excludeUsername" placeholder="多个用户名用 , 号分割" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="模板名称" prop="temName">
          <el-input v-model="formInline.temName" placeholder="" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="模板格式" prop="temFormat">
          <el-select v-model="formInline.temFormat" clearable placeholder="不限" class="input-w">
            <el-option label="请选择" value="0"></el-option>
            <el-option label="变量模板" value="1"></el-option>
            <el-option label="普通模板" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板类型" prop="temType">
          <el-select v-model="formInline.temType" clearable placeholder="不限" class="input-w">
            <el-option label="验证码" value="1"></el-option>
            <el-option label="通知" value="2"></el-option>
            <el-option label="营销推广" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板ID" prop="temId">
          <el-input v-model="formInline.temId" placeholder="" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="审核状态" prop="temStatus">
          <el-select v-model="formInline.temStatus" clearable placeholder="不限" class="input-w">
            <el-option label="待审核" value="1"></el-option>
            <el-option label="审核通过" value="2"></el-option>
            <el-option label="审核未通过" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板内容" prop="temContent">
          <el-input v-model="formInline.temContent" placeholder="" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="审核时间" prop="time2">
          <el-date-picker class="input-time" v-model="time2" value-format="YYYY-MM-DD" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" @change="handeTime2">
          </el-date-picker>
        </el-form-item>
        <div style="margin-bottom: 18px">
          <el-button type="primary" plain style="" @click="Query">查询</el-button>
          <el-button type="primary" plain style="" @click="Reload('queryForm')">重置</el-button>
        </div>
      </el-form>
      <!-- <div class="boderbottom" style="clear: both">
       
            <el-button type="primary" plain  @click="clickAddTem">创建正文模板</el-button>
          </div> -->
      <div class="Templat-table tableColor">
        <!-- 表格和分页开始 -->
        <!-- <el-table
            v-loading="tableDataObj.loading2"
            border
            :stripe="true"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            :data="tableDataObj.tableData"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <!-- <el-button type="primary" @click="openImportDialog">模版导入</el-button> -->
            <!-- <export-button 
              :tableData="tableDataObj.tableData" 
              :parseTime="$parseTime"
              :headers="exportHeaders"
              :date-fields="['createTime', 'checkTime']"
              :formatters="exportFormatters"
              file-name-prefix="模板导出"
              button-text="模版导出"
              @export-start="handleExportStart" 
              @export-end="handleExportEnd" /> -->
            <el-button type="primary" v-if="selected.length > 0" @click="muchPass">批量通过</el-button>
            <el-button type="danger" v-if="selected.length > 0" @click="muchNoPass">批量驳回</el-button>
            <el-button type="danger" v-if="selected.length > 0" @click="delAll">批量删除</el-button>

          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="TemplateAudit" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px"
          :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange" @checkbox-change="handleSelectionChange">
          <vxe-column type="checkbox" width="50"> </vxe-column>
          <vxe-column field="模板ID" title="模板ID" width="90px">
            <template v-slot="scope">
              <div>{{ scope.row.temId }}</div>
            </template>
          </vxe-column>
          <vxe-column field="用户名" title="用户名" width="130">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.clientName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                     {{ scope.row.clientName}}
                                    </span>
                                </el-popover> -->
              <div style="color: #16a589; cursor: pointer" @click="rouTz(scope.row)">
                {{ scope.row.clientName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="模板名称" title="模板名称" width="150px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.temName" :content="scope.row.temName" className="wrapper-text" effect="light">
              </Tooltip>
              <!-- <span>{{ scope.row.temName }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="模板内容" title="模板内容" min-width="500px">
            <template v-slot="scope">
              <!-- <span>{{ scope.row.temContent }}</span> -->
              <!-- <div v-if="scope.row.iframeShow" class="tempContent">
                      {{scope.row.temContent }}
                    </div> -->
              <div>
                <div class="tempContent" v-html="scope.row.temContent"></div>
                <span style="color: #f56c6c">{{ scope.row.prompt }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="150px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.remark" :content="scope.row.remark" className="wrapper-text" effect="light">
              </Tooltip>
              <!-- <span>{{ scope.row.remark }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="模板变量" title="模板变量" min-width="240px">
            <template v-slot="scope">
              <div v-if="scope.row.text == '{}' || !scope.row.text"></div>
              <div v-else v-for="(item, index) in scope.row.text
                .replace(/\{|}/g, '')
                .split(',')">
                <span style="color: red">{{ item.split(":")[0] }}</span> :
                <span style="color: green">{{ item.split(":")[1] }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="模板类型" title="模板类型" width="100px">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.temType == 1">验证码</span>
                <span v-else-if="scope.row.temType == 2">通知</span>
                <span v-else-if="scope.row.temType == 3">营销推广</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="申请时间" title="申请时间" width="120px">
            <template v-slot="scope">
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, "{h}:{i}:{s}") }}
              </p>
              <!-- <span>{{ scope.row.createTime }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="审核状态" title="审核状态" width="100px">
            <template v-slot="scope">
              <el-tag :disable-transitions="true" v-if="scope.row.temStatus == '1'" type="info" effect="dark">
                待审核
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.temStatus == '2'" type="success" effect="dark">
                审核通过
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.temStatus == '3'" type="danger" effect="dark">
                审核未通过
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.temStatus == '4'" type="info" effect="dark">
                待复审
              </el-tag>
              <!-- <span v-if="scope.row.temStatus == 1">待审核</span>
                    <span v-else-if="scope.row.temStatus == 2">审核通过</span>
                    <span v-else-if="scope.row.temStatus == 3" style="color: red">审核未通过</span>
                    <span v-else-if="scope.row.temStatus == 4">待复审</span> -->
            </template>
          </vxe-column>
          <vxe-column v-if="searchInput.temStatus != 1" field="审核人" title="审核人">
            <template v-slot="scope">
              <div>{{ scope.row.checkName }}</div>
            </template>
          </vxe-column>
          <vxe-column v-if="searchInput.temStatus != 1" field="" title="审核时间" width="150">
            <template v-slot="scope">
              <div>{{ scope.row.checkTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="审核原因" title="审核原因" width="150px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.checkReason" :content="scope.row.checkReason" className="wrapper-text"
                effect="light">
              </Tooltip>
              <!-- <span>{{ scope.row.checkReason }}</span> -->
            </template>
          </vxe-column>
          <!-- <vxe-column
                            field="标签" title="标签"
                        
                           >
                            <template #default="scope">
                                <div v-if="scope.row.labels">
                                     <el-tag
:disable-transitions="true" style="margin-right:5px;color:white;margin-bottom:5px" :style="{background:tagColors[index]}"  v-for="(item,index) in scope.row.labels.split(',')"  :key="index">{{ item }}</el-tag>
                                </div>   
                            </template>
                            </vxe-column> -->
          <vxe-column field="操作" title="操作" width="165px" fixed="right">
            <template v-slot="scope">
              <el-button v-if="
                scope.row.temStatus == 1 ||
                (scope.row.temStatus == 4 && roleName != 'ROLE_CSE')
              " style="color: #16a589" link @click="handlePass(scope.$index, scope.row)">
                通过
              </el-button>
              <el-button v-if="
                scope.row.temStatus == 1 ||
                (scope.row.temStatus == 4 && roleName != 'ROLE_CSE')
              " style="color: orange; margin-left: 5px" link @click="handleNoPass(scope.$index, scope.row)">
                驳回
              </el-button>
              <el-button style="color: red; margin-left: 5px" link @click="delAllS(scope.row)">
                <el-icon>
                  <Delete />
                </el-icon>
                删除
              </el-button>
              <!-- <el-button  
                                       v-if="scope.row.temStatus==1 || scope.row.temStatus==2 || scope.row.temStatus==4"
                                        style="color:#16A589;margin-left:5px;"
                                        type="text"
                                        @click="handleEditTag(scope.$index, scope.row)">
                                        <i class="el-icon-edit-outline"></i>
                                        修改标签
                                     </el-button>
                                     <el-button
                                        style="color:#f56c6c;margin-left:5px;"
                                        type="text"
                                        @click="handleDel(scope.$index, scope.row)">
                                        <i class="el-icon-delete"></i>
                                        删除
                                     </el-button> -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.pageTotal">
          </el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 没有通过的弹窗 -->
      <el-dialog title="审核备注" width="450px" style="padding-bottom: 40px" v-model="dialogNoPass">
        <el-form ref="noPassform" :model="formNoPass" :rules="formNoPass1.formRule" label-width="80px">
          <el-form-item label="拒绝原因" prop="checkReason">
            <el-input type="textarea" v-model="formNoPass.checkReason" maxlength="70"
              placeholder="请输入70字以内的内容"></el-input>
          </el-form-item>
          <el-form-item style="text-align: right; margin-top: 30px">
            <el-button @click="dialogNoPass = false">取消</el-button>
            <el-button type="primary" @click="noPassOk('noPassform')">确定</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <el-dialog title="模板审核通过" v-model="AdoptVisible" width="30%" :before-close="handleClose">
        <el-form :inline="true" label-width="170px" :model="formTemp" :rules="temFormRules" class="demo-form-inline"
          ref="formTemp">
          <el-form-item v-if="temType" label="是否切换模板类型" prop="tempFlag">
            <el-radio-group v-model="formTemp.tempFlag">
              <el-radio v-if="temType == 2" value="1">验证码</el-radio>
              <el-radio v-if="temType == 2" value="2">通知</el-radio>
              <el-radio v-if="temType == 1 || temType == 2 || temType == 3" value="3">推广营销</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="formTemp.tempFlag == '3'" label="营销子类型" prop="subServiceType">
            <el-select class="input-w" v-model="formTemp.subServiceType" placeholder="请选择">
              <el-option v-for="item in businessList" :key="item.code" :label="item.desc"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item v-if="formTemp.tempFlag == '1'" label="模板类型" prop="temType">
                <el-radio-group v-model="formTemp.temType">
                  <el-radio value="1">验证码</el-radio>
                  <el-radio value="2">通知</el-radio>
                  <el-radio value="3">营销推广</el-radio>
                </el-radio-group>
              </el-form-item> -->
          <!-- <el-form-item label="确认注意事项" prop="">
            <div>
              <el-checkbox-button
                style="border: 1px solid #eee; margin: 0 5px"
                v-model="variableFlag"
                label="变量 "
                border
                @keyup.enter="addAdopt('formTemp')"
              ></el-checkbox-button>
              <el-checkbox-button
                style="border: 1px solid #eee; margin-right: 5px"
                v-model="linkFlag"
                label="链接"
                border
                @keyup.enter="addAdopt('formTemp')"
              ></el-checkbox-button>
              <el-checkbox-button
                style="border: 1px solid #eee; margin-right: 5px"
                v-model="sceneFlag"
                label="场景"
                border
                @keyup.enter="addAdopt('formTemp')"
              ></el-checkbox-button>
              <el-checkbox-button
                style="border: 1px solid #eee"
                v-model="violateFlag"
                label="违禁词"
                border
                @keyup.enter="addAdopt('formTemp')"
              ></el-checkbox-button>
            </div>
          </el-form-item> -->
        </el-form>
        <!-- <div style="margin: 10px 10px">请确认以下注意事项：</div>
            <el-checkbox-button style="border:1px solid #eee;margin:0 5px" v-model="variableFlag" label="变量 "
              border></el-checkbox-button>
            <el-checkbox-button style="border:1px solid #eee;margin-right:5px" v-model="linkFlag" label="链接"
              border></el-checkbox-button>
            <el-checkbox-button style="border:1px solid #eee;margin-right:5px" v-model="sceneFlag" label="场景"
              border></el-checkbox-button> -->
        <!-- <el-checkbox-button style="border:1px solid #eee" v-model="violateFlag" label="违禁词" border></el-checkbox-button> -->
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="AdoptVisible = false">取 消</el-button>
            <el-button v-if="loadingAudit" type="primary" loading>执行中</el-button>
            <el-button v-else type="primary" @click="addAdopt('formTemp')">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 导入示例对话框 -->
      <!-- <el-dialog title="导入示例" v-model="importDialogVisible" :close-on-click-modal="false" width="520px">
        <el-upload class="upload-demo" :action="actionUrl" :headers="headers" :on-success="handleUploadSuccess"
          :on-error="handleUploadError" :on-progress="handleProgress" :on-remove="handleRemove"
          :before-upload="beforeUpload" :limit="1" :file-list="fileList" accept=".xlsx,.xls">
          <el-button size="default" type="primary">
            <el-icon><upload-filled /></el-icon>选择文件上传
          </el-button>
          <template #tip>
            <div class="el-upload__tip">只能上传 xlsx、xls 格式的Excel文件，且不超过10MB</div>
          </template>
        </el-upload>
        <div v-if="Progressflag" style="text-align: center; font-size: 35px">
          <el-icon class="is-loading">
            <loading />
          </el-icon>
        </div>
        <template #footer>
          <el-button @click="importDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="importTemplate">确定</el-button>
        </template>
      </el-dialog> -->
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from "../../../../../utils/gogocodeTransfer";
import TableTem from "@/components/publicComponents/TableTem.vue";
import templateRule from "./components/TemplateRule.vue";
import editDiv from "./components/editDiv.vue";
import UserLists from "@/components/publicComponents/userList.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import ExportButton from "@/components/publicComponents/ExportButton.vue"; // 从公共组件导入
import common from "../../../../assets/js/common";
import { ElMessage } from 'element-plus';
// import { SERVER_TYPE, BUSINESS_TYPE, PUBLICBENEFIT_TYPE } from "../../../../utils/templateType";

export default {
  components: {
    TableTem,
    templateRule,
    editDiv,
    UserLists,
    Tooltip,
    ExportButton, // 注册导出按钮组件
  },
  name: "TemplateAudit",
  data() {
    var check = (rule, value, callback) => {
      console.log(this.tagForm.value1);
      if (value == "") {
        var status = true;
        for (var i = 0; i < this.tagForm.value1.length; i++) {
          if (this.tagForm.value1[i] == 55) {
            status = false;
            return callback(new Error("此处为必填项"));
          }
        }
        if (status) callback();
      } else {
        callback();
      }
    };
    return {
      customConfig: {
        storage: true,
        // // mode: "popup"
      },
      AdoptVisible: false,
      // importDialogVisible: false,
      userFlag: false,
      loadingAudit: false,
      fileList: [],
      actionUrl: window.path.omcs + 'template/upload',
      headers: {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN')
      },
      // serverList: SERVER_TYPE,
      businessList: [],
      // publicBenefitList: PUBLICBENEFIT_TYPE,
      Progressflag: false,
      // variableFlag: false,
      // linkFlag: false,
      // sceneFlag: false,
      // violateFlag: false,
      temIds: "",
      nameover: "",
      roleName: "",
      time1: "",
      time2: "",
      status: "展开",
      formInline: {
        clientName: "",
        excludeUsername: "",
        temName: "",
        temFormat: "",
        temType: "",
        temContent: "",
        temId: "",
        temStatus: "1",
        labels: "",
        checkName: "",
        beginTime1: "",
        endTime1: "",
        beginTime2: "",
        endTime2: "",
        labelIds: [],
        pageSize: 50,
        currentPage: 1,
      },
      searchInput: {
        clientName: "",
        excludeUsername: "",
        temName: "",
        temFormat: "",
        labelIds: [],
        temType: "",
        temContent: "",
        temId: "",
        temStatus: "1",
        labels: "",
        checkName: "",
        beginTime1: "",
        endTime1: "",
        beginTime2: "",
        endTime2: "",
        pageSize: 50,
        currentPage: 1,
      },
      formTemp: {
        tempFlag: "",
        temType: "",
        subServiceType: "",
      },
      temType: "",
      dialogTag: false, //修改标签弹窗(打开)
      tagForm: {
        //修改标签的弹窗表单
        value1: [],
        value2: [],
        value3: "",
        formRule: {
          // value1:[
          //     {required:true,message:"sss",trigger:'change'}
          // ],
          value3: [{ required: true, validator: check, trigger: "change" }],
          // value2:[
          //     {required:true,message:"sss",trigger:'change'}
          // ]
        },
      },
      //所有的标签
      optionsTag1: [],
      optionsTag2: [],
      optionsTag3: [],
      editTagTemId: "", //点击表格里的 编辑标签的时候带的模板id
      editTagStatus: "1", //编辑标签的状态（1--操作栏中的编辑标签，2---批量编辑标签）

      divInputVal: "", //div输入框的内容
      tabelAlllist: {
        //------发送表格请求的对象
        param: "",
        currentPage: 1, //当前页
        pageSize: 50, //每一页条数
      },
      param: "", //搜索条件--改变监听
      searchPage: {
        //搜索的页数--改变监听
        currentPage: 1,
        pageSize: 50,
      },
      pageTotal: 0, //总共条数
      TemDialogVisible: false, //---添加模板的弹窗
      formAdd: {
        temFormat: "", //模板格式
        temContent: "", //内容
        temType: "", //模板类型
        remark: "", //备注
        temName: "", //模板名
      },
      form: {
        clientName: "",
        temName: "",
        temType: "",
        labelId: {
          type: [],
          type2: "",
        },
        remark: "",
        temId: "",

        temFormat: "2",

        input1: {
          //-----普通模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
        input2: {
          //-----变量模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
      },
      formEmpty: {
        //清空对象
        clientName: "",
        temName: "",
        temType: "",
        labelId: {
          type: [],
          type2: "",
        },
        remark: "",
        temId: "",

        temFormat: "2",
        input1: {
          //-----普通模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
        input2: {
          //-----变量模板的输入框内容
          commonInputVal: "", //输入框内容
          numTextMsg: "0", //短信条数
          wordCount: "0", //字数
        },
      },
      temFormRules: {
        //添加模板内容的验证
        temName: [
          { required: true, message: "请输入模板名", trigger: "blur" },
          {
            pattern: /^[\u4e00-\u9fa5]{1,10}$|^[\dA-Za-z]{1,20}$/gi,
            message: "模板名为10个汉字或20个数字、英文字符!",
          },
        ],
        temType: [
          { required: true, message: "请选择模板类型", trigger: "change" },
        ],
        temContent: [
          { required: true, message: "请输入模板内容", trigger: "blur" },
        ],
      },
      //插入参数弹窗
      insertVal: {
        formData: {
          val: "",
          len: "1",
          min: "",
          max: "",
          fixed: "",
        },
        formRule: {
          val: [{ required: true, message: "请选择参数", trigger: "change" }],
          min: [{ required: true, message: "请输入最小长度", trigger: "blur" }],
          max: [{ required: true, message: "请输入最大长度", trigger: "blur" }],
          fixed: [
            { required: true, message: "请输入固定长度", trigger: "blur" },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        tableData: [],
        loading2: true,
        pageTotal: 0, //总共条数
      },
      myTemlist: {
        isDisable: 1,
        status: 2,
        param: "",
        currentPage: 1,
        pageSize: 10,
      },
      editVarCount: "",
      myTemSearch: "", //搜索我的模板
      myTemPageTotal: "", //总条数
      myTemTableDataObj: {
        //我的模板列表数据
        loading2: true,
        tableData: [],
        tableLabel: [
          {
            prop: "temId",
            showName: "ID",
            fixed: false,
          },
          {
            prop: "temType",
            showName: "模板类型",
            fixed: false,
            width: "150",
            formatData: function (val) {
              let type = "";
              if (val == 1) {
                type = "验证码";
              } else if (val == 2) {
                type = "通知";
              } else if (val == 3) {
                type = "营销推广";
              }
              return type;
            },
          },
          {
            prop: "temName",
            showName: "模板名称",
            fixed: false,
          },
        ],
        // 表头--折叠的
        tableLabelExpand: [
          {
            prop: "temContent",
            showName: "模板内容",
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          // isExpand:true,//是否是折叠的
          // isDefaultExpand:true,//默认打开折叠
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "选取",
            type: "",
            size: "mini",
            optionMethod: "getIt",
            icon: "el-icon-success",
          },
        ],
      },
      dialogTemRule: false,
      dialogMyTems: false, //我的模板库弹窗
      dialogInsertVal: false, //插入参数弹窗
      dialogInsertValStatus: "1", //参数弹窗的状态---1：新增   0：编辑
      insertDom: "", //添加的dom
      getCurIndex: "", //光标位置
      editTemDialog_status: 1, //模板是否是新增---1为新增，0为编辑
      selected: "", //表格中的复选框
      NoPassIds: [], //没有通过的id
      dialogNoPass: false, //没有通过的弹窗--关闭
      formNoPass: {
        arr: [],
        checkReason: "",
      },
      formNoPass1: {
        formRule: {
          checkReason: [
            { required: true, message: "请输入驳回原因", trigger: "change" },
          ],
        },
      },
      isFirstEnter: false,
      tagColors: [
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
        "#ff9900",
        "#e95d69",
        "#16a085",
      ],
      tagtemp: "",
      tempIds: [],
      selectedRows: [], // 当前选中的行
      // 导出表头配置
      exportHeaders: [
        { field: 'temId', title: '模板ID' },
        { field: 'clientName', title: '用户名' },
        { field: 'temName', title: '模板名称' },
        { field: 'temContent', title: '模板内容' },
        { field: 'remark', title: '备注' },
        { field: 'temType', title: '模板类型' },
        { field: 'createTime', title: '申请时间' },
        { field: 'temStatus', title: '审核状态' },
        { field: 'checkName', title: '审核人' },
        { field: 'checkTime', title: '审核时间' },
        { field: 'checkReason', title: '审核原因' }
      ],
      // 导出格式化器
      exportFormatters: {
        temContent: (value) => value ? value.replace(/<\/?[^>]+(>|$)/g, "") : '',
        temType: (value) => {
          if (value === '1' || value === 1) return '验证码';
          else if (value === '2' || value === 2) return '通知';
          else if (value === '3' || value === 3) return '营销推广';
          return '';
        },
        temStatus: (value) => {
          if (value === '1' || value === 1) return '待审核';
          else if (value === '2' || value === 2) return '审核通过';
          else if (value === '3' || value === 3) return '审核未通过';
          else if (value === '4' || value === 4) return '待复审';
          return '';
        }
      },
    };
  },
  computed: {
    numTextMsg1: function () {
      //1短信条数
      let numMsg = 0;
      //字数和短信条数的显示
      if (this.wordCount1 == 0) {
        numMsg = 0;
      } else if (
        parseInt(this.wordCount1) <= 70 &&
        parseInt(this.wordCount1) > 0
      ) {
        numMsg = 1;
      } else {
        numMsg = Math.floor((parseInt(this.wordCount1) - 70) / 67) + 1;
      }
      return numMsg;
    },
    wordCount1: function () {
      //1字数
      return this.countNum1(this.form.input1.commonInputVal);
    },
    numTextMsg2: function () {
      //2短信条数
      let numMsg = 0;
      //字数和短信条数的显示
      if (this.wordCount2 == 0) {
        numMsg = 0;
      } else if (
        parseInt(this.wordCount2) <= 70 &&
        parseInt(this.wordCount2) > 0
      ) {
        numMsg = 1;
      } else {
        numMsg = Math.floor((parseInt(this.wordCount2) - 70) / 67) + 1;
      }
      return numMsg;
    },
    wordCount2: function () {
      //2字数
      return this.countNum1(this.form.input2.commonInputVal);
    },
    dialogInsertValTitle: function () {
      //插入参数弹窗的标题
      return this.dialogInsertValStatus == "1" ? "插入参数" : "编辑参数";
    },
    editTemDialog_title: function () {
      //模板内容弹窗的标题
      return this.editTemDialog_status == "1" ? "新增短信模板" : "编辑短信模板";
    },
  },
  watch: {
    // searchInput:{//监听查询条件
    //         handler(){
    //             this.getTableData();//刷新表格
    //         },
    //         deep:true
    // },
    AdoptVisible(val) {
      const $table = this.$refs.tableRef;
      if (!val) {
        // this.variableFlag = false;
        // this.linkFlag = false;
        // this.sceneFlag = false;
        // this.violateFlag = false;
        this.temIds = "";
        this.formTemp.temType = "";
        this.formTemp.tempFlag = "";
        this.formTemp.subServiceType = "";
        this.temType = "";
        this.tempIds = [];
        this.loadingAudit = false;
        $table.clearCheckboxRow()
        // this.flag == 1
      }
    },
    /**--------------------监听编辑标签弹窗是否关闭--------------- */
    dialogTag(newVal, oldVal) {
      if (newVal == false) {
        this.editTagTemId = "";
        this.tagForm.value1 = [];
        this.tagForm.value2 = [];
        this.tagForm.value3 = "";
      }
    },
    /**---------------监听参数弹窗是否开启关闭------------- */
    dialogInsertVal(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.insertVal.resetFields(); //置空参数弹窗
        this.insertVal.formData.val = ""; //初始参数弹窗
        this.insertVal.formData.len = "1";
        this.insertVal.formData.min = "";
        this.insertVal.formData.max = "";
        this.insertVal.formData.fixed = "";
      }
    },
    /**----------监听添加模板弹窗是否关闭------------- */
    TemDialogVisible(newVal, oldVal) {
      if (newVal == false) {
        Object.assign(this.form, this.formEmpty);
        // this.addVarCount=0;
        this.editVarCount = "";
        this.$refs.temForm.resetFields();
        // this.$refs.divInput2.$refs.input2.innerHTML='';
        // this.$refs.divInput1.$refs.input1.innerHTML='';
      }
    },
    /**---------监听模板的查询条件是否改变--------------- */
    param() {
      this.tabelAlllist.param = this.param;
      this.tabelAlllist.currentPage = 1;
      this.tabelAlllist.pageSize = 50;
    },
    /**-------监听我的模板查询条件是否改变------------- */
    myTemSearch() {
      this.myTemlist.param = this.myTemSearch;
      this.myTemlist.currentPage = 1;
      this.myTemlist.pageSize = 10;
    },
    /**-----------监听我的模板库 弹窗是否关闭(将内容清空和选择page置为初始值) */
    dialogMyTems() {
      this.myTemlist.param = "";
      this.myTemlist.currentPage = 1;
      this.myTemlist.pageSize = 10;
    },
  },
  methods: {
    push(a, b, c) {
      if (this.tableDataObj.tableStyle.isDialog == true) {
        $emit(this, "routePushDialog", {
          rowData: a,
          currentData: b,
          temName: c,
        });
      } else {
        $emit(this, "routePush", { rowData: a, currentData: b, temName: c });
      }
    },
    handeTime: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime1 = this.moment(val[0]).format("YYYY-MM-DD ");
        this.formInline.endTime1 = this.moment(val[1]).format("YYYY-MM-DD ");
      } else {
        this.formInline.beginTime1 = "";
        this.formInline.endTime1 = "";
      }
    },
    handeTime2: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime2 = this.moment(val[0]).format("YYYY-MM-DD ");
        this.formInline.endTime2 = this.moment(val[1]).format("YYYY-MM-DD ");
      } else {
        this.formInline.beginTime2 = "";
        this.formInline.endTime2 = "";
      }
    },
    Query() {
      if (this.tableDataObj.loading2) return; //防止重复请求
      for (let key in this.formInline) {
        this.searchInput[key] = this.formInline[key];
      }
      this.searchInput.pageSize = 50;
      this.searchInput.currentPage = 1;
      this.getTableData();
      // console.log(this.searchInput)
      // console.log(typeof this.searchInput.labelIds)
    },
    Reload(formName) {
      this.formInline.labelIds = [];
      this.time1 = "";
      this.time2 = "";
      this.formInline.beginTime1 = "";
      this.formInline.beginTime2 = "";
      this.formInline.endTime1 = "";
      this.formInline.endTime2 = "";

      this.$refs[formName].resetFields(); //清空查询表单
      for (let k in this.formInline) {
        this.searchInput[k] = this.formInline[k];
      }
      this.searchInput.pageSize = 50;
      this.searchInput.currentPage = 1;
      this.tabelAlllist.pageSize = 50;
      this.tabelAlllist.currentPage = 1;
      this.getTableData();
    },
    handleSelectionChange(selection) {
      this.selected = selection.records;
      this.selectedRows = selection.records;
      if (selection.records.length === 0) {
        // 没有选中时清空类型
        this.temType = "";
        return;
      }
      // 提取选中行的类型
      const types = selection.records.map((item) => item.temType);
      // 判断类型是否一致
      const allSameType = types.every((type) => type === types[0]);
      // 如果一致则赋值为该类型，否则清空
      this.temType = allSameType ? types[0] : "";
      this.formTemp.tempFlag = this.temType + "";
      console.log(this.temType, 'selectedType');
    },

    muchNoPass() {
      //批量驳回
      let arr = [];
      let flag = 1;
      this.selected.forEach((item, index) => {
        if (item.temStatus == 2) {
          flag = 0;
          this.$message({
            message: "选择的模板中有已经通过的，不能再次驳回，请重新选择",
            type: "warning",
          });
          return false;
        }
        arr.push(item.temId);
      });
      //发送请求
      if (flag == 1) {
        this.formNoPass.arr = arr;
        this.formNoPass.checkReason = "";
        this.formNoPass.checkReason = "";
        this.dialogNoPass = true; //打开弹窗
      }
    },
    rouTz(val) {
      this.$router.push({ path: "/UserDetail", query: { id: val.temUserId } });
    },
    userList(row, index) {
      this.userFlag = true;
      this.nameover = index;
    },
    muchPass() {
      //批量通过
      // let arr = [];
      this.tagtemp = "1";
      let flag = 1;
      this.selected.forEach((item, index) => {
        if (item.temStatus == 3) {
          flag = 0;
          this.$message({
            message: '"选择的模板中有已经驳回的，不能再次通过，请重新选择"',
            type: "warning",
          });
          return false;
        }
        if (item.temStatus == 2) {
          flag = 0;
          this.$message({
            message: '"选择的模板中有已经通过的，不能再次通过，请重新选择"',
            type: "warning",
          });
          return false;
        }
        this.tempIds.push(item.temId);
      });

      //发送请求
      if (flag == 1) {
        this.AdoptVisible = true;
        // this.$confirms.confirmation(
        //   "post",
        //   "确认审核通过模板吗？",
        //   window.path.omcs + "templateaudit/auditPass",
        //   { arr: arr },
        //   (res) => {
        //     this.getTableData();
        //   }
        // );
      }
    },
    /**--------------------操作栏的操作-------------- */

    handlePass(index, val) {
      //   let arr = [];
      //   arr.push(val.temId);
      //发送请求
      this.temIds = val.temId;
      this.tagtemp = "0";
      this.formTemp.tempFlag = val.temType + "";
      this.temType = val.temType;
      this.formTemp.subServiceType = val.subServiceType;
      this.AdoptVisible = true;
      //   this.$confirms.confirmation(
      //     "post",
      //     "确认审核通过模板吗？",
      //     window.path.omcs + "templateaudit/auditPass",
      //     { arr: arr },
      //     (res) => {
      //       this.getTableData();
      //     }
      //   );
    },
    // handleCheck(e) {
    //   if (e == '0') {
    //     this.formTemp.temType = ''
    //   } else {
    //     this.formTemp.temType = '3'
    //   }
    // },
    addAdopt(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loadingAudit = true;
          if (this.tagtemp == "1") {
            let data = {
              arr: this.tempIds,
              temType: this.formTemp.tempFlag,
              // subServiceType: this.formTemp.subServiceType
            }
            if (this.formTemp.subServiceType) {
              data.subServiceType = this.formTemp.subServiceType
            }
            this.$confirms.confirmation(
              "post",
              "确认批量审核通过模板吗？",
              window.path.omcs + "templateaudit/auditPass",
              data,
              (res) => {
                this.getTableData();
                this.loadingAudit = false;
                this.AdoptVisible = false;
              }
            );
          } else {
            this.$confirms.confirmation(
              "post",
              "确认审核通过模板吗？",
              window.path.omcs + "templateaudit/auditPass",
              { arr: [this.temIds], temType: this.formTemp.tempFlag, subServiceType: this.formTemp.subServiceType },
              (res) => {
                this.getTableData();
                this.loadingAudit = false;
                this.AdoptVisible = false;
              }
            );
          }
          // if (
          //   this.variableFlag &&
          //   this.linkFlag &&
          //   this.sceneFlag &&
          //   this.violateFlag
          // ) {

          // } else {
          //   this.$message({
          //     message: "审核模版时请确认以下事项：变量、链接、场景、违规词",
          //     type: "warning",
          //   });
          // }
        } else {
          console.log("error submit!!");
        }
      });
    },
    handleClose() {
      this.AdoptVisible = false;
    },
    handleNoPass(index, val) {
      let arr = [];
      arr.push(val.temId);
      this.formNoPass.arr = arr;
      val.prompt != ""
        ? (this.formNoPass.checkReason = val.prompt)
        : (this.formNoPass.checkReason = "");
      this.dialogNoPass = true; //打开弹窗
    },
    //确认不通过
    noPassOk(formName) {
      //发送请求
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "确认驳回模板吗？",
            window.path.omcs + "templateaudit/auditNotPass",
            this.formNoPass,
            (res) => {
              this.getTableData();
              this.dialogNoPass = false;
            }
          );
        }
      });
    },
    handleEditTag(index, val) {
      //编辑标签
      if (val.labelId) {
        let arrLabel = val.labelId.split(",");
        let option1 = [];
        let option2 = [];
        let option3 = [];
        let labelValue1 = [];
        let labelValue2 = [];
        let labelValue3 = "";
        for (let k = 0; k < this.optionsTag1.length; k++) {
          option1.push(this.optionsTag1[k].labelId);
        }
        for (let k = 0; k < this.optionsTag2.length; k++) {
          option2.push(this.optionsTag2[k].labelId);
        }
        for (let k = 0; k < this.optionsTag3.length; k++) {
          option3.push(this.optionsTag3[k].labelId);
        }
        //回显类型 标签
        for (var k = 0; k < arrLabel.length; k++) {
          for (var i = 0; i < option1.length; i++) {
            if (arrLabel[k] == option1[i]) {
              let label = parseInt(arrLabel[k]);
              labelValue1.push(label);
            }
          }
        }
        this.tagForm.value1 = labelValue1;
        // this.tagForm.value1=[]
        //回显行业 标签
        for (let k = 0; k < arrLabel.length; k++) {
          for (var i = 0; i < option2.length; i++) {
            if (arrLabel[k] == option2[i]) {
              let label = parseInt(arrLabel[k]);
              labelValue2.push(label);
            }
          }
        }
        this.tagForm.value2 = labelValue2;
        // this.tagForm.value2=[]
        //回显退订格式
        for (let k = 0; k < arrLabel.length; k++) {
          if (option3.indexOf(parseInt(arrLabel[k])) > -1) {
            labelValue3 = parseInt(arrLabel[k]);
          }
        }
        this.tagForm.value3 = labelValue3;
        // this.tagForm.value3=''
      }
      this.editTagStatus = 1;
      this.editTagTemId = val.temId;
      this.dialogTag = true;
    },
    handleDel(index, val) {
      let temId = val.temId;
      //发送请求
      this.$confirms.confirmation(
        "post",
        "确认删除该模板吗？",
        window.path.omcs + "templateaudit/delete",
        { temId: temId },
        (res) => {
          this.getTableData();
        }
      );
    },

    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      let formDatas = this.searchInput;
      // console.log(formDatas);
      window.api.post(
        window.path.omcs + "templateaudit/page",
        formDatas,
        (res) => {
          this.tableDataObj.tableData = res.records;
          // this.tableDataObj.tableData.forEach((item, index) => {
          //   if(common.hasIframe(item.temContent) ){
          //     item.iframeShow = true;
          //   }else{
          //     item.iframeShow = false;
          //   }
          // });
          this.tableDataObj.loading2 = false;
          this.tableDataObj.pageTotal = res.total;
        }
      );
    },
    /**-----------------列表展示我的模板------------- */
    getMyTemTableData() {
      //获取列表数据
      this.myTemTableDataObj.loading2 = true;
      let formDatas = this.myTemlist;
      // window.api.post(window.path.omcs+'templateaudit/page',formDatas,res=>{
      //     this.myTemTableDataObj.tableData = res.records;
      //     this.myTemTableDataObj.loading2 = false;
      //     this.myTemPageTotal=res.total;
      // })
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.searchInput.pageSize = size;
      this.tabelAlllist.pageSize = size;
      this.getTableData();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.searchInput.currentPage = currentPage;
      this.tabelAlllist.currentPage = currentPage;
      this.getTableData();
    },
    handleSizeChangeMyTem(size) {
      //分页每一页的有几条
      this.searchInput.pageSize = size;
      this.getTableData();
    },
    handleCurrentChangeMyTem: function (currentPage) {
      //分页的第几页
      this.searchInput.currentPage = currentPage;
      console.log(this.myTemlist);
      this.getTableData();
    },

    /**---------插入数据----------- */
    //监听字数
    countNum1(val) {
      let len = 0; //字数
      let regVar1 = /[{](var[1-9]\d?[|])[d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g; //可变长度
      let regVar2 = /[{](var[1-9]\d?[|])[d|w|$|c]([1-9]\d?)[}]/g; //固定长度
      if (regVar1.test(val) || regVar2.test(val)) {
        let regVar1Arr = val.match(regVar1);
        let regVar2Arr = val.match(regVar2);
        if (regVar1Arr) {
          //如果是可变长度类型的参数，要取出长度
          for (let i = 0; i < regVar1Arr.length; i++) {
            let variableLen = Number(
              regVar1Arr[i].split("-")[1].replace(/[^0-9]/gi, "")
            );
            len += variableLen;
          }
        }
        if (regVar2Arr) {
          for (let i = 0; i < regVar2Arr.length; i++) {
            let fixedLen = Number(regVar2Arr[i].match(/\d+/g)[1]); //字符串中找出数字，组中的第二个数字为固定长度
            len += fixedLen;
          }
        }
        val = val.replace(regVar1, "").replace(regVar2, "");
      }
      val = val
        .replace(/<br>/g, "")
        .replace(/\n/g, "")
        .replace(/\r/g, "")
        .replace(/\{/gi, "")
        .replace(/\}/gi, "")
        .replace(/<\/?[^>]*>/g, "")
        .replace(/&nbsp;/gi, " ")
        .replace(/var[1-9]\d?[|]/gi, "");
      len += val.length;
      return len;
    },
    //分割模板内容的参数 验证参数间是否有间隔
    //监听字数
    countNum(dom) {
      let recentVal = ""; //目前的内容
      let len = 0; //字数
      let smsNum = 0; //短信条数
      recentVal = this.$refs[dom].innerText;
      let regVar1 = /[{][d|w|$|c](0|[1-9]\d?)([-][1-9]\d?)[}]/g; //可变长度
      let regVar2 = /[{][d|w|$|c]([1-9]\d?)[}]/g; //固定长度
      if (regVar1.test(recentVal) || regVar2.test(recentVal)) {
        let regVar1Arr = recentVal.match(regVar1);
        let regVar2Arr = recentVal.match(regVar2);
        if (regVar1Arr) {
          //如果是长度类型的参数，要取出长度
          for (let i = 0; i < regVar1Arr.length; i++) {
            let variableLen = Number(
              regVar1Arr[i].split("-")[1].replace(/[^0-9]/gi, "")
            );
            len += variableLen;
          }
        }
        if (regVar2Arr) {
          for (let i = 0; i < regVar2Arr.length; i++) {
            let fixedLen = Number(regVar2Arr[i].replace(/[^0-9]/gi, ""));
            len += fixedLen;
          }
        }
        recentVal = recentVal.replace(regVar1, "").replace(regVar2, "");
      }
      recentVal = recentVal
        .replace(/<br>/g, "")
        .replace(/\n/g, "")
        .replace(/\r/g, "")
        .replace(/\{/gi, "")
        .replace(/\}/gi, "")
        .replace(/<\/?[^>]*>/g, "")
        .replace(/&nbsp;/gi, " ");
      if (recentVal.length >= 450) {
        //超过450个字字数截取
        this.$refs[dom].innerHTML = recentVal.substr(0, 450); //截取后文字内容放在里面--由于有插入的标签内容，所以使用innerHTML
        this.keepLastIndex(this.$refs[dom]); //光标放在最后
        len = 450;
      } else {
        len += recentVal.length;
      }
      //字数和短信条数的显示
      if (len == 0) {
        smsNum = 0;
      } else if (parseInt(len) <= 70 && parseInt(len) > 0) {
        smsNum = 1;
      } else {
        smsNum = Math.floor((parseInt(len) - 70) / 67) + 1;
      }
      if (dom == "input1") {
        this.form.input1.wordCount = len;
        this.form.input1.numTextMsg = smsNum;
      } else if (dom == "input2") {
        this.form.input2.wordCount = len;
        this.form.input2.numTextMsg = smsNum;
      }
      let num = {};
      num.wordCount = len;
      num.numTextMsg = smsNum;
      return num;
    },

    handleClick(tab, event) {
      console.log(tab, event);
    },
    openTemRule() {
      //点击模板规则
      this.dialogTemRule = true;
    },
    //提交
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (val == "insertVal") {
          if (valid) {
            this.dialogInsertVal = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        }
      });
    },
    //批量删除
    delAll() {
      let arr = [];
      this.selected.forEach((item, index) => {
        arr.push(item.temId);
      });
      //批量删除
      this.$confirms.confirmation(
        "get",
        "此操作将永久删除该数据, 是否批量删除？",
        window.path.omcs + "template/batchDelete/" + arr.join(","),
        {},
        () => {
          this.getTableData();
        }
      );
    },
    //单个批量删除
    delAllS(val) {
      this.$confirms.confirmation(
        "get",
        "此操作将永久删除该数据, 是否单个删除？",
        window.path.omcs + "template/batchDelete/" + val.temId,
        {},
        () => {
          this.getTableData();
        }
      );
    },
    // openImportDialog() {
    //   this.importDialogVisible = true;
    //   this.fileList = [];
    // },
    // importTemplate() {
    //   this.importDialogVisible = false;
    //   this.getTableData();
    // },
    // handleUploadSuccess(res, file) {
    //   console.log('上传结果', res);
    //   this.Progressflag = false;
    //   if (res.code === 200) {
    //     ElMessage.success('导入成功');

    //   } else {
    //     ElMessage.error(res.msg || '导入失败');
    //   }
    // },
    // handleUploadError(error) {
    //   this.Progressflag = false;
    //   ElMessage.error('上传失败：' + error);
    // },
    // handleProgress() {
    //   this.Progressflag = true;
    // },
    // handleRemove(file, fileList) {
    //   console.log('文件移除', file, fileList);
    //   this.fileList = [];
    // },
    // beforeUpload(file) {
    //   console.log(file, 'file');

    //   const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    //     file.type === 'application/vnd.ms-excel';
    //   const isLt10M = file.size / 1024 / 1024 < 10;

    //   if (!isExcel) {
    //     ElMessage.error('只能上传Excel文件!');
    //     return false;
    //   }
    //   if (!isLt10M) {
    //     ElMessage.error('文件大小不能超过10MB!');
    //     return false;
    //   }
    //   return true;
    // },
    getSeverType() {
      window.api.post(
        window.path.omcs + "signatureaudit/allSubServiceType",
        {},
        (res) => {
          if (res.code == 200) {
            this.businessList = res.data; //营销类型列表
          } else {
            this.$message({
              type: "error",
              duration: "2000",
              message: res.msg,
            });
          }
        }
      );
    },
    // 处理导出开始事件
    handleExportStart() {
      // 可以在这里添加导出开始的处理，比如显示loading等
      console.log('导出开始');
    },
    
    // 处理导出结束事件
    handleExportEnd(success) {
      console.log('导出结束，状态:', success ? '成功' : '失败');
      // 可以在这里添加导出结束后的处理逻辑
    },
  },
  mounted() {
    // this.getTableData();
    this.getMyTemTableData();
    // window.api.post(/templateaudit/getRole
    window.api.post(window.path.omcs + "templateaudit/getRole", {}, (res) => {
      this.roleName = res;
    });
    // console.log("formInline",this.formInline)
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.getTableData();
      this.getSeverType();
    });
    // this.getTableData();
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  // created(){
  //     if(document.querySelector("#asdasd"))console.log(1)
  //     console.log(document.querySelector("#asdasd1"))
  // }
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getSeverType();
        this.getTableData();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  emits: ["routePushDialog", "routePush"],
};
</script>

<style lang="less" scoped>
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Templat-matter>p {
  padding: 5px 0;
}

.Templat-set {
  color: #0066cc;
}

.Templat-creat {
  margin-top: 20px;
}

.Templat-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Templat-list-header {
  position: absolute;
  font-weight: bold;
  left: 0px;
  top: 12px;
}

.Templat-table {
  padding-bottom: 40px;
}

.Templat-search-fun {
  position: relative;
  height: 40px;
  margin-top: 20px;
  padding-bottom: 6px;
}

.Templat-search-box {
  position: absolute;
  right: 0px;
}

.tem-font {
  font-size: 12px;
}

.tem-font span {
  color: red;
}

.tem-be-careful {
  font-size: 12px;
  color: #999;
  line-height: 20px;
  margin: 10px 0;
}

.tem-title {
  position: absolute;
  left: 32px;
  top: 250px;
}

.tem-title:before {
  position: absolute;
  content: "*";
  color: #f56c6c;
  left: -10px;
}

.tem-subTitle {
  position: absolute;
  left: 32px;
  top: 285px;
  color: #16a589;
  cursor: pointer;
}

.template-btn-1 {
  position: absolute;
  right: -100px;
  top: 12px;
}

.template-btn-2 {
  position: absolute;
  right: -89px;
  top: 57px;
}

.common-template-val,
.variable-template-val {
  min-height: 100px;
  _height: 100px;
  border-right: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  outline: none;
  word-wrap: break-word;
  padding: 4px 10px;
  color: #000;
}

.f-basic {
  color: #16a589;
  font-weight: bold;
}

:deep(.tempContent) {
  .prompt {
    color: #f56c6c;
    display: inline-block;
    font-weight: 800;
    /* background: rgba(0, 0, 0, .5); */
    /* text-shadow: 3px 3px 5px #FF0000; */
  }

  .prompt_error {
    color: #e6a23c;
    display: inline-block;
    text-decoration: underline;
    font-weight: 800;
    /* background: rgba(0, 0, 0, .5); */
    /* text-shadow: 3px 3px 5px #FF0000; */
  }
}
</style>

<style lang="less">
.el-table--striped .el-table__body tr.el-table__row--striped td {
  // background: #99999915 !important;
}

.text-adaNum {
  color: #16a589;
  cursor: pointer;
}

.TemDialog .el-tabs__item {
  height: 32px;
  line-height: 32px;
}

.TemDialog .el-tabs__header {
  margin: 0px;
}

.TemDialog .el-tabs__content {
  overflow: inherit;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.el-tag--mini {
  height: auto;
  word-wrap: break-all;
  white-space: normal;
}

.el-select__tags-text {
  word-wrap: break-all;
  white-space: normal;
}

.el-icon-close {
  display: inline-block;
}

.el-table--small th {
  background: #f5f5f5;
}

.el-checkbox-button__inner {
  border: none;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>