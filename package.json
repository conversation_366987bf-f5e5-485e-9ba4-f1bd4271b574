{"name": "jl-mis", "version": "0.1.0", "private": true, "scripts": {"serve": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode prod"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^0.18.0", "babel-polyfill": "^6.26.0", "clipboard": "^2.0.11", "core-js": "^3.8.3", "dayjs": "^1.11.13", "echarts": "^5.0.2", "element-china-area-data": "^5.0.2", "element-plus": "^2.3.9", "fast-glob": "^3.3.2", "lodash": "^4.17.21", "mavon-editor": "^2.6.17", "moment": "^2.29.1", "socket.io-client": "^4.2.0", "tiny-emitter": "^2.1.0", "vue": "^3.2.41", "vue-cropperjs": "^2.2.0", "vue-quill-editor": "3.0.6", "vue-router": "^4.1.6", "vue-socket.io": "^3.0.10", "vuedraggable": "^2.16.0", "vuex": "^4.0.2", "vxe-table": "^4.10.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@vitejs/plugin-vue": "^1.9.3", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.11.0", "babel-plugin-transform-remove-console": "^6.9.4", "default-passive-events": "^2.0.0", "eslint": "^5.10.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.9.0", "less-loader": "^4.1.0", "path-browserify": "^1.0.1", "postcss-pxtorem": "^6.0.0", "unplugin-auto-import": "^0.5.7", "vite": "^2.8.6", "vite-plugin-style-import": "^1.4.1", "vite-plugin-svg-icons": "^2.0.1", "vite-svg-loader": "^4.0.0"}}