<template>
  <div>
    <div class="OuterFrame fillet">
      <div class="from">
        <el-form
          label-width="82px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="执行状态" prop="status">
            <el-select
              class="input-w"
              v-model="formInline.status"
              clearable
              placeholder="请选择"
            >
              <el-option label="待处理" value="0"> </el-option>
              <el-option label="处理中" value="1"> </el-option>
              <el-option label="已成功 " value="2"> </el-option>
              <el-option label="已下发" value="3"> </el-option>
              <el-option label="已失败" value="4"> </el-option>
              <el-option label="异常" value="-1"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="time">
            <el-date-picker
              v-model="formInline.time"
              type="datetimerange"
              @change="time"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <!-- <div class="boderbottom">
            
            </div> -->
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">彩信路由明细列表</span> -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <el-table-column prop="username" label="用户名称"> </el-table-column>
          <el-table-column label="msgId" width="200px">
            <template v-slot="scope">
              {{ scope.row.msgId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;"
                                    class="el-icon-document-copy" @click="handleCopy(scope.row.msgId, $event)"></i> -->
              <CopyTemp :content="scope.row.msgId" />
            </template>
          </el-table-column>
          <el-table-column label="手机号" width="100px">
            <template v-slot="scope">
              {{ scope.row.mobile }}
              <!-- <div v-if="scope.$index == pindex" style="color: rgb(22, 165, 137); cursor: pointer;">
                                    {{ scope.row.mobile }}
                                </div>
                                <div v-else style="color: rgb(22, 165, 137); cursor: pointer;"
                                    @click="phoneClick(scope.$index, scope.row)">{{ scope.row.maskMobile }}</div> -->
            </template>
          </el-table-column>
          <!-- <el-table-column label="发送类型">
                            <template #default="scope">
                                <span v-if="scope.row.sendType == 1">自动补发</span>
                                <span v-else-if="scope.row.sendType == 2">首次发送</span>
                                <span v-else-if="scope.row.sendType == 3">手动补发</span>
                                <span v-else-if="scope.row.sendType == 4">携号转网</span>
                            </template>
                        </el-table-column> -->
          <el-table-column prop="num" label="短信条数"> </el-table-column>
          <el-table-column prop="pnum" label="回执序号"> </el-table-column>
          <el-table-column prop="channelId" label="通道号"> </el-table-column>
          <el-table-column prop="time" label="回执时间" width="170px">
          </el-table-column>
          <el-table-column label="发送状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                v-if="scope.row.smsStatus == 1"
                type="success"
                effect="dark"
              >
                成功
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.smsStatus == 2"
                type="danger"
                effect="dark"
              >
                失败
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.smsStatus == 3"
                type="info"
                effect="dark"
              >
                待返回
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="code" label="错误码" width="170px">
          </el-table-column>
          <!-- <el-table-column label="备注">
                            <template #default="scope">
                                <Tooltip v-if="scope.row.originalCode" :content="scope.row.originalCode"
                                    className="wrapper-text" effect="light">
                                </Tooltip>
                            </template>
                        </el-table-column> -->
          <el-table-column label="执行状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                待处理
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
              >
                处理中
              </el-tag>
              <el-tag
:disable-transitions="true" v-else-if="scope.row.status == 2" type="" effect="dark">
                已成功
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="success"
                effect="dark"
              >
                已下发
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 4"
                type="danger"
                effect="dark"
              >
                已失败
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == -1"
                type="danger"
                effect="dark"
              >
                异常
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
// import clip from '../../../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    CopyTemp,
  },
  data() {
    return {
      isFirstEnter: false,
      formInline: {
        username: '',
        status: '',
        createStartTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        createEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      // pickerOptions: {
      //     shortcuts: [{
      //         text: '最近三天',
      //         onClick(picker) {
      //             const end = new Date();
      //             const start = new Date();
      //             start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      //             picker.$emit('pick', [start, end]);
      //         }
      //     }, {
      //         text: '最近一周',
      //         onClick(picker) {
      //             const end = new Date();
      //             const start = new Date();
      //             start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      //             picker.$emit('pick', [start, end]);
      //         }
      //     }]
      // },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    }
  },
  methods: {
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumersms/successratemonitor/page',
        this.formInline,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    Query() {
      // Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      this.formInline.userName = ''
      this.formInline.mobile = ''
      this.formInline.status = ''
      this.formInline.createStartTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline.createEndTime = moment(Date.now()).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      this.formInline.time = [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        Date.now(),
      ]
      this.gettableLIst()
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.formInline.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.formInline.currentPage = currentPage
      this.gettableLIst()
    },
    time(val) {
      // console.log(this.value2,'val');
      if (val) {
        this.formInline.createStartTime = moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.formInline.createEndTime = moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline.createStartTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.formInline.createEndTime = moment(Date.now()).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
    },
    // handleCopy(name,event){
    // clip(name, event)
    // },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
</style>
