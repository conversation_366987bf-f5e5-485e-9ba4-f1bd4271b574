<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        ref="formBlackList"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="formBlackList.userName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="formBlackList.mobile"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input
            v-model="formBlackList.signature"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            class="input-time"
            v-model="formBlackList.createTime"
            format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="hande"
          >
          </el-date-picker>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click="quenryBlack()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
        </div>
      </el-form>

      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        >
          <template #tableButtons>
            <el-button type="primary" @click="blackListDialog = true">新增签名黑名单</el-button>
            <el-button type="danger" @click="batchDeletion" v-if="selectId.length > 0">批量删除</el-button>
          </template>
        </table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formBlackListObj.currentPage"
            :page-size="formBlackListObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 弹窗（新增） -->
      <el-dialog
        title="新增黑名单"
        v-model="blackListDialog"
        width="420px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="blackList.formData"
          :rules="blackList.formRule"
          ref="blackList"
          label-width="96px"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="blackList.formData.userName"></el-input>
          </el-form-item>
          <el-form-item
            label="手机号码"
            prop="mobile"
            :rules="
              filter_rules({
                required: true,
                type: 'mobile',
                message: '手机号码不能为空！',
              })
            "
          >
            <el-input v-model="blackList.formData.mobile"></el-input>
          </el-form-item>
          <el-form-item label="签名" prop="signature">
            <el-input v-model="blackList.formData.signature"></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              @click="blackListDialog = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              type="primary"
              @click="blacklist_ok_add('blackList')"
              >确 认</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    TableTem
  },
  name: 'signatureBlack',
  data() {
    return {
      isFirstEnter: false,
      formBlackList: {
        //查询框的值
        userName: '',
        mobile: '',
        signature: '',
        createTime: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      formBlackListObj: {
        //copy查询框的值
        userName: '',
        mobile: '',
        signature: '',
        createTime: [],
        createName: '',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      selectId: '', //批量操作选中id
      rowId: '', //操作行的id
      blackListDialog: false, //弹框显示隐藏
      blackList: {
        formData: {
          blacklistPhone: '',
          blacklistType: '',
          blacklistLevels: [],
          remark: '',
        },
        formRule: {
          userName: [
            { required: true, message: '请填写用户名', trigger: 'change' },
          ],
          signature: [
            { required: true, message: '请填写签名', trigger: 'change' },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        custom: true,
        id: "signatureBlack",
        tableData: [],
        title: '',
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        tableLabel: [
          {
            prop: 'userName',
            showName: '用户名',
          },
          {
            prop: 'mobile',
            showName: '手机号码',
          },
          {
            prop: 'signature',
            showName: '签名',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '160px',
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '100', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'delet',
            icon: 'CircleCloseFilled',
            color: '#F56C6C',
          },
        ],
      },
    }
  },
  methods: {
    //-------------   列表信息   --------------
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true //打开loading
      window.api.post(
        window.path.omcs + 'blacksignaturemobile/page',
        this.formBlackListObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.totalRow = res.data.total
          this.tableDataObj.loading2 = false //关闭loading
        }
      )
    },
    //查询
    quenryBlack() {
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande: function (val) {
      if (val) {
        this.formBlackList.beginTime = this.moment(val[0]).format('YYYY-MM-DD')
        this.formBlackList.endTime = this.moment(val[1]).format('YYYY-MM-DD')
      } else {
        this.formBlackList.beginTime = ''
        this.formBlackList.endTime = ''
      }
    },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.formBlackListObj.pageSize = size
      this.getTableDtate()
    },
    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.formBlackListObj.currentPage = currentPage
      this.getTableDtate()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.formBlackList.beginTime = ''
      this.formBlackList.endTime = ''
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //-------------   列表信息   --------------

    //-------------   新增功能开始   --------------
    blacklist_ok_add(formName) {
      //提交
      this.$refs[formName].validate((valid) => {
        //表单数据验证
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '确定新增当前签名黑名单？',
            window.path.omcs + 'blacksignaturemobile',
            this.blackList.formData,
            (res) => {
              this.getTableDtate()
              this.blackListDialog = false
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //-------------  新增功能结束  --------------
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
      }
      this.selectId = selectId.join(',')
    },
    handelOptionButton: function (val) {
      //操作按钮
      if (val.methods == 'delet') {
        //删除
        this.$confirms.confirmation(
          'delete',
          '此操作将永久删除该条数据, 是否继续?',
          window.path.omcs + 'blacksignaturemobile/delete',
          { ids: val.row.id },
          (res) => {
            this.getTableDtate()
          }
        )
      }
    },
    batchDeletion() {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续?',
        window.path.omcs + 'blacksignaturemobile/delete',
        { ids: this.selectId },
        (res) => {
          this.getTableDtate()
        }
      )
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  watch: {
    //监听查询框对象的变化
    // formBlackListObj:{
    //     handler(){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    blackListDialog(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.blackList.resetFields() //清空表单
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
