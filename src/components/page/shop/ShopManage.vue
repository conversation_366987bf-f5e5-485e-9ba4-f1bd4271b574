<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="商品名称" label-width="80px" prop="signature">
            <el-input
              v-model="formInline.name"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" label-width="80px" prop="time">
            <el-date-picker
              class="input-w"
              v-model="time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              @change="hande"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
        <!-- <el-button type="primary" plain style @click="download">导出数据</el-button> -->
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->

        <!-- <table-tem
              :tableDataObj="tableDataObj"
              @handelOptionButton="handelOptionButton"
              @handelSelection="handelSelection"
              @save="save"
            ></table-tem> -->
        <!--分页-->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">添加商品</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="ShopManage"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <!-- <vxe-column field="" title="序号" width="90">
                <template #default="scope">
                  <span>{{ scope.row.idx }}</span>
                </template>
              </vxe-column> -->
          <vxe-column field="商品排序" title="商品排序">
            <template v-slot="scope">
              <div>{{ scope.row.idx }}</div>
            </template>
          </vxe-column>
          <vxe-column field="商品名称" title="商品名称">
            <template v-slot="scope">
              <div>{{ scope.row.name }}</div>
            </template>
          </vxe-column>
          <vxe-column field="限购数量" title="限购数量">
            <template v-slot="scope">
              <div>{{ scope.row.limits }}</div>
            </template>
          </vxe-column>
          <vxe-column field="购买数量" title="购买数量">
            <template v-slot="scope">
              <div>{{ scope.row.num }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="标价">
                <template #default="scope">
                  <div>{{ scope.row.salePrice }}</div>
                </template>
              </vxe-column> -->
          <vxe-column field="实际售价" title="实际售价">
            <template v-slot="scope">
              <div>{{ scope.row.realPrice }}</div>
            </template>
          </vxe-column>
          <vxe-column field="是否热销" title="是否热销">
            <template v-slot="scope">
              <div v-if="scope.row.bestSell == 1">是</div>
              <div v-else>否</div>
            </template>
          </vxe-column>

          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <div v-if="scope.row.status == 1">上架</div>
              <div v-else>下架</div>
            </template>
          </vxe-column>
          <vxe-column field="自定义" title="自定义">
            <template v-slot="scope">
              <div v-if="scope.row.custome == 1">支持</div>
              <div v-else>不支持</div>
            </template>
          </vxe-column>
          <vxe-column field="商品描述" title="商品描述" width="250">
            <template v-slot="scope">
              <!-- <el-tooltip class="item" effect="dark" placement="left">
                        <div class="tooltip" slot="content">{{scope.row.description}}</div>
                        <span class="span">{{scope.row.description }}</span>
                    </el-tooltip> -->
              <div>{{ scope.row.shortDesc }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="200">
            <template v-slot="scope">
              <div>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="200" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                @click="detailsRow(scope.$index, scope.row)"
                ><el-icon><EditPen /></el-icon>&nbsp;编辑</el-button
              >
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                @click="settingState(scope.$index, scope.row)"
                ><el-icon><Setting /></el-icon>&nbsp;设置</el-button
              >
              <el-button
                link
                style="margin-left: 0px; color: red"
                @click="delState(scope.$index, scope.row)"
                ><el-icon><Delete /></el-icon>&nbsp;删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增栏目 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="700px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="商品排序" label-width="100px" prop="idx">
            <el-input
              v-model="formop.idx"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="商品名称" label-width="100px" prop="name">
            <el-input
              v-model="formop.name"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="限购数量" label-width="100px" prop="limits">
            <el-input-number
              class="input-w"
              v-model="formop.limits"
              :min="0"
              :max="999999999"
              label="限购数量"
            ></el-input-number>
            <!-- <el-input
                  v-model="formop.limits"
                  placeholder=""
                  autocomplete="off"
                ></el-input> -->
          </el-form-item>
          <el-form-item label="实际售价" label-width="100px" prop="realPrice">
            <el-input-number
              class="input-w"
              v-model="formop.realPrice"
              :min="0"
              :max="999999999"
              label="实际售价"
            ></el-input-number>
            <!-- <el-input
                  v-model="formop.realPrice"
                  placeholder=""
                  autocomplete="off"
                ></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="标价" label-width="100px" prop="salePrice">
                <el-input
                  v-model="formop.salePrice"
                  placeholder=""
                  autocomplete="off"
                ></el-input>
              </el-form-item> -->
          <el-form-item label="热销 " label-width="100px" prop="bestSell">
            <el-select v-model="formop.bestSell" placeholder="请选择">
              <el-option label="是" value="1"> </el-option>
              <el-option label="否" value="0"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="自定义 " label-width="100px" prop="custome">
            <el-select v-model="formop.custome" placeholder="请选择">
              <el-option label="支持" value="1"> </el-option>
              <el-option label="不支持" value="0"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上架 " label-width="100px" prop="status">
            <el-select v-model="formop.status" placeholder="请选择">
              <el-option label="上架" value="1"> </el-option>
              <el-option label="下架" value="0"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商品短描述" label-width="100px" prop="shortDesc">
            <el-input
              type="textarea"
              v-model="formop.shortDesc"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="商品长描述" label-width="100px">
            <el-input
              type="textarea"
              v-model="formop.description"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="商品小图片" label-width="100px" prop="simage">
            <el-upload
              class="upload-demo"
              :action="action"
              :headers="token"
              :file-list="fileLists"
              :on-preview="handlePreview"
              :on-remove="handleRemoves"
              :on-success="handlewqsSuccess"
              list-type="picture"
              :limit="1"
            >
              <el-button size="default" type="primary">上传图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="商品大图片" label-width="100px">
            <el-upload
              class="upload-demo"
              :action="action"
              :headers="token"
              :file-list="fileList"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :on-success="handleSuccess"
              list-type="picture"
              :limit="1"
            >
              <el-button size="default" type="primary">上传图片</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增标签商结束 -->

      <!-- 批量测试 -->
      <el-dialog
        title="批量测试"
        v-model="dialogBatchTest"
        :close-on-click-modal="false"
        width="700px"
      >
        <el-form
          :model="formoTest"
          :rules="rulesTest"
          ref="formoTest"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="手机号" label-width="80px" prop="mobile">
            <el-input v-model="formoTest.mobile" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="通道号" label-width="80px" prop="channelId">
            <el-input
              v-model="formoTest.channelId"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitBatchtest()"
              >提 交</el-button
            >
            <el-button @click="dialogBatchTest = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 批量测试 -->
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
// import TableTem from "../../../../publicComponents/TableTem";
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
// import Bus from '@/assets/js/bus.js'
import moment from 'moment'
export default {
  components: {
    FileUpload,
    ChannelView
  },
  name: 'SignatureNumMag',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      ChannelData: '', //传递通道值
      titleMap: {
        add: '添加商品',
        edit: '编辑商品',
      },
      time1: '',
      action: window.path.cpus + 'v3/file/upload',
      token: {},
      fileList: [],
      fileLists: [],
      items: [],
      itemss: [],
      urlObj: {},
      urlObjs: {},
      dialogImageUrl: '',
      invitationFirst: '',
      invitationPosterUrl: '',
      urlArr: [],
      urlArrs: [],
      signatureNum: '',
      status: 0,
      etxNum: '',
      flagPut: '', //用户名禁止状态 签名禁止状态
      dialogStatus: '', //新增编辑标题
      singName: [], //接受标签列表
      dialogFormVisible: false, //新增弹出框显示隐藏
      SignafileDialog: false,
      dialogBatchTest: false, //批量测试
      //查询表单
      formInline: {
        beginTime: '',
        endtime: '',
        name: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        beginTime: '',
        endtime: '',
        name: '',
        currentPage: 1,
        pageSize: 10,
      },
      //批量测试
      formoTest: {
        channelId: '',
        mobile: '',
      },
      rulesTest: {
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确在手机号',
          },
        ],
        channelId: [
          { required: true, message: '请输入通道号', trigger: 'change' },
          {
            pattern: /^([0-9]*[1-9][0-9]*)(,([0-9]*[1-9][0-9]*))*/,
            message: '含有非法字符（只能输入数字或逗号！）',
          },
        ],
      },
      //添加列表
      formop: {
        description: '',
        idx: '',
        bestSell: '',
        limits: null,
        realPrice: null,
        salePrice: '',
        status: '',
        name: '',
        custome: '',
        bimage: '',
        simage: '',
        shortDesc: '',
      },
      id: '', //列表行id
      selectId: '', //批量操作选中id
      tableRow: '', //当前行列表数据
      rules: {
        idx: [{ required: true, message: '请输入商品序号', trigger: 'change' }],
        name: [
          { required: true, message: '请输入商品名称', trigger: 'change' },
        ],
        limits: [
          { required: true, message: '请输入限购数量', trigger: 'change' },
        ],
        realPrice: [
          { required: true, message: '请输入实际售价', trigger: 'change' },
        ],
        salePrice: [
          { required: true, message: '请输入标价', trigger: 'change' },
        ],
        bestSell: [
          { required: true, message: '请选择是否热销', trigger: 'change' },
        ],
        custome: [
          { required: true, message: '请选择是否自定义', trigger: 'change' },
        ],
        status: [
          { required: true, message: '请选择是否上架', trigger: 'change' },
        ],
        bimage: [
          { required: true, message: '请上传商品大图', trigger: 'change' },
        ],
        simage: [
          { required: true, message: '请上传商品小图', trigger: 'change' },
        ],
        shortDesc: [
          { required: true, message: '请输入商品短描述', trigger: 'change' },
        ],
        description: [
          { required: true, message: '请输入商品长描述', trigger: 'change' },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
      Progressflag: false,
      failNum: '',
      failRow: '',
      fileStyle: {
        size: 245678943234,
        style: ['xlsx', 'xls'],
      },
      tip: '仅支持.xlsx .xls 等格式',
      del1: true, //关闭弹框时清空图片
    }
  },
  created() {
    this.token = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.shops + 'shopgoods/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleRemove(file, fileList) {
      // this.formop.bimage = ''
      console.log(file, fileList)
      this.urlArr = []
      if (fileList.length == 0) {
        this.formop.bimage = ''
      } else {
        fileList.forEach((item) => {
          this.urlArr.push(item.url)
          this.formop.bimage = this.urlArr.join(';')
        })
      }
    },
    handleRemoves(file, fileList) {
      // this.formop.simage = ''
      console.log(file, fileList)
      this.urlArrs = []
      if (fileList.length == 0) {
        this.formop.simage = ''
      } else {
        fileList.forEach((item) => {
          this.urlArrs.push(item.url)
          this.formop.simage = this.urlArrs.join(';')
        })
      }
    },
    handlePreview(file) {
      console.log(file)
    },
    handleSuccess(res, file, fileList) {
      //  this.formop.bimage = res.data.fullpath
      //  console.log(this.formop.bestSell,'1');
      this.urlArr.push(res.data.fullpath)
      //  console.log(this.urlArr.join(";"),'ll');
      this.formop.bimage = this.urlArr.join(';')
    },
    handlewqsSuccess(res, file, fileList) {
      this.urlArrs.push(res.data.fullpath)
      this.formop.simage = this.urlArrs.join(';')
      // console.log(this.formop.simage,'2');
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },

    hande: function (val) {
      if (val) {
        console.log(val, '11')
        //获取查询时间框的值
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endtime = this.moment(val[0]).format('YYYY-MM-DD ')
        // this.formInline.createEndTime = this.moment(val[1]).format("YYYY-MM-DD ")+"23:59:59";
      } else {
        this.formInline.beginTime = ''
        this.formInline.endtime = ''
        // this.formInline.createEndTime = '';
      }
    },
    // 监听子组件传递方法
    // save(val) {
    //   this.ChannelData = val;
    //   this.$refs.ChannelRef.ChannelClick();
    // },
    signaTure() {
      window.api.get(
        window.path.omcs + 'operatingsignature/sign/' + this.formop.signature,
        {},
        (res) => {
          this.signatureNum = res.data
        }
      )
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(val) {
      console.log(val)
      let selectId = []
      // let wordsCount = [];
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
        // wordsCount.push(val.records[i].wordsCount);
      }
      this.selectId = selectId.join(',') //批量操作选中id
      // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },
    Reload(val) {
      // console.log(val);
      //重置
      console.log(111)
      this.$refs.formInline.resetFields()
      this.formInline.beginTime = ''
      this.formInline.endtime = ''
      this.time1 = ''
      this.formInline.name = ''
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    settingState(index, row) {
      // console.log(row);
      // Bus.$emit('cateName',row.name)
      this.$router.push({
        path: '/ShopSetting',
        query: {
          goodsId: row.id,
          goodsname: row.name,
        },
      })
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.post(window.path.shops + 'shopgoods', this.formop, (res) => {
              if (res.code == 400) {
                this.$message({
                  message: res.msg,
                  type: 'warning',
                })
              } else {
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            })
          } else {
            //编辑
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.shops + 'shopgoods',
              this.formop,
              (res) => {
                if (res.code == 400) {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'warning',
                  // })
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'success',
                  // })
                }
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.flagPut = false
      this.dialogStatus = 'add'
    },

    delAll(val) {
      //批量删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.shops + 'shopgoods/' + val.id,
        {
          ids: this.selectId,
        },
        () => {
          this.gettableLIst()
        }
      )
    },

    detailsRow(index, val) {
      console.log(val, 'lll')
      this.fileList = []
      this.urlArr = []
      this.fileLists = []
      this.urlArrs = []
      if (val.bimage == 'null' || val.bimage == '') {
        console.log(111111111111)
        this.formop.bimage = ''
        this.fileList = []
        this.urlObj = {}
      } else {
        this.items = val.bimage.split(';')
        this.items.forEach((iten) => {
          this.urlObj.url = window.path.imgU + iten
          this.urlArr.push(iten)
          this.fileList.push(this.urlObj)
          this.urlObj = {}
        })
      }
      if (val.simage == 'null' || val.simage == '') {
        this.formop.simage = ''
        this.fileLists = []
        this.urlObjs = {}
      } else {
        this.itemss = val.simage.split(';')
        this.itemss.forEach((iten) => {
          this.urlObjs.url = window.path.imgU + iten
          this.urlArrs.push(iten)
          this.fileLists.push(this.urlObjs)
          this.urlObjs = {}
        })
        // this.formop.simage = window.path.imgU + val.bimage
        // let obj = new Object()
        // obj.url = this.formop.bimage
        // this.fileList.push(obj)
      }
      //编辑
      this.dialogFormVisible = true
      this.flagPut = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.tableRow = val
      // this.fileList = val.bimage
      for (let i in val) {
        if (
          val[i] == null ||
          i == 'custome' ||
          i == 'bestSell' ||
          i == 'status'
        ) {
          val[i] += ''
        }
      }
      //   this.formop= val;
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })

      //   console.log(this.formop,'ll');
      //   console.log(this.formop,'ddd');
      // for(let i in this.formop){
      //     console.log(i,'l');
      // }
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确认执行此操作吗？',
        window.path.shops + 'shopgoods/' + val.id,
        {},
        (res) => {
          if (res.code == 400) {
            // this.$message({
            //   message: res.msg,
            //   type: 'warning',
            // })
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
          }
          this.gettableLIst()
        }
      )
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    // this.gettableLIst();
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep: true, //深度监听
    //   immediate: true,
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        // this.formop.idx = "";
        this.formop.name = ''
        this.formop.description = ''
        this.formop.bestSell = ''
        this.formop.limits = null
        this.formop.realPrice = null
        this.formop.salePrice = ''
        this.formop.status = ''
        this.formop.custome = ''
        this.formop.shortDesc = ''
        this.formop.bimage = ''
        this.formop.simage = ''
        this.formop.idx = ''
        this.$refs.formop.resetFields()
      }
    },
    'formop.bimage'(val) {
      // console.log(val，‘);
      if (val == '') {
        this.formop.bimage = ''
      }
    },
    'formop.simage'(val) {
      // console.log(val，‘);
      if (val == '') {
        this.formop.simage = ''
      }
    },
    SignafileDialog(val) {
      this.del1 = true
      if (val == false) {
        this.del1 = false
        this.failNum = ''
        this.failRow = ''
      }
    },
    dialogBatchTest(val) {
      if (val == false) {
        this.$refs.formoTest.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.tooltip {
  width: 300px;
}
.span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>