<template>
    <div style="background: #fff; padding: 15px">
        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="通道ID">
                    <el-input class="input-w" v-model="searchForm.channelId" placeholder="请输入通道ID" clearable></el-input>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="input-w">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="开启" :value="0"></el-option>
                        <el-option label="停用" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 工具栏 -->
        <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
                <el-button type="primary" @click="handleAdd">添加</el-button>
            </template>
        </vxe-toolbar>

        <!-- 数据表格 -->
        <vxe-table ref="restartTableRef" id="channelRestartPlanTable" border stripe show-header-overflow
            :custom-config="customConfig" :column-config="{ resizable: true }" :row-config="{ isHover: true }"
            min-height="1" v-loading="loading" element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;" :data="tableData">
            <!-- <vxe-column field="id" title="ID" min-width="80"></vxe-column> -->
            <vxe-column field="channelId" title="通道ID" min-width="120"></vxe-column>
            <vxe-column field="beginTime" title="开始时间" min-width="160">
                <template #default="{ row }">
                    {{ row.beginTime ? moment(row.beginTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="endTime" title="结束时间" min-width="160">
                <template #default="{ row }">
                    {{ row.endTime ? moment(row.endTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="nextTime" title="下次执行时间" min-width="160">
                <template #default="{ row }">
                    {{ row.nextTime ? moment(row.nextTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="connectNum" title="连接数" min-width="100"></vxe-column>
            <vxe-column field="maxNum" title="最大连接数" min-width="120"></vxe-column>
            <vxe-column field="status" title="状态" min-width="100">
                <template #default="{ row }">
                    <el-tag :type="row.status === 0 ? 'success' : 'danger'" size="small">
                        {{ row.status === 0 ? '开启' : '停用' }}
                    </el-tag>
                </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" fixed="right" width="200">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
            </vxe-column>
        </vxe-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pagination.currentPage" :page-size="pagination.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
        </div>

        <!-- 弹窗表单 -->
        <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑通道重启计划' : '添加通道重启计划'" width="700px"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <!-- 新增时显示运营商选择和通道多选 -->
                <template v-if="!isEdit">
                    <el-form-item label="运营商" prop="operatorType">
                        <el-select v-model="form.operatorType" placeholder="请选择运营商" @change="handleOperatorChange" style="width: 100%">
                            <el-option label="移动" :value="1"></el-option>
                            <el-option label="联通" :value="2"></el-option>
                            <el-option label="电信" :value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="通道选择" prop="channelIds">
                        <div class="channel-select-container">
                            <div class="channel-select-header">
                                <el-checkbox
                                    v-model="selectAllChannels"
                                    :indeterminate="isIndeterminate"
                                    @change="handleSelectAllChange"
                                    :disabled="!form.operatorType || channelLoading || channelList.length === 0">
                                    全选通道
                                </el-checkbox>
                                <span class="channel-count" v-if="channelList.length > 0">
                                    (已选择 {{ form.channelIds.length }} / {{ channelList.length }} 个通道)
                                </span>
                            </div>
                            <el-select
                                v-model="form.channelIds"
                                multiple
                                placeholder="请先选择运营商，然后选择通道"
                                style="width: 100%"
                                :loading="channelLoading"
                                :disabled="!form.operatorType"
                                @change="handleChannelSelectionChange">
                                <el-option
                                    v-for="channel in channelList"
                                    :key="channel.id"
                                    :label="`${channel.channelName}(${channel.channelCode})`"
                                    :value="channel.channelCode">
                                </el-option>
                            </el-select>
                        </div>
                    </el-form-item>
                </template>
                <!-- 编辑时显示单个通道ID -->
                <el-form-item v-else label="通道ID" prop="channelId">
                    <el-input v-model="form.channelId" placeholder="请输入通道ID" disabled></el-input>
                </el-form-item>
                <el-form-item label="开始日期" prop="beginTime">
                    <el-date-picker
                        v-model="form.beginTime"
                        type="datetime"
                        placeholder="选择开始日期"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束日期" prop="endTime">
                    <el-date-picker
                        v-model="form.endTime"
                        type="datetime"
                        placeholder="选择结束日期"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="下次执行时间" prop="nextTime">
                    <el-date-picker
                        v-model="form.nextTime"
                        type="datetime"
                        placeholder="选择下次执行时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="连接数" prop="connectNum">
                    <el-input-number v-model="form.connectNum" :min="0" placeholder="请输入连接数" style="width: 100%"></el-input-number>
                </el-form-item>
                <el-form-item label="最大连流速" prop="maxNum">
                    <el-input-number v-model="form.maxNum" :min="0" placeholder="请输入最大连接数" style="width: 100%"></el-input-number>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio :label="0">开启</el-radio>
                        <el-radio :label="1">停用</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import moment from 'moment';

// 表格引用
const restartTableRef = ref(null);
const toolbarRef = ref(null);

// 表格配置
const customConfig = {
    storage: true
};

// 搜索表单
const searchForm = reactive({
    channelId: '',
    status: ''
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 通道相关数据
const channelList = ref([]);
const channelLoading = ref(false);

// 全选相关数据
const selectAllChannels = ref(false);
const isIndeterminate = ref(false);

// 分页
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
});

// 弹窗表单
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const form = reactive({
    id: 0,
    channelId: '',
    channelIds: [], // 新增：多选通道ID数组
    operatorType: '', // 新增：运营商类型
    beginTime: '',
    endTime: '',
    nextTime: '',
    connectNum: 0,
    maxNum: 0,
    status: 0
});

// 表单验证规则
const rules = computed(() => {
    const baseRules = {
        beginTime: [
            { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
            { required: true, message: '请选择结束时间', trigger: 'change' }
        ],
        nextTime: [
            { required: true, message: '请选择下次执行时间', trigger: 'change' }
        ],
        connectNum: [
            { required: true, message: '请输入连接数', trigger: 'blur' }
        ],
        maxNum: [
            { required: true, message: '请输入最大连接数', trigger: 'blur' }
        ],
        status: [
            { required: true, message: '请选择状态', trigger: 'change' }
        ]
    };

    // 新增时需要验证运营商和通道选择
    if (!isEdit.value) {
        baseRules.operatorType = [
            { required: true, message: '请选择运营商', trigger: 'change' }
        ];
        baseRules.channelIds = [
            { required: true, message: '请选择通道', trigger: 'change' },
            { type: 'array', min: 1, message: '请至少选择一个通道', trigger: 'change' }
        ];
    } else {
        // 编辑时需要验证通道ID
        baseRules.channelId = [
            { required: true, message: '请输入通道ID', trigger: 'blur' }
        ];
    }

    return baseRules;
});

// 获取列表数据
const getRestartPlanList = async () => {
    loading.value = true;
    try {
        const params = {
            current: pagination.currentPage,
            size: pagination.pageSize,
            ...searchForm
        };

        window.api.post(window.path.omcs + 'operatingchannelrestarttask/page', params, (res) => {
            if (res.code == 200) {
                tableData.value = res.data.records;
                pagination.total = res.data.total;
                loading.value = false;
            } else {
                ElMessage.error(res.msg);
                loading.value = false;
            }
        }, (error) => {
            loading.value = false;
            ElMessage.error('获取列表失败，请稍后重试');
        });
    } catch (error) {
        console.error('获取列表失败:', error);
        ElMessage.error('获取列表失败，请稍后重试');
        loading.value = false;
    }
};

// 查询
const handleSearch = () => {
    pagination.currentPage = 1;
    getRestartPlanList();
};

// 重置查询条件
const resetSearch = () => {
    searchForm.channelId = '';
    searchForm.status = '';
    handleSearch();
};

// 获取通道列表
const getChannelList = async (operatorType) => {
    if (!operatorType) return;

    channelLoading.value = true;
    try {
        // 这里需要获取productId，可能需要从其他地方获取或者作为参数传入
        // 暂时使用operatorType作为productId
        const productId = 1;

        window.api.get(window.path.omcs + `v3/operatingchannelgroup/channelList?operatorType=${operatorType}&productId=${productId}`, {}, (res) => {
            if (res.code == 200) {
                channelList.value = res.data || [];
                // 重置全选状态
                selectAllChannels.value = false;
                isIndeterminate.value = false;
                channelLoading.value = false;
            } else {
                ElMessage.error(res.msg || '获取通道列表失败');
                channelLoading.value = false;
            }
        }, (error) => {
            console.error('获取通道列表失败:', error);
            ElMessage.error('获取通道列表失败，请稍后重试');
            channelLoading.value = false;
        });
    } catch (error) {
        console.error('获取通道列表失败:', error);
        ElMessage.error('获取通道列表失败，请稍后重试');
        channelLoading.value = false;
    }
};

// 运营商变更处理
const handleOperatorChange = (operatorType) => {
    // 清空之前选择的通道
    form.channelIds = [];
    channelList.value = [];
    // 重置全选状态
    selectAllChannels.value = false;
    isIndeterminate.value = false;

    if (operatorType) {
        getChannelList(operatorType);
    }
};

// 全选/取消全选处理
const handleSelectAllChange = (checked) => {
    if (checked) {
        // 全选：选择所有通道
        form.channelIds = channelList.value.map(channel => channel.channelCode);
    } else {
        // 取消全选：清空选择
        form.channelIds = [];
    }
    isIndeterminate.value = false;
};

// 通道选择变更处理
const handleChannelSelectionChange = (selectedChannels) => {
    const totalChannels = channelList.value.length;
    const selectedCount = selectedChannels.length;

    if (selectedCount === 0) {
        // 没有选择任何通道
        selectAllChannels.value = false;
        isIndeterminate.value = false;
    } else if (selectedCount === totalChannels) {
        // 选择了所有通道
        selectAllChannels.value = true;
        isIndeterminate.value = false;
    } else {
        // 部分选择
        selectAllChannels.value = false;
        isIndeterminate.value = true;
    }
};

// 处理页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    getRestartPlanList();
};

// 处理每页显示数量变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    getRestartPlanList();
};

// 添加
const handleAdd = () => {
    isEdit.value = false;
    resetForm();
    dialogVisible.value = true;
};

// 编辑
const handleEdit = (row) => {
    isEdit.value = true;

    // 清除之前的表单验证状态
    if (formRef.value) {
        formRef.value.clearValidate();
    }

    // 填充表单数据
    form.id = row.id;
    form.channelId = row.channelId;
    // 格式化时间字段，确保正确回显
    form.beginTime = row.beginTime ? moment(row.beginTime).format('YYYY-MM-DD HH:mm:ss') : '';
    form.endTime = row.endTime ? moment(row.endTime).format('YYYY-MM-DD HH:mm:ss') : '';
    form.nextTime = row.nextTime ? moment(row.nextTime).format('YYYY-MM-DD HH:mm:ss') : '';
    form.connectNum = row.connectNum || 0;
    form.maxNum = row.maxNum || 0;
    form.status = row.status !== undefined ? row.status : 0;

    // 打开对话框
    dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                if (isEdit.value) {
                    // 编辑操作使用PUT方法
                    const submitData = {
                        id: form.id,
                        channelId: form.channelId,
                        beginTime: form.beginTime,
                        endTime: form.endTime,
                        nextTime: form.nextTime,
                        connectNum: form.connectNum,
                        maxNum: form.maxNum,
                        status: form.status
                    };

                    window.api.put(window.path.omcs + 'operatingchannelrestarttask', submitData, (res) => {
                        if (res.code == 200) {
                            getRestartPlanList();
                            ElMessage.success('修改成功');
                            dialogVisible.value = false;
                        } else {
                            ElMessage.error(res.msg);
                        }
                    }, (error) => {
                        console.error('修改失败:', error);
                        ElMessage.error('修改失败，请稍后重试');
                    });
                } else {
                    // 新增操作：将多个通道ID以逗号分隔的形式提交
                    const submitData = {
                        channelId: form.channelIds.join(','), // 将数组转换为逗号分隔的字符串
                        beginTime: form.beginTime,
                        endTime: form.endTime,
                        nextTime: form.nextTime,
                        connectNum: form.connectNum,
                        maxNum: form.maxNum,
                        status: form.status
                    };

                    window.api.post(window.path.omcs + 'operatingchannelrestarttask', submitData, (res) => {
                        if (res.code == 200) {
                            getRestartPlanList();
                            ElMessage.success(`成功创建通道重启计划`);
                            dialogVisible.value = false;
                        } else {
                            ElMessage.error(res.msg);
                        }
                    }, (error) => {
                        console.error('添加失败:', error);
                        ElMessage.error('添加失败，请稍后重试');
                    });
                }
            } catch (error) {
                console.error(isEdit.value ? '修改失败:' : '添加失败:', error);
                ElMessage.error(isEdit.value ? '修改失败，请稍后重试' : '添加失败，请稍后重试');
            }
        } else {
            return false;
        }
    });
};

// 删除
const handleDelete = async (row) => {
    try {
        await ElMessageBox.confirm('确定要删除该通道重启计划吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        window.api.delete(window.path.omcs + 'operatingchannelrestarttask/' + row.id, {}, () => {
            ElMessage.success('删除成功');
            getRestartPlanList();
        }, (error) => {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        });
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        }
    }
};

// 重置表单
const resetForm = () => {
    // 重置表单数据到默认值
    form.id = 0;
    form.channelId = '';
    form.channelIds = [];
    form.operatorType = '';
    form.beginTime = '';
    form.endTime = '';
    form.nextTime = '';
    form.connectNum = 0;
    form.maxNum = 0;
    form.status = 0;

    // 清空通道列表和全选状态
    channelList.value = [];
    selectAllChannels.value = false;
    isIndeterminate.value = false;
};

// 监听对话框关闭事件
watch(dialogVisible, (newValue) => {
    if (!newValue) {
        // 对话框关闭时重置表单
        resetForm();
        // 清除表单验证状态
        if (formRef.value) {
            formRef.value.clearValidate();
        }
    }
});

// 页面加载时获取数据
onMounted(() => {
    const $table = restartTableRef.value
    const $toolbar = toolbarRef.value
    if ($table && $toolbar) {
        $table.connect($toolbar)
    }
    getRestartPlanList();
});
</script>

<style scoped>
.search-container {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 4px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.input-w {
    width: 200px;
}

/* 通道选择容器样式 */
.channel-select-container {
    width: 100%;
}

.channel-select-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}

.channel-count {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
}
</style>
