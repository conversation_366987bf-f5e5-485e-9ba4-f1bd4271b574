import * as types from './mutation-types'

export const add = async ({ commit, state }, { value }) => {
  commit(types.TEST, value)
}
export const saveInfo = async (
  { commit, state },
  { userName, userId, roleId, portraitUrl, realName }
) => {
  commit(types.SET_USERNAME, userName)
  commit(types.SET_USERID, userId)
  commit(types.SET_ROLEID, roleId)
  commit(types.SET_PORTRAITURL, portraitUrl)
  commit(types.SET_REALNAME, realName)
  // console.log(realName,'LLLLL');
}
