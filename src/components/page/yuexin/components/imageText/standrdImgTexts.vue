<template>
  <div v-if="tag == 'view'" style="width: 312px; margin: 0 auto">
    <div
      :class="view == 'view' ? 'preview-container-view' : 'preview-container'"
    >
      <div class="headPreview">
        <img
          v-for="(item, index) in imageList"
          :src="item.img"
          alt=""
          :class="imageCount == index ? 'firstImg actionPreview' : ''"
          @click="handelLarge(index)"
        />
      </div>
      <div class="preview-container-inside" style="left: 30px">
        <div class="preview">
          <div class="preview-phone">
            <div class="preview_edit">
              <div class="view_img">
                <img
                  v-if="
                    ssoContentFrom.firstFrom.imgcontents.srcMaterialId == ''
                  "
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  v-else
                  class="img"
                  :src="ssoContentFrom.firstFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
                <div class="title_mode">
                  <div class="rich-text">
                    {{ ssoContentFrom.firstFrom.titlecontents.content }}
                  </div>
                </div>
              </div>
              <div
                v-if="number == '1' || number == '2' || number == '3'"
                class="view_content"
              >
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.secondFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.secondFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.secondFrom.titlecontents.content }}
                </div>
              </div>
              <div v-if="number == '2' || number == '3'" class="view_content">
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.thirdForm.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.thirdForm.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.thirdForm.titlecontents.content }}
                </div>
              </div>
              <div v-if="number == '3'" class="view_content">
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.fourthFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.fourthFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.fourthFrom.titlecontents.content }}
                </div>
              </div>
            </div>
          </div>
          <div class="xiaomi">
            <div class="preview_edit">
              <div class="view_img">
                <img
                  v-if="
                    ssoContentFrom.firstFrom.imgcontents.srcMaterialId == ''
                  "
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  v-else
                  class="img"
                  :src="ssoContentFrom.firstFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
                <div class="title_mode">
                  <div class="rich-text">
                    {{ ssoContentFrom.firstFrom.titlecontents.content }}
                  </div>
                </div>
              </div>
              <div
                v-if="number == '1' || number == '2' || number == '3'"
                class="view_content"
              >
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.secondFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.secondFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.secondFrom.titlecontents.content }}
                </div>
              </div>
              <div v-if="number == '2' || number == '3'" class="view_content">
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.thirdForm.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.thirdForm.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.thirdForm.titlecontents.content }}
                </div>
              </div>
              <div v-if="number == '3'" class="view_content">
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.fourthFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.fourthFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.fourthFrom.titlecontents.content }}
                </div>
              </div>
            </div>
          </div>
          <div class="preview-phone">
            <div class="preview_edit">
              <div class="view_img">
                <img
                  v-if="
                    ssoContentFrom.firstFrom.imgcontents.srcMaterialId == ''
                  "
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  v-else
                  class="img"
                  :src="ssoContentFrom.firstFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
                <div class="title_mode">
                  <div class="rich-text">
                    {{ ssoContentFrom.firstFrom.titlecontents.content }}
                  </div>
                </div>
              </div>
              <div
                v-if="number == '1' || number == '2' || number == '3'"
                class="view_content"
              >
                <div class="contents">
                  {{ ssoContentFrom.secondFrom.titlecontents.content }}
                </div>
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.secondFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.secondFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
              </div>
              <div v-if="number == '2' || number == '3'" class="view_content">
                <div class="contents">
                  {{ ssoContentFrom.thirdForm.titlecontents.content }}
                </div>
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.thirdForm.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.thirdForm.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
              </div>
              <div v-if="number == '3'" class="view_content">
                <div class="contents">
                  {{ ssoContentFrom.fourthFrom.titlecontents.content }}
                </div>
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.fourthFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.fourthFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="preview-phone">
            <div class="preview_edit">
              <div class="view_img">
                <img
                  v-if="
                    ssoContentFrom.firstFrom.imgcontents.srcMaterialId == ''
                  "
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  v-else
                  class="img"
                  :src="ssoContentFrom.firstFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
                <div class="title_mode">
                  <div class="rich-text">
                    {{ ssoContentFrom.firstFrom.titlecontents.content }}
                  </div>
                </div>
              </div>
              <div
                v-if="number == '1' || number == '2' || number == '3'"
                class="view_content"
              >
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.secondFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.secondFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.secondFrom.titlecontents.content }}
                </div>
              </div>
              <div v-if="number == '2' || number == '3'" class="view_content">
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.thirdForm.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.thirdForm.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.thirdForm.titlecontents.content }}
                </div>
              </div>
              <div v-if="number == '3'" class="view_content">
                <div class="images">
                  <img
                    class="img"
                    v-if="ssoContentFrom.fourthFrom.imgcontents.srcMaterialId"
                    :src="ssoContentFrom.fourthFrom.imgcontents.srcMaterialUrl"
                    alt=""
                  />
                  <img
                    class="img"
                    v-else
                    src="@/assets/images/ssobg.png"
                    alt=""
                  />
                </div>
                <div class="contents">
                  {{ ssoContentFrom.fourthFrom.titlecontents.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else>1</div>
</template>

<script>
export default {
  props: {
    list: {
      type: Object,
      default: () => {},
    },
    view: {
      type: String,
      default: '',
    },
    tag: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      number: 1,
      imageList: [
        {
          img: require('@/assets/images/huaweiLarge.png'),
        },
        {
          img: require('@/assets/images/xiaomiLarge.png'),
        },
        {
          img: require('@/assets/images/oppoLarge.png'),
        },
        {
          img: require('@/assets/images/vivoLarge.png'),
        },
      ],
      imageCount: 0,
      ssoContentFrom: {
        firstFrom: {
          imgcontents: {
            type: 'image', //资源类型
            content: '', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            srcMaterialType: '', //素材类型
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 1, //位置序号
          },
          titlecontents: {
            type: 'text', //资源类型
            content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 2, //位置序号
          },
        },
        secondFrom: {
          imgcontents: {
            type: 'image', //资源类型
            content: '', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            srcMaterialType: '', //素材类型
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 3, //位置序号
          },
          titlecontents: {
            type: 'text', //资源类型
            content: '编辑文本，最多显示26个字。', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 4, //位置序号
          },
        },
        thirdForm: {
          imgcontents: {
            type: 'image', //资源类型
            content: '', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            srcMaterialType: '', //素材类型
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 5, //位置序号
          },
          titlecontents: {
            type: 'text', //资源类型
            content: '编辑文本，最多显示26个字。', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 6, //位置序号
          },
        },
        fourthFrom: {
          imgcontents: {
            type: 'image', //资源类型
            content: '', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            srcMaterialType: '', //素材类型
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 7, //位置序号
          },
          titlecontents: {
            type: 'text', //资源类型
            content: '编辑文本，最多显示26个字。', //文本内容
            srcMaterialId: '', //素材ID
            srcMaterialUrl: '', //素材URL
            coverMaterialId: '', //封面素材ID
            coverMaterialUrl: '', //封面URL
            textTitle: false, //是否为文本标题
            visible: '', //是否可见
            currencyDisplay: '', //	是否显示货币符号
            actionType: '', //事件类型
            action: {
              //事件参数
              target: '',
              merchantName: '',
            },
            positionNumber: 8, //位置序号
          },
        },
      },
    }
  },
  methods: {
    handelLarge(index) {
      this.imageCount = index
      let byClass = document.getElementsByClassName(
        'preview-container-inside'
      )[0].style
      if (index == 0) {
        byClass.left = '30px'
      } else if (index == 1) {
        byClass.left = '-243px'
      } else if (index == 2) {
        byClass.left = '-537px'
      } else {
        byClass.left = '-824px'
      }
    },
  },

  watch: {
    list: {
      handler(val) {
        var arr = Object.keys(val)
        if (arr.length) {
          if (val.pages[0].contents.length == 4) {
            number.value = 1
            ssoContentFrom.firstFrom.imgcontents = val.pages[0].contents[0]
            ssoContentFrom.firstFrom.titlecontents = val.pages[0].contents[1]
            ssoContentFrom.secondFrom.imgcontents = val.pages[0].contents[2]
            ssoContentFrom.secondFrom.titlecontents = val.pages[0].contents[3]
          } else if (val.pages[0].contents.length == 6) {
            number.value = 2
            ssoContentFrom.firstFrom.imgcontents = val.pages[0].contents[0]
            ssoContentFrom.firstFrom.titlecontents = val.pages[0].contents[1]
            ssoContentFrom.secondFrom.imgcontents = val.pages[0].contents[2]
            ssoContentFrom.secondFrom.titlecontents = val.pages[0].contents[3]
            ssoContentFrom.thirdForm.imgcontents = val.pages[0].contents[4]
            ssoContentFrom.thirdForm.titlecontents = val.pages[0].contents[5]
          } else if (val.pages[0].contents.length == 8) {
            // console.log(111);
            number.value = 3
            ssoContentFrom.firstFrom.imgcontents = val.pages[0].contents[0]
            ssoContentFrom.firstFrom.titlecontents = val.pages[0].contents[1]
            ssoContentFrom.secondFrom.imgcontents = val.pages[0].contents[2]
            ssoContentFrom.secondFrom.titlecontents = val.pages[0].contents[3]
            ssoContentFrom.thirdForm.imgcontents = val.pages[0].contents[4]
            ssoContentFrom.thirdForm.titlecontents = val.pages[0].contents[5]
            ssoContentFrom.fourthFrom.imgcontents = val.pages[0].contents[6]
            ssoContentFrom.fourthFrom.titlecontents = val.pages[0].contents[7]
          }
          // this.options = val.options
          // this.ruleForm.templateId = val.id
        }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
}
</script>

<style scoped>
.preview-container {
  width: 312px;
  padding-right: 16px;
  border-right: 1px solid #dcdfe6;
  overflow: hidden;
  display: inline-block;
}
.preview-container-view {
  width: 312px;
  padding-right: 16px;
  overflow: hidden;
  display: inline-block;
}
.headPreview {
  display: flex;
  justify-content: space-evenly;
  padding-top: 10px;
  box-sizing: border-box;
}
.headPreview img {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.2s ease 0s;
}
.preview {
  display: inline-block;
  vertical-align: top;
  box-sizing: border-box;
  border-right: none !important;
}
.actionPreview {
  transform: scale(1.7);
  opacity: 1;
}
.preview-container-inside {
  position: relative;
  width: 1548px;
  left: 0;
  transition: left 0.6s;
}
.preview-phone {
  position: relative;
  display: inline-block;
  width: 295px;
  height: 573px;
  background-image: url(../../../../../assets/images/phone-huawei.png);
  background-repeat: no-repeat;
  background-position: 10%;
}
.xiaomi {
  position: relative;
  display: inline-block;
  width: 271px;
  height: 573px;
  background-image: url(../../../../../assets/images/phone-xiaomi.png);
  background-repeat: no-repeat;
  background-position: 10%;
}
.preview_edit {
  width: 342px;
  background: rgb(242, 242, 242);
  border-radius: 10px;
  position: absolute;
  -webkit-transform-origin-x: 0;
  -webkit-transform: scale(0.6);
  left: 0;
  top: 0;
  margin-left: 20px;
  margin-top: 17px;
}
.preview_view_img {
  width: 342px;
  height: 192px;
}
.preview_view_content {
  width: 200px;
}
.preview_view_title {
  width: 300px;
  height: 26px;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  text-decoration: none;
  line-height: 1.5;
  letter-spacing: 0px;
  color: rgb(0, 0, 0);
  text-align: left;
  background-color: transparent;
  opacity: 1;
  box-sizing: border-box;
  margin-top: 25px;
  margin-left: 20px;
}
.preview_view_text {
  width: 300px;
  height: 62px;
  margin-left: 20px;
  font-size: 13px;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  line-height: 1.5;
  letter-spacing: 0px;
  color: rgb(0, 0, 0);
  text-align: left;
  background-color: transparent;
  opacity: 1;
  box-sizing: border-box;
}
.preview_btn {
  text-align: center;
  width: 307px;
  margin: 0 auto;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background-color: #007dff;
  color: #fff;
  font-size: 17px;
  margin-top: 7px;
}
.img {
  width: 100%;
  height: 100%;
}
.images {
  width: 57px;
  height: 57px;
}
.view_content {
  width: 100%;
  height: 57px;
  display: flex;
  padding: 10px;
}
.view_img {
  width: 100%;
  height: 193px;
  position: relative;
}
.title_mode {
  position: absolute;
  top: 140px;
  left: 0px;
  width: 342px;
  height: 54px;
  transform: rotate(0deg);
  z-index: 2;
}
.rich-text {
  height: 100%;
  font-size: 17px;
  font-family: 微软雅黑;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  line-height: 1.5;
  letter-spacing: 0px;
  color: rgb(255, 255, 255);
  text-align: left;
  background: rgba(0, 0, 0, 0.1);
  opacity: 1;
  padding: 0px 14px;
  box-sizing: border-box;
  white-space: pre-wrap;
}
.contents {
  width: 232px;
  height: 57px;
  line-height: 1.9;
  font-size: 14px;
  background-color: transparent;
  color: rgb(0, 0, 0);
  margin-left: 20px;
}
</style>
