<template>
  <div>
    <!-- <el-select v-model="value" @change="handChildLabel" placeholder="请选择">
                <el-option v-for="item in dynamicTags" :key="item.id" :label="item.label" :value="item.label">
                    <span>{{ item.label }}</span>
                    <span style="float: right;" @click.stop="handleClose(item.id)">
                        <div style="padding-left: 5px;padding-right: 5px">x</div>
                    </span>
                </el-option>
            </el-select> -->
    <el-tag
:disable-transitions="true"
      v-for="(tag, index) in tagsList"
      :key="index + 'v'"
      closable
      :="false"
      @close="handleClose(index)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      maxlength="20"
      ref="saveTagInput"
      size="default"
      @keyup.enter="handleInputConfirm"
      @blur="handleInputConfirm"
      placeholder="输入标签名"
    >
    </el-input>
    <!-- <span v-if="inputVisible" style="color:red;font-size:12px;margin-left:10px">标签最多支持20个字符！</span> -->
    <el-button v-else class="button-new-tag" size="default" @click="showInput"
      >+ 添加标签</el-button
    >
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
export default {
  data() {
    return {
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      tagsList: [],
      value: '',
    }
  },
  props: ['t_data'],
  created() {
    //   this.getLabel();
  },
  methods: {
    // getLabel() {
    //     window.api.get(window.path.cpus + "consumerlabel/list", {}, (res) => {
    //         this.dynamicTags = res.data;

    //     });
    // },
    handleClose(tag) {
      this.tagsList.splice(tag, 1)
      $emit(this, 'childLabelEvent', this.tagsList)
      // window.api.delete(window.path.cpus + "consumerlabel/" + tag, {}, (res) => {
      //     this.getLabel();
      //     this.value = ""
      // });
    },
    handChildLabel(e) {
      // console.log(e, 'e');
      // this.$emit('childLabelEvent', e)
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      let inputValue = this.inputValue
      if (inputValue) {
        this.tagsList.push(inputValue)
        // this.value = inputValue
        $emit(this, 'childLabelEvent', this.tagsList)
      }
      this.inputVisible = false
    },
  },
  watch: {
    inputVisible(val) {
      if (!val) {
        this.inputValue = ''
        // this.tagsList = [];
      }
    },
    t_data: {
      handler(val) {
        this.tagsList = val
      },
      deep: true,
      immediate: true, // 初次监听即执行
    },
  },
  emits: ['childLabelEvent'],
}
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 180px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
