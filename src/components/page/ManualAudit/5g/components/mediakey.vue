<template>
  <div>
    <div>
      <div class="view_card">
        <div class="navbar">
          <div class="chatbot_logo_t">
            <!-- <div class="logo_t">
                <img v-if="lists.logoUrl" :src="lists.logoUrl" alt="" style="width:100%;height:100%;border-radius: 50%;">
              </div>
              <div class="chatbot_name_t">{{lists.chatbotName}}</div> -->
          </div>
        </div>
        <div class="card_content">
          <div class="card_item">
            <div class="card_media">
              <!-- <div class="play_active">
                <img
                  v-if="data.fileType == '2' || data.fileType == '3'"
                  src="../../../../../assets/images/paly.png"
                  alt=""
                  class="img"
                />
                <img
                  v-if="data.fileType == '1'"
                  src="../../../../../assets/images/photoIcon.png"
                  alt=""
                  class="img"
                />
              </div> -->
              <div class="media_icon">
                <img
                  v-if="contentFrom.templateContent.fileType == '1'"
                  src="../../../../../assets/images/photoIcon.png"
                  alt=""
                />
                <img
                  v-if="contentFrom.templateContent.fileType == '2'"
                  src="../../../../../assets/images/videoIcon.png"
                  alt=""
                />
                <img
                  v-if="contentFrom.templateContent.fileType == '3'"
                  src="../../../../../assets/images/audioIcon.png"
                  alt=""
                />
              </div>
              <img
                v-if="contentFrom.templateContent.fileType == '1'"
                :src="contentFrom.templateContent.url"
                alt=""
                class="img"
              />
              <img
                v-if="
                  (contentFrom.templateContent.fileType == '2' ||
                    contentFrom.templateContent.fileType == '3') &&
                  contentFrom.templateContent.thumbnailUrl
                "
                :src="contentFrom.templateContent.thumbnailUrl"
                alt=""
                class="img"
              />
              <img
                v-if="
                  (contentFrom.templateContent.fileType == '2' ||
                    contentFrom.templateContent.fileType == '3') &&
                  !contentFrom.templateContent.thumbnailUrl
                "
                src="../../../../../assets/images/zzbg.png"
                class="img"
                alt=""
              />
            </div>
          </div>
        </div>
        <div style="display: flex">
          <div
            class="temp_menu_view"
            v-for="(item, index) in contentFrom.templateContent.buttons"
            :key="index + 'a'"
          >
            <el-button>{{ item.name }}</el-button>
          </div>
        </div>
        <div class="view_footer"></div>
      </div>
    </div>

    <span style="display: inline-block; margin: 10px 0">悬浮菜单详情：</span>
    <div
      style="
        width: 100%;
        display: flex;
        background: #eee;
        flex-wrap: wrap;
        flex-shrink: 0;
      "
    >
      <div
        class="btn_item"
        v-for="(ite, ix) in contentFrom.templateContent.buttons"
        :key="ix"
      >
        <div v-if="ite.functionCode == 'suggestion'">
          <p>
            <span>自动回复</span> - <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>关键字</span>：<span>{{ ite.content.keyWord }}</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'url'">
          <p>
            <span>打开链接</span> - <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>链接地址</span>：<span>{{ ite.content.link }}</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'app'">
          <p>
            <span>打开APP</span> - <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>链接地址</span>：<span>{{ ite.content.link }}</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'dial'">
          <p>
            <span>拨打电话</span> - <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>被叫号码</span>：<span>{{ ite.content.called }}</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'send'">
          <p>
            <span>发送消息</span> - <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>接送号码</span>：<span>{{ ite.content.receiveNumber }}</span>
          </p>
          <p>
            <span>类型</span>：<span v-if="ite.content.receiveType == 1"
              >文本</span
            ><span v-else-if="ite.content.receiveType == 2">视频</span
            ><span v-else>音频</span>
          </p>
          <p>
            <span>内容</span>：<span v-if="ite.content.receiveType == 1">{{
              ite.content.receiveContent
            }}</span
            ><span v-else>由用户在终端设备上自行录制</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'show_location'">
          <p>
            <span>展示地理位置</span> -
            <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>经度</span>：<span>{{ ite.content.longitude }}</span>
          </p>
          <p>
            <span>纬度</span>：<span>{{ ite.content.latitude }}</span>
          </p>
          <p>
            <span>位置标签</span>：<span>{{ ite.content.locationName }}</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'search_location'">
          <p>
            <span>搜索地理位置</span> -
            <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>查询关键词</span>：<span>{{ ite.content.searchKey }}</span>
          </p>
        </div>
        <div v-if="ite.functionCode == 'upcoming'">
          <p>
            <span>创建待办事项</span> -
            <span class="btn_name">{{ ite.name }}</span>
          </p>
          <p>
            <span>起始时间</span>：<span v-if="ite.content.upcomingStartTime">{{
              moment(ite.content.upcomingStartTime).format(
                'YYYY-MM-DD HH:mm:ss'
              )
            }}</span>
          </p>
          <p>
            <span>终止时间</span>：<span v-if="ite.content.upcomingEndTime">{{
              moment(ite.content.upcomingEndTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </p>
          <p>
            <span>标题</span>：<span>{{ ite.content.upcomingTitle }}</span>
          </p>
          <p>
            <span>描述</span>：<span>{{ ite.content.upcomingDesc }}</span>
          </p>
        </div>
      </div>
    </div>
    <span
      v-if="supportFallBack == 1"
      style="display: inline-block; margin: 10px 0"
      >短信回落</span
    >
    <div
      v-if="supportFallBack == 1"
      style="width: 100%; min-height: 60px; background: #eee; line-height: 30px"
    >
      <div style="margin: 0px 10px">
        <span>内容：</span>
        <span>{{ supportFalltext }}</span>
      </div>
      <div style="margin: 0px 10px" v-if="xsmsUrl">
        <span>链接：</span>
        <span
          ><a
            :href="'https://' + xsmsUrl"
            rel="noopener noreferrer"
            target="_blank"
            >{{ xsmsUrl }}</a
          ></span
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['list'],
  data() {
    return {
      contentFrom: {
        templateName: '',
        templateType: 'media',
        chatbotId: '',
        templateContent: {
          materialId: '',
          url: '',
          buttons: [],
        },
      },
      xsmsUrl: '',
      supportFallBack: '',
      supportFalltext: '',
      chatbotName: '',
    }
  },

  // created() {
  //   // console.log(this.list,'11');
  //   this.contentFrom.templateName = this.list.templateName;
  //   this.contentFrom.templateType = this.list.templateType;
  //   this.contentFrom.templateContent = JSON.parse(this.list.templateContent);
  // },
  watch: {
    list: {
      handler(val) {
        this.contentFrom.templateName = this.list.templateName
        this.contentFrom.templateType = this.list.templateType
        this.contentFrom.templateContent = JSON.parse(this.list.templateContent)
        if (val.supportFallBack == 1) {
          this.supportFallBack = val.supportFallBack
          this.supportFalltext = val.smsContent
          this.chatbotName = val.chatbotName
          if (val.rscContent) {
            this.xsmsUrl = val.rscContent
          }
        }
        // if(this.type == 'json'){
        //       this.data = val.templateContent
        //   }else{
        //       this.data = JSON.parse(val.templateContent);
        //   }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
  // mounted(){
  //   console.log(this.list,'list');
  // }
}
</script>

<style lang="less" scoped>
.view_card {
  width: 305px;
  height: 550px;
  border: 7px solid #000;
  border-radius: 25px;
  margin: 10px auto;
  position: relative;
}
.view_text {
  line-height: 30px;
  margin-top: 20px;
}
.navbar {
  width: 100%;
  height: 67px;
  background: url(../../../../../assets/images/navbar.png);
  background-size: 100%;
  background-repeat: no-repeat;
  overflow: hidden;
}
.card_content {
  width: 100%;
  display: flex;
  flex-shrink: 0;
  overflow: auto;
}
.view_footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40px;
  background: url(../../../../../assets/images/foot.png);
  background-size: 100%;
  background-repeat: no-repeat;
}
.card_item {
  width: 196px;
  margin: 10px;
  position: relative;
  border: 1px solid #ccc;
  border-radius: 8px;
}
.card_media {
  width: 196px;
  height: 130px;
  border-radius: 8px 8px 0px 0px;
}
.img {
  width: 100%;
  height: 100%;
  border-radius: 8px 8px 0px 0px;
}
.media_icon {
  width: 24px;
  height: 22px;
  position: absolute;
  left: 2.08%;
  right: 2.08%;
  top: 6.25%;
  bottom: 5.9%;
  margin-left: 10px;
}
.play_active {
  width: 50px;
  height: 47px;
  top: 50%;
  margin-top: -30px;
  left: 50%;
  margin-left: -30px;
  position: absolute;
}
.card_text {
  width: 100%;
}
.text_tit {
  width: 208px;
  font-size: 12px;
  font-weight: 600;
  line-height: 25px;
  margin-left: 20px;
  margin-top: 10px;
}
.text_content {
  width: 208px;
  height: 68px;
  overflow: auto;
  font-size: 8px;
  margin-left: 5px;
  line-height: 20px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  -webkit-transform-origin-x: 0;
  -webkit-transform: scale(0.8);
}
.btn_item_active {
  width: 165px;
  height: 26px;
  text-align: center;
  line-height: 26px;
  background: #4e95ff;
  color: #fff;
  border-radius: 5px;
  margin: 10px 10px;
  cursor: pointer;
  position: relative;
}
.temp_menu_view {
  margin-left: 10px;
}
.chatbot_logo_t {
  display: flex;
  width: 100px;
  margin: 33px auto;
}
.logo_t {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
.chatbot_name_t {
  line-height: 30px;
}
.btn_item {
  margin: 10px;
  padding: 10px;
  background: #fff;
}
.btn_name {
  display: inline-block;
  background: #eee;
  width: 70px;
  height: 30px;
  border: 1px solid #cccc;
  border-radius: 5px;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
}
</style>
