<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="原通道编号" label-width="82px" prop="channelId">
            <el-input
              v-model="formInline.channelId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="切换备用通道编号"
            label-width="130px"
            prop="backupChannelId"
          >
            <el-input
              v-model="formInline.backupChannelId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="操作的通道组id"
            label-width="120px"
            prop="channelGroupId"
          >
            <el-input
              v-model="formInline.channelGroupId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload"
          >重置</el-button
        >
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem :tableDataObj="tableDataObj" @save="save"></table-tem>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '../../../../publicComponents/ChannelView.vue'
import TableTem from '../../../../publicComponents/TableTem.vue'
export default {
  components: {
    ChannelView,
    TableTem
  },
  name: 'ChannelGroupLog',
  data() {
    return {
      isFirstEnter: false,
      ChannelData: '', //传递通道值
      formInline: {
        channelId: '',
        backupChannelId: '',
        channelGroupId: '',
        currentPage: 1,
        pageSize: 10,
      },
      //查询赋值数据
      tabelAlllist: {
        channelId: '',
        backupChannelId: '',
        channelGroupId: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        custom: true,
        id: "ChannelGroupLog",
        tableData: [],
        tableLabel: [
          {
            prop: 'channelGroupLogId',
            showName: '日志管理ID',
            width: '90',
          },
          {
            prop: 'channelId',
            showName: '原通道编号',
            Channel: true,
          },
          {
            prop: 'backupChannelId',
            showName: '切换的备用通道编号',
            Channel: true,
          },
          {
            prop: 'channelGroupIds',
            showName: '操作的通道组id',
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: 160,
          },
          {
            prop: 'updateName',
            showName: '修改人',
          },
          {
            prop: 'updateTime',
            showName: '修改时间',
            width: 160,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '240', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    }
  },
  methods: {
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'v3/operatingchannelgrouplog/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    // 查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //重置
    Reload() {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  // computed(){ },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //     handler() {
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate: true
    // },
  },
}
</script>

<style>
.yunyingshang .el-form-item__content {
  margin-left: 10px !important;
}
</style>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 20px 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
