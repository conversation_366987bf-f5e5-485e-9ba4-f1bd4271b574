<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInlineRef"
          label-width="80px"
        >
          <el-form-item label="模版ID" prop="id">
            <el-input
              v-model.trim="formInline.id"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="模版内容" prop="content">
            <el-input
              v-model.trim="formInline.content"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="状态" prop="status">
            <el-select v-model="formInline.status" placeholder="请选择" clearable class="input-w">
              <el-option label="启用" value="0"></el-option>
              <el-option label="停用" value="1"></el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item label="创建时间" prop="createTime">
                <el-date-picker
                  v-model="formInline.createTime"
                  type="datetimerange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  @change="handleTimeChange"
                  :clearable="false"
                />
              </el-form-item> -->
          <el-form-item>
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button type="primary" plain @click="Reload(formInlineRef)"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div>
        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addsimilarMobile"
              >创建模版</el-button
            >
            <el-button type="primary" @click="exportBlackTemplate"
              >导入模版</el-button
            >
          </template>
        </vxe-toolbar>
        <vxe-table
          ref="tableRef"
          id="blackTemplateTable"
          border
          stripe
          :custom-config="{ storage: true }"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange"
        >
          <!-- <vxe-column type="checkbox" width="50"></vxe-column> -->
          <vxe-column field="模版ID" title="模版ID">
            <template v-slot="scope">
              <div>
                {{ scope.row.id }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            field="content"
            title="模版内容"
            show-overflow
            width="400"
          ></vxe-column>
          <!-- <vxe-column field="关键词" title="关键词">
            <template v-slot="scope">
              <div>
                {{ scope.row.keywords }}
              </div>
            </template>
          </vxe-column> -->
          <!-- <vxe-column field="签名" title="签名">
            <template v-slot="scope">
              <div>
                {{ scope.row.signature }}
              </div>
            </template>
          </vxe-column> -->
          <vxe-column field="相似度" title="相似度">
            <template v-slot="scope">
              <div>{{ scope.row.percent }}%</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>
                {{ scope.row.createName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="更新时间" title="更新时间" width="170">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.updateTime).format("YYYY-MM-DD HH:mm:ss") }}
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag :disable-transitions="true" v-if="scope.row.status == -1" type="danger" effect="dark">
                删除
              </el-tag>
              <el-tag :disable-transitions="true" v-if="scope.row.status == 0" type="success" effect="dark">
                启用
              </el-tag>
              <el-tag :disable-transitions="true" v-if="scope.row.status == 1" type="danger" effect="dark">
                停用
              </el-tag>
            </template>
          </vxe-column> -->
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="color: #409eff"
                @click="editBlackTemplate(scope.row)"
                >编 辑</el-button
              >
              <el-button
                link
                style="color: red"
                @click="deleteSimilarMobile(scope.row)"
                >删 除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
      </div>
      <el-dialog
        v-model="dialogVisible"
        :title="title"
        width="650"
        :before-close="handleClose"
      >
        <el-form
          :model="similarFormop"
          :rules="similaRrules"
          label-width="120px"
          ref="similarFormopFef"
          style="padding: 0 28px 0 20px"
        >
          <!-- <el-form-item label="用户名" prop="username">
            <el-input style="width: 300px" :disabled="similarFormop.id ? true : false" v-model="similarFormop.username"
              autocomplete="off"></el-input>
          </el-form-item> -->
          <el-form-item label="模板内容" prop="content">
            <el-input
              :disabled="similarFormop.id ? true : false"
              type="textarea"
              style="width: 300px; height: 110px"
              v-model="similarFormop.content"
              autocomplete="off"
              placeholder="黑模版内容不能是纯数字加英文"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="关键字" prop="keywords">
            <el-input
              style="width: 300px"
              v-model="similarFormop.keywords"
              autocomplete="off"
            ></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="审核状态" prop="auditStatus">
            <el-select style="width: 300px" v-model="similarFormop.auditStatus" placeholder="请选择">
              <el-option label="审核通过" value="1"> </el-option>
              <el-option label="审核不通过" value="2"> </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="相似度" prop="percent">
            <el-input-number
              style="width: 300px"
              v-model="similarFormop.percent"
              :min="50"
              :max="100"
              label="相似度为正整数，请输入正确数字"
            ></el-input-number>
          </el-form-item>
          <!-- <el-form-item label="是否校验签名" prop="checkSign">
            <el-radio-group v-model="similarFormop.checkSign">
              <el-radio value="0">是</el-radio>
              <el-radio value="1">否</el-radio>
            </el-radio-group>
          </el-form-item> -->
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit(similarFormopFef)">
              确定
            </el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
        v-model="exportVisible"
        title="模版导入"
        width="650"
        :before-close="handleClose"
      >
        <el-form
          :model="exportForm"
          label-width="120px"
          ref="similarFormopFef"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="上传文件" prop="fileList">
            <el-upload
              ref="upload"
              v-model:file-list="exportForm.fileList"
              class="upload-demo"
              :action="uploadUrl"
              :headers="token"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :on-success="handleSuccess"
              :on-error="handleError"
              :limit="1"
            >
              <el-button type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  <a
                    href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/0823fa708801e5f6100f56a574eabae9"
                    target="_blank"
                    rel="noopener noreferrer"
                    >模版示例</a
                  >
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="exportVisible = false">取消</el-button>
            <el-button type="primary" @click="handleExport"> 确定 </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import { ref } from "vue";
import { reactive } from "vue";
const percent = (rule, value, callback) => {
  if (value != "") {
    let reg = /^[0-9]*[1-9][0-9]*$/;
    if (reg.test(value)) {
      if (value >= 50 && value <= 100) {
        callback();
      } else {
        callback(new Error("相似度在于50-100之间"));
      }
    } else {
      callback(new Error("相似度为正整数，请输入正确数字"));
    }
  } else {
    callback(new Error("相似度不能为空"));
  }
};
const formInlineRef = ref(null);
const toolbarRef = ref(null);
const tableRef = ref(null);
const similarFormopFef = ref(null);
const dialogVisible = ref(false);
const exportVisible = ref(false);
const token = {
  Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
};
const exportForm = reactive({
  fileList: [],
});
const upload = ref(null);
const uploadUrl = window.path.omcs + "operatingclientsmsblacksimilar/upload";
const formInline = reactive({
  username: "",
  content: "",
  currentPage: 1,
  pageSize: 10,
});
const statusList = ref([]);
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
  total: 0,
});
const title = ref("");
const similarForm = ref(null);
const similarFormop = reactive({
  id: "",
  content: "",
  // keywords: "",
  percent: undefined,
  snapshot: "",
  // checkSign:"0",
  id: "",
});
const similaRrules = reactive({
  // username: [
  //   { required: true, message: "用户名不能为空", trigger: "change" },
  // ],
  content: [{ required: true, message: "内容不能为空", trigger: "change" }],
  // keywords: [{ required: true, message: "关键词不能为空", trigger: "change" }],
  auditStatus: [
    { required: true, message: "请选择审核状态", trigger: "change" },
  ],
  // checkSign: [
  //   {
  //     required: true,
  //     message: "请选择模板是否校验签名",
  //     trigger: "change",
  //   },
  // ],
  percent: [{ required: true, validator: percent, trigger: "change" }],
});
const similarRules = reactive({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
  ],
  expireTime: [{ required: true, message: "请选择有效期", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "blur" }],
});
const handleSelectionChange = () => {};
const handleSizeChange = (size) => {
  formInline.pageSize = size;
  gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  formInline.currentPage = currentPage;
  gettableLIst();
};
const gettableLIst = () => {
  //获取列表
  tableDataObj.loading2 = true;
  window.api.post(
    window.path.omcs + "operatingclientsmsblacksimilar/page",
    formInline,
    (res) => {
      tableDataObj.loading2 = false;
      tableDataObj.total = res.data.total;
      tableDataObj.tableData = res.data.records;
    }
  );
};
const Query = () => {
  gettableLIst();
};
const Reload = () => {
  formInlineRef.value.resetFields();
  statusList.value = [];
  gettableLIst();
};
const handleClose = () => {
  dialogVisible.value = false;
  exportVisible.value = false;
  exportForm.fileList.value = [];
};
const addsimilarMobile = () => {
  dialogVisible.value = true;
  title.value = "创建模版";
};
const exportBlackTemplate = () => {
  exportVisible.value = true;
};
const editBlackTemplate = (row) => {
  dialogVisible.value = true;
  title.value = "编辑模版";
  // similarFormop.username = row.username;
  similarFormop.content = row.content;
  // similarFormop.keywords = row.keywords;
  similarFormop.percent = row.percent;
  // similarFormop.checkSign = row.checkSign+'';
  similarFormop.id = row.id;
};
const handleSubmit = (formEl) => {
  try {
    formEl.validate((valid) => {
      if (valid) {
        if (similarFormop.id) {
          //编辑
          window.api.put(
            window.path.omcs + "operatingclientsmsblacksimilar",
            similarFormop,
            (res) => {
              if (res.code == 200) {
                dialogVisible.value = false;
                ElMessage.success("编辑成功");
                gettableLIst();
              } else {
                ElMessage.error(res.msg);
              }
            }
          );
        } else {
          //新增
          window.api.post(
            window.path.omcs + "operatingclientsmsblacksimilar",
            similarFormop,
            (res) => {
              if (res.code == 200) {
                dialogVisible.value = false;
                ElMessage.success("新增成功");
                gettableLIst();
              } else {
                ElMessage.error(res.msg);
              }
            }
          );
        }
      } else {
        console.log("error submit!!");
        return false;
      }
    });
  } catch (error) {
    console.error(error);
  }
};
const deleteSimilarMobile = (row) => {
  try {
    ElMessageBox.confirm("确定删除吗？", "提醒", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        window.api.delete(
          window.path.omcs + "operatingclientsmsblacksimilar/" + row.id,
          {},
          (res) => {
            if (res.code == 200) {
              ElMessage.success("删除成功");
              gettableLIst();
            } else {
              ElMessage.error(res.msg);
            }
          }
        );
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "已取消",
        });
      });
  } catch (error) {
    console.error(error);
  }
};
const handlePreview = (file) => {};
const handleRemove = (file, fileList1) => {
  console.log(file, fileList1);
  exportForm.fileList.value = [];
};
const handleSuccess = (response, file, fileList) => {
  if (response.code == 200) {
    ElMessage.success("上传成功");
    exportForm.fileList.value = [];
    // gettableLIst();
  }else{
    ElMessage.error(response.msg);
  }
};

const handleError = (err, file, fileList) => {
  console.error("上传失败", err, file, fileList);
  ElMessage.error("上传失败");
};
const handleExport = () => {
  try {
    if (exportForm.fileList.length == 0) {
      ElMessage.error("请选择上传文件");
      return;
    }
    gettableLIst();
    exportVisible.value = false;
  } catch (error) {
    console.error(error);
  }
};
onMounted(() => {
  const $table = tableRef.value;
  const $toolbar = toolbarRef.value;
  if ($table && $toolbar) {
    $table.connect($toolbar);
  }
  gettableLIst();
});

watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      similarFormopFef.value.resetFields();
      // similarFormop.username = "";
      similarFormop.content = "";
      // similarFormop.keywords = "";
      similarFormop.percent = "";
      // similarFormop.checkSign = "0";
      similarFormop.id = "";
      title.value = "";
    }
  }
);
watch(
  () => exportVisible.value,
  (val) => {
    if (!val) {
      exportForm.fileList = [];
    }
  }
);
</script>

<style lang="less" scoped>
.OuterFrame {
  padding: 20px;
}

.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Templat-matter > p {
  padding: 5px 0;
}
</style>