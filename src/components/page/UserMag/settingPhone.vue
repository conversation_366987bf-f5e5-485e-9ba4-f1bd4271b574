<template>
  <div>
    <div class="LoginCellPhone-matter">
      <div>温馨提示</div>
      <div>1.默认登录手机号为新增时填写的首个手机号。</div>
      <div>2.该登录手机号至多可添加50个，管理员手机号不可删除！</div>
    </div>
    <div class="LoginCellPhone-creat">
      <el-button
        v-if="roleId == 3 || roleId == 4 || roleId == 19 || roleId == 20"
        type="primary"
        @click="addphone()"
        >添加登录手机号</el-button
      >
    </div>
    <div>
      <el-table
        v-loading="tableDataObj.loading2"
        :data="tableDataObj.tableData"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        border
        :stripe="true"
        style="width: 100%"
      >
        <el-table-column label="用户名">
          <template #default="scope">
            <span>{{ scope.row.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号码">
          <template #default="scope">
            <div class="spanColor">
              {{ scope.row.maskMobile }}
              <el-tooltip
                v-if="scope.row.isAdmin == 1"
                effect="dark"
                content="管理员"
                placement="top"
              >
                <i
                  style="color: #409eff; margin-left: 10px"
                  class="iconfont icon-yonghuming"
                ></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注">
          <template #default="scope">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="roleId == 3 || roleId == 4 || roleId == 19 || roleId == 20"
          label="操作"
        >
          <template #default="scope">
            <el-button style="color: #e6a23c" link @click="editphone(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.isAdmin != 1"
              style="color: #f56c6c"
              link
              @click="deletephone(scope.row)"
              >删除</el-button
            >
            <el-button
              v-if="scope.row.isAdmin != 1"
              style="color: #409eff"
              link
              @click="transferAdmin(scope.row)"
              >管理员转让</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      title="添加登录手机号"
      v-model="LcpDialogVisible"
      width="800px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-form
        :inline="true"
        :model="addPhoneForm"
        :rules="addPhoneRules"
        ref="ruleForm2"
        class="demo-ruleForm"
      >
        <div v-if="addPhoneForm.list.length">
          <template v-for="(row, index) in addPhoneForm.list">
            <el-form-item
              label-width="120px"
              :rules="addPhoneRules.phone"
              label="登录手机号"
              :prop="'list.' + index + '.phone'"
            >
              <el-input v-model="row.phone" class="input-t"></el-input>
            </el-form-item>
            <el-form-item
              :rules="addPhoneRules.remark"
              label="备注"
              :prop="'list.' + index + '.remark'"
            >
              <el-input v-model="row.remark" class="input-t"></el-input>
            </el-form-item>
            <i
              style="font-size: 24px; color: #f56c6c; cursor: pointer"
              class="el-icon-remove-outline"
              @click="addPhoneForm.list.splice(index, 1)"
            ></i>
          </template>
          <div class="add-white" @click="addWhiteListAction">添加手机号</div>
        </div>
        <div v-else class="add-white-list" @click="addWhiteListAction">
          添加手机号
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            @click="LcpDialogVisible = false"
            style="width: 100px; padding: 9px 0"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="submitForm(ruleForm2)"
            style="width: 100px; padding: 9px 0"
            >提交</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="title"
      v-model="DeletePhone"
      width="560px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-form
        :model="delphone"
        :rules="addPhoneRules"
        ref="ruleForm3"
        class="demo-ruleForm"
        label-width="130px"
      >
        <el-form-item label="手机号" prop="mobile">
          <el-input
            disabled
            v-model="delphone.mobile"
            class="input-t"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="delphone.remark"
            type="textarea"
            class="input-t"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            @click="DeletePhone = false"
            style="width: 100px; padding: 9px 0"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="delSubmitForm(ruleForm3)"
            style="width: 100px; padding: 9px 0"
            >确认编辑</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const isFirstEnter = ref(false);
const roleId = ref("");
isFirstEnter.value = true;
onActivated(() => {
  if (meta.isBack || isFirstEnter.value) {
    nextTick(() => {
      let userInfo = JSON.parse(localStorage.getItem("userInfo"));
      if (userInfo) {
        roleId.value = userInfo.roleId;
      }
      console.log("roleId", roleId.value);

      InquireList();
      // getApiinfo();
    });
  } else {
    meta.isBack = false;
    isFirstEnter.value = false;
  }
});
const ruleForm2 = ref(null);
const ruleForm3 = ref(null);
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
});
const LcpDialogVisible = ref(false);
const DeletePhone = ref(false);
const title = ref("");
const addPhoneForm = reactive({
  list: [
    {
      phone: "",
      remark: "",
    },
  ],
});
const delphone = reactive({
  phoneId: "",
  mobile: "",
  remark: "",
});
const phone = (rule, value, callback) => {
  if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(value)) {
    return callback(new Error("请输入正确手机号"));
  } else if (value == "") {
    return callback(new Error("请输入手机号"));
  } else {
    callback();
  }
};
const addPhoneRules = reactive({
  remark: [
    { required: true, message: "请输入备注" },
    {
      min: 2,
      max: 10,
      message: "长度在 2 到 10 个字符",
      trigger: "blur",
    },
  ],
  phone: [
    { required: true, validator: phone, trigger: "blur" },
    // { min: 6, max: 6, message: '请输入6位数字验证码' }
  ],
});
const userName = ref("");
//打开弹窗
const addphone = () => {
  if (tableDataObj.tableData.length >= 50) {
    ElMessage({
      type: "error",
      message: "最多只能添加50个手机号！",
    });
    return;
  } else {
    LcpDialogVisible.value = true;
  }
};
//关闭弹窗
const handelClose = () => {
  LcpDialogVisible.value = false;
  DeletePhone.value = false;
};
//添加手机号
const addWhiteListAction = () => {
  let obj = {
    phone: "",
    remark: "",
  };
  addPhoneForm.list.push(obj);
};
//提交表单
const submitForm = (formName) => {
  ruleForm2.value.validate((valid) => {
    if (valid) {
      let data = {
        userName: userName.value,
        phoneList: addPhoneForm.list,
      };
      window.api.post(
        window.path.omcs + "operatinguser/addLoginPhoneV2",
        data,
        (res) => {
          if (res.code == 200) {
            LcpDialogVisible.value = false;
            InquireList();
            ElMessage({
              type: "success",
              message: "添加成功",
            });
          } else {
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      ElMessage({
        type: "error",
        message: "请检查输入项",
      });
    }
  });
};
//获取列表
const InquireList = () => {
  tableDataObj.loading2 = true;
  window.api.get(
    window.path.omcs +
      "operatinguser/userInfo/" +
      $router.currentRoute.value.query.id,
    {},
    (res) => {
      if (res.code == 200) {
        tableDataObj.loading2 = false;
        tableDataObj.tableData = res.data.mobileList;
        tableDataObj.tableData.forEach((item) => {
          item.userName = res.data.consumerName;
        });
        userName.value = res.data.consumerName;
      }
    }
  );
};
//编辑手机号
const editphone = (row) => {
  console.log(row, "row");

  title.value = "编辑";
  delphone.phoneId = row.phoneId;
  delphone.mobile = row.maskMobile;
  delphone.remark = row.remark || "";
  DeletePhone.value = true;
};

const delSubmitForm = (formName) => {
  ruleForm3.value.validate((valid) => {
    if (valid) {
      let data = {
        phoneId: delphone.phoneId,
        remark: delphone.remark,
        userName: userName.value,
      };
      window.api.post(
        window.path.omcs + "operatinguser/updateLoginPhone",
        data,
        (res) => {
          if (res.code == 200) {
            DeletePhone.value = false;
            InquireList();
            ElMessage({
              type: "success",
              message: "编辑成功",
            });
          } else {
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      ElMessage({
        type: "error",
        message: "请检查输入项",
      });
    }
  });
};
//删除手机号
const deletephone = (row) => {
  ElMessageBox.confirm("确认删除该手机号", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      window.api.post(
        window.path.omcs + "operatinguser/deleteLoginPhoneV2",
        { phoneId: row.phoneId, userName: userName.value },
        (res) => {
          if (res.code == 200) {
            InquireList();
            ElMessage({
              type: "success",
              message: "删除成功",
            });
          } else {
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作取消",
      });
    });
};
//管理员转让
const transferAdmin = (row) => {
  try {
    let data = {
      destId: row.phoneId,
      sourceId: "",
      userName: userName.value,
    };
    let dataStr = JSON.stringify(tableDataObj.tableData);
    let dataObj = JSON.parse(dataStr);
    for (let index = 0; index < dataObj.length; index++) {
      if (dataObj[index].isAdmin == 1) {
        data.sourceId = dataObj[index].phoneId;
        break;
      }
    }
    ElMessageBox.confirm("是否将该账号设置为管理员", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        window.api.post(
          window.path.omcs + "operatinguser/transferAdmin",
          data,
          (res) => {
            if (res.code == 200) {
              InquireList();
              ElMessage({
                type: "success",
                message: "转让成功",
              });
            } else {
              ElMessage({
                type: "error",
                message: res.msg,
              });
            }
          }
        );
      })
      .catch(() => {
        ElMessage({
          type: "info",
          message: "操作取消",
        });
      });
  } catch (error) {
    console.log(error, "error");
  }

  // console.log(data, "data");
};

watch(
  () => LcpDialogVisible.value,
  (val) => {
    if (!val) {
      ruleForm2.value.resetFields();
      addPhoneForm.list = [
        {
          phone: "",
          remark: "",
        },
      ];
    }
  }
);
</script>

<style scoped>
.LoginCellPhone-box {
  padding: 20px;
}

.LoginCellPhone-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.LoginCellPhone-matter > div {
  /* height:26px; */
  line-height: 26px;
}

.LoginCellPhone-creat {
  margin: 20px 0px;
}

.add-white-list {
  width: 650px;
  height: 50px;
  line-height: 50px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.add-white {
  width: 640px;
  height: 30px;
  line-height: 30px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.input-t {
  width: 250px;
}
</style>