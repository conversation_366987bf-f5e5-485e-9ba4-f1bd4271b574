<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-page-header @back="goBack" :content="title"> </el-page-header>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form label-width="80px" :inline="true" :model="formInline" class="demo-form-inline" ref="queryForm">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="formInline.taskName" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <!-- <el-form-item label="任务类型" prop="action">
                            <el-select v-model="formInline.action" clearable placeholder="不限" class="input-w">
                                <el-option label="不限" value=""></el-option>
                                <el-option label="管理商" value="12"></el-option>
                                <el-option label="子用户" value="13"></el-option>
                                <el-option label="终端" value="14"></el-option>
                                <el-option label="线上终端" value="22"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间" prop="time">
                            <el-date-picker class="input-time" v-model="formInline.time" value-format="YYYY-MM-DD"
                                type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                                @change="handeTime">
                            </el-date-picker>
                        </el-form-item> -->
          <el-form-item>
            <el-button type="primary" plain style="" @click="Query">查询</el-button>
            <el-button type="primary" plain style="" @click="Reload('queryForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table Mail-table1">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button @click="addUserStopTask" type="primary">新增</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="batchUserLists" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;" :data="tableDataObj.tableData">

          <!-- <vxe-column type="selection"> </vxe-column> -->
          <vxe-column field="任务名称" title="任务名称" width="170">
            <template v-slot="scope">
              <div style="color: #409eff; cursor: pointer" @click="handleDetail(scope.row)">
                <Tooltip v-if="scope.row.taskName" :content="scope.row.taskName" className="wrapper-text"
                  effect="light">
                </Tooltip>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="目标对象" title="目标对象">
            <template v-slot="scope">
              <div>
                {{ scope.row.targetStr }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作类型" title="操作类型">
            <template v-slot="scope">
              <div>
                {{ scope.row.actionStr }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态" width="130">
            <template v-slot="scope">
              <el-tag :disable-transitions="true" type="info" effect="dark" v-if="scope.row.status == '0'">待执行</el-tag>
              <el-tag :disable-transitions="true" type="warning" effect="dark"
                v-else-if="scope.row.status == '1'">执行中</el-tag>
              <el-tag :disable-transitions="true" type="success" effect="dark"
                v-else-if="scope.row.status == '2'">执行完成</el-tag>
              <el-tag :disable-transitions="true" type="danger" effect="dark"
                v-else-if="scope.row.status == '3'">执行失败</el-tag>
              <el-tooltip class="item" effect="dark" :content="scope.row.statusReason" placement="top-start"
                v-if="scope.row.status == '3'">
                <el-icon style="color: #f56c6c; font-size: 18px; margin-left: 10px">
                  <WarningFilled />
                </el-icon>
              </el-tooltip>
            </template>
          </vxe-column>
          <vxe-column field="总匹配数" title="总匹配数">
            <template v-slot="scope">
              <div>
                {{ scope.row.total }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="已处理数" title="已处理数">
            <template v-slot="scope">
              <div>
                {{ scope.row.completeCount }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="处理成功数" title="处理成功数">
            <template v-slot="scope">
              <div>
                {{ scope.row.success }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="处理时长（毫秒）" title="处理时长（毫秒）" width="130">
            <template v-slot="scope">
              <div>
                {{ scope.row.costTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>
                {{ scope.row.createdTimeStr }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>
                {{ scope.row.createdUser }}
              </div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <div style="
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
          ">
          <div></div>
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="formInline.currentPage" :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
          </el-pagination>
        </div>
        <!-- 新增任务弹窗开始 -->
        <el-dialog title="新增任务 " v-model="dialogVisible" width="750px" :before-close="handleClose">
          <el-form :rules="rules" ref="batchForm" :model="batchForm" label-width="120px">
            <el-form-item label="任务名称" prop="taskName">
              <el-input class="input-w" v-model="batchForm.taskName"></el-input>
            </el-form-item>
            <el-form-item label="执行操作" prop="updateValue.type">
              <el-radio-group v-model="batchForm.updateValue.type" @change="handleAction">
                <el-radio v-if="batchForm.action == 'USER_WHITELIST'" label="1">白名单</el-radio>
                <el-radio v-if="batchForm.action == 'USER_BLACKLIST'" label="2">黑名单</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="batchForm.updateValue.type == 1" label="等级" prop="updateValue.level">
              <el-radio-group v-model="batchForm.updateValue.level">
                <el-radio label="1">一级白名单</el-radio>
                <el-radio label="2">二级白名单</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="batchForm.updateValue.type == 1" label="有效期（天）" prop="updateValue.days">
              <el-input-number v-model="batchForm.updateValue.days" controls-position="right" :min="1" :max="9999999"
                :step="1"></el-input-number>
            </el-form-item>
            <el-form-item label="手机号" prop="updateValue.mobile">
              <el-input style="height: 120px;" type="textarea" placeholder="多个手机号以,分割"
                v-model.trim="batchForm.updateValue.mobile" @paste.prevent="handlePaste">
              </el-input>
              <div style="color: #666; font-size: 12px; margin-top: 5px;">
                当前已输入 {{ mobileCount }} 个号码
              </div>
            </el-form-item>
            <el-form-item label="提取号码" prop="">
              <el-upload class="upload-demo" :action="this.API.cpus + 'v3/file/discernMobile'"
                :on-success="handleUploadSuccess" :on-remove="handleRemove" :show-file-list="true" :file-list="fileList"
                :headers="token" :limit="1" :before-upload="beforeUpload" accept=".xlsx,.xls">
                <el-button type="primary">上传文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传xlsx、xls格式文件
                  </div>
                </template>
              </el-upload>
            </el-form-item>
            <div>
              <div style="color: red">筛选条件</div>
              <el-form-item label="公司名" prop="filter.compId">
                <el-select ref="optionRef" class="input-w" v-model="batchForm.filter.compId" clearable filterable remote
                  :remote-method="remoteMethod" :loading="loadingcomp" placeholder="请选择公司名称" @change="bindChange">
                  <el-option v-for="(item, index) in compNamelist" :key="index" :label="item.company" :value="item.id">
                  </el-option>
                </el-select>
                <!-- <el-input v-model="batchForm.filter.compName" placeholder="" class="input-w"></el-input> -->
              </el-form-item>
              <el-form-item label="用户名" prop="filter.consumerNameArr">
                <el-transfer :titles="['待选用户', '已选用户']" v-model="batchForm.filter.consumerNameArr" :data="userData"
                  @change="handleChange"></el-transfer>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="备注" prop="remark">
                <el-input style="height: 70px;" type="textarea" v-model="batchForm.remark"></el-input>
              </el-form-item>
            </div>
          </el-form>
          <template v-slot:footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false">取 消</el-button>
              <el-button type="primary" @click="submitBatchForm('batchForm')">执行</el-button>
            </span>
          </template>
        </el-dialog>

        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import Tooltip from '@/components/publicComponents/tooltip.vue'
import clip from '../../../utils/clipboard'

export default {
  components: {
    Tooltip,
    // ElIconWarning,
  },
  name: 'batchUserShop',
  data() {
    const validateLevel = (rule, value, callback) => {
      if (this.batchForm.updateValue.type == 1) {
        if (!value) {
          callback(new Error('请选择等级'))
        } else if (value !== '1' && value !== '2') {
          callback(new Error('等级只能是1或2'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const fiveMobiles = (rule, value, callback) => {
      if (value != '') {
        let reg =
          /^(1[3|4|5|6|7|8|9][0-9]\d{8},){0,1000}1[3|4|5|6|7|8|9][0-9]\d{8}$/
        if (reg.test(value)) {
          var arr = value.split(',')
          var arr2 = []
          for (var i = 0; i < arr.length; i++) {
            if (arr2.indexOf(arr[i]) < 0) {
              arr2.push(arr[i])
            }
          }
          if (arr2.length != arr.length) {
            return callback(new Error('不允许重复'))
          }
          callback()
        } else {
          return callback(new Error('请输入正确的手机号,多个用逗号(,)隔开'))
        }
      } else {
        callback(new Error('请输入手机号'))
      }
    }
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      dialogVisible: false,
      fileList: [],
      title: '',
      formInline: {
        taskName: '', //任务名称
        action: '', //任务类型
        pageSize: 10,
        currentPage: 1,
      },
      token: {},
      localData: {},
      batchForm: {
        taskName: '', //任务名称
        action: '', //任务类型
        remark: '', //备注
        filter: {
          // time: '',
          // roleId: "",//角色ID
          consumerNameArr: [], //用户名
          compId: '', //公司名
          consumerStatus: '0', //账号状态
          // consumerStatus: "",//账号状态
          // service: "",//所属客服
          // ext: "",//扩展号
          // sales: "",//所属销售
          // createName: "",//创建人
          // smsChargeback: "",//扣费方式
          // smsPayMethod: "",//付费方式
          // createBeginTime: "",//开始的时间
          // createEndTime: "",//结束的时间
          // certificate: "",//是否认证
          // cooperateStatus: "",//合作状态
        },
        updateValue: {
          mobile: '', //手机号
          type: '', //操作类型
          level: '', //等级
          days: '', //有效期（天）
        },
      },
      services: [], //客服列表
      rules: {
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'change' },
        ],
        'updateValue.value': [
          { required: true, message: '请选择执行操作', trigger: 'change' },
        ],
        'updateValue.level': [{ validator: validateLevel, trigger: 'change' }],
        'updateValue.days': [
          { required: true, message: '有效期不能为空', trigger: 'change' },
        ],
        'updateValue.mobile': [
          { required: true, validator: fiveMobiles, trigger: 'change' },
        ],
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        total: 0, //总条数
      },
      compNamelist: [],
      loadingcomp: false,
      userData: [],
      tag: '',
    }
  },
  watch: {
    //监听弹窗显示隐藏
    dialogVisible(val) {
      if (!val) {
        //关闭弹窗时重置表单
        this.$refs['batchForm'].resetFields()
        this.batchForm.action = this.localData.code
        this.batchForm.filter.consumerNameArr = []
        this.userData = []
        this.compNamelist = []
        this.fileList = []
        this.batchForm.updateValue.mobile = ''
        // this.batchForm.action = this.$route.query.action;
      }
    },
  },
  methods: {
    //返回上一页
    goBack() {
      this.$router.go(-1);
    },
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
              })
            }
            // this.services = res.data;
          }
        )
      } catch (error) {
        console.log(error, 'error')
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loadingcomp = true
        this.searchAccount(query)
        this.loadingcomp = false
      } else {
        this.compNamelist = []
        this.searchAccount()
      }
    },
    getUserList(compId, status) {
      window.api.get(
        window.path.omcs +
        `operatinguser/users/${compId}?consumerStatus=${status}`,
        {},
        (res) => {
          if (res.code == 200) {
            this.userData = res.data.map((item) => {
              return {
                key: item.consumerName,
                label: item.consumerName,
              }
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
          // this.services = res.data;
        }
      )
    },
    handleAction(e) { },
    bindChange(e) {
      if (e) {
        this.getUserList(e, this.batchForm.filter.consumerStatus)
      }
    },
    handleChange(e) {
      // console.log(e, 'eee');
      // console.log(this.userData, 'e')
      // this.batchForm.filter.consumerNameArr = e
    },
    //获取列表数据
    getTableData() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operating/client/batch/task/page',
        this.formInline,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    handlePaste(event) {
      const clipboardData = event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData('Text');
      if (pastedData) {
        let formattedMobiles = ''
        // 使用正则表达式拆分内容，考虑多种分隔符
        const ipList = pastedData.split(/[\s,]+/);
        // 去重并过滤掉空字符串
        const uniqueIPs = Array.from(new Set(ipList)).filter(ip => ip.trim() !== '');
        // 将处理后的IP列表转换为逗号分隔的字符串
        formattedMobiles = uniqueIPs.join(',');
        // 阻止默认的粘贴行为并将结果手动插入
        event.target.value = formattedMobiles;
        this.batchForm.updateValue.mobile = formattedMobiles;
      }
    },
    //查询
    Query() {
      this.getTableData()
    },
    //重置
    Reload(formName) {
      this.$refs[formName].resetFields() //清空查询表单
      this.formInline.action = this.localData.code
      this.getTableData()
    },
    //复制
    handleCopy(name, event) {
      clip(name, event)
    },
    //时间格式化
    handeTime(val) {
      if (val) {
        this.batchForm.filter.createBeginTime = this.moment(val[0]).format(
          'YYYY-MM-DD '
        )
        this.batchForm.filter.createEndTime = this.moment(val[1]).format(
          'YYYY-MM-DD '
        )
      } else {
        this.batchForm.filter.createBeginTime = ''
        this.batchForm.filter.createEndTime = ''
      }
    },
    // handleSelectionChange() {

    // },
    //-----翻页操作
    handleSizeChange(size) {
      this.formInline.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      this.formInline.currentPage = currentPage
      this.getTableData()
    },
    handleClose() {
      this.dialogVisible = false
      this.fileList = []
    },
    //获取客服列表
    getServices() {
      window.api.get(
        window.path.upms + 'user/selectAllSalesOrService',
        { flag: 2 },
        (res) => {
          this.services = res.data
        }
      )
    },
    // handleRadioChange(val){
    //     if
    // },
    //新增任务弹窗
    addUserStopTask() {
      this.dialogVisible = true
      let text = this.localData.code == 'USER_WHITELIST' ? '白名单' : '黑名单'
      this.batchForm.taskName = text + new Date().getTime()
      this.getServices()
    },
    //提交表单
    submitBatchForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.batchForm))
          //全选时只看企业id
          if (this.userData.length == params.filter.consumerNameArr.length) {
            params.filter.consumerNameArr = []
          }
          // params.filter.consumerNameArr = this.userData.map(item => item.consumerName)
          // params.taskName = params.updateValue.value == 1 ? '白名单-' + params.taskName : '黑名单-' + params.taskName;
          params.filter = JSON.stringify(params.filter)
          params.updateValue = JSON.stringify(params.updateValue)
          console.log(params, 'params')
          this.$confirms.confirmation(
            'post',
            '是否确认执行此操作？',
            window.path.omcs + 'operating/client/batch/task/create',
            params,
            (res) => {
              this.$message.success('任务创建成功')
              this.dialogVisible = false
              this.getTableData()
            }
          )
          // window.api.post(window.path.omcs + "operating/client/batch/task/create", params, (res) => {
          //     if (res.code == 200) {
          //         this.$message.success('任务创建成功');
          //         this.dialogVisible = false;
          //         this.getTableData();
          //     } else {
          //         this.$message.error(res.msg);
          //     }
          // });
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //查看详情
    handleDetail(row) {
      this.$router.push({
        path: '/batchUserList',
        query: {
          id: row.id,
          action: row.target,
        },
      })
    },
    handleUploadSuccess(response, file) {
      if (response.code === 200) {
        // 将提取的号码用逗号连接并填充到手机号文本域
        this.batchForm.updateValue.mobile = response.data.join(',')
        this.$message.success(`号码提取成功，共提取到 ${response.data.length} 个号码`)
        // 更新文件列表
        this.fileList = [{
          name: file.name,
          url: URL.createObjectURL(file.raw)
        }]
      } else {
        this.$message.error(response.msg || '号码提取失败')
      }
    },
    handleRemove(file) {
      this.fileList = []
      this.batchForm.updateValue.mobile = ''
    },
    beforeUpload(file) {
      // 添加自定义的上传前验证逻辑
      const allowedExtensions = ['.xlsx', '.xls']
      const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
      if (!allowedExtensions.includes(extension)) {
        this.$message.error('只能上传xlsx或xls格式文件')
        return false
      }
      return true
    },
  },
  computed: {
    mobileCount() {
      if (!this.batchForm.updateValue.mobile) return 0;
      return this.batchForm.updateValue.mobile.split(',').filter(mobile => mobile.trim()).length;
    }
  },
  // activated() {
  //     if (this.$route.meta.isBack || !this.isFirstEnter) {
  //         this.$nextTick(() => {
  //             this.localData = JSON.parse(localStorage.getItem('batchAction'));
  //             if (this.localData.code == 'USER_WHITELIST') {
  //                 this.batchForm.updateValue.type = '1';
  //             } else if (this.localData.code == 'USER_BLACKLIST') {
  //                 this.batchForm.updateValue.type = '2';
  //             }
  //             this.formInline.action = this.localData.code;
  //             this.batchForm.action = this.localData.code;
  //             this.title = this.localData.title
  //             this.getTableData();
  //         });
  //     } else {
  //         this.$route.meta.isBack = false;
  //         this.isFirstEnter = false;
  //     }
  // },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.token = {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      }
      this.localData = JSON.parse(localStorage.getItem('batchAction'))
      if (this.localData.code == 'USER_WHITELIST') {
        this.batchForm.updateValue.type = '1'
      } else if (this.localData.code == 'USER_BLACKLIST') {
        this.batchForm.updateValue.type = '2'
      }
      this.formInline.action = this.localData.code
      this.batchForm.action = this.localData.code
      this.title = this.localData.title
      this.getTableData()
    })

    // this.getSales()
    // this.getChannel()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
}
</script>

<style lang="less" scoped>
.fillet {
  margin-top: 18px;
}

:deep(.el-table__row) {
  height: 48px;
}

:deep(.el-textarea__inner) {
  height: 100% !important;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
