<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form :model="ruleForm" :inline="true" ref="ruleForm" class="demo-ruleForm" label-width="100px">
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="模板名称/ID" prop="name">
          <el-input class="input-w" v-model="ruleForm.name" placeholder="支持模板名称/模板ID"></el-input>
        </el-form-item>
        <el-form-item label="运营商模版ID" prop="resourceId">
          <el-input class="input-w" v-model="ruleForm.resourceId" placeholder="运营商模版ID"></el-input>
        </el-form-item>
        <!-- <el-form-item label="模板ID" prop="templateId">
                        <el-input v-model="ruleForm.templateId"></el-input>
                    </el-form-item> -->
        <el-form-item label="模板类型" prop="kindId">
          <el-select class="input-w" v-model="ruleForm.kindId" placeholder="">
            <el-option label="全部类型" value=""></el-option>
            <!-- <el-option label="草稿" value="0"></el-option> -->
            <el-option v-for="item in kindsList" :label="item.name" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板变量" prop="dynamic">
          <el-select class="input-w" v-model="ruleForm.dynamic" placeholder="模板变量">
            <el-option label="全部类型（是否变量）" value=""></el-option>
            <el-option label="静态（不带参数）" value="0"></el-option>
            <el-option label="动态（带参数）" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板用途" prop="useType">
          <el-select class="input-w" v-model="ruleForm.useType" placeholder="模板用途">
            <el-option label="全部类型（模板用途）" value=""></el-option>
            <el-option label="正式模板" value="1"></el-option>
            <el-option label="测试模板" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板用途" prop="factorysList">
          <el-select class="input-w" multiple v-model="ruleForm.factorysList" placeholder="支持厂商">
            <el-option label="全部厂商" value=""></el-option>
            <el-option label="华为" value="HuaWei"></el-option>
            <el-option label="oppo" value="OPPO"></el-option>
            <el-option label="vivo" value="VIVO"></el-option>
            <el-option label="小米" value="XiaoMi"></el-option>
            <el-option label="华硕" value="ASUS"></el-option>
            <el-option label="酷派" value="COOLPAD"></el-option>
            <el-option label="三星" value="SAMSUNG"></el-option>
            <el-option label="荣耀" value="HONOR"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select class="input-w" v-model="ruleForm.status" placeholder="">
            <el-option label="全部状态" value=""></el-option>
            <!-- <el-option label="草稿" value="0"></el-option> -->
            <el-option label="待审核" value="5"></el-option>
            <el-option label="运营商审核中" value="10"></el-option>
            <el-option label="审核成功" value="20"></el-option>
            <el-option label="平台驳回" value="30"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="time">
          <el-date-picker v-model="ruleForm.time" type="datetimerange" range-separator="-" start-placeholder="开始时间"
            end-placeholder="结束时间" @change="handelTime" />
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')">查询</el-button>
          <el-button plain type="primary" @click="resetForm('ruleForm')">重置</el-button>
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableData.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        :data="tableData.data"
        border
        stripe
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table ref="tableRef" id="yuexinTemplateAudit" border stripe :custom-config="customConfig"
        :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
        v-loading="tableData.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;" :data="tableData.data">

        <!-- <vxe-column type="selection" width="5"></vxe-column> -->
        <vxe-column field="用户名称" title="用户名称" width="100">
          <template v-slot="scope">
            <div>{{ scope.row.username }}</div>
          </template>
        </vxe-column>
        <vxe-column field="模板名称" title="模板名称" width="100">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.name" :content="scope.row.name" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <span>{{
                                scope.row.name
                            }}</span> -->
          </template>
        </vxe-column>
        <vxe-column field="模板ID" title="模板ID" width="190">
          <template v-slot="scope">
            <div>
              <span style="color: #409eff; cursor: pointer" @click="handelExamine(scope.row, '1')">{{ scope.row.id
              }}</span>
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                  class="el-icon-document-copy" @click="handleCopy(scope.row.id, $event)"></i> -->
              <CopyTemp :content="scope.row.id" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="签名" title="签名" width="100">
          <template v-slot="scope">
            <div>{{ scope.row.smsSigns }}</div>
          </template>
        </vxe-column>
        <vxe-column field="模板类型" title="模板类型" width="140">
          <template v-slot="scope">
            <div style="display: flex; align-items: center">
              <div v-for="(item, index) in kindsList" :key="item.id">
                <span v-if="item.id == scope.row.kindId">{{ item.name }}</span>
              </div>
              <el-tooltip v-if="scope.row.interfaceSupport === false" effect="dark" content="此模版为新模版，需要回填运营商模版ID"
                placement="top">
                <el-icon style="color: #e6a23c; margin-left: 10px; font-size: 16px">
                  <WarningFilled />
                </el-icon>
              </el-tooltip>
            </div>

            <!-- <span v-if="scope.row.kindId == '1'">单图文</span>
                            <span v-if="scope.row.kindId == '2'">视频</span>
                            <span v-if="scope.row.kindId == '3'">红包</span>
                            <span v-if="scope.row.kindId == '4'">多图文</span>
                            <span v-if="scope.row.kindId == '5'">横滑1</span>
                            <span v-if="scope.row.kindId == '6'">横滑2</span>
                            <span v-if="scope.row.kindId == '7'">长文本</span>
                            <span v-if="scope.row.kindId == '8'">图片轮播</span>
                            <span v-if="scope.row.kindId == '9'">视频图文</span>
                            <span v-if="scope.row.kindId == '10'">图文视频</span>
                            <span v-if="scope.row.kindId == '25'">短剧视频</span>
                            <span v-if="scope.row.kindId == '26'">短剧图片</span>
                            <span v-if="scope.row.kindId == '11'">电商</span>
                            <span v-if="scope.row.kindId == '12'">电商（多商品）</span>
                            <span v-if="scope.row.kindId == '13'">个性化红包</span>
                            <span v-if="scope.row.kindId == '14'">单卡券</span>
                            <span v-if="scope.row.kindId == '15'">多卡券</span>
                            <span v-if="scope.row.kindId == '16'">一般通知类</span>
                            <span v-if="scope.row.kindId == '17'">账单类</span>
                            <span v-if="scope.row.kindId == '18'">增强通知类</span>
                            <span v-if="scope.row.kindId == '19'">火车票类</span>
                            <span v-if="scope.row.kindId == '20'">汽车票类</span>
                            <span v-if="scope.row.kindId == '21'">飞机票类</span> -->
          </template>
        </vxe-column>
        <vxe-column field="厂商" title="厂商" width="250">
          <template #default="scope">
            <div style="display: flex;flex-wrap: wrap;">
              <div style="margin: 0 4px;" v-for="item in scope.row.factoryAuditList">
                <div v-if="item.factoryType == 'HuaWei'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.huaweiLarge" alt="" class="img" />
                      <img v-else :src="iconList.HuaWeiActive" alt="" class="img" />
                      <div>华为</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'XiaoMi'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.xiaomi" alt="" class="img" />
                      <img v-else :src="iconList.XiaoMiActive" alt="" class="img" />
                      <div>小米</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'OPPO'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.oppo" alt="" class="img" />
                      <img v-else :src="iconList.oppoActive" alt="" class="img" />
                      <div>oppo</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'VIVO'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.vivo" alt="" class="img" />
                      <img v-else :src="iconList.vivoActive" alt="" class="img" />
                      <div>vivo</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'HONOR'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.honorLarge" alt="" class="img" />
                      <img v-else :src="iconList.honor" alt="" class="img" />
                      <div>honor</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'ASUS'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.asusLarge" alt="" class="img border-a" />
                      <img v-else :src="iconList.asusActive" alt="" class="img border-a" />
                      <div>华硕</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'COOLPAD'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.coolpadLarge" alt="" class="img border-a" />
                      <img v-else :src="iconList.coolpadActive" alt="" class="img border-a" />
                      <div>酷派</div>
                    </div>
                  </el-tooltip>
                </div>
                <div v-if="item.factoryType == 'SAMSUNG'">
                  <el-tooltip placement="top">
                    <template v-slot:content>
                      <div>
                        <span v-if="item.state == 0">未提交</span>
                        <span v-if="item.state == 1 && scope.row.status == 5">待审核</span>
                        <span v-if="
                          item.state == 1 &&
                          (scope.row.status == 10 ||
                            scope.row.status == 20 ||
                            scope.row.status == 30)
                        ">审核中</span>
                        <span v-if="item.state == 2">审核通过</span>
                        <span v-if="item.state == 3">审核失败</span>
                      </div>
                    </template>
                    <div class="image">
                      <img v-if="item.state == 2" :src="iconList.samsungLarge" alt="" class="img border-a" />
                      <img v-else :src="iconList.samsungActive" alt="" class="img border-a" />
                      <div>三星</div>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="审核人" title="审核人" width="100">
          <template v-slot="scope">
            <p>{{ scope.row.auditUser }}</p>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
            </p>
          </template>
        </vxe-column>
        <vxe-column field="审核时间" title="审核时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.auditTime">
              {{ $parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.auditTime">
              {{ $parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}
            </p>
          </template>
        </vxe-column>
        <vxe-column field="更新时间" title="更新时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.updateTime">
              {{ $parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.updateTime">
              {{ $parseTime(scope.row.updateTime, '{h}:{i}:{s}') }}
            </p>
          </template>
        </vxe-column>
        <vxe-column field="状态" title="状态" width="120">
          <template v-slot="scope">
            <div class="item_status" v-if="scope.row.status == -1">已删除</div>
            <!-- <div class="item_status" style="
                                    background: #f6f6f6;
                                    color: #999999;
                                    border: 1px solid #dddddd
                                " v-if="scope.row.status == 0">
                                草稿
                            </div> -->
            <div class="item_status" style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              " v-if="scope.row.status == 5">
              待审核
            </div>
            <div class="item_status" style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              " v-if="scope.row.status == 10">
              运营商审核中
            </div>
            <div class="item_status" style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              " v-if="scope.row.status == 20">
              审核成功
            </div>

            <el-tooltip v-if="scope.row.status == 30" effect="dark" :content="scope.row.opinion" placement="top-start">
              <div class="item_status" style="
                  background: #ffe7e7;
                  color: #ff6666;
                  border: 1px solid #fac2c2;
                ">
                审核失败
              </div>
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="通道名称" title="通道名称" width="80">
          <template v-slot="scope">
            <Tooltip v-if="scope.row.channelName" :content="scope.row.channelName" className="wrapper-text"
              effect="light">
            </Tooltip>
            <!-- {{ scope.row.chatbotId }} -->
          </template>
        </vxe-column>
        <vxe-column field="运营商模板ID" title="运营商模板ID" width="100">
          <template v-slot="scope">
            <p>{{ scope.row.resourceId }}</p>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" fixed="right" width="180">
          <template v-slot="scope">
            <el-button style="color: #409eff;" v-if="scope.row.status == 5 || scope.row.status == 30" link
              @click="handelEdit(scope.row)">编辑
            </el-button>
            <el-button style="color: #409eff;" v-if="scope.row.status != 5" link
              @click="handelExamine(scope.row, '1')">查看模板
            </el-button>
            <el-button style="color: #ff8a00" v-else link @click="handelExamine(scope.row, '2')">模板审核
            </el-button>
            <el-button v-if="
              scope.row.interfaceSupport === false && scope.row.status == 10
            " style="color: #409eff; margin: 0" link @click="getCorrelationTemp(scope.row)">关联运营商模版ID
            </el-button>
            <el-button v-if="scope.row.status == 20 && scope.row.share == 0" @click="handleShare(scope.row, '1')" link
              style="color: #409eff;">设置公共模板</el-button>
            <el-button v-if="scope.row.status == 20 && scope.row.share != 0" @click="handleShare(scope.row, '0')" link
              style="color: #409eff;">取消公共模板</el-button>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="paginationBox">
        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="tableData.total">
        </el-pagination>
      </div>
    </div>
    <el-dialog :title="'模板审核' + '-' + title" v-model="dialogVisible" width="1640px" :before-close="handleClose"
      v-if="dialogVisible">
      <div style="display: flex">
        <iframe id="myframe" ref="newScreen" style="width: 1200px; height: 900px" :src="flowSrc"></iframe>
        <!-- <img-text v-if="detail.kindId=='1'" ref="child" :list="detail" tag="view"></img-text>
                    <img-text-s v-if="detail.kindId=='4'" ref="child" :list="detail" tag="view"></img-text-s> -->
        <div v-if="status == 5 && auditFlag == '2'" class="auditinfo">
          <el-steps :active="active" finish-status="success">
            <el-step title="审核信息"></el-step>
            <el-step title="渠道选择"></el-step>
          </el-steps>
          <el-descriptions label-width="120px" class="tempMes" title="" :column="1">
            <el-descriptions-item label="模板名称">{{
              detail.name
            }}</el-descriptions-item>
            <!-- 阅信 -->
            <el-descriptions-item label="使用场景">{{
              detail.scene
            }}</el-descriptions-item>
            <el-descriptions-item label="模板用途">
              <span v-if="detail.useType == '1'">正式模板</span>
              <span v-else>测试模板</span>
            </el-descriptions-item>
            <el-descriptions-item label="送审厂商">{{
              detail.factorys
            }}</el-descriptions-item>
            <el-descriptions-item label="签名">{{
              detail.smsSigns
            }}</el-descriptions-item>
            <el-descriptions-item label="模板示例">
              <div style="width: 425px">{{ detail.smsExample }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="模板原文" v-if="detail.smsTemplate">
              <div style="width: 425px">{{ detail.smsTemplate }}</div>
            </el-descriptions-item>
            <!-- AIM -->
          </el-descriptions>
          <el-table stripe v-if="paramsList.length" :data="paramsList" border style="width: 100%">
            <el-table-column prop="name" label="变量名"> </el-table-column>
            <el-table-column prop="example" label="变量"> </el-table-column>
          </el-table>
          <div v-show="active == 0" class="">
            <!-- <img-text v-if="detail.kindId=='1'" ref="child" :list="detail" tag="view1"></img-text>
                            <img-text-s v-if="detail.kindId=='4'" ref="child" :list="detail" tag="view1"></img-text-s> -->
            <el-form label-width="80px" :model="auditForm" ref="auditForm" class="demo-ruleForm">
              <el-form-item label="原因" prop="opinion">
                <el-input style="width: 300px" type="textarea" v-model="auditForm.opinion"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button type="danger" @click="submitFormAudit('auditForm', '2')">驳回</el-button>
                <el-button @click="next">下一步</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div v-show="active == 1">
            <el-form label-width="80px" :model="auditForm" :rules="rules" ref="ruleFormAduit" class="demo-ruleForm">
              <el-form-item label="渠道选择" prop="channel">
                <!-- <el-select style="width: 300px;" v-model="auditForm.channel" placeholder="请选择"
                                        @change="handelChange">
                                        <el-option v-for="item in options" :key="item.channelId" :label="item.name"
                                            :value="item.id">
                                        </el-option>
                                    </el-select> -->

                <el-radio-group @change="handelChange" v-model="auditForm.channel">
                  <el-radio v-for="item in options" :key="item.channelId" :value="item.id">{{ item.name }}</el-radio>
                  <!-- <el-radio :label="6">Option B</el-radio>
                                        <el-radio :label="9">Option C</el-radio> -->
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="auditForm.channel" label="送审厂商" prop="factorysList">
                <el-checkbox-group v-model="auditForm.factorysList">
                  <el-checkbox v-for="(item, index) in factorysList" :value="item" :key="index">
                    <span v-if="item == 'HuaWei'">华为</span>
                    <span v-if="item == 'XiaoMi'">小米</span>
                    <span v-if="item == 'OPPO'">oppo</span>
                    <span v-if="item == 'VIVO'">vivo</span>
                    <span v-if="item == 'HONOR'">荣耀</span>
                    <span v-if="item == 'ASUS'">华硕</span>
                    <span v-if="item == 'COOLPAD'">酷派</span>
                    <span v-if="item == 'SAMSUNG'">三星</span>
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item>
                <el-button @click="previous">上一步</el-button>
                <el-button type="primary" @click="submitFormAudit('ruleFormAduit', '1')">提交运营商</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="auditinfo" v-else>
          <el-descriptions label-width="120px" class="tempMes" title="模板详情" :column="1">
            <el-descriptions-item label="模板名称">{{
              detail.name
            }}</el-descriptions-item>
            <!-- 阅信 -->
            <el-descriptions-item label="使用场景">{{
              detail.scene
            }}</el-descriptions-item>
            <el-descriptions-item label="模板用途">
              <span v-if="detail.useType == '1'">正式模板</span>
              <span v-else>测试模板</span>
            </el-descriptions-item>
            <el-descriptions-item label="送审厂商">{{
              detail.factorys
            }}</el-descriptions-item>
            <el-descriptions-item label="通道名称">{{
              detail.channelName
            }}</el-descriptions-item>
            <el-descriptions-item label="签名">{{
              detail.smsSigns
            }}</el-descriptions-item>
            <el-descriptions-item label="模板示例">
              <div style="width: 425px">{{ detail.smsExample }}</div>
            </el-descriptions-item>
            <el-descriptions-item label="模板原文" v-if="detail.smsTemplate">
              <div style="width: 425px">{{ detail.smsTemplate }}</div>
            </el-descriptions-item>
            <!-- AIM -->
          </el-descriptions>
          <el-table stripe v-if="paramsList.length" :data="paramsList" border style="width: 100%">
            <el-table-column prop="name" label="变量名"> </el-table-column>
            <el-table-column prop="example" label="变量"> </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- <template #footer>
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
                </template> -->
    </el-dialog>
  </div>
</template>

<script>
import IMG_TEXT from './components/imageText/imageAndText.vue'
import IMG_TEXT_S from './components/imageText/standrdImgTexts.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
import huaweiLarge from '@/assets/images/huawei.png'
import HuaWeiActive from '@/assets/images/HuaWei-active.png'
import xiaomi from '@/assets/images/xiaomi.png'
import XiaoMiActive from '@/assets/images/XiaoMi-active.png'
import oppo from '@/assets/images/oppo.png'
import oppoActive from '@/assets/images/oppo-active.png'
import vivo from '@/assets/images/vivo.png'
import vivoActive from '@/assets/images/vivo-active.png'
import honorLarge from '@/assets/images/honorLarge.png'
import honor from '@/assets/images/honor.png'
import asusLarge from '@/assets/images/asusLarge.png'
import asusActive from '@/assets/images/asus-active.png'
import coolpadLarge from '@/assets/images/coolpadLarge.png'
import coolpadActive from '@/assets/images/coolpad-active.png'
import samsungLarge from '@/assets/images/samsungLarge.png'
import samsungActive from '@/assets/images/samsung-active.png'

export default {
  components: {
    'img-text': IMG_TEXT,
    'img-text-s': IMG_TEXT_S,
    Tooltip,
    CopyTemp
  },
  name: 'yuexinTemplateAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      active: 0,
      status: '',
      paramsList: [],
      //查询
      ruleForm: {
        name: '', //模板名称
        kindId: '', //模板种类
        dynamic: '', //变量模板
        useType: '', //模板用途
        factorys: '', //支持厂商
        startTime: '',
        endTime: '',
        resourceId: '',
        time: [],
        factorysList: [],
        currentPage: 1,
        pageSize: 10,
      },
      iconList: {
        huaweiLarge,
        HuaWeiActive,
        xiaomi,
        XiaoMiActive,
        oppo,
        oppoActive,
        vivo,
        vivoActive,
        honorLarge,
        honor,
        asusLarge,
        asusActive,
        coolpadLarge,
        coolpadActive,
        samsungLarge,
        samsungActive,
      },
      tabelAlllist: {
        name: '', //模板名称
        kindId: '', //模板种类
        dynamic: '', //变量模板
        useType: '', //模板用途
        factorys: '', //支持厂商
        startTime: '',
        endTime: '',
        resourceId: '',
        time: [],
        factorysList: [],
        currentPage: 1,
        pageSize: 10,
      },
      kindsList: [
        // {
        //     id: '1',
        //     name: "单图文"
        // },
        // {
        //     id: '2',
        //     name: "视频"
        // },
        // {
        //     id: '3',
        //     name: "红包"
        // },
        // {
        //     id: '4',
        //     name: "多图文"
        // },
        // {
        //     id: '5',
        //     name: "横滑1"
        // },
        // {
        //     id: '6',
        //     name: "横滑2"
        // },
        // {
        //     id: '7',
        //     name: "长文本"
        // },
        // {
        //     id: '8',
        //     name: "图片轮播"
        // },
        // {
        //     id: '9',
        //     name: "视频图文"
        // },
        // {
        //     id: '10',
        //     name: "图文视频"
        // },
        // {
        //     id: '25',
        //     name: "短剧视频"
        // },
        // {
        //     id: '26',
        //     name: "短剧图片"
        // },
        // {
        //     id: '11',
        //     name: "电商"
        // },
        // {
        //     id: '12',
        //     name: "电商（多商品）"
        // },
        // {
        //     id: '13',
        //     name: "个性化红包"
        // },
        // {
        //     id: '14',
        //     name: "单卡券"
        // },
        // {
        //     id: '15',
        //     name: "多卡券"
        // },
        // {
        //     id: '16',
        //     name: "一般通知类"
        // },
        // {
        //     id: '17',
        //     name: "账单类"
        // },
        // {
        //     id: '18',
        //     name: "增强通知类"
        // },
        // {
        //     id: '19',
        //     name: "火车票类"
        // },
        // {
        //     id: '20',
        //     name: "汽车票类"
        // },
        // {
        //     id: '21',
        //     name: "飞机票类"
        // },
      ],
      //列表数据
      tableData: {
        loading2: false,
        data: [],
        total: 0,
      },
      dialogVisible: false,
      flowSrc: null,
      title: '',
      auditFlag: '',
      detail: {}, //模板详情
      options: [],
      auditForm: {
        opinion: '', //初审建议
        channel: '',
        channels: [], //报备通道ID
        factorysList: [],
        audit: '', //审核状态：1 通过 2 不通过
        templateId: '', //模板ID
      },
      factorysList: [],
      rules: {
        channel: [{ required: true, message: '请选择通道', trigger: 'change' }],
      },
      iframeWin: '',
      timer: null,
      kindId: '',
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTempkinds()
      this.getTempList()
      // this.getChannels()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTempkinds()
        this.getTempList()
        // this.getChannels()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getTempkinds() {
      window.api.post(
        window.path.yuexin + '/operator/template/kind/page',
        {
          currentPage: 1,
          pageSize: 100,
        },
        (res) => {
          if (res.code == 200) {
            this.kindsList = res.data.records
          }
        }
      )
    },
    //模板列表
    getTempList() {
      let kindIdList = [22, 23, 24, 25, 26, 27, 28, 29, 30]
      this.tableData.loading2 = true
      this.tabelAlllist.factorys = this.tabelAlllist.factorysList.join(',')
      window.api.post(
        window.path.yuexin + '/operator/template/page',
        this.tabelAlllist,
        (res) => {
          if (res.code == 200) {
            this.tableData.loading2 = false
            this.tableData.data = res.data.records
            this.tableData.total = res.data.total
            // this.tableData.data = res.data.records.map(item => {
            //     console.log(item.kindId, 'item');

            //     if (kindIdList.includes(item.kindId)) {
            //         item.newTemp = 1
            //     } else {
            //         item.newTemp = 0
            //     }
            //     return item
            // })
            // console.log(this.tableData.data, '1');
          }
        }
      )
    },
    handelTime(val) {
      if (val) {
        this.ruleForm.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.ruleForm.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.ruleForm.startTime = ''
        this.ruleForm.endTime = ''
      }
    },
    handleCopy(name, event) {
      clip(name, event)
    },
    //查询
    submitForm() {
      if (this.tableData.loading2) return; //防止重复点击
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getTempList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.ruleForm.startTime = ''
      this.ruleForm.endTime = ''
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getTempList()
    },
    //分页
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getTempList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getTempList()
    },
    // getChannels() {
    //     window.api.get(
    //         window.path.yuexin + "/operator/channelListByKindId/" + this.detail.kindId,
    //         {},
    //         (res) => {
    //             if (res.code == 200) {
    //                 this.options = res.data
    //                 // this.auditForm.channel = res.data[0].id
    //             }

    //         }
    //     );
    // },
    getList(data) {
      // const iframe = document.getElementById("myframe");
      // iframe.contentWindow.postMessage({ type: 'pages', data: res.data }, '*')
      //获取iframe元素
      let mapFrame = document.getElementById('myframe')
      if (mapFrame.attachEvent) {
        //兼容浏览器判断
        this.timer = setTimeout(
          mapFrame.attachEvent('onload', function () {
            let iframeWin = mapFrame.contentWindow
            iframeWin.postMessage(data, '*')
            //data传递的参数   *写成子页面的域名或者是ip
          }),
          1000
        )
      } else {
        this.timer = setTimeout(
          (mapFrame.onload = function () {
            let iframeWin = mapFrame.contentWindow
            iframeWin.postMessage(data, '*')
            // debugger
          }),
          1000
        )
      }
    },
    handelEdit(row) {
      let token = window.btoa(window.common.getCookie('ZTADMIN_TOKEN'))
      var tempwindow = window.open('_blank')
      tempwindow.location =
        window.path.yuexinip +
        '/#/optEditTemplate?token=' +
        token +
        '&templateID=' +
        row.id +
        '&kindId=' +
        row.kindId
    },
    //模板审核/查看
    handelExamine(row, tag) {
      let token = window.btoa(window.common.getCookie('ZTADMIN_TOKEN'))
      if (row.params) {
        this.paramsList = row.params
      }
      this.status = row.status
      this.title = row.name
      this.auditFlag = tag
      this.dialogVisible = true
      this.auditForm.templateId = row.id
      this.kindId = row.kindId
      this.flowSrc =
        window.path.yuexinip +
        '/#/preview?token=' +
        token +
        '&templateID=' +
        row.id +
        '&kindId=' +
        row.kindId
      // location.href = this.flowSrc
      window.api.get(
        window.path.yuexin + '/operator/template/' + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.detail = res.data
            let factorysList = JSON.stringify(res.data.factorys.split(','))
            this.auditForm.factorysList = JSON.parse(factorysList)
            // this.factorysList = this.auditForm.factorysList
            this.detail.channelName = row.channelName
            // var that = this;
            this.$nextTick(() => {
              let mapFrame = document.getElementById('myframe')
              if (mapFrame.attachEvent) {
                //兼容浏览器判断
                this.timer = setTimeout(
                  mapFrame.attachEvent('onload', function () {
                    let iframeWin = mapFrame.contentWindow
                    iframeWin.postMessage(res.data, '*')
                    //res.data传递的参数   *写成子页面的域名或者是ip
                  }),
                  1500
                )
              } else {
                this.timer = setTimeout(
                  (mapFrame.onload = function () {
                    let iframeWin = mapFrame.contentWindow
                    iframeWin.postMessage(res.data, '*')
                    // debugger
                  }),
                  1500
                )
              }
            })
          }
        }
      )
      if (tag == '2') {
        window.api.get(
          window.path.yuexin + '/operator/template/kind/channel/' + row.kindId,
          {},
          (res) => {
            if (res.code == 200) {
              this.options = res.data
              // this.auditForm.channel = res.data[0].id
            }
          }
        )
      }
      //     var tempwindow = window.open("_blank");
      //     tempwindow.location = 'http://yuexin.zthysms.com:3333/#/preview?token='+token+'&templateID='+row.id+'&kindId='+row.kindId+'&status='+row.status
    },
    getCorrelationTemp(row) {
      this.$prompt('请输入运营商模版ID', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入数字',
      })
        .then(({ value }) => {
          // this.$message({
          //     type: 'success',
          //     message: '你的邮箱是: ' + value
          // });
          if (!value) {
            this.$message.error('请输入运营商模版ID')
            return
          }
          window.api.get(
            window.path.yuexin +
            '/operator/template/audit/' +
            row.id +
            '/' +
            value,
            {},
            (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.getTempList()
              } else {
                this.$message.error(res.msg)
              }
            }
          )
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入',
          })
        })
    },
    // sendMessage() {
    //     this.iframeWin.postMessage({ pages: JSON.stringify(this.detail) }, '*')
    // },
    handleClose() {
      this.dialogVisible = false
      // const iframe = document.getElementById("myframe");
      // iframe.contentWindow.jtapi_Loginout("17999", "69999");
    },
    handleShare(row, share) {
      this.$confirms.confirmation(
        'post',
        '确认设置为公用模板！',
        window.path.yuexin + '/operator/template/share',
        {
          templateId: row.id,
          share,
        },
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //     message: res.msg,
            //     type: 'success'
            // });
            this.getTempList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    next() {
      if (this.active++ > 1) this.active = 0
    },
    previous() {
      this.active = 0
    },
    handelChange(e) {
      let obj = {}
      this.options.forEach((item) => {
        if (e == item.id) {
          obj.channelId = item.id
          obj.channelOperator = item.operator
        }
      })
      // console.log(obj, 'obj');
      this.auditForm.channels[0] = obj
      window.api.post(
        window.path.yuexin + '/operator/template/getChannelKindRel',
        {
          channelId: e,
          kindId: this.kindId,
        },
        (res) => {
          if (res.code == 200) {
            this.factorysList = res.data.factorys.split(',')
          } else {
            this.$message.error(res.msg)
          }
        }
      )
    },
    submitFormAudit(formName, audit) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            opinion: this.auditForm.opinion,
            templateId: this.auditForm.templateId,
            audit: audit,
            channels: this.auditForm.channels,
            factorys: this.auditForm.factorysList.join(','),
          }
          this.$confirms.confirmation(
            'post',
            '确认提交运营商？',
            window.path.yuexin + '/operator/template/audit',
            data,
            (res) => {
              if (res.code == 200) {
                // this.$message({
                //   message: res.msg,
                //   type: 'success',
                // })
                this.dialogVisible = false
                this.getTempList()
              } else {
                // this.$message.error(res.msg)
              }
            }
          )
          // window.api.post(
          //     window.path.yuexin + "/operator/template/audit",
          //     data,
          //     (res) => {
          //         if (res.code == 200) {
          //             this.$message({
          //                 message: res.msg,
          //                 type: 'success'
          //             });
          //             this.dialogVisible = false
          //             this.getTempList();
          //             // this.$parent.handleClose()
          //         } else {
          //             this.$message.error(res.msg);
          //         }

          //     }
          // );
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.active = 0
        this.flowSrc = ''
        // this.$refs.ruleFormAduit.resetFields();
        this.auditForm.opinion = ''
        this.auditForm.channel = ''
        this.auditForm.channels = []
        this.auditForm.templateId = ''
        this.auditForm.audit = ''
        this.kindId = ''
        this.auditForm.factorysList = []
        this.paramsList = []
        this.factorysList = []
        clearTimeout(this.timer)
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}

.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}

.auditinfo {
  width: 600px;
  margin-left: 20px;
}

.tempMes {}

.image {
  margin: 0 5px;
  line-height: 1.5;
  font-size: 12px;
}

.img {
  width: 25px;
  height: 25px;
}

.border-a {
  border-radius: 50%;
}
</style>

<style>
.el-descriptions__body {
  color: #555;
  background-color: #eee;
  padding: 5px;
  margin: 10px 0;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>