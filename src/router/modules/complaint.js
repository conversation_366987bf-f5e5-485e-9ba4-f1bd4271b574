// 投诉控制相关路由
export default [
  {
    path: '/routerSetting',
    meta: { title: ' 路由设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/ShuntSetting.vue'),
  },
  {
    path: '/signatureFailurcode',
    meta: { title: ' 签名失败代码', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/signatureFailurcode.vue'),
  },
  {
    path: '/shortTask',
    meta: { title: '提取短链任务', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/shortTask.vue'),
  },
  {
    path: '/successRateMonitor',
    meta: { title: '后置路由明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/successRateMonitor.vue'),
  },
  {
    path: '/mobileCheck',
    meta: { title: '空号检测明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/mobileCheck.vue'),
  },
  {
    path: '/shortStatistics',
    meta: { title: '短链任务明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/shortStatistics.vue'),
  },
  {
    path: '/shorCodetStatistics',
    meta: { title: '短链点击详情', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/shorCodetStatistics.vue'),
  },
  {
    path: '/routerdDetail',
    meta: { title: '前置路由明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/ShuntDetailed.vue'),
  },
  {
    path: '/similarMobile',
    meta: { title: '号码频次限制', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/similarMobile.vue'),
  },
  {
    path: '/recoveryDataSetting',
    meta: { title: '回复数据设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/recoveryDataSetting.vue'),
  },
  {
    path: '/recoveryDataDetail',
    meta: { title: '回复数据明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/recoveryDataDetail.vue'),
  },
  {
    path: '/Marketing',
    meta: { title: ' 营销黑号', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/Marketing.vue'),
  },
  {
    path: '/receiptList',
    meta: { title: '回执补偿设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/ComplaintControl/receiptList.vue'),
  },
] 