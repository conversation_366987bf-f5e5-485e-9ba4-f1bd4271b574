<template>
  <div>
    <div class="OuterFrame fillet">
      <div class="Signature-search-fun">
        <el-button type="primary" @click="addopt">新增</el-button>
        <el-button type="danger" @click="bathdel()" v-if="selectId.length != 0"
          >批量删除</el-button
        >
        <el-button type="info" @click="showTagDescription">标签说明</el-button>
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        >
          <el-table-column type="selection" width="46"></el-table-column>
          <el-table-column label="标签名称">
            <template v-slot="scope">
              <div class="wrapper-text" v-if="scope.row.tagName">
                <el-tag
:disable-transitions="true"
                  style="margin: 5px"
                  v-for="(item, index) in scope.row.tagName.split(',')"
                  :key="index + 'a'"
                  type="primary"
                  effect="dark"
                >
                  {{ item }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建人">
            <template v-slot="scope">{{ scope.row.createName }}</template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">
              <span v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</span></template
            >
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template v-slot="scope">
              <el-button
                link
                style="color: red"
                @click="cancel(scope.row)"
                >删 除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap + '(' + formop.username + ')'"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="40%"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <!-- <el-form-item label="用户名称 " prop="username">
                <el-input disabled v-model="formop.username" class="input-w"></el-input>
              </el-form-item> -->
          <el-form-item label="标签名称" prop="tagName">
            <input-tag
              v-on:childLabelEvent="handChildLabel"
              :t_data="tagList"
            />
            <!-- <el-input v-model="formop.tagName" class="input-w" placeholder="标签以逗号分隔区分;例如：xxx,xxx"></el-input> -->
            <!-- <span style="color: #909399;font-size: 12px;">标签以逗号分隔区分</span> -->
          </el-form-item>
          <!-- <el-form-item label="备注" prop="remark">
                <el-input v-model="formop.remark" class="input-w" type="textarea"></el-input>
              </el-form-item> -->
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增 -->

      <!-- 标签说明弹窗 -->
      <el-dialog
        title="用户标签功能说明"
        v-model="tagDescriptionVisible"
        width="70%"
        :close-on-click-modal="false"
      >
        <div class="tag-description-content">
          <!-- <div class="description-header">
            <p style="font-size: 16px; font-weight: bold; margin-bottom: 20px; color: #333;">
              UserTagEnum 是一个枚举类，定义了不同的功能标签，用于描述与账号相关的各种设置或状态。以下是各个标签的功能说明：
            </p>
          </div> -->
          
          <div class="tag-list">
            <div class="tag-item" v-for="(tag, index) in tagDescriptions" :key="index">
              <div class="tag-header">
                <span class="tag-number">{{ index + 1 }}.</span>
                <el-tag type="primary" size="large" class="tag-name">{{ tag.name }}</el-tag>
                <!-- <span class="tag-english">({{ tag.english }})</span> -->
              </div>
              <div class="tag-content">
                <strong>功能：</strong>{{ tag.description }}
              </div>
            </div>
          </div>

          <div class="description-footer">
            <p style="font-size: 14px; color: #666; margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 6px;">
              💡 <strong>说明：</strong>以上功能标签用于标识账号的不同用途、权限或配置。通过这些标签，系统可以更方便地管理不同的账号行为和功能，确保短信平台的运行更为高效、灵活。
            </p>
          </div>
        </div>
        
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button @click="tagDescriptionVisible = false">关 闭</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 标签说明弹窗 -->
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import inputTag from '@/components/publicComponents/InputTag.vue'
import moment from 'moment'
export default {
  name: 'tagMang',
  components: {
    TableTem,
    inputTag,
  },
  data() {
    var numberRules = (rule, value, callback) => {
      if (!/^([0-9][0-9]{0,3}|100)$/.test(value)) {
        return callback(new Error('请输入0-100的数字'))
      } else {
        callback()
      }
    }
    return {
      titleMap: '新增',
      isFirstEnter: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      tagDescriptionVisible: false, //标签说明弹窗显示隐藏
      ids: '',
      selectId: [],
      expireTime: '',
      formop: {
        username: '',
        tagName: '',
        remark: '',
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名称', trigger: 'change' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        tagName: [
          { required: true, message: '请输入标签名称', trigger: 'change' },
        ],
      },
      // formInline: {
      //   username: "",
      //   tagName: "",
      //   currentPage: 1,
      //   pageSize: 10
      // },
      tabelAlllist: {
        //存储查询数据
        username: '',
        tagName: '',
        currentPage: 1,
        pageSize: 10,
      },
      tagList: [],
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
      // 标签功能说明数据
      tagDescriptions: [
        {
          name: '不自动补发',
          english: 'NOT_AUTO_SEND',
          description: '标识该账号不进行自动补发操作。适用于不需要系统自动重发消息的场景。'
        },
        {
          name: '账号扩展',
          english: 'ACCOUNT_EXTENSION',
          description: '表示使用的是账号扩展。'
        },
        {
          name: 'cmpp',
          english: 'CMPP_ACCOUNT',
          description: '该标签标识账号为CMPP协议账号。CMPP（China Mobile Peer-to-Peer）是一个通信协议，通常用于短信服务。'
        },
        {
          name: '渠道',
          english: 'CHANNEL_USER',
          description: '标识该账号属于某个渠道用户。'
        },
        {
          name: '压测',
          english: 'SEND_TEST',
          description: '标识该账号用于压测（性能测试）。'
        },
        {
          name: '凌晨免监控名单',
          english: 'NOT_AUDIT',
          description: '该标签表示该账号不参与凌晨时段的监控审核。'
        },
        // {
        //   name: '智能检测',
        //   english: 'MOBILE_DETECT',
        //   description: '该账号启用了智能检测功能，用于检测短信是否合规或是否存在风险等。'
        // },
        // {
        //   name: '定时发送',
        //   english: 'TIME_RANGE_SEND',
        //   description: '表示该账号支持定时发送消息。适用于需要在指定时间内发送的场景。'
        // },
        {
          name: '追加签名',
          english: 'APPEND_SIGN',
          description: '表示该账号支持在短信或消息中追加签名。'
        },
        {
          name: '去ZT前缀',
          english: 'REMOVE_ZT_CODE',
          description: '该标签表示需要去除ZT前缀，用于对状态码进行格式化处理。'
        },
        {
          name: '电信390回执',
          english: 'TELECOM_390_RECEIPT',
          description: '表示该账号需要处理电信的390回执。此回执用于电信运营商与短信平台之间的通信确认。'
        }
      ]
    }
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerClient/tag/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    getUserInfo() {
      window.api.get(
        window.path.omcs + 'operatinguser/userInfo/' + this.$route.query.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.tabelAlllist.username = res.data.consumerName
            this.formop.username = res.data.consumerName
            localStorage.setItem('consumerName', res.data.consumerName)
            this.gettableLIst()
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      // Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst()
    },
    handChildLabel(data) {
      this.formop.tagName = data.join(',')
      // console.log(this.configurationItem.formData.label,'data');
    },
    // 显示标签说明
    showTagDescription() {
      this.tagDescriptionVisible = true
    },
    // 新增 编辑
    addopt() {
      this.titleMap = '新增'
      let username = localStorage.getItem('consumerName')
      if (username) {
        this.formop.username = username
      }
      // this.formop.username = this.$route.query.username
      this.dialogFormVisible = true
    },
    edit(val) {
      this.titleMap = '编辑'
      this.dialogFormVisible = true
      this.formop.username = val.username
      this.formop.tagName = val.tagName
      this.formop.remark = val.remark
      this.formop.id = val.id
      this.tagList = val.tagName.split(',')
      //清除编辑默认值
      // this.$nextTick(() => {
      //   this.$refs.formop.resetFields();
      //   Object.assign(this.formop, val);
      // })
    },
    //   handleTime(e){
    //     this.formop.expireTime = moment(e).format('YYYY-MM-DD HH:mm:ss')
    //   },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            this.titleMap == '新增' ? 'post' : 'put',
            this.titleMap == '新增' ? '确定新增操作？' : '确定编辑操作？',
            window.path.omcs + 'consumerClient/tag',
            this.formop,
            (res) => {
              if (res.code == 200) {
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除
    cancel(val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs +
          'consumerClient/tag?ids=' +
          val.id +
          '&userId=' +
          this.$route.query.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    //批量
    handleSelectionChange(val) {
      this.selectId = val.map((item, index) => {
        return item.id
      })
      this.ids = this.selectId.join(',')
    },
    bathdel() {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs +
          'consumerClient/tag?ids=' +
          this.ids +
          '&userId=' +
          this.$route.query.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getUserInfo()
      // this.gettableLIst();
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getUserInfo()
        // this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false;
    }
  },
  deactivated() {
    this.tableDataObj.tableData = []
  },
  watch: {
    // '$route.query.id': {
    //   handler(newVal, oldVal) {
    //     console.log(newVal);
    //     //判断newVal有没有值监听路由变化
    //     if (newVal) {
    //       this.getUserInfo()
    //     }
    //   },
    //   deep: true,
    //   immediate: true, // 初次监听即执行
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
        // this.formop.username = ''
        this.formop.tagName = ''
        this.formop.remark = ''
        this.formop.id = ''
        this.tagList = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}

/* 标签说明弹窗样式 */
.tag-description-content {
  max-height: 70vh;
  overflow-y: auto;
}

.tag-list {
  margin: 20px 0;
}

.tag-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.tag-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.tag-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.tag-number {
  font-weight: bold;
  font-size: 16px;
  color: #409eff;
  margin-right: 10px;
  min-width: 25px;
}

.tag-name {
  margin-right: 10px;
  font-weight: bold;
}

.tag-english {
  font-size: 14px;
  color: #909399;
  font-style: italic;
}

.tag-content {
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  padding-left: 35px;
}

.tag-content strong {
  color: #333;
}
</style>
