<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名" label-width="80px" prop="clientName">
              <el-input
                v-model="formInline.clientName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道号" label-width="80px" prop="channelId">
              <el-input
                v-model="formInline.channelId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="错误码" label-width="80px" prop="code">
              <el-input
                v-model="formInline.code"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="统计时间" label-width="80px" prop="flag">
              <el-select clearable class="input-w" v-model="formInline.flag">
                <el-option label="最近1小时" value="1"></el-option>
                <el-option label="最近3小时" value="2"></el-option>
                <el-option label="最近6小时" value="3"></el-option>
              </el-select>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click="ListSearch"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>

        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- 表格和分页开始 -->
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="errorCode"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData">

            <vxe-column field="用户名" title="用户名">
              <template v-slot="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.clientName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.clientName}}
                                    </span>
                                </el-popover> -->
                <div>{{ scope.row.clientName }}</div>
              </template>
            </vxe-column>
            <vxe-column field="通道号" title="通道号">
              <template v-slot="scope">
                <div
                  @click="save(scope.row.channelId)"
                  style="color: rgb(22, 165, 137); cursor: pointer"
                  >{{ scope.row.channelId }}</div
                >
              </template>
            </vxe-column>
            <vxe-column field="错误码" title="错误码">
              <template v-slot="scope">
                <div>{{ scope.row.code }}</div>
              </template>
            </vxe-column>
            <vxe-column field="错误数" title="错误数">
              <template v-slot="scope">
                <div>{{ scope.row.num }}</div>
              </template>
            </vxe-column>
            <!-- <vxe-column field="操作" title="操作" width='160'>
                                    <template #default="scope">
                                        <el-button type="text" style="color:#F56C6C"  @click="delTem(scope.$index, scope.row)"><i class="el-icon-error"></i>&nbsp;删除</el-button>
                                    </template>
                                </vxe-column> -->
          </vxe-table>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0"
            > -->
          
          <div class="paginationBox">
            <el-pagination
              style="float: right"
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import UserLists from '@/components/publicComponents/userList.vue'
export default {
  components: {
    TableTem,
    ChannelView,
    UserLists
  },
  name: 'errorCode',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      userFlag: false,
      nameover: '',
      ChannelData: '', //传递通道值
      // 搜索数据
      formInline: {
        channelId: '',
        clientName: '',
        code: '',
        flag: '1',
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        channelId: '',
        clientName: '',
        flag: '1',
        code: '',
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'smsMessage/atuoReissue/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 重置
    Reset(formName) {
      console.log(1)
      this.$refs[formName].resetFields()
      this.formInline.begTime = ''
      this.formInline.endTime = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
    // // 分页
    // handleSizeChange(size) {
    //     this.formInlines.pageSize = size;
    // },
    // handleCurrentChange: function(currentPage){
    //     this.formInlines.currentPage = currentPage;
    // },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines: {
    //   handler() {
    //     this.InquireList();
    //   },
    //   deep: true,
    //   immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
