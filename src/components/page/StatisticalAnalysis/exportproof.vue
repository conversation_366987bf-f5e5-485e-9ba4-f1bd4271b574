<template>
  <div class="container_left">
    <div class="fillet shortChain-box">
      <div class="short-box">
        <div>
          <el-form
            :inline="true"
            :model="formInline"
            label-width="80px"
            class="demo-form-inline"
            ref="queryForm"
          >
            <!-- <el-form-item label="用户名" prop="username">
                                <el-input v-model="formInline.username" placeholder="" class="input-w"></el-input>
                            </el-form-item> -->
            <el-form-item label="企业Id" prop="compId">
              <el-input
                v-model="formInline.compId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select class="input-w" v-model="formInline.status" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="开启" value="0"></el-option>
                <el-option label="关闭" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="time">
              <el-date-picker
                type="daterange"
                v-model="formInline.time"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="input-time"
                @change="handleTimeChange"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain style="" @click="Query"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reload('queryForm')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="short-btn">
          <!-- <el-button @click="openShort()" v-if="shortStatus != 2" :disabled="shortStatus== 1 ? true : false" type="primary">开通短链</el-button>
                        <el-button v-if="shortStatus == 2" type="primary">添加白名单</el-button> -->
        </div>
        <div class="Mail-table Mail-table1">
          <!-- 表格和分页开始 -->
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="exportproof"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handleSelectionChange"
            @checkbox-change="handleSelectionChange">

            <!-- <vxe-column field="" title="用户名">
                                <template #default="scope">
                                    <span>{{ scope.row.username }}</span>
                                </template>
                            </vxe-column> -->
            <vxe-column field="企业Id" title="企业Id">
              <template v-slot="scope">
                <div>{{ scope.row.compId }}</div>
              </template>
            </vxe-column>
            <vxe-column field="企业名称" title="企业名称">
              <template v-slot="scope">
                <div>{{ scope.row.companyName }}</div>
              </template>
            </vxe-column>
            <vxe-column field="邮箱" title="邮箱">
              <template v-slot="scope">
                <div>{{ scope.row.email }}</div>
              </template>
            </vxe-column>
            <vxe-column field="状态" title="状态">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true" type="info" effect="dark" v-if="scope.row.status == '1'"
                  >关闭</el-tag
                >
                <el-tag
                  :disable-transitions="true"
                  type="success"
                  effect="dark"
                  v-else-if="scope.row.status == '0'"
                  >开启</el-tag
                >
                <!-- <span>{{ scope.row.email }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="开启时间" title="开启时间">
              <template v-slot="scope">
                <div>{{
                  scope.row.beginTime
                    ? moment(scope.row.beginTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</div>
              </template>
            </vxe-column>
            <vxe-column field="结束时间" title="结束时间">
              <template v-slot="scope">
                <div>{{
                  scope.row.endTime
                    ? moment(scope.row.endTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</div>
              </template>
            </vxe-column>
            <vxe-column field="创建时间" title="创建时间 ">
              <template v-slot="scope">
                <div>{{
                  scope.row.createTime
                    ? moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : ''
                }}</div>
              </template>
            </vxe-column>
          </vxe-table>
          <!--分页-->
          <!-- <div
            style="
              display: flex;
              justify-content: space-between;
              margin-top: 10px;
            "
          >
            <div></div> -->
          
          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.currentPage"
              :page-size="formInline.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            >
            </el-pagination>
          </div>

          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'shortApply',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      shortStatus: 0, //短链状态
      dialogVisible: false, //审核不通过弹窗
      formInline: {
        // username: "",
        beginTime: '',
        endTime: '',
        compId: '',
        status: '',
        time: [],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        total: 0, //总条数
      },
    }
  },
  created() {
    this.getShortList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  methods: {
    //获取短链列表
    getShortList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumersmsexportproof/page',
        this.formInline,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.total = res.data.total
            this.tableDataObj.loading2 = false
          }
        }
      )
    },
    //查询
    Query() {
      this.getShortList()
    },
    //重置
    Reload(formName) {
      this.$refs[formName].resetFields() //清空查询表单
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.getShortList()
    },
    //-----复制功能
    // handleCopy(name, event) {
    //     clip(name, event)
    // },
    //-----翻页操作
    handleSizeChange(size) {
      this.formInline.pageSize = size
      this.getShortList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInline.currentPage = currentPage
      this.getShortList()
    },
    handleTimeChange(val) {
      if (val) {
        this.formInline.beginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.formInline.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    //全选
    handleSelectionChange() {},
  },
}
</script>

<style lang="less" scoped>
.shortChain-box {
  padding: 20px;
}
.shortChain-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  font-size: 12px;
}
.shortChain-matter > p {
  padding: 5px 0px;
}
.short-box {
  margin: 10px 0;
}
.short-btn {
  margin-bottom: 10px;
}
:deep(.el-table__row) {
  height: 48px;
}
</style>
