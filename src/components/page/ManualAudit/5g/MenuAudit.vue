<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form
        label-width="80px"
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名称" prop="createdUser">
          <el-input class="input-w" v-model="ruleForm.createdUser"></el-input>
        </el-form-item>
        <el-form-item label="菜单Id" prop="menuId">
          <el-input class="input-w" v-model="ruleForm.menuId"></el-input>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="MenuAudit"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="用户名称" title="用户名称">
          <template v-slot="scope">
            <div>
              {{ scope.row.createdUser }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="chatbot名称" title="chatbot名称">
          <template v-slot="scope">
            <div>
              {{ scope.row.chatbotName }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="菜单ID" title="菜单ID">
          <template v-slot="scope">
            <div>
              {{ scope.row.menuId }}
            </div>
          </template>
        </vxe-column>
        <!-- <vxe-column field="" title="菜单模板">
              <template #default="scope">
                  <i style="cursor: pointer; color: #406eff" class="el-icon-picture"></i>
                <span
                  style="cursor: pointer; color: #406eff"
                  @click="checkContent(scope.row)"
                  >点击预览</span
                >
              </template>
            </vxe-column> -->
        <vxe-column field="审核状态" title="审核状态">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f6f6f6;
                color: #999999;
                border: 1px solid #dddddd;
              "
              v-if="scope.row.menuStatus == 0"
            >
              未审核
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.menuStatus == 5"
            >
              审核中
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.menuStatus == 10"
            >
              运营商审核中
            </div>
            <div
              class="item_status"
              style="
                background: #ffe7e7;
                color: #ff6666;
                border: 1px solid #fac2c2;
              "
              v-if="scope.row.menuStatus == 20"
            >
              审核失败
            </div>
            <div
              class="item_status"
              style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              "
              v-if="scope.row.menuStatus == 30"
            >
              审核通过
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间">
          <template v-slot="scope">
            <!-- <p v-if="scope.row.createdTime">{{ $parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}</p>
                <p v-if="scope.row.createdTime">{{ $parseTime(scope.row.createdTime, '{h}:{i}:{s}') }}</p> -->
            <div>
              {{ moment(scope.row.createdTime).format('YYYY-MM-DD HH:mm:ss') }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" width="200">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.menuStatus == 30"
              link
              style="color: #16a589; margin-left: 0px"
              @click="checkContent(scope.row)"
              ><el-icon><SuccessFilled /></el-icon>查看固定菜单</el-button
            >
            <el-button
              v-else
              link
              style="color: #e6a23c; margin-left: 0px"
              @click="checkContent(scope.row)"
              ><el-icon><WarningFilled /></el-icon>固定菜单审核</el-button
            >
            <el-button
             style="color: #409eff; margin-left: 0px"
             link @click="menuAudit(scope.row)"
              >固定菜单报备</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>

      <div class="paginationBox">
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="60%"
      :before-close="handleClose"
    >
      <Menus :lists="detial" />
      <div
        style="display: flex; margin: 10px 0"
        v-if="detial.menuStatus == 0 || detial.menuStatus == 5"
      >
        <el-form
          :model="reasonFrom"
          :inline="true"
          :rules="rules"
          ref="reasonFrom"
          class="demo-reasonFrom"
        >
          <el-form-item label="审核原因：" prop="reason">
            <el-input
              :maxlength="20"
              show-word-limit
              type="textarea"
              placeholder="不能超过20个字符"
              style="width: 500px"
              v-model="reasonFrom.reason"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div
        v-else
        style="
          width: 100%;
          background: #f2f5fa;
          line-height: 30px;
          display: flex;
          margin-top: 10px;
        "
      >
        <div style="padding: 5px">
          <div>
            <span>审核状态：</span>
            <span v-if="detial.menuStatus == 0">未审核</span>
            <span style="color: red" v-if="detial.menuStatus == 5">审核中</span>
            <span style="color: red" v-if="detial.menuStatus == 10"
              >运营商审核中</span
            >
            <span style="color: red" v-if="detial.menuStatus == 20"
              >审核失败</span
            >
            <span style="color: #409eff" v-if="detial.menuStatus == 30"
              >审核通过</span
            >
          </div>
          <div>
            <span>审核人：</span>
            <span>{{ detial.auditUser }}</span>
          </div>
        </div>
        <div style="padding: 5px; margin-left: 30px">
          <div>
            <span>审核原因：</span>
            <span>{{ detial.auditReason }}</span>
          </div>
          <div>
            <span>审核时间：</span>
            <span v-if="detial.auditTime">{{
              moment(detial.auditTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            v-if="detial.menuStatus == 0 || detial.menuStatus == 5"
            type="primary"
            @click="audit(detial, 1, '通过', 'reasonFrom')"
          >
            通过</el-button
          >
          <el-button
            v-if="detial.menuStatus == 0 || detial.menuStatus == 5"
            type="danger"
            style="margin-left: 10px"
            @click="audit(detial, 2, '不通过', 'reasonFrom')"
            >不通过</el-button
          >
          <!-- <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Menus from './components/menu.vue'
export default {
  components: {
    Menus,
    // ElIconLxEmoji,
    // ElIconSuccess,
    // ElIconWarning,
  },
  name: 'MenuAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      status: '',
      title: '',
      reason: '',
      dialogVisible: false,
      codeFlag: true,
      ruleForm: {
        menuId: '',
        createdUser: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        menuId: '',
        createdUser: '',
        currentPage: 1,
        pageSize: 10,
      },
      reasonFrom: {
        reason: '',
      },
      rules: {
        reason: [
          { required: true, message: '审核原因不能为空', trigger: 'blur' },
        ],
      },
      detial: {},
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getchatList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/menu/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    checkContent(row) {
      this.dialogVisible = true
      this.title = '固定菜单模板审核'
      this.detial = row
    },

    menuAudit(row) {
      this.$router.push({
        path: '/MenuChannel',
        query: { menuId: row.menuId },
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    checked(row) {
      this.dialogVisible = true
      this.registerFrom = row
      this.registerFrom.renewed = row.renewed + ''
      this.registerFrom.industryTypeCode = row.industryTypeCode * 1
      this.status = row.customerStatus
      //   console.log(this.registerFrom );
      if (row.code) {
        this.codeFlag = true
      } else {
        this.codeFlag = false
      }
    },
    audit(row, val, tit, from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            `初次审核菜单模板，请注意菜单内容！`,
            window.path.fiveWeb + '/operator/menu/audit',
            {
              audit: val,
              menuId: row.menuId,
              reason: this.reasonFrom.reason,
            },
            (res) => {
              if (res.code == 200) {
                this.dialogVisible = false
                // this.$message({
                //   type: 'success',
                //   message: res.msg,
                // })
                if (val == 1) {
                  this.$router.push({
                    path: '/MenuChannel',
                    query: { menuId: row.menuId },
                  })
                }

                this.getchatList()
              } else {
                // this.$message({
                //   type: 'error',
                //   message: res.msg,
                // })
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      // console.log(val);
      if (!val) {
        if (this.$refs['reasonFrom']) this.$refs['reasonFrom'].resetFields()
        // this.detial = {}
        // console.log(this.detial,'ok');
        //   console.log(111);
        // this.$refs["registerFrom"].resetFields();
        // this.registerFrom.customerId = "";
        // this.registerFrom.customerStatus = "";
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>


<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
