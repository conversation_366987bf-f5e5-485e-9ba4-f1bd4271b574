<template>
  <div class="bag">
    <div style="padding: 20px">
      <!-- <div class="alert">
            <div>
              <i style="color: #3199f5" class="el-icon-warning"></i
              ><span>待发布</span>
            </div>
            <div>点击<span>这里</span>，查看当前发布成功的菜单</div>
          </div> -->
    </div>
    <div class="contents">
      <div class="left">
        <div class="menu-box">
          <div class="icons">
            <i class="iconfont icon-jianpan" style="font-size: 30px"></i>
          </div>
          <div class="items_menu" v-for="(item, index) in list" :key="index">
            <div v-if="pIndex == index" class="item_child">
              <div
                v-for="(child, idx) in item.subMenu"
                :class="[cIndex == idx && pIndex == index ? 'active' : '']"
                :key="idx"
                @click="clickMenu(index, idx)"
              >
                <div style="border: 2px solid #eee">
                  {{ child.name }}
                </div>
              </div>
            </div>
            <div
              :class="[pIndex == index && cIndex == -1 ? 'active' : '']"
              @click="clickMenu(index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="right" v-if="tag != 0"></div>
      <div class="right" v-else>
        <div class="right_item" v-for="(item, index) in list" :key="index">
          <p>主菜单：{{ item.name }}</p>
          <div>
            <p v-if="item.functionCode == 'suggestion'">菜单功能：关键词</p>
            <p v-if="item.functionCode == 'url'">菜单功能：链接地址</p>
            <p v-if="item.functionCode == 'app'">菜单功能：打开APP</p>
            <p v-if="item.functionCode == 'dial'">菜单功能：拨打电话</p>
            <p v-if="item.functionCode == 'send'">菜单功能：发送消息</p>
            <p v-if="item.functionCode == 'show_location'">
              菜单功能：展示地理位置
            </p>
            <p v-if="item.functionCode == 'search_location'">
              菜单功能：搜索地理位置
            </p>
            <p v-if="item.functionCode == 'upcoming'">菜单功能：创建待办</p>
          </div>
          <!-- <div>
                <p v-if="item.functionCode == 'suggestion'">
                  关键词：
                      <el-tag
:disable-transitions="true"
                        effect="dark"
                      >
                        {{ item.content }}
                      </el-tag>
                </p>
                <p v-if="item.functionCode == 'url'">
                  链接地址：<a
                        :href="item.content"
                        target="_blank"
                        rel="noopener noreferrer"
                        >{{ item.content }}</a
                      >
                </p>
                <p v-if="item.functionCode == 'dial'">
                  拨打电话：{{ item.content }}
                </p>
              </div> -->
          <div>
            <p v-if="item.functionCode == 'suggestion'">
              关键词：{{ item.content.keyWord }}
            </p>
            <p v-if="item.functionCode == 'url'">
              链接地址：{{ item.content.link }}
            </p>
            <p v-if="item.functionCode == 'app'">
              链接地址：{{ item.content.link }}
            </p>
            <p v-if="item.functionCode == 'dial'">
              拨打电话：{{ item.content.called }}
            </p>
            <div v-if="item.functionCode == 'send'">
              <p>接受号码：{{ item.content.receiveNumber }}</p>
              <p>
                类型：<span v-if="item.content.receiveType == 1">文本</span
                ><span v-else-if="item.content.receiveType == 2">视频</span
                ><span v-else>音频</span>
              </p>
              <p>内容：{{ item.content.receiveContent }}</p>
            </div>
            <div v-if="item.functionCode == 'show_location'">
              <p>经度：{{ item.content.longitude }}</p>
              <p>纬度：{{ item.content.latitude }}</p>
              <p>内容：{{ item.content.locationName }}</p>
            </div>
            <div v-if="item.functionCode == 'search_location'">
              <p>关键字：{{ item.content.searchKey }}</p>
            </div>
            <div v-if="item.functionCode == 'upcoming'">
              <p>
                起始时间：{{
                  moment(item.content.upcomingStartTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                }}
              </p>
              <p>
                终止时间：{{
                  moment(item.content.upcomingEndTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )
                }}
              </p>
              <p>标题：{{ item.content.upcomingTitle }}</p>
              <p>描述：{{ item.content.upcomingDesc }}</p>
            </div>
          </div>
          <div v-if="item.subMenu.length">
            <div
              class="sub_item"
              v-for="(it, ix) in item.subMenu"
              :key="ix + 'a'"
            >
              <p>子菜单：{{ it.name }}</p>
              <div>
                <p v-if="it.functionCode == 'suggestion'">菜单功能：关键词</p>
                <p v-if="it.functionCode == 'url'">菜单功能：链接地址</p>
                <p v-if="it.functionCode == 'dial'">菜单功能：拨打电话</p>
                <p v-if="it.functionCode == 'send'">菜单功能：发送消息</p>
                <p v-if="it.functionCode == 'show_location'">
                  菜单功能：展示地理位置
                </p>
                <p v-if="it.functionCode == 'search_location'">
                  菜单功能：搜索地理位置
                </p>
                <p v-if="it.functionCode == 'upcoming'">菜单功能：创建待办</p>
              </div>
              <div>
                <p v-if="it.functionCode == 'suggestion'">
                  关键词：{{ it.content.keyWord }}
                </p>
                <p v-if="it.functionCode == 'url'">
                  链接地址：{{ it.content.link }}
                </p>
                <p v-if="it.functionCode == 'dial'">
                  拨打电话：{{ it.content.called }}
                </p>
                <div v-if="it.functionCode == 'send'">
                  <p>接受号码：{{ it.content.receiveNumber }}</p>
                  <p>
                    类型：<span v-if="it.content.receiveType == 1">文本</span
                    ><span v-else-if="it.content.receiveType == 2">视频</span
                    ><span v-else>音频</span>
                  </p>
                  <p>内容：{{ it.content.receiveContent }}</p>
                </div>
                <div v-if="it.functionCode == 'show_location'">
                  <p>经度：{{ it.content.longitude }}</p>
                  <p>纬度：{{ it.content.latitude }}</p>
                  <p>内容：{{ it.content.locationName }}</p>
                </div>
                <div v-if="it.functionCode == 'search_location'">
                  <p>关键字：{{ it.content.searchKey }}</p>
                </div>
                <div v-if="it.functionCode == 'upcoming'">
                  <p>
                    起始时间：{{
                      moment(it.content.upcomingStartTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    }}
                  </p>
                  <p>
                    终止时间：{{
                      moment(it.content.upcomingEndTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )
                    }}
                  </p>
                  <p>标题：{{ it.content.upcomingTitle }}</p>
                  <p>描述：{{ it.content.upcomingDesc }}</p>
                </div>
              </div>
              <!-- <div>
                    <p v-if="it.functionCode == 'suggestion'">
                      关键词：
                      <el-tag
:disable-transitions="true"
                        effect="dark"
                      >
                        {{ it.content }}
                      </el-tag>

                  
                    </p>
                    <p v-if="it.functionCode == 'url'">
                      链接地址：<a
                        :href="it.content"
                        target="_blank"
                        rel="noopener noreferrer"
                        >{{ it.content }}</a
                      >
                    </p>
                    <p v-if="it.functionCode == 'dial'">
                      拨打电话：{{ it.content }}
                    </p>
                  </div> -->
            </div>
          </div>
        </div>
        <!-- <div v-if="pIndex != -1 || cIndex != -1">
              <p v-if="form.name">菜单名称：{{ form.name }}</p>
              <div>
                <p v-if="form.functionCode == 'suggestion'">菜单功能：关键词</p>
                <p v-if="form.functionCode == 'url'">菜单功能：链接地址</p>
                <p v-if="form.functionCode == 'dial'">菜单功能：拨打电话</p>
              </div>
              <div>
                <p v-if="form.functionCode == 'suggestion'">
                  关键词：{{ form.content }}
                </p>
                <p v-if="form.functionCode == 'url'">
                  链接地址：{{ form.content }}
                </p>
                <p v-if="form.functionCode == 'dial'">
                  拨打电话：{{ form.content }}
                </p>
              </div>
            </div> -->
      </div>
    </div>
    <!-- <div class="footer" v-if="tag != 0">
          <el-button type="primary" @click="goback">返回</el-button>
          <el-button type="primary" @click="save">存草稿</el-button>
          <el-button type="primary" @click="saveOpen">保存并发布</el-button>
        </div>
        <div class="footer" v-else>
          <el-button type="primary" @click="edit">编辑</el-button>
        </div> -->
  </div>
</template>

<script>
export default {
  props: ["lists"],
  data() {
    return {
      tag: 0,
      chatbotId: "",
      menuId: "",
      count: 0,
      countC: 0,
      list: [],
      pIndex: -1,
      cIndex: -1,
      form: {
        name: "", //主菜单名称
        functionCode: "", //类型
        content: {
          link: "", //链接
          keyWord: "", //关键词
          called: "", //被叫号码
          receiveNumber: "", //接受号码
          receiveType: "", //类型
          receiveContent: "", //接受内容
          longitude: "", //经度
          latitude: "", //纬度
          locationName: "", //位置名称
          searchKey: "", //搜索关键字
          upcomingTitle: "", //代办标题
          upcomingDesc: "", //代办描述
          upcomingStartTime: "", //代办开始时间
          upcomingEndTime: "", //代办结束时间
        }, //如果有子菜单主菜单没有功能
        subMenu: [],
      },
      objform: {
        name: "菜单名称", //主菜单名称
        functionCode: "", //类型
        content: {
          link: "", //链接
          keyWord: "", //关键词
          called: "", //被叫号码
          receiveNumber: "", //接受号码
          receiveType: "", //类型
          receiveContent: "", //接受内容
          longitude: "", //经度
          latitude: "", //纬度
          locationName: "", //位置名称
          searchKey: "", //搜索关键字
          upcomingTitle: "", //代办标题
          upcomingDesc: "", //代办描述
          upcomingStartTime: "", //代办开始时间
          upcomingEndTime: "", //代办结束时间
        }, //如果有子菜单主菜单没有功能
        subMenu: [],
      },
      childObj: {
        name: "子菜单名称", //子菜单名称
        functionCode: "", //子菜单功能
        content: {
          link: "", //链接
          keyWord: "", //关键词
          called: "", //被叫号码
          receiveNumber: "", //接受号码
          receiveType: "", //类型
          receiveContent: "", //接受内容
          longitude: "", //经度
          latitude: "", //纬度
          locationName: "", //位置名称
          searchKey: "", //搜索关键字
          upcomingTitle: "", //代办标题
          upcomingDesc: "", //代办描述
          upcomingStartTime: "", //代办开始时间
          upcomingEndTime: "", //代办结束时间
        },
      },
      rules: {
        name: [
          { required: true, message: "请选择模板名称", trigger: "change" },
        ],
        functionCode: [
          { required: true, message: "请选择模板名称", trigger: "change" },
        ],
      },
      delflag: 0,
    };
  },
  created() {
    // this.chatbotId = localStorage.getItem("charbotId");
    // this.getList();

    this.list = JSON.parse(this.lists.menuContent);
    if (this.list.length) {
      this.form = this.list[0];
      this.pIndex = 0;
    }
  },
  methods: {
    // getList() {
    //   window.api.get(
    //       window.path.fiveWeb + "/menu/"+this.chatbotId,
    //       {},
    //       (res) => {
    //           if(res.code == 200){
    //             this.menuId = res.data.menuId;
    //               this.list = JSON.parse(res.data.menuContent)
    //               if(this.list.length){
    //                 this.form = this.list[0];
    //                 this.pIndex = 0;
    //             }
    //           }

    //       }
    //     );
    //   // window.api.post(window.path.fiveWeb + "/menu/page", {
    //   //   chatbotId:this.chatbotId
    //   // }, (res) => {
    //   //   if (res.data.records.length) {
    //   //     res.data.records.forEach((item) => {
    //   //       // console.log(item);
    //   //       this.menuId = item.menuId;
    //   //     });
    //   //     this.list = JSON.parse(res.data.records[0].menuContent);

    //   //     if(this.list.length){
    //   //         this.form = this.list[0];
    //   //         this.pIndex = 0;
    //   //     }

    //   //   }
    //   // });
    // },
    addMenu() {
      let row = {
        name: "菜单名称", //主菜单名称
        functionCode: "", //类型
        content: {
          link: "", //链接
          keyWord: "", //关键词
          called: "", //被叫号码
          receiveNumber: "", //接受号码
          receiveType: "", //类型
          receiveContent: "", //接受内容
          longitude: "", //经度
          latitude: "", //纬度
          locationName: "", //位置名称
          searchKey: "", //搜索关键字
          upcomingTitle: "", //代办标题
          upcomingDesc: "", //代办描述
          upcomingStartTime: "", //代办开始时间
          upcomingEndTime: "", //代办结束时间
        }, //如果有子菜单主菜单没有功能
        subMenu: [],
      };
      this.delflag = 0;
      if (this.count == 0) {
        //第一次进添加菜单
        this.list.unshift(row);
        this.pIndex = 0;
        this.cIndex = -1;
        this.form = row;
        this.count = this.count + 1;
      } else {
        this.onSubmit(() => {
          this.list.unshift(row);
          this.pIndex = 0;
          this.cIndex = -1;
          if (this.list[0]) {
            this.form = this.list[0];
          }
        });
      }
    },
    addSubMenu(index) {
      let row = {
        //子菜单
        name: "子菜单名称", //子菜单名称
        functionCode: "", //子菜单功能
        content: {
          link: "", //链接
          keyWord: "", //关键词
          called: "", //被叫号码
          receiveNumber: "", //接受号码
          receiveType: "", //类型
          receiveContent: "", //接受内容
          longitude: "", //经度
          latitude: "", //纬度
          locationName: "", //位置名称
          searchKey: "", //搜索关键字
          upcomingTitle: "", //代办标题
          upcomingDesc: "", //代办描述
          upcomingStartTime: "", //代办开始时间
          upcomingEndTime: "", //代办结束时间
        },
      };
      this.delflag = 1;
      if (this.countC == 0 && this.count == 0) {
        //第一次进添加菜单
        this.list[index].subMenu.unshift(row);
        this.pIndex = index;
        this.cIndex = 0;
        this.form = row;
        // this.form.name = "子菜单名称";
        this.countC = this.countC + 1;
      } else {
        this.onSubmit(() => {
          //   console.log("成功");
          this.list[index].subMenu.unshift(row);
          this.pIndex = index;
          this.cIndex = 0;
          if (this.list[index].subMenu[0]) {
            this.form = this.list[index].subMenu[0];
          }
        });
      }
    },
    clickMenu(index, idx) {
      // 当有两个参数的时候选中的是子级
      const { pIndex, cIndex } = this;
      //   console.log(arguments);
      if (arguments.length > 1) {
        this.pIndex = index;
        this.cIndex = idx;
        if (this.list[index].subMenu[idx]) {
          this.form = this.list[index].subMenu[idx];
        }

        this.delflag = 1;
      } else {
        this.delflag = 0;
        this.pIndex = index;
        this.cIndex = -1;
        if (this.list[index]) {
          this.form = this.list[index];
        }
      }
    },
    onSubmit(callback) {
      // 当只有pIndex有值的时候选中的是父级
      // 当pIndex 有值 cIndex也有值 选中的是子级
      const { pIndex, cIndex } = this;
      //   console.log(this.form, "lll");
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (cIndex == -1) {
            this.list[pIndex] = this.form;
          } else {
            this.list[pIndex].subMenu[cIndex] = this.form;
          }
          // console.log('true');
          if (typeof callback == "function") callback();
        }
      });
    },
    edit() {
      if (this.chatbotId == "") {
        this.tag = 1;
      } else {
        window.api.get(
          window.path.fiveWeb + "/menu/" + this.chatbotId,
          {},
          (res) => {
            if (res.code == 200) {
              this.tag = 1;
              this.list = JSON.parse(res.data.menuContent);
              if (this.list.length) {
                this.form = this.list[0];
                this.pIndex = 0;
              }
            }
          }
        );
      }
    },
  },
  watch: {
    lists: {
      deep: true,
      handler: function (val) {
        // console.log(val,'val');
        this.list = JSON.parse(val.menuContent);
        if (this.list.length) {
          this.form = this.list[0];
          this.pIndex = 0;
        }
        // console.log(this.lists,'list');
      },
    },
  },
};
</script>

<style lang="less" scoped>

.right_item{
  width:100%;
  background:#fff;
  margin:10px 0;
  padding:10px
}

.sub_item{
  width:100%;
  background:#eee;
  margin:10px 0;
  line-height:25px;
  padding-left:5px
}

.alert{
  width:98%;
  height:30px;
  line-height:30px;
  padding-left:10px;
  background:#e0f1ff;
  border-radius:5px;
  display:flex;
  justify-content:space-between;
  padding-right:10px
}

.contents{
  display:flex;
}

.left{
  width:350px;
  height:701px;
  margin-left:16px;
  background:url(../../../../../assets/images/phonet.png) 0 0 no-repeat;
  overflow:hidden;
  border-radius:16px;
  position:relative
}

.menu-box{
  position:absolute;
  bottom:0;
  left:0;
  width:100%;
  height:50px;
  display:flex;
  align-items:flex-end;
  font-size:12px;
  border-top:2px solid #eee;
  margin-bottom:11px
}

.icons{
  width:70px;
  height:50px;
  text-align:center;
  line-height:50px;
  border-right:2px solid #e6e9eb
}

.conentHeader{
  width:100%;
  height:50px;
  display:flex;
  justify-content:space-between;
  align-items:center;
  border-bottom:1px solid #eee
}

.text{
  width:100%;
  height:30px;
  background:#e0f1ff;
  border-radius:5px;
  line-height:30px;
  padding-left:10px;
  margin-bottom:10px
}

.items_menu{
  flex:1;
  height:50px;
  text-align:center;
  line-height:50px;
  border-right:2px solid #e6e9eb;
  cursor:pointer;
  position:relative
}

.item_child{
  position:absolute;
  width:100%;
  bottom:0;
  margin-bottom:50px
}

.footer{
  width:100%;
  text-align:center;
  margin:10px 0;
  padding:10px 0
}

</style>
