<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label-width="80px" label="用户名" prop="username">
              <el-input
                v-model="formInline.username"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="手机号" prop="phone">
              <el-input
                v-model="formInline.phone"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="角色名称" prop="roleId">
              <el-select
                v-model="formInline.roleId"
                clearable
                filterable
                placeholder="不限"
                class="input-w"
              >
                <el-option
                  v-for="item in optionRole"
                  :key="item.roleId"
                  :label="item.roleDesc"
                  :value="item.roleId"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label-width="80px" label="账号状态" prop="delFlag">
              <el-select
                v-model="formInline.delFlag"
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="不限" value=""></el-option>
                <el-option label="启用" value="0"></el-option>
                <el-option label="停用" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label-width="80px" label="创建人" prop="createName">
              <el-input
                v-model="formInline.createName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label-width="80px" label="创建时间" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button
            type="primary"
            plain
            style=""
            @click.prevent="Query()"
            @keyup.enter="Query()"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>

        <div class="Mail-table" style="padding-bottom: 40px">
          <!-- 表格和分页开始 -->
          <table-tem
            :tableDataObj="tableDataObj"
            @handelOptionButton="handelOptionButton2"
            @handelSelection="handelSelection"
          >
            <template #tableButtons>
              <el-button
                type="primary"
                @click="addUser"
                >添加用户</el-button
              >
              <el-button
                type="danger"
                @click="batchDeletion"
                v-if="selectId.length > 0"
                >批量删除</el-button
              >
              <el-button type="warning" @click="Disable" v-if="selectId.length > 0"
                >批量停用</el-button
              >
              <el-button type="primary" @click="Enable" v-if="selectId.length > 0"
                >批量启用</el-button
              >
            </template>
          </table-tem>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>
      <!-- 添加用户 -->
      <el-dialog
        :title="roleDialog"
        v-model="addCharacterPopUps1"
        width="485px"
        :close-on-click-modal="false"
        :before-close="handleClose1"
      >
        <div class="addC" style="padding-right: 50px">
          <el-form
            ref="formAdd"
            :rules="rules"
            :model="formAdd"
            label-width="100px"
          >
            <el-form-item label="真实姓名" prop="realName">
              <el-input v-model="formAdd.realName"></el-input>
            </el-form-item>
            <el-form-item
              label="用户名"
              prop="username"
              :rules="
                filter_rules({
                  required: true,
                  type: 'userName',
                  message: '用户名不能为空',
                  min: 6,
                  max: 15,
                })
              "
            >
              <el-input
                v-if="roleDialog == '新增用户'"
                v-model="formAdd.username"
                @blur="propName(formAdd.username)"
              ></el-input>
              <el-input
                v-else-if="roleDialog == '编辑用户'"
                readonly
                disabled
                v-model="formAdd.username"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-if="roleDialog == '新增用户'"
              label="密码"
              prop="password"
              :rules="
                filter_rules({
                  required: true,
                  type: 'passwords',
                  message: '密码不能为空',
                  min: 8,
                  max: 16,
                })
              "
            >
              <el-input v-model="formAdd.password" type="password"></el-input>
            </el-form-item>
            <el-form-item
              v-if="roleDialog == '编辑用户'"
              label="密码"
              prop="password"
            >
              <el-input
                readonly
                disabled
                type="password"
                v-model="pasw"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item label="账号状态" prop="delFlag">
                                <el-select v-model="formAdd.delFlag" placeholder="请选择">
                                    <el-option label="启用" value="0"></el-option>
                                    <el-option label="停用" value="1"></el-option>
                                </el-select>
                            </el-form-item> -->
            <el-form-item label="用户角色" prop="role">
              <el-select
                v-if="roleDialog == '新增用户'"
                @clear="Empty"
                v-model="formAdd.role[0]"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in optionRole"
                  :key="item.roleId"
                  :label="item.roleDesc"
                  :value="item.roleId"
                >
                </el-option>
              </el-select>
              <el-select
                v-else-if="roleDialog == '编辑用户'"
                v-model="formAdd.role[0]"
                readonly
                disabled
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in optionRole"
                  :key="item.roleId"
                  :label="item.roleDesc"
                  :value="item.roleId"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="手机号码" prop="phone">
              <el-input v-model="formAdd.phone"></el-input>
            </el-form-item>
            <el-form-item label="电子邮件" prop="mailbox">
              <el-input v-model="formAdd.mailbox"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="addCharacterPopUps1 = false">取 消</el-button>
            <el-button type="primary" @click="addIPDialog">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 重置密码 -->
      <el-dialog
        title="重置密码"
        v-model="addCharacterPopUps2"
        width="450px"
        :close-on-click-modal="false"
        :before-close="handleClose2"
      >
        <div class="addC" style="padding-right: 50px">
          <el-form
            ref="Password"
            :rules="passwordRules"
            :model="Password"
            label-width="100px"
          >
            <el-form-item
              label="用户名"
              prop="name"
              :rules="
                filter_rules({
                  required: true,
                  type: 'userName',
                  min: 6,
                  max: 15,
                })
              "
            >
              <el-input readonly disabled v-model="Password.name"></el-input>
            </el-form-item>
            <el-form-item
              label="用户新密码"
              prop="new"
              :rules="
                filter_rules({
                  required: true,
                  type: 'passwords',
                  message: '新密码不能为空',
                  min: 8,
                  max: 16,
                })
              "
            >
              <el-input type="password" v-model="Password.new"></el-input>
            </el-form-item>
            <el-form-item label="确认新密码" prop="confirm">
              <el-input type="password" v-model="Password.confirm"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="addCharacterPopUps2 = false">取 消</el-button>
            <el-button type="primary" @click="resetPasswords">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 分配角色 -->
      <el-dialog
        title="分配角色"
        v-model="addCharacterPopUps3"
        width="250px"
        :close-on-click-modal="false"
        :before-close="handleClose3"
      >
        <el-select v-model="roleDistribution" filterable placeholder="请选择">
          <el-option
            v-for="item in optionRole"
            :key="item.roleId"
            :label="item.roleDesc"
            :value="item.roleId"
          >
          </el-option>
        </el-select>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="addCharacterPopUps3 = false">取 消</el-button>
            <el-button type="primary" @click="assigningRoles">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    DatePlugin,
    TableTem
  },
  name: 'authorityManagement',
  data() {
    // 验证密码规则
    var SecondaryVerification = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('请输入确认新密码'))
      } else if (value != this.Password.new) {
        return callback(new Error('两次输入密码不同'))
      } else {
        callback()
      }
    }
    //邮箱验证
    var mailbox = (rule, value, callback) => {
      if (value == '') {
        callback()
      } else {
        if (
          !/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/.test(
            value
          )
        ) {
          return callback(new Error('请正确填写邮箱'))
        } else {
          callback()
        }
      }
    }
    // 真实姓名验证
    var realName = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('真实姓名不能为空'))
      } else if (!/^[\u4e00-\u9fa5]{2,4}$/.test(value)) {
        return callback(new Error('请输入正确的姓名'))
      } else {
        callback()
      }
    }
    // 多个手机号验证
    var phone = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('手机号码不能为空'))
      } else if (
        !/^1[3|4|5|6|7|8|9]\d{9}(,1[3|4|5|6|7|8|9]\d{9})*$/.test(value)
      ) {
        return callback(new Error('请正确填写手机号,最多5个,请用(,)隔开'))
      } else {
        var phoneArr = this.formAdd.phone.split(',')
        var flag = false
        if (phoneArr.length > 5) {
          return callback(new Error('最多添加5个手机号'))
        } else {
          if (phoneArr.length > 1) {
            for (var i = 0; i < phoneArr.length; i++) {
              for (var j = i; j < phoneArr.length; j++) {
                if (phoneArr[i] == phoneArr[j + 1]) {
                  flag = true
                }
              }
            }
          }
          if (flag) {
            return callback(new Error('手机号不可重复添加'))
          } else {
            callback()
          }
        }
      }
    }
    return {
      roleDialog: '',
      selectId: '', //批量操作选中id
      selectStatus: [], //批量操作选中状态
      pasw: '******',
      rules: {
        realName: [{ required: true, validator: realName, trigger: 'blur' }],
        delFlag: [
          { required: true, message: '账号状态不能为空', trigger: 'change' },
        ],
        role: [
          { required: true, message: '用户角色不能为空', trigger: 'change' },
        ],
        mailbox: [
          { required: false, validator: mailbox, trigger: ['blur', 'change'] },
          {
            min: 3,
            max: 100,
            message: '请输入100长度以内',
            trigger: ['blur', 'change'],
          },
        ],
        phone: [{ required: true, validator: phone, trigger: 'blur' }],
      },
      passwordRules: {
        name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        confirm: [
          { required: true, validator: SecondaryVerification, trigger: 'blur' },
        ],
      },
      // 搜索数据
      formInline: {
        username: '',
        phone: '',
        roleId: '',
        delFlag: '',
        createName: '',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        username: '',
        phone: '',
        roleId: '',
        delFlag: '',
        createName: '',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 添加用户弹出窗
      addCharacterPopUps1: false,
      // 重置密码弹出窗
      addCharacterPopUps2: false,
      //分配角色弹出窗
      addCharacterPopUps3: false,
      // 添加用户
      formAdd: {
        realName: '',
        username: '',
        password: '',
        delFlag: '0',
        role: [],
        phone: '',
        mailbox: '',
      },
      //重置密码
      Password: {
        name: '',
        new: '',
        confirm: '',
      },
      // 角色名称
      options: [],
      // 分配角色
      optionRole: [],
      // 角色分配
      roleDistribution: '',
      roleIdUser: '',
      //用户列表数据
      tableDataObj: {
        id: "authorityManagement",
        custom: true,
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'username',
            showName: '用户名',
          },
          {
            prop: 'realName',
            showName: '真实姓名',
          },
          {
            prop: 'roleDesc',
            showName: '角色名称',
          },
          {
            prop: 'phone',
            showName: '手机号码',
          },
          // {
          // prop:"mailbox",
          // showName:'电子邮件',
          // },
          {
            prop: 'delFlag',
            showName: '账号状态',
            width: '80',
            showCondition: {
              condition: '1',
            },
            formatData: function (val) {
              return val == '0' ? '启用' : '停用'
            },
          },
          {
            prop: 'createName',
            showName: '创建人',
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '170',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '290', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'delFlag', //关联的表格属性
            contactData: '0', //关联的表格属性-值
            optionName: '重置密码', //按钮的显示文字
            optionMethod: 'Permission', //按钮的方法
            icon: 'Setting', //按钮图标
            optionButtonColor: '', //按钮颜色
          },
          {
            contactCondition: 'delFlag', //关联的表格属性
            contactData: '0', //关联的表格属性-值
            optionName: '编辑', //按钮的显示文字
            optionMethod: 'edit', //按钮的方法
            icon: 'EditPen', //按钮图标
            optionButtonColor: '', //按钮颜色
          },
          {
            contactCondition: 'delFlag', //关联的表格属性
            contactData: '0', //关联的表格属性-值
            optionName: '停用', //按钮的显示文字
            optionMethod: 'DisableActivation', //按钮的方法
            icon: 'CircleCloseFilled', //按钮图标
            optionButtonColor: 'orange', //按钮颜色
            otherOptionName: '启用', //其他条件的按钮显示文字
            otherOptionMethod: 'DisableActivation', //其他条件的按钮方法
            otherIcon: 'el-icon-circle-check-outline', //其他条件按钮的图标
            optionOtherButtonColor: '', //其他条件按钮的颜色
          },
          {
            contactCondition: 'delFlag', //关联的表格属性
            contactData: '0', //关联的表格属性-值
            optionName: '分配角色', //按钮的显示文字
            optionMethod: 'distribution', //按钮的方法
            icon: 'Star', //按钮图标
            optionButtonColor: '', //按钮颜色
          },
          {
            contactCondition: 'delFlag', //关联的表格属性
            contactData: '1', //关联的表格属性-值
            optionName: '删除', //按钮的显示文字
            optionMethod: 'delete', //按钮的方法
            icon: 'el-icon-delete', //按钮图标
            optionButtonColor: '#f56c6c', //按钮颜色
          },
        ],
      },
    }
  },
  methods: {
    addUser() {
      this.roleDialog = '新增用户';
      this.addCharacterPopUps1 = true
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.upms + 'user/userPage',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    // 用户操作
    handelOptionButton2: function (val) {
      console.log(658)
      if (val.methods == 'Permission') {
        this.roleIdUser = val.row.userId
        this.Password.name = val.row.username
        this.addCharacterPopUps2 = true
      } else if (val.methods == 'edit') {
        this.roleDialog = '编辑用户'
        this.addCharacterPopUps1 = true
        this.$nextTick(() => {
          console.log(677)
          this.$refs.formAdd.resetFields()
          val.row.delFlag += ''
          Object.assign(this.formAdd, val.row)
          this.formAdd.role[0] = val.row.roleId
        })
      } else if (val.methods == 'DisableActivation') {
        console.log(val)
        this.$confirms.confirmation(
          'put',
          val.row.delFlag == 0 ? '确认停用' : '确认启用',
          window.path.upms +
            'user/updateFlagBatch?delFlag=' +
            (val.row.delFlag == 0 ? 1 : 0) +
            '&ids=' +
            val.row.userId,
          {},
          (res) => {
            this.InquireList()
            this.addCharacterPopUps1 = false
          }
        )
      } else if (val.methods == 'distribution') {
        // 分配角色
        this.roleDistribution = val.row.roleId
        this.roleIdUser = val.row.userId
        this.addCharacterPopUps3 = true
      } else if (val.methods == 'delete') {
        this.$confirms.confirmation(
          'delete',
          '此操作将永久删除该数据, 是否继续?',
          window.path.upms + 'user/' + val.row.userId,
          {},
          (res) => {
            this.InquireList()
          }
        )
      }
    },
    // 搜索
    Query() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      Object.assign(this.formInlines, this.formInline)
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0]
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 新增用户
    addIPDialog() {
      console.log(729)
      this.$refs['formAdd'].validate((valid) => {
        if (valid) {
          if (this.roleDialog == '新增用户') {
            window.api.get(
              window.path.upms + 'online/existUser/' + this.formAdd.username,
              {},
              (res) => {
                if (res.data == 0) {
                  this.formAdd.createTime = ''
                  this.$confirms.confirmation(
                    'post',
                    '确认新增',
                    window.path.upms + 'user/addUser',
                    this.formAdd,
                    (res) => {
                      this.InquireList()
                      this.addCharacterPopUps1 = false
                    }
                  )
                } else {
                  this.$message({
                    type: 'error',
                    duration: 2000,
                    message: '用户名已存在',
                  })
                }
              }
            )
          } else if (this.roleDialog == '编辑用户') {
            let formAdd1 = {}
            formAdd1.realName = this.formAdd.realName
            formAdd1.delFlag = this.formAdd.delFlag
            formAdd1.phone = this.formAdd.phone
            formAdd1.mailbox = this.formAdd.mailbox
            formAdd1.userId = this.formAdd.userId
            this.$confirms.confirmation(
              'put',
              '确认编辑',
              window.path.upms + 'user',
              formAdd1,
              (res) => {
                this.InquireList()
                this.addCharacterPopUps1 = false
              }
            )
          }
        } else {
          return false
        }
      })
    },
    // 用户名唯一验证
    propName(val) {
      window.api.get(window.path.upms + 'online/existUser/' + val, {}, (res) => {
        if (res.data != 0) {
          this.$message({
            type: 'error',
            duration: 2000,
            message: '用户名已存在',
          })
        }
      })
    },
    // 角色分配
    assigningRoles() {
      this.$confirms.confirmation(
        'post',
        '确认分配',
        window.path.upms + 'user/assignRoles',
        { roleId: this.roleDistribution, userId: this.roleIdUser },
        (res) => {
          this.InquireList()
          this.addCharacterPopUps3 = false
        }
      )
    },
    // 清空角色选中
    Empty() {
      this.formAdd.role = []
    },
    //重置密码
    resetPasswords() {
      this.$refs['Password'].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'put',
            '确认重置密码',
            window.path.upms +
              'user/updatePassword?id=' +
              this.roleIdUser +
              '&password=' +
              this.Password.confirm,
            {},
            (res) => {
              this.addCharacterPopUps2 = false
            }
          )
        } else {
          return false
        }
      })
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      let selectStatus = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].userId)
        selectStatus.push(val[i].delFlag)
      }
      this.selectStatus = selectStatus
      this.selectId = selectId.join(',')
    },
    //批量删除
    batchDeletion() {
      let flag = true
      for (var i = 0; i < this.selectStatus.length; i++) {
        if (this.selectStatus[i] == '0') {
          flag = false
          break
        }
      }
      if (flag) {
        this.$confirms.confirmation(
          'delete',
          '此操作将永久删除该数据, 是否继续?',
          window.path.upms + 'user/' + this.selectId,
          {},
          (res) => {
            this.InquireList()
            this.selectStatus = []
          }
        )
      } else {
        this.$message({
          type: 'error',
          duration: 2000,
          message: '批量操作用户中存在未停用用户,请先停用',
        })
      }
    },
    //批量停用
    Disable() {
      this.$confirms.confirmation(
        'put',
        '确认批量停用',
        window.path.upms +
          'user/updateFlagBatch?delFlag=1' +
          '&ids=' +
          this.selectId,
        {},
        (res) => {
          this.InquireList()
          this.addCharacterPopUps1 = false
        }
      )
    },
    //批量启用
    Enable() {
      this.$confirms.confirmation(
        'put',
        '确认批量停用',
        window.path.upms +
          'user/updateFlagBatch?delFlag=0' +
          '&ids=' +
          this.selectId,
        {},
        (res) => {
          this.InquireList()
          this.addCharacterPopUps1 = false
        }
      )
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
    // 关闭弹出层
    handleClose1(done) {
      this.addCharacterPopUps1 = false
    },
    handleClose2(done) {
      this.addCharacterPopUps2 = false
    },
    handleClose3(done) {
      this.addCharacterPopUps3 = false
    },
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    window.api.get(
      window.path.upms + 'role/selectSubordinateRoleById',
      {},
      (res) => {
        this.optionRole = res
      }
    )
  },
  activated() {
    this.InquireList()
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps1: function (val) {
      if (val == false) {
        this.$refs.formAdd.resetFields() //清空表单
      }
    },
    addCharacterPopUps2: function (val) {
      if (val == false) {
        this.$refs.Password.resetFields() //清空表单
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
