<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="平台账号" prop="ptUsername">
            <el-input
              v-model="formInline.ptUsername"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="企业代码" prop="corpid">
            <el-input
              v-model="formInline.corpid"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="网关端口" prop="wgPort">
            <el-input
              v-model="formInline.wgPort"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
        ></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          :data="tableDataObj.tableData"
          border
          stripe
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">添加</el-button>
            <el-button type="primary" @click="Synchronize">同步</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="CMPPmanagement"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column width="120" field="平台账号" title="平台账号">
            <template #default="scope">
              <div>{{ scope.row.ptUsername }}</div>
            </template>
          </vxe-column>

          <vxe-column field="网关IP" title="网关IP" width="130">
            <template #default="scope">
              <div>
                {{ scope.row.wgIp }}
                <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                  @click="handleCopy(scope.row.wgIp, $event)"></i> -->
                <CopyTemp :content="scope.row.wgIp" />
              </div>
            </template>
          </vxe-column>
          <vxe-column field="网关端口" title="网关端口" width="120">
            <template #default="scope">
              <div>{{ scope.row.wgPort }}</div>
            </template>
          </vxe-column>
          <vxe-column field="网关用户名" title="网关用户名" width="120">
            <template #default="scope">
              <div>{{ scope.row.wgUsername }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column prop="wgPassword" field="" title="网关密码"> </vxe-column> -->
          <vxe-column field="企业代码" title="企业代码" width="100">
            <template #default="scope">
              {{ scope.row.corpid }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                @click="handleCopy(scope.row.corpid, $event)"></i> -->
              <CopyTemp :content="scope.row.corpid" />
            </template>
          </vxe-column>
          <vxe-column field="接入号" title="接入号" width="120">
            <template #default="scope">
              <div>{{ scope.row.spport }}</div>
            </template>
          </vxe-column>
          <vxe-column field="最大链接数" title="最大链接数" width="120">
            <template #default="scope">
              <div>{{ scope.row.connetMax }}</div>
            </template>
          </vxe-column>
          <vxe-column field="实际链接数" title="实际链接数" width="120">
            <template #default="scope">
              <div
                style="color: #409eff; cursor: pointer"
                @click="handelConnect(scope.row)"
                >{{ scope.row.connectNum }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="流速" title="流速" width="120">
            <template #default="scope">
              <div>{{ scope.row.speed }}</div>
            </template>
          </vxe-column>
          <vxe-column prop="ip" field="绑定IP" title="绑定IP" width="180">
            <template #default="scope">
              <Tooltip
                v-if="scope.row.ip"
                :content="scope.row.ip"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="发送队列" title="发送队列" width="120">
            <template #default="scope">
              <div
                style="color: #409eff; cursor: pointer"
                v-if="scope.row.submitNum > 0"
                @click="channeClear(scope.row, 'sendsms')"
                >{{ scope.row.submitNum }}</div
              >
              <div v-else>{{ scope.row.submitNum }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回执队列" title="回执队列" width="120">
            <template #default="scope">
              <div
                style="color: #409eff; cursor: pointer"
                v-if="scope.row.deliverNum > 0"
                @click="channeClear(scope.row, 'deliver')"
                >{{ scope.row.deliverNum }}</div
              >
              <div v-else>{{ scope.row.deliverNum }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态" width="60">
            <template #default="scope">
              <div style="color: red" v-if="scope.row.status == '2'"
                >停用</div
              >
              <div v-else-if="scope.row.status == '1'">启用</div>
              <div v-else>全部</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template #default="scope">
              <div>
                <el-button
                style="color: #16a589"
                link
                @click="detailsRow(scope.row)"
                ><el-icon><EditPen /></el-icon>编辑</el-button
              >
              
              <el-button
                style="color: red"
                v-if="scope.row.status == '1'"
                link
                @click="Enable(scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>停用</el-button
              >
              <el-button
                style="color: #16a589"
                v-if="scope.row.status == '2'"
                link
                @click="Enable(scope.row)"
                ><el-icon><SuccessFilled /></el-icon>启用</el-button
              >
              </div>
              <div>
                <el-button
                style="color: #409eff"
                link
                @click="showChangePwdDialog(scope.row)"
                ><el-icon><Key /></el-icon>修改密码</el-button
              >
              </div>
              
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <div style="float: right; margin-top: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增行业类别 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="100px"
          ref="formop"
          style="padding: 0 110px 0 20px"
        >
          <el-form-item label="平台账号" prop="ptUsername">
            <el-input
              :disabled="id == '' ? false : true"
              v-model="formop.ptUsername"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="网关IP" prop="wgIp">
            <el-input
              :disabled="id == '' ? false : true"
              type="textarea"
              v-model="formop.wgIp"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="网关端口" prop="wgPort">
            <el-input
              :disabled="id == '' ? false : true"
              v-model="formop.wgPort"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="网关用户名" prop="wgUsername">
            <el-input :disabled="id == '' ? false : true" v-model="formop.wgUsername" disabled
              autocomplete="off"></el-input>
            <span style="font-size: 12px;color: #909399;">默认生成6位</span>
          </el-form-item> -->
          <el-form-item label="企业代码" prop="corpid">
            <el-input
              v-model="formop.corpid"
              disabled
              autocomplete="off"
            ></el-input>
            <span style="font-size: 12px; color: #909399">默认生成6位</span>
          </el-form-item>
          <el-form-item
            label="网关密码"
            prop="wgPassword"
            v-if="dialogStatus == 'add'"
          >
            <el-input
              v-model="formop.wgPassword"
              show-password
              autocomplete="off"
            ></el-input>
          </el-form-item>

          <!-- <span style="
                position: absolute;
                top: 90px;
                right: 40px;">默认生成6位</span>
          <span style="
                position: absolute;
                top: 142px;
                right: 35px;">与企业ID一样</span> -->
          <el-form-item label="接入号" prop="spport">
            <el-input
              :disabled="id == '' ? false : true"
              v-model="formop.spport"
              autocomplete="off"
            ></el-input>
          </el-form-item>

          <el-form-item label="绑定IP" prop="ip">
            <el-input
              type="textarea"
              v-model="formop.ip"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="最大链接数" prop="connetMax">
            <el-input-number
              class="input-w"
              v-model="formop.connetMax"
              :min="1"
              :max="999"
              label="最大链接数"
            ></el-input-number>
            <!-- <el-input v-model="formop.connetMax" autocomplete="off"></el-input> -->
          </el-form-item>
          <el-form-item label="流速" prop="speed">
            <el-input-number
              class="input-w"
              v-model="formop.speed"
              :min="1"
              :max="999"
              label="流速"
            ></el-input-number>
            <!-- <el-input v-model="formop.speed" autocomplete="off"></el-input> -->
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formop.status"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
        title="添加成功(CMPP参数分配)"
        v-model="argumentsVisible"
        width="30%"
        :close-on-click-modal="false"
      >
        <div class="dialog-content">
          <div class="dialog-content-item">
            <div class="dialog-tit">网关协议:</div>
            <div style="color: #67c23a" class="dialog-con">CMPP2.0</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">网关IP:</div>
            <div class="dialog-con">{{ showData.wgIp }}</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">网关端口:</div>
            <div class="dialog-con">{{ showData.wgPort }}</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">账号:</div>
            <div class="dialog-con">{{ showData.wgUsername }}</div>
          </div>
          <div v-if="dialogStatus != 'edit'" class="dialog-content-item">
            <div class="dialog-tit">密码:</div>
            <div class="dialog-con">{{ showData.wgPassword }}</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">企业代码:</div>
            <div class="dialog-con">{{ showData.corpid }}</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">接入号:</div>
            <div class="dialog-con">{{ showData.spport }}</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">绑定IP:</div>
            <div class="dialog-con" style="width: 200px">
              <Tooltip
                v-if="showData.ip"
                :content="showData.ip"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
            </div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">最大链接数:</div>
            <div class="dialog-con">{{ showData.connetMax }}</div>
          </div>
          <div class="dialog-content-item">
            <div class="dialog-tit">最大流速（条/秒）:</div>
            <div class="dialog-con">{{ showData.speed }}</div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="argumentsVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 修改密码弹出框 -->
      <el-dialog
        title="修改密码"
        v-model="passwordDialogVisible"
        :close-on-click-modal="false"
        width="400px"
      >
        <el-form
          :model="passwordForm"
          :rules="passwordRules"
          label-width="100px"
          ref="passwordForm"
          style="padding: 0 20px"
        >
          <el-form-item label="新密码" prop="wgPassword">
            <el-input
              v-model="passwordForm.wgPassword"
              show-password
              autocomplete="new-password"
              placeholder="请输入新密码"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitPassword('passwordForm')"
              >提 交</el-button
            >
            <el-button @click="passwordDialogVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 修改密码弹出框结束 -->

      <!-- 新增行业类别结束 -->
    </div>
  </div>
</template>
<script>
import TableTem from "../../../../publicComponents/TableTem.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
// import clip from '../../../../../utils/clipboard'
import CopyTemp from "@/components/publicComponents/CopyTemp.vue";
import { Key } from '@element-plus/icons-vue'
export default {
  name: "CMPPmanagement",
  components: {
    TableTem,
    Tooltip,
    CopyTemp,
  },
  data() {
    // 验证平台账号
    var ptUsername = (rule, value, callback) => {
      if (value) {
        window.api.get(
          window.path.upms + "online/existUser/" + value,
          {},
          (res) => {
            if (res.data == 1) {
              callback();
            } else {
              callback(new Error("平台账号不存在"));
            }
          }
        );
      } else {
        return callback(new Error("请输入平台账号"));
      }
    };
    // // 验证IP规则
    var wgIp = (rule, value, callback) => {
      if (value) {
        if (
          !/^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])(\.(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)){3}$/.test(
            value
          ) &&
          !/^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,6}$/.test(
            value
          )
        ) {
          return callback(new Error("请正确输入网关IP地址"));
        } else {
          callback();
        }
      } else {
        return callback(new Error("请输入网关IP地址"));
      }
    };
    //验证网关端口号
    var wgPort = (rule, value, callback) => {
      if (value) {
        if (!/^[+]{0,1}(\d+)$/.test(value)) {
          return callback(new Error("请输入大于0的整数"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入网关端口号"));
      }
    };
    //验证SP码号
    var spport = (rule, value, callback) => {
      if (value) {
        if (!/^[+]{0,1}(\d+)$/.test(value)) {
          return callback(new Error("请输入大于0的整数"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入SP码号"));
      }
    };
    //验证最大链接数
    var connetMax = (rule, value, callback) => {
      if (value) {
        if (!/^[+]{0,1}(\d+)$/.test(value)) {
          return callback(new Error("请输入大于0的整数"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入最大链接数"));
      }
    };
    //验证流速
    var speed = (rule, value, callback) => {
      if (value) {
        if (!/^[+]{0,1}(\d+)$/.test(value)) {
          return callback(new Error("请输入大于0的整数"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入流速"));
      }
    };
    //验证IP
    var ip = (rule, value, callback) => {
      if (value) {
        if (/[^\d+(,\d\d\d)*.\d+$]/g.test(value)) {
          return callback(new Error("请输入大于0的整数"));
        } else {
          callback();
        }
      } else {
        callback(new Error("ip不能为空"));
      }
    };
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      titleMap: {
        add: "CMPP服务账号添加",
        edit: "CMPP服务账号编辑",
      },
      dialogStatus: "", //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      argumentsVisible: false, //参数分配弹出框显示隐藏
      id: "", //编辑的id
      showData: {},
      formop: {
        connetMax: 1,
        corpid: "",
        ip: "",
        ptUsername: "",
        speed: 1,
        spport: "",
        status: "",
        wgIp: "",
        wgPassword: "",
        wgPort: "",
        wgUsername: "",
      },
      rules: {
        corpid: [
          {
            required: true,
            message: "请输入企业代码",
            trigger: ["blur", "change"],
          },
        ],
        wgUsername: [
          {
            required: true,
            message: "请输入网关用户名",
            trigger: ["blur", "change"],
          },
        ],
        wgIp: [
          { required: true, validator: wgIp, trigger: ["blur", "change"] },
        ],
        wgPort: [
          { required: true, validator: wgPort, trigger: ["blur", "change"] },
          {
            min: 1,
            max: 20,
            message: "长度在 20 个字符以内",
            trigger: ["blur", "change"],
          },
        ],
        wgPassword: [
          {
            required: true,
            message: "请输入网关密码",
            trigger: ["blur", "change"],
          },
          {
            min: 1,
            max: 20,
            message: "长度在 20 个字符以内",
            trigger: ["blur", "change"],
          },
        ],
        ptUsername: [
          { required: true, validator: ptUsername, trigger: ["blur"] },
        ],
        spport: [
          { required: true, validator: spport, trigger: ["blur", "change"] },
          {
            min: 1,
            max: 20,
            message: "长度在 20 个字符以内",
            trigger: ["blur", "change"],
          },
        ],
        connetMax: [
          { required: true, validator: connetMax, trigger: ["blur", "change"] },
          // { min: 1, max: 2, message: '长度在 2 个字符以内', trigger: ['blur', 'change'] },
        ],
        speed: [
          { required: true, validator: speed, trigger: ["blur", "change"] },
          // { min: 1, max: 3, message: '长度在 3 个字符以内', trigger: ['blur', 'change'] },
        ],
        ip: [
          { required: true, validator: ip, trigger: ["blur", "change"] },
          {
            min: 1,
            max: 500,
            message: "长度在 500 个字符以内",
            trigger: ["blur", "change"],
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: ["blur", "change"],
          },
        ],
      },
      formInline: {
        corpid: "",
        ptUsername: "",
        wgPort: "",
        status: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        corpid: "",
        ptUsername: "",
        wgPort: "",
        status: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
        tableLabel: [
          {
            prop: "corpid",
            showName: "企业代码",
            fixed: false,
          },
          {
            prop: "wgIp",
            showName: "网关IP",
            fixed: false,
          },
          {
            prop: "wgPort",
            showName: "网关端口",
            fixed: false,
          },
          {
            prop: "wgUsername",
            showName: "网关用户名",
            fixed: false,
          },
          {
            prop: "wgPassword",
            showName: "网关密码",
            fixed: false,
          },
          {
            prop: "ptUsername",
            showName: "平台账号",
            fixed: false,
          },
          {
            prop: "spport",
            showName: "SP码号",
            fixed: false,
          },
          {
            prop: "connetMax",
            showName: "最大链接数",
            fixed: false,
          },
          {
            prop: "connectNum",
            showName: "实际链接数",
            fixed: false,
          },
          {
            prop: "speed",
            showName: "流速",
            fixed: false,
          },
          {
            prop: "ip",
            showName: "绑定IP",
            fixed: false,
            width: "180",
          },
          {
            prop: "submitNum",
            showName: "发送队列",
            fixed: false,
          },
          {
            prop: "deliverNum",
            showName: "回执队列",
            fixed: false,
          },
          {
            prop: "status",
            showName: "状态",
            fixed: false,
            width: "60",
            showCondition: {
              condition: "2",
            },
            formatData: function (val) {
              if (val == 1) {
                return (val = "启用");
              } else if (val == 2) {
                return (val = "停用");
              } else {
                return (val = "全部");
              }
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "140", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            // contactCondition:'',//关联的表格属性
            // contactData:'',//关联的表格属性-值
            optionName: "编辑", //按钮的显示文字
            optionMethod: "details", //按钮的方法
            icon: "el-icon-edit", //按钮图标
            optionButtonColor: "", //按钮颜色
          },
          {
            contactCondition: "status", //关联的表格属性
            contactData: "1", //关联的表格属性-值
            optionName: "停用", //按钮的显示文字
            optionMethod: "Enable", //按钮的方法
            icon: "el-icon-error", //按钮图标
            optionButtonColor: "#f56c6c", //按钮颜色
            otherOptionName: "启用", //其他条件的按钮显示文字
            otherOptionMethod: "Enable", //其他条件的按钮方法
            otherIcon: "el-icon-success", //其他条件按钮的图标
            optionOtherButtonColor: "", //其他条件按钮的颜色
          },
        ],
      },
      passwordDialogVisible: false,
      passwordForm: {
        id: "",
        wgPassword: "",
      },
      passwordRules: {
        wgPassword: [
          {
            required: true,
            message: "请输入新密码",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "cmppserver/page",
        this.tabelAlllist,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false;
            this.tableDataObj.total = res.data.total;
            this.tableDataObj.tableData = res.data.records;
          }
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    Reload() {
      this.formInline.beginTime = "";
      this.formInline.endTime = "";
      this.$refs["formInline"].resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          let strdata = JSON.stringify(this.formop);
          this.showData = JSON.parse(strdata);
          if (this.dialogStatus == "add") {
            this.$confirms.confirmation(
              "post",
              "确认添加操作？",
              window.path.omcs + "cmppserver",
              this.showData,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false;
                  this.argumentsVisible = true;
                  this.gettableLIst();
                }
              }
            );
          } else {
            //编辑
            this.$confirms.confirmation(
              "put",
              "确认编辑操作？",
              window.path.omcs + "cmppserver",
              this.showData,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false;
                  this.argumentsVisible = true;
                  this.gettableLIst();
                }
              }
            );
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    addopt() {
      window.api.get(window.path.omcs + "cmppserver/corpid", {}, (res) => {
        if (res.code == 200) {
          this.formop.wgUsername = this.formop.corpid = res.data.corpid;
          this.dialogFormVisible = true;
          this.dialogStatus = "add";
        }
      });
    },
    //同步
    Synchronize() {
      window.api.get(window.path.omcs + "cmppserver/sync", {}, (res) => {
        if (res.code == 200) {
          this.$message({
            message: "当前通道正常!",
            type: "success",
          });
        } else {
          this.$message({
            message: "同步失败",
            type: "warning",
          });
        }
      });
    },
    detailsRow(val) {
      console.log(val, "val");
      //编辑
      this.dialogFormVisible = true;
      this.dialogStatus = "edit";
      this.tableRow = val;
      this.id = val.id;
      val.status += "";
      //清除编辑默认值
      this.$nextTick(() => {
        this.$refs.formop.resetFields();
        Object.assign(this.formop, val);
        this.formop.speed = val.speed * 1;
        this.formop.connetMax = val.connetMax * 1;
      });
    },
    // 停用启用
    Enable: function (val) {
      this.$confirms.confirmation(
        "post",
        "确认执行此操作吗？",
        window.path.omcs + "cmppserver/overrule/" + val.id,
        {},
        (res) => {
          this.gettableLIst();
        }
      );
    },
    // handleCopy(name, event) {
    //   clip(name, event)
    // },
    handelConnect(row) {
      console.log(row, "rwo");
      this.$router.push({
        path: "/cmppConnect",
        query: { corpId: row.corpid, port: row.wgPort },
      });
    },
    channeClear(val, type) {
      this.$confirms.confirmation(
        "post",
        "确定执行清除队列",
        window.path.omcs + "cmppserver/queue/clear",
        {
          corpid: val.corpid,
          type,
        },
        (res) => {
          if (res.code == 200) {
            this.gettableLIst();
          }
        }
      );
    },
    handelOptionButton: function (val) {
      if (val.methods == "details") {
        this.detailsRow(val);
      }
      if (val.methods == "Enable") {
        this.Enable(val);
      }
    },
    showChangePwdDialog(row) {
      // Implement the logic to show the change password dialog
      // console.log("Show change password dialog for:", row);
      this.passwordDialogVisible = true;
      this.passwordForm.id = row.id;
    },
    submitPassword(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let strdata = JSON.stringify(this.passwordForm);
          let data = JSON.parse(strdata);
          // this.showData = JSON.parse(strdata);
          this.$confirms.confirmation(
            "put",
            "确认修改密码？",
            window.path.omcs + "cmppserver/password",
            data,
            (res) => {
              if (res.code == 200) {
                this.gettableLIst();
                this.passwordDialogVisible = false;
              }
            }
          );
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.gettableLIst();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep:true,//深度监听
    //   immediate: true
    // },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields();
        this.id = "";
      }
    },
    passwordDialogVisible(val) {
      if (!val) {
        this.$refs.passwordForm.resetFields();
        this.passwordForm.id = "";
      }
    },
  },
};
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.dialog-content {
  line-height: 2.5;
}

.dialog-content-item {
  display: flex;
  align-items: center;
}

.dialog-tit {
  width: 130px;
  text-align: right;
}
.dialog-con {
  margin-left: 8px;
}
</style>

