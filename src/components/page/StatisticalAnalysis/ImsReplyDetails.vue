<template>
  <div class="container_left">
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <div class="passageWay-title">
        <div style="display: flex; align-items: center; width: 650px;">
          <span
            style="
              display: inline-block;
              width: 68px;
              text-align: right;
              padding-right: 10px;
            "
            >时间</span
          >
          <el-radio-group
            v-model="flag"
            size="default"
            text-color="#fff"
            fill="#16a589"
            @change="handleChangeTimeOptions"
          >
            <el-radio-button value="1">今天</el-radio-button>
            <el-radio-button value="2">近4天</el-radio-button>
            <!-- <el-radio-button label="8">上月</el-radio-button> -->
          </el-radio-group>
          <!-- <date-plugin
            class="search-date"
            :datePluginValueList="datePluginValueList"
            @handledatepluginVal="handledatepluginVal"
          ></date-plugin> -->

          <el-date-picker
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="datePluginValue"
            type="datetimerange"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="至"
            @change="timeClick"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>

        <!-- 查询框开始 -->
        <el-form
          label-width="80px"
          :inline="true"
          :model="form1"
          class="demo-form-inline"
          ref="sendjl1"
        >
          <el-form-item label="用户名称" prop="clientName">
            <el-input v-model="form1.clientName" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="form1.mobile" class="input-w"></el-input>
          </el-form-item>
          <!-- <el-form-item label="通道号"  prop='channelId'>
                                    <el-input v-model="form1.channelId" class="input-w"></el-input>
                                </el-form-item> -->
          <div>
            <el-button type="primary" plain @click="Query2">查询</el-button>
            <el-button type="primary" plain @click="reset1('sendjl1')"
              >重置</el-button
            >
            <!-- <el-button type="primary" plain @click="export2()">导出</el-button> -->
            <el-button
              type="primary"
              style="margin-left: 15px"
              v-if="selectId.length > 0"
              @click="delAll"
              >批量加入黑名单</el-button
            >
          </div>
        </el-form>
        <!-- 查询框结束 -->
        <div class="passage-table" style="margin-top: 10px">
          <!-- 表格和分页开始 -->
          <table-tem
            :tableDataObj="tableDataObj1"
            @handelOptionButton="handelOptionButton"
            @handelSelection="handelSelection"
            @routers="getOperations"
          ></table-tem>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange1"
              @current-change="handleCurrentChange1"
              :current-page="formData1.currentPage"
              :page-size="formData1.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj1.totalRow"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
        <!-- 新增 -->
        <el-dialog
          :title="titleMap[dialogStatus]"
          v-model="dialogFormVisible"
          :close-on-click-modal="false"
          width="520px"
        >
          <el-form
            :model="formop"
            :rules="rules"
            ref="formop"
            label-width="100px"
            style="padding: 0 28px 0 20px"
          >
            <el-form-item label="备注" prop="remark">
              <el-input
                type="textarea"
                v-model="formop.remark"
                autocomplete="off"
              ></el-input>
            </el-form-item>
          </el-form>
          <template v-slot:footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitForm('formop')"
                >提 交</el-button
              >
              <el-button @click="dialogFormVisible = false">取 消</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 新增结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import monment from 'moment'
export default {
  components: {
    DatePlugin,
    TableTem
  },
  name: 'ImsReplyDetails',
  data() {
    return {
      datePluginValue: [
        monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      ],
      isFirstEnter: false,
      titleMap: {
        add: '批量加入黑名单',
        edit: '加入黑名单',
      },
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      idStr: '',
      rules: {
        //验证规则
        remark: [
          { required: false, message: '请输入备注', trigger: 'blur' },
          {
            min: 1,
            max: 70,
            message: '长度在 1 到 70 个字符',
            trigger: ['blur', 'change'],
          },
        ],
      },
      dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: '',
      },
      flag: '1', //选择那一天
      // Times: {
      //   beginTime: '',
      //   endTime: '',
      // },
      Times: {
        beginTime: monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      datePluginValueList: {
        //日期选择器
        type: 'daterange',
        start: '',
        end: '',
        range: '-',
        clearable: false,
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.pickerMinDate = minDate.getTime()
            if (maxDate) {
              this.pickerMinDate = ''
            }
          },
          disabledDate: (time) => {
            if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
              return false
            }
            if (this.pickerMinDate !== '') {
              const day30 = 30 * 24 * 3600 * 1000
              let maxTime = this.pickerMinDate + day30
              if (maxTime > Date.now() - 345600000) {
                maxTime = Date.now() - 345600000
              }
              const minTime = this.pickerMinDate - day30
              return time.getTime() < minTime || time.getTime() > maxTime
            }
            return time.getTime() > Date.now() - 345600000
          },
        },
        datePluginValue: '',
      },
      //发送查询的值
      form: {
        mobile: '',
        clientName: '',
        channelId: '',
        signature: '',
        smsStatus: '',
        temId: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        clientName: '',
        channelId: '',
        signature: '',
        smsStatus: '',
        temId: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //回复查询的值
      form1: {
        mobile: '',
        clientName: '',
        channelId: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //复制回复查询的值
      formData1: {
        mobile: '',
        clientName: '',
        channelId: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
        tableLabelExpand: [
          //折叠的列表表头
          { prop: 'content', showName: '短信内容:' },
        ],
        tableLabel: [
          //列表表头
          { prop: 'username', showName: '用户名称' },
          { prop: 'mobile', showName: '手机号码', width: '100' },
          { prop: 'chargeNum', showName: '计费数', width: '80' },
          { prop: 'channelId', showName: '通道ID', width: '80' },
          // {prop:"signature",showName:'签名'},
          // {prop:"smsStatus",showName:'短信类型',formatData: function(val) { return val == '2' ? '成功' : '失败' }},

          {
            prop: 'createTime',
            showName: '提交时间',
            width: '170'
          },
          {
            prop: 'sendTime',
            showName: '发送时间',
            width: '170'
          },
          {
            prop: 'reportTime',
            showName: '回执时间',
            width: '170'
          },
          {
            prop: 'smsStatus',
            showName: '发送状态',
            formatData: function (val) {
              if (val == '1') {
                return (val = '成功 ')
              } else if (val == '2') {
                return (val = '失败')
              } else if (val == '3') {
                return (val = '待返回')
              }
            },
            width: '70'
          },
          { prop: 'originalCode', showName: '备注' },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: true, //是否是折叠的
          isDefaultExpand: true, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      tableDataObj1: {
        //表格数据
        loading2: false,
        custom: true,
        tableFontSize: 13,
        id: "ImsReplyDetails",
        totalRow: 0,
        tableData: [],
        tableLabel: [
          //列表表头
          {
            prop: 'username',
            showName: '用户名称',
            width: '120'
          },
          { prop: 'mobile', showName: '手机号码', width: '100' },
          { prop: 'channelId', showName: '通道号', width: '150' },
          { prop: 'content', showName: '回复内容' },
          {
            prop: 'createTime',
            showName: '回复时间',
            width: '170'
          },
          { prop: 'ext', showName: '扩展码', width: '150' },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'isBlacknumber', //关联的表格属性
            contactData: '2', //关联的表格属性-值
            optionName: '加入黑名单', //按钮的显示文字
            optionMethod: 'details', //按钮的方法
            icon: 'el-icon-success', //按钮图标
            optionButtonColor: '#16A589', //按钮颜色
          },
        ],
        tableOptions: [],
      },
    }
  },
  methods: {
    timeClick(val) {
      if (val) {
        this.Times.beginTime = this.moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = this.moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
        this.flag = '3'
      } else {
        this.Times.beginTime = ""
        this.Times.endTime = ""
        this.flag = ''
      }
      
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsReplyId)
      }
      this.selectId = selectId.join(',')
    },
    detailsRow(val) {
      //加入黑名单
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.idStr = val.row.smsReplyId
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        // Object.assign(this.formop.remark,val.row.remark);
      })
    },
    getOperations(val, index) {
      // console.log(val);
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    //列表操作
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
    },
    // handPhone(row){
    //     window.api.post(window.path.upms+'/generatekey/decryptMobile',{
    //         keyId:row.keyId,
    //         smsInfoId:row.decryptMobile,
    //         cipherMobile:row.cipherMobile
    //     },res=>{
    //        this.tableDataObj1.tableData[index].mobile = res.data;
    //     })
    // },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //批量//加入黑名单
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingStatisticalAnalysis/blacklistBatch',
              {
                idStr: this.selectId,
                remark: this.formop.remark,
              },
              () => {
                this.dialogFormVisible = false
                this.getChallDate1()
              }
            )
          } else {
            //加入黑名单
            this.formop.idStr = this.idStr
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingStatisticalAnalysis/blacklistBatch',
              this.formop,
              () => {
                this.dialogFormVisible = false
                this.getChallDate1()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    delAll() {
      //批量加入黑名单
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    //获取 回复记录数据
    getChallDate1() {
      let bb = {}
      Object.assign(bb, this.formData1)
      bb.flag = this.flag
      bb.beginTime = this.Times.beginTime
      bb.endTime = this.Times.endTime
      bb.productId = 4
      this.tableDataObj1.loading2 = true
      window.api.post(window.path.omcs + 'statistics/mmsReply/page', bb, (res) => {
        this.tableDataObj1.loading2 = false
        this.tableDataObj1.tableData = res.data.records
        this.tableDataObj1.totalRow = res.data.total
      })
    },
    //导出 （回复记录）
    export2() {
      let bb = {}
      Object.assign(bb, this.formData1)
      bb.flag = this.flag
      bb.beginTime = this.Times.beginTime
      bb.endTime = this.Times.endTime
      bb.isDownload = 1
      bb.productId = 4
      if (this.tableDataObj1.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingStatisticalAnalysis/smsReply/page',
          bb,
          '回复记录报表.xlsx'
        )
      }
    },
    handledatepluginVal: function (val1, val2) {
      if (val1) {
        this.Times.beginTime = val1
        this.Times.endTime = val2
        this.flag = '3'
      } else {
        this.flag = '1'
        this.Times.beginTime = ''
        this.Times.endTime = ''
      }
    },
    handleChangeTimeOptions: function () {
      // this.datePluginValueList.datePluginValue = ''
    },
    //发送查询
    Query1() {
      Object.assign(this.formData, this.form)
    },
    //回复查询
    Query2() {
      Object.assign(this.formData1, this.form1)
      this.getChallDate1()
    },
    //发送重置
    reset() {
      this.$refs.sendjl.resetFields()
      Object.assign(this.formData, this.form)
    },
    //回复重置
    reset1() {
      this.$refs.sendjl1.resetFields()
      Object.assign(this.formData1, this.form1)
    },
    handleSizeChange(size) {
      this.formData.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formData.currentPage = currentPage
    },
    handleSizeChange1(size) {
      this.formData1.pageSize = size
    },
    handleCurrentChange1: function (currentPage) {
      this.formData1.currentPage = currentPage
    },
  },
  mounted() {
    // this.getChallDate1();
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate1()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate1()
    })
  },
  watch: {
    //监听时间范围
    flag: function (val) {
      // if (val != '3') {
      //   this.Times.beginTime = ''
      //   this.Times.endTime = ''
      // }
      // this.getChallDate1()
      if (val == '1') {
        this.Times.beginTime = monment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = monment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
      } else if (val == '2') {
        this.Times.beginTime = monment()
          .subtract(4, 'days')
          .format('YYYY-MM-DD 00:00:00')
        this.Times.endTime = monment().format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          monment().subtract(4, 'days').format('YYYY-MM-DD 00:00:00'),
          monment().format('YYYY-MM-DD HH:mm:ss'),
        ]
      }
    },
    //监听时间是否改变
    Times: {
      handler(val) {
        if (val) {
          this.getChallDate1()
        }
      },
      deep: true,
    },
    activeName2: function (val) {
      this.getChallDate1()
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
    formData1: {
      handler(val) {
        this.getChallDate1()
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
</style>
