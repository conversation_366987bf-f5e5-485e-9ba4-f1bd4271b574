<template>
    <div class="sales-management-container">
      <div class="query-section">
        <el-form :inline="true" :model="queryForm" class="query-form">
          <el-form-item label="销售姓名">
            <el-input v-model="queryForm.salesName" placeholder="请输入销售姓名" clearable></el-input>
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="queryForm.mobile" placeholder="请输入手机号码" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
  
      <div class="table-operation">
        <el-button type="primary" @click="openAddDialog">新增销售人员</el-button>
      </div>
  
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
        highlight-current-row
      >
        <!-- <el-table-column type="index" label="序号" width="60" align="center"></el-table-column> -->
        <el-table-column prop="salesName" label="销售姓名" show-overflow-tooltip></el-table-column>
        <el-table-column label="手机号码" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.isDecrypted">{{ scope.row.mobile }}</span>
            <span style="cursor: pointer;color: #16a589;" v-else @click="handleDecryptMobile(scope.row)">{{ scope.row.maskMobile }}</span>
            <!-- <el-button 
              v-if="!scope.row.isDecrypted" 
              link 
              type="primary" 
              size="small" 
              @click="handleDecryptMobile(scope.row)"
            >
              解密
            </el-button> -->
          </template>
        </el-table-column>
        <!-- <el-table-column label="创建时间" show-overflow-tooltip>
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
  
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
  
      <!-- 新增/编辑对话框 -->
      <el-dialog
        :title="dialogType === 'add' ? '新增销售人员' : '编辑销售人员'"
        v-model="dialogVisible"
        width="650px"
        :close-on-click-modal="false"
        :destroy-on-close="true"
      >
        <el-form :model="dialogType === 'edit' ? formData : { salesList }" :rules="rules" ref="formRef" label-width="100px">
          <!-- 单个销售人员编辑 -->
          <template v-if="dialogType === 'edit'">
            <el-form-item label="销售姓名" prop="salesName">
              <el-input v-model="formData.salesName" placeholder="请输入销售姓名"></el-input>
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input v-model="formData.mobile" placeholder="请输入手机号码"></el-input>
            </el-form-item>
          </template>
          
          <!-- 批量添加销售人员 -->
          <template v-else>
            <div class="batch-add-area">
              <div class="batch-header">
                <div class="title">销售人员信息</div>
                <el-button type="primary" plain size="small" @click="addSalesItem">添加一行</el-button>
              </div>
              
              <div v-for="(item, index) in salesList" :key="index" class="sales-item">
                <el-row :gutter="20">
                  <el-col :span="9">
                    <el-form-item 
                      :prop="`salesList.${index}.salesName`" 
                      :rules="rules.salesName"
                      label-width="0"
                    >
                      <el-input 
                        v-model="salesList[index].salesName" 
                        placeholder="请输入销售姓名"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="9">
                    <el-form-item 
                      :prop="`salesList.${index}.mobile`" 
                      :rules="rules.mobile"
                      label-width="0"
                    >
                      <el-input 
                        v-model="salesList[index].mobile" 
                        placeholder="请输入手机号码"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" class="operation-col">
                    <el-button 
                      type="danger" 
                      circle 
                      plain
                      icon="Delete"
                      @click="removeSalesItem(index)"
                      :disabled="salesList.length <= 1"
                    ></el-button>
                  </el-col>
                </el-row>
              </div>
            </div>
          </template>
        </el-form>
        
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import axios from 'axios'
  
  // 查询表单
  const queryForm = reactive({
    salesName: '',
    mobile: ''
  })
  
  // 表格数据
  const tableData = ref([])
  const loading = ref(false)
  const total = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(10)
  
  // 对话框相关
  const dialogVisible = ref(false)
  const dialogType = ref('add') // add 或 edit
  const formRef = ref(null)
  const formData = reactive({
    id: '',
    salesName: '',
    mobile: ''
  })
  
  // 批量添加的销售人员列表
  const salesList = ref([
    { salesName: '', mobile: '' }
  ])
  
  // 表单验证规则
  const rules = reactive({
    salesName: [
      { required: true, message: '请输入销售姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    mobile: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    'salesList.0.salesName': [
      { required: true, message: '请输入销售姓名', trigger: 'blur' },
      { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    'salesList.0.mobile': [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
  })
  
  // 初始化加载数据
  onMounted(() => {
    fetchSalesList()
  })
  
  // 获取销售人员列表
  const fetchSalesList = async () => {
    loading.value = true
    try {
      // 调用接口获取销售人员列表
      window.api.post(
        window.path.omcs + 'consumersalesinfo/page', 
        {
          ...queryForm,
          currentPage: currentPage.value,
          pageSize: pageSize.value
        }, 
        (res) => {
          if (res.code === 200) {
            // 处理每一条记录，添加解密状态标记
            const records = res.data.records || []
            tableData.value = records.map(item => ({
              ...item,
              isDecrypted: false // 初始状态为未解密
            }))
            total.value = res.data.total || 0
          } else {
            ElMessage.error(res.msg || '获取销售人员列表失败')
            tableData.value = []
            total.value = 0
          }
          loading.value = false
        }
      )
    } catch (error) {
      console.error('获取销售人员列表失败:', error)
      loading.value = false
      ElMessage.error('获取销售人员列表失败')
    }
  }
  
  // 查询
  const handleQuery = () => {
    currentPage.value = 1
    fetchSalesList()
  }
  
  // 重置查询条件
  const resetQuery = () => {
    queryForm.salesName = ''
    queryForm.mobile = ''
    handleQuery()
  }
  
  // 打开新增对话框
  const openAddDialog = () => {
    dialogType.value = 'add'
    salesList.value = [{ salesName: '', mobile: '' }]
    dialogVisible.value = true
    
    // 重置销售列表相关的验证规则状态
    Object.keys(rules).forEach(key => {
      if (key.startsWith('salesList.') && key !== 'salesList.0.salesName' && key !== 'salesList.0.mobile') {
        delete rules[key]
      }
    })
    
    nextTick(() => {
      if (formRef.value) {
        formRef.value.resetFields()
        formRef.value.clearValidate()
      }
    })
  }
  
  // 打开编辑对话框
  const handleEdit = (row) => {
    dialogType.value = 'edit'
    // Object.assign(formData, row)
    formData.id = row.id
    formData.salesName = row.salesName
    formData.mobile = row.mobile
    dialogVisible.value = true
    
    nextTick(() => {
      if (formRef.value) {
        formRef.value.resetFields()
      }
    })
  }
  
  // 添加一行销售人员
  const addSalesItem = () => {
    let newSalesItem = { salesName: '', mobile: '' }
    salesList.value.push(newSalesItem)
    
    // 动态添加新行的验证规则
    const newIndex = salesList.value.length - 1
    const salesNameKey = `salesList.${newIndex}.salesName`
    const mobileKey = `salesList.${newIndex}.mobile`
    
    if (!rules[salesNameKey]) {
      rules[salesNameKey] = [
        { required: true, message: '请输入销售姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
      ]
    }
    
    if (!rules[mobileKey]) {
      rules[mobileKey] = [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ]
    }
    
    // 下一个周期更新表单
    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    })
  }
  
  // 移除一行销售人员
  const removeSalesItem = (index) => {
    if (salesList.value.length > 1) {
      salesList.value.splice(index, 1)
      
      // 清除对应的验证规则
      delete rules[`salesList.${index}.salesName`]
      delete rules[`salesList.${index}.mobile`]
      
      // 下一个周期更新表单
      nextTick(() => {
        if (formRef.value) {
          formRef.value.clearValidate()
        }
      })
    }
  }
  
  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return
    
    // 强制触发所有字段验证
    await nextTick()
    
    formRef.value.validate(async (valid, invalidFields) => {
      if (valid) {
        try {
          if (dialogType.value === 'add') {
            // 检查所有输入是否有效
            let isAllValid = true
            
            for (let i = 0; i < salesList.value.length; i++) {
              const item = salesList.value[i]
              if (!item.salesName || !item.mobile || item.salesName.length < 2 || !(/^1[3-9]\d{9}$/.test(item.mobile))) {
                isAllValid = false
                break
              }
            }
            
            if (!isAllValid) {
              ElMessage.warning('请确保所有销售人员信息填写正确')
              return
            }
            
            // 过滤掉可能的空行
            const validSalesList = salesList.value.filter(item => 
              item.salesName.trim() !== '' && item.mobile.trim() !== ''
            )
            
            if (validSalesList.length === 0) {
              ElMessage.warning('请至少添加一条有效的销售人员信息')
              return
            }
            
            // 新增销售人员
            window.api.post(
              window.path.omcs + 'consumersalesinfo/add',
              validSalesList,
              (res) => {
                if (res.code === 200) {
                  ElMessage.success('添加成功')
                  dialogVisible.value = false
                  fetchSalesList()
                } else {
                  ElMessage.error(res.msg || '添加失败')
                }
              }
            )
          } else {
            // 编辑销售人员
            window.api.put(
              window.path.omcs + 'consumersalesinfo/update',
              formData,
              (res) => {
                if (res.code === 200) {
                  ElMessage.success('更新成功')
                  dialogVisible.value = false
                  fetchSalesList()
                } else {
                  ElMessage.error(res.msg || '更新失败')
                }
              }
            )
          }
        } catch (error) {
          console.error('操作失败:', error)
          ElMessage.error('操作失败')
        }
      }
    })
  }
  
  // 删除销售人员
  const handleDelete = (row) => {
    ElMessageBox.confirm('确认删除该销售人员吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      window.api.delete(
        window.path.omcs + 'consumersalesinfo/delete/' + row.id,
        {},
        (res) => {
          if (res.code === 200) {
            ElMessage.success('删除成功')
            fetchSalesList()
          } else {
            ElMessage.error(res.msg || '删除失败')
          }
        }
      )
    }).catch(() => {})
  }
  
  // 分页大小变化
  const handleSizeChange = (val) => {
    pageSize.value = val
    fetchSalesList()
  }
  
  // 当前页变化
  const handleCurrentChange = (val) => {
    currentPage.value = val
    fetchSalesList()
  }
  
  // 格式化日期
  const formatDate = (timestamp) => {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  }
  
  // 解密手机号码
  const handleDecryptMobile = (row) => {
    if (!row.keyId || !row.cipherMobile) {
      ElMessage.warning('缺少解密所需的信息')
      return
    }
    
    try {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code === 200 && res.data) {
            // 更新表格数据中的手机号
            const index = tableData.value.findIndex(item => item.id === row.id)
            if (index !== -1) {
              tableData.value[index].mobile = res.data
              tableData.value[index].isDecrypted = true
            }
            ElMessage.success('手机号解密成功')
          } else {
            ElMessage.error(res.msg || '手机号解密失败')
          }
        }
      )
    } catch (error) {
      console.error('手机号解密失败:', error)
      ElMessage.error('手机号解密失败')
    }
  }
  </script>
  
  <style scoped>
  .sales-management-container {
    padding: 20px;
    background-color: #fff;
  }
  
  .query-section {
    margin-bottom: 20px;
    padding: 20px;
    /* background-color: #f5f7fa; */
    border-radius: 4px;
  }
  
  .table-operation {
    margin-bottom: 15px;
    display: flex;
    justify-content: flex-start;
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .batch-add-area {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 10px;
  }
  
  .batch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
    border-bottom: 1px dashed #ebeef5;
  }
  
  .batch-header .title {
    font-weight: bold;
    color: #606266;
  }
  
  .sales-item {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #f0f0f0;
  }
  
  .sales-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
  
  .operation-col {
    display: flex;
    /* align-items: center; */
  }
  </style> 