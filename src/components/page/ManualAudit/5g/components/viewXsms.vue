<template>
  <div>
    <div class="sso_right">
      <el-tabs v-model="activeName">
        <el-tab-pane label="阅信" name="first">
          <div style="display: flex">
            <div class="sso_view_content">
              <div class="sso_navbar">
                <img
                  src="../../../../../assets/images/navbar.png"
                  alt=""
                  class="view_img_bag"
                />
              </div>
              <div class="sso_view_item">
                <div
                  v-if="ssoFrom.templateType == 'IMAGE_AND_TEXT'"
                  class="content_item"
                >
                  <div
                    class="item_img"
                    v-if="xsmsForm.ssoContentFrom.imgcontents.sourceId == ''"
                  >
                    <img
                      src="../../../../../assets/images/ssobg.png"
                      alt=""
                      class="image"
                    />
                  </div>
                  <div v-else class="item_img">
                    <a
                      :href="xsmsForm.ssoContentFrom.imgcontents.url"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <img
                        :src="xsmsForm.ssoContentFrom.imgcontents.url"
                        class="image"
                      />
                    </a>
                  </div>
                  <div class="sso_text">
                    <div
                      class="sso_title"
                      v-if="xsmsForm.ssoContentFrom.titlecontents.content == ''"
                    >
                      标题最多一行，不超过17个字
                    </div>
                    <div v-else class="sso_title">
                      {{ xsmsForm.ssoContentFrom.titlecontents.content }}
                    </div>
                    <div
                      class="sso_tcontent"
                      v-if="xsmsForm.ssoContentFrom.textcontents.content == ''"
                    >
                      摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字
                    </div>
                    <div class="sso_tcontent" v-else>
                      {{ xsmsForm.ssoContentFrom.textcontents.content }}
                    </div>
                  </div>
                  <div class="sso_btn">
                    <div
                      class="btn"
                      v-if="xsmsForm.ssoContentFrom.btncontents.content == ''"
                    >
                      按钮
                    </div>
                    <div class="btn" v-else>
                      {{ xsmsForm.ssoContentFrom.btncontents.content }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="xsmsForm.templateType == 'STANDARD_IMAGE_AND_TEXT'"
                  class="items_contents"
                >
                  <div
                    class="item_img"
                    v-if="xsmsForm.ssosContentFrom.firstImg.sourceId == ''"
                  >
                    <img
                      src="../../../../../assets/images/ssobg.png"
                      alt=""
                      class="image"
                    />
                    <div class="image_header_tite">
                      标题最多两行，不超过30个字标题最多两行，不超过30个字
                    </div>
                  </div>
                  <div v-else class="item_img">
                    <a
                      :href="xsmsForm.ssosContentFrom.firstImg.url"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <img
                        :src="xsmsForm.ssosContentFrom.firstImg.url"
                        class="image"
                      />
                    </a>
                    <div class="image_header_tite">
                      {{ xsmsForm.ssosContentFrom.firstTit.content }}
                    </div>
                  </div>
                  <div
                    class="first_contents"
                    v-if="
                      xsmsForm.number == '2' ||
                      xsmsForm.number == '3' ||
                      xsmsForm.number == '4'
                    "
                  >
                    <div class="first_img">
                      <img
                        v-if="xsmsForm.ssosContentFrom.secondImg.sourceId == ''"
                        src="../../../../../assets/images/ssobg.png"
                        alt=""
                        class="image_s"
                      />
                      <a
                        v-else
                        :href="xsmsForm.ssosContentFrom.secondImg.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="xsmsForm.ssosContentFrom.secondImg.url"
                          class="image_s"
                        />
                      </a>
                    </div>
                    <div
                      v-if="xsmsForm.ssosContentFrom.secondTit.content == ''"
                      class="first_tit"
                    >
                      标题最多两行，不超过26个字
                    </div>
                    <div v-else class="first_tit">
                      {{ xsmsForm.ssosContentFrom.secondTit.content }}
                    </div>
                  </div>
                  <div
                    class="first_contents"
                    v-if="xsmsForm.number == '3' || xsmsForm.number == '4'"
                  >
                    <div class="first_img">
                      <img
                        v-if="xsmsForm.ssosContentFrom.thirdImg.sourceId == ''"
                        src="../../../../../assets/images/ssobg.png"
                        alt=""
                        class="image_s"
                      />
                      <a
                        v-else
                        :href="xsmsForm.ssosContentFrom.thirdImg.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="xsmsForm.ssosContentFrom.thirdImg.url"
                          class="image_s"
                        />
                      </a>
                    </div>
                    <div
                      v-if="xsmsForm.ssosContentFrom.thirdTit.content == ''"
                      class="first_tit"
                    >
                      标题最多两行，不超过26个字
                    </div>
                    <div v-else class="first_tit">
                      {{ xsmsForm.ssosContentFrom.thirdTit.content }}
                    </div>
                  </div>
                  <div class="first_contents" v-if="xsmsForm.number == '4'">
                    <div class="first_img">
                      <img
                        v-if="xsmsForm.ssosContentFrom.fourthImg.sourceId == ''"
                        src="../../../../../assets/images/ssobg.png"
                        alt=""
                        class="image_s"
                      />
                      <a
                        v-else
                        :href="xsmsForm.ssosContentFrom.fourthImg.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="xsmsForm.ssosContentFrom.fourthImg.url"
                          class="image_s"
                        />
                      </a>
                    </div>
                    <div
                      v-if="xsmsForm.ssosContentFrom.fourthTit.content == ''"
                      class="first_tit"
                    >
                      标题最多两行，不超过26个字
                    </div>
                    <div v-else class="first_tit">
                      {{ xsmsForm.ssosContentFrom.fourthTit.content }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="xsmsForm.templateType == 'RED_PACKET'"
                  class="content_item_red"
                >
                  <div class="red_header">
                    <div>
                      <img
                        src="../../../../../assets/images/red_header.png"
                        alt=""
                      />
                    </div>
                    <div class="red_logo">
                      <img
                        v-if="
                          xsmsForm.ssoRedPacketFrom.imgcontents.sourceId == ''
                        "
                        src="../../../../../assets/images/red_logo.png"
                        alt=""
                        width="100%"
                        height="100%"
                      />
                      <a
                        v-else
                        :href="xsmsForm.ssoRedPacketFrom.imgcontents.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="xsmsForm.ssoRedPacketFrom.imgcontents.url"
                          alt=""
                          width="100%"
                          height="100%"
                          style="border-radius: 50%"
                        />
                      </a>
                    </div>
                  </div>
                  <div
                    v-if="xsmsForm.ssoRedPacketFrom.titlecontents.content == ''"
                    class="semll_tit"
                  >
                    小标题最多1行，不超过18个字
                  </div>
                  <div v-else class="semll_tit">
                    {{ xsmsForm.ssoRedPacketFrom.titlecontents.content }}
                  </div>
                  <div
                    v-if="
                      xsmsForm.ssoRedPacketFrom.bigtitlecontents.content == ''
                    "
                    class="big_tit"
                  >
                    大标题最多1行，不超过11个字
                  </div>
                  <div v-else class="big_tit">
                    {{ xsmsForm.ssoRedPacketFrom.bigtitlecontents.content }}
                  </div>
                  <div class="rad_content">
                    <div
                      v-if="
                        xsmsForm.ssoRedPacketFrom.textcontents.content == ''
                      "
                      class="red_text"
                    >
                      摘要内容居中展示，不超过54个字。摘要内容居中展示，不超过54个字。摘要内容居中展示，不超过54个字。
                    </div>
                    <div v-else class="red_text">
                      {{ xsmsForm.ssoRedPacketFrom.textcontents.content }}
                    </div>
                  </div>
                  <div class="rad_footer">
                    <div
                      v-if="xsmsForm.ssoRedPacketFrom.btncontents.content == ''"
                      class="red_btn"
                    >
                      立即领取
                    </div>
                    <div v-else class="red_btn">
                      {{ xsmsForm.ssoRedPacketFrom.btncontents.content }}
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="xsmsForm.templateType == 'IMAGE_AND_TEXT'"
                style="margin: 10px 30px; font-size: 12px"
              >
                审核标准：摘要内容不可超过3行
              </div>
              <div class="sso_view_footer">
                <img
                  src="../../../../../assets/images/foot.png"
                  alt=""
                  class="view_img_bag"
                />
              </div>
            </div>
            <div class="sso_view_right">
              <image-text
                v-if="xsmsForm.templateType == 'IMAGE_AND_TEXT'"
                :list="xsmsForm"
              ></image-text>
              <red-temp
                v-if="ssoFrom.templateType == 'RED_PACKET'"
                :list="xsmsForm"
              ></red-temp>
              <images-texts
                v-if="ssoFrom.templateType == 'STANDARD_IMAGE_AND_TEXT'"
                :list="xsmsForm"
              ></images-texts>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="AIM" name="second">
          <div style="display: flex">
            <div class="sso_view_content">
              <div class="sso_navbar">
                <img
                  src="../../../../../assets/images/navbar.png"
                  alt=""
                  class="view_img_bag"
                />
              </div>
              <div class="sso_view_item">
                <div
                  v-if="aimForm.templateType == 'IMAGE_AND_TEXT'"
                  class="content_item"
                >
                  <div
                    class="item_img"
                    v-if="aimForm.ssoContentFrom.imgcontents.sourceId == ''"
                  >
                    <img
                      src="../../../../../assets/images/ssobg.png"
                      alt=""
                      class="image"
                    />
                  </div>
                  <div v-else class="item_img">
                    <a
                      :href="aimForm.ssoContentFrom.imgcontents.url"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <img
                        :src="aimForm.ssoContentFrom.imgcontents.url"
                        class="image"
                      />
                    </a>
                  </div>
                  <div class="sso_text">
                    <div
                      class="sso_title"
                      v-if="aimForm.ssoContentFrom.titlecontents.content == ''"
                    >
                      标题最多一行，不超过17个字
                    </div>
                    <div v-else class="sso_title">
                      {{ aimForm.ssoContentFrom.titlecontents.content }}
                    </div>
                    <div
                      class="sso_tcontent"
                      v-if="aimForm.ssoContentFrom.textcontents.content == ''"
                    >
                      摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字，摘要不超过69个字
                    </div>
                    <div class="sso_tcontent" v-else>
                      {{ aimForm.ssoContentFrom.textcontents.content }}
                    </div>
                  </div>
                  <div class="sso_btn">
                    <div
                      class="btn"
                      v-if="aimForm.ssoContentFrom.btncontents.content == ''"
                    >
                      按钮
                    </div>
                    <div class="btn" v-else>
                      {{ aimForm.ssoContentFrom.btncontents.content }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="aimForm.templateType == 'STANDARD_IMAGE_AND_TEXT'"
                  class="items_contents"
                >
                  <div
                    class="item_img"
                    v-if="aimForm.ssosContentFrom.firstImg.sourceId == ''"
                  >
                    <img
                      src="../../../../../assets/images/ssobg.png"
                      alt=""
                      class="image"
                    />
                    <div class="image_header_tite">
                      标题最多两行，不超过30个字标题最多两行，不超过30个字
                    </div>
                  </div>
                  <div v-else class="item_img">
                    <a
                      :href="aimForm.ssosContentFrom.firstImg.url"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <img
                        :src="aimForm.ssosContentFrom.firstImg.url"
                        class="image"
                      />
                    </a>
                    <div class="image_header_tite">
                      {{ aimForm.ssosContentFrom.firstTit.content }}
                    </div>
                  </div>
                  <div
                    class="first_contents"
                    v-if="
                      aimForm.number == '2' ||
                      aimForm.number == '3' ||
                      aimForm.number == '4'
                    "
                  >
                    <div class="first_img">
                      <img
                        v-if="aimForm.ssosContentFrom.secondImg.sourceId == ''"
                        src="../../../../../assets/images/ssobg.png"
                        alt=""
                        class="image_s"
                      />
                      <a
                        v-else
                        :href="aimForm.ssosContentFrom.secondImg.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="aimForm.ssosContentFrom.secondImg.url"
                          class="image_s"
                        />
                      </a>
                    </div>
                    <div
                      v-if="aimForm.ssosContentFrom.secondTit.content == ''"
                      class="first_tit"
                    >
                      标题最多两行，不超过26个字
                    </div>
                    <div v-else class="first_tit">
                      {{ aimForm.ssosContentFrom.secondTit.content }}
                    </div>
                  </div>
                  <div
                    class="first_contents"
                    v-if="aimForm.number == '3' || aimForm.number == '4'"
                  >
                    <div class="first_img">
                      <img
                        v-if="aimForm.ssosContentFrom.thirdImg.sourceId == ''"
                        src="../../../../../assets/images/ssobg.png"
                        alt=""
                        class="image_s"
                      />
                      <a
                        v-else
                        :href="aimForm.ssosContentFrom.thirdImg.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="aimForm.ssosContentFrom.thirdImg.url"
                          class="image_s"
                        />
                      </a>
                    </div>
                    <div
                      v-if="aimForm.ssosContentFrom.thirdTit.content == ''"
                      class="first_tit"
                    >
                      标题最多两行，不超过26个字
                    </div>
                    <div v-else class="first_tit">
                      {{ aimForm.ssosContentFrom.thirdTit.content }}
                    </div>
                  </div>
                  <div class="first_contents" v-if="aimForm.number == '4'">
                    <div class="first_img">
                      <img
                        v-if="aimForm.ssosContentFrom.fourthImg.sourceId == ''"
                        src="../../../../../assets/images/ssobg.png"
                        alt=""
                        class="image_s"
                      />
                      <a
                        v-else
                        :href="aimForm.ssosContentFrom.fourthImg.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="aimForm.ssosContentFrom.fourthImg.url"
                          class="image_s"
                        />
                      </a>
                    </div>
                    <div
                      v-if="aimForm.ssosContentFrom.fourthTit.content == ''"
                      class="first_tit"
                    >
                      标题最多两行，不超过26个字
                    </div>
                    <div v-else class="first_tit">
                      {{ aimForm.ssosContentFrom.fourthTit.content }}
                    </div>
                  </div>
                </div>
                <div
                  v-if="aimForm.templateType == 'RED_PACKET'"
                  class="content_item_red"
                >
                  <div class="red_header">
                    <div>
                      <img
                        src="../../../../../assets/images/red_header.png"
                        alt=""
                      />
                    </div>
                    <div class="red_logo">
                      <img
                        v-if="
                          aimForm.ssoRedPacketFrom.imgcontents.sourceId == ''
                        "
                        src="../../../../../assets/images/red_logo.png"
                        alt=""
                        width="100%"
                        height="100%"
                      />
                      <a
                        v-else
                        :href="aimForm.ssoRedPacketFrom.imgcontents.url"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          :src="aimForm.ssoRedPacketFrom.imgcontents.url"
                          alt=""
                          width="100%"
                          height="100%"
                          style="border-radius: 50%"
                        />
                      </a>
                    </div>
                  </div>
                  <div
                    v-if="aimForm.ssoRedPacketFrom.titlecontents.content == ''"
                    class="semll_tit"
                  >
                    小标题最多1行，不超过18个字
                  </div>
                  <div v-else class="semll_tit">
                    {{ aimForm.ssoRedPacketFrom.titlecontents.content }}
                  </div>
                  <div
                    v-if="
                      aimForm.ssoRedPacketFrom.bigtitlecontents.content == ''
                    "
                    class="big_tit"
                  >
                    大标题最多1行，不超过11个字
                  </div>
                  <div v-else class="big_tit">
                    {{ aimForm.ssoRedPacketFrom.bigtitlecontents.content }}
                  </div>
                  <div class="rad_content">
                    <div
                      v-if="aimForm.ssoRedPacketFrom.textcontents.content == ''"
                      class="red_text"
                    >
                      摘要内容居中展示，不超过54个字。摘要内容居中展示，不超过54个字。摘要内容居中展示，不超过54个字。
                    </div>
                    <div v-else class="red_text">
                      {{ aimForm.ssoRedPacketFrom.textcontents.content }}
                    </div>
                  </div>
                  <div class="rad_footer">
                    <div
                      v-if="aimForm.ssoRedPacketFrom.btncontents.content == ''"
                      class="red_btn"
                    >
                      立即领取
                    </div>
                    <div v-else class="red_btn">
                      {{ aimForm.ssoRedPacketFrom.btncontents.content }}
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="xsmsForm.templateType == 'IMAGE_AND_TEXT'"
                style="margin: 10px 30px; font-size: 12px"
              >
                审核标准：摘要内容不可超过3行
              </div>
              <div class="sso_view_footer">
                <img
                  src="../../../../../assets/images/foot.png"
                  alt=""
                  class="view_img_bag"
                />
              </div>
            </div>
            <div class="sso_view_right">
              <aimimage-text
                v-if="aimForm.templateType == 'IMAGE_AND_TEXT'"
                :list="aimForm"
              ></aimimage-text>
              <aimred-temp
                v-if="aimForm.templateType == 'RED_PACKET'"
                :list="aimForm"
              ></aimred-temp>
              <aimimages-texts
                v-if="aimForm.templateType == 'STANDARD_IMAGE_AND_TEXT'"
                :list="aimForm"
              ></aimimages-texts>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!-- <div class="sso_view_tit">阅览效果</div> -->
    </div>
  </div>
</template>

<script>
import ImageText from './xsmscomponents/imageAndText.vue'
import RedTemp from './xsmscomponents/redTemp.vue'
import ImagesTexts from './xsmscomponents/imagesAndTexts.vue'
import AIMImageText from './aimcomponents/imageAndText.vue'
import AIMRedTemp from './aimcomponents/redTemp.vue'
import AIMImagesTexts from './aimcomponents/imagesAndTexts.vue'
export default {
  props: ['list', 'xsmsPages', 'aimPages'],
  components: {
    'image-text': ImageText,
    'red-temp': RedTemp,
    'images-texts': ImagesTexts,
    'aimimage-text': AIMImageText,
    'aimred-temp': AIMRedTemp,
    'aimimages-texts': AIMImagesTexts,
  },
  data() {
    return {
      activeName: 'first',
      xsmsForm: {
        templateType: 'IMAGE_AND_TEXT',
        number: '4',
        ssoContentFrom: {
          imgcontents: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 1,
          },
          titlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          textcontents: {
            type: 'text',
            content: '',
            isTextTitle: false,
            positionNumber: 3,
          },
          btncontents: {
            type: 'button',
            content: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 4,
          },
        },
        ssosContentFrom: {
          firstImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 1,
          },
          firstTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          secondImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 3,
          },
          secondTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 4,
          },
          thirdImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 5,
          },
          thirdTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 6,
          },
          fourthImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 7,
          },
          fourthTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 8,
          },
        },
        ssoRedPacketFrom: {
          imgcontents: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            positionNumber: 1,
          },
          titlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          bigtitlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 3,
          },
          textcontents: {
            type: 'text',
            content: '',
            isTextTitle: false,
            positionNumber: 4,
          },
          btncontents: {
            type: 'button',
            content: '立即领取',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 5,
          },
        },
      },
      aimForm: {
        templateType: 'IMAGE_AND_TEXT',
        number: '4',
        ssoContentFrom: {
          imgcontents: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 1,
          },
          titlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          textcontents: {
            type: 'text',
            content: '',
            isTextTitle: false,
            positionNumber: 3,
          },
          btncontents: {
            type: 'button',
            content: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 4,
          },
        },
        ssosContentFrom: {
          firstImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 1,
          },
          firstTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          secondImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 3,
          },
          secondTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 4,
          },
          thirdImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 5,
          },
          thirdTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 6,
          },
          fourthImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 7,
          },
          fourthTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 8,
          },
        },
        ssoRedPacketFrom: {
          imgcontents: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            positionNumber: 1,
          },
          titlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          bigtitlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 3,
          },
          textcontents: {
            type: 'text',
            content: '',
            isTextTitle: false,
            positionNumber: 4,
          },
          btncontents: {
            type: 'button',
            content: '立即领取',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 5,
          },
        },
      },
      ssoFrom: {
        chatbotId: '',
        templateName: '',
        templateType: 'IMAGE_AND_TEXT',
        number: '4',
        ssoContentFrom: {
          imgcontents: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 1,
          },
          titlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          textcontents: {
            type: 'text',
            content: '',
            isTextTitle: false,
            positionNumber: 3,
          },
          btncontents: {
            type: 'button',
            content: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 4,
          },
        },
        ssosContentFrom: {
          firstImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 1,
          },
          firstTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          secondImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 3,
          },
          secondTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 4,
          },
          thirdImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 5,
          },
          thirdTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 6,
          },
          fourthImg: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            thumbnailUrl: '',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 7,
          },
          fourthTit: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 8,
          },
        },
        ssoRedPacketFrom: {
          imgcontents: {
            type: 'image',
            srcType: '',
            sourceId: '',
            url: '',
            positionNumber: 1,
          },
          titlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 2,
          },
          bigtitlecontents: {
            type: 'text',
            content: '',
            isTextTitle: true,
            positionNumber: 3,
          },
          textcontents: {
            type: 'text',
            content: '',
            isTextTitle: false,
            positionNumber: 4,
          },
          btncontents: {
            type: 'button',
            content: '立即领取',
            actionType: '',
            action: {
              target: '',
            },
            positionNumber: 5,
          },
        },
      },
    }
  },
  watch: {
    list: {
      handler(val) {
        // console.log(obj, "obj");
        if (val.templateType != '') {
          let obj = JSON.parse(val.templateContent)
          if (val.templateType == 'IMAGE_AND_TEXT') {
            this.ssoFrom.templateType = val.templateType
            this.ssoFrom.ssoContentFrom.imgcontents = obj[0].contents[0]
            this.ssoFrom.ssoContentFrom.imgcontents.srcType =
              obj[0].contents[0].fileType
            this.ssoFrom.ssoContentFrom.imgcontents.url =
              obj[0].contents[0].fileUrl
            this.ssoFrom.ssoContentFrom.titlecontents = obj[0].contents[1]
            this.ssoFrom.ssoContentFrom.textcontents = obj[0].contents[2]
            this.ssoFrom.ssoContentFrom.btncontents = obj[0].contents[3]
          } else if (val.templateType == 'STANDARD_IMAGE_AND_TEXT') {
            this.ssoFrom.templateType = val.templateType
            if (obj[0].contents.length == 8) {
              this.ssoFrom.number = '4'
              this.ssoFrom.ssosContentFrom.firstImg = obj[0].contents[0]
              this.ssoFrom.ssosContentFrom.firstImg.srcType =
                obj[0].contents[0].srcType
              this.ssoFrom.ssosContentFrom.firstImg.url =
                obj[0].contents[0].fileUrl
              this.ssoFrom.ssosContentFrom.firstTit = obj[0].contents[1]
              // --2
              this.ssoFrom.ssosContentFrom.secondImg = obj[0].contents[2]
              this.ssoFrom.ssosContentFrom.secondImg.srcType =
                obj[0].contents[2].srcType
              this.ssoFrom.ssosContentFrom.secondImg.url =
                obj[0].contents[2].fileUrl
              this.ssoFrom.ssosContentFrom.secondTit = obj[0].contents[3]
              // --3
              this.ssoFrom.ssosContentFrom.thirdImg = obj[0].contents[4]
              this.ssoFrom.ssosContentFrom.thirdImg.srcType =
                obj[0].contents[4].srcType
              this.ssoFrom.ssosContentFrom.thirdImg.url =
                obj[0].contents[4].fileUrl
              this.ssoFrom.ssosContentFrom.thirdTit = obj[0].contents[5]
              // --4
              this.ssoFrom.ssosContentFrom.fourthImg = obj[0].contents[6]
              this.ssoFrom.ssosContentFrom.fourthImg.srcType =
                obj[0].contents[6].srcType
              this.ssoFrom.ssosContentFrom.fourthImg.url =
                obj[0].contents[6].fileUrl
              this.ssoFrom.ssosContentFrom.fourthTit = obj[0].contents[7]
            } else if (obj[0].contents.length == 6) {
              this.ssoFrom.number = '3'
              this.ssoFrom.ssosContentFrom.firstImg = obj[0].contents[0]
              this.ssoFrom.ssosContentFrom.firstImg.srcType =
                obj[0].contents[0].srcType
              this.ssoFrom.ssosContentFrom.firstImg.url =
                obj[0].contents[0].fileUrl
              this.ssoFrom.ssosContentFrom.firstTit = obj[0].contents[1]
              // --2
              this.ssoFrom.ssosContentFrom.secondImg = obj[0].contents[2]
              this.ssoFrom.ssosContentFrom.secondImg.srcType =
                obj[0].contents[2].srcType
              this.ssoFrom.ssosContentFrom.secondImg.url =
                obj[0].contents[2].fileUrl
              this.ssoFrom.ssosContentFrom.secondTit = obj[0].contents[3]
              // --3
              this.ssoFrom.ssosContentFrom.thirdImg = obj[0].contents[4]
              this.ssoFrom.ssosContentFrom.thirdImg.srcType =
                obj[0].contents[4].srcType
              this.ssoFrom.ssosContentFrom.thirdImg.url =
                obj[0].contents[4].fileUrl
              this.ssoFrom.ssosContentFrom.thirdTit = obj[0].contents[5]
            } else if (obj[0].contents.length == 4) {
              this.ssoFrom.number = '2'
              this.ssoFrom.ssosContentFrom.firstImg = obj[0].contents[0]
              this.ssoFrom.ssosContentFrom.firstImg.srcType =
                obj[0].contents[0].srcType
              this.ssoFrom.ssosContentFrom.firstImg.url =
                obj[0].contents[0].fileUrl
              this.ssoFrom.ssosContentFrom.firstTit = obj[0].contents[1]
              // --2
              this.ssoFrom.ssosContentFrom.secondImg = obj[0].contents[2]
              this.ssoFrom.ssosContentFrom.secondImg.srcType =
                obj[0].contents[2].srcType
              this.ssoFrom.ssosContentFrom.secondImg.url =
                obj[0].contents[2].fileUrl
              this.ssoFrom.ssosContentFrom.secondTit = obj[0].contents[3]
            }
          } else if (val.templateType == 'RED_PACKET') {
            this.ssoFrom.templateType = val.templateType
            this.ssoFrom.ssoRedPacketFrom.imgcontents = obj[0].contents[0]
            this.ssoFrom.ssoRedPacketFrom.imgcontents.srcType =
              obj[0].contents[0].fileType
            this.ssoFrom.ssoRedPacketFrom.imgcontents.url =
              obj[0].contents[0].fileUrl

            this.ssoFrom.ssoRedPacketFrom.titlecontents = obj[0].contents[1]
            this.ssoFrom.ssoRedPacketFrom.bigtitlecontents = obj[0].contents[2]
            this.ssoFrom.ssoRedPacketFrom.textcontents = obj[0].contents[3]
            this.ssoFrom.ssoRedPacketFrom.btncontents = obj[0].contents[4]
          }
        }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
    xsmsPages: {
      handler(val) {
        console.log(val, 'xsms')
        if (val.length) {
          let obj = val
          if (this.ssoFrom.templateType == 'IMAGE_AND_TEXT') {
            this.xsmsForm.templateType = 'IMAGE_AND_TEXT'
            this.xsmsForm.ssoContentFrom.imgcontents = obj[0].contents[0]
            this.xsmsForm.ssoContentFrom.imgcontents.srcType =
              obj[0].contents[0].fileType
            this.xsmsForm.ssoContentFrom.imgcontents.url =
              obj[0].contents[0].fileUrl
            this.xsmsForm.ssoContentFrom.titlecontents = obj[0].contents[1]
            this.xsmsForm.ssoContentFrom.textcontents = obj[0].contents[2]
            this.xsmsForm.ssoContentFrom.btncontents = obj[0].contents[3]
          } else if (this.ssoFrom.templateType == 'STANDARD_IMAGE_AND_TEXT') {
            this.xsmsForm.templateType = 'STANDARD_IMAGE_AND_TEXT'
            if (obj[0].contents.length == 8) {
              this.xsmsForm.number = '4'
              this.xsmsForm.ssosContentFrom.firstImg = obj[0].contents[0]
              this.xsmsForm.ssosContentFrom.firstImg.srcType =
                obj[0].contents[0].srcType
              this.xsmsForm.ssosContentFrom.firstImg.url =
                obj[0].contents[0].fileUrl
              this.xsmsForm.ssosContentFrom.firstTit = obj[0].contents[1]
              // --2
              this.xsmsForm.ssosContentFrom.secondImg = obj[0].contents[2]
              this.xsmsForm.ssosContentFrom.secondImg.srcType =
                obj[0].contents[2].srcType
              this.xsmsForm.ssosContentFrom.secondImg.url =
                obj[0].contents[2].fileUrl
              this.xsmsForm.ssosContentFrom.secondTit = obj[0].contents[3]
              // --3
              this.xsmsForm.ssosContentFrom.thirdImg = obj[0].contents[4]
              this.xsmsForm.ssosContentFrom.thirdImg.srcType =
                obj[0].contents[4].srcType
              this.xsmsForm.ssosContentFrom.thirdImg.url =
                obj[0].contents[4].fileUrl
              this.xsmsForm.ssosContentFrom.thirdTit = obj[0].contents[5]
              // --4
              this.xsmsForm.ssosContentFrom.fourthImg = obj[0].contents[6]
              this.xsmsForm.ssosContentFrom.fourthImg.srcType =
                obj[0].contents[6].srcType
              this.xsmsForm.ssosContentFrom.fourthImg.url =
                obj[0].contents[6].fileUrl
              this.xsmsForm.ssosContentFrom.fourthTit = obj[0].contents[7]
            } else if (obj[0].contents.length == 6) {
              this.xsmsForm.number = '3'
              this.xsmsForm.ssosContentFrom.firstImg = obj[0].contents[0]
              this.xsmsForm.ssosContentFrom.firstImg.srcType =
                obj[0].contents[0].srcType
              this.xsmsForm.ssosContentFrom.firstImg.url =
                obj[0].contents[0].fileUrl
              this.xsmsForm.ssosContentFrom.firstTit = obj[0].contents[1]
              // --2
              this.xsmsForm.ssosContentFrom.secondImg = obj[0].contents[2]
              this.xsmsForm.ssosContentFrom.secondImg.srcType =
                obj[0].contents[2].srcType
              this.xsmsForm.ssosContentFrom.secondImg.url =
                obj[0].contents[2].fileUrl
              this.xsmsForm.ssosContentFrom.secondTit = obj[0].contents[3]
              // --3
              this.xsmsForm.ssosContentFrom.thirdImg = obj[0].contents[4]
              this.xsmsForm.ssosContentFrom.thirdImg.srcType =
                obj[0].contents[4].srcType
              this.xsmsForm.ssosContentFrom.thirdImg.url =
                obj[0].contents[4].fileUrl
              this.xsmsForm.ssosContentFrom.thirdTit = obj[0].contents[5]
            } else if (obj[0].contents.length == 4) {
              this.xsmsForm.number = '2'
              this.xsmsForm.ssosContentFrom.firstImg = obj[0].contents[0]
              this.xsmsForm.ssosContentFrom.firstImg.srcType =
                obj[0].contents[0].srcType
              this.xsmsForm.ssosContentFrom.firstImg.url =
                obj[0].contents[0].fileUrl
              this.xsmsForm.ssosContentFrom.firstTit = obj[0].contents[1]
              // --2
              this.xsmsForm.ssosContentFrom.secondImg = obj[0].contents[2]
              this.xsmsForm.ssosContentFrom.secondImg.srcType =
                obj[0].contents[2].srcType
              this.xsmsForm.ssosContentFrom.secondImg.url =
                obj[0].contents[2].fileUrl
              this.xsmsForm.ssosContentFrom.secondTit = obj[0].contents[3]
            }
          } else if (this.ssoFrom.templateType == 'RED_PACKET') {
            this.xsmsForm.templateType = 'RED_PACKET'
            this.xsmsForm.ssoRedPacketFrom.imgcontents = obj[0].contents[0]
            this.xsmsForm.ssoRedPacketFrom.imgcontents.srcType =
              obj[0].contents[0].fileType
            this.xsmsForm.ssoRedPacketFrom.imgcontents.url =
              obj[0].contents[0].fileUrl

            this.xsmsForm.ssoRedPacketFrom.titlecontents = obj[0].contents[1]
            this.xsmsForm.ssoRedPacketFrom.bigtitlecontents = obj[0].contents[2]
            this.xsmsForm.ssoRedPacketFrom.textcontents = obj[0].contents[3]
            this.xsmsForm.ssoRedPacketFrom.btncontents = obj[0].contents[4]
          }
        }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
    aimPages: {
      handler(val) {
        console.log(val, 'valaim')
        if (val) {
          if (val.length) {
            console.log(111)
            let obj = val
            if (this.ssoFrom.templateType == 'IMAGE_AND_TEXT') {
              this.aimForm.templateType = 'IMAGE_AND_TEXT'
              this.aimForm.ssoContentFrom.imgcontents = obj[0].contents[0]
              this.aimForm.ssoContentFrom.imgcontents.srcType =
                obj[0].contents[0].fileType
              this.aimForm.ssoContentFrom.imgcontents.url =
                obj[0].contents[0].fileUrl
              this.aimForm.ssoContentFrom.titlecontents = obj[0].contents[1]
              this.aimForm.ssoContentFrom.textcontents = obj[0].contents[2]
              this.aimForm.ssoContentFrom.btncontents = obj[0].contents[3]
            } else if (this.ssoFrom.templateType == 'STANDARD_IMAGE_AND_TEXT') {
              this.aimForm.templateType = 'STANDARD_IMAGE_AND_TEXT'
              if (obj[0].contents.length == 8) {
                this.aimForm.number = '4'
                this.aimForm.ssosContentFrom.firstImg = obj[0].contents[0]
                this.aimForm.ssosContentFrom.firstImg.srcType =
                  obj[0].contents[0].srcType
                this.aimForm.ssosContentFrom.firstImg.url =
                  obj[0].contents[0].fileUrl
                this.aimForm.ssosContentFrom.firstTit = obj[0].contents[1]
                // --2
                this.aimForm.ssosContentFrom.secondImg = obj[0].contents[2]
                this.aimForm.ssosContentFrom.secondImg.srcType =
                  obj[0].contents[2].srcType
                this.aimForm.ssosContentFrom.secondImg.url =
                  obj[0].contents[2].fileUrl
                this.aimForm.ssosContentFrom.secondTit = obj[0].contents[3]
                // --3
                this.aimForm.ssosContentFrom.thirdImg = obj[0].contents[4]
                this.aimForm.ssosContentFrom.thirdImg.srcType =
                  obj[0].contents[4].srcType
                this.aimForm.ssosContentFrom.thirdImg.url =
                  obj[0].contents[4].fileUrl
                this.aimForm.ssosContentFrom.thirdTit = obj[0].contents[5]
                // --4
                this.aimForm.ssosContentFrom.fourthImg = obj[0].contents[6]
                this.aimForm.ssosContentFrom.fourthImg.srcType =
                  obj[0].contents[6].srcType
                this.aimForm.ssosContentFrom.fourthImg.url =
                  obj[0].contents[6].fileUrl
                this.aimForm.ssosContentFrom.fourthTit = obj[0].contents[7]
              } else if (obj[0].contents.length == 6) {
                this.aimForm.number = '3'
                this.aimForm.ssosContentFrom.firstImg = obj[0].contents[0]
                this.aimForm.ssosContentFrom.firstImg.srcType =
                  obj[0].contents[0].srcType
                this.aimForm.ssosContentFrom.firstImg.url =
                  obj[0].contents[0].fileUrl
                this.aimForm.ssosContentFrom.firstTit = obj[0].contents[1]
                // --2
                this.aimForm.ssosContentFrom.secondImg = obj[0].contents[2]
                this.aimForm.ssosContentFrom.secondImg.srcType =
                  obj[0].contents[2].srcType
                this.aimForm.ssosContentFrom.secondImg.url =
                  obj[0].contents[2].fileUrl
                this.aimForm.ssosContentFrom.secondTit = obj[0].contents[3]
                // --3
                this.aimForm.ssosContentFrom.thirdImg = obj[0].contents[4]
                this.aimForm.ssosContentFrom.thirdImg.srcType =
                  obj[0].contents[4].srcType
                this.aimForm.ssosContentFrom.thirdImg.url =
                  obj[0].contents[4].fileUrl
                this.aimForm.ssosContentFrom.thirdTit = obj[0].contents[5]
              } else if (obj[0].contents.length == 4) {
                this.aimForm.number = '2'
                this.aimForm.ssosContentFrom.firstImg = obj[0].contents[0]
                this.aimForm.ssosContentFrom.firstImg.srcType =
                  obj[0].contents[0].srcType
                this.aimForm.ssosContentFrom.firstImg.url =
                  obj[0].contents[0].fileUrl
                this.aimForm.ssosContentFrom.firstTit = obj[0].contents[1]
                // --2
                this.aimForm.ssosContentFrom.secondImg = obj[0].contents[2]
                this.aimForm.ssosContentFrom.secondImg.srcType =
                  obj[0].contents[2].srcType
                this.aimForm.ssosContentFrom.secondImg.url =
                  obj[0].contents[2].fileUrl
                this.aimForm.ssosContentFrom.secondTit = obj[0].contents[3]
              }
            } else if (this.ssoFrom.templateType == 'RED_PACKET') {
              this.aimForm.templateType = 'RED_PACKET'
              this.aimForm.ssoRedPacketFrom.imgcontents = obj[0].contents[0]
              this.aimForm.ssoRedPacketFrom.imgcontents.srcType =
                obj[0].contents[0].fileType
              this.aimForm.ssoRedPacketFrom.imgcontents.url =
                obj[0].contents[0].fileUrl

              this.aimForm.ssoRedPacketFrom.titlecontents = obj[0].contents[1]
              this.aimForm.ssoRedPacketFrom.bigtitlecontents =
                obj[0].contents[2]
              this.aimForm.ssoRedPacketFrom.textcontents = obj[0].contents[3]
              this.aimForm.ssoRedPacketFrom.btncontents = obj[0].contents[4]
            }
          }
        } else {
          let obj = {
            templateType: 'IMAGE_AND_TEXT',
            number: '4',
            ssoContentFrom: {
              imgcontents: {
                type: 'image',
                srcType: '',
                sourceId: '',
                url: '',
                thumbnailUrl: '',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 1,
              },
              titlecontents: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 2,
              },
              textcontents: {
                type: 'text',
                content: '',
                isTextTitle: false,
                positionNumber: 3,
              },
              btncontents: {
                type: 'button',
                content: '',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 4,
              },
            },
            ssosContentFrom: {
              firstImg: {
                type: 'image',
                srcType: '',
                sourceId: '',
                url: '',
                thumbnailUrl: '',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 1,
              },
              firstTit: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 2,
              },
              secondImg: {
                type: 'image',
                srcType: '',
                sourceId: '',
                url: '',
                thumbnailUrl: '',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 3,
              },
              secondTit: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 4,
              },
              thirdImg: {
                type: 'image',
                srcType: '',
                sourceId: '',
                url: '',
                thumbnailUrl: '',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 5,
              },
              thirdTit: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 6,
              },
              fourthImg: {
                type: 'image',
                srcType: '',
                sourceId: '',
                url: '',
                thumbnailUrl: '',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 7,
              },
              fourthTit: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 8,
              },
            },
            ssoRedPacketFrom: {
              imgcontents: {
                type: 'image',
                srcType: '',
                sourceId: '',
                url: '',
                positionNumber: 1,
              },
              titlecontents: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 2,
              },
              bigtitlecontents: {
                type: 'text',
                content: '',
                isTextTitle: true,
                positionNumber: 3,
              },
              textcontents: {
                type: 'text',
                content: '',
                isTextTitle: false,
                positionNumber: 4,
              },
              btncontents: {
                type: 'button',
                content: '立即领取',
                actionType: '',
                action: {
                  target: '',
                },
                positionNumber: 5,
              },
            },
          }
          this.aimForm = obj
          this.aimForm.templateType = this.ssoFrom.templateType
        }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
}
</script>

<style scoped>
.sso_temp {
  width: 100%;
  position: relative;
}
.sso_content {
  width: 100%;
  height: 100%;
  display: flex;
}
.sso_left {
  flex: 1;
  background: #fff;
  margin-right: 20px;
}
.temp_from {
  width: 100%;
}
.sso_temp_content {
  width: 100%;
}
.sso_text {
  width: 100%;
  padding: 10px;
}
.sso_title {
  width: 208px;
  font-size: 14px;
  color: #666;
  line-height: 30px;
  height: 30px;
}
.sso_tcontent {
  width: 300px;
  height: 86px;
  font-size: 13px;
  color: #ccc;
  line-height: 20px;
  transform: scale(0.634503);
  transform-origin: 0px 0px;
  white-space: pre-wrap;
  font-family: '微软雅黑';
  color: #000;
}
.sso_right {
  width: 100%;
}
.sso_btn {
  width: 100%;
  margin-bottom: 10px;
}
.btn {
  width: 208px;
  height: 26px;
  margin: 0 auto;
  line-height: 26px;
  text-align: center;
  background: #eee;
  border-radius: 5px;
}
.content_item {
  width: 220px;
  margin-left: 30px;
  border: 1px solid #ccc;
  border-radius: 10px;
}
.content_item_red {
  width: 268px;
  height: 360px;
  margin-left: 20px;
  background: #fb3556;
  border-radius: 10px;
}
.red_header {
  width: 100%;
  height: 98px;
  position: relative;
}
.red_logo {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 0;
  left: 0;
  margin-left: 100px;
  margin-top: 35px;
}
.semll_tit {
  width: 125%;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #eeeeee;
  -webkit-transform-origin-x: 0;
  -webkit-transform: scale(0.8);
  margin-top: 5px;
}
.big_tit {
  width: 100%;
  height: 20px;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  color: #fff;
  margin-top: 35px;
}
.rad_content {
  width: 100%;
  height: 60px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
}
.red_text {
  width: 220px;
  height: 100%;
  margin: 0 auto;
  color: #eee;
}
.rad_footer {
  width: 100%;
  height: 28px;
}
.red_btn {
  width: 140px;
  height: 28px;
  text-align: center;
  line-height: 28px;
  color: #fff;
  background: #fd9a27;
  border-radius: 10px;
  margin: 60px auto;
  cursor: pointer;
}
.item_img {
  width: 100%;
  height: 135px;
  position: relative;
}
.image {
  width: 100%;
  height: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.items_contents {
  width: 240px;
  margin-left: 30px;
  border: 1px solid #ccc;
  border-radius: 10px;
}
.image_header_tite {
  font-size: 14px;
  position: absolute;
  bottom: 0;
  color: #fff;
  left: 0;
  margin-left: 10px;
  margin-bottom: 10px;
}
.first_contents {
  width: 100%;
  display: flex;
  align-items: center;
  font-size: 12px;
  margin: 10px 0;
}
.first_img {
  width: 30px;
  height: 30px;
  margin-left: 10px;
}
.first_tit {
  width: 170px;
  margin-left: 10px;
  color: #666;
}
.sso_view_tit {
  width: 100%;
  height: 70px;
  line-height: 70px;
  text-align: center;
  font-size: 14px;
}
.sso_view_content {
  width: 310px;
  height: 660px;
  border: 7px solid #000;
  border-radius: 25px;
  position: relative;
}
.sso_navbar {
  width: 100%;
  height: 70px;
}
.view_img_bag {
  width: 100%;
  height: 100%;
}
.sso_view_footer {
  position: absolute;
  width: 100%;
  height: 50px;
  bottom: 0;
}
.sso_view_item {
  width: 100%;
}
.media_icon {
  width: 24px;
  height: 22px;
  position: absolute;
  left: 2.08%;
  right: 2.08%;
  top: 6.25%;
  bottom: 5.9%;
}
.image_s {
  width: 100%;
  height: 100%;
}
.sso_view_right {
  flex: 1;
  margin-left: 30px;
}
.imgAndtext {
  margin-left: 10px;
}
.nav_img_action {
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: #eee;
  padding-left: 10px;
}
.action_cont {
  margin-top: 10px;
}
</style>
