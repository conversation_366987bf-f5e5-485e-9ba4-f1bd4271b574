<template>
  <div>
    <p style="margin-top: 30px; font-weight: bold">敏感词设置</p>
    <div
      class="sensitive-fun"
      style="padding-top: 15px; display: flex; flex-wrap: wrap"
    >
      <div v-for="item in sensitiveOptions" :key="item.id">
        <el-select
          class="selectSensitive"
          style="width: 260px; margin-left: 10px"
          v-model="item.selectValue"
          multiple
          clearable
          :placeholder="item.labelName"
        >
          <el-option
            v-for="child in item.children"
            :key="child.id"
            :label="child.labelName"
            :value="child.id"
          />
        </el-select>
      </div>
    </div>
    <div style="text-align: right; padding: 0 70px">
      <el-button type="primary" class="sure-balance-tips" @click="save"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import TableTem from "@/components/publicComponents/TableTem.vue";
// 引入时间戳转换
import { formatDate } from "@/assets/js/date.js";
import { mapState, mapMutations, mapActions } from "vuex";

export default {
  name: "SensitiveSettings",
  components: {
    TableTem,
  },
  data() {
    return {
      sensitiveValue: [],
      sensitiveOptions: [],
      labelId: [],
      isFirstEnter: false,
    };
  },
  methods: {
    save() {
      this.labelId = [];
      for (let i in this.sensitiveOptions) {
        this.sensitiveOptions[i].selectValue.forEach((item) => {
          this.labelId.push(item);
        })
      }
      this.$confirms.confirmation(
        "post",
        "确认保存敏感词设置",
        `${window.path.omcs}operatingclientblackwords/update`,
        { labelIds: this.labelId, userId: this.$route.query.id },
        (res) => {
          if (res.code !== 200) {
            // this.$message.error("保存失败，请重试");
          }
        }
      );
    },
    removeDuplicates(arr) {
      return [...new Set(arr)];
    },
    getValue(arr, id) {
      for (const item of arr) {
        const matchedChild = item.children?.find((child) => child.id === id);
        if (matchedChild) {
          item.selectValue.push(matchedChild.id);
        }
      }
      let arr2 = arr.map((item) => {
        item.selectValue = this.removeDuplicates(item.selectValue);
        return item;
      });
      return arr2;
    },
    getList() {
      try {
        window.api.post(
          window.path.omcs + "operatingblackwordslabel/page",
          {},
          (res) => {
            if (res.code === 200) {
              this.sensitiveOptions = res.data.map((item) => ({
                ...item,
                selectValue: [],
              }));
              window.api.get(
                `${window.path.omcs}operatingclientblackwords/${this.$route.query.id}`,
                {},
                (result) => {
                  if (result.code === 200) {
                    result.data.forEach((item) =>
                      this.getValue(this.sensitiveOptions, item.labelId)
                    );
                  }
                }
              );
            }
          }
        );
      } catch (error) {
        console.error("获取敏感词失败", error);
        this.$message.error("获取敏感词失败，请重试！");
      }
    },
  },
  created() {
      this.isFirstEnter = true
      this.getList()
  },
  activated() {
      if (this.$route.meta.isBack || !this.isFirstEnter) {
          this.$nextTick(() => {
              this.getList()
          });
      } else {
          this.$route.meta.isBack = false;
          this.isFirstEnter = false;
      }
  },
  // activated() {
  //   this.$nextTick(() => {
  //     this.getList();
  //   });
  // },
};
</script>

<style scoped>
.selectSensitive {
  padding: 10px 10px;
}
.sensitive-fun {
  display: flex;
  flex-wrap: wrap;
}
</style>
