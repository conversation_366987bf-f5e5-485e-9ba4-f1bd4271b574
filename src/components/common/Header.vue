<template>
  <div class="header">
    <!-- 折叠按钮 -->
    <div class="collapse-btn" @click="collapseChage">
      <el-icon><el-icon-menu /></el-icon>
    </div>
    <div class="logo">
      助通科技后台管理系统<span class="version">{{ version }}</span>
    </div>
    <div class="header-right">
      <div class="header-user-con">
        <!-- 封装的工具栏组件 -->
        <HeaderTools 
          @show-shortlink="shortlinlShow = true"
          @show-encipher="encipherShow = true"
          @show-sms="smsShow"
          @generate-video="generateVideo"
          @calculate="calculate"
        />

        <!-- 用户头像 -->
        <div class="user-avator"><img :src="HportraitUrl" /></div>
        <!-- 用户名下拉菜单 -->
        <el-dropdown class="user-name" trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            {{ hName }} <el-icon><el-icon-caret-bottom /></el-icon>
          </span>
          <template v-slot:dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="loginout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <G-Video :visible="videoVisible"></G-Video>
    <smsTest :visible="smstestVideo"></smsTest>
    <el-dialog
      title="内容计算"
      v-model="ContentCalculate"
      width="600px"
      :before-close="handleClose"
    >
      <div>
        <!-- <el-input style="height: 150px;" type="textarea" v-model="contentText">
                    </el-input> -->
        <el-input
          style="height: 150px"
          type="textarea"
          placeholder="请输入内容"
          v-model="contentText"
        >
        </el-input>
        <div class="tem-be-careful">
          <div class="tem-font" style="display: inline-block">
            当前发送内容 <span>{{ wordCount2 }}</span
            >个字, 预计发送条数约为<span> {{ numTextMsg2 }} </span>条短信
          </div>
          <div class="tem-font" style="display: inline-block; color: red">
            （如果此内容包含变量，以实际下发条数为准）
          </div>
        </div>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="ContentCalculate = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="加密/解密工具"
      v-model="encipherShow"
      width="900px"
      :before-close="handleClose"
    >
      <div class="cipher-container">
        <!-- 加密部分 -->
        <div class="cipher-block encryption-block">
          <div class="block-title">号码加密</div>
          <el-form
            :model="ruleForm"
            :rules="rules"
            ref="ruleForm"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-form-item label="手机号" prop="mobile">
              <el-input
                style="width: 280px"
                placeholder="请输入手机号"
                v-model.trim="ruleForm.mobile"
              >
              </el-input>
              <el-button
                style="margin-top: 8px;"
                type="primary"
                @click="handelEncipher('ruleForm')"
                >一键加密</el-button
              >
            </el-form-item>
            <el-form-item v-if="cipherMobile != ''" label="号码密文1" prop="">
              <span>{{ cipherMobile }}</span>
              <CopyTemp :content="cipherMobile" :fontSize="'22px'" />
            </el-form-item>
            <!-- 86 -->
            <el-form-item v-if="cipherMobile8 != ''" label="号码密文2" prop="">
              <span>{{ cipherMobile8 }}</span>
              <CopyTemp :content="cipherMobile8" :fontSize="'22px'" />
            </el-form-item>
            <!-- +86 -->
            <el-form-item v-if="cipherMobilej86 != ''" label="号码密文3" prop="">
              <span>{{ cipherMobilej86 }}</span>
              <CopyTemp :content="cipherMobilej86" :fontSize="'22px'" />
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 解密部分 -->
        <div class="cipher-block decryption-block">
          <div class="block-title">账号加密/解密</div>
          <el-form
            :model="decryptForm"
            :rules="decryptRules"
            ref="decryptForm"
            label-width="100px"
            class="demo-ruleForm"
          >
            <el-form-item label="账号" prop="content">
              <el-input
                style="width: 280px"
                placeholder="请输入账号"
                v-model.trim="decryptForm.content"
              >
              </el-input>
              <el-button
                style="margin-top: 8px;"
                type="primary"
                @click="handleEncrypt('decryptForm',1)"
                >加密</el-button
              >
              <el-button
                style="margin-left: 16px; margin-top: 8px;"
                type="primary"
                @click="handleEncrypt('decryptForm',2)"
                >解密</el-button
              >
            </el-form-item>
            <el-form-item v-if="decryptedContent != ''" :label="decrypteType == '1' ? '加密结果' : '解密结果'" prop="">
              <span>{{ decryptedContent }}</span>
              <CopyTemp :content="decryptedContent" :fontSize="'22px'" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="encipherShow = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="短链检索"
      v-model="shortlinlShow"
      width="1000px"
      :before-close="handleClose"
    >
      <sLink :visible="shortlinlShow"></sLink>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="shortlinlShow = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 签名查询工具 -->
    <SignatureQuery v-model:visible="signatureQueryShow" />
  </div>
</template>

<script>
import {
  Menu as ElIconMenu,
  CaretBottom as ElIconCaretBottom,
  DocumentCopy as ElIconDocumentCopy,
} from '@element-plus/icons-vue'
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import bus from '../common/bus'
import GenerateVideo from '../publicComponents/generateVideo.vue'
import SmsTest from '../publicComponents/smsTest.vue'
import shortLinkCheck from '../page/SecurityCheck/shortLinkCheck/shortLinkCheck.vue'
import clip from '../../utils/clipboard'
import { version } from '../../utils/version'
import { mapState, mapMutations, mapActions } from 'vuex'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
import HeaderTools from './HeaderTools.vue'

export default {
  components: {
    'G-Video': GenerateVideo,
    smsTest: SmsTest,
    sLink: shortLinkCheck,
    ElIconMenu,
    ElIconCaretBottom,
    ElIconDocumentCopy,
    CopyTemp,
    HeaderTools
  },
  data() {
    var validateMobile = function (rule, value, callback) {
      if (value != '') {
        // /^1[3|4|5|6|7|8|9][0-9]\d{8}$/
        let reg = /^1\d{10}$/
        if (reg.test(value)) {
          callback()
        } else {
          return callback(new Error('请输入正确的手机号'))
        }
      } else {
        return callback(new Error('请输入手机号'))
      }
    }
    return {
      isToolboxVisible: false, // 工具箱是否显示
      toolboxTimeout: null, // 工具箱定时器
      tools: [
        { icon: '🔲', name: '全屏', click: 'this.handleFullScreen()' },
        { icon: '🔗', name: '短链检索', click: 'this.shortlinlShow = true' },
        { icon: '🔒', name: '加密工具', click: 'this.encipherShow = true' },
        { icon: '📩', name: '短信测试', click: 'this.smsShow()' },
        { icon: '📹', name: '视频压缩', click: 'this.generateVideo()' },
        { icon: '🧮', name: '内容计算', click: 'this.calculate()' },
      ],
      collapse: true,
      videoVisible: false,
      ContentCalculate: false,
      smstestVideo: false,
      contentText: '',
      encipherShow: false, //加密框
      shortlinlShow: false,
      signatureQueryShow: false, //签名查询
      version: version,
      ruleForm: {
        mobile: '',
      },
      cipherMobile: '',
      cipherMobile8: '',
      cipherMobilej86: '',
      rules: {
        mobile: [
          {
            required: true,
            validator: validateMobile,
            trigger: ['change', 'blur'],
          },
        ],
      },
      decryptForm: {
        content: '',
      },
      decryptedContent: '',
      decrypteType: '',
      decryptRules: {
        ciphertext: [
          {
            required: true,
            message: '请输入密文',
            trigger: ['blur', 'change'],
          },
        ],
      },
      message: 2,
    }
  },
  props: ['hName', 'HportraitUrl'],
  created() {
    $on(bus, 'closeVideo', (msg) => {
      this.videoVisible = msg
      this.smstestVideo = msg
    })
  },
  computed: {
    numTextMsg2: function () {
      //2短信条数
      let numMsg = 0
      //字数和短信条数的显示
      if (this.wordCount2 == 0) {
        numMsg = 0
      } else if (
        parseInt(this.wordCount2) <= 70 &&
        parseInt(this.wordCount2) > 0
      ) {
        numMsg = 1
      } else {
        numMsg = Math.ceil(parseInt(this.wordCount2) / 67)
      }
      return numMsg
    },
    wordCount2: function () {
      //2字数
      if (this.contentText == ' ') {
        // debugger
        return 0
      } else {
        if (
          this.contentText.length == 2 &&
          this.contentText.indexOf(' ') != -1
        ) {
          return 1
        }
        if (this.contentText) {
          return this.countNum1(this.contentText)
        } else {
          return 0
        }
      }
    },
  },
  methods: {
    // 用户名下拉菜单选择事件
    handleCommand(command) {
      if (command == 'loginout') {
        window.api.post(window.path.auth + 'loginOut', {}, (res) => {
          if (res.code == 200) {
            var oDate = new Date()
            oDate.setDate(oDate.getDate() - 1)
            if (window.common.getCookie('ZTADMIN_TOKEN')) {
              document.cookie =
                'ZTADMIN_TOKEN=' +
                window.common.getCookie('ZTADMIN_TOKEN') +
                ';path=/;expires=' +
                oDate.toGMTString()
              // document.cookie='ZTADMIN_TOKEN='+ window.common.getCookie('ZTADMIN_TOKEN') +';path=/;domain=.mixcloud.cn;expires='+oDate.toGMTString(); //线上
            }
            localStorage.removeItem('ms_username')
            this.$router.push('/login')
          }
        })
      }
    },
    // 侧边栏折叠
    collapseChage() {
      this.collapse = !this.collapse
      $emit(bus, 'collapse', this.collapse)
    },
    generateVideo() {
      this.videoVisible = true
    },
    smsShow() {
      this.smstestVideo = true
    },
    calculate() {
      this.ContentCalculate = true
    },
    handleClose() {
      this.ContentCalculate = false
      this.encipherShow = false
      this.shortlinlShow = false
    },
    countNum1(val) {
      let len = 0 //字数
      if (val != null) {
        len += val.length
        return len
      } else {
        return (len = 0)
      }
    },
    handelEncipher(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.api.get(
            window.path.upms +
              'generatekey/encryptMobile?mobile=' +
              this.ruleForm.mobile,
            null,
            (res) => {
              if (res.code == 200) {
                this.cipherMobile = res.data.mobile1
                this.cipherMobile8 = res.data.mobile2
                this.cipherMobilej86 = res.data.mobile3
                
                // this.$message({
                //     message: res.msg,
                //     type: 'success'
                // });
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        } else {
          console.log('error')
        }
      })
    },
    //f复制
    handleCopy(text, event) {
      clip(text, event)
    },
    handleEncrypt(formName, type) {
      this.decrypteType = type
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs +
              'consumerSignature/decodeAndEncode',
            {
              content: this.decryptForm.content,
              type: type,
            },
            (res) => {
              if (res.code == 200) {
                this.decryptedContent = res.data
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        } else {
          console.log('error')
        }
      })
    },
  },
  mounted() {
    if (document.body.clientWidth < 1500) {
      this.collapseChage()
    }
  },
  watch: {
    encipherShow(val) {
      if (!val) {
        if (this.$refs['ruleForm']) {
          this.$refs['ruleForm'].resetFields()
        }
        if (this.$refs['decryptForm']) {
          this.$refs['decryptForm'].resetFields()
        }
        this.cipherMobile = ''
        this.cipherMobile8 = ''
        this.cipherMobilej86 = ''
        this.decryptedContent = ''
        this.decrypteType = ''
      }
    },
  },
  emits: ['collapse'],
}
</script>

<style scoped>
.header {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 50px;
  font-size: 22px;
  color: #fff;
}
.collapse-btn {
  float: left;
  padding: 0 21px;
  cursor: pointer;
  line-height: 50px;
}
.header .logo {
  float: left;
  line-height: 50px;
  position: relative;
}
.version {
  font-size: 13px;
  position: absolute;
  top: 5px;
  left: 0;
  margin-left: 230px;
}
.header-right {
  float: right;
  padding-right: 50px;
}
.header-user-con {
  display: flex;
  height: 50px;
  align-items: center;
}
.user-name {
  margin-left: 10px;
}
.user-avator {
  margin-left: 20px;
}
.user-avator img {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.el-dropdown-link {
  color: #fff;
  cursor: pointer;
}
.el-dropdown-menu__item {
  text-align: center;
}

/* 加密解密工具样式 */
.cipher-container {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}
.cipher-block {
  width: 48%;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
}
.block-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #409EFF;
  text-align: center;
}
.encryption-block {
  border-left: 3px solid #67c23a;
}
.decryption-block {
  border-left: 3px solid #409EFF;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
</style>
