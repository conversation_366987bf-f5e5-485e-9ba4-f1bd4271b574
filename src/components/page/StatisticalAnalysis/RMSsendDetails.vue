<template>
  <div class="container_left">
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <el-tabs v-model="activeName2" type="card">
        <el-tab-pane label="用户发送明细报表" name="first">
          <component v-bind:is="currentTabComponent"></component>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import RMSrecord from './components/RMSrecord.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    RMSrecord
  },
  name: 'RMSsendDetails',
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  data() {
    return {
      currentTabComponent: 'RMSrecord',
      activeName2: 'first',
    }
  },
}
</script>
