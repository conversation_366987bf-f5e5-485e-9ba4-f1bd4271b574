<template>
    <div class="container_left">
        <div class="channel-tabs" style="margin-top:22px;">
            <el-tabs
              v-model="formInline.operator"
              type="card"
              @tab-change="handleClick"
            >
              <el-tab-pane name="1">
                <template v-slot:label>
                  <span
                    ><i class="iconfont icon-yidong" style="color: #409eff"></i>
                    移动</span
                  >
                </template>
              </el-tab-pane>
              <el-tab-pane name="2">
                <template v-slot:label>
                  <span
                    ><i class="iconfont icon-liantong" style="color: #f56c6c"></i>
                    联通</span
                  >
                </template>
              </el-tab-pane>
              <el-tab-pane label="电信" name="3">
                <template v-slot:label>
                  <span
                    ><i class="iconfont icon-dianxin" style="color: #0c36f2"></i>
                    电信</span
                  >
                </template>
              </el-tab-pane>
              <el-tab-pane label="三网" name="4">
                <template v-slot:label>
                  <div style="display: flex; align-items: center;"
                    ><i class="iconfont icon-zhichisanwang" style="color: #E6A23C; font-size: 24px;"></i>
                    <span>三网</span></div
                  >
                </template>
              </el-tab-pane>
              <!-- <el-tab-pane label="三网" name="4"></el-tab-pane> -->
            </el-tabs>
        </div>
        <div class="OuterFrame fillet OuterFrameList">
            <div>
                <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
                    <el-form-item label="通道号" prop="channelId">
                        <el-input v-model="formInline.channelId" placeholder="" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="formInline.status" clearable placeholder="请选择" class="input-w">
                            <el-option label="全部" value></el-option>
                            <el-option label="启用" :value="0"></el-option>
                            <el-option label="停用" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <div class="sensitive-table">
                <!-- 表格和分页开始 -->
                <!-- <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                    element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                    ref="multipleTable" border :stripe="true" :data="tableDataObj.tableData" style="width: 100%"> -->

                <vxe-toolbar ref="toolbarRef" custom>
                    <template #buttons>
                        <el-button type="primary" plain @click="sensitiveQuery()">查询</el-button>
                        <el-button type="primary" plain @click="sensitiveReload('formInline')">重置</el-button>
                        <el-button type="primary" plain @click="handleAdopt1()">添加通道</el-button>
                    </template>
                </vxe-toolbar>

                <vxe-table
                    ref="tableRef"
                    id="ChannelPool"
                    border
                    stripe
                    :custom-config="customConfig"
                    :column-config="{ resizable: true }"
                    :row-config="{ isHover: true }"
                    min-height="1"
                    v-loading="tableDataObj.loading2"
                    element-loading-text="拼命加载中"
                    element-loading-background="rgba(0, 0, 0, 0.6)"
                    style="font-size: 12px;"
                    :data="tableDataObj.tableData">

                    <!-- <vxe-column type="checkbox" width="50"></vxe-column> -->
                    <!-- <vxe-column field="运营商" title="运营商">
                        <template #default="scope">
                            <div v-if="scope.row.operator == 1">移动</div>
                            <div v-if="scope.row.operator == 2">联通</div>
                            <div v-if="scope.row.operator == 3">电信</div>
                            <div v-if="scope.row.operator == 4">三网</div>
                        </template>
                    </vxe-column> -->
                    <vxe-column field="产品" title="产品">
                        <template #default="scope">
                            <div v-for="(item, index) in options" :key="index">
                                <span v-if="item.id == scope.row.productId">{{ item.name }}</span>
                            </div>
                            <!-- <span>{{scope.row.productId}}</span> -->
                        </template>
                    </vxe-column>
                    <vxe-column field="通道号" title="通道号">
                        <template #default="scope">
                            <div>{{ scope.row.channelId }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="优先级" title="优先级">
                        <template #default="scope">
                            <div>{{ scope.row.idx }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="状态" title="状态">
                        <template #default="scope">
                            <div style="color:#16A589" v-if="scope.row.status == 0">启用</div>
                            <div style="color:orange" v-if="scope.row.status == 1">停用</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="创建时间" title="创建时间">
                        <template #default="scope">
                            <div>{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
                        </template>
                    </vxe-column>
                    <vxe-column field="操作" title="操作" width='180' fixed="right">
                        <template #default="scope">
                            <el-button link style="color:#409eff;margin-left:0px;"
                                @click="handleEdit(scope.$index, scope.row)"><el-icon><EditPen /></el-icon>&nbsp;编辑</el-button>
                            <el-button link style="color:orange;margin-left:0px;" v-if="scope.row.status == 0"
                                @click="handleStop(scope.$index, scope.row)">&nbsp;停用</el-button>
                            <el-button link style="color:#16A589;margin-left:0px;" v-if="scope.row.status == 1"
                                @click="handleOpen(scope.$index, scope.row)">&nbsp;启用</el-button>
                            <el-button link style="color:red;margin-left:10px;"
                                @click="handDel(scope.$index, scope.row)"><el-icon><CircleCloseFilled /></el-icon>&nbsp;删除</el-button>
                        </template>
                    </vxe-column>
                </vxe-table>
                <!--分页-->
                <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                    style="background:#fff;padding:10px 0;text-align:right;"> -->
                <div class="paginationBox">
                  <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                      :current-page="formInline.currentPage" :page-size="formInline.pageSize"
                      :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                      :total="tableDataObj.total">
                  </el-pagination>
                </div>
                <!-- </el-col> -->
            </div>
        </div>
        <el-dialog :title="title" v-model="dialogVisible" width="30%">
            <el-form :inline="true" :model="formDialog" :rules="rules" class="demo-form-inline" label-width="82px"
                ref="formDialog">
                <el-form-item label="通道号" label-width="82px" prop="channelId">
                    <el-input v-model="formDialog.channelId" placeholder="" class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="优先级" label-width="82px" prop="idx">
                    <el-input-number class="input-w" v-model="formDialog.idx" :min="1" :max="999999999" label="优先级"></el-input-number>
                    <!-- <el-input v-model="formDialog.idx" placeholder="" class="input-w"></el-input> -->
                </el-form-item>
            </el-form>
            <template #footer class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="channelSave('formDialog')">确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment'
export default {
    data() {
        return {
            customConfig: {
                storage: true,
                // mode: "popup"
            },
            isFirstEnter: false,
            dialogVisible: false,
            options: [],
            title: '',
            id: "",
            activeName: "1",
            formInline: {
                channelId: '',
                status: "",
                operator: "1",
                currentPage: 1,
                pageSize: 10
            },
            //查询赋值数据
            tabelAlllist: {
                channelId: '',
                status: "",
                operator: "1",
                currentPage: 1,
                pageSize: 10
            },
            formDialog: {
                channelId: "",
                idx: null
            },
            rules: {
                channelId: [
                    { required: true, message: '请输入通道号', trigger: 'change' }
                ],
            },
            //列表数据
            tableDataObj: {
                loading2: false,
                tableData: [],

                total: 0
            },
        }
    },
    created() {
        this.isFirstEnter = true
        this.$nextTick(() => {
            this.getTableDtate();
        });
        this.options = JSON.parse(localStorage.getItem('list'))
        // this.getTableDtate()
    },
    mounted() {
        const $table = this.$refs.tableRef
        const $toolbar = this.$refs.toolbarRef
        if ($table && $toolbar) {
            $table.connect($toolbar)
        }
    },
    activated() {
        this.options = JSON.parse(localStorage.getItem('list'))
        if (this.$route.meta.isBack || !this.isFirstEnter) {
            this.$nextTick(() => {
                this.getTableDtate()
            });
        } else {
            this.$route.meta.isBack = false
            this.isFirstEnter = false;
        }

    },
    methods: {
        handleClick(e) {
            Object.assign(this.tabelAlllist, this.formInline);
            this.getTableDtate()
        },
        //获取列表数据
        getTableDtate() {
            this.tableDataObj.loading2 = true;
            window.api.post(window.path.omcs + 'operatingchannelswitchpools/page', this.tabelAlllist, res => {
                this.tableDataObj.tableData = res.data.records;
                // if (this.activeName == "1") {
                //     this.tableDataObj.tableData = this.tableDataObj.tableData.filter(item => item.operator == 1)
                // } else if (this.activeName == "2") {
                //     this.tableDataObj.tableData = this.tableDataObj.tableData.filter(item => item.operator == 2)
                // } else if (this.activeName == "3") {
                //     this.tableDataObj.tableData = this.tableDataObj.tableData.filter(item => item.operator == 3)
                // } else if (this.activeName == "4") {
                //     this.tableDataObj.tableData = this.tableDataObj.tableData.filter(item => item.operator == 4)
                // }
                // this.tableDataObj.tableData.sort((a, b) => b.idx - a.idx);
                this.tableDataObj.total = res.data.total;
                this.tableDataObj.loading2 = false;
            })
        },
        sensitiveQuery() {
            if (this.tableDataObj.loading2) {
                return
            }
            Object.assign(this.tabelAlllist, this.formInline);
            this.getTableDtate()
        },
        sensitiveReload(formName) {
            if (this.tableDataObj.loading2) {
                return
            }
            this.$refs[formName].resetFields();
            // this.formInline.channelId =''
            Object.assign(this.tabelAlllist, this.formInline);
            this.getTableDtate()
        },
        handleAdopt1() {
            this.title = '添加通道池切换'
            this.dialogVisible = true
        },
        handleSizeChange(size) {
            this.formInline.pageSize = size;
            Object.assign(this.tabelAlllist, this.formInline);
            this.getTableDtate()
        },
        handleCurrentChange(currentPage) {
            this.formInline.currentPage = currentPage;
            Object.assign(this.tabelAlllist, this.formInline);
            this.getTableDtate()
        },
        //切换通道
        channelSave(val) {
            this.$refs[val].validate((valid, value) => {
                if (valid) {
                    if (this.title == '添加通道池切换') {
                        window.api.post(window.path.omcs + 'operatingchannelswitchpools', this.formDialog, res => {
                            if (res.code == 200) {
                                this.$message({
                                    message: res.msg,
                                    type: 'success'
                                });
                                this.dialogVisible = false
                                this.getTableDtate()
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: 'error'
                                });
                            }

                        })
                    } else {
                        this.formDialog.id = this.id
                        window.api.put(window.path.omcs + 'operatingchannelswitchpools', this.formDialog, res => {
                            if (res.code == 200) {
                                this.$message({
                                    message: res.msg,
                                    type: 'success'
                                });
                                this.dialogVisible = false
                                this.getTableDtate()
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: 'error'
                                });
                            }

                        })
                    }

                }
            })
        },
        //编辑
        handleEdit(index, val) {
            this.title = '编辑通道切换池'
            this.id = val.id
            this.formDialog.channelId = val.channelId
            this.formDialog.idx = val.idx
            this.dialogVisible = true
        },
        //删除通道
        handDel(index, val) {
            this.$confirms.confirmation('delete', '确认删除通道？', window.path.omcs + 'operatingchannelswitchpools/' + val.id, {
            }, res => {
                // this.uploadImage=false
                this.getTableDtate();
            });
        },
        handleStop(index, val) {
            this.$confirms.confirmation('put', '确认停用通道？', window.path.omcs + 'operatingchannelswitchpools/enable', {
                enable: false,
                id: val.id
            }, res => {
                this.getTableDtate();
            });
        },
        handleOpen(index, val) {
            this.$confirms.confirmation('put', '确认启用通道？', window.path.omcs + 'operatingchannelswitchpools/enable', {
                enable: true,
                id: val.id
            }, res => {
                this.getTableDtate();
            });
        }

    },
    watch: {
        dialogVisible(val) {
            if (!val) {
                this.$refs['formDialog'].resetFields();
                this.formDialog.channelId = ''
                this.formDialog.idx = null,
                    this.formDialog.id = ''
            }
        }
    }

}
</script>
<style scoped>
.el-checkbox-group>.el-checkbox {
    margin-left: 30px;
}

.OuterFrame {
    padding: 20px;
}

.demo-form-inline .el-form-item {
    margin-right: 50px;
}

.sensitive-table {
    padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
    margin-left: 0px;
}

.sig-type .el-radio {
    display: block;
    white-space: normal;
    padding-bottom: 16px;
}

.sig-type .el-radio-group {
    padding-top: 8px;
}

.tips {
    margin-top: 30px;
}

.tips p {
    margin-left: 29px;
    color: #c5c5c5;
}

.divWidth>div {
    width: 100%;
}

.sensitive {
    /* border-bottom: 1px solid #ccc; */
    /* margin: 40px 0 20px 10px; */
}

.boderbottom {
    border-bottom: 1px solid #f5f5f5;
    padding-bottom: 20px;
}

.channel-tabs :deep(.el-tabs__item) {
    width: 150px;
    text-align: center;
}

/* .boderbottom{
    border-bottom: 1px solid rgb(212, 211, 211);
    height: 50px;
} */
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>