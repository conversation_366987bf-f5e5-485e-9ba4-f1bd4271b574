<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title">
        <span
          style="
            display: inline-block;
            padding-right: 10px;
            cursor: pointer;
            color: #16a589;
          "
          @click="goBack()"
          ><el-icon><el-icon-arrow-left /></el-icon> 返回</span
        >|
        <span>彩信配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span><span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="参数设置" name="parameterRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="parameterRef"
            label-width="120px"
          >
            <el-form-item label="通道组名称" prop="mmsChannelGroupId">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.mmsChannelGroupId"
                filterable
                placeholder="请选择通道名称"
              >
                <el-option
                  v-for="(item, index) in channel"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="计费方式" prop="mmsChargeback">
              <el-radio-group v-model="addUserStep2Form.formData.mmsChargeback">
                <el-radio disabled label="1">提交计费</el-radio>
                <el-radio disabled label="2" style="margin-left: 10px"
                  >成功计费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="付费方式" prop="mmsPayMethod">
              <el-radio-group v-model="addUserStep2Form.formData.mmsPayMethod">
                <el-radio disabled label="1">预付费</el-radio>
                <el-radio disabled label="2" style="margin-left: 10px"
                  >后付费</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item label="黑词是否加黑" prop="mmsIsRepliesBlack">
              <el-radio-group
                v-model="addUserStep2Form.formData.mmsIsRepliesBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="加黑关键词"
              prop="mmsRepliesBlackWords"
              v-if="addUserStep2Form.formData.mmsIsRepliesBlack == 1"
            >
              <el-input
                type="textarea"
                :rows="4"
                show-word-limit
                resize="none"
                class="input-w-3"
                placeholder=" 多个用逗号(,)隔开"
                v-model="addUserStep2Form.formData.mmsRepliesBlackWords"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="是否验证黑号"
              style="width: 360px"
              prop="mmsIsUserBlack"
            >
              <el-radio-group
                v-model="addUserStep2Form.formData.mmsIsUserBlack"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="黑号验证级别"
              prop="mmsUserBlackLevel1"
              v-if="addUserStep2Form.formData.mmsIsUserBlack == 1"
            >
              <el-checkbox-group
                style="
                  width: 250px;
                  display: flex;
                  flex-shrink: 0;
                  flex-wrap: wrap;
                "
                v-model="addUserStep2Form.formData.mmsUserBlackLevel1"
              >
                <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="扩展号" prop="ext">
              <el-input
                style="width: 224px"
                v-model="addUserStep2Form.formData.ext"
              ></el-input>
            </el-form-item>
            <el-form-item class="clear-div">
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm('parameterRef')"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="状态设置" name="stateRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="stateRef"
            label-width="100px"
          >
            <el-form-item label="消息报告" prop="mmsReportLevel">
              <el-radio-group
                v-model="addUserStep2Form.formData.mmsReportLevel"
                class="input-w"
              >
                <el-radio label="1">不接收报告</el-radio>
                <el-radio label="2" style="margin-left: 10px"
                  >批量推送</el-radio
                >
                <el-radio label="3" style="margin-left: 10px"
                  >主动抓取</el-radio
                >
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="上行地址"
              prop="voiceRepliesUrl"
              v-if="addUserStep2Form.formData.mmsReportLevel == 2"
            >
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.mmsRepliesUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取回复短信的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="下行地址"
              prop="voiceReportUrl"
              v-if="addUserStep2Form.formData.mmsReportLevel == 2"
            >
              <el-input
                type="textarea"
                style="width: 200px"
                v-model="addUserStep2Form.formData.mmsReportUrl"
              ></el-input>
              <p style="width: 345px">
                <span style="font-size: 12px">注：获取状态报告的地址</span
                >(以http/https开头)
              </p>
            </el-form-item>
            <el-form-item
              label="回复推送数量"
              prop="mmsRepliesPackageSize"
              v-if="addUserStep2Form.formData.mmsReportLevel == 2"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.mmsRepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsRepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="状态推送数量"
              prop="mmsStrepliesPackageSize"
              v-if="addUserStep2Form.formData.mmsReportLevel == 2"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.mmsStrepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsStrepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item
              label="回复抓取数量"
              prop="mmsRepliesPackageSize"
              v-if="addUserStep2Form.formData.mmsReportLevel == 3"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.mmsRepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsRepliesPackageSize"></el-input> -->
            </el-form-item>

            <el-form-item
              label="状态抓取数量"
              prop="mmsStrepliesPackageSize"
              v-if="addUserStep2Form.formData.mmsReportLevel == 3"
            >
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.mmsStrepliesPackageSize"
                :min="0"
                :max="999999999"
                label="v5默认150，融合默认400"
              ></el-input-number>
              <!-- <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsStrepliesPackageSize"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm('stateRef')"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="防轰炸设置" name="bombRef">
          <el-form
            :model="addUserStep2Form.formData"
            :rules="addUserStep2Form.formRule"
            ref="bombRef"
            label-width="160px"
          >
            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.dailyLimited"
                :min="0"
                :max="999999999"
                label="账号每日提交发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input> -->
            </el-form-item>
            <el-form-item label="彩信24h发送限制" prop="mmsOverrunMarket">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.mmsOverrunMarket"
                :min="0"
                :max="999999999"
                label="彩信24h发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.mmsOverrunMarket"></el-input> -->
            </el-form-item>
            <el-form-item label="彩信七天发送限制" prop="mmsOverrunTotalMarket">
              <el-input-number
                class="input-w-3"
                v-model="addUserStep2Form.formData.mmsOverrunTotalMarket"
                :min="0"
                :max="999999999"
                label="彩信七天发送限制"
              ></el-input-number>
              <!-- <el-input class="input-w-3"
                                    v-model="addUserStep2Form.formData.mmsOverrunTotalMarket"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button style="width: 120px" @click="goBack()">返回</el-button>
              <el-button
                type="primary"
                style="margin-left: 20px; width: 120px"
                @click="submitForm('bombRef')"
                >提 交</el-button
              >
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      <!-- <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="addUserStep2Form"
                    label-width="140px">
                    <div class="float-div-1" style="padding-right:20px;">
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">参数设置</p>
                            <el-form-item label="通道组名称" prop="mmsChannelGroupId">
                                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.mmsChannelGroupId"
                                    filterable placeholder="请选择通道名称">
                                    <el-option v-for="(item, index) in channel" :label="item.channelGroupName"
                                        :value="item.channelGroupId" :key="index"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="计费方式" prop="mmsChargeback">
                                <el-radio-group v-model="addUserStep2Form.formData.mmsChargeback">
                                    <el-radio disabled label="1">提交计费</el-radio>
                                    <el-radio disabled label="2" style="margin-left:10px">成功计费</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="付费方式" prop="mmsPayMethod">
                                <el-radio-group v-model="addUserStep2Form.formData.mmsPayMethod">
                                    <el-radio disabled label="1">预付费</el-radio>
                                    <el-radio disabled label="2" style="margin-left:10px">后付费</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="黑词是否加黑" prop="mmsIsRepliesBlack">
                                <el-radio-group v-model="addUserStep2Form.formData.mmsIsRepliesBlack">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="加黑关键词" prop="mmsRepliesBlackWords"
                                v-if="addUserStep2Form.formData.mmsIsRepliesBlack == 1">
                                <el-input type="textarea" :rows="4" show-word-limit resize='none' class="input-w-3"
                                    placeholder=" 多个用逗号(,)隔开"
                                    v-model="addUserStep2Form.formData.mmsRepliesBlackWords"></el-input>
                            </el-form-item>
                            <el-form-item label="是否验证黑号" style="width:360px;" prop='mmsIsUserBlack'>
                                <el-radio-group v-model="addUserStep2Form.formData.mmsIsUserBlack">
                                    <el-radio label="1">是</el-radio>
                                    <el-radio label="2" style="margin-left:10px">否</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="黑号验证级别" prop="mmsUserBlackLevel1"
                                v-if="addUserStep2Form.formData.mmsIsUserBlack == 1">
                                <el-checkbox-group style="width:250px;display: flex;flex-shrink: 0;flex-wrap: wrap;"
                                    v-model="addUserStep2Form.formData.mmsUserBlackLevel1">
                                    <el-checkbox style="margin-left:0" label="1">一级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="2">二级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="3">三级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="4">四级</el-checkbox>
                                    <el-checkbox style="margin-left:0" label="5">五级</el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                            <el-form-item label="扩展号" prop="ext">
                                <el-input style="width:224px;" v-model="addUserStep2Form.formData.ext"></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">防轰炸设置</p>
                            <el-form-item label="账号每日提交发送限制" prop="dailyLimited">
                                <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input>
                            </el-form-item>
                            <el-form-item label="彩信24h发送限制" prop="overrunCode">
                                <el-input class="input-w-3" v-model="addUserStep2Form.formData.mmsOverrunMarket"></el-input>
                            </el-form-item>
                            <el-form-item label="彩信七天发送限制" prop="mmsOverrunTotalMarket">
                                <el-input class="input-w-3"
                                    v-model="addUserStep2Form.formData.mmsOverrunTotalMarket"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="float-div-2" style="padding-right:20px;">
                        <div>
                            <p style="padding:0 0 18px 40px;font-weight:bold;font-size:14px;">状态设置</p>
                            <el-form-item label="消息报告" prop="mmsReportLevel">
                                <el-radio-group v-model="addUserStep2Form.formData.mmsReportLevel" class="input-w">
                                    <el-radio label="1">不接收报告</el-radio>
                                    <el-radio label="2" style="margin-left:10px">批量推送</el-radio>
                                    <el-radio label="3" style="margin-left:10px">主动抓取</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="上行地址" prop="voiceRepliesUrl"
                                v-if="addUserStep2Form.formData.mmsReportLevel == 2">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.mmsRepliesUrl"></el-input>
                                <p style="width: 345px;"><span style="font-size:12px;">注：获取回复短信的地址</span>(以http/https开头)
                                </p>
                            </el-form-item>
                            <el-form-item label="下行地址" prop="voiceReportUrl"
                                v-if="addUserStep2Form.formData.mmsReportLevel == 2">
                                <el-input type="textarea" style="width: 200px;"
                                    v-model="addUserStep2Form.formData.mmsReportUrl"></el-input>
                                <p style="width: 345px;"><span style="font-size:12px;">注：获取状态报告的地址</span>(以http/https开头)
                                </p>
                            </el-form-item>
                            <el-form-item label="回复推送数量" prop="voiceRepliesPackageSize"
                                v-if="addUserStep2Form.formData.mmsReportLevel == 2">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsRepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="状态推送数量" prop="voiceStrepliesPackageSize"
                                v-if="addUserStep2Form.formData.mmsReportLevel == 2">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsStrepliesPackageSize"></el-input>
                            </el-form-item>
                            <el-form-item label="回复抓取数量" prop="mmsRepliesPackageSize"
                                v-if="addUserStep2Form.formData.mmsReportLevel == 3">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsRepliesPackageSize"></el-input>
                            </el-form-item>

                            <el-form-item label="状态抓取数量" prop="mmsStrepliesPackageSize"
                                v-if="addUserStep2Form.formData.mmsReportLevel == 3">
                                <el-input style="width: 200px;" placeholder="v5默认150，融合默认400"
                                    v-model="addUserStep2Form.formData.mmsStrepliesPackageSize"></el-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div style="text-align: center" class="clear-div">
                        <el-button style="width:120px" @click="goBack()">返回</el-button>
                        <el-button type="primary" style="margin-left: 20px;width:120px"
                            @click="submitForm('addUserStep2Form')">提 交</el-button>
                    </div>
                </el-form> -->
      <el-dialog
        title="彩信配置"
        v-model="dialogVisible"
        width="30%"
        :before-close="handleClose"
      >
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="70px"
          class="demo-ruleForm"
        >
          <el-form-item label="备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.remark"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button  v-if="!subLoding" type="primary" @click="submitSetting('ruleForm')">确 定</el-button>
            <el-button v-else type="primary" :loading="subLoding" disabled>稍 等</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    ElIconArrowLeft,
  },
  name: 'MMSconfiguration',
  data() {
    var checkBlackWords = (rule, value, callback) => {
      if (value == '') {
        return callback(new Error('加黑关键字不能为空'))
      } else {
        console.log(value.length)
        if (value.length <= 200) {
          var reg =
            /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,200}$/gi
          if (!reg.test(value)) {
            return callback(
              new Error('加黑关键词包括数字、汉字、字母、多个由英文逗号隔开')
            )
          } else {
            var arr = value.split(',')
            var arr2 = []
            for (var i = 0; i < arr.length; i++) {
              if (arr2.indexOf(arr[i]) < 0) {
                arr2.push(arr[i])
              }
            }
            if (arr2.length != arr.length) {
              return callback(new Error('不允许重复'))
            } else {
              callback()
            }
          }
        }
        return callback(new Error('加黑关键字不能多于200个字'))
      }
    }
    var checkSignature = (rule, value, callback) => {
      var reg = /^(\d+,?)+$/
      if (value) {
        if (!reg.test(value)) {
          callback(new Error('扩展号由1-12位数字组成'))
        } else {
          window.api.get(
            window.path.omcs + 'operatingSignature/checkClientExt',
            { ext: value, userId: this.addUserStep2Form.formData.userId },
            (res) => {
              if (res.data == true) {
                callback(new Error('此扩展号已存在'))
              } else {
                callback()
              }
            }
          )
        }
      } else {
        return callback(new Error('请输入扩展号'))
      }
      // if (!/^\d{1,12}$/.test(value)) {
      //   callback(new Error("扩展号由1-12位数字组成"));
      // } else {

      // }
    }
    return {
      isFirstEnter: false,
      jsonData: {
        consumerName: '',
        compName: '',
      }, //用户资料
      dialogVisible: false,
      ruleForm: {
        remark: '',
      },
      activeName: 'parameterRef',
      rules: {
        remark: [{ required: true, message: '请填写备注', trigger: 'change' }],
      },
      subLoding:false,
      addUserStep2Form: {
        //弹窗步骤2的数据和验证
        formData: {
          userId: '', //用户id
          mmsPayMethod: '1',
          mmsChannelGroupId: '',
          mmsChargeback: '1',
          ext: '',
          mmsOverrunMarket: '0', //防轰炸
          mmsOverrunTotalMarket: '0',
          mmsReportLevel: '1',
          mmsRepliesUrl: '',
          mmsReportUrl: '',
          mmsRepliesPackageSize: '',
          mmsStrepliesPackageSize: '',
          mmsIsRepliesBlack: '2', //是否加黑
          mmsRepliesBlackWords: '', //加黑关键词
          mmsIsUserBlack: '1', //是否验证黑号
          mmsUserBlackLevel1: ['1', '2', '3'], //黑号验证级别存储
          mmsUserBlackLevel: '', //黑号验证级别存储
          mmsProvinceRatio: '',
          mmsSplitDb: '',
          mmsSplitRatio: '',
          dailyLimited: '', //每日限量
        },
        formRule: {
          mmsPayMethod: [
            { required: true, message: '请选择付费方式', trigger: 'change' },
          ],
          mmsIsRepliesBlack: [
            { required: true, message: '请选择是否加黑', trigger: 'blur' },
          ],
          mmsIsUserBlack: [
            {
              required: true,
              message: '请选择是否验证黑号',
              trigger: 'change',
            },
          ],
          mmsUserBlackLevel1: [
            {
              required: true,
              message: '请选择黑号验证级别',
              trigger: 'change',
            },
          ],
          ext: [
            { required: true, validator: checkSignature, trigger: ['blur'] },
          ],
          mmsRepliesUrl: [
            {
              required: false,
              message: '请输入上行地址',
              trigger: ['change', 'blur'],
            },
            {
              min: 1,
              max: 255,
              message: '长度在 255 个字符以内',
              trigger: ['blur', 'change'],
            },
          ],
          mmsReportUrl: [
            {
              required: false,
              message: '请输入下行地址',
              trigger: ['change', 'blur'],
            },
            {
              min: 1,
              max: 255,
              message: '长度在 255 个字符以内',
              trigger: ['blur', 'change'],
            },
          ],
          mmsChannelGroupId: [
            { required: true, message: '请选择通道', trigger: 'change' },
          ],
          mmsChargeback: [
            { required: true, message: '请选择计费方式', trigger: 'change' },
          ],
          mmsRepliesBlackWords: [
            { required: true, validator: checkBlackWords, trigger: 'change' },
          ],
        },
      },
      channel: [], //通道
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      window.api.get(
        window.path.omcs +
          'operatinguser/' +
          this.addUserStep2Form.formData.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName
          this.jsonData.compName = res.data.compName
        }
      )
    },
    /**获取通道列表 */
    getChannel() {
      window.api.get(
        window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup?productId=2',
        {},
        (res) => {
          this.channel = res.data
        }
      )
    },
    //返回
    goBack() {
      // this.$router.push({ path: 'UserDetail',query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({
        path: 'UserDetail',
        query: { id: this.$route.query.id },
      })
    },
    //编辑的赋值
    handleEdit() {
      window.api.get(
        window.path.omcs + 'operatinguser/userMmsInfo/' + this.$route.query.id,
        {},
        (res) => {
          for (let i in res.data) {
            if (
              !(
                res.data[i] == null ||
                i == 'mmsChannelGroupId' ||
                i == 'mmsUserBlackLevel'
              )
            ) {
              res.data[i] += ''
            }
          }
          res.data['mmsUserBlackLevel1'] = res.data.mmsUserBlackLevel.split(',')
          this.addUserStep2Form.formData = res.data
        }
      )
    },
    handleClick(tab, event) {
      console.log(tab, event)
    },
    //弹窗表单的提交--第一步表单和第二步表单
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          if (this.addUserStep2Form.formData.mmsReportLevel != 2) {
            //消息报告如果不选批量推送时，重置批量推送下面的推送方式，上下行地址
            this.addUserStep2Form.formData.mmsRepliesUrl = ''
            this.addUserStep2Form.formData.mmsReportUrl = ''
          }
          if (this.addUserStep2Form.formData.mmsReportLevel == 1) {
            this.addUserStep2Form.formData.mmsRepliesPackageSize = ''
            this.addUserStep2Form.formData.mmsStrepliesPackageSize = ''
          }
          this.addUserStep2Form.formData.mmsUserBlackLevel =
            this.addUserStep2Form.formData.mmsUserBlackLevel1.join(',')
          this.dialogVisible = true
          // this.$confirms.confirmation('put','确认提交短信配置？',window.path.omcs+'operatinguser/userMmsInfo',this.addUserStep2Form.formData,res =>{
          //     //返回用户管理页面
          //     this.goBack()
          // });
        } else {
          return false
        }
      })
    },
    submitSetting(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.subLoding = true
          this.addUserStep2Form.formData.remark = this.ruleForm.remark
          window.api.put(
            window.path.omcs + 'operatinguser/userMmsInfo',
            this.addUserStep2Form.formData,
            (res) => {
              if (res.code == 200) {
                //返回用户管理页面
                this.dialogVisible = false
                this.subLoding = false
                this.goBack()
              } else {
                this.subLoding = false
                this.$message.error(res.msg)
              }
            }
          )
          // this.$confirms.confirmation('put','确认提交短信配置？',window.path.omcs+'operatinguser/userMmsInfo',this.addUserStep2Form.formData,res =>{
          //         //返回用户管理页面
          //         this.dialogVisible = false
          //         this.goBack()
          //     });
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.addUserStep2Form.formData.userId = this.$route.query.id
      this.getChannel() /**获取通道列表 */
      this.handleEdit()
      this.getBasicData()
    })
  },
  // activated() {
  //     if (this.$route.meta.isBack || !this.isFirstEnter) {
  //         this.$nextTick(() => {
  //             this.addUserStep2Form.formData.userId = this.$route.query.id
  //             this.getChannel();  /**获取通道列表 */
  //             this.handleEdit()
  //             this.getBasicData()
  //         });
  //     } else {
  //         this.$route.meta.isBack = false
  //         this.isFirstEnter = false;
  //     }

  // },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs['ruleForm'].resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
  background: #fff;
  border-radius: 5px;
}
.input-w {
  width: 345px !important;
}
.el-radio-group .el-radio {
  margin-right: 0px;
}
.fillet {
  padding: 20px;
}
.input-w-2 {
  width: 200px !important;
}
.input-w-3 {
  width: 200px !important;
}
.input-w-4 {
  width: 190px !important;
}
.input-w-sm {
  width: 126px !important;
}
.input-w-f {
  width: 80px !important;
}
.float-div-1 {
  float: left;
}
.float-div-2 {
  float: left;
}
.float-div-3 {
  float: left;
}
.clear-div {
  clear: both;
}
.red {
  color: red;
}
@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}
</style>

<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
  top: 1px !important;
}

.radioLeft .el-form-item__content {
  margin-left: 74px !important;
}
</style>
