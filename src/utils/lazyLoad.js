/**
 * 懒加载和动态导入工具
 * 支持组件和第三方库的按需加载，优化首屏加载性能
 */

import { defineAsyncComponent } from 'vue'

/**
 * 创建懒加载组件
 * @param {Function} loader - 组件加载函数
 * @param {Object} options - 加载选项
 * @returns {Object} Vue异步组件
 */
export function createLazyComponent(loader, options = {}) {
  const defaultOptions = {
    // 加载中显示的组件
    loadingComponent: () => import('@/components/common/Loading.vue'),
    // 错误时显示的组件  
    errorComponent: () => import('@/components/common/LoadError.vue'),
    // 显示loading组件前的延迟时间
    delay: 200,
    // 超时时间
    timeout: 10000
  }

  return defineAsyncComponent({
    loader,
    ...defaultOptions,
    ...options
  })
}

/**
 * 预加载组件
 * @param {Array} componentLoaders - 组件加载函数数组
 */
export function preloadComponents(componentLoaders) {
  if ('requestIdleCallback' in window) {
    // 在浏览器空闲时预加载
    window.requestIdleCallback(() => {
      componentLoaders.forEach(loader => {
        try {
          loader()
        } catch (error) {
          console.warn('Preload component failed:', error)
        }
      })
    })
  } else {
    // 降级方案：延迟预加载
    setTimeout(() => {
      componentLoaders.forEach(loader => {
        try {
          loader()
        } catch (error) {
          console.warn('Preload component failed:', error)
        }
      })
    }, 2000)
  }
}

/**
 * 动态导入第三方库
 * @param {string} libName - 库名称
 * @returns {Promise} 导入的库
 */
export async function importLibrary(libName) {
  const libraryMap = {
    // 图表库
    'echarts': () => import(/* webpackChunkName: "vendor-echarts" */ 'echarts'),
    
    // 表格组件
    'vxe-table': () => import(/* webpackChunkName: "vendor-table" */ 'vxe-table'),
    'vxe-ui': () => import(/* webpackChunkName: "vendor-table" */ 'vxe-pc-ui'),
    
    // 编辑器
    'quill': () => import(/* webpackChunkName: "vendor-editors" */ 'vue-quill-editor'),
    'mavon': () => import(/* webpackChunkName: "vendor-editors" */ 'mavon-editor'),
    
    // 工具库
    'lodash': () => import(/* webpackChunkName: "vendor-utils" */ 'lodash'),
    'moment': () => import(/* webpackChunkName: "vendor-utils" */ 'moment'),
    'dayjs': () => import(/* webpackChunkName: "vendor-utils" */ 'dayjs'),
    
    // 其他
    'socket.io': () => import(/* webpackChunkName: "vendor-misc" */ 'socket.io-client'),
    'cropperjs': () => import(/* webpackChunkName: "vendor-misc" */ 'vue-cropperjs')
  }

  const loader = libraryMap[libName]
  if (!loader) {
    throw new Error(`Library ${libName} not found in library map`)
  }

  try {
    const lib = await loader()
    return lib.default || lib
  } catch (error) {
    console.error(`Failed to load library ${libName}:`, error)
    throw error
  }
}

/**
 * 创建路由懒加载函数
 * @param {string} path - 组件路径
 * @param {string} chunkName - chunk名称
 * @returns {Function} 路由组件加载函数
 */
export function createRouteComponent(path, chunkName) {
  return () => import(
    /* webpackChunkName: "page-[request]" */ 
    `@/components/page/${path}.vue`
  )
}

/**
 * 批量创建路由组件
 * @param {Object} routes - 路由配置对象
 * @returns {Object} 处理后的路由组件对象
 */
export function createRouteComponents(routes) {
  const components = {}
  
  Object.entries(routes).forEach(([name, path]) => {
    components[name] = createRouteComponent(path, name)
  })
  
  return components
}

/**
 * 智能资源预加载
 * 根据用户行为预测和预加载可能需要的资源
 */
export class SmartPreloader {
  constructor() {
    this.loadedChunks = new Set()
    this.preloadQueue = []
    this.isPreloading = false
  }

  /**
   * 添加预加载任务
   * @param {Function} loader - 加载函数
   * @param {number} priority - 优先级 (1-10)
   */
  addTask(loader, priority = 5) {
    this.preloadQueue.push({ loader, priority })
    this.preloadQueue.sort((a, b) => b.priority - a.priority)
    
    if (!this.isPreloading) {
      this.startPreloading()
    }
  }

  /**
   * 开始预加载
   */
  async startPreloading() {
    if (this.isPreloading || this.preloadQueue.length === 0) return
    
    this.isPreloading = true
    
    while (this.preloadQueue.length > 0) {
      const task = this.preloadQueue.shift()
      
      try {
        // 在空闲时间预加载
        await this.waitForIdle()
        await task.loader()
      } catch (error) {
        console.warn('Preload task failed:', error)
      }
    }
    
    this.isPreloading = false
  }

  /**
   * 等待浏览器空闲
   */
  waitForIdle() {
    return new Promise(resolve => {
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(resolve)
      } else {
        setTimeout(resolve, 100)
      }
    })
  }

  /**
   * 预加载用户可能访问的页面
   * @param {Array} routeNames - 路由名称数组
   */
  preloadRoutes(routeNames) {
    routeNames.forEach(routeName => {
      this.addTask(() => {
        return import(`@/components/page/${routeName}.vue`)
      }, 3)
    })
  }

  /**
   * 预加载图表库（当检测到图表组件时）
   */
  preloadCharts() {
    this.addTask(() => importLibrary('echarts'), 8)
  }

  /**
   * 预加载表格库（当检测到大量数据时）
   */
  preloadTables() {
    this.addTask(() => importLibrary('vxe-table'), 7)
  }

  /**
   * 预加载编辑器（当用户有编辑权限时）
   */
  preloadEditors() {
    this.addTask(() => importLibrary('quill'), 6)
    this.addTask(() => importLibrary('mavon'), 5)
  }
}

// 创建全局预加载器实例
export const smartPreloader = new SmartPreloader()

/**
 * 组件可见性监听器
 * 当组件即将进入视口时触发加载
 */
export function createIntersectionLoader(threshold = 0.1) {
  const loadedComponents = new Set()
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting && !loadedComponents.has(entry.target)) {
        const loader = entry.target.dataset.loader
        if (loader && window[loader]) {
          window[loader]()
          loadedComponents.add(entry.target)
          observer.unobserve(entry.target)
        }
      }
    })
  }, { threshold })

  return {
    observe: (element, loader) => {
      element.dataset.loader = loader
      observer.observe(element)
    },
    unobserve: (element) => {
      observer.unobserve(element)
    },
    disconnect: () => {
      observer.disconnect()
    }
  }
}

/**
 * 网络状态检测和适配
 */
export class NetworkAdaptiveLoader {
  constructor() {
    this.connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
    this.isSlowNetwork = this.detectSlowNetwork()
  }

  /**
   * 检测慢网络
   */
  detectSlowNetwork() {
    if (!this.connection) return false
    
    // 2G或更慢的网络
    return this.connection.effectiveType === 'slow-2g' || 
           this.connection.effectiveType === '2g' ||
           this.connection.downlink < 1.5
  }

  /**
   * 根据网络状况决定是否预加载
   */
  shouldPreload() {
    if (this.isSlowNetwork) return false
    if (this.connection && this.connection.saveData) return false
    return true
  }

  /**
   * 自适应加载策略
   */
  adaptiveLoad(urgentLoader, normalLoader, lowPriorityLoader) {
    if (this.isSlowNetwork) {
      // 慢网络只加载紧急内容
      return urgentLoader()
    } else if (this.shouldPreload()) {
      // 快网络可以预加载
      Promise.all([
        urgentLoader(),
        normalLoader(),
        lowPriorityLoader()
      ])
    } else {
      // 正常网络加载必要内容
      return Promise.all([
        urgentLoader(),
        normalLoader()
      ])
    }
  }
}

// 导出网络适配器实例
export const networkAdapter = new NetworkAdaptiveLoader() 