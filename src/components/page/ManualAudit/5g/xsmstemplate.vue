<template>
  <div class="container_left">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="iconfont icon-xiaolian"></i>阅信模板审核</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="padding: 10px">
      <el-form
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="模板名称" prop="templateName">
          <el-input class="input-w" v-model="ruleForm.templateName"></el-input>
        </el-form-item>
        <el-form-item label="chatbotId" prop="chatbotId">
          <el-input class="input-w" v-model="ruleForm.chatbotId"></el-input>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <div style="padding: 0 10px"></div>
    <div style="padding: 10px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        height="620"
        :data="tableDataObj.tableData"
        :span-method="objectSpanMethod"
        :header-cell-style="{
          background: '#eef1f6',
          color: '#606266',
        }"
      >
        > >
        <el-table-column label="提交时间" width="100">
          <template v-slot="scope">
            <p>{{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</p>
            <p>{{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</p>
            <!-- <div class="item_time">
                  {{
                  moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                }}
                </div> -->
          </template>
        </el-table-column>
        <el-table-column label="用户名称">
          <template v-slot="scope">{{ scope.row.username }}</template>
        </el-table-column>
        <el-table-column label="chatbot名称" width="90">
          <template v-slot="scope">{{ scope.row.chatbotName }}</template>
        </el-table-column>
        <el-table-column label="chatbotId">
          <template v-slot="scope">{{ scope.row.chatbotId }}</template>
        </el-table-column>
        <el-table-column label="模板名称">
          <template v-slot="scope">{{ scope.row.templateName }}</template>
        </el-table-column>
        <el-table-column label="模板ID">
          <template v-slot="scope">{{ scope.row.id }}</template>
        </el-table-column>
        <el-table-column label="模板类型">
          <template v-slot="scope">
            <span v-if="scope.row.templateType == 'IMAGE_AND_TEXT'"
              >单图文</span
            >
            <span v-if="scope.row.templateType == 'STANDARD_IMAGE_AND_TEXT'"
              >多图文</span
            >
            <span v-if="scope.row.templateType == 'RED_PACKET'">红包</span>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" width="110">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f6f6f6;
                color: #999999;
                border: 1px solid #dddddd;
              "
              v-if="scope.row.status == -1"
            >
              已删除
            </div>
            <div
              class="item_status"
              style="
                background: #f6f6f6;
                color: #999999;
                border: 1px solid #dddddd;
              "
              v-if="scope.row.status == 0"
            >
              草稿
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.status == 5"
            >
              待审核
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.status == 10"
            >
              运营商审核中
            </div>
            <el-tooltip effect="dark" placement="top-start" v-if="scope.row.status == 20">
              <template v-slot:content>
                <div>
                  {{ scope.row.auditReason }}
                </div>
              </template>
              <div
                class="item_status"
                style="
                  background: #f5fff4;
                  color: #00c159;
                  border: 1px solid #a6edb1;
                "
              >
                审核通过
              </div>
            </el-tooltip>
            <el-tooltip effect="dark" placement="top-start" v-if="scope.row.status == 30">
              <template v-slot:content>
                <div>
                  {{ scope.row.auditReason }}
                </div>
              </template>
              <div
                class="item_status"
                style="
                  background: #ffe7e7;
                  color: #ff6666;
                  border: 1px solid #fac2c2;
                "
              >
                审核失败
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="审核人">
          <template v-slot="scope">{{ scope.row.auditUser }}</template>
        </el-table-column>
        <el-table-column label="审核时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.auditTime">
              {{ $parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.auditTime">
              {{ $parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time" v-if="scope.row.auditTime">{{
                  moment(scope.row.auditTime).format("YYYY-MM-DD HH:mm:ss")
                }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="来源">
          <template v-slot="scope">
            <span
              style="color: #409eff; cursor: pointer"
              @click="ssoLogin(scope.row)"
              v-if="scope.row.source == '1'"
              >阅信</span
            >
            <span v-if="scope.row.source == '2'">AIM</span>
          </template>
        </el-table-column>
        <el-table-column label="模板ID" width="90">
          <template v-slot="scope">
            <span>{{ scope.row.resourceId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="运营商审核状态" width="110">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f6f6f6;
                color: #999999;
                border: 1px solid #dddddd;
              "
              v-if="scope.row.applyStatus == 0"
            >
              待提交
            </div>
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.applyStatus == 1"
            >
              审核中
            </div>
            <div
              class="item_status"
              style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              "
              v-if="scope.row.applyStatus == 2"
            >
              成功
            </div>
            <el-tooltip
              v-if="scope.row.applyStatus == 3"
              effect="dark"
              placement="top-start"
            >
              <template v-slot:content>
                <div>
                  {{ scope.row.reason }}
                </div>
              </template>
              <div
                class="item_status"
                style="
                  background: #ffe7e7;
                  color: #ff6666;
                  border: 1px solid #fac2c2;
                "
              >
                失败
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.status == 20"
              link
              style="color: #16a589; margin-left: 0px"
              @click="checkContent(scope.row)"
              ><el-icon><SuccessFilled /></el-icon> 查看模板</el-button
            >
            <el-button
              v-else
              link
              style="color: #e6a23c; margin-left: 0px"
              @click="checkContent(scope.row)"
              ><el-icon><WarningFilled /></el-icon> 模板审核</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="display: felx">
        <div></div>
        <div style="margin: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      :title="'模板审核' + '-' + title"
      v-model="dialogVisible"
      width="60%"
      :before-close="handleClose"
    >
      <viewXsmsTemo :list="detail" :xsmsPages="xsmsList" :aimPages="aimList" />
      <div
        style="display: flex; margin: 10px 0"
        v-if="detail.status == 0 || detail.status == 5"
      >
        <el-form
          :model="reasonFrom"
          :inline="true"
          :rules="rules"
          ref="reasonFrom"
          class="demo-reasonFrom"
        >
          <el-form-item label="审核原因：" prop="reason">
            <el-input
              :maxlength="20"
              show-word-limit
              type="textarea"
              placeholder="不能超过20个字符"
              style="width: 500px"
              v-model="reasonFrom.reason"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <!-- <div
            v-else
            style="
              width: 100%;
              background: #f2f5fa;
              line-height: 30px;
              display: flex;
              margin-top: 10px;
            "
          >
            <div style="padding: 5px">
              <div>
                <span>审核状态：</span>
                <span v-if="detail.status == 0">未审核</span>
                <span style="color: red" v-if="detail.status == 5">审核中</span>
                <span style="color: red" v-if="detail.status == 10"
                  >运营商审核中</span
                >
                <span style="color: #409eff" v-if="detail.status == 20"
                  >审核通过</span
                >
                <span style="color: red" v-if="detail.status == 30"
                  >审核失败</span
                >
              </div>
              <div>
                <span>审核人：</span>
                <span>{{detail.auditUser}}</span>
              </div>
            </div>
            <div style="padding: 5px; margin-left: 30px">
              <div>
                <span>审核原因：</span>
                <span>{{detail.auditReason}}</span>
              </div>
              <div>
                <span>审核时间：</span>
                <span v-if="detail.auditTime">{{
                  moment(detail.auditTime).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
              </div>
            </div>
          </div> -->
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <!-- <el-button
              v-if="detail.status == 0 || detail.status == 5 && detail.templateType != 'text'"
              type="primary"
              @click="audit(detail, 1, '通过','reasonFrom')"
            >
              提交运营商</el-button
            > -->
          <el-button
            v-if="detail.status == 0 || detail.status == 5"
            type="primary"
            @click="audit(detail, 1, '通过', 'reasonFrom')"
          >
            通过</el-button
          >
          <el-button
            v-if="detail.status == 0 || detail.status == 5"
            type="danger"
            style="margin-left: 10px"
            @click="audit(detail, 2, '不通过', 'reasonFrom')"
            >不通过</el-button
          >
          <!-- <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../../../utils/gogocodeTransfer'
import moment from 'moment'
import bus from '@/components/common/bus'
import viewXsmsTemo from './components/viewXsms.vue'
export default {
  components: {
    viewXsmsTemo
  },
  name: 'xsmsTemplate',
  data() {
    return {
      status: '',
      title: '',
      reason: '',
      dialogVisible: false,
      codeFlag: true,
      ruleForm: {
        templateName: '',
        username: '',
        chatbotId: '',
        currentPage: 1,
        pageSize: 10,
      },
      reasonFrom: {
        reason: '',
      },
      rules: {
        reason: [
          { required: true, message: '审核原因不能为空', trigger: 'blur' },
        ],
      },
      tabelAlllist: {
        templateName: '',
        username: '',
        currentPage: 1,
        pageSize: 10,
      },
      detail: {},
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      xsmsList: [],
      aimList: [],
      number: 0,
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getchatList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  mounted() {
    $on(bus, 'num', (mes) => {
      console.log(mes,'ll');
      // this.number = mes
    })
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 8 === 0) {
        return 'warning-row'
      }
      return ''
    },

    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 10 || columnIndex == 13) {
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1,
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0,
          }
        }
      }
    },
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/xsmstemplate/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
          // console.log(res, "res");
        }
      )
    },
    ssoLogin(row) {
      this.$router.push({
        path: '/chatbotChannel',
        query: { chatbotId: row.chatbotId, username: row.createUser },
      })
    },
    checkContent(row) {
      this.title = row.templateName
      this.detail = row
      this.dialogVisible = true
      window.api.post(
        window.path.fiveWeb + '/operator/xsmstemplate/queryXsmsTemplateContent',
        {
          id: row.id,
        },
        (res) => {
          if (res.code == 200) {
            this.xsmsList = res.data.pages
            this.aimList = res.data.aimPages
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        }
      )
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    checked(row) {
      this.dialogVisible = true
      this.registerFrom = row
      this.registerFrom.renewed = row.renewed + ''
      this.registerFrom.industryTypeCode = row.industryTypeCode * 1
      this.status = row.customerStatus
      //   console.log(this.registerFrom );
      if (row.code) {
        this.codeFlag = true
      } else {
        this.codeFlag = false
      }
    },
    audit(row, val, tit, from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          if (val == 2) {
            this.$confirms.confirmation(
              'post',
              `确认审核${tit}模板吗？`,
              window.path.fiveWeb + '/operator/xsmstemplate/audit',
              {
                audit: val,
                reason: this.reasonFrom.reason,
                aimPages: this.aimList,
                pages: this.xsmsList,
                templateId: row.id,
              },
              (res) => {
                if (res.code == 200) {
                  this.dialogVisible = false
                  this.getchatList()
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'error',
                  // })
                }
              }
            )
          } else {
            if (this.number > 69) {
              this.$message({
                message: 'AIM摘要内容字数超过69个字！',
                type: 'warning',
              })
            } else {
              this.$confirms.confirmation(
                'post',
                `确认审核${tit}模板吗？`,
                window.path.fiveWeb + '/operator/xsmstemplate/audit',
                {
                  audit: val,
                  reason: this.reasonFrom.reason,
                  aimPages: this.aimList,
                  pages: this.xsmsList,
                  templateId: row.id,
                },
                (res) => {
                  if (res.code == 200) {
                    this.dialogVisible = false
                    this.getchatList()
                  } else {
                    // this.$message({
                    //   message: res.msg,
                    //   type: 'error',
                    // })
                  }
                }
              )
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.reason = ''
        this.reasonFrom.reason = ''
        this.xsmsList = []
        this.aimList = []
        // this.$refs['reasonFrom'].resetFields();
        // this.detail = {};
        if (this.detail.status == 0 || this.detail.status == 5) {
          this.$refs['reasonFrom'].resetFields()
        }
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>

<style>
.el-table .warning-row {
  background: #fafafa;
}
.el-table .success-row {
  background: #f0f9eb;
}
/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>
