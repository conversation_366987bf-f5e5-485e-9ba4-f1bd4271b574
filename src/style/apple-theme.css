/* Apple风格主题 */
:root {
  /* 主色调 */
  --apple-primary: #007aff;
  --apple-secondary: #5ac8fa;
  --apple-success: #34c759;
  --apple-warning: #ff9500;
  --apple-danger: #ff3b30;
  --apple-info: #5ac8fa;
  
  /* 中性色 */
  --apple-text-primary: #000000;
  --apple-text-secondary: #3c3c43;
  --apple-text-tertiary: #8e8e93;
  --apple-border: rgba(60, 60, 67, 0.3);
  --apple-border-light: rgba(60, 60, 67, 0.12);
  
  /* 背景色 */
  --apple-bg-primary: #ffffff;
  --apple-bg-secondary: #f2f2f7;
  --apple-bg-tertiary: #e5e5ea;
  
  /* 圆角 */
  --apple-radius-sm: 6px;
  --apple-radius-md: 8px;
  --apple-radius-lg: 12px;
  
  /* 阴影 */
  --apple-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --apple-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.05);
  --apple-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05);
  
  /* 字体 */
  --apple-font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  
  /* 动画 */
  --apple-transition-fast: 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  --apple-transition-normal: 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* 基础元素样式 */
body {
  font-family: var(--apple-font-family);
  color: var(--apple-text-primary);
  background-color: var(--apple-bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 按钮样式 */
.el-button {
  border-radius: var(--apple-radius-md);
  font-weight: 500;
  transition: all var(--apple-transition-fast);
}

.el-button--primary {
  background-color: var(--apple-primary);
  border-color: var(--apple-primary);
}

.el-button--success {
  background-color: var(--apple-success);
  border-color: var(--apple-success);
}

.el-button--warning {
  background-color: var(--apple-warning);
  border-color: var(--apple-warning);
}

.el-button--danger {
  background-color: var(--apple-danger);
  border-color: var(--apple-danger);
}

.el-button--info {
  background-color: var(--apple-info);
  border-color: var(--apple-info);
}

/* 输入框样式 */
.el-input__inner {
  border-radius: var(--apple-radius-md);
  border-color: var(--apple-border-light);
  transition: all var(--apple-transition-fast);
}

.el-input__inner:focus {
  border-color: var(--apple-primary);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
}

/* 卡片样式 */
.el-card {
  border-radius: var(--apple-radius-lg);
  border: none;
  box-shadow: var(--apple-shadow-md);
  overflow: hidden;
}

/* 表格样式 */
.el-table {
  border-radius: var(--apple-radius-md);
  overflow: hidden;
}

.el-table th {
  background-color: var(--apple-bg-secondary);
  font-weight: 500;
}

.el-table tr:hover > td {
  background-color: rgba(0, 122, 255, 0.05);
}

/* 标签页样式 */
.el-tabs__item {
  font-weight: 500;
}

.el-tabs__active-bar {
  background-color: var(--apple-primary);
}

/* 下拉菜单样式 */
.el-dropdown-menu {
  border-radius: var(--apple-radius-md);
  box-shadow: var(--apple-shadow-md);
  border: none;
}

/* 对话框样式 */
.el-dialog {
  border-radius: var(--apple-radius-lg);
  overflow: hidden;
  box-shadow: var(--apple-shadow-lg);
}

.el-dialog__header {
  padding: 20px;
  border-bottom: 1px solid var(--apple-border-light);
}

.el-dialog__title {
  font-weight: 500;
}

/* 分页样式 */
.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: var(--apple-primary);
}

/* 通知和消息样式 */
.el-message {
  border-radius: var(--apple-radius-md);
  border: none;
  box-shadow: var(--apple-shadow-md);
}

.el-notification {
  border-radius: var(--apple-radius-md);
  border: none;
  box-shadow: var(--apple-shadow-md);
}

/* 加载样式 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

/* 容器样式 */
.container {
  background-color: var(--apple-bg-primary);
  border-radius: var(--apple-radius-lg);
  border: none;
  box-shadow: var(--apple-shadow-sm);
  padding: 24px;
}

/* 移动适配 */
@media screen and (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .el-dialog {
    width: 90% !important;
  }
} 