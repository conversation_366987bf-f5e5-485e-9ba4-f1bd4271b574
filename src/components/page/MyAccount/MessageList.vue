<template>
  <div class="showMsg" style="height: 100%">
    <el-container>
      <el-aside width="250px" style="height: 100%; background-color: #f7f8fa">
        <div class="showMsgLeft">
          <div class="showMsg-title">消息分类</div>
          <show-msg-left :showMsgData="msgData"></show-msg-left>
        </div>
      </el-aside>
      <el-main style="background-color: #ffffff">
        <div class="showMsgMain">
          <div class="tableTitle">{{ tableTitle }}</div>
          <div class="tableSubTitle">
            <div class="subTitleLeft">
              <el-button type="primary" @click="markRead">标记为已读</el-button>
              <el-button type="primary" @click="muchDel">删除</el-button>
            </div>
            <div class="subTitleRight">
              <el-input
                class="searchInput"
                v-model="formInline.content"
                placeholder="消息内容"
              >
              </el-input>
              <div class="searchLog">
                <i class="icon iconfont icon-sousuo1"></i>
              </div>
              <!-- 封装的时间 -->
              <el-date-picker
                class="input-w"
                v-model="formInline.createTime"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="hande"
              >
              </el-date-picker>
            </div>
          </div>
          <!-- 表格 -->
          <el-table
            :row-class-name="rowClass"
            ref="multipleTable"
            :data="tableDataObj.tableData"
            style="width: 100%"
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              type="index"
              :index="indexMethod"
              label="序号"
              width="50"
            >
            </el-table-column>
            <el-table-column
              prop="messageType"
              label="消息类别"
              :formatter="formatData"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="messageContent"
              label="消息内容"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="日期" prop="createTime" width="150">
            </el-table-column>
          </el-table>
          <!--分页-->
          <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0"
            >
              <el-pagination
                style="float: right"
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="tabelAlllist.currentPage"
                :page-size="tabelAlllist.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageTotal"
              >
              </el-pagination>
            </el-col>
          </template>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import showMsgLeft from '@/components/page/MyAccount/component/showMsgLeft'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
let _ = require('lodash')
export default {
  name: 'showMsg',
  components: {
    showMsgLeft,
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      tableTitle: '', //表格上面的展示消息
      hasSelectBox: [], //选择的复选框值
      msgData: {}, //左侧msg消息条数
      isSelect: '',
      formInline: {
        content: '',
        messageType: '', //消息类型
        createTime: '',
        startTime: '',
        endTime: '',
      },
      pageTotal: 0, //分页的总条数
      tabelAlllist: {
        content: '',
        createTime: '',
        startTime: '',
        endTime: '',
        messageType: '', //消息类别
        currentPage: 1, //当前页
        pageSize: 10, //每一页条数
      },

      //  时间插件配置
      datePluginValueList: {
        type: 'date',
        placeholder: '请选择时间',
      },
      tableDataObj: {
        loading2: false,
        // 表格的数据
        tableData: [],
      },
    }
  },
  watch: {
    formInline: {
      handler(val, oldVal) {
        for (let key in this.formInline) {
          this.tabelAlllist[key] = this.formInline[key]
        }
        this.tabelAlllist.currentPage = 1
        this.tabelAlllist.pageSize = 10
        this.getCompany()
      },
      deep: true,
    },
    $route: {
      deep: true,

      //监听路由的变化
      handler() {
        let msgType = this.$route.query.msg
        if (msgType == 0) {
          this.formInline.messageType = ''
          this.tableTitle = '全部消息'
        } else if (msgType == 1) {
          this.formInline.messageType = '1'
          this.tableTitle = '预警通知'
        } else if (msgType == 2) {
          this.formInline.messageType = '2'
          this.tableTitle = '系统消息'
        } else if (msgType == 3) {
          this.formInline.messageType = '3'
          this.tableTitle = '审批提醒'
        }
        this.searchEmpty() //清空查询条件
      },

      immediate: true,
    },
  },
  methods: {
    searchEmpty() {
      console.log(22)
      this.formInline.content = ''
      this.formInline.createTime = ''
      this.tabelAlllist.content = ''
      this.tabelAlllist.startTime = ''
      this.tabelAlllist.endTime = ''
      this.tabelAlllist.currentPage = 1
      this.tabelAlllist.pageSize = 10
    },
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      let formDatas = this.tabelAlllist
      window.api.post(
        window.path.omcs + 'operatingaccountmessage/page',
        formDatas,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.loading2 = false
          this.msgData = res.countMap
          this.pageTotal = res.total
        }
      )
    },
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.startTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime =
          this.moment(val[1]).format('YYYY-MM-DD ') + ' 23:59:59'
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.getTableData()
    },
    //表格数据转换
    formatData: function (row, column) {
      if (column.property == 'messageType') {
        let msgType
        if (row[column.property] == 2) {
          msgType = '系统消息'
        } else if (row[column.property] == 1) {
          msgType = '预警通知'
        } else if (row[column.property] == 3) {
          msgType = '审批提醒'
        } else if (row[column.property] == 4) {
          msgType = '活动福利'
        }
        return msgType
      } else if (column.property == 'createTime') {
        return formatDate(
          new Date(row[column.property] * 1000),
          'YYYY-MM-DD HH:mm:ss'
        )
      }
    },
    // 设置行的颜色
    rowClass: function (row, rowIndex) {
      if (row.row.messageStatus == 2) {
        //2为未读
        return 'normalClass'
      } else {
        return 'noReadClass'
      }
    },
    //标记为已读
    markRead: function () {
      if (this.hasSelectBox.length == 0) {
        this.dialogAlert('请至少选择一条消息 "标记已读"')
      } else {
        let messageIds = []
        for (let i = 0; i < this.hasSelectBox.length; i++) {
          messageIds.push(this.hasSelectBox[i].messageId)
        }
        //发送请求
        this.$confirms.confirmation(
          'put',
          '确定标记已读？',
          window.path.omcs + 'operatingaccountmessage/status?ids=' + messageIds,
          {},
          (res) => {
            this.getTableData()
          }
        )
      }
      this.isSelect = false
    },
    //删除
    muchDel: function () {
      if (this.hasSelectBox.length == 0) {
        this.dialogAlert('请至少选择一条消息 "删除"')
      } else {
        let messageIds = []
        for (let i = 0; i < this.hasSelectBox.length; i++) {
          messageIds.push(this.hasSelectBox[i].messageId)
        }
        //发送请求
        this.$confirms.confirmation(
          'delete',
          '此操作将永久删除该数据, 是否继续？',
          window.path.omcs + 'operatingaccountmessage/deleteBatch',
          { ids: messageIds + '' },
          (res) => {
            this.getTableData()
          }
        )
      }
    },
    //弹窗
    dialogAlert: function (msg) {
      this.$alert(msg, '温馨提示', {
        confirmButtonText: '确定',
      })
    },
    /**
     * 表格公共的方法--start
     * **/
    //复选框选择
    handleSelectionChange(val) {
      const selectionArr = []
      val.forEach(function (el) {
        selectionArr.push(el)
      })
      this.hasSelectBox = selectionArr
    },
    /**
     * 表格公共的方法--end
     * **/

    indexMethod(index) {
      return (index += 1)
    },
  },
  created() {
    this.getTableData()
    this.getCompany = _.debounce(function () {
      this.getTableData()
    }, 500)
  },
}
</script>

<style scoped>
.showMsg-title {
  padding: 20px 10px;
  font-size: 24px;
  color: #333;
  font-weight: 500;
}
.tableSubTitle {
  position: relative;
  margin-bottom: 20px;
}
.subTitleRight {
  position: absolute;
  right: 0;
  top: 0;
}
.searchInput {
  width: 220px;
}
.searchLog {
  position: relative;
  display: inline-block;
  left: -30px;
  cursor: pointer;
}
.tableTitle {
  color: #333;
  font-weight: 600;
  margin-bottom: 30px;
}
.showMsgMain {
  padding: 40px 20px;
}
.showMsg {
  position: relative;
}
</style>

<style>
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
/* 没有看的内容 */
.noReadClass {
  font-weight: bold;
}
/* 看过的内容 */
.normalClass {
  color: #999;
}
</style>
