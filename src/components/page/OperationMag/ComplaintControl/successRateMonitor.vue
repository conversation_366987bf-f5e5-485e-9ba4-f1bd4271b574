<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select
              v-model="formInline.status"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="待处理" value="0"></el-option>
              <el-option label="处理中" value="1"></el-option>
              <el-option label="已成功" value="2"></el-option>
              <el-option label="已回执" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <el-date-picker
              v-model="formInline.createTime"
              type="datetimerange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              @change="handleTimeChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button type="primary" plain @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <!-- <el-button type="primary" @click="addopt">新增</el-button> -->
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="successRateMonitor"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
        >
          <!-- <vxe-column type="checkbox" width="50"></vxe-column> -->
          <vxe-column field="用户名" title="用户名">
            <template #default="{ row }">
              <div>
                {{ row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="110px">
            <template #default="{ row, rowIndex }">
              <div v-if="rowIndex == pindex" style="cursor: pointer">{{
                row.mobile
              }}</div>
              <div
                v-else
                style="cursor: pointer; color: #16a589"
                @click="phoneClickTable(rowIndex, row)"
                >{{ row.maskMobile }}</div
              >
              <!-- <div>{{ row.mobile }}</div> -->
            </template>
          </vxe-column>
          <vxe-column field="失败代码" title="失败代码">
            <template #default="{ row }">
              <div>
                {{ row.code }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template #default="{ row }">
              <el-tag
:disable-transitions="true" v-if="row.status == 0" type="info" effect="dark">
                待处理
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="row.status == 1" type="warning" effect="dark">
                处理中
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="row.status == 2" type="success" effect="dark">
                已成功
              </el-tag>
              <el-tag
:disable-transitions="true" v-if="row.status == 3" type="primary" effect="dark">
                已回执
              </el-tag>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template #default="{ row }">
              <div v-if="row.createTime">{{
                moment(row.createTime).format("YYYY-MM-DD HH:mm:ss")
              }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template  #default="{ row }">
              <el-button link style="color: #409eff" @click="edit(row)"
                >编 辑</el-button
              >
              <el-button link style="color: red" @click="cancel(row)"
                >删 除</el-button
              >
            </template>
          </vxe-column> -->
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import TableTem from "../../../publicComponents/TableTem.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import moment from "moment";
export default {
  components: {
    TableTem,
    Tooltip,
  },
  name: "ShuntSetting",
  data() {
    var numberRules = (rule, value, callback) => {
      if (!/^([0-9][0-9]{0,3}|100)$/.test(value)) {
        return callback(new Error("请输入0-100的数字"));
      } else {
        callback();
      }
    };
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      titleMap: "新增",
      isFirstEnter: false,
      pindex: -1,
      formInline: {
        userName: "",
        mobile: "",
        timeEnd: moment().format("YYYY-MM-DD 23:59:59"),
        timeStart: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        createTime: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD 23:59:59"),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        userName: "",
        mobile: "",
        timeEnd: moment().format("YYYY-MM-DD 23:59:59"),
        timeStart: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        createTime: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD 23:59:59"),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    };
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.pindex = -1;
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingdivert/sms/success/ratio/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    Reload() {
      this.$refs["formInline"].resetFields();
      this.formInline.timeStart = moment()
        .startOf("day")
        .format("YYYY-MM-DD 00:00:00");
      this.formInline.timeEnd = moment().format("YYYY-MM-DD 23:59:59");
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    handleTimeChange(val) {
      if (val) {
        this.formInline.timeStart = moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.formInline.timeEnd = moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      } else {
        this.formInline.timeStart = "";
        this.formInline.timeEnd = "";
      }
      Object.assign(this.tabelAlllist, this.formInline);
      // this.gettableLIst();
    },
    phoneClickTable(index, row) {
      this.pindex = index;
      window.api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.gettableLIst();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
};
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>