<template>
  <div>
    <div class="passageWay-title">
      <div style="display: flex; align-items: center; width: 650px;">
        <span
          style="
            display: inline-block;
            width: 68px;
            text-align: right;
            padding-right: 10px;
          "
          >时间</span
        >
        <el-radio-group
          v-model="flag"
          size="default"
          text-color="#fff"
          fill="#16a589"
          @change="handleChangeTimeOptions"
        >
          <el-radio-button value="1">今天</el-radio-button>
          <el-radio-button value="2">近一个月</el-radio-button>
        </el-radio-group>
        <!-- <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin> -->
        <el-date-picker
          :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName"
          v-model="datePluginValue"
          type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          range-separator="至"
          @change="timeClick"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
      <!-- 查询框开始 -->
      <el-form
        :inline="true"
        :model="form"
        label-width="85px"
        class="demo-form-inline"
        ref="sendjl"
      >
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form.clientName" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="通道ID" prop="channelId">
          <el-input
            v-model="form.channelId"
            class="input-w"
            onInput="value=value.replace(/[^\d]/g,'')"
            maxlength="10"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form.mobile" class="input-w"></el-input>
        </el-form-item>
        <!-- <el-form-item label="短信签名"  prop='signature'>
                            <el-input v-model="form.signature" class="input-w"></el-input>
                        </el-form-item> -->
        <el-form-item label="短信内容" prop="content">
          <el-input v-model="form.content" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="msgId" prop="msgid">
          <el-input v-model="form.msgid" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="发送状态" prop="smsStatus">
          <el-select v-model="form.smsStatus" class="input-w">
            <el-option label="全部" value=""></el-option>
            <el-option label="成功" value="1"></el-option>
            <el-option label="失败" value="2"></el-option>
            <el-option label="待返回" value="3"></el-option>
            <!-- <el-option label="补发待返回" value="4"></el-option> -->
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="模板名称"  prop='temId'>
                            <el-input v-model="form.temId"  class="input-w"></el-input>
                        </el-form-item> -->
        <el-form-item label="原始状态码" prop="originalCode">
          <el-input v-model="form.originalCode" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="国家" prop="area">
          <el-input v-model="form.area" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-input v-model="form.source" class="input-w"></el-input>
        </el-form-item>
        <div>
          <el-button
            type="primary"
            plain
            @click.prevent="Query()"
            @keyup.enter="Query()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="reset('sendjl')"
            >重置</el-button
          >
        </div>
      </el-form>
      <!-- <div class="boderbottom">
                    
                        <el-button type="primary" plain  @click="export1()">导出</el-button>
                    </div> -->
      <!-- 查询框结束 -->
      <div class="passage-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="imsRMSrecord"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px;"
          :data="tableDataObj.tableData">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称" width="120px">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              <!-- <span>
                                        {{ scope.row.username}}
                                    </span> -->
              <div
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
              >
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="msgId" title="msgId" width="200px">
            <template v-slot="scope">
              {{ scope.row.msgid }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
              <CopyTemp :content="scope.row.msgid" />
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="120px">
            <template #default="{row, rowIndex}">
              <div v-if="rowIndex == pIndex">
                <span>{{ row.mobile }}</span>
              </div>
              <div
                v-else
                style="color: #16a589; cursor: pointer"
                @click="tableContent(row, rowIndex)"
              >
                <span>{{ row.maskMobile }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="短信内容" title="短信内容" width="500px">
            <template #default="scope">
              <!-- <Tooltip v-if="scope.row.content" :content="scope.row.content" className="multiline-text"
                                     widthpx="400px" effect="light">
                                </Tooltip> -->
              <!-- <span>{{ scope.row.content }}</span> -->
              <Tooltip
                v-if="scope.row.content"
                :content="scope.row.content"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="计费数" title="计费数" width="80px">
            <template v-slot="scope">
              <div>{{ scope.row.chargeNum }}</div>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="100px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间" width="100px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="发送状态" title="发送状态" width="90px">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 1" type="success" effect="dark">
                成功
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 2"
                type="danger"
                effect="dark"
              >
                失败
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="info"
                effect="dark"
              >
                待返回
              </el-tag>
              <!-- <span v-if="scope.row.status == 1">成功</span>
                                <span v-else-if="scope.row.status == 2">失败</span>
                                <span v-else-if="scope.row.status == 3">待返回</span> -->
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="90px">
            <template v-slot="scope">
              <div>{{ scope.row.originalCode }}</div>
            </template>
          </vxe-column>
          <vxe-column field="扩展号" title="扩展号" width="110px">
            <template v-slot="scope">
              <div>{{ scope.row.ext }}</div>
            </template>
          </vxe-column>
          <vxe-column field="发送通道号" title="发送通道号" width="90px">
            <template v-slot="scope">
              <div
                @click="save(scope.row.channelId)"
                style="color: rgb(22, 165, 137); cursor: pointer"
                >{{ scope.row.channelId }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="国家" title="国家" width="90px">
            <template v-slot="scope">
              <div>{{ scope.row.area }}</div>
            </template>
          </vxe-column>
          <vxe-column field="单价" title="单价" width="90px">
            <template v-slot="scope">
              <div>{{ scope.row.price }}</div>
            </template>
          </vxe-column>
          <vxe-column field="提交Ip" title="提交Ip" width="110px">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.ip"
                :content="scope.row.ip"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源" width="100px">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.source"
                :content="scope.row.source"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formData.currentPage"
            :page-size="formData.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <!-- 点手机号查询补发 -->
    <el-dialog
      title="补发详情"
      v-model="moreInfoDialog"
      width="810px"
      class="pro-style"
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="tableDataObj1.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj1.tableData"
        style="width: 100%"
      >
        <el-table-column label="发送类型" width="80px">
          <template v-slot="scope">
            <span v-if="scope.row.sendType == 1">自动补发</span>
            <span v-else-if="scope.row.sendType == 2">首次发送</span>
            <span v-else-if="scope.row.sendType == 3">手动补发</span>
            <span v-else-if="scope.row.sendType == 4">携号转网</span>
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="手机号" width="100px">
        </el-table-column>
        <el-table-column label="通道ID" width="70px">
          <template v-slot="scope">
            <span
              @click="save(scope.row.channelId)"
              style="color: rgb(22, 165, 137); cursor: pointer"
              >{{ scope.row.channelId }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80px">
        </el-table-column>
        <el-table-column prop="originalCode" label="原始状态码" width="150px">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="140px">
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="140px">
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 20px">
        <el-button type="primary" @click="moreInfoDialog = false"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
// import clip from '../../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  name: 'SendRecord',
  components: {
    DatePlugin,
    TableTem,
    ChannelView,
    UserLists,
    Tooltip,
    CopyTemp,
  },
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      userFlag: false,
      pIndex:-1,
      nameover: '',
      ChannelData: '', //传递通道值
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      idStr: '',
      moreInfoDialog: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: '',
      },
      flag: '1', //选择那一天
      Times: {
        beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      },
      datePluginValue: [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        // onPick: ({ maxDate, minDate }) => {
        //     this.pickerMinDate = minDate.getTime();
        //     if (maxDate) {
        //         this.pickerMinDate = ''
        //     }
        // },
        // disabledDate: (time) => {
        //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
        //         return false
        //     }
        //     if (this.pickerMinDate !=='') {
        //         const day7 = 6 * 24 * 3600 * 1000
        //         let maxTime = this.pickerMinDate + day7
        //         if (maxTime >  Date.now()) {
        //             maxTime =  Date.now()
        //         }
        //         const minTime = this.pickerMinDate - day7
        //         return time.getTime() < minTime || time.getTime() > maxTime
        //     }
        //     return time.getTime() > Date.now();
        // }
      },
      //    datePluginValue:''
      // },
      //发送查询的值
      form: {
        mobile: '',
        channelId: '',
        clientName: '',
        // signature:'',
        smsStatus: '',
        content: '',
        msgid: '',
        // temId:'',
        originalCode: '',
        area: '',
        source: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        channelId: '',
        clientName: '',
        // signature:'',
        smsStatus: '',
        content: '',
        msgid: '',
        // temId:'',
        originalCode: '',
        area: '',
        source: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      activeName2: 'first', //发送和回复选项卡
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        totalRow: 0,
        tableData: [],
      },
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    }
  },
  methods: {
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsReplyId)
      }
      this.selectId = selectId.join(',')
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // handleCopy(name,event){
    // clip(name, event)
    // },
    // 点击手机号
    tableContent(row, index) {
      this.pIndex = index;
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.decryptMobile,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile=res.data
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      )
      //  window.api.get(window.path.omcs + "smsMessage/decrypt?smsInfoId="+val,{},res=>{
      //     if(res.code==200){
      //         this.tableDataObj.tableData[index].mobile=res.data[0]
      //     }else{
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }
      // })
    },
    //获取 发送记录数据
    getChallDate() {
      this.pIndex = -1;
      let aa = {}
      Object.assign(aa, this.formData)
      aa.flag = this.flag
      aa.sendBeginTime = this.Times.beginTime
      aa.sendEndTime = this.Times.endTime
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'statistics/imsmessage/page',
        aa,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.totalRow = res.data.total
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = []
          }
        }
      )
    },
    //导出 （发送记录）
    export1() {
      let aa = {}
      Object.assign(aa, this.formData)
      aa.flag = this.flag
      aa.sendBeginTime = this.Times.beginTime
      aa.sendEndTime = this.Times.endTime
      aa.isDownload = 1
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'statistics/imsmessage/page',
          aa,
          '发送记录报表.xlsx'
        )
      }
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    timeClick(val) {
      if (val) {
        this.Times.beginTime = this.moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = this.moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
        this.flag = '3'
      } else {
        this.Times.beginTime = ""
        this.Times.endTime = ""
        this.flag=''
      }
      
      // if(val){
      //     this.Times.beginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
      //     this.Times.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      //     this.flag='5'
      // }else{
      //     this.flag='1'
      // }
    },
    handleChangeTimeOptions: function () {
      // this.datePluginValue = ''
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    //发送查询
    Query() {
      Object.assign(this.formData, this.form)
      this.getChallDate()
    },
    //发送重置
    reset() {
      if (this.flag == '1') {
        // console.log(11);
        this.$refs.sendjl.resetFields()
        this.Times.beginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
        Object.assign(this.formData, this.form)
        this.getChallDate()
      } else {
        this.flag = '1'
        this.$refs.sendjl.resetFields()
        Object.assign(this.formData, this.form)
      }
      // this.flag='1'
      // this.$refs.sendjl.resetFields();
      // Object.assign(this.formData,this.form);
    },
    handleSizeChange(size) {
      this.formData.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formData.currentPage = currentPage
    },
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听时间范围
    flag: function (val) {
      // if(val != '5'){
      //     this.Times.beginTime = '';
      //     this.Times.endTime = '';
      //     this.datePluginValue='';
      // }
      if (val == '1') {
        this.Times.beginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
      } else if (val == '2') {
        this.Times.beginTime = moment()
          .subtract(30, 'days')
          .format('YYYY-MM-DD 00:00:00')
        this.Times.endTime = moment().format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().subtract(30, 'days').format('YYYY-MM-DD 00:00:00'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ]
      }
      this.getChallDate()
    },
    //监听时间是否改变
    Times: {
      handler(val) {
        if (val) {
          this.getChallDate()
        }
      },
      deep: true,
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
    formData: {
      handler(val) {
        this.getChallDate()
      },
      deep: true,
    },
    moreInfoDialog(val) {
      if (val == false) {
        this.tableDataObj1.loading2 = false
        this.tableDataObj1.tableData = []
      }
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
