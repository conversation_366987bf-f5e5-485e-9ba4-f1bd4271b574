# 📋 通道重启计划功能说明

## 🎯 功能概述

在PassageWay目录下新增了通道重启计划管理功能，提供通道重启计划的增删改查操作。页面排版风格参考了signatureFailurcode.vue的设计。

## ✨ 功能特性

### 📊 主要功能
- **列表查询**: 支持按通道ID、开始时间、结束时间筛选
- **新增计划**: 创建新的通道重启计划
- **编辑计划**: 修改现有的通道重启计划
- **删除计划**: 删除不需要的通道重启计划
- **分页显示**: 支持分页浏览和每页数量调整

### 🔧 数据字段
- **ID**: 计划唯一标识
- **通道ID**: 需要重启的通道标识
- **开始时间**: 计划开始执行时间
- **结束时间**: 计划结束时间
- **下次执行时间**: 下一次执行的时间
- **连接数**: 当前连接数
- **最大连接数**: 允许的最大连接数

## 🔧 技术实现

### 1. API接口

#### **列表查询**
- **接口**: `POST /operatingchannelrestarttask/page`
- **参数**: 
```javascript
{
  current: 1,           // 当前页
  size: 10,            // 每页数量
  channelId: "",       // 通道ID（可选）
  beginTime: "",       // 开始时间（可选）
  endTime: ""          // 结束时间（可选）
}
```

#### **新增计划**
- **接口**: `POST /operatingchannelrestarttask`
- **数据结构**:
```javascript
{
  "beginTime": "string",      // 开始时间
  "channelId": "string",      // 通道ID
  "connectNum": 0,            // 连接数
  "endTime": "string",        // 结束时间
  "id": 0,                    // ID（新增时为0）
  "maxNum": 0,                // 最大连接数
  "nextTime": "string"        // 下次执行时间
}
```

#### **修改计划**
- **接口**: `PUT /operatingchannelrestarttask`
- **数据结构**: 同新增接口，但需要提供有效的ID

#### **删除计划**
- **接口**: `DELETE /operatingchannelrestarttask/{id}`
- **参数**: 路径参数中的计划ID

### 2. 组件结构

#### **文件位置**
```
src/components/page/OperationMag/PassageWay/ChannelRestartPlan/
└── ChannelRestartPlan.vue
```

#### **路由配置**
```javascript
{
  path: '/ChannelRestartPlan',
  meta: { title: ' 通道重启计划', keepAlive: true, isBack: false },
  component: () => import('@/components/page/OperationMag/PassageWay/ChannelRestartPlan/ChannelRestartPlan.vue'),
}
```

### 3. 核心代码结构

#### **数据定义**
```javascript
// 搜索表单
const searchForm = reactive({
    channelId: '',
    beginTime: '',
    endTime: ''
});

// 表单数据
const form = reactive({
    id: 0,
    channelId: '',
    beginTime: '',
    endTime: '',
    nextTime: '',
    connectNum: 0,
    maxNum: 0
});
```

#### **表单验证规则**
```javascript
const rules = {
    channelId: [
        { required: true, message: '请输入通道ID', trigger: 'blur' }
    ],
    beginTime: [
        { required: true, message: '请选择开始时间', trigger: 'change' }
    ],
    endTime: [
        { required: true, message: '请选择结束时间', trigger: 'change' }
    ],
    nextTime: [
        { required: true, message: '请选择下次执行时间', trigger: 'change' }
    ],
    connectNum: [
        { required: true, message: '请输入连接数', trigger: 'blur' }
    ],
    maxNum: [
        { required: true, message: '请输入最大连接数', trigger: 'blur' }
    ]
};
```

#### **主要方法**
```javascript
// 获取列表数据
const getRestartPlanList = async () => {
    // 调用列表API
};

// 新增/编辑提交
const submitForm = async () => {
    // 根据isEdit判断调用新增或编辑API
};

// 删除操作
const handleDelete = async (row) => {
    // 确认后调用删除API
};
```

## 🎨 UI设计

### 页面布局
1. **搜索区域**: 通道ID、开始时间、结束时间筛选
2. **工具栏**: 添加按钮
3. **数据表格**: 显示计划列表，包含操作列
4. **分页组件**: 支持页码和每页数量调整
5. **弹窗表单**: 新增/编辑计划的表单

### 表格列配置
- ID (80px)
- 通道ID (120px)
- 开始时间 (160px)
- 结束时间 (160px)
- 下次执行时间 (160px)
- 连接数 (100px)
- 最大连接数 (120px)
- 操作 (200px) - 编辑、删除按钮

### 表单字段
- **通道ID**: 文本输入框
- **开始时间**: 日期时间选择器
- **结束时间**: 日期时间选择器
- **下次执行时间**: 日期时间选择器
- **连接数**: 数字输入框
- **最大连接数**: 数字输入框

## 🔍 功能特点

### 1. 搜索功能
- 支持按通道ID模糊搜索
- 支持按时间范围筛选
- 重置功能清空所有搜索条件

### 2. 表格功能
- 响应式列宽调整
- 悬停高亮效果
- 自定义存储配置

### 3. 表单功能
- 完整的表单验证
- 日期时间选择器
- 数字输入限制（最小值0）

### 4. 交互体验
- 加载状态提示
- 操作成功/失败消息提示
- 删除确认对话框
- 表单重置功能

## 📊 数据流程

### 1. 页面加载
```
页面初始化 → 连接表格和工具栏 → 获取列表数据
```

### 2. 搜索流程
```
输入搜索条件 → 点击查询 → 重置页码 → 调用列表API → 更新表格数据
```

### 3. 新增流程
```
点击添加 → 打开弹窗 → 填写表单 → 验证通过 → 调用新增API → 刷新列表
```

### 4. 编辑流程
```
点击编辑 → 填充表单数据 → 打开弹窗 → 修改数据 → 调用编辑API → 刷新列表
```

### 5. 删除流程
```
点击删除 → 确认对话框 → 确认删除 → 调用删除API → 刷新列表
```

## 🚀 使用说明

### 1. 访问页面
通过路由 `/ChannelRestartPlan` 访问通道重启计划管理页面

### 2. 查看计划列表
- 页面加载后自动显示所有计划
- 可以使用搜索条件筛选特定计划

### 3. 添加新计划
1. 点击"添加"按钮
2. 在弹窗中填写计划信息
3. 点击"确定"保存

### 4. 编辑计划
1. 点击表格中的"编辑"按钮
2. 在弹窗中修改计划信息
3. 点击"确定"保存修改

### 5. 删除计划
1. 点击表格中的"删除"按钮
2. 在确认对话框中点击"确定"

## 📋 注意事项

1. **时间格式**: 所有时间字段使用 `YYYY-MM-DD HH:mm:ss` 格式
2. **时间回显**: 编辑时使用moment.js格式化时间字段，确保正确回显
3. **数字限制**: 连接数和最大连接数不能为负数
4. **必填验证**: 所有字段都是必填项
5. **API依赖**: 需要确保后端API接口正常可用
6. **权限控制**: 需要根据实际需求添加相应的权限控制

## 🔧 时间回显修复

### 问题描述
编辑通道重启计划时，时间字段无法正确回显到日期选择器中。

### 解决方案
在编辑方法中使用moment.js格式化时间字段：

```javascript
// 编辑
const handleEdit = (row) => {
    isEdit.value = true;

    // 清除之前的表单验证状态
    if (formRef.value) {
        formRef.value.clearValidate();
    }

    // 填充表单数据
    form.id = row.id;
    form.channelId = row.channelId;
    // 格式化时间字段，确保正确回显
    form.beginTime = row.beginTime ? moment(row.beginTime).format('YYYY-MM-DD HH:mm:ss') : '';
    form.endTime = row.endTime ? moment(row.endTime).format('YYYY-MM-DD HH:mm:ss') : '';
    form.nextTime = row.nextTime ? moment(row.nextTime).format('YYYY-MM-DD HH:mm:ss') : '';
    form.connectNum = row.connectNum || 0;
    form.maxNum = row.maxNum || 0;

    // 打开对话框
    dialogVisible.value = true;
};
```

### 关键修复点
1. **时间格式化**: 使用`moment(time).format('YYYY-MM-DD HH:mm:ss')`确保时间格式正确
2. **空值处理**: 使用三元运算符处理空时间值
3. **表单验证清除**: 在编辑前清除之前的验证状态
4. **数字字段默认值**: 使用`||`运算符设置数字字段的默认值

## 🔧 扩展建议

1. **批量操作**: 可以添加批量删除功能
2. **状态管理**: 可以添加计划状态字段（启用/禁用）
3. **执行日志**: 可以添加计划执行历史记录
4. **定时任务**: 可以集成定时任务调度功能
5. **监控告警**: 可以添加计划执行失败的告警机制

---

**注意**: 此功能已完全集成到现有系统中，遵循了项目的代码规范和设计模式。
