// 安全检查相关路由
export default [
  {
    path: '/SensitiveWordMag',
    meta: { title: '敏感词设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/SensitiveWords/SensitiveWordMag/SensitiveWordMag.vue'),
  },
  {
    path: '/SensitiveWordsTag',
    meta: { title: '敏感词标签库', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/SensitiveWords/SensitiveWordsTag/SensitiveWordsTag.vue'),
  },
  {
    path: '/IPWhiteList',
    meta: { title: 'IP白名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/whiteList/IPWhiteList.vue'),
  },
  {
    path: '/AccountWhiteList',
    meta: { title: '手机号白名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/whiteList/PhoneWhiteList.vue'),
  },
  {
    path: '/userWhitelist',
    meta: { title: '账号白名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/whiteList/userWhitelist.vue'),
  },
  {
    path: '/BlackLIst',
    meta: { title: '系统黑名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/ListManagement/BlackLIst/BlackLIst.vue'),
  },
  {
    path: '/UserBlackLIst',
    meta: { title: '账号黑名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/ListManagement/UserBlackLIst/UserBlackLIst.vue'),
  },
  {
    path: '/MarketBlackLIst',
    meta: { title: '营销黑名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/ListManagement/MarketBlackLIst/MarketBlackLIst.vue'),
  },
  {
    path: '/signatureBlack',
    meta: { title: '签名黑名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/ListManagement/signatureBlack/signatureBlack.vue'),
  },
  {
    path: '/BlockList',
    meta: { title: '屏蔽签名', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SecurityCheck/ListManagement/BlockList/BlockList.vue'),
  },
  {
    path: '/templateSkipChecks',
    component: () => import('@/components/page/SecurityCheck/SkipChecks/templateSkipChecks.vue'),
    meta: { title: '模板参数不检测授权证明', keepAlive: true, isBack: false },
  },
  {
    path: '/crawlerLibrary',
    component: () => import('@/components/page/SecurityCheck/Crawler/crawlerLibrary.vue'),
    meta: { title: '爬虫IP管理', keepAlive: true, isBack: false },
  },
  {
    path: '/numbPortability',
    component: () => import('@/components/page/SecurityCheck/NumberPortability/index.vue'),
    meta: { title: '携号转网库', keepAlive: true, isBack: false },
  },
  {
    path: '/IPblacklist',
    meta: { title: 'IP黑名单', keepAlive: true, isBack: false },
    component: () => import('@/components/page/SystemSettings/IPblacklist.vue'),
  },
] 