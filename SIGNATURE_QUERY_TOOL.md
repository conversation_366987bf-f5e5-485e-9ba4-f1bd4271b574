# 🔍 签名查询工具组件

## 🎯 功能概述

基于SignatureMag.vue创建的快速签名查询工具，封装在header.vue中作为全局工具，提供现代化卡片风格的签名信息查询功能，支持分页和详情查看。

## ✨ 核心功能特性

### 📋 主要功能
- **快速查询**: 支持用户名、签名、签名属性等多维度查询
- **表格展示**: 现代化表格展示签名基本信息
- **详情查看**: 弹窗展示签名详细信息和实名状态
- **分页支持**: 完整的分页功能，支持页面大小调整
- **实名状态**: 三大运营商实名状态可视化展示
- **资质图片**: 支持资质图片预览和轮播

### 🎨 UI设计特色
- **现代化卡片风格**: 采用Element Plus卡片设计
- **响应式布局**: 支持不同屏幕尺寸适配
- **运营商图标**: 移动、联通、电信运营商专属图标和颜色
- **状态标签**: 直观的状态标签展示
- **图片预览**: 支持图片点击预览和轮播

## 🔧 技术实现

### 1. 组件结构

#### **主要文件**
- `src/components/common/SignatureQuery.vue` - 签名查询主组件
- `src/components/common/HeaderTools.vue` - 工具栏组件（新增签名查询按钮）
- `src/components/common/header.vue` - 头部组件（集成签名查询工具）

### 2. 接口使用

#### **查询接口**
```javascript
// POST 查询签名列表
window.api.post(
  window.path.omcs + 'consumerSignature/page',
  {
    current: 1,
    size: 20,
    consumerName: '用户名',
    signature: '签名',
    signatureAttr: '签名属性'
  },
  callback
)
```

#### **详情接口**
```javascript
// GET 获取签名详情和实名信息
window.api.get(
  window.path.omcs + "consumerSignature/realName?userId=" + userId + "&signatureId=" + signatureId,
  {},
  callback
)
```

### 3. 核心组件实现

#### **查询表单**
```vue
<el-form :inline="true" :model="queryForm" class="demo-form-inline">
  <el-form-item label="用户名">
    <el-input v-model="queryForm.consumerName" placeholder="请输入用户名" clearable />
  </el-form-item>
  <el-form-item label="签名">
    <el-input v-model="queryForm.signature" placeholder="请输入签名" clearable />
  </el-form-item>
  <el-form-item label="签名属性">
    <el-select v-model="queryForm.signatureAttr" placeholder="请选择" clearable>
      <el-option label="全部" value="0" />
      <el-option label="报备签名" value="1" />
      <el-option label="自定义签名" value="2" />
    </el-select>
  </el-form-item>
</el-form>
```

#### **数据表格**
```vue
<el-table
  v-loading="loading"
  :data="tableData"
  border
  stripe
  style="width: 100%"
  max-height="400"
  @row-click="handleRowClick"
>
  <el-table-column prop="consumerName" label="用户名称" width="140" />
  <el-table-column prop="signature" label="签名" width="150" />
  <el-table-column prop="contentExample" label="短信示例" width="200" />
  <el-table-column prop="serviceType" label="短信类型" width="100" />
  <el-table-column label="实名状态" width="300" />
  <el-table-column prop="createTime" label="创建时间" width="160" />
  <el-table-column label="操作" width="100" fixed="right" />
</el-table>
```

#### **分页组件**
```vue
<el-pagination
  v-model:current-page="pagination.current"
  v-model:page-size="pagination.size"
  :page-sizes="[10, 20, 50, 100]"
  :total="pagination.total"
  layout="total, sizes, prev, pager, next, jumper"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

### 4. 详情弹窗设计

#### **基本信息展示**
```vue
<el-descriptions :column="2" border>
  <el-descriptions-item label="签名">{{ detailData.signature }}</el-descriptions-item>
  <el-descriptions-item label="用户名称">{{ detailData.consumerName }}</el-descriptions-item>
  <el-descriptions-item label="短信示例">{{ detailData.contentExample }}</el-descriptions-item>
  <el-descriptions-item label="短信类型">
    <el-tag :type="getServiceTypeTag(detailData.serviceType).type">
      {{ getServiceTypeTag(detailData.serviceType).text }}
    </el-tag>
  </el-descriptions-item>
</el-descriptions>
```

#### **实名状态卡片**
```vue
<el-row :gutter="20">
  <el-col :span="8">
    <el-card class="operator-card yd-card">
      <template #header>
        <div class="card-header">
          <i class="iconfont icon-yidong"></i>
          <span>移动</span>
        </div>
      </template>
      <div class="status-content">
        <el-tag :type="detailData.ydRealNameStatus == 1 ? 'success' : 'info'" size="large">
          {{ detailData.ydRealNameStatus == 1 ? '已实名' : '未实名' }}
        </el-tag>
      </div>
    </el-card>
  </el-col>
  <!-- 联通、电信卡片类似 -->
</el-row>
```

## 🎨 样式设计

### 1. 运营商颜色主题

#### **运营商图标颜色**
```css
.operator-icon {
  &.yd { color: #409eff; } /* 移动 - 蓝色 */
  &.lt { color: #f56c6c; } /* 联通 - 红色 */
  &.dx { color: #e6a23c; } /* 电信 - 橙色 */
}
```

#### **卡片主题色**
```css
.yd-card .card-header i { color: #409eff; }
.lt-card .card-header i { color: #f56c6c; }
.dx-card .card-header i { color: #e6a23c; }
```

### 2. 现代化卡片设计

#### **查询表单样式**
```css
.query-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}
```

#### **运营商卡片样式**
```css
.operator-card {
  height: 140px;
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
  }
  
  .status-content {
    text-align: center;
  }
}
```

### 3. 响应式设计

#### **移动端适配**
```css
@media (max-width: 768px) {
  .query-form .el-form--inline .el-form-item {
    display: block;
    margin-bottom: 15px;
  }
  
  .realname-status {
    flex-direction: column;
    gap: 8px;
  }
}
```

## 🔄 功能流程

### 1. 查询流程
```
用户点击签名查询图标 → 打开查询弹窗 → 输入查询条件 → 
点击查询按钮 → 调用API接口 → 展示查询结果 → 支持分页浏览
```

### 2. 详情查看流程
```
点击表格行详情按钮 → 调用详情API → 获取实名信息 → 
展示详情弹窗 → 显示基本信息、实名状态、资质图片
```

### 3. 分页操作流程
```
用户切换页码/页面大小 → 更新分页参数 → 重新调用查询API → 
更新表格数据 → 更新分页组件状态
```

## 📊 数据结构

### 1. 查询参数
```javascript
queryForm: {
  consumerName: '',    // 用户名
  signature: '',       // 签名
  signatureAttr: ''    // 签名属性：0-全部，1-报备签名，2-自定义签名
}
```

### 2. 分页参数
```javascript
pagination: {
  current: 1,          // 当前页码
  size: 20,           // 页面大小
  total: 0            // 总记录数
}
```

### 3. 表格数据字段
```javascript
tableData: [
  {
    consumerName: '',        // 用户名称
    signature: '',           // 签名
    contentExample: '',      // 短信示例
    serviceType: 1,          // 短信类型：1-验证码，2-通知，3-营销
    ydRealNameStatus: 1,     // 移动实名状态：0-未实名，1-已实名
    ltRealNameStatus: 1,     // 联通实名状态：0-未实名，1-已实名
    dxRealNameStatus: 1,     // 电信实名状态：0-未实名，1-已实名
    createTime: '',          // 创建时间
    userId: '',              // 用户ID
    signatureId: ''          // 签名ID
  }
]
```

### 4. 详情数据字段
```javascript
detailData: {
  signature: '',           // 签名
  consumerName: '',        // 用户名称
  contentExample: '',      // 短信示例
  serviceType: 1,          // 短信类型
  signatureAttr: 1,        // 签名属性
  createTime: '',          // 创建时间
  ydRealNameStatus: 1,     // 移动实名状态
  ydAvalibleType: '',      // 移动可用类型
  ltRealNameStatus: 1,     // 联通实名状态
  ltAvalibleType: '',      // 联通可用类型
  dxRealNameStatus: 1,     // 电信实名状态
  dxAvalibleType: '',      // 电信可用类型
  imgUrl: ''               // 资质图片URL（逗号分隔）
}
```

## 🎯 使用场景

### 1. 快速查询签名
```
运营人员需要快速查询某个用户的签名信息 → 
点击头部签名查询图标 → 输入用户名 → 查看结果
```

### 2. 检查实名状态
```
客服需要查看签名的实名状态 → 
搜索签名 → 点击详情 → 查看三大运营商实名状态
```

### 3. 查看资质材料
```
审核人员需要查看签名资质 → 
查询签名 → 查看详情 → 预览资质图片
```

## 🔧 集成方式

### 1. HeaderTools组件集成
```vue
<!-- 在HeaderTools.vue中添加签名查询按钮 -->
<div class="btn-bell" @click="$emit('show-signature')">
  <el-tooltip effect="dark" content="签名查询" placement="bottom">
    <i class="iconfont icon-qianming" style="color: #fff; font-size: 26px !important;"></i>
  </el-tooltip>
</div>
```

### 2. Header组件集成
```vue
<!-- 在header.vue中集成SignatureQuery组件 -->
<HeaderTools @show-signature="signatureQueryShow = true" />
<SignatureQuery v-model:visible="signatureQueryShow" />
```

### 3. 数据状态管理
```javascript
data() {
  return {
    signatureQueryShow: false, // 签名查询弹窗显示状态
  }
}
```

## 📋 功能特色

### 1. 全局可访问
- 集成在header.vue中，任何页面都可以快速访问
- 独立的弹窗设计，不影响当前页面操作

### 2. 现代化设计
- 采用Element Plus最新设计规范
- 卡片式布局，视觉层次清晰
- 运营商专属颜色和图标

### 3. 完整功能
- 支持多条件查询
- 完整的分页功能
- 详细的信息展示
- 图片预览功能

### 4. 用户体验
- 响应式设计，适配各种屏幕
- 加载状态提示
- 错误处理和用户反馈
- 直观的状态展示

## 🚀 扩展建议

### 1. 功能扩展
- 添加导出功能
- 支持批量操作
- 添加收藏/常用签名功能
- 集成签名使用统计

### 2. 性能优化
- 添加查询结果缓存
- 实现虚拟滚动（大数据量时）
- 图片懒加载优化

### 3. 用户体验
- 添加搜索历史记录
- 支持快捷键操作
- 添加签名状态变更通知

---

**功能完成**: 签名查询工具已成功集成到header.vue中，提供了现代化的签名信息查询功能，支持完整的查询、详情查看和分页操作。
