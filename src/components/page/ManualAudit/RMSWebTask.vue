<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            label-width="80px"
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item
              label="发送类型"
              prop="timingSend"
            >
              <el-select
                v-model="formInline.timingSend"
                clearable
                placeholder="请选择类型"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="立即发送" value="0"></el-option>
                <el-option label="定时发送" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="formInline.username"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="消息id" prop="msgid">
              <el-input
                v-model="formInline.msgid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="formInline.title"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="来源" prop="source">
              <el-input v-model="formInline.source" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" label-width="80px" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="true"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>

            <div>
              <el-button type="primary" plain style="" @click.prevent="Query()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>

        <div class="Mail-table" style="padding-bottom: 40px;">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handelSelection"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
              <el-button
                type="danger"
                style="margin-right: 10px"
                @click="batchDeletion"
                v-if="selectId.length"
                >批量取消</el-button
              >
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="RMSWebTask"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handelSelection"
            @checkbox-change="handelSelection">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="用户名" title="用户名" width="140">
              <template v-slot="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
                <div>
                  {{ scope.row.username }}
                </div>
              </template>
            </vxe-column>
            <vxe-column width="140" field="发送ID" title="发送ID">
              <template v-slot="scope">
                <div>{{ scope.row.id }}</div>
              </template>
            </vxe-column>
            <vxe-column field="消息ID" title="消息ID" width="200">
              <template v-slot="scope">
                <div>
                  {{ scope.row.msgid }}
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                  <CopyTemp :content="scope.row.msgid" />
                </div>
              </template>
            </vxe-column>

            <vxe-column field="任务名称" title="任务名称" width="140">
              <template v-slot="scope">
                <div>{{ scope.row.taskName }}</div>
              </template>
            </vxe-column>
            <vxe-column field="title" title="title" width="140">
              <template v-slot="scope">
                <div
                  style="color: #409eff; cursor: pointer"
                  @click="View(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.title"
                    :content="scope.row.title"
                    className="wrapper-text"
                    effect="light"
                  ></Tooltip>
                </div>

                <!-- <span style="cursor: pointer;color: #16A589;" @click="View(scope.row)"><i
                                            class="el-icon-picture"></i>预览</span> -->
              </template>
            </vxe-column>
            <vxe-column field="发送类型" title="发送类型" width="140">
              <template v-slot="scope">
                <div>
                  <span v-if="scope.row.timingSend == 0">立即发送</span>
                  <span v-else-if="scope.row.timingSend == 1">定时发送</span>
                </div>   
              </template>
            </vxe-column>
            <!-- <vxe-column field="" title="提交时间" >
                                <template #default="scope">
                                    <span >{{ scope.row.createTime }}</span>
                                </template>
                            </vxe-column> -->

            <vxe-column field="文件名/手机号" title="文件名/手机号" width="140">
              <template v-slot="scope">
                <div
                  v-if="scope.row.filePath"
                  style="cursor: pointer; color: #16a589"
                  @click="download(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.fileOriginalName"
                    :content="scope.row.fileOriginalName"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- {{ scope.row.fileOriginalName }} -->
                </div>
                <div v-else :title="scope.row.mobile">
                  <Tooltip
                    v-if="scope.row.mobile"
                    :content="scope.row.mobile"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- {{ scope.row.mobile }} -->
                </div>
              </template>
            </vxe-column>
            <vxe-column field="发送时间" title="发送时间" width="170">
              <template v-slot="scope">
                <div>{{ scope.row.sendTime }}</div>
              </template>
            </vxe-column>
            <vxe-column field="提交号码数（总行数）" title="提交号码数（总行数）" width="150">
              <template v-slot="scope">
                <div>{{ scope.row.totalNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="有效号码（有效行数）" title="有效号码（有效行数）" width="150">
              <template v-slot="scope">
                <div>{{ scope.row.effectiveNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="无效号码（无效行）" title="无效号码（无效行）" width="150">
              <template v-slot="scope">
                <div>{{ scope.row.invalidNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="发送状态" title="发送状态" width="140">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.status == 1"
                  type="warning"
                  effect="dark"
                  
                >
                  处理中
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 2"
                  type="success"
                  effect="dark"
                  
                >
                  已完成
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 3"
                  type="info"
                  effect="dark"
                  
                >
                  取消发送
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == 0"
                  type="info"
                  effect="dark"
                  
                >
                  待处理
                </el-tag>
                <el-tooltip
                  v-if="scope.row.status == -1"
                  class="item"
                  effect="dark"
                  :content="scope.row.reason"
                  placement="top"
                >
                  <el-tag
                  :disable-transitions="true"
                    type="danger"
                    effect="dark"
                    
                  >
                    处理异常
                  </el-tag>
                </el-tooltip>
                <!-- <span v-if="scope.row.status == 1">处理中</span>
                                    <span v-else-if="scope.row.status == 2">已完成</span>
                                    <span v-else-if="scope.row.status == 3">取消发送</span>
                                    <span v-else-if="scope.row.status == 0">待处理</span>
                                    <span style="color: red;" v-else-if="scope.row.status == -1">处理异常</span> -->
              </template>
            </vxe-column>
            <vxe-column field="审核状态" title="审核状态" width="140">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.auditStatus == 1"
                  type="info"
                  effect="dark"
                  
                >
                  未审核
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.auditStatus == 2"
                  type="success"
                  effect="dark"
                  
                >
                  审核通过
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.auditStatus == 3"
                  type="danger"
                  effect="dark"
                  
                >
                  审核未通过
                </el-tag>
                <!-- <span v-if="scope.row.auditStatus == 1">未审核</span>
                                    <span v-else-if="scope.row.auditStatus == 2">审核通过</span>
                                    <span v-else-if="scope.row.auditStatus == 3">审核未通过</span> -->
              </template>
            </vxe-column>
            <vxe-column field="来源" title="来源" width="140">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.source"
                  :content="scope.row.source"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
                <!-- <span>{{ scope.row.source }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="140" fixed="right">
              <template v-slot="scope">
                <el-button
                  link
                  v-if="scope.row.status == 0"
                  style="color: red; margin-left: 0px"
                  @click="cancel(scope.row)"
                  ><el-icon><CircleCloseFilled /></el-icon> 取消</el-button
                >
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

            <div class="paginationBox">
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              >
              </el-pagination>
            </div>

            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in viewData"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png' ||
                    item.media == 'jpeg'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                ></audio>
                <div style="white-space: pre-line" v-html="item.txt"></div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 编辑时间   -->
    <el-dialog title="编辑定时时间" v-model="dialogVisibleTime" width="22%">
      <div>
        <date-plugin
          class="Mail-search-date"
          :datePluginValueList="datePluginValueList"
          @handledatepluginVal="handledatepluginVal"
          style="width: 364px"
        ></date-plugin>
      </div>
      <div
        class="sms-seconnd-steps-btns"
        style="margin-top: 15px; padding-right: 10px"
      >
        <el-button
          type="primary"
          @click="determine()"
          style="padding: 10px 20px"
          >确 定</el-button
        >
        <el-button @click="dialogVisibleTime = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    UserLists,
    Tooltip,
    CopyTemp
  },
  name: 'RMSWebTask',
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      name: 'RMSWebTask',
      userFlag: false,
      nameover: '',
      // 定时时间
      datePluginValueList: {
        //日期参数配置
        type: 'datetime',
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7
          },
        },
        defaultTime: '', //默认起始时刻
        datePluginValue: '',
      },
      sendTime: '',
      taskSmsId: '',
      dialogVisible: false,
      viewData: [], // 查看内容
      title: '',
      dialogVisibleTime: false,
      //复选框值
      selectId: '',
      // 搜索数据
      formInline: {
        timingSend: '',
        username: '',
        msgid: '',
        source: '',
        title: '',
        beginTime: moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ],
        flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        timingSend: '',
        username: '',
        msgid: '',
        source: '',
        title: '',
        beginTime: moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ],
        flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        'post',
        '确定取消定时发送？',
        window.path.omcs + 'consumertaskvideo/cancel',
        { ids: this.selectId },
        (res) => {
          this.InquireList()
        }
      )
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
      }
      this.selectId = selectId //批量操作选中id
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumertaskvideo/auditPage',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 预览
    View(val) {
      this.viewData = val.contents
      this.title = val.title
      this.dialogVisible = true
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        'post',
        '确定取消定时视频短信？',
        window.path.omcs + 'consumertaskvideo/cancel',
        { ids: [val.id] },
        (res) => {
          this.InquireList()
        }
      )
    },
    // 下载
    download(val) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          'M+': this.getMonth() + 1,
          'd+': this.getDate(),
          'h+': this.getHours(),
          'm+': this.getMinutes(),
          's+': this.getSeconds(),
          'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        }
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + '').substr(4 - RegExp.$1.length)
          )
        for (var i in args) {
          var n = args[i]
          if (new RegExp('(' + i + ')').test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
            )
        }
        return format
      }
      var that = this
      filedownload()
      function filedownload() {
        fetch(
          window.path.cpus +
            'v3/file/download?fileName=' +
            val.fileOriginalName +
            '&group=group1&path=' +
            val.filePath.slice(7),
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
            // body: JSON.stringify({
            //     batchNo: val.row.batchNo,
            // })
          }
        )
          .then((res) => res.blob())
          .then((data) => {
            let blobUrl = window.URL.createObjectURL(data)
            download(blobUrl)
          })
      }
      function download(blobUrl) {
        var a = document.createElement('a')
        a.style.display = 'none'
        a.download =
          '(' +
          new Date().format('YYYY-MM-DD HH:mm:ss') +
          ') ' +
          val.fileOriginalName
        a.href = blobUrl
        a.click()
      }
    },
    // 查询
    Query() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      ;(this.formInline.time = []), (this.formInline.beginTime = '')
      this.formInline.endTime = ''
      ;(this.formInline.time1 = []), (this.formInline.startTime = '')
      this.formInline.stopTime = ''
      this.formInline.source = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + ' 00:00:00'
        this.formInline.stopTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.startTime = ''
        this.formInline.stopTime = ''
      }
    },
    // 定时时间
    handledatepluginVal: function (val1, val2) {
      //日期
      this.sendTime = val1
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
    // handleCopy(name,event){
    //     clip(name, event)
    // },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.el-scrollbar__wrap {
  margin-bottom: 0px !important;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>
<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>