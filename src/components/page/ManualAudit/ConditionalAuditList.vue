<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名称" prop="username">
              <el-input
                v-model="formInline.username"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="内容" prop="content">
              <el-input
                v-model="formInline.content"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select
                v-model="formInline.auditStatus"
                clearable
                placeholder="请选择类型"
                class="input-w"
              >
                <el-option label="审核通过" value="2"></el-option>
                <el-option label="审核不通过" value="3"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审核时间" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="false"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click.prevent="Query()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
                   
                    </div> -->
        <div class="sensitive-fun">
          <!-- <span class="sensitive-list-header">条件审核列表</span><span style="font-size: 12px;font-weight: 500;"></span> -->
        </div>
        <div class="Mail-table" style="padding-bottom: 40px; margin-top: 10px">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="ConditionalAuditList"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData">

            <vxe-column width="120" field="审核用户名" title="审核用户名">
              <template #default="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.auditUsername"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.auditUsername}}
                                    </span>
                                </el-popover> -->
                <div>
                  {{ scope.row.auditUsername }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="审核内容" title="审核内容" min-width="500">
              <template #default="scope">
                <div>{{ scope.row.auditContent }}</div>
              </template>
            </vxe-column>
            <vxe-column field="审核数" title="审核数">
              <template #default="scope">
                <div>{{ scope.row.auditNum }}</div>
              </template>
            </vxe-column>
            <vxe-column field="审核类型" title="审核类型" width="110">
              <template #default="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.auditStatus == '2'"
                  effect="dark"
                  
                >
                  审核通过
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.auditStatus == '3'"
                  type="danger"
                  effect="dark"
                  
                >
                  审核不通过
                </el-tag>
                <!-- <span v-if="scope.row.auditStatus == 2">审核通过</span>
                                    <span v-else-if="scope.row.auditStatus == 3">审核不通过</span> -->
              </template>
            </vxe-column>
            <vxe-column field="执行状态" title="执行状态" width="90">
              <template #default="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.status == '0'"
                  type="info"
                  effect="dark"
                  
                >
                  待执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.status == '2'"
                  type="success"
                  effect="dark"
                  
                >
                  已完成
                </el-tag>
                <!-- <span v-if="scope.row.status == 0">待执行</span>
                                    <span v-else-if="scope.row.status == 2">已完成</span> -->
              </template>
            </vxe-column>
            <vxe-column field="创建人" title="创建人" width="140">
              <template #default="scope">
                <div>{{ scope.row.createName }}</div>
              </template>
            </vxe-column>
            <vxe-column field="创建时间" title="创建时间" width="100">
              <template #default="scope">
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
                </p>
                <!-- <span>{{ scope.row.createTime }}</span> -->
              </template>
            </vxe-column>
            <vxe-column field="完成时间" title="完成时间" width="100">
              <template #default="scope">
                <p v-if="scope.row.finishTime">
                  {{ $parseTime(scope.row.finishTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.finishTime">
                  {{ $parseTime(scope.row.finishTime, '{h}:{i}:{s}') }}
                </p>
                <!-- <span>{{ scope.row.finishTime }}</span> -->
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->
          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>
            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    UserLists
  },
  name: 'ConditionalAuditList',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      name: 'ConditionalAuditList',
      userFlag: false,
      nameover: '',
      //复选框值
      selectId: '',
      // 搜索数据
      formInline: {
        content: '',
        username: '',
        productId: '1',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        content: '',
        username: '',
        productId: '1',
        beginTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
      isFirstEnter: false,
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'servicebatchaudit/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    // 查询
    Query() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      ;(this.formInline.time = []), (this.formInline.beginTime = '')
      this.formInline.endTime = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // rouTz(val){
    //     console.log(val);
    // },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  // watch:{
  //     // 监听搜索/分页数据
  //     formInlines:{
  //         handler() {
  //             this.InquireList()
  //         },
  //         deep: true,
  //         immediate: true,
  //     },
  // },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
