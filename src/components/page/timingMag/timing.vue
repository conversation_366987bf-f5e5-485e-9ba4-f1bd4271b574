<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div class="Timing-matter">
        <p style="font-weight: bolder">温馨提醒：</p>
        <p>1. 定时短信在未发送前取消需至少提前十分钟；</p>
        <p>2.取消定时任务操作不可逆转，请确认好之后再进行处理。</p>
      </div>
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="100px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="msgId" prop="msgid">
            <el-input
              style="width: 200px"
              v-model="sensitiveCondition.msgid"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="短信内容"  prop="smsContent">
                            <el-input v-model="sensitiveCondition.smsContent" placeholder="" class="input-w"></el-input>
                        </el-form-item> -->
          <el-form-item label="提交状态" prop="smsContent">
            <el-select
              v-model="sensitiveCondition.timingStatus"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="未执行" value="1"> </el-option>
              <el-option label="已取消" value="3"> </el-option>
              <el-option label="执行完成" value="5"> </el-option>
              <el-option label="执行失败" value="6"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="自动审核状态" prop="matchSimilar">
            <el-select
              v-model="sensitiveCondition.matchSimilar"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="已审核" value="1"></el-option>
              <el-option label="未审核" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="消息类型" prop="smsType">
            <el-select
              v-model="sensitiveCondition.smsType"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="普通短信" value="1"></el-option>
              <el-option label="变量模板短信" value="2"></el-option>
              <el-option label="固定模板短信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提交时间" prop="subtime">
            <el-date-picker
              class="input-time"
              v-model="sensitiveCondition.subtime"
              value-format="YYYY-MM-DD"
              type="daterange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handeleDate1"
              :clearable="true"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="定时时间" prop="dsTime">
            <el-date-picker
              class="input-time"
              v-model="sensitiveCondition.dsTime"
              value-format="YYYY-MM-DD"
              type="daterange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handeleDate2"
              :clearable="true"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button
              type="danger"
              v-if="selectId.length != 0"
              @click="batchCancellation()"
              >批量取消</el-button
            >
            <el-button
              type="primary"
              v-if="selectId.length != 0"
              @click="handeExtract()"
              >提取模板</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="timingMag"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称" width="142">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.userName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                     {{ scope.row.userName}}
                                    </span>
                                </el-popover> -->
              <div
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
                >{{ scope.row.userName }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="msgid" title="msgid" width="200">
            <template v-slot="scope">
              <div>
                {{ scope.row.msgid }}
                <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                <CopyTemp :content="scope.row.msgid" />
              </div>
            </template>
          </vxe-column>
          <vxe-column field="提交时间" title="提交时间" width="160">
            <template v-slot="scope">
              <div>{{ scope.row.createTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="短信内容" title="短信内容" min-width="500">
            <template v-slot="scope">
              <div>{{ scope.row.signature }}</div>
              <div>{{ scope.row.content }}</div>
            </template>
          </vxe-column>
          <vxe-column
            field="提交数量"
            title="提交数量"
            width="90"
          >
            <template v-slot="scope">
              <div>{{ scope.row.number }}</div>
            </template>
          </vxe-column>
          <vxe-column
            field="定时时间"
            title="定时时间"
            width="140"
          >
            <template v-slot="scope">
              <div>{{ scope.row.sendTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="提交状态" title="提交状态" width="80">
            <template v-slot="scope">
              <div v-if="scope.row.timingStatus == '1'">未执行</div>
              <div v-else-if="scope.row.timingStatus == '3'">已取消</div>
              <div v-else-if="scope.row.timingStatus == '5'">执行完成</div>
              <div v-else-if="scope.row.timingStatus == '6'">执行失败</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="130" fixed="right">
            <template v-slot="scope">
              <!-- <el-button type="text"  @click="handleReject(scope.$index, scope.row)"><i class="el-icon-success"></i>&nbsp;报告</el-button> -->
              <el-button
                type="text"
                style="color: red"
                v-if="scope.row.timingStatus == '1'"
                @click="handleAdopt(scope.$index, scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;取消</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConditions.pagesize"
            :page-size="sensitiveConditions.pagesize"
            :page-sizes="[10, 50, 100, 200]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
    </div>
    <!-- 报告的弹出框 -->
    <el-dialog
      title="任务报告查看"
      v-model="sendMsgDialogVisible"
      width="860px"
    >
      <div
        style="
          display: inline-block;
          width: 49%;
          text-align: center;
          border-right: 1px solid #f7f7f7;
          margin-top: -20px;
        "
      >
        <PieChart
          id="pie2"
          width="390px"
          height="340px"
          :basicOption="basicOption1"
        ></PieChart>
      </div>
      <div
        style="
          display: inline-block;
          width: 49%;
          text-align: center;
          margin-top: -20px;
        "
      >
        <PieChart
          id="pie1"
          width="390px"
          height="340px"
          :basicOption="basicOption2"
        ></PieChart>
      </div>
      <span style="display: block; padding: 0 0 10px 0; color: #333">
        发送明细列表</span
      >
      <table-tem :tableDataObj="tableDataObj1"></table-tem>
      <span style="display: block; padding: 20px 0 10px 0; color: #333">
        回执失败代码分析列表</span
      >
      <table-tem :tableDataObj="tableDataObj2"></table-tem>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="sendMsgDialogVisible = false"
            >知道了</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 相似度 -->
    <SimilarTemlpate
      :dialogVisibles="dialogVisible"
      :tableDatas="extractObj.tableData"
      :similarTemplateLists="extractObj.similarTemplateList"
      @handleClose="handleCloses"
    >
    </SimilarTemlpate>
    <!-- <el-dialog
          title="相似度"
          v-model="dialogVisible"
          width="1000px"
          :before-close="handleClose"
        >
          <div style="display: flex">
            <div>
              <div style="margin: 16px 0" class="title">模板相似度列表</div>
              <el-table
                v-loading="extractObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="extractObj.tableData"
                style="width: 550px"
                max-height="350"
              >
                <el-table-column label="用户名" width="120">
                  <template #default="scope">
                    <span>{{ scope.row.username }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="模板内容" min-width="300">
                  <template #default="scope">
                    <div style="display: flex">
                      <div>
                        <span class="spanColor" v-html="scope.row.content"></span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="相似度">
                  <template #default="scope">
                    <span>{{ scope.row.percent }}%</span>
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin: 16px 0" class="title">已存在模板列表</div>
              <el-table
                v-loading="extractObj.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="extractObj.similarTemplateList"
                style="width: 550px"
                max-height="350"
              >
                <el-table-column label="用户名" width="120">
                  <template #default="scope">
                    <span>{{ scope.row.username }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="模板内容" min-width="300">
                  <template #default="scope">
                    <div style="display: flex">
                      <div>
                        <span class="spanColor" v-html="scope.row.content"></span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="相似度">
                  <template #default="scope">
                    <span>{{ scope.row.percent }}%</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div style="margin-left: 30px">
              <div style="margin: 16px 0" class="title">自定义相似度</div>
              <el-form
                :model="similarFormop"
                :rules="similaRrules"
                label-width="100px"
                ref="similarFormop"
                style="padding: 0 28px 0 20px"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input
                    style="width: 200px"
                    v-model="similarFormop.username"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="模板内容" prop="content">
                  <el-input
                    type="textarea"
                    style="height: 110px"
                    v-model="similarFormop.content"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="关键字" prop="keywords">
                  <el-input
                    style="width: 200px"
                    v-model="similarFormop.keywords"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
                <el-form-item label="审核状态" prop="auditStatus">
                  <el-select
                    v-model="similarFormop.auditStatus"
                    placeholder="请选择"
                  >
                    <el-option label="审核通过" value="1"> </el-option>
                    <el-option label="审核不通过" value="2"> </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="相似度" prop="percent">
                  <el-input
                    style="width: 200px"
                    placeholder="相似度为正整数，请输入正确数字"
                    v-model="similarFormop.percent"
                    autocomplete="off"
                    class="input-w"
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>
          </div>

          <template #footer>
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm('similarFormop')"
              >提交</el-button
            >
          </template>
        </el-dialog> -->
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import PieChart from '@/components/publicComponents/PieChart.vue' //饼图
import SimilarTemlpate from '@/components/publicComponents/similarTemlpate.vue' //相似度
// import clip from '../../../utils/clipboard'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import moment from 'moment'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    TableTem,
    PieChart,
    UserLists,
    SimilarTemlpate,
    CopyTemp
  },
  name: 'timing',
  data() {
    // var percent = (rule, value, callback) => {
    //   if (value != "") {
    //     let reg = /^[0-9]*[1-9][0-9]*$/;
    //     if (reg.test(value)) {
    //       if (value >= 50 && value <= 100) {
    //         callback();
    //       } else {
    //         callback(new Error("相似度在于50-100之间"));
    //       }
    //     } else {
    //       callback(new Error("相似度为正整数，请输入正确数字"));
    //     }
    //   } else {
    //     callback(new Error("相似度不能为空"));
    //   }
    // };
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      userFlag: false,
      dialogVisible: false,
      contentFlag: false,
      contentList: [],
      nameover: '',
      sensitiveCondition: {
        //查询条件的值
        username: '',
        smsContent: '',
        msgid: '',
        timingStatus: '1',
        dsTime: [
          moment().format('YYYY-MM-DD HH:mm:ss'),
          moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
        startTime: '', //提交开始时间
        stopTime: '', //提交结束时间
        beginTime: moment().format('YYYY-MM-DD HH:mm:ss'), //定时开始时间
        endTime: moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'), //定时结束时间
        matchSimilar: '',
        smsType: '',
        currentPage: 1,
        pageSize: 10,
      },
      sensitiveConditions: {
        //赋值查询条件的值
        username: '',
        smsContent: '',
        msgid: '',
        timingStatus: '1',
        dsTime: [
          moment().format('YYYY-MM-DD HH:mm:ss'),
          moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        ],
        startTime: '', //提交开始时间
        stopTime: '', //提交结束时间
        beginTime: moment().format('YYYY-MM-DD HH:mm:ss'), //定时开始时间
        endTime: moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'), //定时结束时间
        matchSimilar: '',
        smsType: '',
        currentPage: 1,
        pageSize: 10,
      },
      // similarFormop: {
      //   //相似度
      //   username: "",
      //   content: "",
      //   percent: "",
      //   keywords: "",
      //   auditStatus: "1",
      //   snapshot: "",
      // },
      // similaRrules: {
      //   username: [
      //     { required: true, message: "用户名不能为空", trigger: "change" },
      //   ],
      //   content: [
      //     { required: true, message: "内容不能为空", trigger: "change" },
      //   ],
      //   auditStatus: [
      //     { required: true, message: "请选择审核状态", trigger: "change" },
      //   ],
      //   percent: [{ required: true, validator: percent, trigger: "change" }],
      // },
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        tableData: [],
      },
      extractObj: {
        loading2: false,
        tableData: [],
        similarTemplateList: [],
      },
      selectId: '', //列表选中项的id
      timingStatus: [], //列表选中项的的状态
      sendTimess: [], //列表选中项的定时时间
      sendMsgDialogVisible: false, //弹出框
      tableDataObj1: {
        tableData: [],
        tableLabel: [
          {
            prop: 'mobileNumber',
            showName: '提交号码数',
            fixed: false,
          },
          {
            prop: 'mobileChanrgeNum',
            showName: '提交号码计费数',
            fixed: false,
          },
          {
            prop: 'successAmount',
            showName: '成功号计费数',
            fixed: false,
          },
          {
            prop: 'failureAmount',
            showName: '失败号计费数',
            width: 100,
            fixed: false,
          },
          {
            prop: 'waitNumber',
            showName: '待返回号码计费数',
            width: 120,
            fixed: false,
          },
        ],
        tableStyle: {
          //列表配置项
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '120', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      tableDataObj2: {
        tableData: [],
        tableLabel: [
          {
            //列表表头
            prop: 'failureCodeNoteName',
            showName: '失败原因',
            fixed: false,
          },
          {
            prop: 'codeNoteNum',
            showName: '数量',
            fixed: false,
          },
          {
            prop: 'codeNoteProportion',
            showName: '占比',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:240,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '120', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      basicOption1: {
        //发送明细图表
        data: [
          {
            value: 0,
            name: '成功',
          },
          {
            value: 0,
            name: '失败',
          },
          {
            value: 0,
            name: '待返回',
          },
        ],
        ledate: ['成功', '失败', '待返回'],
        bgColor: ['#8996E6', '#98D87D', '#FFD86E'],
        radius: '62%',
        title: {
          textStyle: {
            color: '#999',
            fontSize: 14,
          },
          text: '发送明细图表',
          x: 'right',
        },
      },
      basicOption2: {
        //回执失败代码分析图表
        data: [],
        ledate: [],
        bgColor: [
          '#8996E6',
          '#49A9EE',
          '#98D87D',
          '#FFD86E',
          '#F3857C',
          '#8996E6',
          '#49A9EE',
          '#98D87D',
          '#FFD86E',
          '#F3857C',
          '#8996E6',
          '#49A9EE',
          '#98D87D',
          '#FFD86E',
          '#F3857C',
        ],
        radius: '62%',
        title: {
          text: '回执失败代码分析图表',
          textStyle: {
            color: '#999',
            fontSize: 14,
          },
          x: 'right',
        },
      },
      msgid: '', //任务报告行的消息ID
    }
  },
  methods: {
    goBack() {
      //返回
      this.$router.go(-1)
    },
    getTableDtate() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.cpus + 'v3/consumertimingsms/page',
        this.sensitiveConditions,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.totalRow = res.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    sensitiveQuery() {
      //查询
      if(this.tableDataObj.loading2) return; //防止重复请求
      Object.assign(this.sensitiveConditions, this.sensitiveCondition)
      this.getTableDtate()
    },
    sensitiveReload(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.sensitiveCondition.username = ''
      this.sensitiveCondition.timingStatus = ''
      this.sensitiveCondition.smsType = ''
      this.sensitiveCondition.matchSimilar = ''
      this.sensitiveCondition.timingStatus = '1'
      this.sensitiveCondition.startTime = '' //提交开始时间
      this.sensitiveCondition.stopTime = '' //提交结束时间
      this.sensitiveCondition.beginTime = moment().format('YYYY-MM-DD HH:mm:ss') //定时开始时间
      this.sensitiveCondition.endTime = moment()
        .subtract(-7, 'days')
        .format('YYYY-MM-DD HH:mm:ss') //定时结束时间
      Object.assign(this.sensitiveConditions, this.sensitiveCondition)
      this.getTableDtate()
    },
    handleSizeChange(size) {
      this.sensitiveConditions.pageSize = size
      this.getTableDtate()
    },
    handleCurrentChange: function (currentPage) {
      this.sensitiveConditions.currentPage = currentPage
      this.getTableDtate()
    },
    // handleCopy(name,event){
    //   clip(name, event)
    // },
    handeleDate1(val) {
      if (val) {
        this.sensitiveCondition.startTime = val[0] //提交开始时间
        this.sensitiveCondition.stopTime = val[1] //提交结束时间
      } else {
        this.sensitiveCondition.startTime = '' //提交开始时间
        this.sensitiveCondition.stopTime = '' //提交结束时间
      }
    },
    handeleDate2(val) {
      if (val) {
        this.sensitiveCondition.beginTime = val[0] //定时开始时间
        this.sensitiveCondition.endTime = val[1] //定时结束时间
      } else {
        this.sensitiveCondition.beginTime = '' //定时开始时间
        this.sensitiveCondition.endTime = '' //定时结束时间
      }
    },
    //报告
    handleReject(index, row) {
      this.msgid = row.msgid
      this.getconsunerTaskData()
      this.sendMsgDialogVisible = true
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    //取消
    handleAdopt(index, row) {
      // let aa =  new Date(row.sendTime).getTime(); //定时时间
      // let bb =  new Date().getTime(); //当前时间
      // if((aa-bb)>600000) {
      this.$confirms.confirmation(
        'get',
        '此操作将取消该条发送数据, 是否继续?',
        window.path.cpus + 'v3/consumertimingsms/batchCancel/' + row.msgid,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
      // }else{
      //     this.$message({
      //         message: '该项的定时时间距离发送时间不到10分钟，不可取消！',
      //         type: 'warning'
      //     });
      // }
    },
    //批量取消
    batchCancellation() {
      let aas = true
      let bbs = true
      for (let i = 0; i < this.timingStatus.length; i++) {
        if (this.timingStatus[i] != 1) {
          aas = false
          break
        }
        let cc = new Date().getTime()
        let dd = new Date(this.sendTimess[i]).getTime()
        if (dd - cc < 600000) {
          bbs = false
          break
        }
      }
      if (aas == true) {
        if (bbs == true) {
          this.$confirms.confirmation(
            'get',
            '此操作将取消该条发送数据, 是否继续?',
            window.path.cpus + 'v3/consumertimingsms/batchCancel/' + this.selectId,
            {},
            (res) => {
              this.getTableDtate()
            }
          )
        } else {
          this.$message({
            message: '选中项中的定时时间距离发送时间不到10分钟，不可取消！',
            type: 'warning',
          })
        }
      } else {
        this.$message({
          message: '选中项中包含不可取消项，需重新选取！',
          type: 'warning',
        })
      }
    },
    //提取相似度
    handeExtract() {
      if (this.contentFlag) {
        window.api.post(
          window.path.omcs + 'operatingclientsmssimilar/content/percent',
          this.contentList,
          (res) => {
            if (res.code == 200) {
              this.dialogVisible = true
              this.extractObj.tableData = res.data.similarList
              this.extractObj.similarTemplateList = res.data.similarTemplateList
              // this.similarFormop.content = res.data.similarList[0].content
              //   .replace(/<span class='prompt'>/g, "")
              //   .replace(/<\/span>/g, "");
              // this.similarFormop.username = res.data.similarList[0].username;
              // this.similarFormop.snapshot = JSON.stringify(
              //   res.data.similarList
              // );
            }
          }
        )
      } else {
        this.$message({
          message: '请选择同一用户名！',
          type: 'warning',
        })
      }
    },
    handleCloses(val) {
      this.dialogVisible = val
    },
    isAllEqual(array) {
      if (array.length > 0) {
        return !array.some(function (value, index) {
          return value.userName !== array[0].userName
        })
      } else {
        return true
      }
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      let timingStatus = []
      let sendTimess = []
      for (let i = 0; i < val.records.length; i++) {
        // selectId.push(val.records[i].timingSmsId);
        selectId.push(val.records[i].msgid)
        timingStatus.push(val.records[i].timingStatus)
        sendTimess.push(val.records[i].sendTime)
      }
      this.selectId = selectId.join(',') //批量操作选中消息id
      this.timingStatus = timingStatus //批量操作选中项的 状态
      this.sendTimess = sendTimess //批量操作选中的发送时间
      this.contentFlag = this.isAllEqual(val.records)
      //   const newListLength = new Set(val.records.map((item) => item.userName)).size;
      //   const listLength = val.records.length;
      //   if (listLength == newListLength || listLength > newListLength) {
      //     this.contentFlag = true;
      //   } else {
      //     this.contentFlag = false;
      //   }
      if (this.contentFlag) {
        this.contentList = val.records.map((item) => {
          //   return item.content;
          return {
            username: item.userName,
            content: item.content,
          }
        })
      }
    },
    //提交相似度
    // submitForm(formop) {
    //   this.$refs[formop].validate((valid) => {
    //     if (valid) {
    //       window.api.post(
    //         window.path.omcs + "operatingclientsmssimilar",
    //         this.similarFormop,
    //         (res) => {
    //           if (res.code == 200) {
    //             this.$message({
    //               message: res.msg,
    //               type: "success",
    //             });
    //             this.dialogVisible = false;
    //             this.getTableDtate();
    //           } else {
    //             this.$message({
    //               message: res.msg,
    //               type: "error",
    //             });
    //           }
    //         }
    //       );
    //     }
    //   });
    // },
    getconsunerTaskData() {
      //获得短信失败状态报告的图表数据
      window.api.get(
        window.path.cpas +
          'consumertasksms/selectConsumerFailureCodeNoteStatisticsByTemplateId?msgid=' +
          this.msgid,
        {},
        (res) => {
          this.tableDataObj2.tableData = res
          this.basicOption2.data = []
          this.basicOption2.ledate = []
          for (let i = 0; i < res.length; i++) {
            this.basicOption2.data.push({
              name: res[i].failureCodeNoteName,
              value: res[i].codeNoteNum,
            })
            this.basicOption2.ledate.push(res[i].failureCodeNoteName)
          }
        }
      )
      //获得任务报告图表数据
      window.api.get(
        window.path.cpas + 'consumertasksms/selTemplateDetail?msgId=' + this.msgid,
        {},
        (res) => {
          this.tableDataObj1.tableData = [res]
          this.basicOption1.data[0].value = res.successAmount
          this.basicOption1.data[1].value = res.failureAmount
          this.basicOption1.data[2].value = res.waitNumber
        }
      )
    },
    loacl() {
      let obj = JSON.parse(localStorage.getItem('timingSms'))
      if (obj) {
        this.sensitiveCondition.username = obj.username
        this.sensitiveCondition.timingStatus = obj.timingStatus
        this.sensitiveCondition.smsType = obj.smsType
        this.sensitiveCondition.matchSimilar = obj.matchSimilar
        this.sensitiveCondition.beginTime = obj.beginTime
        this.sensitiveCondition.endTime = obj.endTime
        this.sensitiveCondition.dsTime = [obj.beginTime, obj.endTime]
        Object.assign(this.sensitiveConditions, this.sensitiveCondition)
      }
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.loacl()
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.loacl()
    this.getTableDtate()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  deactivated() {
    if (JSON.parse(localStorage.getItem('timingSms'))) {
      localStorage.removeItem('timingSms')
    }
  },
  watch: {
    // dialogVisible(val) {
    //   if (val == false) {
    //     this.$refs["similarFormop"].resetFields();
    //   }
    // },
    // sensitiveConditions:{
    //     handler(){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
  },
}
</script>

<style scoped>
.Timing-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
  margin-bottom: 20px;
}
.Timing-matter > p {
  padding: 5px 0px;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.title {
  font-size: 16px;
  font-weight: 700;
  color: #000;
  margin: 10px 0;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
.el-textarea__inner {
  height: 100%;
}
.el-tabs__content {
  overflow: visible !important;
}
</style>
