<template>
  <div class="container_left">
    <!-- <div class="crumbs">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                ><i class="el-icon-lx-emoji"></i>固定菜单审核</el-breadcrumb-item
              >
            </el-breadcrumb>
          </div> -->
    <div class="Templat-matter">
      <p style="font-weight: bolder">tips：5G跳转通道生效条件</p>
      <p>1、目前仅移动、电信号码支持5G跳转。</p>
      <p>2、发送的短信签名必须在5G通道有对应chatbot上架。</p>
      <p>
        3、计费类型分为单条计费、多条计费；单条计费：计费数为1；多条计费：计费数为2-6。
      </p>
      <!-- <p>4、相同省份启用状态有且仅有一条生效</p> -->
      <!-- <p>4、验证码不支持5G跳转。</p>
          <p>5、仅能启用一条5G跳转通道。</p>
          <p>6、如果发送号码属于必过省份。则忽略计费数条件。</p> -->
    </div>
    <div style="padding: 10px">
      <el-form
        label-width="82px"
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="短信通道号" prop="channelId">
          <el-input
            class="input-w"
            v-model="ruleForm.channelId"
            placeholder="请输入短信通道号"
          ></el-input>
        </el-form-item>
        <el-form-item label="5G通道号" prop="channelCode">
          <el-input
            v-model="ruleForm.channelCode"
            placeholder="请输入5G通道号"
          ></el-input>
        </el-form-item>
        <el-form-item label="运营商" prop="operator">
          <el-select
            class="input-w"
            v-model="ruleForm.operator"
            placeholder="选择运营商"
            clearable
          >
            <el-option label="移动" value="1"></el-option>
            <el-option label="联通" value="2"></el-option>
            <el-option label="电信" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短信类型" prop="smsType">
          <el-select
            class="input-w"
            v-model="ruleForm.smsType"
            placeholder="选择短信类型"
            clearable
          >
            <el-option label="验证码" value="1"></el-option>
            <el-option label="通知" value="2"></el-option>
            <el-option label="营销推广" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            class="input-w"
            v-model="ruleForm.status"
            placeholder="选择状态"
            clearable
          >
            <el-option label="启用" value="0"></el-option>
            <el-option label="停用" value="1"></el-option>
          </el-select>
        </el-form-item>
        <div>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>

    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="channeltAdd()">新增</el-button>
          </template>
        </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="5gSmsChannelSetting"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="运营商" title="运营商">
          <template v-slot="scope">
            <div
              v-if="scope.row.operator == 1"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-yidong"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">移动</span>
            </div>
            <div
              v-else-if="scope.row.operator == 2"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-liantong"
                style="font-size: 16px; color: #f56c6c"
              ></i>
              <span style="margin-left: 5px">联通</span>
            </div>
            <div
              v-else-if="scope.row.operator == 3"
              style="display: flex; align-items: center"
            >
              <i
                class="iconfont icon-dianxin"
                style="font-size: 16px; color: #409eff"
              ></i>
              <span style="margin-left: 5px">电信</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="省份" title="省份">
          <template v-slot="scope">
            <div v-if="scope.row.specialProvince">
              {{ scope.row.specialProvince }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="短信类型" title="短信类型">
          <template v-slot="scope">
            <div
              style="margin: 5px 0"
              v-for="(item, index) in scope.row.smsType"
              :key="index + 'a'"
            >
              <el-tag
                :disable-transitions="true" v-if="item == '1'">验证码</el-tag>
              <el-tag
                :disable-transitions="true" v-else-if="item == '2'">通知 </el-tag>
              <el-tag
                :disable-transitions="true" v-else>营销推广</el-tag>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="计费类型" title="计费类型">
          <template v-slot="scope">
            <div
              style="margin: 5px 0"
              v-for="(item, index) in scope.row.billingType"
              :key="index + 'b'"
            >
              <el-tag
                :disable-transitions="true" v-if="item == '1'">单条计费</el-tag>
              <el-tag
                :disable-transitions="true" type="warning" v-else-if="item == '2'">多条计费 </el-tag>
            </div>
          </template>
        </vxe-column>
        <!-- <vxe-column field="" title="跳转计费数" width="150">
              <template #default="scope">{{ scope.row.smsBilling + '&nbsp;' + '~' + '&nbsp;' + scope.row.maxSmsBilling
              }}</template>
            </vxe-column> -->
        <vxe-column field="短信通道号" title="短信通道号">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelId }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="5G通道号" title="5G通道号">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelCode }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="回落类型" title="回落类型">
          <template v-slot="scope">
            <div>
              <el-tag
                :disable-transitions="true" v-if="scope.row.fallbackType == '1'">文本回落</el-tag>
              <el-tag
                :disable-transitions="true" type="warning" v-else-if="scope.row.fallbackType == '2'"
                >视信回落</el-tag
              >
              <!-- <div style="color: #409eff" v-if="scope.row.status == 0">启用</div>
                  <div style="color: #ff0224" v-if="scope.row.status == 1">停用</div> -->
            </div>
          </template>
        </vxe-column>
        <vxe-column field="状态" title="状态">
          <template v-slot="scope">
            <div style="color: #409eff" v-if="scope.row.status == 0">启用</div>
            <div style="color: #ff0224" v-if="scope.row.status == 1">停用</div>
          </template>
        </vxe-column>
        <vxe-column field="排除省份" title="排除省份">
          <template v-slot="scope">
            <div v-if="scope.row.province">{{ scope.row.province }}</div>
          </template>
        </vxe-column>
        <vxe-column field="创建人" title="创建人">
          <template v-slot="scope">
            <div>
              {{ scope.row.createName }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.createTime">
              {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
            </p>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" width="150" fixed="right">
          <template v-slot="scope">
            <el-button
              link
              style="color: #409eff;"
              @click="channelEdit(scope.row)"
              ><el-icon><EditPen /></el-icon>编辑</el-button
            >
            <el-button
              link
              style="color: red"
              @click="channelDel(scope.row)"
              ><el-icon><Delete /></el-icon>删除</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>

      <div class="paginationBox">
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="700px"
      :before-close="handleClose"
    >
      <div>
        <el-form
          :inline="true"
          :model="reasonFrom"
          :rules="rules"
          label-width="100px"
          ref="reasonFrom"
          style="width: 100%"
        >
          <el-form-item label="运营商" prop="operator">
            <el-select
              style="width: 200px"
              v-model="reasonFrom.operator"
              placeholder="请选择运营商"
            >
              <el-option label="移动" value="1"> </el-option>
              <el-option label="联通" value="2"> </el-option>
              <el-option label="电信" value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份" prop="specialProvince">
            <el-select
              style="width: 200px"
              v-model="reasonFrom.specialProvince"
              placeholder="请选择省份"
            >
              <el-option
                v-for="item in options"
                :key="item.provincialId"
                :label="item.provincial"
                :value="item.provincial"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="短信类型" prop="smsType">
            <el-select
              style="width: 200px"
              v-model="reasonFrom.smsType"
              multiple
              placeholder="请选择短信类型"
            >
              <el-option label="验证码" value="1"> </el-option>
              <el-option label="通知" value="2"> </el-option>
              <el-option label="营销推广" value="3"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计费类型" prop="billingType">
            <el-select
              style="width: 200px"
              v-model="reasonFrom.billingType"
              multiple
              placeholder="请选择计费类型"
            >
              <el-option label="单条计费" value="1"> </el-option>
              <el-option label="多条计费" value="2"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="短信通道号" prop="channelId">
            <el-input
              style="width: 200px"
              v-model="reasonFrom.channelId"
              placeholder="短信通道号"
            ></el-input>
          </el-form-item>
          <el-form-item label="5G通道号" prop="channelCode">
            <el-input
              style="width: 200px"
              v-model="reasonFrom.channelCode"
              placeholder="5G通道号"
            ></el-input>
          </el-form-item>
          <el-form-item label="回落类型" prop="fallbackType">
            <el-radio-group
              style="width: 200px"
              v-model="reasonFrom.fallbackType"
            >
              <el-radio value="1">文本回落</el-radio>
              <el-radio value="2">视信回落</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              style="width: 200px"
              v-model="reasonFrom.status"
              placeholder="请选择状态"
            >
              <el-option label="停用" value="1"> </el-option>
              <el-option label="启用" value="0"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排除省份" prop="province">
            <el-select
              style="width: 200px"
              v-model="reasonFrom.province"
              multiple
              placeholder="请选择排除省份"
            >
              <el-option
                v-for="item in options"
                :key="item.provincialId"
                :label="item.provincial"
                :value="item.provincial"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <div style="display: flex;">
                <el-form-item label="跳转计费数" prop="smsBilling">
                  <el-input-number :disabled="reasonFrom.longSmsflag === '1'" v-model="reasonFrom.smsBilling" :min="1" :max="6" placeholder="最小计费数"></el-input-number>
                </el-form-item>
                <span style="margin: 0 10px;">-</span>
                <el-form-item label-width="0px" label="" prop="maxSmsBilling">
                  <el-input-number :disabled="reasonFrom.longSmsflag === '1'" v-model="reasonFrom.maxSmsBilling" :min="1" :max="6" placeholder="最大计费数"></el-input-number>
                </el-form-item>
              </div> -->
        </el-form>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="channeSave('reasonFrom')"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      status: '',
      title: '',
      reason: '',
      dialogVisible: false,
      ruleForm: {
        //   menuId: "",
        //   createdUser:"",
        channelCode: '',
        channelId: '',
        operator: '',
        smsType: '',
        status: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //   menuId: "",
        //   createdUser:"",
        currentPage: 1,
        pageSize: 10,
      },
      reasonFrom: {
        id: '',
        channelCode: '', //通道号
        channelId: '', //跳转5G通道号
        smsBilling: 1, //计费数
        maxSmsBilling: 1, //最大计费数
        province: [], //省份排除
        specialProvince: '', //必过省份
        smsType: [], //短信行业类型
        fallbackType: '1', //回落类型
        billingType: [], //计费类型
        status: '', //状态
        longSmsflag: '0', //是否长短信
        operator: '',
      },
      options: [],
      rules: {
        specialProvince: [
          { required: true, message: '请选择省份', trigger: 'blur' },
        ],
        smsType: [
          { required: true, message: '请选择短信行业类型', trigger: 'blur' },
        ],
        fallbackType: [
          { required: true, message: '请选择回落类型', trigger: 'blur' },
        ],
        channelCode: [
          { required: true, message: '通道号不能为空', trigger: 'blur' },
        ],
        channelId: [
          { required: true, message: '跳转5G通道不能为空', trigger: 'blur' },
        ],
        smsBilling: [
          { required: true, message: '最小计费数不能为空', trigger: 'blur' },
        ],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }],
        maxSmsBilling: [
          { required: true, message: '最大计费数不能为空', trigger: 'blur' },
        ],
        operator: [
          { required: true, message: '请选择运营商', trigger: 'blur' },
        ],
        billingType: [
          { required: true, message: '请选择计费类型', trigger: 'blur' },
        ],
      },
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false
    }
  },
  name: 'smsChannelSetting',
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
      this.getProvincial()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getchatList()
        this.getProvincial()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + '/smsTo5gSetting/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData.forEach((item) => {
            if (item.smsType) {
              item.smsType = item.smsType.split(',')
            }
            if (item.billingType) {
              item.billingType = item.billingType.split(',')
            }
          })
          this.tableDataObj.loading2 = false
        }
      )
    },
    getProvincial() {
      // 获取省市
      window.api.get(
        window.path.omcs + 'operatingchannelprovincial/list',
        {},
        (res) => {
          this.options = res.data
        }
      )
    },
    handleClose() {
      this.dialogVisible = false
    },
    handelChange(e) {
      if (e == '1') {
        this.reasonFrom.smsBilling = '1'
        this.reasonFrom.maxSmsBilling = '1'
      }
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    channeltAdd() {
      this.dialogVisible = true
      this.title = '新增'
    },
    channelEdit(row) {
      this.dialogVisible = true
      this.title = '编辑'
      this.reasonFrom.id = row.id
      this.reasonFrom.channelCode = row.channelCode
      this.reasonFrom.channelId = row.channelId
      this.reasonFrom.smsBilling = row.smsBilling
      this.reasonFrom.status = row.status + ''
      this.reasonFrom.maxSmsBilling = row.maxSmsBilling
      this.reasonFrom.fallbackType = row.fallbackType || '1'
      this.reasonFrom.operator = row.operator + ''
      if (row.province) {
        this.reasonFrom.province = row.province.split(',')
      }
      if (row.specialProvince) {
        this.reasonFrom.specialProvince = row.specialProvince
      }
      if (row.smsType) {
        this.reasonFrom.smsType = row.smsType
      }
      if (row.billingType) {
        this.reasonFrom.billingType = row.billingType
      }
      if (row.longSms === true) {
        this.reasonFrom.longSmsflag = '1'
      } else {
        this.reasonFrom.longSmsflag = '0'
      }
    },
    //新增
    channeSave(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = {
            id: this.reasonFrom.id,
            channelCode: this.reasonFrom.channelCode, //通道号
            channelId: this.reasonFrom.channelId, //跳转5G通道号
            smsBilling: this.reasonFrom.smsBilling, //计费数
            maxSmsBilling: this.reasonFrom.maxSmsBilling, //计费数
            province: this.reasonFrom.province.join(','), //省份排除
            specialProvince: this.reasonFrom.specialProvince, //必过省份
            smsType: this.reasonFrom.smsType.join(','), //短信行业类型
            billingType: this.reasonFrom.billingType.join(','), //计费类型
            status: this.reasonFrom.status, //状态
            longSms: this.reasonFrom.longSmsflag == '1' ? true : false, //是否长短信
            operator: this.reasonFrom.operator,
            fallbackType: this.reasonFrom.fallbackType,
          }
          this.$confirms.confirmation(
            'post',
            '确认执行此操作吗？',
            window.path.omcs + '/smsTo5gSetting',
            obj,
            (res) => {
              if (res.code == 200) {
                this.getchatList()
                this.dialogVisible = false
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //删除
    channelDel(row) {
      this.$confirms.confirmation(
        'delete',
        '确认是否删除？',
        window.path.omcs + '/smsTo5gSetting/' + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.getchatList()
            this.dialogVisible = false
          } else {
            this.$message.error(res.msg)
          }
        }
      )
    },
  },
  watch: {
    dialogVisible(val) {
      // console.log(val);
      if (!val) {
        this.$refs['reasonFrom'].resetFields()
        this.reasonFrom.id = ''
        this.reasonFrom.channelCode = ''
        this.reasonFrom.channelId = ''
        this.reasonFrom.smsBilling = ''
        this.reasonFrom.maxSmsBilling = ''
        this.reasonFrom.status = ''
        this.reasonFrom.province = []
        this.reasonFrom.specialProvince = ''
        this.reasonFrom.smsType = []
        this.reasonFrom.billingType = []
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
  margin: 10px;
}
.Templat-matter > p {
  padding: 5px 0;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
