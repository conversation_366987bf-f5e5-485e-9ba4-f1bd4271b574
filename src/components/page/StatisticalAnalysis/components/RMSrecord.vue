<template>
  <div>
    <div class="passageWay-title">
      <div style="display: flex; align-items: center; width: 650px;">
        <span style="
            display: inline-block;
            width: 68px;
            text-align: right;
            padding-right: 10px;
          ">时间</span>
        <el-radio-group v-model="flag" size="default" text-color="#fff" fill="#16a589"
          @change="handleChangeTimeOptions">
          <el-radio-button value="1">今天</el-radio-button>
          <el-radio-button value="2">近一个月</el-radio-button>
        </el-radio-group>
        <!-- <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin> -->
        <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
          :disabled-date="pickerOptions && pickerOptions.disabledDate"
          :cell-class-name="pickerOptions && pickerOptions.cellClassName" v-model="datePluginValue" type="datetimerange"
          format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" range-separator="至" @change="timeClick"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </div>
      <!-- 查询框开始 -->
      <el-form :inline="true" :model="form" label-width="80px" class="demo-form-inline" ref="sendjl">
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form.clientName" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="通道ID" prop="channelId">
          <el-input v-model="form.channelId" class="input-w" onInput="value=value.replace(/[^\d]/g,'')"
            maxlength="10"></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="mobile">
          <el-input v-model="form.mobile" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="发送标题" prop="title">
          <el-input v-model="form.title" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input v-model="form.signature" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="msgId" prop="msgid">
          <el-input v-model="form.msgid" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="videoId" prop="videoId">
          <el-input v-model="form.videoId" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="运营商" prop="operator">
          <el-select v-model="form.operator" class="input-w">
            <el-option label="全部" value=""></el-option>
            <el-option label="移动" value="1"></el-option>
            <el-option label="联通" value="2"></el-option>
            <el-option label="电信" value="3"></el-option>
            <el-option label="未知" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发送状态" prop="smsStatus">
          <el-select v-model="form.smsStatus" class="input-w">
            <el-option label="全部" value=""></el-option>
            <el-option label="成功" value="1"></el-option>
            <el-option label="失败" value="2"></el-option>
            <el-option label="待返回" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="失败代码" prop="originalCode">
          <el-input v-model="form.originalCode" class="input-w"></el-input>
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-input v-model="form.source" class="input-w"></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" plain @click.prevent="Query()" @keyup.enter="Query()">查询</el-button>
          <el-button type="primary" plain @click="reset('sendjl')">重置</el-button>
        </div>
      </el-form>
      <!-- <div class="boderbottom">
               
                    <el-button type="primary" plain  @click="export1()">导出</el-button>
                </div> -->
      <!-- 查询框结束 -->
      <div class="passage-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="statisticalAnalysisRMSrecord" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px;" :data="tableDataObj.tableData">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column prop="username" field="用户名称" title="用户名称" width="120px">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              <!-- <span>
                                        {{ scope.row.username}}
                                    </span> -->
              <div style="color: #16a589; cursor: pointer" @click="rouTz(scope.row)">
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="msgId" title="msgId" width="230px">
            <template v-slot="scope">
              <div>
                {{ scope.row.msgid }}
                <!-- <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                <CopyTemp :content="scope.row.msgid" />
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="110px">
            <template #default="{ row, rowIndex }">
              <div style="color: #16a589; cursor: pointer" @click="tableContent(row, rowIndex)">
                <span>{{ row.mobile }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="签名" title="签名" width="140px">
            <template #default="scope">
              <div>
                {{ scope.row.signature }}
                <!-- <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                <!-- <CopyTemp :content="scope.row.signature" /> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="运营商" title="运营商" width="90px">
            <template v-slot="scope">
              <div v-if="scope.row.operator == 1" style="display: flex; align-items: center">
                <i class="iconfont icon-yidong" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div v-else-if="scope.row.operator == 2" style="display: flex; align-items: center">
                <i class="iconfont icon-liantong" style="font-size: 16px; color: #f56c6c"></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div v-else-if="scope.row.operator == 3" style="display: flex; align-items: center">
                <i class="iconfont icon-dianxin" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">电信</span>
              </div>
              <div v-else-if="scope.row.operator == 4">未知</div>
            </template>
          </vxe-column>
          <vxe-column field="视频短信标题" title="视频短信标题" width="200px">
            <template v-slot="scope">
              <div style="color: #409eff; cursor: pointer" @click="View(scope.row)">
                <Tooltip v-if="scope.row.title" :content="scope.row.title" className="wrapper-text" effect="light">
                </Tooltip>
              </div>
              <!-- <Tooltip v-if="scope.row.title" :content="scope.row.title"
                                    className="wrapper-text" effect="light">
                                </Tooltip> -->
              <!-- <span>{{ scope.row.title }}</span> -->
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="预览">
                            <template #default="scope">
                                <el-button type="text" @click="View(scope.row)"><i
                                        class="el-icon-picture"></i>&nbsp;预览</el-button>
                            </template>
                        </vxe-column> -->
          <vxe-column field="计费数" title="计费数" width="80px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.chargeNum" :content="scope.row.chargeNum" className="wrapper-text"
                effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="100px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间" width="100px">
            <template v-slot="scope">
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}
              </p>
              <p class="wrapper-text" v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="发送状态" title="发送状态" width="90px">
            <template v-slot="scope">
              <el-tag :disable-transitions="true" v-if="scope.row.status == 1" type="success" effect="dark">
                成功
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.status == 2" type="danger" effect="dark">
                失败
              </el-tag>
              <el-tag :disable-transitions="true" v-else-if="scope.row.status == 3" type="info" effect="dark">
                待返回
              </el-tag>
              <!-- <span v-if="scope.row.status == 1">成功</span>
                                <span v-else-if="scope.row.status == 2">失败</span>
                                <span v-else-if="scope.row.status == 3">待返回</span> -->
            </template>
          </vxe-column>
          <vxe-column field="下载状态" title="下载状态" width="90px">
            <template v-slot="scope">
              <div v-if="scope.row.downloadStatus == '1000'">下载成功</div>
              <div v-else-if="scope.row.downloadStatus == '1001'">下载失败</div>
              <div v-else>未知</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="80px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.originalCode" :content="scope.row.originalCode" className="wrapper-text"
                effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="错误码类别" title="错误码类别" width="100">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.originalCodeGroup" :content="scope.row.originalCodeGroup"
                className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="扩展号" title="扩展号" width="110px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.ext" :content="scope.row.ext" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="通道号" title="通道号" width="85px">
            <template v-slot="scope">
              <div @click="save(scope.row.channelId)" style="color: rgb(22, 165, 137); cursor: pointer">{{
                scope.row.channelId }}</div>
            </template>
          </vxe-column>
          <vxe-column field="videoId" title="videoId" width="90px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.videoId" :content="scope.row.videoId" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="提交Ip" title="提交Ip" width="110px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.ip" :content="scope.row.ip" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源" width="100px">
            <template v-slot="scope">
              <Tooltip v-if="scope.row.source" :content="scope.row.source" className="wrapper-text" effect="light">
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column fixed="right" field="操作" title="操作" width="120">
            <template v-slot="scope">
              <el-button link style="margin-left: 10px; color: #409eff;" @click="handelCheck(scope.row)"><el-icon>
                  <View />
                </el-icon>&nbsp;实名明细</el-button>
              <el-button style="margin-left: 10px; color: #409eff;" @click="handlewhite(scope.row)" link size="default">
                <i class="iconfont icon-jiarubaimingdan" style="font-size: 12px"><span style="margin-left: 5px;">加白</span></i>
              </el-button>
              <!-- <el-tooltip effect="dark" content="加白" placement="top">
                    
                  </el-tooltip> -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="formData.currentPage" :page-size="formData.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
          </el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  ">{{ title }}</span>
              </div>
              <div style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                " v-for="(item, index) in viewData" :key="index">
                <img v-if="
                  item.media == 'jpg' ||
                  item.media == 'gif' ||
                  item.media == 'png' ||
                  item.media == 'jpeg'
                " :src="API.imgU + item.mediaGroup + '/' + item.mediaPath" style="width: 235px"
                  class="avatar video-avatar" ref="avatar" />
                <video v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath" v-if="item.type == 'video'"
                  style="width: 235px" class="avatar video-avatar" controls="controls"></video>
                <audio v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath" v-if="item.type == 'audio'"
                  style="width: 235px" autoplay="autoplay" controls="controls" preload="auto"></audio>
                <div style="white-space: pre-line">
                  {{ item.txt }}
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog title="一键加白" v-model="whiteVisible" width="30%" :before-close="handleCloseImp">
      <el-form :model="whiltlist" label-width="100px" ref="whiltlist" style="padding: 0 28px 0 20px">
        <el-form-item label="用户名称" prop="level">
          <div>{{ whiltlist.username }}</div>
        </el-form-item>
        <el-form-item label="加白手机号" prop="level">
          <div>{{ maskMobile }}</div>
        </el-form-item>
        <el-form-item label="白名单等级" prop="level">
          <el-radio-group v-model="whiltlist.level">
            <el-radio value="1">一级白名单</el-radio>
            <el-radio value="2" style="margin-left: 10px">二级白名单</el-radio>
          </el-radio-group>
          <div style="color: #606266; font-size: 12px">
            (白名单有效期默认180天)
          </div>
        </el-form-item>
        <!-- <el-form-item label="是否补发" prop="resend">
          <el-radio-group v-model="whiltlist.resend">
            <el-radio :value="true">是</el-radio>
            <el-radio :value="false" style="margin-left: 10px">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="whiteVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm('whiltlist')">确定</el-button>
      </template>
    </el-dialog>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
    <SignatureView v-if="autonymVisible" ref="SignatureRef" :signatureData="signatureData"
      @handleClose="closeSignature">
    </SignatureView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import SignatureView from "@/components/publicComponents/signatureDetail.vue";
import monment from 'moment'
// import clip from '../../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  name: 'RMSrecord',
  components: {
    DatePlugin,
    TableTem,
    ChannelView,
    UserLists,
    Tooltip,
    CopyTemp,
    SignatureView,
  },
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      userFlag: false,
      autonymVisible: false,
      signatureData: {
        signatoryId: "",
        userId: "",
        userName: "",
        productId: "3",
      },
      nameover: '',
      // 预览
      viewData: '',
      indexVeew: 1,
      title: '',
      dialogVisible: false,
      //
      ChannelData: '', //传递通道值
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      idStr: '',
      // moreInfoDialog:false,
      //  dialogFormVisible: false, //新增弹出框显示隐藏
      formop: {
        //表单数据
        remark: '',
      },
      flag: '1', //选择那一天
      Times: {
        beginTime: monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      },
      datePluginValue: [
        monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      ],
      // datePluginValueList: { //日期选择器
      //     type:"datetimerange",
      //     start:"",
      //     end:'',
      //     range:'-',
      //     clearable:false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        // onPick: ({ maxDate, minDate }) => {
        //     this.pickerMinDate = minDate.getTime();
        //     if (maxDate) {
        //         this.pickerMinDate = ''
        //     }
        // },
        // disabledDate: (time) => {
        //     console.log(time,'time');
        //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
        //         return false
        //     }
        //     if (this.pickerMinDate !=='') {
        //         const day7 = 6 * 24 * 3600 * 1000
        //         let maxTime = this.pickerMinDate + day7
        //         let now = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        //         if (maxTime >  Date.now()) {
        //             maxTime =  Date.now()
        //         }
        //         const minTime = this.pickerMinDate - day7
        //         // console.log(monment(now).unix(),'pp');
        //         // console.log(monment(minTime).format('YYYY MM DD, hh:mm:ss'),'ll');
        //         return time.getTime() < minTime || time.getTime() > maxTime
        //     }
        //     return time.getTime() > Date.now();
        // }
      },
      //    datePluginValue:''
      // },
      //发送查询的值
      form: {
        mobile: '',
        channelId: '',
        clientName: '',
        title: '',
        signature:"",
        smsStatus: '',
        operator: '',
        msgid: '',
        videoId: '',
        source: '',
        originalCode: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        channelId: '',
        clientName: '',
        title: '',
        signature:"",
        smsStatus: '',
        operator: '',
        msgid: '',
        videoId: '',
        source: '',
        originalCode: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      activeName2: 'first', //发送和回复选项卡
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        totalRow: 0,
        tableData: [],
      },
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      whiteVisible: false,
      whiltlist: {
        cipherMobile: "",
        keyId: "",
        level: "1",
        // resend: false,
        // smsInfoId: "",
        username: "",
      },
      maskMobile: "",
      username: ""
    }
  },
  methods: {
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsReplyId)
      }
      this.selectId = selectId.join(',')
    },
    // 预览
    View(val) {
      this.indexVeew = 1
      this.viewData = val.contents
      this.title = val.title
      this.dialogVisible = true
    },
    // handleCopy(name,event){
    //     clip(name, event)
    // },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    // 点击手机号
    tableContent(row, index) {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.decryptMobile,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile=res.data
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      )
      //  window.api.get(window.path.omcs + "smsMessage/decrypt?smsInfoId="+val,{},res=>{
      //     if(res.code==200){
      //         this.tableDataObj.tableData[index].mobile=res.data[0]
      //     }else{
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }
      // })
    },
    //获取 发送记录数据
    getChallDate() {
      let aa = {}
      Object.assign(aa, this.formData)
      aa.flag = this.flag
      aa.sendBeginTime = this.Times.beginTime
      aa.sendEndTime = this.Times.endTime
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'statistics/sendVideoInfo/page',
        aa,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.totalRow = res.data.total
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
    },
    timeClick(val) {
      if (val) {
        this.Times.beginTime = this.moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = this.moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
        this.flag = '3'
      } else {
        this.Times.beginTime = ""
        this.Times.endTime = ""
        this.flag = ''
      }

      // if(val){
      //     this.Times.beginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
      //     this.Times.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      //     this.flag='5'
      // }else{
      //     this.flag='1'
      // }
    },
    handleChangeTimeOptions: function () {
      // this.datePluginValue = ''
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    //发送查询
    Query() {
      Object.assign(this.formData, this.form)
      this.getChallDate()
    },
    //发送重置
    reset() {
      if (this.flag == '1') {
        // console.log(11);
        this.$refs.sendjl.resetFields()
        this.Times.beginTime = monment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = monment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
        Object.assign(this.formData, this.form)
        this.getChallDate()
      } else {
        this.flag = '1'
        this.$refs.sendjl.resetFields()
        Object.assign(this.formData, this.form)
        this.getChallDate()
      }
      // this.flag='1'
      // this.$refs.sendjl.resetFields();
      // Object.assign(this.formData,this.form);
    },
    handleSizeChange(size) {
      this.formData.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formData.currentPage = currentPage
    },
    handelCheck(row) {
      // this.checkForm.userId = row.userId;
      // this.signatoryId = row.signature;
      this.signatureData.signatoryId = row.signature;
      this.signatureData.userId = row.userId;
      this.signatureData.userName = row.username;
      this.autonymVisible = true;
      // this.handleClick();
    },
    closeSignature(data) {
      this.autonymVisible = data;
    },
    //加白
    handlewhite(row) {
      this.maskMobile = row.maskMobile;
      this.username = row.username;
      this.whiltlist.username = row.username;
      this.whiltlist.cipherMobile = row.cipherMobile;
      // this.whiltlist.smsInfoId = row.smsInfoId;
      this.whiltlist.keyId = row.keyId;
      this.whiteVisible = true;
    },
    handleCloseImp() {
      this.whiteVisible = false
    },
    submitForm() {
      let data = {
        username: this.whiltlist.username,
        cipherMobile: this.whiltlist.cipherMobile,
        keyId: this.whiltlist.keyId,
        // resend: this.whiltlist.resend,
        level: this.whiltlist.level,
      };
      this.$confirms.confirmation(
        "post",
        "确认执行此操作吗？",
        window.path.omcs + "smsMessage/diagnosis/addWhite",
        data,
        (res) => {
          if (res.code == 200) {
            this.whiteVisible = false;
            this.getTableDtate();
          }

          // this.gettableLIst();
        }
      );
    },
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听时间范围
    flag: function (val) {
      // if(val != '5'){
      //     this.Times.beginTime = '';
      //     this.Times.endTime = '';
      //     this.datePluginValue='';
      // }
      if (val == '1') {
        this.Times.beginTime = monment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = monment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          monment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          monment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
      } else if (val == '2') {
        this.Times.beginTime = monment()
          .subtract(30, 'days')
          .format('YYYY-MM-DD 00:00:00')
        this.Times.endTime = monment().format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          monment().subtract(30, 'days').format('YYYY-MM-DD 00:00:00'),
          monment().format('YYYY-MM-DD HH:mm:ss'),
        ]
      }
      // this.getChallDate()
      // this.getChallDate();
    },
    //监听时间是否改变
    Times: {
      handler(val) {
        if (val) {
          this.getChallDate()
        }
      },
      deep: true,
    },
    formData: {
      handler(val) {
        this.getChallDate()
      },
      deep: true,
    },
    whiteVisible(val) {
      if (!val) {
        this.whiltlist.username = "" 
        this.whiltlist.cipherMobile = ""
        // this.whiltlist.smsInfoId = "";
        this.whiltlist.keyId = "";
        this.whiltlist.level = "1";
        // this.whiltlist.resend = false;
      }
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}

.demo-form-inline {
  margin-top: 12px;
}

.passage-table {
  margin-bottom: 40px;
}

.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}

.send-mobel-box img {
  width: 300px;
}

.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
