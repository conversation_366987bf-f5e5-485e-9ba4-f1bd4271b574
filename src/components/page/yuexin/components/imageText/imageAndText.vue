<template>
  <div v-if="tag == 'view'" style="width: 312px; margin: 0 auto">
    <div
      :class="view == 'view' ? 'preview-container-view' : 'preview-container'"
    >
      <div class="headPreview">
        <img
          v-for="(item, index) in imageList"
          :src="item.img"
          alt=""
          :class="imageCount == index ? 'firstImg actionPreview' : ''"
          @click="handelLarge(index)"
        />
      </div>
      <div class="preview-container-inside" style="left: 30px">
        <div class="preview">
          <div class="preview-phone">
            <div class="preview_edit">
              <div class="preview_view_img">
                <img
                  style="border-radius: 10px"
                  v-if="ssoContentFrom.imgcontents.srcMaterialId == ''"
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  style="border-radius: 10px"
                  v-else
                  class="img"
                  :src="ssoContentFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
              </div>
              <div class="preview_view_content">
                <div class="preview_view_title">
                  {{ ssoContentFrom.titlecontents.content }}
                </div>
                <div class="preview_view_text">
                  {{ ssoContentFrom.textcontents.content }}
                </div>
                <div class="preview_btn">
                  {{ ssoContentFrom.btncontents.content }}
                </div>
              </div>
            </div>
          </div>
          <div class="xiaomi">
            <div class="preview_edit">
              <div class="preview_view_img">
                <img
                  style="border-radius: 10px"
                  v-if="ssoContentFrom.imgcontents.srcMaterialId == ''"
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  style="border-radius: 10px"
                  v-else
                  class="img"
                  :src="ssoContentFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
              </div>
              <div class="preview_view_content">
                <div class="preview_view_title">
                  {{ ssoContentFrom.titlecontents.content }}
                </div>
                <div class="preview_view_text">
                  {{ ssoContentFrom.textcontents.content }}
                </div>
                <div class="preview_btn">
                  {{ ssoContentFrom.btncontents.content }}
                </div>
              </div>
            </div>
          </div>
          <div class="preview-phone">
            <div class="preview_edit">
              <div class="preview_view_img">
                <img
                  style="border-radius: 10px"
                  v-if="ssoContentFrom.imgcontents.srcMaterialId == ''"
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  style="border-radius: 10px"
                  v-else
                  class="img"
                  :src="ssoContentFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
              </div>
              <div class="preview_view_content">
                <div class="preview_view_title">
                  {{ ssoContentFrom.titlecontents.content }}
                </div>
                <div class="preview_view_text">
                  {{ ssoContentFrom.textcontents.content }}
                </div>
                <div class="preview_btn">
                  {{ ssoContentFrom.btncontents.content }}
                </div>
              </div>
            </div>
          </div>
          <div class="preview-phone">
            <div class="preview_edit">
              <div class="preview_view_img">
                <img
                  style="border-radius: 10px"
                  v-if="ssoContentFrom.imgcontents.srcMaterialId == ''"
                  class="img"
                  src="@/assets/images/ssobg.png"
                  alt=""
                  srcset=""
                />
                <img
                  style="border-radius: 10px"
                  v-else
                  class="img"
                  :src="ssoContentFrom.imgcontents.srcMaterialUrl"
                  alt=""
                  srcset=""
                />
              </div>
              <div class="preview_view_content">
                <div class="preview_view_title">
                  {{ ssoContentFrom.titlecontents.content }}
                </div>
                <div class="preview_view_text">
                  {{ ssoContentFrom.textcontents.content }}
                </div>
                <div class="preview_btn">
                  {{ ssoContentFrom.btncontents.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else>1</div>
</template>

<script>
export default {
  props: {
    list: {
      type: Object,
      default: () => {},
    },
    view: {
      type: String,
      default: '',
    },
    tag: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      imageList: [
        {
          img: require('@/assets/images/huaweiLarge.png'),
        },
        {
          img: require('@/assets/images/xiaomiLarge.png'),
        },
        {
          img: require('@/assets/images/oppoLarge.png'),
        },
        {
          img: require('@/assets/images/vivoLarge.png'),
        },
      ],
      imageCount: 0,
      ssoContentFrom: {
        //图片
        imgcontents: {
          type: 'image', //资源类型
          content: '', //文本内容
          srcMaterialId: '', //素材ID
          srcMaterialUrl: '', //素材URL
          srcMaterialType: '', //素材类型
          coverMaterialId: '', //封面素材ID
          coverMaterialUrl: '', //封面URL
          textTitle: false, //是否为文本标题
          visible: '', //是否可见
          currencyDisplay: '', //	是否显示货币符号
          actionType: '', //事件类型
          action: {
            //事件参数
            target: '',
            merchantName: '',
          },
          positionNumber: 1, //位置序号
        },
        //标题
        titlecontents: {
          type: 'text', //资源类型
          content: '编辑文本，最多显示17个字', //文本内容
          srcMaterialId: '', //素材ID
          srcMaterialUrl: '', //素材URL
          coverMaterialId: '', //封面素材ID
          coverMaterialUrl: '', //封面URL
          textTitle: false, //是否为文本标题
          visible: '', //是否可见
          currencyDisplay: '', //	是否显示货币符号
          actionType: '', //事件类型
          action: {
            //事件参数
            target: '',
            merchantName: '',
          },
          positionNumber: 2, //位置序号
          // type: "text",
          // content: "编辑文本，最多显示17个字",
          // textTitle: true,
          // positionNumber: 2,
        },
        //内容
        textcontents: {
          type: 'text', //资源类型
          content:
            '编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。', //文本内容
          srcMaterialId: '', //素材ID
          srcMaterialUrl: '', //素材URL
          coverMaterialId: '', //封面素材ID
          coverMaterialUrl: '', //封面URL
          textTitle: false, //是否为文本标题
          visible: '', //是否可见
          currencyDisplay: '', //	是否显示货币符号
          actionType: '', //事件类型
          action: {
            //事件参数
            target: '',
            merchantName: '',
          },
          positionNumber: 3, //位置序号
          // type: "text",
          // content: "编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。",
          // textTitle: false,
          // positionNumber: 3,
        },
        //按钮
        btncontents: {
          type: 'button', //资源类型
          content: '编辑按钮', //文本内容
          srcMaterialId: '', //素材ID
          srcMaterialUrl: '', //素材URL
          coverMaterialId: '', //封面素材ID
          coverMaterialUrl: '', //封面URL
          textTitle: false, //是否为文本标题
          visible: '', //是否可见
          currencyDisplay: '', //	是否显示货币符号
          actionType: '', //事件类型
          action: {
            //事件参数
            target: '',
            merchantName: '',
          },
          positionNumber: 4, //位置序号
          // type: "button",
          // content: "编辑按钮",
          // actionType: "",
          // action: {
          //   target: "",
          //   merchantName: "",
          // },
          // positionNumber: 4,
        },
      },
    }
  },
  methods: {
    handelLarge(index) {
      this.imageCount = index
      let byClass = document.getElementsByClassName(
        'preview-container-inside'
      )[0].style
      if (index == 0) {
        byClass.left = '30px'
      } else if (index == 1) {
        byClass.left = '-243px'
      } else if (index == 2) {
        byClass.left = '-537px'
      } else {
        byClass.left = '-824px'
      }
    },
  },

  watch: {
    list: {
      handler(val) {
        var arr = Object.keys(val)
        if (arr.length) {
          this.ssoContentFrom.imgcontents = val.pages[0].contents[0]
          this.ssoContentFrom.titlecontents = val.pages[0].contents[1]
          this.ssoContentFrom.textcontents = val.pages[0].contents[2]
          this.ssoContentFrom.btncontents = val.pages[0].contents[3]
          // this.options = val.options
          // this.ruleForm.templateId = val.id
        }
      },
      deep: true, // 深度监听
      immediate: true, // 初次监听即执行
    },
  },
}
</script>

<style scoped>
.preview-container {
  width: 312px;
  padding-right: 16px;
  border-right: 1px solid #dcdfe6;
  overflow: hidden;
  display: inline-block;
}
.preview-container-view {
  width: 312px;
  padding-right: 16px;
  overflow: hidden;
  display: inline-block;
}
.headPreview {
  display: flex;
  justify-content: space-evenly;
  padding-top: 10px;
  box-sizing: border-box;
}
.headPreview img {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.2s ease 0s;
}
.preview {
  display: inline-block;
  vertical-align: top;
  box-sizing: border-box;
  border-right: none !important;
}
.actionPreview {
  transform: scale(1.7);
  opacity: 1;
}
.preview-container-inside {
  position: relative;
  width: 1548px;
  left: 0;
  transition: left 0.6s;
}
.preview-phone {
  position: relative;
  display: inline-block;
  width: 295px;
  height: 573px;
  background-image: url(../../../../../assets/images/phone-huawei.png);
  background-repeat: no-repeat;
  background-position: 10%;
}
.xiaomi {
  position: relative;
  display: inline-block;
  width: 271px;
  height: 573px;
  background-image: url(../../../../../assets/images/phone-xiaomi.png);
  background-repeat: no-repeat;
  background-position: 10%;
}
.preview_edit {
  width: 342px;
  height: 364px;
  background: #fff;
  border-radius: 10px;
  position: absolute;
  -webkit-transform-origin-x: 0;
  -webkit-transform: scale(0.6);
  left: 0;
  top: 0;
  margin-left: 20px;
  margin-top: 17px;
}
.preview_view_img {
  width: 342px;
  height: 192px;
}
.preview_view_content {
  width: 200px;
}
.preview_view_title {
  width: 300px;
  height: 26px;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  text-decoration: none;
  line-height: 1.5;
  letter-spacing: 0px;
  color: rgb(0, 0, 0);
  text-align: left;
  background-color: transparent;
  opacity: 1;
  box-sizing: border-box;
  margin-top: 25px;
  margin-left: 20px;
}
.preview_view_text {
  width: 300px;
  height: 62px;
  margin-left: 20px;
  font-size: 13px;
  font-style: normal;
  font-weight: normal;
  text-decoration: none;
  line-height: 1.5;
  letter-spacing: 0px;
  color: rgb(0, 0, 0);
  text-align: left;
  background-color: transparent;
  opacity: 1;
  box-sizing: border-box;
}
.preview_btn {
  text-align: center;
  width: 307px;
  margin: 0 auto;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  background-color: #007dff;
  color: #fff;
  font-size: 17px;
  margin-top: 7px;
}
.img {
  width: 100%;
  height: 100%;
}
</style>
