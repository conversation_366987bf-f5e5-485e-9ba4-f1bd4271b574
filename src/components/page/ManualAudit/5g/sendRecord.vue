<template>
  <div class="container_left">
    <div style="padding: 10px">
      <div>
        <el-form inline ref="form" :model="form" label-width="80px">
          <el-form-item label="用户名" prop="username">
            <el-input class="input-w" v-model="form.username"></el-input>
          </el-form-item>
          <el-form-item label="消息ID" prop="msgId">
            <el-input class="input-w" v-model="form.msgId"></el-input>
          </el-form-item>
          <el-form-item label="模板ID" prop="templateId">
            <el-input class="input-w" v-model="form.templateId"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input class="input-w" v-model="form.mobile"></el-input>
          </el-form-item>
          <el-form-item label="回执码" prop="reportCode">
            <el-input class="input-w" v-model="form.reportCode"></el-input>
          </el-form-item>
          <!-- <el-form-item label="发件人">
                            <el-input v-model="form.name"></el-input>
                        </el-form-item> -->
          <el-form-item label="回执状态" prop="reportStatus">
            <el-select
              class="input-w"
              v-model="form.reportStatus"
              clearable
              placeholder="请选择"
            >
              <el-option label="全部" value=""> </el-option>
              <el-option label="成功" value="1"> </el-option>
              <el-option label="失败" value="2"> </el-option>
              <el-option label="待返回" value="3"> </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="创建时间">
                             <el-date-picker
                                v-model="value1"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item> -->
          <el-form-item label="发送时间" prop="value1">
            <el-date-picker
              :shortcuts="pickerOptions && pickerOptions.shortcuts"
              :disabled-date="pickerOptions && pickerOptions.disabledDate"
              :cell-class-name="pickerOptions && pickerOptions.cellClassName"
              v-model="form.value1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              @change="timeClick"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button plain type="primary" @click="search">查询</el-button>
            <el-button plain type="primary" @click="Reset('form')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div>
        <!-- <el-table
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          :data="tableData.data"
          border
          stripe
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="5GsendRecord"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          :cell-config="{height: 80}"
          min-height="1"
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableData.data">

          <vxe-column field="用户名" title="用户名" width="120">
            <template v-slot="scope">
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="140">
            <template v-slot="{row, rowIndex}">
              <div
                style="color: #16a589; cursor: pointer"
                @click="tableContent(row, rowIndex)"
              >
                <span>{{ row.maskMobile }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="运营商" title="运营商" width="120">
            <template v-slot="scope">
              <div>
                <div
                  v-if="scope.row.operator == 1"
                  style="display: flex; align-items: center"
                >
                  <i
                    class="iconfont icon-yidong"
                    style="font-size: 16px; color: #409eff"
                  ></i>
                  <span style="margin-left: 5px">移动</span>
                </div>
                <div
                  v-else-if="scope.row.operator == 2"
                  style="display: flex; align-items: center"
                >
                  <i
                    class="iconfont icon-liantong"
                    style="font-size: 16px; color: #f56c6c"
                  ></i>
                  <span style="margin-left: 5px">联通</span>
                </div>
                <div
                  v-else-if="scope.row.operator == 3"
                  style="display: flex; align-items: center"
                >
                  <i
                    class="iconfont icon-dianxin"
                    style="font-size: 16px; color: #409eff"
                  ></i>
                  <span style="margin-left: 5px">电信</span>
                </div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="归属地" title="归属地" width="120">
            <template v-slot="scope">
              <!-- <span >{{scope.row.provincial}}</span> -->
              <div>{{ scope.row.city }}</div>
            </template>
          </vxe-column>
          <vxe-column width="180px" field="消息ID" title="消息ID">
            <template v-slot="scope">
              <div>{{ scope.row.msgId }}</div>
            </template>
          </vxe-column>
          <vxe-column width="180px" field="模板ID" title="模板ID">
            <template v-slot="scope">
              <div>{{ scope.row.templateId }}</div>
            </template>
          </vxe-column>
          <vxe-column field="模板类型" title="模板类型" width="120">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.templateType == 'card'">卡片</span>
                <span v-if="scope.row.templateType == 'text'">文本</span>
                <span v-if="scope.row.templateType == 'media'">多媒体</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="通道号" title="通道号" width="120">
            <template v-slot="scope">
              <div>{{ scope.row.channelId }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回执状态" title="回执状态" width="120">
            <template v-slot="scope">
              <div>
                <span style="color: #409eff" v-if="scope.row.reportStatus == '1'"
                  >成功</span
                >
                <span style="color: red" v-else-if="scope.row.reportStatus == '2'"
                  >失败</span
                >
                <span style="color: #e6a23c" v-else>待返回</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="100">
            <template v-slot="scope">
              <p v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
              </p>
              <!-- <div class="item_time" v-if="scope.row.sendTime">{{
                    moment(scope.row.sendTime).format("YYYY-MM-DD HH:mm:ss")
                  }}</div> -->
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间" width="100">
            <template v-slot="scope">
              <p v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}
              </p>
              <!-- <div class="item_time" v-if="scope.row.reportTime">{{
                    moment(scope.row.reportTime).format("YYYY-MM-DD HH:mm:ss")
                  }}</div> -->
            </template>
          </vxe-column>
          <vxe-column field="回执码" title="回执码" width="120">
            <template v-slot="scope">
              <div>{{ scope.row.reportCode }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回执码解释" title="回执码解释" width="140">
            <template v-slot="scope">
              <div>
                <Tooltip
                  v-if="scope.row.reportDetail"
                  :content="scope.row.reportDetail"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="预计费类型" title="预计费类型" width="120">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.deductionBillType == 'TEXT'">文本</span>
                <span v-else-if="scope.row.deductionBillType == 'MEDIA'"
                  >多媒体</span
                >
                <span v-else-if="scope.row.deductionBillType == 'SESSION'"
                  >会话</span
                >
                <span v-else-if="scope.row.deductionBillType == 'FALLBACK_SMS'"
                  >回落短信</span
                >
                <span v-else-if="scope.row.deductionBillType == 'FALLBACK_MMS'"
                  >回落视信</span
                >
                <span v-else-if="scope.row.deductionBillType == 'FALLBACK_RMS'"
                  >回落阅信</span
                >
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column prop="deductionPrice" field="下发扣费金额" title="下发扣费金额"> </vxe-column> -->
          <vxe-column field="实际计费类型" title="实际计费类型" width="120">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.finalBillType == 'TEXT'">文本</span>
                <span v-else-if="scope.row.finalBillType == 'MEDIA'">多媒体</span>
                <span v-else-if="scope.row.finalBillType == 'SESSION'">会话</span>
                <span v-else-if="scope.row.finalBillType == 'FALLBACK_SMS'"
                  >回落短信</span
                >
                <span v-else-if="scope.row.finalBillType == 'FALLBACK_MMS'"
                  >回落视信</span
                >
                <span v-else-if="scope.row.finalBillType == 'FALLBACK_RMS'"
                  >回落阅信</span
                >
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column prop="finalPrice" field="最终扣费金额" title="最终扣费金额"> </vxe-column> -->
        </vxe-table>
        <div class="send_foot">
          <div></div>
          <div style="margin: 10px 0">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    Tooltip
  },
  data() {

return {
customConfig: {
  storage: true,
  // mode: "popup"
},
dialogVisible: false,
value1: '',
message: '群发记录',
type: '',
detail: {},
form: {
username: '',
msgId: '',
templateId: '',
reportStatus: '',
mobile: '',
reportCode: '',
startTime: moment().subtract(1, 'months').format('YYYY-MM-DD 00:00:00'),
endTime: moment().format('YYYY-MM-DD 23:59:59'),
value1: [
moment().subtract(1, 'months').format('YYYY-MM-DD 00:00:00'),
moment().format('YYYY-MM-DD 23:59:59'),
],
pageSize: 10,
currentPage: 1,
},
pickerOptions: {
disabledDate(time) {
return (
time.getTime() >
new Date(
new Date(new Date().toLocaleDateString()).getTime() +
24 * 60 * 60 * 1000 -
1
)
)
},
},
tabelAlllist: {
username: '',
msgId: '',
templateId: '',
reportStatus: '',
mobile: '',
reportCode: '',
startTime: moment().subtract(1, 'months').format('YYYY-MM-DD 00:00:00'),
endTime: moment().format('YYYY-MM-DD 23:59:59'),
value1: [
moment().subtract(1, 'months').format('YYYY-MM-DD 00:00:00'),
moment().format('YYYY-MM-DD 23:59:59'),
],
pageSize: 10,
currentPage: 1,
},
tableData: {
loading2: false,
data: [],
total: 0,
},
title: '',
}
},
name: 'SendRecord5G',
created() {
this.getList()
},
mounted() {
  const $table = this.$refs.tableRef
  const $toolbar = this.$refs.toolbarRef
  if ($table && $toolbar) {
    $table.connect($toolbar)
  }
},
computed: {
...mapState({
userId: (state) => state.userId,
}),
},
methods: {
getList() {
this.tableData.loading2 = true
window.api.post(
window.path.fiveWeb + '/operator/record/page',
this.tabelAlllist,
(res) => {
  this.tableData.data = res.data.records
  this.tableData.total = res.data.total
  this.tableData.loading2 = false
}
)
},
search() {
Object.assign(this.tabelAlllist, this.form)
this.getList()
},
Reset(formName) {
this.$refs[formName].resetFields()
this.form.startTime = moment()
.subtract(1, 'months')
.format('YYYY-MM-DD 00:00:00')
this.form.endTime = moment().format('YYYY-MM-DD 23:59:59')
Object.assign(this.tabelAlllist, this.form)
this.getList()
},
timeClick(val) {
if (val) {
this.form.startTime = this.moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
this.form.endTime = this.moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
} else {
this.form.startTime = ""
this.form.endTime = ""
}
},
tableContent(row, index) {
window.api.post(
window.path.upms + '/generatekey/decryptMobile',
{
  keyId: row.keyId,
  smsInfoId: '5G',
  cipherMobile: row.cipherMobile,
},
(res) => {
  if (res.code == 200) {
    this.tableData.data[index].maskMobile = res.data
  } else {
    this.$message({
      message: res.msg,
      type: 'warning',
    })
  }
}
)
},
handleClose() {
this.dialogVisible = false
},
handleSizeChange(size) {
this.tabelAlllist.pageSize = size
this.getList()
},
handleCurrentChange: function (currentPage) {
this.tabelAlllist.currentPage = currentPage
this.getList()
},
}
}
</script>

<style scoped>
.send_foot {
  display: flex;
  justify-content: space-between;
}
</style>

<style>
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>