# 📋 SignatureQuery.vue 签名详细信息功能

## 🎯 功能概述

根据用户需求，在SignatureQuery.vue中新增了签名详细信息展示功能，作为第二级优先展示内容。通过调用realName接口获取企业详细信息，包括企业名称、社会统一信用代码、企业法人、责任人信息等。

## ✨ 核心功能特性

### 📊 详细信息展示
- **企业名称**: 显示签名所属企业的完整名称
- **社会统一信用代码**: 企业的统一社会信用代码
- **企业法人**: 企业法定代表人姓名
- **责任人姓名**: 签名责任人的姓名
- **责任人证件号码**: 责任人身份证号码
- **责任人手机号**: 责任人联系电话
- **签名来源**: 详细的签名来源分类和说明

### 🔄 数据获取方式
- **接口地址**: `window.path.omcs + "consumerSignature/realName?userId=" + userId + "&signatureId=" + signatureId`
- **请求方式**: GET
- **自动加载**: 查询结果的第一条记录自动加载详细信息
- **按需加载**: 其他记录点击按钮后加载详细信息

### 🎨 UI设计特点
- **第二级优先**: 位于基本信息行之后，实名状态之前
- **网格布局**: 2列网格展示，信息密度适中
- **加载状态**: 支持loading状态显示
- **错误处理**: 接口失败时显示默认值

## 🔧 技术实现

### 1. 数据结构设计

#### **记录数据扩展**
```javascript
// 为每个签名记录添加详情相关字段
const records = (res.records || []).map(item => ({
  ...item,
  detailLoading: false,  // 详情加载状态
  detailData: null       // 详情数据
}))
```

#### **详情数据结构**
```javascript
item.detailData = {
  enterpriseName: '企业名称',
  socialCreditCode: '社会统一信用代码',
  legalPerson: '企业法人',
  responsiblePersonName: '责任人姓名',
  responsiblePersonId: '责任人证件号码',
  responsiblePersonPhone: '责任人手机号',
  signatureSource: {
    category: '企业名称',
    description: '事业单位、如机关、学校、科研单位、街道社区等',
    types: [
      { name: '商标', description: '(须提供商标注册证书图片...)' },
      { name: 'App', description: '(须提供app在ICP/IP/域名备案...)' },
      { name: '小程序', description: '(须提供小程序在ICP/IP/域名备案...)' }
    ]
  }
}
```

### 2. 接口调用实现

#### **加载详细信息方法**
```javascript
loadSignatureDetail(item) {
  if (item.detailLoading || item.detailData) return
  
  item.detailLoading = true
  
  window.api.get(
    window.path.omcs + "consumerSignature/realName?userId=" + item.userId + "&signatureId=" + item.signatureId,
    {},
    (res) => {
      item.detailLoading = false
      if (res.code === 200 && res.data) {
        // 处理详细信息数据
        item.detailData = {
          enterpriseName: res.data.enterpriseName || res.data.companyName,
          socialCreditCode: res.data.socialCreditCode || res.data.creditCode,
          legalPerson: res.data.legalPerson || res.data.legalRepresentative,
          responsiblePersonName: res.data.responsiblePersonName || res.data.contactName,
          responsiblePersonId: res.data.responsiblePersonId || res.data.contactIdCard,
          responsiblePersonPhone: res.data.responsiblePersonPhone || res.data.contactPhone,
          signatureSource: this.parseSignatureSource(res.data.signatureSource || res.data.source)
        }
      }
    }
  )
}
```

#### **数据字段映射**
```javascript
// 支持多种可能的字段名称
enterpriseName: res.data.enterpriseName || res.data.companyName
socialCreditCode: res.data.socialCreditCode || res.data.creditCode
legalPerson: res.data.legalPerson || res.data.legalRepresentative
responsiblePersonName: res.data.responsiblePersonName || res.data.contactName
responsiblePersonId: res.data.responsiblePersonId || res.data.contactIdCard
responsiblePersonPhone: res.data.responsiblePersonPhone || res.data.contactPhone
```

### 3. UI组件实现

#### **详细信息展示区域**
```vue
<div class="signature-detail-info" v-loading="item.detailLoading">
  <div class="detail-title">
    <i class="el-icon-document"></i>
    签名详细信息
  </div>
  <div v-if="item.detailData" class="detail-content-grid">
    <div class="detail-row">
      <div class="detail-item">
        <div class="detail-label">企业名称</div>
        <div class="detail-value">{{ item.detailData.enterpriseName || '-' }}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">社会统一信用代码</div>
        <div class="detail-value">{{ item.detailData.socialCreditCode || '-' }}</div>
      </div>
    </div>
    <!-- 更多行... -->
  </div>
  <div v-else class="detail-loading-placeholder">
    <el-button type="text" @click="loadSignatureDetail(item)" :loading="item.detailLoading">
      <i class="el-icon-view"></i>
      点击加载详细信息
    </el-button>
  </div>
</div>
```

#### **签名来源特殊展示**
```vue
<div class="detail-value signature-source">
  <div class="source-category">{{ item.detailData.signatureSource.category }}</div>
  <div class="source-description">{{ item.detailData.signatureSource.description }}</div>
  <div class="source-types" v-if="item.detailData.signatureSource.types">
    <div class="source-type" v-for="type in item.detailData.signatureSource.types" :key="type.name">
      <span class="type-name">{{ type.name }}</span>
      <span class="type-desc">{{ type.description }}</span>
    </div>
  </div>
</div>
```

### 4. 样式设计

#### **详细信息容器样式**
```css
.signature-detail-info {
  margin-bottom: 20px;
  
  .detail-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    
    i {
      color: #409eff;
      font-size: 18px;
    }
  }
}
```

#### **网格布局样式**
```css
.detail-content-grid {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  
  .detail-row {
    display: flex;
    gap: 24px;
    margin-bottom: 16px;
    
    .detail-item {
      flex: 1;
      
      .detail-label {
        font-size: 13px;
        font-weight: 600;
        color: #606266;
        margin-bottom: 6px;
      }
      
      .detail-value {
        font-size: 14px;
        color: #303133;
        min-height: 20px;
      }
    }
  }
}
```

#### **签名来源特殊样式**
```css
.signature-source {
  .source-category {
    font-weight: 600;
    color: #409eff;
    margin-bottom: 4px;
  }
  
  .source-description {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }
  
  .source-types {
    .source-type {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
      
      .type-name {
        font-weight: 500;
        color: #606266;
        min-width: 60px;
      }
      
      .type-desc {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}
```

## 🔄 用户交互流程

### 1. 自动加载流程
```
用户查询签名 → 获取签名列表 → 自动加载第一条记录的详细信息 → 
显示详细信息在卡片中
```

### 2. 手动加载流程
```
用户查看其他签名卡片 → 点击"点击加载详细信息"按钮 → 
显示loading状态 → 获取详细信息 → 显示在卡片中
```

### 3. 错误处理流程
```
接口调用失败 → 显示默认值"-" → 提示用户获取失败 → 
用户可重新点击加载
```

## 📊 信息展示优先级

### 第一级：卡片头部
1. 签名名称
2. 用户信息
3. 实名状态

### 第二级：签名详细信息（新增）
1. **企业名称** - 最重要的企业标识
2. **社会统一信用代码** - 企业唯一标识
3. **企业法人** - 法定代表人
4. **责任人信息** - 姓名、证件、手机号
5. **签名来源** - 详细分类说明

### 第三级：其他信息
1. 基本信息（类型、来源、时间）
2. 短信示例
3. 详细实名状态
4. 资质图片

## 🎯 使用场景

### 1. 企业信息核实
```
运营人员需要核实签名所属企业 → 查询签名 → 
查看企业名称、统一信用代码 → 确认企业身份
```

### 2. 责任人联系
```
客服需要联系签名责任人 → 查询签名 → 
查看责任人姓名和手机号 → 进行联系
```

### 3. 合规性检查
```
审核人员检查签名合规性 → 查询签名 → 
查看企业法人、责任人信息 → 核实身份一致性
```

## 🚀 优化效果

### 1. 信息完整性
- 提供了完整的企业和责任人信息
- 支持多种字段名称的兼容性
- 错误情况下的友好提示

### 2. 用户体验
- 第一条记录自动加载，减少操作
- 其他记录按需加载，提高性能
- Loading状态提供良好的反馈

### 3. 界面设计
- 网格布局清晰易读
- 签名来源信息特殊展示
- 响应式设计适配移动端

---

**功能完成**: SignatureQuery.vue已成功集成签名详细信息功能，通过realName接口获取企业详细信息，作为第二级优先展示内容，提供了完整的签名相关企业和责任人信息。
