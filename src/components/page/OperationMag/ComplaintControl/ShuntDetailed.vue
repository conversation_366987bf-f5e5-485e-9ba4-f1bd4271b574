<template>
  <div class="container_left">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="短信" name="first">
        <keep-alive>
          <SMS v-if="activeName === 'first'" />
        </keep-alive>
      </el-tab-pane>
      <!-- <el-tab-pane label="彩信" name="second">
        <MMS v-if="activeName === 'second'" />
      </el-tab-pane> -->
      <el-tab-pane label="视频短信" name="third">
        <keep-alive>
          <VideoSms v-if="activeName === 'third'" />
        </keep-alive>
      </el-tab-pane>
      <!-- <el-tab-pane label="长短信回执" name="four">
                    <longsSmsVue v-if="activeName==='four'"/>
                </el-tab-pane>
                <el-tab-pane label="成功率监控" name="five">
                    <successRateVue v-if="activeName==='five'"/>
                </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import SMS from './components/SMSshun.vue'
// import MMS from './components/MMSshun.vue'
import VideoSms from './components/Videoshun.vue'
import longsSmsVue from './components/longsSms.vue'
import successRateVue from './components/successRate.vue'
export default {
  components: {
    SMS,
    // MMS,
    VideoSms,
    longsSmsVue,
    successRateVue
  },
  data() {
    return {
      activeName: 'first',
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    },
  },
}
</script>
