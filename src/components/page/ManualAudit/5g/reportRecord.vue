<template>
  <div class="container_left">
    <div style="padding: 10px">
      <div>
        <el-form inline ref="form" :model="form">
          <el-form-item label="消息Id" prop="msgId">
            <el-input class="input-w" v-model="form.msgId"></el-input>
          </el-form-item>
          <el-form-item label="批次号" prop="reportCode">
            <el-input class="input-w" v-model="form.reportCode"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input class="input-w" v-model="form.mobile"></el-input>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button plain type="primary" @click="search">查询</el-button>
            <el-button plain type="primary" @click="Reset('form')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div>
        <!-- <el-table
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          :data="tableData.data"
          border
          stripe
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="reportRecord"
          border
          stripe
          show-overflow
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableData.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableData.data">

          <!-- <el-table-column prop="username" field="" title="用户名"> </el-table-column> -->
          <vxe-column field="手机号" title="手机号" width="300px">
            <template v-slot="scope">
              <div>{{ scope.row.mobile }}</div>
            </template>
          </vxe-column>
          <vxe-column field="消息ID" title="消息ID">
            <template v-slot="scope">
              <div>
                <span>{{ scope.row.msgId }}</span>
                <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgId,$event )"></i> -->
                <CopyTemp :content="scope.row.msgId" />
              </div>
            </template>
          </vxe-column>
          <vxe-column field="批次号" title=" 批次号">
            <template v-slot="scope">
              <div>{{ scope.row.reportCode }}</div>
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间">
            <template v-slot="scope">
              <!-- <p v-if="scope.row.reportTime">{{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}</p>
                  <p v-if="scope.row.reportTime">{{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}</p> -->
              <div>{{
                moment(scope.row.reportTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
        </vxe-table>
        <div class="send_foot">
          <div></div>
          <div style="margin: 10px 0">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import clip from '../../../../utils/clipboard'
import { mapState } from 'vuex'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    CopyTemp
  },
  name: 'reportRecord',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      dialogVisible: false,
      value1: '',
      message: '群发记录',
      type: '',
      detail: {},
      form: {
        mobile: '',
        msgId: '',
        reportCode: '',
        pageSize: 10,
        currentPage: 1,
      },
      tabelAlllist: {
        mobile: '',
        msgId: '',
        reportCode: '',
        pageSize: 10,
        currentPage: 1,
      },
      tableData: {
        loading2: false,
        data: [],
        total: 0,
      },
      title: '',
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  computed: {
    ...mapState({
      userId: (state) => state.userId,
    }),
  },
  methods: {
    getList() {
      this.tableData.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/report/flow/page',
        this.tabelAlllist,
        (res) => {
          this.tableData.data = res.data.records
          this.tableData.total = res.data.total
          this.tableData.loading2 = false
        }
      )
    },
    // handleCopy(name,event){
    //   clip(name, event)
    // },
    search() {
      Object.assign(this.tabelAlllist, this.form)
      this.getList()
    },
    Reset(formName) {
      this.$refs[formName].resetFields()
      Object.assign(this.tabelAlllist, this.form)
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getList()
    },
    handleCurrentChange: function (currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getList()
    },
  },
}
</script>

<style scoped>
.send_foot {
  display: flex;
  justify-content: space-between;
}
</style>

<style>
.el-table .el-table__header tr,
.el-table .el-table__header tr th,
.el-table__fixed-right-patch {
  background-color: #f5f5f5;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
