<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="手机号" label-width="80px" prop="mobile">
              <el-input
                v-model="formInline.mobile"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="发送状态" label-width="80px" prop="smsStatus">
              <el-select v-model="formInline.smsStatus" class="input-w">
                <el-option label="全部" value=""></el-option>
                <el-option label="成功" value="1"></el-option>
                <el-option label="失败" value="2"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="补发时间" label-width="80px" prop="channelId">
                                <el-date-picker
                                    v-model="formInline.time"
                                    type="datetimerange"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    range-separator="至"
                                    @change="timeClick"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </el-form-item> -->
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
          <el-button
            type="primary"
            v-if="isResissueSmsStatus && isResissue"
            @click="addCharacterPopUps = true"
            plain
            style=""
            >再次补发</el-button
          >
        </div>
        <div class="sensitive-fun">
          <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">补发记录详情列表</span> -->
        </div>
        <div class="Mail-table" style="padding-bottom: 40px; margin-top: 10px">
          <!-- 表格和分页开始 -->
          <!-- <table-tem :tableDataObj="tableDataObj"></table-tem> -->
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          >
            <el-table-column type="selection"> </el-table-column>
            <el-table-column label="用户名" prop="clientName" width="90">
            </el-table-column>
            <el-table-column label="手机号" width="100">
              <!-- <template #default="scope" >
                                    <div @click="phoneClick(scope.$index,scope.row)" style="color: rgb(22, 165, 137); cursor: pointer;">
                                        <span >{{scope.row.mobile}}</span>
                                    </div>
                                </template> -->
              <template v-slot="scope">
                <div
                  style="color: #16a589; cursor: pointer"
                  @click="tableContent(scope.row.smsInfoId)"
                >
                  <span>{{ scope.row.mobile }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发送内容" prop="content" min-width="500">
            </el-table-column>
            <el-table-column label="计费条数" prop="chargeNum" width="80">
            </el-table-column>
            <el-table-column label="消息ID" prop="msgid" width="180">
            </el-table-column>
            <el-table-column label="发送通道号" prop="channelId" width="85">
            </el-table-column>
            <el-table-column label="回执时间" prop="reportTime" width="150">
            </el-table-column>
            <el-table-column label="发送时间" prop="sendTime" width="150">
            </el-table-column>
            <el-table-column label="发送状态" width="80" prop="smsStatusStr">
              <!-- <template #default="scope" >
                                    <span v-if="scope.row.smsStatus==3">待返回</span>
                                    <span style="color: #f56c6c;" v-else>发送失败</span>
                                </template> -->
            </el-table-column>
            <el-table-column label="状态代码" prop="originalCode" width="90">
            </el-table-column>
          </el-table>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[100, 500, 1000]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
    <el-dialog
      title="短信补发"
      v-model="addCharacterPopUps"
      width="25%"
      :close-on-click-modal="false"
      :before-close="handleClose1"
    >
      <div class="addC" style="padding-right: 50px">
        <el-form
          :inline="true"
          ref="formAdd"
          :rules="rules"
          :model="formAdd"
          class="demo-form-inline"
        >
          <!-- <el-form-item label="选择通道" label-width="100px" prop="aisle">
                            <el-select v-model="formAdd.aisle" placeholder="请选择" style="width:280px">
                                <el-option label="人工通道组" value="1"></el-option>
                            </el-select>
                        </el-form-item> -->
          <!-- <el-form-item v-if='formAdd.aisle=="1"' label="通道组名称"  label-width="100px" prop="ChannelSelection">
                            <el-select v-model="formAdd.ChannelSelection"  style="width:280px" clearable filterable placeholder="请选择">
                                <el-option
                                v-for="item in options"
                                :key="item.channelGroupId"
                                :label="item.channelGroupName"
                                :value="item.channelGroupId">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
          <el-form-item
            label="通道组名称"
            label-width="100px"
            prop="channelGroupId"
          >
            <el-select
              v-model="formAdd.channelGroupId"
              @change="GroupIdChange"
              style="width: 280px"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in options"
                :key="item.channelGroupId"
                :label="item.channelGroupName"
                :value="item.channelGroupId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="移动通道号"
            label-width="100px"
            prop="ydChannelId"
          >
            <el-select
              v-model="formAdd.ydChannelId"
              style="width: 280px"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in GroupArry.listYD"
                :key="item.channelId"
                :label="item.channelName"
                :value="item.channelId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="联通通道号"
            label-width="100px"
            prop="ltChannelId"
          >
            <el-select
              v-model="formAdd.ltChannelId"
              style="width: 280px"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in GroupArry.listLT"
                :key="item.channelId"
                :label="item.channelName"
                :value="item.channelId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="电信通道号"
            label-width="100px"
            prop="dxChannelId"
          >
            <el-select
              v-model="formAdd.dxChannelId"
              style="width: 280px"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in GroupArry.listDX"
                :key="item.channelId"
                :label="item.channelName"
                :value="item.channelId"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="addCharacterPopUps = false" v-if="StatusT == true"
            >取 消</el-button
          >
          <el-button type="primary" v-if="StatusT == true" @click="RoleADD()"
            >确 定</el-button
          >
          <el-button type="primary" v-if="StatusT == false" :loading="true"
            >操作进行中，请稍等</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 点手机号查询补发 -->
    <el-dialog
      title="补发详情"
      v-model="moreInfoDialog"
      width="810px"
      class="pro-style"
      :close-on-click-modal="false"
    >
      <el-table
        v-loading="tableDataObj1.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj1.tableData"
        style="width: 100%"
      >
        <el-table-column label="发送类型" width="80px">
          <template v-slot="scope">
            <span v-if="scope.row.sendType == 1">自动补发</span>
            <span v-else-if="scope.row.sendType == 2">首次发送</span>
            <span v-else-if="scope.row.sendType == 3">手动补发</span>
            <span v-else-if="scope.row.sendType == 4">携号转网</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" width="100px">
          <template v-slot="scope">
            <span
              style="color: #16a589; cursor: pointer"
              @click="phoneClick(scope.$index, scope.row)"
              >{{ scope.row.mobile }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="通道ID" width="70px">
          <template v-slot="scope">
            <span
              >{{ scope.row.channelId }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="smsStatusStr" label="状态" width="80px">
        </el-table-column>
        <el-table-column prop="originalCode" label="失败代码" width="150px">
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="140px">
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="140px">
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 20px">
        <el-button type="primary" @click="moreInfoDialog = false"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    DatePlugin,
    TableTem
  },
  name: 'ReissueDetails',
  data() {
    return {
      moreInfoDialog: false,
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
      rules: {
        channelGroupId: [
          { required: true, message: '请选择通道组名称', trigger: 'change' },
        ],
        ydChannelId: [
          { required: true, message: '移动通道不能为空', trigger: 'change' },
        ],
        ltChannelId: [
          { required: true, message: '联通通道不能为空', trigger: 'change' },
        ],
        dxChannelId: [
          { required: true, message: '电信通道不能为空', trigger: 'change' },
        ],
      },
      formAdd: {
        recordId: '',
        // 选择通道类型
        dxChannelId: '',
        ltChannelId: '',
        ydChannelId: '',
      },
      roleDialog: '',
      // 查询列表数据
      formInline: {
        mobile: '',
        smsStatus: '',
        // beginTime:'',
        // endTime:'',
        // time:[],
        pageSize: 100,
        currentPage: 1,
      },
      //储存查询列表数据
      formInlines: {
        mobile: '',
        smsStatus: '',
        // beginTime:'',
        // endTime:'',
        // time:[],
        pageSize: 100,
        currentPage: 1,
      },
      // 再次补发状态
      isResissueSmsStatus: false,
      isResissue: false,

      // 补发弹出层
      addCharacterPopUps: false,
      // 通道组
      GroupArry: [],
      // 存储二次弹出框状态
      StatusT: true,
      // 存储通道
      options: [],
      //角色列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    goBack() {
      this.$router.push({ path: '/SMSReplacement' })
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      this.formInlines.recordId = this.$route.query.id
      window.api.post(
        window.path.omcs + 'servicereissuerecord/detail',
        this.formInlines,
        (res) => {
          if (res.code == 200) {
            this.isResissue = res.data.isResissue
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data.pageInfo.records
            this.tableDataObj.tablecurrent.total = res.data.pageInfo.total
          }
        }
      )
    },
    //日期选择
    timeClick(val) {
      this.formInline.beginTime = this.moment(val[0]).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      this.formInline.endTime = this.moment(val[1]).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      // console.log(this.moment(val[0]).format("YYYY-MM-DD hh:mm:ss"))
      // console.log(this.moment(val[1]).format("YYYY-MM-DD hh:mm:ss"))
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      if (this.formInlines.smsStatus == '2') {
        this.isResissueSmsStatus = true
      } else {
        this.isResissueSmsStatus = false
      }
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      Object.assign(this.formInlines, this.formInline)
      if (this.formInlines.smsStatus == '2') {
        this.isResissueSmsStatus = true
      } else {
        this.isResissueSmsStatus = false
      }
    },
    // 点击手机号
    tableContent(smsInfoId) {
      window.api.get(
        window.path.omcs + 'smsMessage/details?smsInfoId=' + smsInfoId,
        {},
        (res) => {
          if (res.code == 200) {
            this.tableDataObj1.loading2 = false
            this.tableDataObj1.tableData = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
        }
      )
      this.moreInfoDialog = true
    },
    // 关闭弹出层
    handleClose1(done) {
      this.addCharacterPopUps = false
    },
    // 选择通道组改变
    GroupIdChange() {
      this.formAdd.dxChannelId = ''
      this.formAdd.ltChannelId = ''
      this.formAdd.ydChannelId = ''
      window.api.get(
        window.path.omcs +
          '/v3/smsreplacement/allsmschannelgroup/' +
          this.formAdd.channelGroupId,
        {},
        (res) => {
          this.GroupArry = res.data
        }
      )
    },
    phoneClick(index, row) {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.smsInfoId,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj1.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      )
    },
    // 提交
    RoleADD() {
      this.$refs['formAdd'].validate((valid) => {
        if (valid) {
          this.$confirm('确定短信补发?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
          })
            .then(() => {
              this.StatusT = false
              this.formAdd.recordId = this.$route.query.id
              window.api.post(
                window.path.omcs + 'v3/smsreplacement/groupReplacement/again',
                this.formAdd,
                (res) => {
                  this.StatusT = true
                  this.addCharacterPopUps = false
                  if (res.code == 200) {
                    this.$message({
                      type: 'success',
                      message: '操作成功!',
                    })
                  } else {
                    this.$message({
                      type: 'error',
                      message: res.msg,
                    })
                  }
                }
              )
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除',
              })
            })
        }
      })
    },
    // 发送时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.sendBeginTime = val[0] + ' 00:00:00'
        this.formInline.sendEndTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.sendBeginTime =
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00'
        this.formInline.sendEndTime =
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59'
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
  },
  created() {
    window.api.get(
      window.path.omcs + 'v3/operatingchannelgroup/getAllSMSGroup',
      {},
      (res) => {
        this.options = res.data
      }
    )
  },
  activated() {
    this.InquireList()
    // window.api.get(window.path.omcs+'v3/operatingchannelgroup/getAllSMSGroup',{},res=>{
    //     this.options=res.data
    // })
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps: function (val) {
      if (val == false) {
        this.InquireList()
        this.$refs.formAdd.resetFields() //清空表单
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
    moreInfoDialog(val) {
      if (val == false) {
        this.tableDataObj1.loading2 = false
        this.tableDataObj1.tableData = []
      }
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
