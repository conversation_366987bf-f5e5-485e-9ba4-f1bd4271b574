<template>
  <div class="container_left">
    <div class="outer-frame fillet">
      <!-- 搜索表单 -->
      <el-form label-width="80px" :inline="true" :model="formInline" class="search-form" ref="formInline">
        <el-form-item label="模板ID" prop="temId">
          <el-input v-model="formInline.temId" placeholder="必须为数字" class="input-time"
            oninput="value=value.replace(/[^\d]/g,'')"></el-input>
        </el-form-item>

        <el-form-item label="用户名" prop="clientName">
          <el-input v-model="formInline.clientName" placeholder="请输入用户名" class="input-time"></el-input>
        </el-form-item>

        <el-form-item label="模板名称" prop="temName">
          <el-input v-model="formInline.temName" placeholder="请输入模板名称" class="input-time"></el-input>
        </el-form-item>

        <el-form-item label="模板内容" prop="temContent">
          <el-input v-model="formInline.temContent" placeholder="请输入模板内容" class="input-time"></el-input>
        </el-form-item>
        <el-form-item label="模板类型" prop="temType">
          <el-select v-model="formInline.temType" clearable placeholder="请选择" class="input-time">
            <el-option label="不限" value=""></el-option>
            <el-option label="验证码" value="1"></el-option>
            <el-option label="通知" value="2"></el-option>
            <el-option label="营销推广" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板格式" prop="temFormat">
          <el-select v-model="formInline.temFormat" clearable placeholder="请选择" class="input-time">
            <el-option label="不限" value=""></el-option>
            <el-option label="变量模板" value="1"></el-option>
            <el-option label="全文模板" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间" prop="dataCreat">
          <el-date-picker class="input-time" v-model="formInline.dataCreat" type="daterange" range-separator="-"
            start-placeholder="开始日期" end-placeholder="结束日期" @change="hande"></el-date-picker>
        </el-form-item>
        <el-form-item label="审核时间" prop="shenhedataCreat">
          <el-date-picker class="input-time" v-model="formInline.shenhedataCreat" type="daterange" range-separator="-"
            @change="handes" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <div class="form-buttons">
          <el-button type="primary" @click="Query">查询</el-button>
          <el-button @click="Reload('formInline')">重置</el-button>
        </div>
      </el-form>

      <!-- 表格部分 -->
      <div class="table-container">
        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="openImportDialog">模版导入</el-button>
            <el-button type="primary" v-if="selectId.length > 0" @click="addopt">批量设置标签</el-button>
            <el-button type="danger" v-if="selectId.length > 0" @click="delAll">批量删除</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="TemplateMag" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="400"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          :data="tableDataObj.tableData" @checkbox-all="handelSelection" @checkbox-change="handelSelection">
          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="模板ID" title="模板ID" width="80px">
            <template v-slot="scope">
              <div class="link-text" @click="xiangqing(scope.row)">{{ scope.row.temId }}</div>
            </template>
          </vxe-column>
          <vxe-column field="用户名" title="用户名" width="120">
            <template v-slot="scope">
              <div class="link-text" @click="rouTersZ(scope.row)">{{ scope.row.clientName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="模板类型" title="模板类型" width="90px">
            <template v-slot="scope">
              <div v-if="scope.row.temType == 1">验证码</div>
              <div v-else-if="scope.row.temType == 2">通知</div>
              <div v-else-if="scope.row.temType == 3">营销推广</div>
            </template>
          </vxe-column>
          <vxe-column field="模板名称" title="模板名称" width="200px">
            <template v-slot="scope">
              <div>{{ scope.row.temName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="模板内容" title="模板内容" min-width="500px">
            <template v-slot="scope">
              <div class="temp-content" v-html="scope.row.temContent"></div>
            </template>
          </vxe-column>
          <vxe-column field="模板变量" title="模板变量" min-width="180px">
            <template v-slot="scope">
              <div v-if="scope.row.text == '{}' || !scope.row.text"></div>
              <div v-else v-for="(item, index) in scope.row.text
                .replace(/\{|}/g, '')
                .split(',')" :key="index" class="variable-item">
                <span class="variable-name">{{ item.split(':')[0] }}</span> :
                <span class="variable-value">{{ item.split(':')[1] }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="参数检测" title="参数检测" width="90px">
            <template v-slot="scope">
              <div class="status-enabled" v-if="scope.row.skipParamCheck == false">是</div>
              <div class="status-disabled" v-else>否</div>
            </template>
          </vxe-column>
          <vxe-column field="审核人" title="审核人" width="100">
            <template v-slot="scope">
              <div>{{ scope.row.checkName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="审核时间" title="审核时间" width="160">
            <template v-slot="scope">
              <div>{{ scope.row.checkTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="冷热模版" title="冷热模版" width="100px" fixed="right">
            <template v-slot="scope">
              <el-switch 
                v-model="scope.row.heat" 
                class="mb-2" 
                inline-prompt
                active-text="热" 
                inactive-text="冷" 
                :active-value="1"
                :inactive-value="0"
                @change="(val) => changeHeatStatus(scope.row, val)" 
              />
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180px" fixed="right">
            <template v-slot="scope">
              <!-- <el-button type="primary" link @click="handlePass(scope.$index, scope.row)">
                <el-icon>
                  <Sort />
                </el-icon>
                变更模板类型
              </el-button> -->
              <el-button type="primary" link @click="checks(scope.row)">
                <el-icon>
                  <Sort />
                </el-icon>
                参数检测
              </el-button>
              <el-button type="danger" link @click="delAllS(scope.row)">
                <el-icon>
                  <Delete />
                </el-icon>
                删除
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>

        <!--分页-->
        <div class="pagination-box">
          <div></div>
          <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </div>
      </div>

      <!-- 设置标签对话框 -->
      <el-dialog :title="titleMap[dialogStatus]" v-model="dialogFormVisible" :close-on-click-modal="false"
        width="520px">
        <el-form :model="setLabel" :rules="setLabel.formRule" ref="setLabel" label-width="82px">
          <el-form-item label="类型" prop="style">
            <el-select v-model="setLabel.formData.style" multiple filterable placeholder="请选择标签" style="width: 100%">
              <el-option v-for="(item, index) in labelpass1" :key="index" :label="item.labelContent"
                :value="item.labelId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="行业" prop="hangye">
            <el-select v-model="setLabel.formData.hangye" multiple filterable placeholder="请选择标签" style="width: 100%">
              <el-option v-for="(item, index) in labelpass2" :key="index" :label="item.labelContent"
                :value="item.labelId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="退订格式" prop="tuidinggeshi">
            <el-select v-model="setLabel.formData.tuidinggeshi" filterable placeholder="请选择标签" style="width: 100%">
              <el-option label="请选择" value=""></el-option>
              <el-option v-for="(item, index) in labelpass3" :key="index" :label="item.labelContent"
                :value="item.labelId"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('setLabel')">确认</el-button>
            <el-button @click="dialogFormVisible = false">取消</el-button>
          </div>
        </template> -->
      </el-dialog>

      <!-- 变更模板对话框 -->
      <el-dialog title="变更模板" v-model="dialogTemplate" :close-on-click-modal="false" width="500px">
        <div class="template-details">
          <div class="details-item">
            <span class="details-label">用户名:</span>
            <span class="details-value">{{ TemplateObject.clientName }}</span>
          </div>
          <div class="details-item">
            <span class="details-label">模板类型:</span>
            <span class="details-value" v-if="TemplateObject.temType == 1">验证码</span>
            <span class="details-value" v-else-if="TemplateObject.temType == 2">通知</span>
            <span class="details-value" v-else-if="TemplateObject.temType == 3">营销推广</span>
          </div>
          <div class="details-item">
            <span class="details-label">模板名称:</span>
            <span class="details-value">{{ TemplateObject.temName }}</span>
          </div>
          <div class="details-item">
            <span class="details-label">模板内容:</span>
            <div class="details-value" v-html="TemplateObject.temContent"></div>
          </div>
          <div class="details-item">
            <span class="details-label">修改模板:</span>
            <el-select v-model="templateVal" placeholder="请选择" class="template-select">
              <el-option label="验证码" value="1"></el-option>
              <el-option label="通知" value="2"></el-option>
              <el-option label="营销推广" value="3"></el-option>
            </el-select>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="templaD(TemplateObject.temId, templateVal)">提交</el-button>
            <el-button @click="dialogTemplate = false">取消</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 详情对话框 -->
      <el-dialog title="模板详情" v-model="dialogVisible" width="540px" class="details-dialog">
        <div v-for="(item, index) in detailsList" :key="index" class="details-item">
          <span class="details-label">{{ item.title }}</span>
          <span class="details-value" :class="{
            'status-enabled': item.content === '是',
            'status-disabled': item.content === '否'
          }">{{ item.content }}</span>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false" class="know-btn">知道了</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 导入示例对话框 -->
      <el-dialog title="导入示例" v-model="importDialogVisible" :close-on-click-modal="false" width="520px">
        <el-upload class="upload-demo" :action="actionUrl" :headers="headers" :on-success="handleUploadSuccess"
          :on-error="handleUploadError" :on-progress="handleProgress" :on-remove="handleRemove"
          :before-upload="beforeUpload" :limit="1" :file-list="fileList" accept=".xlsx,.xls">
          <el-button size="default" type="primary">
            <el-icon><upload-filled /></el-icon>选择文件上传
          </el-button>
          <template #tip>
            <div class="el-upload__tip">只能上传 xlsx、xls 格式的Excel文件，且不超过10MB</div>
          </template>
        </el-upload>
        <div v-if="Progressflag" style="text-align: center; font-size: 35px">
          <el-icon class="is-loading">
            <loading />
          </el-icon>
        </div>
        <template #footer>
          <el-button @click="importDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="importTemplate">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TemplateMag',
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      userFlag: false,
      nameover: '',
      dialogVisible: false,
      checkFlag: false,
      dialogStatus: '',
      dataCreat: '',
      selectId: [],
      dialogFormVisible: false,
      dialogTemplate: false,
      detailsRows: {},
      labelpass1: [],
      labelpass2: [],
      labelpass3: [],
      templatId: '',
      labelType: '',
      labelIndustry: '',
      labelUnsubscribe: '',
      labelName: [],
      TemplateObject: {},
      templateVal: '',
      formInline: {
        temId: '',
        clientName: '',
        temName: '',
        temContent: '',
        labelIds: [],
        temType: '',
        temFormat: '',
        dataCreat: [],
        shenhedataCreat: [],
        beginTime1: '',
        endTime1: '',
        beginTime2: '',
        endTime2: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        temId: '',
        clientName: '',
        temName: '',
        temContent: '',
        labelIds: [],
        temType: '',
        temFormat: '',
        dataCreat: [],
        shenhedataCreat: [],
        beginTime1: '',
        endTime1: '',
        beginTime2: '',
        endTime2: '',
        currentPage: 1,
        pageSize: 10,
      },
      rules: {},

      setLabel: {
        formData: {
          style: [],
          hangye: [],
          tuidinggeshi: '',
        },
        formRule: {
          style: [{ required: false, message: '请选择标签', trigger: 'blur' }],
          hangye: [{ required: false, message: '请选择标签', trigger: 'blur' }],
          tuidinggeshi: [
            { required: false, message: '请选择标签', trigger: 'blur' },
          ],
        },
      },
      detailsList: [
        { title: '用户名', content: '' },
        { title: '模板ID', content: '' },
        { title: '模板类型', content: '' },
        { title: '模板格式', content: '' },
        { title: '模板名称', content: '' },
        { title: '模板内容', content: '' },
        { title: '参数检测', content: '' },
        { title: '申请时间', content: '' },
        { title: '审核人', content: '' },
        { title: '审核时间', content: '' },
        { title: '申请说明', content: '' },
      ],
      fileList2: [],
      titleMap: {
        add: '标签批量修改',
        edit: '标签修改',
      },

      tableDataObj: {
        total: 0,
        loading2: false,
        tableData: []
      },
      importDialogVisible: false,
      fileList: [],
      actionUrl: window.path.omcs + 'template/upload',
      headers: {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN')
      },
    }
  },
  methods: {
    gettableLIst() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'template/page',
        this.tabelAlllist,
        (res) => {
          for (let i = 0; i < res.records.length; i++) {
            if (res.records[i].labels) {
              res.records[i].labels = res.records[i].labels.split(',')
            }
          }
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    hande(val) {
      if (val) {
        this.formInline.beginTime1 = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime1 = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime1 = ''
        this.formInline.endTime1 = ''
      }
    },
    handes(val) {
      if (val) {
        this.formInline.beginTime2 = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime2 = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime2 = ''
        this.formInline.endTime2 = ''
      }
    },
    Query() {
      if (this.formInline.temId) {
        if (!/^(?!0)[0-9]+$/.test(this.formInline.temId)) {
          this.$message({
            message: '模板id输入有误，只能输入数字',
            type: 'warning',
          })
          this.formInline.temId = ''
        } else {
          Object.assign(this.tabelAlllist, this.formInline)
          this.gettableLIst()
        }
      } else {
        Object.assign(this.tabelAlllist, this.formInline)
        this.gettableLIst()
      }
    },
    Reload() {
      this.$refs.formInline.resetFields()
      this.formInline.beginTime1 = ''
      this.formInline.endTime1 = ''
      this.formInline.beginTime2 = ''
      this.formInline.endTime2 = ''
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(setLabel) {
      this.$refs[setLabel].validate((valid) => {
        let labelIds = []
        let labelNames = []
        let hangye = this.setLabel.formData.hangye
        let style = this.setLabel.formData.style
        if (this.setLabel.formData.tuidinggeshi != '') {
          labelIds.push(this.setLabel.formData.tuidinggeshi)
        }
        if (hangye.length) {
          for (let i = 0; i < hangye.length; i++) {
            labelIds.push(hangye[i])
          }
        }
        if (style.length) {
          for (let i = 0; i < style.length; i++) {
            labelIds.push(style[i])
          }
        }
        this.labelIds = labelIds.join(',')
        let arrlabel = labelIds
        if (valid) {
          if (this.dialogStatus == 'edit') {
            if (arrlabel.indexOf(55) == -1) {
              this.$confirms.confirmation(
                'post',
                '确认执行此操作吗？',
                window.path.omcs + 'template/updateLable',
                {
                  labelId: this.labelIds,
                  temId: this.templatId,
                },
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              if (
                (arrlabel.indexOf(55) != -1 && arrlabel.indexOf(4) != -1) ||
                (arrlabel.indexOf(55) != -1 && arrlabel.indexOf(5) != -1)
              ) {
                this.$confirms.confirmation(
                  'post',
                  '确认执行此操作吗？',
                  window.path.omcs + 'template/updateLable',
                  {
                    labelId: this.labelIds,
                    temId: this.templatId,
                  },
                  () => {
                    this.dialogFormVisible = false
                    this.gettableLIst()
                  }
                )
              } else {
                this.$message({
                  message: '退订格式必须选择！',
                  type: 'warning',
                })
              }
            }
          } else {
            let arr = this.selectId.split(',')
            if (arrlabel.indexOf(55) == -1) {
              this.$confirms.confirmation(
                'post',
                '确认执行此操作吗？',
                window.path.omcs + 'template/updateLable',
                {
                  labelId: this.labelIds,
                  arr: arr,
                },
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              if (
                (arrlabel.indexOf(55) != -1 && arrlabel.indexOf(4) != -1) ||
                (arrlabel.indexOf(55) != -1 && arrlabel.indexOf(5) != -1)
              ) {
                this.$confirms.confirmation(
                  'post',
                  '确认执行此操作吗？',
                  window.path.omcs + 'template/updateLable',
                  {
                    labelId: this.labelIds,
                    arr: arr,
                  },
                  () => {
                    this.dialogFormVisible = false
                    this.gettableLIst()
                  }
                )
              } else {
                this.$message({
                  message: '退订格式必须选择！',
                  type: 'warning',
                })
              }
            }
          }
        } else {
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    resetForm(formop) {
      this.$refs.formop.resetFields()
      this.dialogFormVisible = false
    },
    delAll() {
      this.$confirms.confirmation(
        'get',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'template/batchDelete/' + this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    templaD(id, val) {
      if (val) {
        this.$confirms.confirmation(
          'post',
          '确认变更模板？',
          window.path.omcs + 'template/changeType',
          {
            temId: id,
            temType: val,
          },
          () => {
            this.dialogTemplate = false
            this.gettableLIst()
          }
        )
      } else {
        this.$message({
          message: '请选择修改模板',
          type: 'warning',
        })
      }
    },
    delAllS(val) {
      this.$confirms.confirmation(
        'post',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'template/delete',
        {
          temId: val.temId,
        },
        () => {
          this.gettableLIst()
        }
      )
    },
    xiangqing(val) {
      this.dialogVisible = true
      this.detailsRows = val

      this.detailsList[0].content = this.detailsRows.clientName
      this.detailsList[1].content = this.detailsRows.temId
      if (this.detailsRows.temType == 1) {
        this.detailsList[2].content = '验证码'
      }
      if (this.detailsRows.temType == 2) {
        this.detailsList[2].content = '通知'
      }
      if (this.detailsRows.temType == 3) {
        this.detailsList[2].content = '营销推广'
      }
      if (this.detailsRows.temFormat == 1) {
        this.detailsList[3].content = '变量模板'
      }
      if (this.detailsRows.temFormat == 2) {
        this.detailsList[3].content = '全文模板'
      }
      if (!this.detailsRows.skipParamCheck) {
        this.detailsList[6].content = '是'
      } else {
        this.detailsList[6].content = '否'
      }
      this.detailsList[4].content = this.detailsRows.temName
      this.detailsList[5].content = this.detailsRows.temContent
      this.detailsList[7].content = this.detailsRows.createTime
      this.detailsList[8].content = this.detailsRows.checkName
      this.detailsList[9].content = this.detailsRows.checkTime
      this.detailsList[10].content = this.detailsRows.remark
    },
    handelOptionButton(val) {
      if (val.methods == 'template') {
        this.TemplateObject = val.row
        this.dialogTemplate = true
      }
      if (val.methods == 'xiangqing') {
        this.xiangqing(val)
      }
      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
    handlePass(index, val) {
      this.TemplateObject = val
      this.dialogTemplate = true
    },
    rouTersZ(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.temUserId } })
    },
    userList(row) {
      this.userFlag = true
      this.nameover = row.temId
    },
    checks(val) {
      let skipParamCheck = !val.skipParamCheck
      this.$confirms.confirmation(
        'post',
        '此操作将切换模板ID参数检测，是否继续？',
        window.path.omcs + 'template/skipParamCheck',
        {
          temId: val.temId,
          skipParamCheck: skipParamCheck,
        },
        () => {
          this.gettableLIst()
        }
      )
    },
    handelSelection(val) {
      if (val.records && val.records.length > 0) {
        let arr = []
        val.records.forEach(item => {
          arr.push(item.temId)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    changeHeatStatus(row, val) {
      this.$confirms.confirmation(
        'post',
        '此操作将切换模板ID冷热状态，是否继续？',
        window.path.omcs + 'template/changeHeat',
        {
          temId: row.temId,
          heat: val,
        },
        () => {
          this.gettableLIst()
        },
        () => {
          // 取消操作时恢复原状态
          row.heat = val === 0 ? 1 : 0;
        }
      )
    },
    openImportDialog() {
      this.importDialogVisible = true;
      this.fileList = [];
    },
    importTemplate() {
      this.importDialogVisible = false;
      this.gettableLIst();
    },
    handleUploadSuccess(res, file) {
      console.log('上传结果', res);
      this.Progressflag = false;
      if (res.code === 200) {
        this.$message({
          message: '导入成功',
          type: 'success',
        });

      } else {
        this.$message({
          message: res.msg || '导入失败',
          type: 'error',
        });
      }
    },
    handleUploadError(error) {
      this.Progressflag = false;
      this.$message({
        message: '上传失败：' + error,
        type: 'error',
      });
    },
    handleProgress() {
      this.Progressflag = true;
    },
    handleRemove(file, fileList) {
      console.log('文件移除', file, fileList);
      this.fileList = [];
    },
    beforeUpload(file) {
      console.log(file, 'file');

      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message({
          message: '只能上传Excel文件!',
          type: 'error',
        });
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: '文件大小不能超过10MB!',
          type: 'error',
        });
        return false;
      }
      return true;
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    dialogVisible(val) {
      if (val == false) {
        this.labelType = ''
        this.labelIndustry = ''
        this.labelUnsubscribe = ''
      }
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.setLabel.resetFields()
        this.setLabel.formData.style = []
        this.setLabel.formData.hangye = []
        this.setLabel.formData.tuidinggeshi = ''
      }
    },
    dialogTemplate(val) {
      if (val == false) {
        this.templateVal = ''
      }
    },
  },
}
</script>

<style scoped>
.outer-frame {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.search-form .el-form-item {
  margin-right: 20px;
  margin-bottom: 15px;
}

.input-time {
  width: 220px;
}

.form-buttons {
  margin-top: 10px;
}

.form-buttons .el-button {
  margin-right: 10px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-box {
  margin-top: 20px;
  text-align: right;
  display: flex;
  justify-content: space-between;
}

.link-text {
  color: #16a589;
  cursor: pointer;
}

.link-text:hover {
  text-decoration: underline;
}

.variable-item {
  margin-bottom: 5px;
}

.variable-name {
  color: #f56c6c;
  font-weight: bold;
}

.variable-value {
  color: #67c23a;
  font-weight: bold;
}

.status-enabled {
  color: #67c23a;
}

.status-disabled {
  color: #f56c6c;
}

.details-dialog .el-dialog__body {
  padding: 20px 0;
}

.details-item {
  display: flex;
  padding: 8px 20px;
  font-size: 14px;
  line-height: 1.5;
}

.details-label {
  flex: 0 0 80px;
  font-weight: bold;
}

.details-value {
  flex: 1;
  color: #606266;
  word-break: break-all;
}

.template-details {
  padding: 0 20px;
}

.template-select {
  width: 100%;
}

.know-btn {
  width: 160px;
}

.temp-content {
  max-height: 120px;
  overflow: auto;
  line-height: 1.5;
}
:deep(.temp-content) {
  .prompt {
    color: #f56c6c;
    display: inline-block;
    font-weight: 800;
    /* background: rgba(0, 0, 0, .5); */
    /* text-shadow: 3px 3px 5px #FF0000; */
  }

  .prompt_error {
    color: #e6a23c;
    display: inline-block;
    text-decoration: underline;
    font-weight: 800;
    /* background: rgba(0, 0, 0, .5); */
    /* text-shadow: 3px 3px 5px #FF0000; */
  }
}
.dialog-footer {
  text-align: right;
}
</style>