# 📋 通道重启计划 - 通道全选功能

## 🎯 功能概述

为通道重启计划的通道选择功能添加了全选支持，用户可以一键选择或取消选择所有可用通道，提升操作效率。

## ✨ 新增功能特性

### 📊 主要功能
- **全选/取消全选**: 一键选择或取消选择所有通道
- **半选状态**: 部分选择时显示半选状态（indeterminate）
- **选择计数**: 实时显示已选择通道数量和总数
- **智能禁用**: 在适当的时机禁用全选功能
- **状态同步**: 全选状态与通道选择状态实时同步

### 🎨 UI设计

#### **全选控件布局**
```vue
<div class="channel-select-container">
    <div class="channel-select-header">
        <el-checkbox 
            v-model="selectAllChannels" 
            :indeterminate="isIndeterminate"
            @change="handleSelectAllChange"
            :disabled="!form.operatorType || channelLoading || channelList.length === 0">
            全选通道
        </el-checkbox>
        <span class="channel-count" v-if="channelList.length > 0">
            (已选择 {{ form.channelIds.length }} / {{ channelList.length }} 个通道)
        </span>
    </div>
    <el-select
        v-model="form.channelIds"
        multiple
        @change="handleChannelSelectionChange">
        <!-- 通道选项 -->
    </el-select>
</div>
```

#### **视觉特性**
- **全选框**: 位于通道选择器上方，清晰可见
- **计数显示**: 实时显示选择进度 "已选择 X / Y 个通道"
- **半选状态**: 部分选择时显示半选状态
- **禁用状态**: 无通道或加载中时禁用全选

## 🔧 技术实现

### 1. 数据结构

#### **全选相关状态**
```javascript
// 全选相关数据
const selectAllChannels = ref(false);  // 全选状态
const isIndeterminate = ref(false);    // 半选状态（部分选择）
```

### 2. 核心方法

#### **全选/取消全选处理**
```javascript
const handleSelectAllChange = (checked) => {
    if (checked) {
        // 全选：选择所有通道
        form.channelIds = channelList.value.map(channel => channel.channelCode);
    } else {
        // 取消全选：清空选择
        form.channelIds = [];
    }
    isIndeterminate.value = false;
};
```

#### **通道选择变更处理**
```javascript
const handleChannelSelectionChange = (selectedChannels) => {
    const totalChannels = channelList.value.length;
    const selectedCount = selectedChannels.length;
    
    if (selectedCount === 0) {
        // 没有选择任何通道
        selectAllChannels.value = false;
        isIndeterminate.value = false;
    } else if (selectedCount === totalChannels) {
        // 选择了所有通道
        selectAllChannels.value = true;
        isIndeterminate.value = false;
    } else {
        // 部分选择
        selectAllChannels.value = false;
        isIndeterminate.value = true;
    }
};
```

#### **运营商变更处理**
```javascript
const handleOperatorChange = (operatorType) => {
    // 清空之前选择的通道
    form.channelIds = [];
    channelList.value = [];
    // 重置全选状态
    selectAllChannels.value = false;
    isIndeterminate.value = false;
    
    if (operatorType) {
        getChannelList(operatorType);
    }
};
```

### 3. 状态管理

#### **状态同步逻辑**
| 选择情况 | selectAllChannels | isIndeterminate | 显示效果 |
|----------|-------------------|-----------------|----------|
| 未选择任何通道 | false | false | 空复选框 |
| 选择部分通道 | false | true | 半选状态（-） |
| 选择所有通道 | true | false | 全选状态（✓） |

#### **禁用条件**
全选复选框在以下情况下被禁用：
- 未选择运营商 (`!form.operatorType`)
- 通道列表加载中 (`channelLoading`)
- 通道列表为空 (`channelList.length === 0`)

### 4. 生命周期管理

#### **状态重置时机**
1. **运营商变更时**: 重置全选状态
2. **获取通道列表成功后**: 重置全选状态
3. **表单重置时**: 重置全选状态

```javascript
// 重置全选状态的通用方法
const resetSelectAllState = () => {
    selectAllChannels.value = false;
    isIndeterminate.value = false;
};
```

## 🎨 样式设计

### CSS样式
```css
/* 通道选择容器样式 */
.channel-select-container {
    width: 100%;
}

.channel-select-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
}

.channel-count {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
}
```

### 设计特点
- **背景色**: 浅灰色背景 (#f5f7fa) 区分选择区域
- **边框**: 淡色边框 (#e4e7ed) 定义边界
- **圆角**: 4px圆角提升视觉效果
- **间距**: 8px内边距保证舒适的点击区域
- **字体**: 12px小字体显示计数信息

## 🔄 交互流程

### 1. 全选操作流程
```
点击全选复选框 → 触发handleSelectAllChange → 更新form.channelIds → 设置isIndeterminate为false
```

### 2. 手动选择流程
```
选择/取消通道 → 触发handleChannelSelectionChange → 计算选择状态 → 更新全选和半选状态
```

### 3. 运营商切换流程
```
选择运营商 → 触发handleOperatorChange → 重置选择状态 → 获取新通道列表 → 重置全选状态
```

## 🎯 用户体验优化

### 1. 视觉反馈
- **即时更新**: 选择状态实时反映在全选复选框上
- **计数显示**: 清晰显示选择进度
- **状态区分**: 全选、半选、未选三种状态清晰可辨

### 2. 操作便利性
- **一键操作**: 全选/取消全选只需一次点击
- **智能禁用**: 在不适当的时机自动禁用功能
- **状态保持**: 在合理的范围内保持用户的选择状态

### 3. 错误预防
- **禁用控制**: 防止在无效状态下进行全选操作
- **状态同步**: 确保UI状态与数据状态一致
- **清晰提示**: 通过计数显示让用户了解当前选择情况

## 🚀 使用说明

### 1. 全选所有通道
1. 选择运营商，等待通道列表加载
2. 点击"全选通道"复选框
3. 所有可用通道将被选中

### 2. 取消全选
1. 在已全选状态下，点击"全选通道"复选框
2. 所有通道选择将被清除

### 3. 部分选择
1. 手动选择部分通道
2. 全选复选框将显示半选状态（-）
3. 可以点击全选复选框选择剩余通道

### 4. 查看选择状态
- 在全选复选框右侧查看选择计数
- 格式：(已选择 X / Y 个通道)

## 📋 注意事项

1. **数据字段**: 使用`channelCode`作为通道的唯一标识
2. **状态同步**: 全选状态与手动选择状态实时同步
3. **性能考虑**: 大量通道时全选操作仍然高效
4. **用户体验**: 提供清晰的视觉反馈和操作提示

## 🔧 扩展建议

1. **搜索过滤**: 在搜索过滤后仍支持全选功能
2. **分组全选**: 支持按通道类型或状态分组全选
3. **记忆功能**: 记住用户的选择偏好
4. **快捷键**: 支持Ctrl+A等快捷键全选
5. **批量操作**: 扩展更多批量操作功能

---

**功能完成**: 通道选择现在支持全选功能，大大提升了批量操作的效率和用户体验。
