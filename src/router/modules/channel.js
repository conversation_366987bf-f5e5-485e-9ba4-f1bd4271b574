// 通道管理相关路由
export default [
  {
    path: '/PassagewayMag',
    meta: { title: ' 通道设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/PassagewayMag/PassagewayMag.vue'),
  },
  {
    path: '/channelPool',
    meta: { title: ' 通道切换设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/ChannelPool/ChannelPool.vue'),
  },
  {
    path: '/ChannelGroupLog',
    meta: { title: ' 通道组日志', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/ChannelGroupLog/ChannelGroupLog.vue'),
  },
  {
    path: '/ChannelRouting',
    meta: { title: ' 通道组改写设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/ChannelRouting/ChannelRouting.vue'),
  },
  {
    path: '/channelGroupRule',
    meta: { title: ' 人工通道组规则', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/channelGroupRule/channelGroupRule.vue'),
  },
  {
    path: '/ArtificialMag',
    meta: { title: '旧人工通道组管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/ArtificialMag/ArtificialMag.vue'),
  },
  {
    path: '/ArtificialMagNew',
    meta: { title: '新人工通道组管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/ArtificialMag/ArtificialMagNew.vue'),
  },
  {
    path: '/SpecialChannel',
    meta: { title: ' 个性化通道设置', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/SpecialChannel/SpecialChannel.vue'),
  },
  {
    path: '/ChannelRestartPlan',
    meta: { title: ' 通道重启计划', keepAlive: true, isBack: false },
    component: () => import('@/components/page/OperationMag/PassageWay/ChannelRestartPlan/ChannelRestartPlan.vue'),
  },
] 