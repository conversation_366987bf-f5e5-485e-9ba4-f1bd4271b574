# 路由模块化说明

## 概述

为了提高项目的可维护性和代码组织性，我们对路由进行了模块化拆分。将原来的单一大文件 `router.js` 拆分成了多个按业务功能划分的模块。

## 目录结构

```
src/router/
├── index.js                 # 新的主路由文件，聚合所有模块
├── router.js                # 原主路由文件（已弃用）
├── README.md                # 本说明文件
└── modules/                 # 路由模块目录
    ├── dashboard.js         # 首页相关路由
    ├── user.js              # 用户管理相关路由
    ├── operation.js         # 运营管理相关路由
    ├── audit.js             # 审核管理相关路由
    ├── channel.js           # 通道管理相关路由
    ├── security.js          # 安全检查相关路由
    ├── system.js            # 系统设置相关路由
    ├── statistics.js        # 统计分析相关路由
    ├── task.js              # 任务管理相关路由
    ├── complaint.js         # 投诉管控相关路由
    ├── message.js           # 消息业务相关路由（短信、彩信、视频短信等）
    ├── fiveG.js             # 5G短信相关路由
    ├── yuexin.js            # 阅信相关路由
    ├── shop.js              # 商城相关路由
    ├── sendTask.js          # 发送任务和群发相关路由
    ├── finance.js           # 充值消费相关路由
    ├── push.js              # 推送管理相关路由
    └── errors.js            # 错误页面和特殊页面相关路由
```

## 模块划分原则

1. **按业务功能划分**：将相关的业务功能路由归类到同一模块
2. **职责单一**：每个模块专注于特定的业务领域
3. **便于维护**：模块化后便于团队协作和代码维护
4. **易于扩展**：新增功能时可以直接在对应模块中添加路由

## 模块详细说明

### 核心业务模块

- **user.js**: 用户管理、子用户、批量操作、用户配置等
- **operation.js**: 运营商配置、失败代码、行业类别、通道设置等运营管理功能
- **audit.js**: 各类审核功能，包括认证审核、模板审核、签名审核等
- **message.js**: 短信、彩信、视频短信、国际短信、语音、闪验等消息业务

### 专项功能模块

- **fiveG.js**: 5G短信相关的所有功能
- **yuexin.js**: 阅信相关的所有功能
- **channel.js**: 通道管理、通道组、通道切换等
- **security.js**: 安全检查、敏感词、黑白名单等
- **complaint.js**: 投诉管控、路由设置、频次限制等

### 系统功能模块

- **system.js**: 系统设置、角色权限、菜单管理、日志等
- **statistics.js**: 统计分析、数据报表等
- **dashboard.js**: 首页和基础页面
- **errors.js**: 错误页面和特殊页面

## 使用方法

### 添加新路由

1. 确定新路由所属的业务模块
2. 在对应的模块文件中添加路由配置
3. 如果是新的业务领域，可以创建新的模块文件

### 创建新模块

1. 在 `modules/` 目录下创建新的 `.js` 文件
2. 按照现有模块的格式导出路由数组
3. 在 `index.js` 中导入并添加到路由合并中

### 模块文件格式示例

```javascript
// 业务模块相关路由
export default [
  {
    path: '/example',
    meta: { title: '示例页面', keepAlive: true, isBack: false },
    component: () => import('@/components/page/Example.vue'),
  },
  // ... 更多路由
]
```

## 迁移说明

1. **新项目**: 直接使用 `src/router/index.js` 作为路由配置入口
2. **现有项目**: 需要将路由引入从 `router.js` 改为 `index.js`
3. **兼容性**: 保留原 `router.js` 文件以确保向后兼容

## 注意事项

1. 每个模块都应该有清晰的业务边界
2. 避免模块间的路由重复或冲突
3. 保持路由 meta 信息的一致性
4. 定期清理不使用的路由配置

## 维护指南

- 定期检查模块划分是否合理
- 将相关功能的路由归类到同一模块
- 保持模块大小适中，避免单个模块过大
- 及时更新本文档以反映最新的模块结构 