<template>
  <div>
    <div class="Top_title">
      <span>签名审核asdsadsadsad</span>
    </div>
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="82px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" label-width="82px" prop="consumerName">
            <el-input
              v-model="sensitiveCondition.consumerName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="签名名称" label-width="82px" prop="signature">
            <el-input
              v-model="sensitiveCondition.signature"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" label-width="82px" prop="auditStatus">
            <el-select
              v-model="sensitiveCondition.auditStatus"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间" label-width="82px" prop="time1">
            <el-date-picker
              class="input-w"
              v-model="sensitiveCondition.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande1"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="审核时间" label-width="82px"  prop="time2">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time2"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande2"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div class="boderbottom" style="padding-left: 10px">
            <el-button
              type="primary"
              plain
              @click.prevent="sensitiveQuery()"
              @keyup.enter="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">签名审核列表</span> -->
        <!-- <el-button type="primary"  @click="establishSig()">创建签名</el-button> -->
        <el-button
          type="primary"
          @click="sensitiveBatchDel()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量通过</el-button
        >
        <el-button
          type="primary"
          @click="sensitiveBatchSet()"
          v-if="remarkObj.formData.idArr.length != 0"
          >批量驳回</el-button
        >
      </div>
      <div class="sensitive-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="46"></el-table-column>
          <!-- <el-table-column label="签名ID" width="80">
                            <template #default="scope">{{ scope.row.signatureId }}</template>
                        </el-table-column> -->
          <el-table-column label="用户名称">
            <template v-slot="scope">{{ scope.row.consumerName }}</template>
          </el-table-column>
          <el-table-column label="签名内容">
            <template v-slot="scope">
              <span>{{ scope.row.signature }}</span>
              <span style="color: #f56c6c">{{ scope.row.prompt }}</span>
            </template>
          </el-table-column>
          <el-table-column label="截图" width="80">
            <template v-slot="scope">
              <el-button type="text" @click="lookUp(scope.$index, scope.row)"
                ><el-icon><el-icon-picture /></el-icon>&nbsp;查看</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="申请时间"
            width="140"
          ></el-table-column>
          <el-table-column label="审核状态" width="90">
            <template v-slot="scope">
              <span v-if="scope.row.auditStatus == '1'">待审核</span>
              <span
                v-else-if="scope.row.auditStatus == '2'"
                style="color: #16a589"
                >审核通过</span
              >
              <span v-else style="color: #f56c6c">审核不通过</span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注内容"></el-table-column>
          <el-table-column
            prop="auditReason"
            label="驳回原因"
            width=""
          ></el-table-column>
          <el-table-column label="操作" width="120">
            <template v-slot="scope">
              <!-- <el-button type="text" style="color:#f56c6c;margin-left:0px;" @click="handleEdit(scope.$index, scope.row)"><i class="el-icon-delete"></i>&nbsp;删除</el-button> -->
              <el-button
                type="text"
                style="margin-left: 0px"
                v-if="scope.row.auditStatus == '1'"
                @click="handleAdopt(scope.$index, scope.row)"
                ><el-icon><el-icon-success /></el-icon>&nbsp;通过</el-button
              >
              <el-button
                type="text"
                style="color: orange; margin-left: 0px"
                v-if="scope.row.auditStatus == '1'"
                @click="handleReject(scope.$index, scope.row)"
                ><el-icon><el-icon-error /></el-icon>&nbsp;驳回</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="sensitiveConObj.currentPage"
              :page-size="sensitiveConObj.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            >
            </el-pagination>
          </el-col>
        </template>
      </div>
      <!-- 弹窗（查看图片） -->
      <el-dialog
        :title="titleS"
        v-model="imagesshow"
        width="600px"
        :close-on-click-modal="false"
      >
        <template>
          <el-carousel
            :interval="5000"
            arrow="always"
            :autoplay="false"
            style="height: 560px"
          >
            <el-carousel-item
              style="height: 560px"
              v-for="(item, index) in imgUrl"
              :key="index"
            >
              <img style="width: 100%; max-height: 580px" :src="item" alt="" />
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-dialog>
      <!-- 弹框 （审核 通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="500px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="80px"
          style="padding: 0 28px"
        >
          <el-form-item label="拒绝原因" prop="auditReason">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 新增 弹框 -->
      <el-dialog
        title="创建短信签名"
        v-model="SigDialogVisible"
        width="680px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="signatureFrom.formData"
          :rules="signatureFrom.formRule"
          label-width="80px"
          style="padding-right: 14px"
          ref="signatureFrom"
        >
          <el-form-item
            label="用户名称"
            prop="clientName"
            :rules="
              filter_rules({ required: true, message: '用户名称不能为空！' })
            "
          >
            <el-input v-model="signatureFrom.formData.clientName"></el-input>
          </el-form-item>
          <el-form-item
            label="签名内容"
            prop="signature"
            style="position: relative"
          >
            <el-input v-model="signatureFrom.formData.signature"></el-input>
            <span style="position: absolute; top: 2px; left: 0px">【</span>
            <span style="position: absolute; top: 2px; right: 0px">】</span>
          </el-form-item>
          <el-form-item label="签名类型" class="sig-type" prop="signatureType">
            <el-radio-group v-model="signatureFrom.formData.signatureType">
              <el-radio label="1" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">公司名全称或简称：</span
                >须提供营业执照截图</el-radio
              >
              <el-radio label="2" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">APP名全称或简称：</span
                >须提供任一应用商店的下载链接与该应用商店的后台管理截图</el-radio
              >
              <el-radio label="3" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips"
                  >工信部备案的网站名全称或简称：</span
                >提供域名备案服务商的后台备案截图</el-radio
              >
              <el-radio label="4" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips"
                  >公众号或小程序名全称或简称：</span
                >须提供公众号（小程序）微信开放平台截图</el-radio
              >
              <el-radio label="5" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">商标全称或简称：</span
                >须提供商标注册证书截图</el-radio
              >
              <el-radio label="6" style="padding-bottom: 6px"
                ><span class="sig-type-title-tips">其他</span></el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="上传图片"
            v-if="signatureFrom.formData.signatureType != 6"
          >
            <file-upload
              style="display: inline-block"
              :action="actionUrl"
              :limit="3"
              listType="picture"
              tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M"
              :fileStyle="fileStyle"
              :del="del1"
              :showfileList="true"
              @fileup="fileup"
              @fileupres="fileupres"
              >选择上传文件</file-upload
            >
          </el-form-item>
          <el-form-item label="备注内容" prop="remark">
            <el-input
              type="textarea"
              v-model="signatureFrom.formData.remark"
              placeholder="请输入备注内容"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button
              type="primary"
              @click="signature_add('signatureFrom', 'signatureFrom.title')"
              >确 定</el-button
            >
            <el-button @click="SigDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 编辑弹框 -->
    </div>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload' //文件上传
import TableTem from '@/components/publicComponents/TableTem'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    TableTem,
    FileUpload,
    ElIconPicture,
    ElIconSuccess,
    ElIconError,
  },
  name: 'SignatureAudit',
  data() {
    return {
      actionUrl: window.path.smcs + 'signatureaudit/uploadFile',
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      SigDialogVisible: false, //弹出框创建签名显示隐藏
      remarkshow: false,
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          idArr: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: '请输入拒绝原因', trigger: 'blur' },
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      idArrs: [], // 驳回的id
      title: '',
      signatureFrom: {
        title: '',
        formData: {
          signature: '',
          clientName: '',
          signatureType: '1',
          remark: '',
          imgUrl: '',
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: '该输入项为必填项!', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
          ],
          signatureType: [
            { required: true, message: '请选择签名类型', trigger: 'change' },
          ],
          imgUrl: [
            { required: true, message: '请选择上传图片', trigger: 'change' },
          ],
        },
        signature: '', //签名
        signatureId: '', //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        consumerName: '',
        signature: '',
        time1: [],
        time2: [],
        auditStatus: '1',
        auditUsername: '',
        createStartTime: '',
        createEndTime: '',
        auditStartTime: '',
        auditEndTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        consumerName: '',
        signature: '',
        time1: [],
        time2: [],
        auditStatus: '1',
        auditUsername: '',
        createStartTime: '',
        createEndTime: '',
        auditStartTime: '',
        auditEndTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
    }
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.smcs + 'signatureaudit/page',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.total = res.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    sensitiveQuery() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.createStartTime = ''
      this.sensitiveCondition.createEndTime = ''
      this.sensitiveCondition.auditStartTime = ''
      this.sensitiveCondition.auditEndTime = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.createStartTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.createEndTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.createStartTime = ''
        this.sensitiveCondition.createEndTime = ''
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.auditEndTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.auditStartTime = ''
        this.sensitiveCondition.auditEndTime = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
    },
    //查看图片
    lookUp(index, row) {
      this.imgUrl = []
      if (row.signatureType == '1') {
        this.titleS = '签名类型：公司名称（营业执照截图）'
      } else if (row.signatureType == '2') {
        this.titleS = '签名类型：APP名称（下载链接截图及后台管理截图）'
      } else if (row.signatureType == '3') {
        this.titleS = '签名类型：网站名称（后台备案截图）'
      } else if (row.signatureType == '4') {
        this.titleS = '签名类型：公众号或小程序名称（微信开放平台截图）'
      } else if (row.signatureType == '5') {
        this.titleS = '签名类型：商标名称（商标名称注册证书截图）'
      }
      window.api.get(
        window.path.smcs + 'signatureaudit/imgUrl/' + row.signatureId,
        {},
        (res) => {
          if (res != '') {
            let aa = res.split(',')
            let bb = []
            console.log(aa)
            for (let i = 0; i < aa.length; i++) {
              if (aa[i] != '') {
                bb.push(window.path.imgU + aa[i])
              }
            }
            console.log(bb)
            this.imgUrl = bb
            this.imagesshow = true
          } else {
            this.$message({
              message: '无图片',
              type: 'warning',
            })
          }
        }
      )
    },
    //删除
    handleEdit(index, row) {
      this.$confirms.confirmation(
        'get',
        '此操作将永久删除该条数据, 是否继续?',
        window.path.smcs + 'signatureaudit/delete/' + row.signatureId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //通过
    handleAdopt(index, row) {
      this.$confirms.confirmation(
        'post',
        '此操作不可逆，是否继续?',
        window.path.smcs + 'signatureaudit/auditPass',
        { idArr: [row.signatureId] },
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //批量通过
    sensitiveBatchDel() {
      let status = true
      //判断选项中是否有审核过的状态
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != '1') {
          status = false
          break
        }
      }
      if (status) {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.smcs + 'signatureaudit/auditPass',
          { idArr: this.remarkObj.formData.idArr },
          (res) => {
            this.getTableDtate()
            this.remarkObj.formData.idArr = []
          }
        )
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //驳回
    handleReject(index, row) {
      this.idArrs = []
      this.remarkshow = true
      this.title = '驳回备注'
      this.idArrs.push(row.signatureId) //赋值ID
      this.remarkObj.formData.auditReason = row.prompt
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let aa = {}
          if (this.title == '驳回备注') {
            Object.assign(aa, this.remarkObj.formData)
            aa.idArr = this.idArrs
          } else {
            this.operationFlag = false //操作进行中（按钮的显示）
            Object.assign(aa, this.remarkObj.formData)
          }
          this.$confirm('此操作不可逆，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
          })
            .then(() => {
              window.api.post(
                window.path.smcs + 'signatureaudit/auditNotPass',
                aa,
                (res) => {
                  this.getTableDtate()
                  this.remarkshow = false
                  this.remarkObj.formData.idArr = [] //置空ID
                  if (res.code == 200) {
                    this.$message({
                      type: 'success',
                      duration: '2000',
                      message: '操作成功!',
                    })
                  } else {
                    this.$message({
                      type: 'error',
                      duration: '2000',
                      message: res.msg,
                    })
                  }
                }
              )
            })
            .catch(() => {
              this.operationFlag = true
              this.$message({
                type: 'info',
                duration: '2000',
                message: '已取消操作!',
              })
            })
          // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.smcs + 'signatureaudit/auditNotPass', aa,res =>{
          //         this.getTableDtate();
          //         this.remarkshow = false;
          //         this.remarkObj.formData.idArr=[]; //置空ID
          //     });
        } else {
          return false
        }
      })
    },
    //批量驳回
    sensitiveBatchSet() {
      let status = true
      //判断选项中是否有审核过的状态
      this.title = '批量驳回备注'
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != '1') {
          status = false
          break
        }
      }
      if (status) {
        this.remarkshow = true
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let auditStatus = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].signatureId)
        //批量操作列表选中项的审核状态
        auditStatus.push(val[i].auditStatus)
      }
      this.auditStatus = auditStatus //选中项的审核状态
      this.remarkObj.formData.idArr = selectId //批量通过，不通过的id数组
    },
    //创建签名
    establishSig() {
      this.SigDialogVisible = true
      this.del1 = true
    },
    //提交新增签名的表单
    signature_add(formName, title) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {}
          Object.assign(formDates, this.signatureFrom.formData)
          if (this.signatureFrom.formData.signatureType != 6) {
            if (this.signatureFrom.formData.imgUrl != '') {
              this.sendRequest(
                'post',
                '确定新增签名',
                window.path.smcs + 'signatureaudit/addResourceSignature',
                formDates
              )
            } else {
              this.$message({
                message: '请上传图片！',
                type: 'warning',
              })
            }
          } else {
            this.sendRequest(
              'post',
              '确定新增签名',
              window.path.smcs + 'signatureaudit/addResourceSignature',
              formDates
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //发送新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      window.api.get(
        window.path.smcs +
          'signatureaudit/findModelBySignature/' +
          this.signatureFrom.formData.signature,
        {},
        (res) => {
          if (res.code == 200 && res.data == '0') {
            //发送新增的请求
            this.$confirms.confirmation(
              type,
              title,
              action,
              formDates,
              (res) => {
                this.getTableDtate() //重载列表
                this.SigDialogVisible = false //隐藏弹窗
              }
            )
          } else {
            this.$message({
              message: '签名已存在，切勿重复！',
              type: 'warning',
            })
          }
        }
      )
    },
    //移除文件
    fileup(val) {
      let aa = this.signatureFrom.formData.imgUrl.split(',')
      aa.splice(aa.indexOf(val), 1)
      this.signatureFrom.formData.imgUrl = aa.join(',')
    },
    //文件上传成功
    fileupres(val) {
      this.signatureFrom.imgUrl.push(val)
      this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(',')
    },
  },
  watch: {
    //监听查询框对象的变化
    sensitiveConObj: {
      handler(val) {
        this.getTableDtate()
      },
      deep: true,
      immediate: true,
    },
    //监听设置标签弹框是否关闭
    setLabelDialog(val) {
      if (this.$refs.setLabel) {
        if (val == false) {
          this.$refs.setLabel.resetFields()
        }
      }
    },
    //监听创建签名弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.signatureFrom.imgUrl = []
        this.signatureFrom.formData.imgUrl = ''
        this.del1 = false
        this.$refs.signatureFrom.resetFields() //清空表单
      }
    },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true
      } else {
        this.disabledinput = false
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.operationFlag = true //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.idArr = [] //置空ID
          this.getTableDtate()
        }
      }
    },
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.sensitiveQuery()
      }
    }
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>
