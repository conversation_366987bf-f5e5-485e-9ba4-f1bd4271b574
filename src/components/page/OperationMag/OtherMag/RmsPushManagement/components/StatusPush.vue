<template>
  <div class="login_cell_phone">
    <div class="OuterFrame fillet" style="height: 100%">
      <div>
        <el-form
          :inline="true"
          ref="formInline"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item label="用户名称" prop="clientName">
            <el-input
              v-model="formInline.clientName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="发送内容"  prop="content">
                                <el-input v-model="formInline.content" placeholder="" class="input-w"></el-input>
                            </el-form-item> -->
          <!-- <el-form-item label="手机号"  prop="mobile">
                                <el-input v-model="formInline.mobile" placeholder="" class="input-w"></el-input>
                            </el-form-item>
                            <el-form-item label="消息ID"  prop="msgid">
                                <el-input v-model="formInline.msgid" placeholder="" class="input-w"></el-input>
                            </el-form-item> -->
          <el-form-item label="状态重推时间" prop="time">
            <el-date-picker
              v-model="formInline.time"
              type="datetimerange"
              format="YYYY-MM-DD HH:MM:ss"
              value-format="YYYY-MM-DD HH:MM:ss"
              range-separator="至"
              @change="timeClick"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="ListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reset('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table" style="padding-bottom: 40px;">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="RmsPushManagementStatusPush"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>{{ scope.row.clientName }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="发送内容">
                                <template #default="scope">{{ scope.row.content}}</template>
                            </vxe-column>   -->
          <vxe-column width="170" field="推送状态" title="推送状态">
            <template v-slot="scope">
              <el-tag
                :disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                初始
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
              >
                处理中
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == 2"
                type="success"
                effect="dark"
              >
                处理完成
              </el-tag>
              <el-tag
                :disable-transitions="true" v-else type="danger" effect="dark"> 处理异常 </el-tag>
              <!-- <span v-if="scope.row.status==0">初始</span>
                                    <span v-else-if="scope.row.status==1">处理中</span>
                                    <span v-else-if="scope.row.status==2">处理完成</span>
                                    <span v-else>处理异常</span> -->
            </template>
          </vxe-column>
          <vxe-column field="推送数量" title="推送数量">
            <template v-slot="scope">
              <div>{{ scope.row.number }}</div>
            </template>
          </vxe-column>
          <vxe-column width="170" field="推送总数" title="推送总数">
            <template v-slot="scope">
              <div>{{ scope.row.total }}</div>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="180">
            <template v-slot="scope">
              <div>{{ scope.row.sendTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态重推时间" title="状态重推时间" width="180">
            <template v-slot="scope">
              <div>{{ scope.row.createTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作人" title="操作人">
            <template v-slot="scope">
              <div>{{ scope.row.operatorName }}</div>
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInlines.currentPage"
            :page-size="formInlines.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  name: 'ProcessingQueue',
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      customConfig: {
        storage: true
      },
      name: 'ProcessingQueue',
      //复选框值
      selectId: '',
      isFirstEnter: false,
      // 搜索数据
      formInline: {
        clientName: '',
        content: '',
        sendBeginTime: '',
        sendEndTime: '',
        time: [],
        productId: '3',
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        clientName: '',
        content: '',
        sendBeginTime: '',
        sendEndTime: '',
        time: [],
        productId: '3',
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'servicepushrecord/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      ;(this.formInline.time = []), (this.formInline.sendBeginTime = '')
      this.formInline.sendEndTime = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 定时时间
    timeClick(val) {
      if (val) {
        this.formInline.sendBeginTime = this.moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.formInline.sendEndTime = this.moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline.sendBeginTime = ''
        this.formInline.sendEndTime = ''
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
