// 发送任务和群发相关路由
export default [
  {
    path: '/similarUsers',
    meta: { title: ' 定时账号管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/similarUsers.vue'),
  },
  {
    path: '/webtaskgroup',
    meta: { title: '发送活动', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/sendGroup/webtaskgroup.vue'),
  },
  {
    path: '/webtaskpool',
    meta: { title: '号码池管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/sendGroup/webtaskpool.vue'),
  },
  {
    path: '/webSendRecord',
    meta: { title: '发送计划明细', keepAlive: true, isBack: false },
    component: () => import('@/components/page/ManualAudit/sendGroup/sendRecord.vue'),
  },
  {
    path: '/timing',
    meta: { title: ' 接口定时管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/timingMag/timing.vue'),
  },
  {
    path: '/sendAssignment',
    meta: { title: '触发设置列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/manageVip/sendAssignment.vue'),
  },
  {
    path: '/manageSendList',
    meta: { title: '发送任务列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/manageVip/manageSendList.vue'),
  },
] 