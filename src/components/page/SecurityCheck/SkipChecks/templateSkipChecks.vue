<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.username"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="Query">查询</el-button>
            <el-button type="primary" plain @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">创建</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="templateSkipChecks"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <!-- <vxe-column type="selection" width="46"></vxe-column> -->
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="材料证明" title="材料证明">
            <template v-slot="scope">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.proof"
                :preview-src-list="[scope.row.proof]"
                :append-to-body="true"
                                    :preview-teleported="true"
              >
              </el-image>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态">
            <template v-slot="scope">
              <el-tag
                :disable-transitions="true" type="success" v-if="scope.row.status == 0">正常</el-tag>
              <el-tag
                :disable-transitions="true" type="info" v-if="scope.row.status == 1">已删除</el-tag>
              <!-- <el-tag
                :disable-transitions="true" type="info" v-if="scope.row.status == 2">待审核</el-tag> -->
            </template>
          </vxe-column>
          <vxe-column field="过期时间" title="过期时间">
            <template v-slot="scope">
              <div v-if="scope.row.expireTime">{{
                moment(scope.row.expireTime).format('YYYY-MM-DD')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template v-slot="scope">
              <el-button link style="color: #409EFF;" @click="edit(scope.row)">编 辑</el-button>
              <el-button
                link
                style="color: red"
                @click="cancel(scope.row)"
                >删 除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增 -->
      <el-dialog
        :title="titleMap"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="650px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          label-width="120px"
          ref="formop"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              class="input-w"
              :disabled="titleMap == '编辑不检测证明授权' ? true : false"
              v-model="formop.username"
              autocomplete="off"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>

          <el-form-item label="有效期" prop="expireTime">
            <el-date-picker
              class="input-w"
              v-model="formop.expireTime"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="材料证明" prop="proof">
            <el-upload
              class="upload-demo"
              :action="action"
              :headers="token"
              :limit="1"
              :before-upload="handleUpload"
              :on-remove="handleRemove"
              :on-success="handleSuccess"
              :file-list="fileList"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <template v-slot:tip>
                <div class="el-upload__tip">
                  上传文件只能是 pdf、doc、jpg、jpeg、docx、格式!
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
          </div>
        </template>
      </el-dialog>
      <!-- 新增 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../publicComponents/TableTem.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
export default {
  components: {
    TableTem
  },
  name: 'ShuntSetting',
  data() {
    var numberRules = (rule, value, callback) => {
      if (!/^([0-9][0-9]{0,3}|100)$/.test(value)) {
        return callback(new Error('请输入0-100的数字'))
      } else {
        callback()
      }
    }
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      titleMap: '创建不检测证明授权',
      isFirstEnter: false,
      dialogFormVisible: false, //新增弹出框显示隐藏
      ids: '',
      selectId: [],
      action: window.path.cpus + 'v3/file/uploadFile',
      token: {},
      formop: {
        username: '',
        expireTime: '', //有效期
        proof: '', //材料证明
        id: '',
      },
      fileList: [],
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'change' },
          // {
          //     min: 1,
          //     max: 20,
          //     message: "长度在 1 到 20 个字符",
          //     trigger: "blur"
          // },
        ],
        expireTime: [
          { required: true, message: '请输入有效期', trigger: 'change' },
        ],
        // successRatioLow: [
        //     { required: true, message: "请输入成功率最大值", trigger: "change" },
        // ],
        // errorCode: [
        //     { required: true, message: "请输入失败代码", trigger: "change" },
        // ],
      },
      formInline: {
        username: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    }
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingclienttemplateskipcheck/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handleUpload(file, fileList) {
      const siJPGGIF = file.name.split('.')[file.name.split('.').length - 1]
      const fileType = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning('上传文件只能是 pdf、doc、jpg、jpeg、docx、格式!')
        return false
      }
    },
    handleSuccess(res, file) {
      if (res.code == 200) {
        this.formop.proof = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.formop.proof = ''
    },
    // 新增 编辑
    addopt() {
      this.titleMap = '创建不检测证明授权'
      this.dialogFormVisible = true
    },
    edit(val) {
      this.titleMap = '编辑不检测证明授权'
      this.dialogFormVisible = true
      this.formop.id = val.id
      this.formop.username = val.username
      this.formop.expireTime = val.expireTime || ''
      this.formop.proof = val.proof || ''
      if (val.proofUrl) {
        this.fileList = [
          {
            name: '材料',
            url: val.proofUrl,
          },
        ]
      }
    },
    // handleTime(e) {
    //     this.formop.expireTime = moment(e).format('YYYY-MM-DD HH:mm:ss')
    // },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.titleMap == '创建不检测证明授权') {
            let data = {
              username: this.formop.username,
              expireTime: moment(this.formop.expireTime).format('YYYY-MM-DD'),
              proof: this.formop.proof,
            }
            window.api.post(
              window.path.omcs + 'operatingclienttemplateskipcheck',
              data,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false
                  this.$message({
                    type: 'success',
                    duration: 2000,
                    message: '新增成功！',
                  })
                  this.gettableLIst()
                } else {
                  this.$message({
                    type: 'error',
                    duration: 2000,
                    message: res.msg,
                  })
                }
              }
            )
          } else {
            let data = {
              username: this.formop.username,
              expireTime: moment(this.formop.expireTime).format('YYYY-MM-DD'),
              proof: this.formop.proof,
              id: this.formop.id,
            }
            window.api.put(
              window.path.omcs + 'operatingclienttemplateskipcheck',
              data,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false
                  this.$message({
                    type: 'success',
                    duration: 2000,
                    message: '编辑成功！',
                  })
                  this.gettableLIst()
                } else {
                  this.$message({
                    type: 'error',
                    duration: 2000,
                    message: res.msg,
                  })
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 删除
    cancel(val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs + 'operatingclienttemplateskipcheck/' + val.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
    //批量
    handleSelectionChange(val) {
      this.selectId = val.map((item, index) => {
        return item.id
      })
      this.ids = this.selectId.join(',')
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
      this.token = {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      }
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
        this.formop.id = ''
        this.fileList = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
