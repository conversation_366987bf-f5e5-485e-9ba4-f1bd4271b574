<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form
        label-width="80px"
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName">
          <el-input class="input-w" v-model="ruleForm.fileName"></el-input>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    
    <div style="padding: 0 10px">
      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="pictureAudit"
        border
        stripe
        show-overflow
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        :cell-config="{height: 100}"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="用户名称" title="用户名称">
          <template v-slot="scope">
            <div>
              {{ scope.row.username }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="文件名称" title="文件名称">
          <template v-slot="scope">
            <div>
              {{ scope.row.fileName }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="文件类型" title="文件类型">
          <template v-slot="scope">
            <div v-if="scope.row.fileType == 1">图片</div>
            <div v-if="scope.row.fileType == 2">视频</div>
            <div v-if="scope.row.fileType == 3">音频</div>
          </template>
        </vxe-column>
        <vxe-column field="素材内容" title="素材内容" width="180">
          <template v-slot="scope">
            <div
              style="margin: 10px 0; width: 100px; height: 70px"
              v-if="scope.row.fileType == 1"
            >
              <img
                v-if="scope.row.fileUrl"
                :src="scope.row.fileUrl"
                alt=""
                style="width: 100%"
              />
            </div>
            <div
              style="margin: 10px 0; width: 150px; height: 70px"
              v-if="scope.row.fileType == 2"
            >
              <video
                v-if="scope.row.fileUrl"
                style="width: 100%; height: 70px"
                class="avatar video-avatar"
                controls="controls"
                :src="scope.row.fileUrl"
              ></video>
            </div>
            <div
              style="margin: 10px 0; width: 150px; height: 70px"
              v-if="scope.row.fileType == 3"
            >
              <audio
                v-if="scope.row.fileUrl"
                style="width: 100%"
                controls="controls"
                preload="auto"
                :src="scope.row.fileUrl"
              ></audio>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="审核状态" title="审核状态">
          <template v-slot="scope">
            <!-- <span style="color: red" v-if="scope.row.status == 0">审核中</span>
                <span style="color: red" v-if="scope.row.status == 5"
                  >运营商审核中</span
                >
                <span style="color: #409eff" v-if="scope.row.status == 10"
                  >审核通过</span
                >
                <span style="color: red" v-if="scope.row.status == 20"
                  >审核失败</span
                > -->
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.status == 0"
            >
              审核中
            </div>
            <div
              class="item_status"
              style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              "
              v-if="scope.row.status == 10"
            >
              审核通过
            </div>
            <div
              class="item_status"
              style="
                background: #ffe7e7;
                color: #ff6666;
                border: 1px solid #fac2c2;
              "
              v-if="scope.row.status == 20"
            >
              审核失败
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="100">
          <template v-slot="scope">
            <p v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time">{{
                  moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss")
                }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" width="200">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.status == 10"
              link
              style="color: #16a589; margin-left: 0px"
              @click="checkContent(scope.row)"
              ><el-icon><SuccessFilled /></el-icon>查看素材</el-button
            >
            <el-button
              v-else
              link
              style="color: #e6a23c; margin-left: 0px"
              @click="checkContent(scope.row)"
              ><el-icon><WarningFilled /></el-icon>素材审核</el-button
            >
            <el-button link @click="picAudit(scope.row)"
              style="color: #409eff; margin-left: 0px"
              >素材报备</el-button
            >
          </template>
        </vxe-column>
      </vxe-table>

      <div class="paginationBox">
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableDataObj.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :title="'素材审核' + '-' + title"
      v-model="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <!-- <span>文件名称：</span> <span>{{ detial.fileName }}</span> -->
      <div style="margin: 10px 0" v-if="detial.fileType == 1">
        <img
          v-if="detial.fileUrl"
          :src="detial.fileUrl"
          alt=""
          style="width: 100%"
        />
      </div>
      <div style="margin: 10px 0" v-if="detial.fileType == 2">
        <video
          v-if="detial.fileUrl"
          style="width: 100%; height: 300px"
          class="avatar video-avatar"
          controls="controls"
          :src="detial.fileUrl"
        ></video>
      </div>
      <div style="margin: 10px 0" v-if="detial.fileType == 3">
        <audio
          v-if="detial.fileUrl"
          style="width: 100%"
          controls="controls"
          preload="auto"
          :src="detial.fileUrl"
        ></audio>
      </div>
      <div>
        <span v-if="detial.sensitiveInformation">敏感词提醒：</span
        ><span style="color: red">{{ detial.sensitiveInformation }}</span>
      </div>
      <div style="display: flex; margin: 10px 0" v-if="detial.status == 0">
        <el-form
          :model="reasonFrom"
          :inline="true"
          :rules="rules"
          ref="reasonFrom"
          class="demo-reasonFrom"
        >
          <el-form-item label="审核原因：" prop="reason">
            <el-input
              :maxlength="20"
              show-word-limit
              type="textarea"
              placeholder="不能超过20个字符"
              style="width: 400px"
              v-model="reasonFrom.reason"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div
        v-else
        style="
          width: 100%;
          background: #f2f5fa;
          line-height: 20px;
          display: flex;
          margin-top: 10px;
        "
      >
        <div style="padding: 5px">
          <div>
            <span>审核状态：</span>
            <span v-if="detial.status == 0">审核中</span>
            <span style="color: red" v-if="detial.status == 5"
              >运营商审核中</span
            >
            <span style="color: #409eff" v-if="detial.status == 10"
              >审核通过</span
            >
            <span style="color: red" v-if="detial.status == 20">审核失败</span>
          </div>
          <div>
            <span>审核人：</span>
            <span>{{ detial.auditUser }}</span>
          </div>
        </div>
        <div style="padding: 5px; margin-left: 10px">
          <div>
            <span>审核原因：</span>
            <span>{{ detial.auditReason }}</span>
          </div>
          <div>
            <span>审核时间：</span>
            <span v-if="detial.auditTime">{{
              moment(detial.auditTime).format('YYYY-MM-DD HH:mm:ss')
            }}</span>
          </div>
        </div>
      </div>

      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            v-if="detial.status == 0"
            type="primary"
            @click="audit(detial, 1, '通过', 'reasonFrom')"
            >通过</el-button
          >
          <el-button
            v-if="detial.status == 0"
            type="danger"
            style="margin-left: 10px"
            @click="audit(detial, 2, '不通过', 'reasonFrom')"
            >不通过</el-button
          >
          <!-- <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  components: {
    // ElIconLxEmoji,
    // ElIconSuccess,
    // ElIconWarning,
  },
  name: 'pictureAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      status: '',
      title: '',
      reason: '',
      dialogVisible: false,
      codeFlag: true,
      ruleForm: {
        fileName: '',
        username: '',
        currentPage: 1,
        pageSize: 10,
      },
      reasonFrom: {
        reason: '',
      },
      rules: {
        reason: [
          { required: true, message: '审核原因不能为空', trigger: 'blur' },
        ],
      },
      tabelAlllist: {
        fileName: '',
        username: '',
        currentPage: 1,
        pageSize: 10,
      },
      detial: {},
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      titFlag: true,
    }
  },
  created() {
    if (this.$route.query.fileName || this.$route.query.username) {
      this.ruleForm.fileName = this.$route.query.fileName
      this.ruleForm.username = this.$route.query.username
      Object.assign(this.tabelAlllist, this.ruleForm)
    }
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      console.log(this.$route, 'this.$route.query')
      this.$nextTick(() => {
        if (this.$route.query.fileName || this.$route.query.username) {
          this.ruleForm.fileName = this.$route.query.fileName
          this.ruleForm.username = this.$route.query.username
          Object.assign(this.tabelAlllist, this.ruleForm)
        }
        this.getchatList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/material/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
          // console.log(res, "res");
        }
      )
    },
    checkContent(row) {
      this.title = row.fileName
      this.dialogVisible = true
      // console.log(row);
      this.detial = row
    },
    picAudit(row) {
      localStorage.setItem('fileList', JSON.stringify(row))
      // console.log(row);
      this.$router.push({
        path: '/pictureChannel',
        query: {
          materialId: row.id,
          username: row.username,
          fileName: row.fileName,
        },
      })
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.ruleForm.username = ''
      this.ruleForm.fileName = ''
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    checked(row) {
      this.dialogVisible = true
      this.registerFrom = row
      this.registerFrom.renewed = row.renewed + ''
      this.registerFrom.industryTypeCode = row.industryTypeCode * 1
      this.status = row.customerStatus
      //   console.log(this.registerFrom );
      if (row.code) {
        this.codeFlag = true
      } else {
        this.codeFlag = false
      }
    },
    audit(row, val, tit, from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            `确认审核${tit}素材吗？`,
            window.path.fiveWeb + '/operator/material/audit',
            {
              audit: val,
              reason: this.reasonFrom.reason,
              materialId: row.id,
            },
            (res) => {
              if (res.code == 200) {
                this.dialogVisible = false
                this.getchatList()
                // this.$message({
                //   type: 'success',
                //   message: res.msg,
                // })
                if (val == 1) {
                  this.$router.push({
                    path: '/pictureChannel',
                    query: { materialId: row.id },
                  })
                }
              } else {
                // this.$message({
                //   type: 'error',
                //   message: res.msg,
                // })
              }
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        if (this.$refs['reasonFrom']) this.$refs['reasonFrom'].resetFields()
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>


<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
