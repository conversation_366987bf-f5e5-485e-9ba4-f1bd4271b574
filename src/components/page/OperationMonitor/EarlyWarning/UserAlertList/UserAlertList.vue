<template>
  <div>
    <div class="Top_title">
      <span>用户预警</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          label-width="82px"
          ref="queryForm"
        >
          <el-form-item label="状态" label-width="42px" prop="warnStatus">
            <el-select
              v-model="formInline.warnStatus"
              clearable
              placeholder="全部"
              class="input-w"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="待处理" value="1"></el-option>
              <el-option label="已处理" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间" label-width="82px" prop="createTime">
            <el-date-picker
              class="input-w"
              v-model="formInline.createTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="用户名" label-width="82px" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名" label-width="82px" prop="compName">
            <el-input
              v-model="formInline.compName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom" style="padding-left: 40px">
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload('queryForm')"
          >重置</el-button
        >
      </div>
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="Signature-list-header">用户预警列表</span> -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
        ></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="searchInput.currentPage"
              :page-size="searchInput.pagesize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalSize"
            >
            </el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>
      <!-- 弹窗（点击操作） -->
      <el-dialog title="处理预警" v-model="dialogFormVisible" width="560px">
        <el-form
          :model="handelAlert.formData"
          :rules="handelAlert.formRule"
          ref="handelAlert"
          label-width="130px"
        >
          <el-form-item label="选择处理状态" prop="warnStatus">
            <el-select
              v-model="handelAlert.formData.warnStatus"
              placeholder="请选择处理状态"
            >
              <el-option label="提醒我" value="1"></el-option>
              <el-option label="已处理" value="2"></el-option>
            </el-select>
          </el-form-item>
          <div class="tips" v-if="handelAlert.formData.warnStatus == 1">
            <p>选择提醒我，会根据设置的时间推送到指定位置</p>
            <el-radio-group
              v-model="handelAlert.formData.remindFlag"
              size="default"
              style="margin-left: 30px; margin-top: 10px"
            >
              <el-radio-button label="1"
                ><el-icon><el-icon-bell /></el-icon
                >1小时后提醒我</el-radio-button
              >
              <el-radio-button label="2"
                ><el-icon><el-icon-bell /></el-icon
                >2小时后提醒我</el-radio-button
              >
              <el-radio-button label="3"
                ><el-icon><el-icon-bell /></el-icon
                >明天早上十点提示我</el-radio-button
              >
            </el-radio-group>
          </div>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              @click="handelAlert_cancel('handelAlert')"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              type="primary"
              @click="handelAlert_ok('handelAlert')"
              >确 认</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
export default {
  components: {
    TableTem,
    ElIconBell,
  },
  name: 'UserAlertList',
  data() {
    return {
      handelAlert: {
        //处理预警--弹窗
        formData: {
          id: '',
          warnStatus: '', //处理状态
          remindFlag: 1, //时间
        },
        formRule: {
          //验证规则
          warnStatus: [
            { required: true, message: '请选择处理状态', trigger: 'change' },
          ],
        },
      },
      dialogFormVisible: false, //处理预警弹窗显示状态
      //  pagesize:'',//每页大小
      //  currentPage:'',//当前页
      totalSize: 0, //总条数
      searchInput: {
        //搜索条件改变
        warnStatus: '',
        createTime: '',
        startTime: '',
        endTime: '',
        pageSize: 10,
        currentPage: 1,
        userName: '',
        compName: '',
      },
      formInline: {
        warnStatus: '',
        createTime: '',
        startTime: '',
        endTime: '',
        userName: '',
        compName: '',
      },
      tableDataObj: {
        //列表数据
        tableData: [],
        tableLabel: [
          {
            prop: 'createTime',
            showName: '发生时间',
            fixed: false,
            width: 150,
          },
          {
            prop: 'userName',
            showName: '用户名',
            width: 200,
            fixed: false,
          },
          {
            prop: 'compName',
            showName: '公司名称',
            fixed: false,
            width: 200,
          },
          {
            prop: 'warnType',
            showName: '预警类型',
            fixed: false,
            width: 150,
            formatData: function (val) {
              let type = ''
              if (val == 1) {
                //1.单号发送超限 2.发送比例异常 3.成功率低 4.回执率低
                type = '单号发送超限'
              } else if (val == 2) {
                type = '发送比例异常'
              } else if (val == 3) {
                type = '成功率低'
              } else if (val == 4) {
                type = '回执率低'
              }
              return type
            },
          },
          {
            prop: 'warnStatus',
            showName: '状态',
            fixed: false,
            width: 100,
            formatData: function (val) {
              return val == '1' ? '待处理' : '已处理'
            },
          },
          {
            prop: 'content',
            showName: '内容',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '240', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'warnStatus', //关联的表格属性
            contactData: '1', //关联的表格属性-值
            optionName: '操作', //按钮的显示文字
            optionMethod: 'operate', //按钮的方法
            icon: 'el-icon-setting', //按钮图标
          },
        ],
      },
    }
  },
  watch: {
    searchInput: {
      deep: true,

      handler() {
        this.getTableData(this.searchInput) //刷新表格
      },

      //   deep:true,
      immediate: true,
    },
    dialogFormVisible(val) {
      if (val == false) {
        //关闭弹窗------清空表单内容
        this.handelAlert.formData.warnStatus = ''
        this.handelAlert.formData.id = ''
        this.handelAlert.formData.remindFlag = 1
        this.$refs.handelAlert.resetFields() //验证置空
      }
    },
  },
  methods: {
    Query() {
      for (let key in this.formInline) {
        this.searchInput[key] = this.formInline[key]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData(this.searchInput)
    },
    Reload(formName) {
      this.formInline.startTime = ''
      this.formInline.endTime = ''
      this.$refs[formName].resetFields() //清空查询表单
      for (let k in this.formInline) {
        this.searchInput[k] = this.formInline[k]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
    },
    /* --------------- 列表展示 ------------------*/
    getTableData(formDatas) {
      //获取列表数据
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingclientwarn/page',
        formDatas,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.totalSize = res.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    handelOptionButton: function (val) {
      if (val.methods == 'operate') {
        this.handelAlert.formData.id = val.row.id
        this.dialogFormVisible = true
      }
    },
    handelAlert_ok(formName) {
      //表单数据验证
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //发送请求--"操作"的请求
          if (this.handelAlert.formData.warnStatus == 2) {
            //已处理
            this.handelAlert.formData.remindFlag = ''
          }
          //发送请求
          this.$confirms.confirmation(
            'put',
            '确定改变状态？',
            window.path.omcs + 'operatingclientwarn',
            this.handelAlert.formData,
            (res) => {
              this.dialogFormVisible = false //隐藏弹窗
            }
          )
        } else {
          return false
        }
      })
    },
    handelAlert_cancel(formName) {
      this.dialogFormVisible = false //隐藏弹窗
      this.$refs[formName].resetFields() //清空弹窗
      //清空表单
    },
    handleSizeChange(size) {
      this.searchInput.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.searchInput.currentPage = currentPage
    },
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.startTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.startTime = ''
        this.formInline.endTime = ''
      }
    },
  },
  created() {
    this.getTableData(this.searchInput)
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
