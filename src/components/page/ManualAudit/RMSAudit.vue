<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="sensitiveCondition.title"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" prop="status">
            <el-select
              v-model="sensitiveCondition.status"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="time1">
            <el-date-picker
              class="input-time"
              v-model="sensitiveCondition.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande1"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain @click.prevent="Query()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button
              type="primary"
              @click="handImage(null, 'Pass')"
              v-if="remarkObj.formData.ids.length != 0"
              >批量通过</el-button
            >
            <el-button
              type="danger"
              @click="handImage(null, 'Not')"
              v-if="remarkObj.formData.ids.length != 0"
              >批量不通过</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="ManualAuditRMSAudit"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <!-- <vxe-column field="" title="签名ID" width="80">
                            <template #default="scope">{{ scope.row.signatureId }}</template>
                        </vxe-column> -->
          <vxe-column field="用户名称" title="用户名称">
            <template #default="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              <div>{{ scope.row.username }}</div>
            </template>
          </vxe-column>
          <vxe-column field="模板ID" title="模板ID">
            <template #default="scope">
              <div class="video-id">
                <div>{{ scope.row.videoId }}</div>
                <div
                  v-if="scope.row.variableTemplate == true"
                  class="watermark"
                >
                  变
                </div>
                <!-- <el-tooltip effect="light" content="变量模版" placement="top">
                                    <img v-if="scope.row.variableTemplate == true" src="../../../assets/images/bianliang.png"
                                        alt="" srcset="" style="margin-left: 5px;cursor: pointer;" width="18px">
                                </el-tooltip> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="签名" title="签名">
            <template #default="scope">
              <div>{{ scope.row.signature }}</div>
            </template>
          </vxe-column>
          <vxe-column field="视频短信标题" title="视频短信标题">
            <template #default="scope">
              <div
                style="color: #409eff; cursor: pointer"
                @click="View(scope.row)"
              >
                <Tooltip
                  v-if="scope.row.title"
                  :content="scope.row.title"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="变量模板" width="100">
                            <template #default="scope">
                                <span style="color:#67c23a" v-if="scope.row.variableTemplate == true">是</span>
                                <span style="color:red" v-else>否</span>
                            </template>
                        </vxe-column> -->
          <!-- <vxe-column field="" title="预览">
                            <template #default="scope">
                                <el-button type="text" @click="View(scope.row)"><i
                                        class="el-icon-picture"></i>&nbsp;预览</el-button>
                            </template>
                        </vxe-column> -->
          <vxe-column v-if="KZNum != '1'" field="快照预览" title="快照预览" width="70">
            <template #default="scope">
              <div
                class="demo-image__preview"
                style="cursor: pointer"
                @click="KZCK(scope.row.auditPic)"
              >
                <el-image
                  v-if="scope.row.auditPic"
                  style="width: 50px; height: 50px"
                  :z-index="9999"
                  :src="
                    scope.row.auditPic
                      ? API.imgU + scope.row.auditPic.split(',')[0]
                      : ''
                  "
                  :preview-src-list="KFck.srcList"
                  :append-to-body="true"
                  :preview-teleported="true"
                >
                  <template v-slot:error>
                    <div class="image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
          </vxe-column>

          <vxe-column
            v-if="sensitiveConObj.status == '1'"
            field="模版大小（KB）"
            title="模版大小（KB）"
            width="120"
          >
            <template #default="scope">
              <div>{{ scope.row.size }}</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="120">
            <template #default="scope">
              <div v-if="scope.row.tempType == '1'">文本</div>
              <div v-if="scope.row.tempType == '2'">图片</div>
              <div v-if="scope.row.tempType == '3'">视频</div>
              <div v-if="scope.row.tempType == '4'">图片和视频</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template #default="scope">
              <!-- <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</p>
                                <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</p> -->
              <div>
                {{ scope.row.createTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="审核状态" title="审核状态" width="110">
            <template #default="scope">
              <el-tag
                :disable-transitions="true" v-if="scope.row.status == '1'" type="info" effect="dark" >
                待审核
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.status == '2'"
                type="success"
                effect="dark"
                
              >
                审核通过
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == '3'"
                type="danger"
                effect="dark"
                
              >
                审核不通过
              </el-tag>
              <!-- <span v-if="scope.row.status=='1'">未审核</span>
                                <span v-else-if="scope.row.status=='2'"  style="color:#16A589">审核通过</span>
                                <span v-else  style="color:#f56c6c">审核未通过</span> -->
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" v-if="sensitiveConObj.status != '1'">
            <template #default="scope">
              <div>
                {{ scope.row.auditReason }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="sensitiveConObj.status != 1"
            field="审核人"
            title="审核人"
          >
            <template #default="scope">
              <div>
                {{ scope.row.auditUsername }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="sensitiveConObj.status != 1"
            field="审核时间"
            title="审核时间"
            width="100"
          >
            <template #default="scope">
              <p v-if="scope.row.auditTime">
                {{ $parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.auditTime">
                {{ $parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}
              </p>
              <!-- {{ scope.row.createTime}} -->
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180">
            <template #default="scope">
              <!-- <el-button type="text" style="color:#f56c6c;margin-left:0px;" @click="handleEdit(scope.$index, scope.row)"><i class="el-icon-delete"></i>&nbsp;删除</el-button> -->
              <el-button
                link
                style="margin-left: 0px; color: #16a589"
                @click="handleReport(scope.row)"
                ><el-icon><SuccessFilled /></el-icon>&nbsp;签名报备</el-button
              >
              <el-button
                link
                style="margin-left: 10px; color: #409eff;"
                v-if="scope.row.status == '1'"
                @click="handImage(scope.row, 'Pass')"
                ><el-icon><SuccessFilled /></el-icon>模版报备</el-button
              >
              <el-button
                link
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.status == '1'"
                @click="handImage(scope.row, 'Not')"
                ><el-icon><CircleCloseFilled /></el-icon>模版驳回</el-button
              >
              <el-button
                link
                style="margin-left: 10px; color: #409eff;"
                @click="handelCheck(scope.row)"
                > <el-icon><View /></el-icon>&nbsp;实名明细</el-button
              >
              <el-button
                link
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.status != '1'"
                @click="turnDown(scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;驳回</el-button
              >
              <!-- <el-button
                link
                style="margin-left: 10px; color: #16a589"
                @click="handleReport(scope.row)"
                ><el-icon><SuccessFilled /></el-icon>&nbsp;签名报备</el-button
              >
              <el-button
                link
                style="margin-left: 10px; color: #409eff;"
                @click="handelCheck(scope.row)"
                ><el-icon><View /></el-icon>&nbsp;实名明细</el-button
              > -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹框 （审核 通过）
                <el-dialog :title="title" v-model="remarkshow" width="500px"  :close-on-click-modal=false :show-close=false>
                    <el-form :model="remarkObj.formData" :rules="remarkObj.formRule" ref="remarkObjs" label-width="80px" style="padding:0 28px;">
                        <el-form-item label="拒绝原因" prop="auditReason">
                            <el-input type="textarea" :disabled="disabledinput" rows='4' resize='none' v-model="remarkObj.formData.auditReason"></el-input>
                        </el-form-item>
                        <el-form-item style="margin-top: 40px;text-align: right;">
                            <el-button class="footer-center-button" v-if="operationFlag == true" @click="remarkshow = false">取 消</el-button> 
                            <el-button class="footer-center-button" v-if="operationFlag == true" type="primary" @click="handelAlertdd('remarkObjs')">确 认</el-button>
                            <el-button type="primary" v-if="operationFlag == false" :loading="true">操作进行中，请稍等</el-button>
                        </el-form-item>
                    </el-form>    
                </el-dialog> -->
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in viewData"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png' ||
                    item.media == 'jpeg'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                ></audio>
                <div
                  class="tempContent"
                  style="white-space: pre-line"
                  v-html="item.txt"
                ></div>
                <!-- {{item.txt}} -->
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div style="padding: 10px 60px">
          <div
            v-for="(item, index) in viewData"
            :key="index"
            style="display: inline-block; margin: 0 15px"
          >
            <span
              v-if="item.media"
              style="cursor: pointer; color: #16a589"
              @click="downloadView(item.mediaPath)"
              >{{ item.media }}素材下载</span
            >
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 预览手机弹框   -->
    <el-dialog
      :title="title"
      v-model="uploadImage"
      width="430px"
      class="TemDialog"
      :close-on-click-modal="false"
    >
      <el-upload
        class="upload-demo"
        :action="actionPath"
        :headers="token"
        :file-list="fileList1"
        :on-remove="handleRemove1"
        :on-success="handleAvatarSuccess1"
        :before-upload="beforeAvatarUpload1"
        list-type="picture"
      >
        <el-button size="default" type="primary">点击上传</el-button>
        <template v-slot:tip>
          <div class="el-upload__tip">请上传图片</div>
        </template>
      </el-upload>
      <div class="divWidth" style="width: 100%; margin-top: 20px">
        <el-input
          type="textarea"
          placeholder="备注"
          v-model="auditReason"
          resize="none"
          class="input-w-3"
          :rows="4"
        ></el-input>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleAdopt">确 定</el-button>
          <el-button @click="uploadImage = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 实名明细 -->
    <SignatureView v-if="autonymVisible" ref="SignatureRef" :signatureData="signatureData" @handleClose="closeSignature"></SignatureView>

    <!-- <el-dialog title="视频实名明细" v-model="autonymVisible" width="700px">
      <div class="table-box">
        <el-tabs v-model="operator" type="card" @tab-click="handleClick">
          <el-tab-pane name="1">
            <span slot="label"
              ><i class="iconfont icon-yidong" style="color: #409eff"></i>
              移动</span
            >
          </el-tab-pane>
          <el-tab-pane name="2">
            <span slot="label"
              ><i class="iconfont icon-liantong" style="color: #f56c6c"></i>
              联通</span
            >
          </el-tab-pane>
          <el-tab-pane label="电信" name="3">
            <span slot="label"
              ><i class="iconfont icon-dianxin" style="color: #0c36f2"></i>
              电信</span
            >
          </el-tab-pane>
        </el-tabs>
      </div>

      <el-form
        label-width="80px"
        :inline="true"
        :model="checkForm"
        class="demo-form-inline"
        ref="checkForm"
      >
        <el-form-item label="通道号" prop="channelCode">
          <el-input
            v-model="checkForm.channelCode"
            placeholder
            class="input-time"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain style @click="searchAutonym"
            >查询</el-button
          >
          <el-button
            type="primary"
            plain
            style
            @click="resetAutonym('checkForm')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="autonymData"
        style="width: 100%"
        border
        max-height="400px"
      >
        <el-table-column label="产品名称" prop="">
          <template #default="scope">
              <span>视频短信 </span>
          </template>
        </el-table-column>
        <el-table-column label="通道号" prop="channelCode"></el-table-column>
        <el-table-column label="子端口">
          <template #default="scope">
            <span> {{ scope.row.spCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="扩展号">
          <template #default="scope">
            <span> {{ scope.row.ext }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用状态">
          <template #default="scope">
            <el-tag
:disable-transitions="true" type="success" v-if="scope.row.status == 1">可用</el-tag>
            <el-tag
:disable-transitions="true" type="danger" v-else-if="scope.row.status == 2"
              >未生成</el-tag
            >
            <el-tag
:disable-transitions="true" type="warning" v-else-if="scope.row.status == 0"
              >实名中</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="autonymVisible = false">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import SignatureView from "@/components/publicComponents/signatureDetail.vue";
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    TableTem,
    FileUpload,
    UserLists,
    Tooltip,
    SignatureView
  },
  name: 'RMSAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      actionPath: window.path.cpus + 'v3/file/upload',
      userFlag: false,
      nameover: '',
      // 预览
      viewData: '',
      indexVeew: 1,
      title: '',
      videoId: '',
      auditReason: '',
      dialogVisible: false,
      uploadImage: false,
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      remarkshow: false,
      fileList1: [],
      fileauditpic: [],
      autonymVisible: false,
      // signatoryId: "",
      // checkForm: {
      //   channelCode: "",
      //   userId: "",
      // },
      // autonymData: [],
      // operator: "1",
      signatureData:{
        signatoryId: "",
        userId: "",
        userName:"",
        productId: "3",
      },
      token: {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      },
      KZNum: '1',
      KFck: {
        url: '',
        srcList: [window.path.imgU],
      },
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          ids: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: '请输入拒绝原因', trigger: 'blur' },
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      idArrs: [], // 驳回的id
      title: '',
      signatureFrom: {
        title: '',
        formData: {
          signature: '',
          clientName: '',
          signatureType: '1',
          remark: '',
          imgUrl: '',
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: '该输入项为必填项!', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
          ],
          signatureType: [
            { required: true, message: '请选择签名类型', trigger: 'change' },
          ],
          imgUrl: [
            { required: true, message: '请选择上传图片', trigger: 'change' },
          ],
        },
        signature: '', //签名
        signatureId: '', //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        username: '',
        title: '',
        status: '1',
        time1: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        username: '',
        title: '',
        status: '1',
        time1: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
      isFirstEnter: false,
    }
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumervideo/auditPage',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    Query() {
      if(this.tableDataObj.loading2) return; //防止重复点击
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.KZNum = this.sensitiveConObj.status
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.KZNum = '1'
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
        app.config.globalProperties.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
        app.config.globalProperties.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
      this.getTableDtate()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
      this.getTableDtate()
    },
    // 素材下载------------------------------
    downloadView(val) {
      this.downloadAjax(window.path.imgU + 'group1/' + val)
    },
    ajax(url, callback, options) {
      window.URL = window.URL || window.webkitURL
      var xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      if (options.responseType) {
        xhr.responseType = options.responseType
      }
      xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
          callback(xhr)
        }
      }
      xhr.send()
    },
    downloadAjax(urls) {
      let innerurl = urls // 文件地址
      var name = urls.replace(/(.*\/)*([^.]+).*/gi, '$2')
      this.ajax(
        innerurl,
        function (xhr) {
          let filename = name + '.' + innerurl.replace(/(.*\.)/, '')
          let content = xhr.response
          let a = document.createElement('a')
          let blob = new Blob([xhr.response])
          let url = window.URL.createObjectURL(blob)
          a.href = url
          a.download = filename
          a.click()
          window.URL.revokeObjectURL(url)
        },
        {
          responseType: 'blob',
        }
      )
    },
    // 素材下载------------------------------

    // 下载
    download(val) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          'M+': this.getMonth() + 1,
          'd+': this.getDate(),
          'h+': this.getHours(),
          'm+': this.getMinutes(),
          's+': this.getSeconds(),
          'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        }
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + '').substr(4 - RegExp.$1.length)
          )
        for (var i in args) {
          var n = args[i]
          if (new RegExp('(' + i + ')').test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
            )
        }
        return format
      }
      var that = this
      filedownload()
      function filedownload() {
        fetch(
          that.API.cpus +
            'v3/file/download?fileName=' +
            val.fileOriginalName +
            '&group=group1&path=' +
            val.filePath.slice(7),
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
            // body: JSON.stringify({
            //     batchNo: val.row.batchNo,
            // })
          }
        )
          .then((res) => res.blob())
          .then((data) => {
            let blobUrl = window.URL.createObjectURL(data)
            download(blobUrl)
          })
      }
      function download(blobUrl) {
        var a = document.createElement('a')
        a.style.display = 'none'
        a.download =
          '(' +
          new Date().format('YYYY-MM-DD hh:mm:ss') +
          ') ' +
          val.fileOriginalName
        a.href = blobUrl
        a.click()
      }
    },
    // 图片上传限制
    beforeAvatarUpload1(file) {
      const isJPG1 = file.type === 'image/png'
      const isJPG2 = file.type === 'image/jpeg'
      const isJPG3 = file.type === 'image/tiff'
      const isJPG4 = file.type === 'image/raw'
      const isJPG5 = file.type === 'image/bmp'
      // const isLt2M = file.size / 1024 / 1024 < 1;
      if (!isJPG1 && !isJPG2 && !isJPG3 && !isJPG4 && !isJPG5) {
        this.$message.error('只允许上传图像')
        return false
      }
    },
    //快照查看
    KZCK(val) {
      if (val) {
        this.KFck.srcList = []
        let a = val.split(',')
        console.log(val.split(','))
        for (var i = 0; i < a.length; i++) {
          this.KFck.srcList.push(window.path.imgU + a[i])
        }
      }
    },
    // 审核上传图片
    handleRemove1(file, fileList) {
      this.fileauditpic = []
      for (let i = 0; i < fileList.length; i++) {
        this.fileauditpic.push(fileList[i].response.data.fullpath)
      }
    },
    //图片上传成功
    handleAvatarSuccess1(res, file) {
      this.fileauditpic.push(res.data.fullpath)
    },
    //审核通过上传图
    handImage(row, val) {
      this.title = val == 'Pass' ? '审核通过' : '审核不通过'
      this.uploadImage = true
      if (row) {
        this.videoId = row.videoId
      } else {
        this.videoId = this.remarkObj.formData.ids.join()
      }
    },
    //审核
    handleAdopt(index, row) {
      this.$confirms.confirmation(
        'post',
        '确认当前操作？',
        window.path.omcs + 'consumervideo/audit',
        {
          ids: this.videoId + '',
          auditStatus: this.title == '审核通过' ? 2 : 3,
          auditPic: this.fileauditpic.join(','),
          auditReason: this.auditReason,
        },
        (res) => {
          this.getTableDtate()
          this.uploadImage = false
        }
      )
    },
    // 驳回审核
    turnDown(rowID) {
      this.$confirms.confirmation(
        'post',
        '确认驳回审核？',
        window.path.omcs + 'consumervideo/overrule/' + rowID.videoId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },

    // //批量通过
    // sensitiveBatchDel(){
    //     let status = true;
    //     //判断选项中是否有审核过的状态
    //     for(let i =0; i< this.auditStatus.length; i++){
    //         if(this.auditStatus[i] != '1'){
    //             status = false;
    //             break;
    //         }
    //     }
    //     if(status){
    //         this.$confirms.confirmation('post','确认当条审核?',window.path.omcs+'consumertaskvideo/audit',{ids:this.remarkObj.formData.ids.join(','),auditStatus:2},res =>{
    //             this.getTableDtate();
    //             this.remarkObj.formData.ids = [];
    //         });
    //     }else{
    //         this.$message({
    //             message: '选项中包含已审核项，需重新选择待审核项！',
    //             type: 'warning'
    //         });
    //     }
    // },
    // //驳回
    // handleReject(index,row){
    //     this.remarkshow = true;
    //     this.title = '驳回备注'
    //     this.remarkObj.formData.ids.push(row.id); //赋值ID
    // },
    // //驳回（发送请求）
    // handelAlertdd(formName){
    //     this.$refs[formName].validate((valid) => {
    //         if (valid) {
    //             let obj={}
    //             obj.auditReason=this.remarkObj.formData.auditReason
    //             obj.ids=this.remarkObj.formData.ids.join(',')
    //             obj.auditStatus=3
    //             this.$confirms.confirmation('post','确认当条驳回?',window.path.omcs+'consumertaskvideo/audit',obj,res =>{
    //                 this.remarkshow = false
    //                 this.getTableDtate();
    //             });
    //         }else{
    //             return false;
    //         }
    //     })
    // },
    // //批量驳回
    // sensitiveBatchSet(){
    //     let status = true;
    //     //判断选项中是否有审核过的状态
    //     this.title = '批量驳回备注'
    //     for(let i =0; i< this.auditStatus.length; i++){
    //         if(this.auditStatus[i] != '1'){
    //             status = false;
    //             break;
    //         }
    //     }
    //     if(status){
    //         this.remarkshow = true;
    //     }else{
    //         this.$message({
    //             message: '选项中包含已审核项，需重新选择待审核项！',
    //             type: 'warning'
    //         });
    //     }
    // },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let auditStatus = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].videoId)
        //批量操作列表选中项的审核状态
        auditStatus.push(val.records[i].auditStatus)
      }
      this.auditStatus = auditStatus //选中项的审核状态
      this.remarkObj.formData.ids = selectId //批量通过，不通过的id数组
    },
    // 预览
    View(val) {
      if (val.invalidContent) {
        this.$message.error('数据异常')
      } else {
        this.dialogVisible = true
        this.indexVeew = 1
        this.title = val.title
        this.viewData = val.contents
      }
    },
    handelCheck(row) {
      this.signatureData.signatoryId = row.signature;
      this.signatureData.userId = row.userId;
      this.signatureData.userName = row.username;
      this.autonymVisible = true;
    },
    // searchAutonym() {
    //   this.handleClick();
    // },
    // resetAutonym() {
    //   this.checkForm.channelCode = "";
    //   this.handleClick();
    // },
    // handleClick() {
    //   window.api.post(
    //     window.path.omcs + "consumerSignature/queryRealNameInfo",
    //     {
    //       userId: this.checkForm.userId,
    //       signature: this.signatoryId,
    //       operator: this.operator,
    //       channelCode: this.checkForm.channelCode,
    //       productId: "3",
    //     },
    //     (res) => {
    //       if (res.code == 200) {
    //         this.autonymVisible = true;
    //         this.autonymData = res.data;
    //       } else {
    //         this.autonymData = [];
    //         this.$message({
    //           message: res.msg,
    //           type: "warning",
    //         });
    //       }
    //     }
    //   );
    // },
    closeSignature(data){
      this.autonymVisible = data;
    },
    handleReport(row) {
      this.$confirms.confirmation(
        "post",
        "签名报备!",
        window.path.omcs + "consumervideo/auditSignature",
        {
          ids: row.videoId,
        },
        (res) => {
          if (res.code == 200) {
            this.getTableDtate();
          }
        }
      );
    },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
    // this.getTableDtate();
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true
      } else {
        this.disabledinput = false
      }
    },
    //弹出框是否关闭
    uploadImage(val) {
      if (val == false) {
        this.remarkObj.formData.ids = [] //置空ID
        this.auditReason = ''
        this.fileList1 = []
        this.fileauditpic = []
        this.getTableDtate()
      }
    },
    // autonymVisible(val) {
    //   if (!val) {
    //     this.autonymData = [];
    //     this.operator = '1'
    //     this.checkForm.userId = ''
    //     this.checkForm.channelCode = ''
    //   }
    // },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.table-box :deep(.el-tabs__item) {
  width: 150px;
  text-align: center;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style lang="less">
.el-table--small th {
  background: #f5f5f5;
}

.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}

.tempContent {
  .prompt {
    color: #f56c6c;
    display: inline-block;
    font-weight: 800;
    /* background: rgba(0, 0, 0, .5); */
    /* text-shadow: 3px 3px 5px #FF0000; */
  }

  .prompt_error {
    color: #e6a23c;
    display: inline-block;
    text-decoration: underline;
    font-weight: 800;
    /* background: rgba(0, 0, 0, .5); */
    /* text-shadow: 3px 3px 5px #FF0000; */
  }
}
.video-id {
  display: flex;
  align-items: center;
}
.watermark {
  width: 20px;
  height: 20px;
  color: #fff;
  font-size: 12px;
  opacity: 0.7;
  margin-left: 4px;
  border: 1px solid #7957d5;
  background: #7957d5;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
