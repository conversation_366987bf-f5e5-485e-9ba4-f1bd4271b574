<template>
  <div class="container_left">
    <div class="fillet LoginCellPhone-box" style="padding-bottom: 60px">
      <div class="LoginCellPhone-matter">
        <div>温馨提示</div>
        <div>1.默认登录手机号为您注册账号时的手机号。</div>
        <div>
          2.该登录手机号至多可添加50个，至少一个。当只有一个手机号时不允许删除！
        </div>
      </div>
      <div class="LoginCellPhone-creat">
        <el-button type="primary" @click="addphone">添加登录手机号</el-button>
      </div>
      <div class="LoginCellPhone-search-fun">
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
        ></table-tem>
      </div>
    </div>
    <!-- 添加手机号 -->
    <el-dialog
      title="添加登录手机号"
      v-model="LcpDialogVisible"
      width="600px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-steps :active="setPhoneSteps" simple style="margin-bottom: 26px">
        <el-step title="获取原手机号验证">
          <template #icon>
            <el-icon><EditPen /></el-icon>
          </template>
        </el-step>
        <el-step title="新手机号添加">
          <template #icon>
            <el-icon><UploadFilled /></el-icon>
          </template>
        </el-step>
      </el-steps>
      <div v-show="setPhoneSteps == 1">
        <el-table
          :data="tableD"
          class="Login-c-p-getPhone"
          border
          style="width: 100%"
        >
          <el-table-column align="center" prop="name" label="编号" width="120">
          </el-table-column>
          <el-table-column prop="address" align="center" label="手机号">
          </el-table-column>
          <el-table-column align="center" width="80" label="选择">
            <template v-slot="scope">
              <el-radio
                @change="getCurrentRow(scope.$index)"
                :label="scope.$index"
                v-model="radio"
                class="textRadio"
                >&nbsp;</el-radio
              >
            </template>
          </el-table-column>
        </el-table>
        <el-form
          :model="setphoneFrom.ruleForm1"
          :rules="setphoneFrom.rules1"
          ref="ruleForm1"
          class="demo-ruleForm"
          label-width="120px"
        >
          <el-form-item
            label="手机验证码"
            prop="verCode"
            style="margin: 40px auto"
          >
            <el-input
              v-model="setphoneFrom.ruleForm1.verCode"
              style="display: inline-block; width: 180px"
            ></el-input>
            <el-button
              type="primary"
              plain
              style="width: 124px; padding: 9px 0px"
              @click="CountdownCode"
              v-if="nmb == 120"
              >获取验证码</el-button
            >
            <el-button
              type="primary"
              plain
              style="width: 124px; padding: 9px 0px"
              disabled
              v-else
              >重新获取({{ nmb }})</el-button
            >
          </el-form-item>
          <el-form-item style="">
            <el-button @click="cancel()" style="width: 100px; padding: 9px 0"
              >取消</el-button
            >
            <el-button
              type="primary"
              @click="submitForm('ruleForm1')"
              style="width: 100px; padding: 9px 0"
              >下一步</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div v-show="setPhoneSteps == 2">
        <el-form
          :model="setphoneFrom.ruleForm2"
          :rules="setphoneFrom.rules2"
          ref="ruleForm2"
          class="demo-ruleForm"
          label-width="130px"
        >
          <el-form-item
            label="输入手机号"
            prop="setNewPhone"
            style="margin-bottom: 200px"
          >
            <el-input
              v-model="setphoneFrom.ruleForm2.setNewPhone"
              style="width: 290px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="cancel()" style="width: 100px; padding: 9px 0"
              >取消</el-button
            >
            <el-button
              type="primary"
              @click="submitForm('ruleForm2')"
              style="width: 100px; padding: 9px 0"
              >提交</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
    <!-- 删除手机号
            <el-dialog
            title="删除手机号"
            v-model="DeletePhone"
            width="560px"
            class="LoginCellPhoneDialog"
            :before-close='handelClose1'
            >
                <el-steps :active="setPhoneSteps" simple style="margin-bottom:26px;">
                    <el-step title="获取手机号验证码" icon="el-icon-edit"></el-step>
                </el-steps>
                    <el-table
                    :data="tableD"
                    class="Login-c-p-getPhone"
                    border
                    style="width: 100%;">
                        <el-table-column align="center"  width="80" label="选择" >
                            <template #default="scope" >
                                <el-radio @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio" class="textRadio" >&nbsp;</el-radio>
                            </template>
                        </el-table-column>
                        <el-table-column
                        align="center"
                        prop="name"
                        label="序号"
                        width="120">
                        </el-table-column>
                        <el-table-column
                        prop="address"
                        align="center"
                        label="手机号">
                        </el-table-column>
                    </el-table>
                    <el-form :model="setphoneFrom.ruleForm1" :rules="setphoneFrom.rules1" ref="ruleForm2"  class="demo-ruleForm" label-width="120px">
                        <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto ;">
                            <el-input v-model="setphoneFrom.ruleForm1.verCode" style="display:inline-block;width:180px;"></el-input>
                            <el-button  type="primary" plain style="width:124px;padding:9px 0px;" @click="CountdownCode" v-if="nmb==20">获取验证码</el-button>
                            <el-button  type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{nmb}})</el-button>
                        </el-form-item>
                        <el-form-item style="">
                            <el-button @click="cancel()"  style="width:100px; padding:9px 0;">取消</el-button>
                            <el-button type="primary" @click="submitFormDelete('ruleForm2')" style="width:100px; padding:9px 0;">提交</el-button>
                        </el-form-item>
                    </el-form>       
            </el-dialog> -->
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  data() {
    // 验证IP规则
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error('请选中手机号'))
      } else if (value == '') {
        return callback(new Error('请输入验证码'))
      } else {
        callback()
      }
    }
    var phone = (rule, value, callback) => {
      if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(value)) {
        return callback(new Error('请输入正确手机号'))
      } else if (value == '') {
        return callback(new Error('请输入手机号'))
      } else {
        callback()
      }
    }
    var optionWidths = () => {
      window.onresize = function () {
        //须要注意做用域的问题 方法内this是window
        let windowWidth = document.documentElement.clientWidth
        if (windowWidth > 950) {
          return 40
        } else {
          return 180
        }
      }
    }
    return {
      nmb: 120,
      radio: '',
      // 设置手机号的步骤
      setPhoneSteps: 1,
      //弹出框显示隐藏
      LcpDialogVisible: false,
      DeletePhone: false,
      // 存储选中手机号
      phoneData: '',
      // 存储原有手机号
      phoneOriginal: [],
      // 存储个人信息
      roleInfo: {},
      setphoneFrom: {
        ruleForm1: {
          verCode: '',
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: 'blur' },
            { min: 6, max: 6, message: '请输入6位数字验证码' },
          ],
        },
        ruleForm2: {
          setNewPhone: '',
        },
        rules2: {
          setNewPhone: [
            { required: true, validator: phone, trigger: 'blur' },
            // { min: 6, max: 6, message: '请输入6位数字验证码' }
          ],
        },
      },
      tableD: [],
      tableDataObj: {
        //列表数据
        // loading2:false,
        id: "LoginCellPhone",
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'Numbering',
            showName: '编号',
            width: '60',
          },
          {
            prop: 'username',
            showName: '用户名',
          },
          {
            prop: 'phone',
            showName: '手机号码',
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: 240, //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dele',
            icon: 'CircleCloseFilled',
          },
        ],
      }
    }
  },
  components: {
    TableTem
  },
  name: 'LoginCellPhone',
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.cpus + 'consumerclientinfo/loginTelephoneManager/list',
        {},
        (res) => {
          let resdata = []
          let tabledata = []
          let phonelength = res.data.data
          if (phonelength.length && phonelength.length > 0) this.phoneData = phonelength[0].mobile
          this.phoneOriginal = []
          // console.log("------------------")
          // console.log(phonelength)
          for (var i = 0; i < phonelength.length; i++) {
            // 列表数据
            let a = {}
            a.Numbering = i + 1
            a.username = phonelength[i].consumerName
            a.phone = phonelength[i].mobile
            resdata[resdata.length] = a
            this.phoneOriginal.push(phonelength[i].mobile)
            // 登录手机号列表
            let b = {}
            b.index = i
            b.name = i + 1
            b.address = phonelength[i].mobile
            tabledata[tabledata.length] = b
          }
          this.tableDataObj.tableData = resdata
          this.tableD = tabledata
          this.tableDataObj.loading2 = false
          // 存储个人信息
          this.roleInfo = res.data[0]
        }
      )
    },
    // 获取验证码倒计时
    CountdownCode() {
      if (this.phoneData) {
        --this.nmb
        const timer = setInterval((res) => {
          --this.nmb
          if (this.nmb < 1) {
            this.nmb = 120
            clearInterval(timer)
          }
        }, 1000)
        window.api.get(
          window.path.cpus +
            'code/sendVerificationCode?phone=' +
            this.phoneData +
            '&flag=2',
          {},
          (res) => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                duration: '2000',
                message: '验证码已发送至手机!',
              })
            } else {
              this.$message({
                type: 'warning',
                message: '验证码未失效，需失效后重新获取!',
              })
            }
          }
        )
      } else {
        this.$message({
          message: '请先选中手机号码',
          type: 'warning',
        })
      }
    },
    detailsRow() {
      this.SigDialogVisible = true
    },
    showRow(row) {
      //赋值给radio
      this.radio = this.tableD.indexOf(row)
    },
    getCurrentRow(val) {
      this.phoneData = this.tableD[val].address //赋值手机号
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow()
      }
    },
    //登录手机号弹出层
    addphone() {
      if (this.tableDataObj.tableData.length >= 50) {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '用户最多添加50个手机号码',
        })
      } else {
        this.radio = 0
        this.LcpDialogVisible = true
      }
    },
    // 新增登录手机号
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (val == 'ruleForm1') {
          if (valid) {
            window.api.get(
              window.path.cpus +
                'code/checkVerificationCode?code=' +
                this.setphoneFrom.ruleForm1.verCode +
                '&flag=2',
              {},
              (res) => {
                if (res.code == 200) {
                  this.setPhoneSteps = 2
                } else {
                  this.$message({
                    type: 'error',
                    duration: '2000',
                    message: '验证码无效！',
                  })
                }
              }
            )
          } else {
            console.log('error submit!!')
            return false
          }
        } else {
          if (valid) {
            let flag = true
            for (var i = 0; i < this.phoneOriginal.length; i++) {
              if (
                this.phoneOriginal[i] == this.setphoneFrom.ruleForm2.setNewPhone
              ) {
                flag = false
                break
              }
            }
            if (flag == true) {
              this.$confirms.confirmation(
                'get',
                '确认添加该手机号',
                window.path.cpus +
                  'consumerclientinfo/addLoginPhone/' +
                  this.setphoneFrom.ruleForm2.setNewPhone,
                {},
                (res) => {
                  this.cancel()
                }
              )
            } else {
              this.$message({
                type: 'error',
                duration: '2000',
                message: '该手机号已存在,不可重复添加',
              })
            }
          } else {
            console.log('error submit!!')
            return false
          }
        }
      })
    },
    cancel() {
      this.LcpDialogVisible = false //关闭弹出框
      this.setPhoneSteps = 1 //步进改为1
      this.setphoneFrom.ruleForm1.verCode = '' //验证码置空
      this.setphoneFrom.ruleForm2.setNewPhone = '' //手机号置空
      this.InquireList()
    },
    handelClose() {
      //×号关闭弹窗
      // 点击关闭
      this.LcpDialogVisible = false //关闭弹出框
    },
    /**
     * 表格公共的方法--start
     * **/
    handelOptionButton(val) {
      //操作列表的点击
      if (val.methods == 'dele') {
        //点击删除
        if (this.tableDataObj.tableData.length <= 1) {
          this.$message({
            type: 'error',
            duration: '2000',
            message: '最少存在一个手机号',
          })
        } else {
          this.$confirms.confirmation(
            'get',
            '确认删除该手机号',
            window.path.cpus +
              'consumerclientinfo/deleteLoginPhone/' +
              val.row.phone,
            {},
            (res) => {
              this.InquireList()
            }
          )
        }
      }
    },
    handleSizeChange(size) {
      this.pagesize = size
    },
    handleCurrentChange: function (currentPage) {
      this.currentPage = currentPage
    },
    /**
     * 表格公共的方法--end
     * **/
  },
  created() {
    this.InquireList()
  },
  watch: {
    DeletePhone: function (val) {
      if (val == false) {
        this.InquireList()
      }
    },
    LcpDialogVisible: function (val) {
      if (val == false) {
        this.setphoneFrom.ruleForm1.verCode = '' //验证码置空
        this.setphoneFrom.ruleForm2.setNewPhone = '' //手机号置空
        this.setPhoneSteps = 1 //步进改为1
        this.radio = ''
        this.phoneData = this.phoneOriginal[0]
        this.$refs.ruleForm1.resetFields()
        this.$refs.ruleForm2.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.LoginCellPhone-box {
  padding: 20px;
}
.LoginCellPhone-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.LoginCellPhone-matter > div {
  height: 26px;
  line-height: 26px;
}
.LoginCellPhone-set {
  color: #0066cc;
}
.LoginCellPhone-creat {
  margin: 20px 0px;
}
.LoginCellPhone-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.LoginCellPhone-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Mail-table {
  padding-bottom: 40px;
}
.LoginCP-type .el-radio + .el-radio {
  margin-left: 0px;
}
.LoginCP-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.LoginCP-type .el-radio-group {
  padding-top: 8px;
}
.LoginCP-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>

<style>
.LoginCellPhoneDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}
.Login-c-p-getPhone .el-radio__label {
  padding-left: 0px;
}
.LoginCellPhoneDialog .el-dialog__body {
  padding: 20px 40px 30px;
}
.login_cell_phone .el-steps--simple > div:nth-child(1) .el-step__title {
  width: 70%;
}
</style>
