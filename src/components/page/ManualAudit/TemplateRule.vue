<template>
  <div class="templateRule">
    <div class="layui-card-body stylePading">
      <p class="rule_title">1.什么是模板？</p>
      <div class="article article_indent">
        应运营商要求，发送的短信内容都需要通过审核才可以发送，使用根据模板编辑的短信内容进行发送，则默认该条短信内容已通过审核，可直达目标手机号码，如短信内容没有与模板相匹配，则无法提交至运营商进行发送，短信模板及短信内容关系如下所示：
      </div>
      <div class="article">
        短信模板：尊敬的用户，您本次支付确认码是：{d6}<br />
        短信内容：尊敬的用户，您本次支付确认码是：256897
      </div>
      <div class="article">
        上述短信模板示例中，{d6}这种用大括号围起来的形式，叫做参数。参数可根据不同场景进行变换,所有参数根据开头字母区分类型，后面的阿拉伯数字表示参数位数长度，又分为固定长度和非固定长度，如只显示n，则为固定长度，表示该参数长度n位，如显示n-m，则为非固定长度，表示该参数长度在n到m位。n和m在此处表示可自定义变量。
      </div>
    </div>
    <div class="layui-card-body stylePading">
      <div class="layui-row">
        <p class="rule_title">2.参数规则</p>
        <p class="rule_subtitle">1 模板中的变量是纯数字</p>
        <p class="third_title">
          <span class="sub2title_num">1</span>模板中的变量是纯数字
        </p>
        <div class="layui-col-md6">
          <div class="half_para">
            <p>
              1.1数字位数固定，比如六位数，那么用{d6}
              d表示数字，后面阿拉伯数字表示长度。
            </p>
            <p class="f-9">
              实例：<br />
              模板内容：您的注册码:{d6}
            </p>
            <div class="bubble">
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：123456</div>
              </div>
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：342434</div>
              </div>
            </div>
          </div>
        </div>
        <div class="layui-col-md6">
          <div class="half_para">
            <p>
              1.2数字位数不固定，比如四至六位数，那么用{d4-6}代替。d表示数字，4-6表示长度有4到6位。
            </p>
            <p class="f-9">
              实例：<br />
              模板内容：您的注册码:{d4-6}
            </p>
            <div class="bubble">
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：123</div>
              </div>
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：123456</div>
              </div>
            </div>
          </div>
        </div>

        <p class="rule_subtitle">2 模板中的变量是数字或者字母混合的</p>
        <p class="third_title">
          <span class="sub2title_num">2</span>模板中的变量是数字或者字母混合的
        </p>
        <div class="layui-col-md6">
          <div class="half_para">
            <p>
              2.1位数固定，比如六位，那么用{w6}代替。w表示数字或者字母，后面阿拉伯数字表示长度。
            </p>
            <p class="f-9">
              实例：<br />
              模板内容：您的注册码:{w6}
            </p>
            <div class="bubble">
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：abc123</div>
              </div>
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：342434</div>
              </div>
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：abcdef</div>
              </div>
            </div>
          </div>
        </div>
        <div class="layui-col-md6">
          <div class="half_para">
            <p>
              2.2位数不固定，比如四至六位数字或字母，那么用{w4-6}。w表示数字或者字母，4-6表示长度有4到6位。
            </p>
            <p class="f-9">
              实例：<br />
              模板内容：您的注册码:{w4-6}
            </p>
            <div class="bubble">
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：1234</div>
              </div>
              <div class="bubble_line clearFix">
                <span class="triangle"></span>
                <div class="bubble_article">您的验证码是：123abc</div>
              </div>
            </div>
          </div>
        </div>

        <div class="layui-col-md12">
          <p class="rule_subtitle">3 模板中的变量是金额</p>
          <p class="third_title">
            <span class="sub2title_num">3</span>模板中的变量是金额
          </p>
          <div class="layui-col-md6">
            <div class="half_para">
              <p>
                3.1金额位数固定，比如六位数，那么用{$6}代替。$表示金额，后面阿拉伯数字表示长度。
              </p>
              <p class="f-9">
                实例：<br />
                模板内容：尊敬的用户，您申请的{$6}元人民币提现业务已受理成功，预计1-7个工作日到账，请注意查收。
              </p>
              <div class="bubble">
                <div class="bubble_line clearFix">
                  <span class="triangle"></span>
                  <div class="bubble_article">
                    尊敬的用户，您申请的{$6}元人民币提现业务已受理成功，预计1-7个工作日到账，请注意查收。
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="half_para">
              <p>
                3.2金额位数不固定，比如四到六位，那么用{$4-6}代替。$表示金额，4-6表示长度有4到6位。
              </p>
              <p class="f-9">
                实例：<br />
                模板内容：尊敬的用户，您申请的{$4-6}元人民币提现业务已受理成功，预计1-7个工作日到账，请注意查收。
              </p>
              <div class="bubble">
                <div class="bubble_line clearFix">
                  <span class="triangle"></span>
                  <div class="bubble_article">
                    尊敬的用户，您申请的50,00元人民币提现业务已受理成功，预计1-7个工作日到账，请注意查收。
                  </div>
                </div>
                <div class="bubble_line clearFix">
                  <span class="triangle"></span>
                  <div class="bubble_article">
                    尊敬的用户，您申请的500000元人民币提现业务已受理成功，预计1-7个工作日到账，请注意查收。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="layui-col-md12">
          <p class="rule_subtitle">4 模板中的变量是中文（最多5个字）</p>
          <p class="third_title">
            <span class="sub2title_num">4</span>模板中的变量是中文
          </p>
          <div class="layui-col-md6">
            <div class="half_para">
              <p>
                4.1中文变量位数固定，比如3位，那么用{c3}代替。c表示中文字符，后面阿拉伯数字表示长度。
              </p>
              <p class="f-9">
                实例：<br />
                模板内容：尊敬的{c3}，您正在使用本手机号码绑定的银行进行支付，如非本人操作，请忽略次短信。
              </p>
              <div class="bubble">
                <div class="bubble_line clearFix">
                  <span class="triangle"></span>
                  <div class="bubble_article">
                    尊敬的张力力，您正在使用本手机号码绑定的银行进行支付，如非本人操作，请忽略次短信。
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="layui-col-md6">
            <div class="half_para">
              <p>
                4.2
                中文位数不固定，比如四到五位，那么用{4-5}代替。c表示中文字符，4-5表示长度有4到5位。
              </p>
              <p class="f-9">
                实例：<br />
                模板内容：尊敬的{c2-3}，您正在使用本手机号码绑定的银行进行支付，如非本人操作，请忽略次短信。
              </p>
              <div class="bubble">
                <div class="bubble_line clearFix">
                  <span class="triangle"></span>
                  <div class="bubble_article">
                    尊敬的杨瀚，您正在使用本手机号码绑定的银行进行支付，如非本人操作，请忽略次短信。
                  </div>
                </div>
                <div class="bubble_line clearFix">
                  <span class="triangle"></span>
                  <div class="bubble_article">
                    尊敬的张力力，您正在使用本手机号码绑定的银行进行支付，如非本人操作，请忽略次短信。
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="layui-card-body stylePading">
      <p class="rule_title">3.为什么要设置模板？</p>
      <div class="article article_indent">
        如果您不设置模板，您通过接口发送的短信将很可能会进入短信审核流程（注：进入该流程需要客服人工审核，
        将会影响短信的发送和到达时间） 或者
        直接被网关驳回，所以，为了您平台用户使用短信的稳定性，请设置模板。
      </div>
      <p class="rule_title">4.模板内容与模板示例输入内容注意事项！</p>
      <div class="article article_indent">4.1模板内容首尾不能添加【】</div>
      <div class="article article_indent">
        4.2模板内容合法，不能发送房产、发票、移民等国家法律法规严格禁止的内容。
      </div>
      <div class="article article_indent">
        4.3发送短信时，如含有动态参数，根据顺序依次将变量动态参数替换为相应的参数内容。
      </div>
      <div class="article article_indent">
        4.4 两个参数不可挨在一起，至少中间有一个字符隔开
      </div>
      <div class="article article_indent">
        4.5 最少需添加1个参数，最多可添加32个参数。
      </div>
      <p class="rule_title">5.短信条数计算规则</p>
      <div class="article article_indent">
        5.1短信字数+签名字数&lt;=70个字，按照70个字(含签名)作为一条短信计算。中文英文符号统一计算为一个字符。
      </div>
      <div class="article article_indent">
        5.2短信字数+签名字数>70个字，按照67字符为一条计费。
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'templateRule',
  data() {
    return {}
  },
}
</script>

<style scoped>
.layui-col-md6 {
  width: 50%;
  float: left;
}
.layui-col-md12 {
  clear: both;
}
.article {
  margin-bottom: 20px;
}
.article_indent {
  text-indent: 20px;
}
.sub2title_num {
  margin-right: 5px;
  color: #ffffff;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  background: #1cb496;
  display: inline-block;
  text-align: center;
}
.f-9 {
  color: #999999;
}
.half_para {
  background: #f2f2f2;
  margin: 10px;
  border-radius: 4px;
  padding: 10px;
}
.bubble {
  padding: 10px 30px;
  overflow: hidden;
}
.bubble_line {
  position: relative;
}
.clearFix {
  content: '\20';
  display: block;
  height: 0;
  clear: both;
}
.triangle {
  position: absolute;
  top: 10px;
  left: -8px;
  display: block;
  width: 0;
  height: 0;
  overflow: hidden;
  line-height: 0;
  font-size: 0;
  border-bottom: 8px solid transparent;
  border-top: 8px solid transparent;
  border-left: none;
  border-right: 8px solid #25bfa1;
}
.bubble_article {
  color: #fff;
  display: inline-block;
  margin-bottom: 15px;
  zoom: 1;
  padding: 5px 10px;
  border: 1px solid #18b999;
  background: #eee;
  border-radius: 5px;
  background-color: #36debd;
  background-image: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(#38c1a6),
    to(#12bd9b)
  );
  background-image: -webkit-linear-gradient(top, #38c1a6, #12bd9b);
  background-image: -moz-linear-gradient(center top, #38c1a6, #12bd9b);
  background-image: linear-gradient(top, #38c1a6, #12bd9b);
  float: left;
}
.stylePading {
  padding: 10px 20px;
}
.rule_title {
  margin-bottom: 10px;
  font-size: 18px;
  color: #333;
}
.rule_subtitle {
  color: #333333;
}
.third_title {
  margin-top: 10px;
}
</style>
