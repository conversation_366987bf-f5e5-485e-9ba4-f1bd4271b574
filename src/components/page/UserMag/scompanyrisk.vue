<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="公司名" prop="company">
            <el-input v-model="formInline.company" placeholder></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="formInline.mobile" placeholder></el-input>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">新增风险账号</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="scompanyrisk"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <!-- <vxe-column type="selection" width="46"></vxe-column> -->
          <vxe-column field="公司名" title="公司名">
            <template v-slot="scope">
              <div>{{ scope.row.company }}</div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号">
            <template v-slot="scope">
              <div>{{ scope.row.mobile }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createUser }}</div>
            </template>
          </vxe-column>
          <vxe-column field="原因" title="原因">
            <template v-slot="scope">
              <div>{{ scope.row.reason }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="更新时间" title="更新时间" width="170">
            <template v-slot="scope">
              <div v-if="scope.row.updateTime">{{
                moment(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="150" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                style="margin-left: 0px; color: #4099ff;"
                @click="detailsRow(scope.$index, scope.row)"
                >编辑</el-button
              >
              <el-button
                link
                style="margin-left: 10px; color: red"
                @click="delState(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增账单 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          label-width="110px"
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="公司名" prop="">
            <el-input
              class="input-w"
              v-model="formop.company"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="">
            <el-input
              class="input-w"
              v-model="formop.mobile"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="原因" prop="">
            <el-input
              class="input-w"
              type="textarea"
              v-model="formop.reason"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'syscompanyrisk',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      titleMap: {
        add: '新增风险账号',
        edit: '编辑风险账号',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        company: '', //公司名
        mobile: '', //手机号
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        company: '', //公司名
        mobile: '', //手机号
        currentPage: 1,
        pageSize: 10,
      },
      //添加列表
      formop: {
        company: '', //公司名
        mobile: '', //号码
        reason: '', //原因
      },
      id: '', //列表行id
      rules: {
        company: [
          {
            required: true,
            message: '公司名不能为空',
            trigger: 'change',
          },
        ],
        mobile: [
          {
            required: true,
            message: '手机号不能为空',
            trigger: 'change',
          },
        ],
        reason: [
          { required: true, message: '原因不能为空', trigger: 'change' },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'syscompanyrisk/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handedTime(e) {
      this.formop.startTime = moment(e).format('YYYY-MM-DD hh:mm:ss')
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.omcs + 'syscompanyrisk',
              this.formop,
              (res) => {
                if (res.code != 200) {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'success',
                  // })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.omcs + 'syscompanyrisk',
              this.formop,
              (res) => {
                if (res.code != 200) {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'warning',
                  // })
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'success',
                  // })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },

    detailsRow(index, val) {
      // console.log(val);
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })
      // this.formop.status = val.status+""
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确认执行此操作吗？',
        window.path.omcs + 'syscompanyrisk/' + val.id,
        {},
        (res) => {
          if (res.code == 400) {
            // this.$message({
            //   message: res.msg,
            //   type: 'warning',
            // })
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
          }
          this.gettableLIst()
        }
      )
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.id = ''
        this.$refs.formop.resetFields()
        this.formop = {
          company: '', //公司名
          mobile: '', //号码
          reason: '', //原因
        }
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.input-w {
  width: 300px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>