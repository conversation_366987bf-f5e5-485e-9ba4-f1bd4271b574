<template>
    <div style="background: #fff; padding: 15px">
        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <el-form-item label="通道ID">
                    <el-input class="input-w" v-model="searchForm.channelId" placeholder="请输入通道ID" clearable></el-input>
                </el-form-item>
                <el-form-item label="开始时间">
                    <el-date-picker
                        v-model="searchForm.beginTime"
                        type="datetime"
                        placeholder="选择开始时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束时间">
                    <el-date-picker
                        v-model="searchForm.endTime"
                        type="datetime"
                        placeholder="选择结束时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        clearable>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 工具栏 -->
        <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
                <el-button type="primary" @click="handleAdd">添加</el-button>
            </template>
        </vxe-toolbar>

        <!-- 数据表格 -->
        <vxe-table ref="restartTableRef" id="channelRestartPlanTable" border stripe show-header-overflow
            :custom-config="customConfig" :column-config="{ resizable: true }" :row-config="{ isHover: true }"
            min-height="1" v-loading="loading" element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;" :data="tableData">
            <vxe-column field="id" title="ID" min-width="80"></vxe-column>
            <vxe-column field="channelId" title="通道ID" min-width="120"></vxe-column>
            <vxe-column field="beginTime" title="开始时间" min-width="160">
                <template #default="{ row }">
                    {{ row.beginTime ? moment(row.beginTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="endTime" title="结束时间" min-width="160">
                <template #default="{ row }">
                    {{ row.endTime ? moment(row.endTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="nextTime" title="下次执行时间" min-width="160">
                <template #default="{ row }">
                    {{ row.nextTime ? moment(row.nextTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="connectNum" title="连接数" min-width="100"></vxe-column>
            <vxe-column field="maxNum" title="最大连接数" min-width="120"></vxe-column>
            <vxe-column field="操作" title="操作" fixed="right" width="200">
                <template #default="{ row }">
                    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
            </vxe-column>
        </vxe-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pagination.currentPage" :page-size="pagination.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
        </div>

        <!-- 弹窗表单 -->
        <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑通道重启计划' : '添加通道重启计划'" width="600px"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="通道ID" prop="channelId">
                    <el-input v-model="form.channelId" placeholder="请输入通道ID"></el-input>
                </el-form-item>
                <el-form-item label="开始时间" prop="beginTime">
                    <el-date-picker
                        v-model="form.beginTime"
                        type="datetime"
                        placeholder="选择开始时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="结束时间" prop="endTime">
                    <el-date-picker
                        v-model="form.endTime"
                        type="datetime"
                        placeholder="选择结束时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="下次执行时间" prop="nextTime">
                    <el-date-picker
                        v-model="form.nextTime"
                        type="datetime"
                        placeholder="选择下次执行时间"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="连接数" prop="connectNum">
                    <el-input-number v-model="form.connectNum" :min="0" placeholder="请输入连接数" style="width: 100%"></el-input-number>
                </el-form-item>
                <el-form-item label="最大连接数" prop="maxNum">
                    <el-input-number v-model="form.maxNum" :min="0" placeholder="请输入最大连接数" style="width: 100%"></el-input-number>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import moment from 'moment';

// 表格引用
const restartTableRef = ref(null);
const toolbarRef = ref(null);

// 表格配置
const customConfig = {
    storage: true
};

// 搜索表单
const searchForm = reactive({
    channelId: '',
    beginTime: '',
    endTime: ''
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
});

// 弹窗表单
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const form = reactive({
    id: 0,
    channelId: '',
    beginTime: '',
    endTime: '',
    nextTime: '',
    connectNum: 0,
    maxNum: 0
});

// 表单验证规则
const rules = {
    channelId: [
        { required: true, message: '请输入通道ID', trigger: 'blur' }
    ],
    beginTime: [
        { required: true, message: '请选择开始时间', trigger: 'change' }
    ],
    endTime: [
        { required: true, message: '请选择结束时间', trigger: 'change' }
    ],
    nextTime: [
        { required: true, message: '请选择下次执行时间', trigger: 'change' }
    ],
    connectNum: [
        { required: true, message: '请输入连接数', trigger: 'blur' }
    ],
    maxNum: [
        { required: true, message: '请输入最大连接数', trigger: 'blur' }
    ]
};

// 获取列表数据
const getRestartPlanList = async () => {
    loading.value = true;
    try {
        const params = {
            current: pagination.currentPage,
            size: pagination.pageSize,
            ...searchForm
        };

        window.api.post(window.path.omcs + 'operatingchannelrestarttask/page', params, (res) => {
            if (res.code == 200) {
                tableData.value = res.data.records;
                pagination.total = res.data.total;
                loading.value = false;
            } else {
                ElMessage.error(res.msg);
                loading.value = false;
            }
        }, (error) => {
            loading.value = false;
            ElMessage.error('获取列表失败，请稍后重试');
        });
    } catch (error) {
        console.error('获取列表失败:', error);
        ElMessage.error('获取列表失败，请稍后重试');
        loading.value = false;
    }
};

// 查询
const handleSearch = () => {
    pagination.currentPage = 1;
    getRestartPlanList();
};

// 重置查询条件
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = '';
    });
    handleSearch();
};

// 处理页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    getRestartPlanList();
};

// 处理每页显示数量变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    getRestartPlanList();
};

// 添加
const handleAdd = () => {
    isEdit.value = false;
    resetForm();
    dialogVisible.value = true;
};

// 编辑
const handleEdit = (row) => {
    isEdit.value = true;
    // 填充表单数据
    form.id = row.id;
    form.channelId = row.channelId;
    form.beginTime = row.beginTime;
    form.endTime = row.endTime;
    form.nextTime = row.nextTime;
    form.connectNum = row.connectNum;
    form.maxNum = row.maxNum;

    dialogVisible.value = true;
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                const submitData = {
                    ...form
                };

                if (isEdit.value) {
                    // 编辑操作使用PUT方法
                    window.api.put(window.path.omcs + 'operatingchannelrestarttask', submitData, (res) => {
                        if (res.code == 200) {
                            getRestartPlanList();
                            ElMessage.success('修改成功');
                            dialogVisible.value = false;
                        } else {
                            ElMessage.error(res.msg);
                        }
                    }, (error) => {
                        console.error('修改失败:', error);
                        ElMessage.error('修改失败，请稍后重试');
                    });
                } else {
                    // 新增操作使用POST方法
                    window.api.post(window.path.omcs + 'operatingchannelrestarttask', submitData, (res) => {
                        if (res.code == 200) {
                            getRestartPlanList();
                            ElMessage.success('添加成功');
                            dialogVisible.value = false;
                        } else {
                            ElMessage.error(res.msg);
                        }
                    }, (error) => {
                        console.error('添加失败:', error);
                        ElMessage.error('添加失败，请稍后重试');
                    });
                }
            } catch (error) {
                console.error(isEdit.value ? '修改失败:' : '添加失败:', error);
                ElMessage.error(isEdit.value ? '修改失败，请稍后重试' : '添加失败，请稍后重试');
            }
        } else {
            return false;
        }
    });
};

// 删除
const handleDelete = async (row) => {
    try {
        await ElMessageBox.confirm('确定要删除该通道重启计划吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        window.api.delete(window.path.omcs + 'operatingchannelrestarttask/' + row.id, {}, () => {
            ElMessage.success('删除成功');
            getRestartPlanList();
        }, (error) => {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        });
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        }
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    form.id = 0;
    form.channelId = '';
    form.beginTime = '';
    form.endTime = '';
    form.nextTime = '';
    form.connectNum = 0;
    form.maxNum = 0;
};

// 监听对话框关闭事件
watch(dialogVisible, (newValue) => {
    if (!newValue) {
        resetForm();
    }
});

// 页面加载时获取数据
onMounted(() => {
    const $table = restartTableRef.value
    const $toolbar = toolbarRef.value
    if ($table && $toolbar) {
        $table.connect($toolbar)
    }
    getRestartPlanList();
});
</script>

<style scoped>
.search-container {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 4px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.input-w {
    width: 200px;
}
</style>
