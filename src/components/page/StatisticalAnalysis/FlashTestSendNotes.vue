<template>
  <div class="container_left">
    <div class="fillet Statistics-box" style="padding: 6px 18px 18px 18px">
      <div class="passageWay-title">
        <div style="display: flex; align-items: center; width: 650px;">
          <span
            style="
              display: inline-block;
              width: 68px;
              text-align: right;
              padding-right: 10px;
            "
            >时间</span
          >
          <el-radio-group
            v-model="flag"
            size="default"
            text-color="#fff"
            fill="#16a589"
            @change="handleChangeTimeOptions"
          >
            <el-radio-button value="1">今天</el-radio-button>
            <el-radio-button value="2">近一个月</el-radio-button>
          </el-radio-group>
          <el-date-picker
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="datePluginValue"
            type="datetimerange"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            range-separator="至"
            @change="timeClick"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </div>
        <!-- 查询框开始 -->
        <el-form
          :inline="true"
          :model="form"
          label-width="85px"
          class="demo-form-inline"
          ref="sendjl"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input v-model="form.username" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="appid" prop="appid">
            <el-input v-model="form.appid" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="form.mobile" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="流水号" prop="processId">
            <el-input v-model="form.processId" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="charge">
            <el-select v-model="form.charge" class="input-w">
              <el-option label="全部" value=""></el-option>
              <el-option label="计费" value="0"></el-option>
              <el-option label="不计费" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="form.type" class="input-w">
              <el-option label="全部" value=""></el-option>
              <el-option label="一键登录" value="onelogin"></el-option>
              <el-option label="本机号码校验" value="onepass"></el-option>
            </el-select>
          </el-form-item>
          <div>
            <el-button
              type="primary"
              plain
              @click.prevent="Query()"
              @keyup.enter="Query()"
              >查询</el-button
            >
            <el-button type="primary" plain @click="reset('sendjl')"
              >重置</el-button
            >
          </div>
        </el-form>
        <!-- <div class="boderbottom">
                       
                        </div> -->
        <!-- 查询框结束 -->
        <div class="passage-table">
          <!-- 表格和分页开始 -->
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="FlashTestSendNotes"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="用户名称" title="用户名称" width="120px">
              <template v-slot="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
                <!-- <span>
                                        {{ scope.row.username}}
                                    </span> -->
                <div
                  style="color: #16a589; cursor: pointer"
                  @click="rouTz(scope.row)"
                >
                  {{ scope.row.username }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="appid" title="appid" width="200px">
              <template v-slot="scope">
                <div>
                  {{ scope.row.appid }}
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.appid,$event )"></i> -->
                  <CopyTemp :content="scope.row.appid" />
                </div>
              </template>
            </vxe-column>
            <vxe-column field="手机号" title="手机号" width="110px">
              <template v-slot="scope">
                <div
                  style="color: #16a589; cursor: pointer"
                  @click="tableContent(scope.row, scope.$index)"
                >
                  <span>{{ scope.row.mobile }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="流水号" title="流水号">
              <template v-slot="scope">
                <div>
                  <span>{{ scope.row.processId }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="ip" title="ip">
              <template v-slot="scope">
                <div>
                  <span>{{ scope.row.ip }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="状态" title="状态" width="90px">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true" v-if="scope.row.charge == 0" type="primary" effect="dark">
                  成功
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-else-if="scope.row.charge == 1"
                  type="info"
                  effect="dark"
                >
                  不计费
                </el-tag>
                <!-- <span v-if="scope.row.charge == 0">计费</span>
                                    <span v-else-if="scope.row.charge == 1">不计费</span> -->
              </template>
            </vxe-column>
            <vxe-column field="类型" title="类型" width="110px">
              <template v-slot="scope">
                <div v-if="scope.row.type == 'onelogin'">一键登录</div>
                <div v-else-if="scope.row.type == 'onepass'"
                  >本机号码校验</div
                >
              </template>
            </vxe-column>
            <vxe-column field="创建时间" title="创建时间" width="170px">
              <template v-slot="scope">
                <div>
                  <span>{{ scope.row.createTime }}</span>
                </div>
              </template>
            </vxe-column>
          </vxe-table>
          <!--分页-->
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formData.currentPage"
              :page-size="formData.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.totalRow"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
          <!-- 表格和分页结束 -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //时间
import TableTem from '@/components/publicComponents/TableTem.vue' //列表
import UserLists from '@/components/publicComponents/userList.vue'
import moment from 'moment'
// import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    ChannelView,
    UserLists,
    CopyTemp
  },
  name: 'FlashTestSendNotes',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      userFlag: false,
      nameover: '',
      dialogStatus: '', //新增编辑标题
      selectId: '', //列表id
      formop: {
        //表单数据
        remark: '',
      },
      flag: '1', //选择那一天
      Times: {
        beginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      },
      datePluginValue: [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        // onPick: ({ maxDate, minDate }) => {
        //     this.pickerMinDate = minDate.getTime();
        //     if (maxDate) {
        //         this.pickerMinDate = ''
        //     }
        // },
        // disabledDate: (time) => {
        //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
        //         return false
        //     }
        //     if (this.pickerMinDate !=='') {
        //         const day7 = 6 * 24 * 3600 * 1000
        //         let maxTime = this.pickerMinDate + day7
        //         if (maxTime >  Date.now()) {
        //             maxTime =  Date.now()
        //         }
        //         const minTime = this.pickerMinDate - day7
        //         return time.getTime() < minTime || time.getTime() > maxTime
        //     }
        //     return time.getTime() > Date.now();
        // }
      },
      //发送查询的值
      form: {
        mobile: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
        appid: '',
        charge: '',
        processId: '',
        type: '',
        username: '',
        userId: '',
      },
      //复制发送查询的值
      formData: {
        mobile: '',
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
        appid: '',
        charge: '',
        processId: '',
        type: '',
        username: '',
        userId: '',
      },
      activeName2: 'first', //发送和回复选项卡
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        totalRow: 0,
        tableData: [],
      },
      tableDataObj1: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    }
  },
  methods: {
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsReplyId)
      }
      this.selectId = selectId.join(',')
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // handleCopy(name,event){
    // clip(name, event)
    // },
    // 点击手机号
    tableContent(row, index) {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.decryptMobile,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile=res.data
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      )
      //  window.api.get(window.path.omcs + "smsMessage/decrypt?smsInfoId="+val,{},res=>{
      //     if(res.code==200){
      //         this.tableDataObj.tableData[index].mobile=res.data[0]
      //     }else{
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }
      // })
    },
    //获取 发送记录数据
    getChallDate() {
      let aa = {}
      Object.assign(aa, this.formData)
      aa.flag = this.flag
      aa.beginTime = this.Times.beginTime
      aa.endTime = this.Times.endTime
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerflash/message/page',
        aa,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data.records
            this.tableDataObj.totalRow = res.data.total
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = []
          }
        }
      )
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    timeClick(val) {
      if (val) {
        this.Times.beginTime = this.moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = this.moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
        this.flag = '3'
      } else {
        this.Times.beginTime = ""
        this.Times.endTime = ""
        this.flag = ''
      }
      
      // if(val){
      //     this.Times.beginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
      //     this.Times.endTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      //     this.flag='5'
      // }else{
      //     this.flag='1'
      // }
    },
    handleChangeTimeOptions: function () {
      // this.datePluginValue = ''
    },
    //发送查询
    Query() {
      Object.assign(this.formData, this.form)
      this.getChallDate()
    },
    //发送重置
    reset() {
      if (this.flag == '1') {
        // console.log(11);
        this.$refs.sendjl.resetFields()
        this.Times.beginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
        Object.assign(this.formData, this.form)
        this.getChallDate()
      } else {
        this.flag = '1'
        this.$refs.sendjl.resetFields()
        Object.assign(this.formData, this.form)
      }
      // this.flag='1'
      // this.$refs.sendjl.resetFields();
      // Object.assign(this.formData,this.form);
    },
    handleSizeChange(size) {
      this.formData.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formData.currentPage = currentPage
    },
  },
  created() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getChallDate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getChallDate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    // this.getChallDate();
    // this.getChallDate();
  },
  watch: {
    //监听时间范围
    flag: function (val) {
      // if(val != '5'){
      //     this.Times.beginTime = '';
      //     this.Times.endTime = '';
      //     this.datePluginValue='';
      // }
      if (val == '1') {
        this.Times.beginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.Times.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        ]
      } else if (val == '2') {
        this.Times.beginTime = moment()
          .subtract(30, 'days')
          .format('YYYY-MM-DD 00:00:00')
        this.Times.endTime = moment().format('YYYY-MM-DD HH:mm:ss')
        this.datePluginValue = [
          moment().subtract(30, 'days').format('YYYY-MM-DD 00:00:00'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ]
      }
      this.getChallDate()
    },
    //监听时间是否改变
    Times: {
      handler(val) {
        if (val) {
          this.getChallDate()
        }
      },
      deep: true,
    },
    formData: {
      handler(val) {
        this.getChallDate()
      },
      deep: true,
    },
  },
}
</script>

<style scoped>
.search-date {
  position: relative;
  top: 2px;
  margin-bottom: 6px;
  margin-top: 10px;
}
.demo-form-inline {
  margin-top: 12px;
}
.passage-table {
  margin-bottom: 40px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
