<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          label-width="82px"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" label-width="82px" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="公司名称" label-width="82px" prop="compName">
            <el-input
              v-model="sensitiveCondition.compName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" label-width="82px" prop="status">
            <el-select
              v-model="sensitiveCondition.status"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="待认证" value="0"></el-option>
              <el-option label="合同待签署" value="1"></el-option>
              <el-option label="认证不通过" value="2"></el-option>
              <el-option label="已认证" value="3"></el-option>
              <el-option label="合同签署中" value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间" label-width="82px" prop="time1">
            <el-date-picker
              class="input-w"
              v-model="sensitiveCondition.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande1"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="审核时间" label-width="82px"  prop="time2">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time2"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande2"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div>
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-fun">
        <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">签名审核列表</span> -->
        <!-- <el-button type="primary"  @click="establishSig()">创建签名</el-button> -->
        <!-- <el-button type="primary" @click="sensitiveBatchDel()" v-if="remarkObj.formData.idArr.length != 0">批量通过</el-button>
                    <el-button type="primary" @click="sensitiveBatchSet()" v-if="remarkObj.formData.idArr.length != 0">批量驳回</el-button> -->
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="AuditCertification"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <!-- <vxe-column field="签名ID" title="签名ID" width="80">
                            <template #default="scope">{{ scope.row.id }}</template>
                        </vxe-column> -->
          <vxe-column field="用户名称" title="用户名称">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row)'>
                                    <UserLists v-if="userFlag&&scope.row.username==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              <div
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
              >
                {{ scope.row.username }}
              </div>
              <!-- <div class="userL" v-if="overF&&scope.row.username==nameover">{{nameover}}</div> -->
            </template>
          </vxe-column>
          <vxe-column field="姓名" title="姓名">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.name"
                :content="scope.row.name"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="公司名称" title="公司名称">
            <template v-slot="scope">
              <!-- <span>{{ scope.row.signature }}</span> -->
              <Tooltip
                v-if="scope.row.compName"
                :content="scope.row.compName"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
              <!-- <span style="color: #f56c6c">{{ scope.row.compName }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="认证类型" title="认证类型">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.type == 1">法人认证</span>
                <span v-else-if="scope.row.type == 2">代理人认证</span>
                <span v-else>个体工商户认证</span>
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="手机号码">
                             <template #default="scope">{{ scope.row.phone}}</template>
                        </vxe-column> -->
          <!-- <vxe-column field="" title="身份证号码">
                             <template #default="scope">{{ scope.row.idCard}}</template>
                        </vxe-column>
                        <vxe-column field="" title="证照编号">
                             <template #default="scope">{{ scope.row.number}}</template>
                        </vxe-column> -->
          <!-- <vxe-column field="" title="证件照" width="80">
                            <template #default="scope">
                                <el-button type="text" @click="lookUp(scope.$index, scope.row)"><i class="el-icon-picture"></i>&nbsp;预览</el-button>
                             </template>
                        </vxe-column> -->
          <vxe-column field="提交时间" title="提交时间" width="110">
            <template v-slot="scope">
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
              </p>
              <!-- <span v-if="scope.row.createTime">{{
                    scope.row.createTime | fmtDate
                  }}</span> -->
            </template>
          </vxe-column>
          <!-- <vxe-column field="初申请时间" title="初申请时间">
                <template #default="scope">
                  <span v-if="scope.row.auditTime">{{
                    scope.row.auditTime | fmtDate
                  }}</span>
                </template>
              </vxe-column> -->

          <vxe-column field="审核状态" title="审核状态">
            <template v-slot="scope">
              <el-tag
                :disable-transitions="true" v-if="scope.row.status == '0'"  type="info" effect="dark">
                待认证
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == '1'"
                
                type="info"
                effect="dark"
              >
                合同待签署
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == '2'"
                
                type="danger"
                effect="dark"
              >
                认证不通过
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == '3'"
                
                type="success"
                effect="dark"
              >
                已认证
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-else-if="scope.row.status == '4'"
                
                type="warning"
                effect="dark"
              >
                合同签署中
              </el-tag>
              <!-- <span v-if="scope.row.status == '0'">待认证</span>
                  <span v-else-if="scope.row.status == '1'" style="color: #f56c6c">合同待签署</span>
                  <span v-else-if="scope.row.status == '2'" style="color: #f56c6c">认证不通过</span>
                  <span v-else-if="scope.row.status == '3'" style="color: #16a589">已认证</span>
                  <span v-else-if="scope.row.status == '4'" style="color: #f56c6c">合同签署中</span> -->
            </template>
          </vxe-column>
          <vxe-column field="原因" title="原因">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.checkReason"
                :content="scope.row.checkReason"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
              <!-- <el-tooltip class="item" effect="dark" placement="top">
                    <div class="tooltip" slot="content">
                      {{ scope.row.checkReason }}
                    </div>
                    <span class="span">{{ scope.row.checkReason }}</span>
                  </el-tooltip> -->

              <!-- <span>{{scope.row.auditReason}}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="合同编号" title="合同编号">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.contractCode"
                :content="scope.row.contractCode"
                className="wrapper-text"
                effect="dark"
              ></Tooltip>
              <!-- <span>{{ scope.row.contractCode }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="120">
            <template v-slot="scope">
              <!-- <el-button type="text" style="color:#f56c6c;margin-left:0px;" @click="handleEdit(scope.$index, scope.row)"><i class="el-icon-delete"></i>&nbsp;删除</el-button> -->
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                @click="handleAdopt(1, scope.row)"
                ><el-icon><Tickets /></el-icon>&nbsp;查看</el-button
              >
              <!-- <el-button type="text" style="color:orange;margin-left:0px;" @click="handleReject(2,scope.row)"><i class="el-icon-error"></i>&nbsp;不通过</el-button> -->
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹框 （审核 通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="500px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="80px"
          style="padding: 0 28px"
        >
          <el-form-item label="拒绝原因" prop="auditReason">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog title="证件照" v-model="dialogVisible" width="30%">
        <el-image
          style="width: 250px; height: 250px"
          :z-index="9999"
          :src="authorization"
          :preview-src-list="srcList"
        >
        </el-image>
        <el-image
          style="width: 250px; height: 250px"
          :z-index="9999"
          :src="businessLicense"
          :preview-src-list="srcList1"
        >
        </el-image>
      </el-dialog>
      <el-dialog
        title="详情"
        v-model="dialogTemplate"
        :close-on-click-modal="false"
        width="700px"
      >
        <div>
          <div class="detailsList">
            <span class="detailsList-title">用户名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.username
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">认证类型:</span>
            <span class="detailsList-content" v-if="TemplateObject.type == 1"
              >法人认证</span
            >
            <span
              class="detailsList-content"
              v-else-if="TemplateObject.type == 2"
              >代理人认证</span
            >
            <span class="detailsList-content" v-else>个体工商户认证</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">公司名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.compName
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">姓名:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.name
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">手机号:</span>
            <span class="detailsList-content" style="width: 300px">{{
              TemplateObject.phone
            }}</span>
          </div>
          <!-- <div class="detailsList">
                <span class="detailsList-title">身份证号:</span>
                <span class="detailsList-content" style="width: 300px;">{{TemplateObject.idCard}}</span>
              </div> -->
          <!-- <div class="detailsList">
                <span class="detailsList-title">证照编号:</span>
                <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span>
              </div> -->
          <div class="detailsList">
            <span class="detailsList-title">身份证正反照:</span>
            <el-image
              v-if="TemplateObject.idCardFront"
              style="width: 100px; height: 100px"
              :z-index="9999"
              :src="API.imgU + TemplateObject.idCardFront"
              :preview-src-list="srcList2"
            >
            </el-image>
            <el-image
              v-if="TemplateObject.idCardBack"
              :z-index="9999"
              style="width: 100px; height: 100px"
              :src="API.imgU + TemplateObject.idCardBack"
              :preview-src-list="srcList3"
            >
            </el-image>
            <!-- <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span> -->
          </div>
          <div class="detailsList">
            <span class="detailsList-title">证件照:</span>
            <el-image
              v-if="TemplateObject.authorization && TemplateObject.type == 2"
              style="width: 100px; height: 100px; z-index: 99999"
              :z-index="99999"
              :src="API.imgU + TemplateObject.authorization"
              :preview-src-list="srcList"
            >
            </el-image>
            <el-image
              style="width: 100px; height: 100px; z-index: 99999"
              :z-index="9999"
              :src="API.imgU + TemplateObject.businessLicense"
              :preview-src-list="srcList1"
            >
            </el-image>
            <!-- <span class="detailsList-content" style="width: 300px;">{{TemplateObject.number}}</span> -->
          </div>
          <div class="detailsList">
            <span class="detailsList-title">原因:</span>
            <span class="detailsList-content">{{
              TemplateObject.checkReason
            }}</span>
          </div>
          <div class="detailsList">
            <span class="detailsList-title">审核状态:</span>
            <span
              class="detailsList-content"
              v-if="TemplateObject.status == '0'"
              >待认证</span
            >
            <span
              class="detailsList-content"
              v-else-if="TemplateObject.status == '1'"
              style="color: #f56c6c"
              >合同待签署</span
            >
            <span
              class="detailsList-content"
              v-else-if="TemplateObject.status == '2'"
              style="color: #f56c6c"
              >认证不通过</span
            >
            <span
              class="detailsList-content"
              v-else-if="TemplateObject.status == '3'"
              style="color: #16a589"
              >已认证</span
            >
            <span
              class="detailsList-content"
              v-else-if="TemplateObject.status == '4'"
              style="color: #f56c6c"
              >合同签署中</span
            >
          </div>

          <!-- <div class="detailsList">
                <span class="detailsList-title">上次审核人:</span>
                <span class="detailsList-content">{{
                  TemplateObject.auditName
                }}</span>
              </div>
              <div class="detailsList">
                <span class="detailsList-title">上次审核时间:</span>
                <span class="detailsList-content" v-if="TemplateObject.auditTime">{{
                  moment(TemplateObject.auditTime).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
                <span class="detailsList-content" v-else></span>
              </div>
              <div class="detailsList">
                <span class="detailsList-title">上次审核备注:</span>
                <span class="detailsList-content">{{
                  TemplateObject.auditReason
                }}</span>
              </div> -->
          <div
            class="detailsList"
            v-if="TemplateObject.downloadUrl && TemplateObject.status == '3'"
          >
            <span class="detailsList-title">合同:</span>
            <span
              style="color: blue; cursor: pointer"
              @click="download(TemplateObject.downloadUrl)"
              class="detailsList-content"
            >
              <!-- <a
                    :href="TemplateObject.viewUrl"
                    target="_blank"
                    rel="noopener noreferrer"
                    >查看合同</a
                  > -->
              点击下载
            </span>
          </div>
        </div>
        <!-- <div class="detailsList" v-if="TemplateObject.status == 0">
              <span class="detailsList-title">备注:</span>
              <span class="bs">*</span
              ><el-input
                style="width: 300px; margin-top: 10px"
                type="textarea"
                v-model="auditReason"
              ></el-input>
            </div>
            <template
              #footer
              v-if="TemplateObject.status == 0"
            >
              <el-button type="primary" @click="templaS()">开始认证</el-button>
              <el-button type="danger" @click="templaF()">驳回</el-button>
            </template> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import { ElMessage } from 'element-plus'
import { mapState } from 'vuex'
import moment from 'moment'
export default {
  components: {
    TableTem,
    FileUpload,
    UserLists,
    Tooltip
  },
  name: 'SignatureAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      userFlag: false,
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      SigDialogVisible: false, //弹出框创建签名显示隐藏
      remarkshow: false,
      overF: false,
      dialogTemplate: false,
      dialogVisible: false, //证件照
      authorization: '',
      businessLicense: '',
      TemplateObject: {},
      auditReason: '',
      nameover: '',
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          idArr: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: '请输入拒绝原因', trigger: 'blur' },
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      srcList: [],
      srcList1: [],
      srcList2: [],
      srcList3: [],
      idArrs: [], // 驳回的id
      title: '',
      signatureFrom: {
        title: '',
        formData: {
          signature: '',
          clientName: '',
          signatureType: '1',
          remark: '',
          imgUrl: '',
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: '该输入项为必填项!', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
          ],
          signatureType: [
            { required: true, message: '请选择签名类型', trigger: 'change' },
          ],
          imgUrl: [
            { required: true, message: '请选择上传图片', trigger: 'change' },
          ],
        },
        signature: '', //签名
        id: '', //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      status: [],
      //查询条件的值
      sensitiveCondition: {
        compName: '',
        phone: '',
        status: '',
        consumerName: '',
        beginTime: '',
        endTime: '',
        time1: [],
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        compName: '',
        phone: '',
        status: '',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
      isFirstEnter: false,
    }
  },
  mounted() {},
  computed: {},
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatinguser/certificate/page',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    sensitiveQuery() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      this.sensitiveCondition.status = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
          moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.auditEndTime =
          moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.auditStartTime = ''
        this.sensitiveCondition.auditEndTime = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
      this.getTableDtate()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
      this.getTableDtate()
    },
    //通过
    handleAdopt(status, row) {
      console.log(row)
      this.dialogTemplate = true
      this.TemplateObject = row
      this.srcList.push(window.path.imgU + row.authorization)
      this.srcList1.push(window.path.imgU + row.businessLicense)
      this.srcList2.push(window.path.imgU + row.idCardFront)
      this.srcList3.push(window.path.imgU + row.idCardBack)
    },
    templaS() {
      console.log(this.TemplateObject, 'll')
      if (this.auditReason == '') {
        this.$message({
          type: 'warning',
          duration: '2000',
          message: '请填写备注',
        })
      } else {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.omcs + 'operatinguser/certificate/audit',
          {
            auditStatus: 1,
            ids: this.TemplateObject.id,
            auditReason: this.auditReason,
          },
          (res) => {
            this.dialogTemplate = false
            this.getTableDtate()
          }
        )
      }
    },
    templaF() {
      if (this.auditReason == '') {
        this.$message({
          type: 'warning',
          duration: '2000',
          message: '请填写备注',
        })
      } else {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.omcs + 'operatinguser/certificate/audit',
          {
            auditStatus: 2,
            ids: this.TemplateObject.id,
            auditReason: this.auditReason,
          },
          (res) => {
            this.dialogTemplate = false
            this.getTableDtate()
          }
        )
      }
    },
    //批量通过
    sensitiveBatchDel() {
      let ids = this.remarkObj.formData.idArr.join(',')
      // console.log(ids);
      this.$confirms.confirmation(
        'post',
        '此操作不可逆，是否继续?',
        window.path.omcs + 'operatinguser/certificate/audit',
        { auditStatus: 1, ids: ids },
        (res) => {
          this.getTableDtate()
          this.remarkObj.formData.idArr = []
        }
      )
    },
    //驳回
    handleReject(index, row) {
      this.idArrs = []
      this.remarkshow = true
      this.title = '驳回备注'
      this.idArrs.push(row.id) //赋值ID
      this.idArrs.join(',')
      console.log(this.idArrs.join(','))
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let aa = {}
          if (this.title == '驳回备注') {
            aa.auditReason = this.remarkObj.formData.auditReason
            aa.ids = this.idArrs.join(',')
            aa.auditStatus = 2
          } else {
            this.operationFlag = false //操作进行中（按钮的显示）
            aa.auditReason = this.remarkObj.formData.auditReason
            aa.ids = this.remarkObj.formData.idArr.join(',')
            aa.auditStatus = 2
          }
          this.$confirm('此操作不可逆，是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
          })
            .then(() => {
              window.api.post(
                window.path.omcs + 'operatinguser/certificate/audit',
                aa,
                (res) => {
                  this.getTableDtate()
                  this.remarkshow = false
                  this.remarkObj.formData.idArr = [] //置空ID
                  if (res.code == 200) {
                    this.$message({
                      type: 'success',
                      duration: '2000',
                      message: '操作成功!',
                    })
                  } else {
                    this.$message({
                      type: 'error',
                      duration: '2000',
                      message: res.msg,
                    })
                  }
                }
              )
            })
            .catch(() => {
              this.operationFlag = true
              this.$message({
                type: 'info',
                duration: '2000',
                message: '已取消操作!',
              })
            })
          // this.$confirms.confirmation('post','此操作不可逆，是否继续?',window.path.omcs + 'signatureaudit/auditNotPass', aa,res =>{
          //         this.getTableDtate();
          //         this.remarkshow = false;
          //         this.remarkObj.formData.idArr=[]; //置空ID
          //     });
        } else {
          return false
        }
      })
    },
    //批量驳回
    sensitiveBatchSet() {
      this.remarkshow = true
    },
    //合同下载
    //合同下载
    download(url) {
      let name = new Date().getTime()
      axios({
        method: 'get',
        url:
          window.path.cpus +
          'v3/file/downloadFile?fileName=' +
          name +
          '&group=oss' +
          '&path=' +
          url +
          '&bucketName=contract',
        data: {},
        headers: {
          'Content-Type': 'application/json',
          Authorization:
            'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
        },
        responseType: 'blob',
      })
        .then(function (res) {
          const blob = new Blob([res.data], {
            type: 'application/octet-stream;charset=utf-8',
          })
          let link = document.createElement('a')
          let href = window.URL.createObjectURL(blob) //下载链接
          link.href = href
          link.text = '下载'
          link.download = name + '.PDF' //下载后文件名
          document.body.appendChild(link)
          link.click() //点击下载
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(href)
          ElMessage({
            message: '下载成功',
            type: 'success',
          })
        })
        .catch((err) => {
          console.log(err, 'err')
          ElMessage({
            message: '下载失败',
            type: 'error',
          })
        })
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let status = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
        //批量操作列表选中项的审核状态
        status.push(val[i].status)
      }
      this.status = status //选中项的审核状态
      this.remarkObj.formData.idArr = selectId //批量通过，不通过的id数组
    },
    //创建签名
    establishSig() {
      this.SigDialogVisible = true
      this.del1 = true
    },
    //移除文件
    fileup(val) {
      let aa = this.signatureFrom.formData.imgUrl.split(',')
      aa.splice(aa.indexOf(val), 1)
      this.signatureFrom.formData.imgUrl = aa.join(',')
    },
    //文件上传成功
    fileupres(val) {
      this.signatureFrom.imgUrl.push(val)
      this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(',')
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    userList(row) {
      this.userFlag = true
      this.nameover = row.username
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
    //    this.getTableDtate();
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  // beforeRouteEnter(to, from, next){
  //     console.log(from,'ll');
  //     if (from.path === '/TemplateAudit') {
  //     //判断是从哪个路由过来的，
  //     //刷新数据
  //     to.meta.isBack = false;
  //   } else {
  //     //不刷新
  //     to.meta.isBack = true;
  //   }
  //   next();

  // },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
        // this.init()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }

    // if(!this.isFirstEnter){
    //     this.getTableDtate();
    // }
  },
  watch: {
    dialogTemplate(val) {
      if (!val) {
        this.srcList = []
        this.srcList1 = []
        this.srcList2 = []
        this.srcList3 = []
      }
    },
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听创建签名弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.signatureFrom.imgUrl = []
        this.signatureFrom.formData.imgUrl = ''
        this.del1 = false
        this.$refs.signatureFrom.resetFields() //清空表单
      }
    },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true
      } else {
        this.disabledinput = false
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.operationFlag = true //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.idArr = [] //置空ID
          this.getTableDtate()
        }
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.detailsList {
  line-height: 42px;
  padding-left: 30px;
  font-size: 12px;
  position: relative;
}
.bs {
  position: absolute;
  top: 0px;
  left: 100px;
  color: red;
  font-size: 14px;
  z-index: 9999;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tooltip {
  width: 300px;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}

.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>


<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>