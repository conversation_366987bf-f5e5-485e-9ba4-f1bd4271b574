<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            label-width="80px"
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="账号" prop="clientName">
              <el-input
                v-model="formInline.clientName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="formInline.title"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="签名" prop="signature">
              <el-input
                v-model="formInline.signature"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="视频Id" prop="videoId">
              <el-input
                v-model="formInline.videoId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="mmsId" prop="mmsid">
              <el-input
                v-model="formInline.mmsid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道号" prop="channelId">
              <el-input
                v-model="formInline.channelId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="tempType">
              <el-select
                v-model="formInline.tempType"
                placeholder="请选择"
                clearable
                class="input-w"
              >
                <el-option label="文本" value="1"></el-option>
                <el-option label="图片" value="2"></el-option>
                <el-option label="视频" value="3"></el-option>
                <el-option label="图片和视频" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="审核状态" prop="status">
              <el-select class="input-w" v-model="formInline.status" clearable>
                <el-option label="通过" value="2"> </el-option>
                <el-option label="未通过" value="3"> </el-option>
                <el-option label="未审核" value="1"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="通道状态" prop="channelStatus">
              <el-select
                class="input-w"
                v-model="formInline.channelStatus"
                clearable
              >
                <el-option label="未审核" value="0"> </el-option>
                <el-option label="审核通过" value="1"> </el-option>
                <el-option label="审核未通过" value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="true"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button type="primary" plain style="" @click.prevent="Query()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>
        <div class="Mail-table" style="padding-bottom: 40px;">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            stripe
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handelSelection"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
              <el-button
                v-if="selectId.length != 0"
                type="primary"
                plain
                style=""
                @click="cancels"
                >批量推送</el-button
              >
              <el-button
                v-if="selectId.length != 0"
                type="primary"
                style=""
                @click="addReport('report')"
                >一键报备</el-button
              >
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="videoTemplate"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handelSelection"
            @checkbox-change="handelSelection">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="账号" title="账号" width="120">
              <template #default="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
                <div>
                  {{ scope.row.username }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="id" title="id" width="110">
              <template #default="scope">
                <div class="video-id">
                  <div>
                    {{ scope.row.videoId }}
                  </div>
                  <div
                    v-if="scope.row.variableTemplate == true"
                    class="watermark"
                  >
                    变
                  </div>
                  <el-tooltip
                    v-if="scope.row.yxTemplateId"
                    effect="light"
                    placement="top"
                  >
                    <template #content>
                      <div>
                        <div style="width: 300px">
                          <el-descriptions title="" direction="vertical" border>
                            <el-descriptions-item label="阅信模版ID">{{
                              scope.row.yxTemplateId
                            }}</el-descriptions-item>
                            <el-descriptions-item label="阅信链接ID">{{
                              scope.row.yxLinkId
                            }}</el-descriptions-item>
                          </el-descriptions>

                          <div
                            v-if="!scope.row.effective"
                            style="color: #f56c6c; margin-top: 8px"
                          >
                            tips：阅信模版链接已失效，请重新绑定阅信模版
                          </div>
                        </div>
                      </div>
                    </template>
                    <div
                      :class="
                        !scope.row.effective ? 'yxrmark err' : 'yxrmark yx'
                      "
                    >
                      阅
                    </div>
                  </el-tooltip>

                  <!-- <el-tooltip effect="light" content="变量模版" placement="top">
                                            <img v-if="scope.row.variableTemplate == true"
                                                src="../../../assets/images/bianliang.png" alt="" srcset=""
                                                style="margin-left: 5px;cursor: pointer;" width="18px">
                                        </el-tooltip> -->
                </div>
              </template>
            </vxe-column>
            <vxe-column field="标题" title="标题" width="220">
              <template #default="scope">
                <div
                  style="color: #409eff; cursor: pointer"
                  @click="View(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.title"
                    :content="scope.row.title"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                </div>
              </template>
            </vxe-column>
            <!-- <vxe-column field="变量模板" title="变量模板" width="140">
                                <template #default="scope">
                                    <span style="color:#67c23a" v-if="scope.row.variableTemplate == true">是</span>
                                    <span style="color:red" v-else>否</span>
                                </template>
                            </vxe-column> -->
            <!-- <vxe-column field="预览" title="预览" width="140">
                                <template #default="scope">
                                    <span style="cursor: pointer;color: #16A589;" @click="View(scope.row)"><i
                                            class="el-icon-picture"></i>预览</span>
                                </template>
                            </vxe-column> -->
            <!-- <vxe-column field="模版大小（KB）" title="模版大小（KB）" width="120">
                                <template #default="scope">{{ scope.row.size }}</template>
                            </vxe-column> -->
            <!-- <vxe-column field="阅信模版ID" title="阅信模版ID" width="140">
                                <template #default="scope">
                                    <div v-if="scope.row.yxTemplateId" style="color: #409eff; cursor: pointer"
                                        @click="handView(scope.row)">
                                        <Tooltip v-if="scope.row.yxTemplateId" :content="scope.row.yxTemplateId"
                                            className="wrapper-text" effect="light">
                                        </Tooltip>
                                    </div>
                                </template>
                            </vxe-column>
                            <vxe-column field="阅信链接ID" title="阅信链接ID" width="140">
                                <template #default="scope">
                                    <div>
                                        {{ scope.row.yxLinkId }}
                                        <el-tooltip effect="light" content="阅信模版链接已失效，请重新绑定阅信模版" placement="top">
                                            <i v-if="scope.row.yxTemplateId && !scope.row.effective" class="el-icon-warning"
                                                style="color: #F56C6C;font-size: 14px;"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </vxe-column> -->
            <vxe-column field="签名" title="签名" width="120">
              <template #default="scope">
                <div>
                  {{ scope.row.signature }}
                </div>
              </template>
            </vxe-column>

            <vxe-column field="创建时间" title="创建时间" width="170">
              <template #default="scope">
                <div>{{ scope.row.createTime }}</div>
              </template>
            </vxe-column>

            <vxe-column field="审核状态" title="审核状态" width="90">
              <template #default="scope">
                <el-tag
                  :disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                  编辑中
                </el-tag>
                <el-tag
                  :disable-transitions="true" v-if="scope.row.status == 1" type="info" effect="dark">
                  未执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.status == 2"
                  type="success"
                  effect="dark"
                >
                  通过
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.status == 3"
                  type="danger"
                  effect="dark"
                >
                  未通过
                </el-tag>
                <!-- <span v-if="scope.row.status == 1" style="color:#E6A23C">未审核</span>
                                    <span v-if="scope.row.status == 2" style="color:rgb(22, 165, 137)">通过</span>
                                    <span v-if="scope.row.status == 3" style="color:rgb(245, 108, 108)">未通过</span> -->
              </template>
            </vxe-column>
            <vxe-column field="移动（通道/mmsid/时间）" title="移动（通道/mmsid/时间）" width="240">
              <template #default="scope">
                <div
                  v-for="(item, index) in scope.row.ydChannels"
                  :key="index"
                  class="channelsStyle"
                >
                  <el-tooltip class="item" effect="dark" placement="top-start">
                    <template v-slot:content>
                      <div>
                        <div v-if="item.message">
                          <span style="margin-right: 10px">{{
                            item.message
                          }}</span>
                          |
                          <span style="margin-left: 10px">{{
                            scope.row.ydReportTime
                          }}</span>
                        </div>
                        <div v-else>
                          <span
                            v-if="item.status == 0"
                            style="margin-right: 10px"
                            >成功</span
                          >
                          <span
                            v-if="item.status == 1"
                            style="margin-right: 10px"
                            >失败</span
                          >
                          <span
                            v-if="item.status == 2"
                            style="margin-right: 10px"
                            >等待审核</span
                          >
                          |
                          <span style="margin-left: 10px">{{
                            scope.row.ydReportTime
                          }}</span>
                        </div>
                        <!-- <span style="margin-right:10px">{{item.message?item.message:'等待审核'}}</span>|<span style="margin-left:10px">{{item.createTime}}</span> -->
                      </div>
                    </template>
                    <div
                      v-if="item.status == 0"
                      style="display: flex; align-items: center"
                    >
                      <el-tag
                  :disable-transitions="true"
                        type="success"
                        closable
                        style="margin-top: 5px"
                        @close="handleCloseTemp(item)"
                      >
                        <span @click="channel(item, scope.row)">{{
                          item.channelId
                        }}</span
                        >|<span>{{ item.mmsId }}</span>
                      </el-tag>
                      <!-- <i @click="channel(item, scope.row)" style="font-size: 16px;color: #409eff;cursor: pointer;" class="el-icon-edit-outline"></i> -->
                    </div>
                    <div v-if="item.status == 1">
                      <el-tag
                  :disable-transitions="true"
                        type="danger"
                        closable
                        style="margin-top: 5px"
                        @close="handleCloseTemp(item)"
                      >
                        <span @click="channel(item, scope.row)">{{
                          item.channelId
                        }}</span
                        >|<span>{{ item.mmsId }}</span>
                      </el-tag>
                    </div>
                    <div v-if="item.status == 2">
                      <el-tag
                  :disable-transitions="true"
                        type="warning"
                        closable
                        style="margin-top: 5px"
                        @close="handleCloseTemp(item)"
                      >
                        <span @click="channel(item, scope.row)">{{
                          item.channelId
                        }}</span
                        >|<span>{{ item.mmsId }}</span>
                      </el-tag>
                    </div>
                  </el-tooltip>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="联通（通道/mmsid/时间）" title="联通（通道/mmsid/时间）" width="240">
              <template #default="scope">
                <div
                  v-for="(item, index) in scope.row.ltChannels"
                  :key="index"
                  class="channelsStyle"
                >
                  <el-tooltip class="item" effect="dark" placement="top-start">
                    <template v-slot:content>
                      <div>
                        <div v-if="item.message">
                          <span style="margin-right: 10px">{{
                            item.message
                          }}</span>
                          |
                          <span style="margin-left: 10px">{{
                            scope.row.ltReportTime
                          }}</span>
                        </div>
                        <div v-else>
                          <span
                            v-if="item.status == 0"
                            style="margin-right: 10px"
                            >成功</span
                          >
                          <span
                            v-if="item.status == 1"
                            style="margin-right: 10px"
                            >失败</span
                          >
                          <span
                            v-if="item.status == 2"
                            style="margin-right: 10px"
                            >等待审核</span
                          >
                          |
                          <span style="margin-left: 10px">{{
                            scope.row.ltReportTime
                          }}</span>
                        </div>
                        <!-- <span style="margin-right:10px">{{item.message?item.message:'等待审核'}}</span>
                                                |
                                                <span style="margin-left:10px">{{item.createTime}}</span> -->
                      </div>
                    </template>
                    <el-tag
                  :disable-transitions="true"
                      v-if="item.status == 0"
                      type="success"
                      closable
                      style="margin-top: 5px"
                      @close="handleCloseTemp(item)"
                    >
                      <span @click="channel(item, scope.row)">{{
                        item.channelId
                      }}</span
                      >|<span>{{ item.mmsId }}</span>
                    </el-tag>
                    <el-tag
                  :disable-transitions="true"
                      v-if="item.status == 1"
                      type="danger"
                      closable
                      style="margin-top: 5px"
                      @close="handleCloseTemp(item)"
                    >
                      <span @click="channel(item, scope.row)">{{
                        item.channelId
                      }}</span
                      >|<span>{{ item.mmsId }}</span>
                    </el-tag>
                    <el-tag
                  :disable-transitions="true"
                      v-if="item.status == 2"
                      type="warning"
                      closable
                      style="margin-top: 5px"
                      @close="handleCloseTemp(item)"
                    >
                      <span @click="channel(item, scope.row)">{{
                        item.channelId
                      }}</span
                      >|<span>{{ item.mmsId }}</span>
                    </el-tag>
                  </el-tooltip>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="电信（通道/mmsid/时间）" title="电信（通道/mmsid/时间）" width="240">
              <template #default="scope">
                <div
                  v-for="(item, index) in scope.row.dxChannels"
                  :key="index"
                  class="channelsStyle"
                >
                  <el-tooltip class="item" effect="dark" placement="top-start">
                    <template v-slot:content>
                      <div>
                        <div v-if="item.message">
                          <span style="margin-right: 10px">{{
                            item.message
                          }}</span>
                          |
                          <span style="margin-left: 10px">{{
                            scope.row.dxReportTime
                          }}</span>
                        </div>
                        <div v-else>
                          <span
                            v-if="item.status == 0"
                            style="margin-right: 10px"
                            >成功</span
                          >
                          <span
                            v-if="item.status == 1"
                            style="margin-right: 10px"
                            >失败</span
                          >
                          <span
                            v-if="item.status == 2"
                            style="margin-right: 10px"
                            >等待审核</span
                          >
                          |
                          <span style="margin-left: 10px">{{
                            scope.row.dxReportTime
                          }}</span>
                        </div>
                        <!-- <span style="margin-right:10px">{{item.message?item.message:'等待审核'}}</span>|<span style="margin-left:10px">{{item.createTime}}</span> -->
                      </div>
                    </template>
                    <el-tag
                  :disable-transitions="true"
                      v-if="item.status == 0"
                      type="success"
                      closable
                      style="margin-top: 5px"
                      @close="handleCloseTemp(item)"
                    >
                      <span @click="channel(item, scope.row)">{{
                        item.channelId
                      }}</span
                      >|<span>{{ item.mmsId }}</span>
                    </el-tag>
                    <el-tag
                  :disable-transitions="true"
                      v-if="item.status == 1"
                      type="danger"
                      closable
                      style="margin-top: 5px"
                      @close="handleCloseTemp(item)"
                    >
                      <span @click="channel(item, scope.row)">{{
                        item.channelId
                      }}</span
                      >|<span>{{ item.mmsId }}</span>
                    </el-tag>
                    <el-tag
                  :disable-transitions="true"
                      v-if="item.status == 2"
                      type="warning"
                      closable
                      style="margin-top: 5px"
                      @close="handleCloseTemp(item)"
                    >
                      <span @click="channel(item, scope.row)">{{
                        item.channelId
                      }}</span
                      >|<span>{{ item.mmsId }}</span>
                    </el-tag>
                  </el-tooltip>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="阅信短链到期时间" title="阅信短链到期时间" width="170">
              <template #default="scope">
                <div>{{ scope.row.expireTime }}</div>
              </template>
            </vxe-column>
            <vxe-column field="备注" title="备注" width="100">
              <template #default="scope">
                <div>
                  <span v-if="scope.row.tempType == '1'">文本</span>
                  <span v-if="scope.row.tempType == '2'">图片</span>
                  <span v-if="scope.row.tempType == '3'">视频</span>
                  <span v-if="scope.row.tempType == '4'">图片和视频</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="280" fixed="right">
              <template #default="scope">
                <div style="display: flex; flex-wrap: wrap; flex-shrink: 0">
                  <div>
                    <div>
                      <el-button
                        link
                        style="margin-left: 8px; color: #4caf50"
                        @click="addChannel(scope.row)"
                        ><el-icon><Plus /></el-icon> 新增模板ID</el-button
                      >
                    </div>
                    <div>
                      <el-button
                        link
                        style="margin-left: 8px; color: #13b2d2"
                        @click="addReport('add', scope.row)"
                        ><el-icon><Plus /></el-icon> 新增通道</el-button
                      >
                    </div>
                  </div>
                  <div>
                    <div>
                      <el-button
                        v-if="scope.row.status == 2 || scope.row.status == 3"
                        link
                        style="margin-left: 8px; color: #e6b400"
                        @click="cancel(scope.row)"
                        ><el-icon><SuccessFilled /></el-icon>
                        审核推送</el-button
                      >
                    </div>
                    <div>
                      <el-button
                        v-if="
                          !scope.row.yxTemplateId && !scope.row.variableTemplate
                        "
                        link
                        style="margin-left: 8px; color: #409eff"
                        @click="bundleYx(scope.row)"
                        ><el-icon><Connection /></el-icon> 关联阅信</el-button
                      >
                      <el-button
                        v-if="
                          scope.row.yxTemplateId && !scope.row.variableTemplate
                        "
                        link
                        style="margin-left: 8px; color: #f56c6c"
                        @click="handelRelieve(scope.row)"
                        ><el-icon><Connection /></el-icon> 解绑阅信</el-button
                      >
                    </div>
                  </div>
                  <div>
                    <div>
                      <el-button
                        link
                        style="margin-left: 10px; color: #16a589"
                        @click="handleReportSignature(scope.row)"
                        ><el-icon><SuccessFilled /></el-icon
                        >&nbsp;签名报备</el-button
                      >
                    </div>
                    <div>
                      <el-button
                        link
                        style="margin-left: 10px; color: #409eff"
                        @click="handelCheck(scope.row)"
                        ><el-icon><View /></el-icon>&nbsp;实名明细</el-button
                      >
                    </div>
                  </div>
                </div>
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

          <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in viewData"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png' ||
                    item.media == 'jpeg'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                ></audio>
                <div style="white-space: pre-line" v-html="item.txt"></div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="通道操作"
      v-model="dialogTemplate"
      :close-on-click-modal="false"
      width="700px"
    >
      <el-tabs tab-position="left" v-model="activeName">
        <el-tab-pane label="修改模板ID" name="1">
          <div v-if="activeName == '1'">
            <div class="detailsList">
              <span class="detailsList-title">账号:</span>
              <span class="detailsList-content" style="width: 300px">{{
                TemplateObject.username
              }}</span>
            </div>
            <div class="detailsList">
              <span class="detailsList-title">标题:</span>
              <span class="detailsList-content" style="width: 300px">{{
                TemplateObject.title
              }}</span>
            </div>
            <div class="detailsList">
              <span class="detailsList-title">创建时间:</span>
              <span class="detailsList-content" style="width: 300px">{{
                TemplateObject.createTime
              }}</span>
            </div>
            <div class="detailsList">
              <span class="detailsList-title">状态:</span>
              <span
                v-if="objList.status == 0"
                class="detailsList-content"
                style="width: 300px; color: #67c23a"
                >通过</span
              >
              <span
                v-if="objList.status == 1"
                class="detailsList-content"
                style="width: 300px; color: #f56c6c"
                >不通过</span
              >
              <span
                v-if="objList.status == 2"
                class="detailsList-content"
                style="width: 300px; color: #e6a23c"
                >等待审核</span
              >
            </div>
            <div class="detailsList">
              <span class="detailsList-title">通道号:</span>
              <!-- <span class="detailsList-content" style="width: 300px;">{{objList.channelId}}</span> -->
              <el-input
                disabled
                style="width: 300px; margin-top: 10px"
                v-model="objList.channelId"
              ></el-input>
            </div>
            <div class="detailsList">
              <span class="detailsList-title">模板Id:</span>
              <el-input
                style="width: 300px; margin-top: 10px"
                v-model="objList.mmsId"
              ></el-input>
            </div>
            <div class="detailsList">
              <span class="detailsList-title">审核状态:</span>
              <el-radio-group v-model="status">
                <el-radio label="通过" value="0">通过</el-radio>
                <el-radio label="不通过" value="1">不通过</el-radio>
              </el-radio-group>
              <!-- <el-select v-model="status" placeholder="请选择">
                <el-option
                label="通过"
                value="0">
                </el-option>
                <el-option
                label="不通过"
                value="1">
                </el-option>
                <el-option
                disabled
                label="等待审核"
                value="2">
                </el-option>
            </el-select> -->

              <!-- <span v-if="TemplateObject.status==1" class="detailsList-content" style="color:#ffc800ed">未审核</span>
            <span v-if="TemplateObject.status==2" class="detailsList-content" style="color:rgb(22, 165, 137)">通过</span>
            <span v-if="TemplateObject.status==3" class="detailsList-content" style="color:rgb(245, 108, 108)">未通过</span> -->
              <!-- <span class="detailsList-content" style="width: 300px;">{{TemplateObject.name}}</span> -->
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="测试发送" name="2">
          <div v-if="activeName == '2'">
            <el-form
              :model="sendform"
              :rules="rules"
              ref="sendList"
              style="padding: 0 28px 0 20px"
            >
              <el-form-item label="手机号" label-width="80px" prop="mobile">
                <el-input
                  type="textarea"
                  v-model="sendform.mobile"
                  placeholder="请输入手机号,多个号码请用英文逗号隔开"
                  autocomplete="off"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="templaS()">取消</el-button>
          <el-button
            v-if="activeName == '1'"
            type="primary"
            @click="anewrReport()"
            >重新报备</el-button
          >
          <el-button
            v-if="!objList.support5g && activeName == '1'"
            type="success"
            @click="templaF()"
            >修改</el-button
          >
          <el-button
            v-if="activeName == '2'"
            type="primary"
            @click="sendMsg('sendList')"
            >发送</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      :title="reportType == 'report' ? '一键报备' : '新增通道报备'"
      v-model="reportVisible"
      :close-on-click-modal="false"
      width="700px"
    >
      <el-form
        :model="reportForm"
        :inline="reportType == 'report' ? false : true"
        status-icon
        ref="reportForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item v-if="reportType == 'report'" label="ID" prop="">
          <div>{{ selectId }}</div>
        </el-form-item>
        <el-form-item v-if="reportType == 'add'" label="账号" prop="username">
          <el-input
            class="input-w"
            disabled
            v-model="reportForm.username"
            placeholder="请输入账号"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="reportType == 'add'" label="标题" prop="title">
          <el-input
            class="input-w"
            disabled
            v-model="reportForm.title"
            placeholder="请输入标题"
          ></el-input>
        </el-form-item>
        <el-form-item label="移动" prop="ydChannels">
          <el-select
            class="input-w"
            v-model="reportForm.ydChannels"
            multiple
            placeholder="请选择通道"
          >
            <el-option
              v-for="item in ydoptions"
              :key="item.value"
              :label="item.channelName"
              :value="item.channelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联通" prop="ltChannels">
          <el-select
            class="input-w"
            v-model="reportForm.ltChannels"
            multiple
            placeholder="请选择通道"
          >
            <el-option
              v-for="item in ltoptions"
              :key="item.value"
              :label="item.channelName"
              :value="item.channelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电信" prop="dxChannels">
          <el-select
            class="input-w"
            v-model="reportForm.dxChannels"
            multiple
            placeholder="请选择通道"
          >
            <el-option
              v-for="item in dxoptions"
              :key="item.value"
              :label="item.channelName"
              :value="item.channelCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="reportVisible = false">取消</el-button>
          <el-button type="primary" @click="handleReport">确认</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="关联阅信模版"
      v-model="bundleVisible"
      :close-on-click-modal="false"
      width="550px"
    >
      <div>
        <el-form
          :model="bundleForm"
          :rules="rules"
          status-icon
          ref="bundleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              style="width: 355px"
              readonly
              v-model="bundleForm.username"
              placeholder="请输入用户名"
            ></el-input>
          </el-form-item>
          <el-form-item label="阅信模版" prop="templateId">
            <el-select
              ref="optionRef"
              style="width: 355px"
              v-model="bundleForm.templateId"
              clearable
              filterable
              remote
              :remote-method="remoteMethod"
              :loading="loadingcomp"
              placeholder="请选择阅信模版、支持模版ID、模版名称搜索"
              @change="bindChange"
            >
              <el-option
                v-for="(item, index) in templist"
                :key="index"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <!-- <el-input style="width: 355px;" v-model="bundleForm.templateId" placeholder="请输入用户名"></el-input> -->
          </el-form-item>
          <el-form-item label="阅信短链ID" prop="linkId">
            <el-select
              style="width: 355px"
              v-model="bundleForm.linkId"
              @change="handelChange"
              clearable
              filterable
              placeholder="请选择阅信短链ID"
            >
              <el-option
                v-for="(item, index) in linklist"
                :key="index"
                :label="item.id"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <!-- <el-input class="input-w" v-model="bundleForm.linkId" placeholder="请输入用户名"></el-input> -->
          </el-form-item>
        </el-form>
        <div style="width: 450px; margin-left: 10px">
          <el-descriptions
            v-if="showFlag"
            title="阅信短链信息"
            :column="2"
            border
          >
            <el-descriptions-item label="短链">
              <a
                style="color: #409eff"
                :href="'https://' + linkObj.resourceLink"
                target="_blank"
                rel="noopener noreferrer"
                >{{ linkObj.resourceLink }}</a
              >
            </el-descriptions-item>
            <el-descriptions-item label="解析数上限">{{
              linkObj.showTimes
            }}</el-descriptions-item>
            <el-descriptions-item label="有效期（天）">{{
              linkObj.expireDays
            }}</el-descriptions-item>
            <el-descriptions-item label="到期时间">
              <span v-if="linkObj.expireTime">
                {{ moment(linkObj.expireTime).format("YYYY-MM-DD HH:mm:ss") }}
              </span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="bundleVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBundle('bundleForm')"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="模板预览"
      v-model="tempVisible"
      width="1440px"
      :before-close="handleClose"
    >
      <iframe
        id="myframe"
        ref="newScreen"
        style="width: 1400px; height: 900px"
        :src="flowSrc"
      ></iframe>
    </el-dialog>
    <el-dialog
      title="新增模版ID"
      v-model="channelVisible"
      :close-on-click-modal="false"
      width="700px"
    >
      <el-form
        label-width="100px"
        :inline="true"
        ref="addVideoTempObj"
        :rules="rules"
        :model="addVideoTempObj"
        class="demo-form-inline"
      >
        <el-form-item label="账号" prop="username">
          <el-input
            disabled
            v-model="addVideoTempObj.username"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input
            disabled
            v-model="addVideoTempObj.title"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="通道号" prop="channelId">
          <el-input
            v-model="addVideoTempObj.channelId"
            placeholder="请输入通道号"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="报备视频ID" prop="mmsId">
          <el-input
            v-model="addVideoTempObj.mmsId"
            placeholder="请输入报备视频ID"
            class="input-w"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="视频模版ID" prop="videoId">
                        <el-input  v-model="addVideoTempObj.videoId" placeholder="请输入视频模版ID" class="input-w"></el-input>
                    </el-form-item> -->
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="channelVisible = false">取消</el-button>
          <el-button type="primary" @click="addTemp('addVideoTempObj')"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 实名明细 -->
    <SignatureView
      v-if="autonymVisible"
      ref="SignatureRef"
      :signatureData="signatureData"
      @handleClose="closeSignature"
    ></SignatureView>

    <!-- <el-dialog
      title="视频实名明细"
      :visible.sync="autonymVisible"
      width="700px"
    >
      <div class="table-box">
        <el-tabs v-model="operator" type="card" @tab-click="handleClick">
          <el-tab-pane name="1">
            <span slot="label"
              ><i class="iconfont icon-yidong" style="color: #409eff"></i>
              移动</span
            >
          </el-tab-pane>
          <el-tab-pane name="2">
            <span slot="label"
              ><i class="iconfont icon-liantong" style="color: #f56c6c"></i>
              联通</span
            >
          </el-tab-pane>
          <el-tab-pane label="电信" name="3">
            <span slot="label"
              ><i class="iconfont icon-dianxin" style="color: #0c36f2"></i>
              电信</span
            >
          </el-tab-pane>
        </el-tabs>
      </div>

      <el-form
        label-width="80px"
        :inline="true"
        :model="checkForm"
        class="demo-form-inline"
        ref="checkForm"
      >
        <el-form-item label="通道号" prop="channelCode">
          <el-input
            v-model="checkForm.channelCode"
            placeholder
            class="input-time"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain style @click="searchAutonym"
            >查询</el-button
          >
          <el-button
            type="primary"
            plain
            style
            @click="resetAutonym('checkForm')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="autonymData"
        style="width: 100%"
        border
        max-height="400px"
      >
        <el-table-column label="产品名称" prop="">
          <template #default="scope">
            <span>视频短信 </span>
          </template>
        </el-table-column>
        <el-table-column label="通道号" prop="channelCode"></el-table-column>
        <el-table-column label="子端口">
          <template #default="scope">
            <span> {{ scope.row.spCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="扩展号">
          <template #default="scope">
            <span> {{ scope.row.ext }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用状态">
          <template #default="scope">
            <el-tag
                  :disable-transitions="true" type="success" v-if="scope.row.status == 1">可用</el-tag>
            <el-tag
                  :disable-transitions="true" type="danger" v-else-if="scope.row.status == 2"
              >未生成</el-tag
            >
            <el-tag
                  :disable-transitions="true" type="warning" v-else-if="scope.row.status == 0"
              >实名中</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="autonymVisible = false">取 消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>

<script>
import moment from "moment";
import TableTem from "@/components/publicComponents/TableTem.vue";
import UserLists from "@/components/publicComponents/userList.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import SignatureView from "@/components/publicComponents/signatureDetail.vue";
export default {
  components: {
    TableTem,
    UserLists,
    Tooltip,
    SignatureView,
  },
  name: "MMStemplate",
  data() {
    return {
      customConfig: {
        storage: true
      },
      isFirstEnter: false,
      userFlag: false,
      nameover: "",
      activeName: "1",
      dialogVisible: false,
      reportVisible: false,
      channleFlag: true,
      dialogTemplate: false,
      bundleVisible: false,
      channelVisible: false,
      chindex: "",
      value: "",
      TemplateObject: {},
      viewData: [], // 查看内容
      title: "",
      selectId: "",
      status: "",
      objList: {
        channelId: "",
        mmsId: "",
        id: "",
        status: "",
        support5g: false,
      },
      // 搜索数据
      formInline: {
        clientName: "",
        title: "",
        signature: "",
        videoId: "",
        mmsid: "",
        channelId: "",
        tempType: "",
        status: "",
        channelStatus: "",
        beginTime: moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        time: [
          moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss"),
          moment().format("YYYY-MM-DD HH:mm:ss"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        clientName: "",
        title: "",
        signature: "",
        videoId: "",
        mmsid: "",
        channelId: "",
        status: "",
        tempType: "",
        channelStatus: "",
        beginTime: moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        time: [
          moment().subtract(7, "days").format("YYYY-MM-DD HH:mm:ss"),
          moment().format("YYYY-MM-DD HH:mm:ss"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
      ydoptions: [],
      ltoptions: [],
      dxoptions: [],
      reportForm: {
        username: "",
        ids: "",
        title: "",
        ydChannels: [],
        ltChannels: [],
        dxChannels: [],
      },
      templist: [],
      linklist: [],
      loadingcomp: false,
      bundleForm: {
        username: "",
        videoId: "",
        templateId: "",
        variableTemplate: "",
        linkId: "",
      },
      sendform: {
        mobile: "",
      },
      rules: {
        templateId: [
          { required: true, message: "请选择阅信模版", trigger: "change" },
        ],
        linkId: [
          { required: true, message: "请选择阅信短链ID", trigger: "change" },
        ],
        channelId: [
          { required: true, message: "通道号不能为空", trigger: "change" },
        ],
        mmsId: [
          { required: true, message: "报备视频ID不能为空", trigger: "change" },
        ],
        videoId: [
          { required: true, message: "视频模版ID不能为空", trigger: "change" },
        ],
        mobile: [
          { required: true, message: "手机号不能为空", trigger: "change" },
        ],
      },
      tempVisible: false,
      detail: {},
      flowSrc: null,
      timer: null,
      linkObj: {},
      showFlag: false,
      addVideoTempObj: {
        username: "",
        title: "",
        channelId: "",
        mmsId: "",
        videoId: "",
      },
      reportType: "",
      autonymVisible: false,
      // signatoryId: "",
      // checkForm: {
      //   channelCode: "",
      //   userId: "",
      // },
      // autonymData: [],
      // operator: "1",
      signatureData: {
        signatoryId: "",
        userId: "",
        userName: "",
        productId: "3",
      },
    };
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "consumervideo/page",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tablecurrent.total = res.data.total;
        }
      );
    },
    // 预览
    View(val) {
      this.viewData = val.contents;
      this.title = val.title;
      this.dialogVisible = true;
    },
    // 查询
    Query() {
      if (this.tableDataObj.loading2) return; // 防止重复请求
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      (this.formInline.time = []), (this.formInline.beginTime = "");
      this.formInline.endTime = "";
      (this.formInline.time1 = []), (this.formInline.startTime = "");
      this.formInline.stopTime = "";
      this.formInline.videoId = "";
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    userList(row, index) {
      this.userFlag = true;
      this.nameover = index;
    },
    // 分页
    handleSizeChange(size) {
      // console.log(size,'size');
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      // console.log(currentPage,'pahe');
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
    channel(item, row) {
      console.log(row);
      console.log(item);
      this.dialogTemplate = true;
      this.TemplateObject = row;
      this.status = item.status + "";
      this.objList.channelId = item.channelId;
      // this.value = row.status+''
      this.objList.id = item.id;
      this.objList.mmsId = item.mmsId;
      this.objList.status = item.status + "";
      this.objList.support5g = item.support5g;
      // this.channleFlag = false
      // this.chengFlage= true
      // this.chindex = index
    },
    // changeC(val){
    //     // console.log(val);
    //     this.objList.status = val
    // },
    templaS() {
      this.dialogTemplate = false;
    },
    templaF() {
      if (this.status == 2) {
        this.$message({
          message: "请勾选审核状态！",
          type: "warning",
        });
      } else {
        this.objList.status = this.status;
        this.$confirms.confirmation(
          "put",
          "视频短信报备视频id",
          window.path.omcs + "consumervideo/userVideoChannel",
          this.objList,
          (res) => {
            this.InquireList();
            this.dialogTemplate = false;
          }
        );
      }
    },
    anewrReport() {
      this.$confirms.confirmation(
        "post",
        "视频模版重新报备",
        window.path.omcs + "consumervideo/channelReport",
        { id: this.objList.id },
        (res) => {
          this.InquireList();
          this.dialogTemplate = false;
        }
      );
    },
    sendMsg(form) {
      this.$refs[form].validate((valid) => {
        if (valid) {
          let data = {
            id: this.objList.id,
            mobile: this.sendform.mobile,
          };
          this.$confirms.confirmation(
            "post",
            "视频短信测试发送",
            window.path.omcs + "consumervideo/testSend",
            data,
            (res) => {
              if (res.code == 200) {
                // this.$message.success("发送成功！");
                this.dialogTemplate = false;
              }
              //   this.InquireList();
              //   this.dialogTemplate = false;
            }
          );
        } else {
          return false;
        }
      });
    },
    // channele(item,index){
    //     this.channleFlag = true
    //     this.chengFlage= false
    //     // this.chindex = index
    // },
    // 创建时间
    getTimeOperating(val) {
      console.log(val);
      if (val) {
        this.formInline.beginTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 编辑模板
    edit(val) {
      this.$router.push({
        path: "/videoTemplateCreate",
        query: { id: val.id },
      });
    },
    // 删除模板
    deleteTemplate(val) {
      this.$confirms.confirmation(
        "delete",
        "确定删除当前模板？",
        window.path.cpus + "consumermmstemplate/" + val.id,
        {},
        (res) => {
          this.InquireList();
        }
      );
    },
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].videoId);
      }
      this.selectId = selectId.join();
      // console.log(this.selectId,'this.selectId');
    },
    cancel(val) {
      // console.log(val,'val');
      this.$confirms.confirmation(
        "post",
        "视频模板审核结果推送",
        window.path.omcs + "consumervideo/auditResult/push",
        { ids: val.videoId },
        (res) => {
          this.InquireList();
        }
      );
    },
    searchAccount(val, username) {
      try {
        window.api.post(
          window.path.yuexin + "/operator/template/list",
          {
            username: username || this.bundleForm.username,
            name: val,
          },
          (res) => {
            if (res.code == 200) {
              this.templist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    remoteMethod(query) {
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.compNamelist = [];
        this.searchAccount();
      }
    },
    bindChange(val) {
      if (val) {
        this.bundleForm.templateId = val;
        // if (!this.bundleForm.variableTemplate) {

        // }
        window.api.get(
          window.path.yuexin + "/operator/link/list?templateId=" + val,
          {},
          (res) => {
            if (res.code == 200) {
              this.linklist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } else {
        this.searchAccount();
        this.bundleForm.templateId = "";
      }
      this.bundleForm.linkId = "";
      this.linkObj = {};
    },
    handelChange(val) {
      if (val) {
        this.showFlag = true;
        let obj = this.linklist.find((item) => item.id == val);
        this.linkObj = obj;
      } else {
        this.showFlag = false;
        this.linkObj = {};
      }
    },
    addReport(type, row) {
      if (type == "report") {
        this.reportType = type;
        this.reportVisible = true;
      } else {
        this.reportType = type;
        this.reportForm.username = row.username;
        this.reportForm.ids = row.videoId;
        this.reportForm.title = row.title;
        this.reportVisible = true;
      }
    },
    handleCloseTemp(item) {
      this.$confirms.confirmation(
        "post",
        "删除此模版？",
        window.path.omcs + "consumervideo/channelDel",
        { id: item.id },
        (res) => {
          this.InquireList();
        }
      );
    },
    addChannel(row) {
      this.addVideoTempObj.username = row.username;
      this.addVideoTempObj.title = row.title;
      this.addVideoTempObj.videoId = row.videoId;
      this.channelVisible = true;
    },
    addTemp(from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          try {
            let data = {
              channelId: this.addVideoTempObj.channelId,
              mmsId: this.addVideoTempObj.mmsId,
              videoId: this.addVideoTempObj.videoId,
            };
            window.api.post(
              window.path.omcs + "consumervideo/channelSave",
              data,
              (res) => {
                if (res.code == 200) {
                  this.$message.success("创建成功！");
                  this.InquireList();
                  this.channelVisible = false;
                } else {
                  this.$message.error(res.msg);
                }
              }
            );
          } catch (error) {
            console.log(error, "error");
          }
        } else {
          return false;
        }
      });
    },
    bundleYx(row) {
      this.bundleForm.username = row.username;
      this.bundleForm.videoId = row.videoId;
      this.bundleForm.templateId = row.templateId;
      this.bundleForm.linkId = row.linkId;
      // this.bundleForm.variableTemplate = row.variableTemplate
      this.searchAccount(row.templateId, row.username);
      this.bundleVisible = true;
    },
    handelRelieve(row) {
      this.$confirms.confirmation(
        "delete",
        "是否确认解绑阅信模版？",
        window.path.omcs + "consumerVideo/yueXin/template",
        { videoRefId: row.videoRefId },
        (res) => {
          this.InquireList();
        }
      );
    },
    handleBundle(from) {
      this.$refs[from].validate((valid) => {
        if (valid) {
          let data = {
            username: this.bundleForm.username,
            videoId: this.bundleForm.videoId,
            templateId: this.bundleForm.templateId,
            linkId: this.bundleForm.linkId,
            expireTime: this.linkObj.expireTime,
          };
          window.api.post(
            window.path.omcs + "consumerVideo/yueXin/template",
            data,
            (res) => {
              if (res.code == 200) {
                this.$message.success("关联阅信成功！");
                this.InquireList();
                this.bundleVisible = false;
              } else {
                this.$message.error(res.msg);
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    cancels() {
      this.$confirms.confirmation(
        "post",
        "视频模板审核结果推送",
        window.path.omcs + "consumervideo/auditResult/push",
        { ids: this.selectId },
        (res) => {
          this.InquireList();
        }
      );
    },
    //获取通道号
    getOptions() {
      window.api.get(
        window.path.omcs +
          "v3/operatingchannelgroup/channelList?operatorType=1" +
          "&productId=3",
        {},
        (res) => {
          this.ydoptions = res.data;
        }
      );
      window.api.get(
        window.path.omcs +
          "v3/operatingchannelgroup/channelList?operatorType=2" +
          "&productId=3",
        {},
        (res) => {
          this.ltoptions = res.data;
        }
      );
      window.api.get(
        window.path.omcs +
          "v3/operatingchannelgroup/channelList?operatorType=3" +
          "&productId=3",
        {},
        (res) => {
          this.dxoptions = res.data;
        }
      );
    },
    //一键报备
    handleReport() {
      let yd = this.reportForm.ydChannels.join(",");
      let lt = this.reportForm.ltChannels.join(",");
      let dx = this.reportForm.dxChannels.join(",");
      try {
        if (this.reportType == "report") {
          this.$confirms.confirmation(
            "post",
            "一键报备！",
            window.path.omcs + "consumervideo/batchReport/",
            {
              ids: this.selectId,
              ydChannels: yd,
              ltChannels: lt,
              dxChannels: dx,
            },
            (res) => {
              this.InquireList();
              this.reportVisible = false;
            }
          );
        } else {
          let data = {
            dxChannels: dx,
            ltChannels: lt,
            ydChannels: yd,
            ids: this.reportForm.ids,
          };
          window.api.post(
            window.path.omcs + "consumervideo/batchReport/new",
            data,
            (res) => {
              if (res.code == 200) {
                this.$message.success("新增报备成功！");
                this.reportVisible = false;
                this.InquireList();
              } else {
                this.$message.error(res.msg);
              }
            }
          );
        }
      } catch (error) {
        console.log(error, "error");
      }
    },
    handView(row) {
      this.tempVisible = true;
      let token = window.btoa(window.common.getCookie("ZTADMIN_TOKEN"));
      window.api.get(
        window.path.yuexin + "/operator/template/" + row.yxTemplateId,
        {},
        (res) => {
          if (res.code == 200) {
            this.detail = res.data;
            this.flowSrc =
              window.path.yuexinip +
              "/#/preview?token=" +
              token +
              "&templateID=" +
              res.data.id +
              "&kindId=" +
              res.data.kindId;
            this.$nextTick(() => {
              let mapFrame = document.getElementById("myframe");
              if (mapFrame.attachEvent) {
                //兼容浏览器判断
                this.timer = setTimeout(
                  mapFrame.attachEvent("onload", function () {
                    let iframeWin = mapFrame.contentWindow;
                    iframeWin.postMessage(res.data, "*");
                    //res.data传递的参数   *写成子页面的域名或者是ip
                  }),
                  1000
                );
              } else {
                this.timer = setTimeout(
                  (mapFrame.onload = function () {
                    let iframeWin = mapFrame.contentWindow;
                    iframeWin.postMessage(res.data, "*");
                    // debugger
                  }),
                  1000
                );
              }
            });
          }
        }
      );
    },
    handleClose() {
      this.tempVisible = false;
    },
    handleReportSignature(row) {
      this.$confirms.confirmation(
        "post",
        "签名报备!",
        window.path.omcs + "consumervideo/auditSignature",
        {
          ids: row.videoId,
        },
        (res) => {
          if (res.code == 200) {
            this.InquireList();
          }
        }
      );
    },
    handelCheck(row) {
      this.signatureData.signatoryId = row.signature;
      this.signatureData.userId = row.userId;
      this.signatureData.userName = row.username;
      this.autonymVisible = true;
    },
    closeSignature(data) {
      this.autonymVisible = data;
    },
    // searchAutonym() {
    //   this.handleClick();
    // },
    // resetAutonym() {
    //   this.checkForm.channelCode = "";
    //   this.handleClick();
    // },
    // handleClick() {
    //   window.api.post(
    //     window.path.omcs + "consumerSignature/queryRealNameInfo",
    //     {
    //       userId: this.checkForm.userId,
    //       signature: this.signatoryId,
    //       operator: this.operator,
    //       channelCode: this.checkForm.channelCode,
    //       productId: "3",
    //     },
    //     (res) => {
    //       if (res.code == 200) {
    //         this.autonymVisible = true;
    //         this.autonymData = res.data;
    //       } else {
    //         this.autonymData = [];
    //         this.$message({
    //           message: res.msg,
    //           type: "warning",
    //         });
    //       }
    //     }
    //   );
    // },
  },
  mounted() {
    // 注册回车事件
    var _self = this;
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode;
      } else {
        var key = window.event.keyCode;
      }
      if (key == 13) {
        _self.Query();
      }
    };
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList();
        this.getOptions();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.InquireList();
      this.getOptions();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
    reportVisible(val) {
      if (!val) {
        this.$refs.reportForm.resetFields();
        // this.selectId = ''
      }
    },
    bundleVisible(val) {
      if (!val) {
        this.$refs.bundleForm.resetFields();
        this.linkObj = {};
        this.showFlag = false;
      }
    },
    tempVisible(val) {
      if (!val) {
        this.detail = {};
        this.flowSrc = null; // 清空iframe
        clearTimeout(this.timer); // 清空定时器
      }
    },
    channelVisible(val) {
      if (!val) {
        this.$refs.addVideoTempObj.resetFields();
      }
    },
    dialogTemplate(val) {
      if (!val) {
        if (this.activeName == "2") {
          this.$refs.sendList.resetFields();
        }
        this.activeName = "1";
        this.sendform.mobile = "";
        
      }
    },
    // autonymVisible(val) {
    //   if (!val) {
    //     this.autonymData = [];
    //     this.operator = "1";
    //     this.checkForm.userId = "";
    //     this.checkForm.channelCode = "";
    //   }
    // },
  },
};
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
.YYSstatus {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: relative;
}
.channelsStyle span {
  margin: 0 5px;
  cursor: pointer;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.detailsList {
  line-height: 42px;
  padding-left: 30px;
  font-size: 12px;
  position: relative;
}
.video-id {
  display: flex;
  align-items: center;
}
.watermark {
  width: 20px;
  height: 20px;
  color: #fff;
  font-size: 12px;
  opacity: 0.7;
  margin-left: 4px;
  border: 1px solid #7957d5;
  background: #7957d5;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
}
.yxrmark {
  width: 20px;
  height: 20px;
  color: #fff;
  font-size: 12px;
  opacity: 0.7;
  margin-left: 4px;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  cursor: pointer;
}
.err {
  border: 1px solid #f56c6c;
  background: #f56c6c;
}
.yx {
  border: 1px solid #67c23a;
  background: #67c23a;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
