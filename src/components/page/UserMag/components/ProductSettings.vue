<template>
  <div>
    <p style="margin-top: 30px; font-weight: bold">产品设置</p>
    <div class="sensitive-fun" style="padding-top: 50px">
      <div>
        <div>
          <img
            src="@/assets/images/9bbecc31-cacb-4647-b980-99ee40db3277.png"
            alt=""
          />
        </div>
        <div style="font-size: 16px">
          <span>短信产品</span>
        </div>
        <div style="color: #ccc">
          <p>平台群发，接口调用</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('SMSconfiguration')">
            <span>短信配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('1')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <!-- <div>
        <div>
          <img
            src="@/assets/images/e9b36dc8-28ca-4add-a6a2-a79784a711b6.png"
            alt=""
          />
        </div>
        <div style="font-size: 16px">
          <span>彩信产品</span>
        </div>
        <div style="color: #ccc">
          <p>多媒体信息，企业营销</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('MMSconfiguration')">
            <span>彩信配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('2')">
            <span>账户设置</span>
          </div>
        </div>
      </div> -->
      <div>
        <div>
          <img
            src="@/assets/images/de5b2200-9222-4b25-9075-af4143af9c7f.png"
            alt=""
          />
        </div>
        <div style="font-size: 16px">
          <span>视频短信</span>
        </div>
        <div style="color: #ccc">
          <p>多媒体信息，企业营销</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('RMSconfiguration')">
            <span>视频短信配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('3')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <div>
        <div>
          <img src="@/assets/images/gjdx.png" alt="" />
        </div>
        <div style="font-size: 16px">
          <span>国际短信</span>
        </div>
        <div style="color: #ccc">
          <p>多媒体信息，企业营销</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('ImsInternational')">
            <span>国际短信配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('4')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <div>
        <div>
          <img src="@/assets/images/SYDL.png" alt="" />
        </div>
        <div style="font-size: 16px">
          <span>闪验产品</span>
        </div>
        <div style="color: #ccc">
          <p>多媒体信息，企业营销</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('FlashTest')">
            <span>闪验配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('5')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <div>
        <div>
          <img src="@/assets/images/YYYZM.png" alt="" />
        </div>
        <div style="font-size: 16px">
          <span>语音验证码</span>
        </div>
        <div style="color: #ccc">
          <p>多媒体信息，企业营销</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('voiceCode')">
            <span>语音验证码配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('6')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <div>
        <div>
          <img src="@/assets/images/YYTZ.png" alt="" />
        </div>
        <div style="font-size: 16px">
          <span>语音通知</span>
        </div>
        <div style="color: #ccc">
          <p>多媒体信息，企业营销</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('voiceNotice')">
            <span>语音通知配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('7')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <div>
        <div>
          <img
            src="@/assets/images/9bbecc31-cacb-4647-b980-99ee40db3277.png"
            alt=""
          />
        </div>
        <div style="font-size: 16px">
          <span>5G短信</span>
        </div>
        <div style="color: #ccc">
          <p>开启5G短信多样化</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('SMS5Gconfiguration')">
            <span>5G配置</span>
          </div>
        </div>
        <div>
          <div class="div_font" @click="DisableEnable('9')">
            <span>账户设置</span>
          </div>
        </div>
      </div>
      <div>
        <div>
          <img
            src="@/assets/images/9bbecc31-cacb-4647-b980-99ee40db3277.png"
            alt=""
          />
        </div>
        <div style="font-size: 16px">
          <span>阅信</span>
        </div>
        <div style="color: #ccc">
          <p>开启阅信多样化</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('yuexinconfiguration')">
            <span>阅信配置</span>
          </div>
        </div>
        <div>
          <div class="div_fonts">
            <span>&nbsp;</span>
          </div>
        </div>
      </div>
      <div class="dlset">
        <div>
          <!-- <i class="iconfont icon-lianjie" style="font-size: 102px;"></i> -->
          <img src="@/assets/images/short2.png" alt="" />
        </div>
        <div style="font-size: 16px">
          <span>短链设置</span>
        </div>
        <div style="color: #ccc">
          <p>短链接可节省字符数空间</p>
        </div>
        <div>
          <div class="div_font" @click="Jump('ShortchainSetting')">
            <span>短链用户配置</span>
          </div>
        </div>
        <div>
          <div class="div_fonts">
            <span>&nbsp;</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductSettings',
  data() {
    return {}
  },
  methods: {
    // 产品
    Jump(val) {
      console.log(val,'val');
      
      // this.$router.push({ path:val,query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({ path: val, query: { id: this.$route.query.id } })
    },
    DisableEnable(val) {
      sessionStorage.setItem('productSetUp', val)
      // this.$router.push({ path:'productSetUp',query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({
        path: 'productSetUp',
        query: { id: this.$route.query.id },
      })
    },
    // shorchin(){
    //      this.$router.push('ShortchainSetting')
    // }
  },
  created() {},
  watch: {},
}
</script>

<style scoped>
.sensitive-fun > div {
  display: inline-block;
  width: 13%;
  height: 350px;
  text-align: center;
  padding: 20px;
  margin: 20px;
  box-shadow: 0px 0px 10px 2px #ccc;
  cursor: pointer;
}
.div_font {
  height: 45px;
  font-size: 18px;
  line-height: 45px;
  background: #16a589;
  color: #fff;
  font-weight: 500;
  border-radius: 15px;
}
.div_fonts {
  height: 45px;
  font-size: 18px;
  line-height: 45px;
  color: #fff;
  font-weight: 500;
  border-radius: 15px;
}
.sensitive-fun > div > div {
  margin-top: 20px;
}
.div_font:hover {
  background: #16a589d6;
}
img {
  width: 100px;
  height: 100px;
}
.selectSensitive {
  padding: 10px 10px;
}
.bas-block {
  margin-bottom: 10px;
  padding-top: 36px;
  position: relative;
  height: 220px;
}
.basic-cfg-title {
  font-weight: bold;
  padding-bottom: 14px;
}
.basic-cfg-tit {
  width: 92px;
  display: inline-block;
}
.basic-cfg-cot {
  margin-bottom: 24px;
}
.basic-cfg-tips {
  padding-left: 96px;
  padding-top: 2px;
  font-size: 12px;
}
.basic-help {
  margin: 30px 0 20px 0;
  padding-bottom: 10px;
  width: 500px;
  border-bottom: 1px solid #e6e6e6;
}
.tips-box {
  position: absolute;
  background: #fff;
  padding: 20px;
  border: 1px solid #e6e6e6;
}
.fade-enter-active {
  animation: show-in 1s;
  transition: all 1s;
}
.fade-leave-active {
  animation: show-out 1s;
  transition: all 1s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
@keyframes show-in {
  0% {
    transform: rotateX(0deg);
  }
  100% {
    transform: rotateX(360deg);
  }
}
@keyframes show-out {
  0% {
    transform: rotateX(360deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}
.bac-color-red {
  color: red;
}
</style>
