<template>
  <div class="container_left">
    <!-- <div class="crumbs">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item
            ><i class="el-icon-lx-emoji"></i> 人工通道组规则</el-breadcrumb-item
          >
        </el-breadcrumb>
      </div> -->
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="formInline.ruleName" placeholder></el-input>
          </el-form-item>
          <el-form-item label="产品类型" prop="productId">
            <el-select
              v-model="formInline.productId"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="item in productList"
                :key="item.productId"
                :label="item.name"
                :value="item.productId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运营商" prop="operator">
            <el-select
              v-model="formInline.operator"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value=""> </el-option>
              <el-option label="移动" value="1"></el-option>
              <el-option label="联通" value="2"></el-option>
              <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">创建规则</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="channelGroupRule"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px"
          :data="tableDataObj.tableData"
        >
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->

          <vxe-column field="规则名称" title="规则名称">
            <template #default="scope">
              <div>{{ scope.row.ruleName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="产品名称" title="产品名称">
            <template #default="scope">
              <div v-for="item in productList" :key="item.productId">
                <div v-if="item.productId == scope.row.productId">{{
                  item.name
                }}</div>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="运营商" title="运营商">
            <template #default="scope">
              <div
                v-if="scope.row.operator == 1"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yidong"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div
                v-else-if="scope.row.operator == 2"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-liantong"
                  style="font-size: 16px; color: #f56c6c"
                ></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div
                v-else-if="scope.row.operator == 3"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-dianxin"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">电信</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="主通道" title="主通道">
            <template #default="scope">
              <div v-if="scope.row.mainChannels">
                <span
                  style="
                    color: rgb(22, 165, 137);
                    cursor: pointer;
                    margin-left: 8px;
                  "
                  v-for="item in scope.row.mainChannels.split(',')"
                  @click="detailsChannel(item)"
                >
                  {{ item }}
                </span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="备通道" title="备通道">
            <template #default="scope">
              <div v-if="scope.row.backupChannels">
                <span
                  style="
                    color: rgb(22, 165, 137);
                    cursor: pointer;
                    margin-left: 8px;
                  "
                  v-for="item in scope.row.backupChannels.split(',')"
                  @click="detailsChannel(item)"
                >
                  {{ item }}
                </span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="180">
            <template #default="scope">
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format("YYYY-MM-DD hh:mm:ss")
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button
                link
                type="primary"
                style="margin-left: 10px"
                v-if="scope.row.editAble"
                @click="detailsRow(scope.row)"
                >编辑</el-button
              >
              <el-button
                link
                style="margin-left: 10px; color: rgb(22, 165, 137)"
                @click="copyState(scope.row)"
                >复制</el-button
              >
              <el-button
                link
                v-if="scope.row.editAble"
                type="danger"
                style="margin-left: 10px; color: red"
                @click="delState(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <div style="float: right; margin-top: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增账单 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        width="1100px"
        :close-on-click-modal="false"
      >
        <div class="dialog-form">
          <div style="width: 40%">
            <el-form
              label-width="110px"
              :model="formop"
              :rules="rules"
              ref="formopRef"
            >
              <el-form-item label="规则名称" prop="ruleName">
                <el-input
                  class="input-w"
                  v-model="formop.ruleName"
                  placeholder=""
                  autocomplete="off"
                ></el-input>
              </el-form-item>
              <el-form-item label="产品类型" prop="productId">
                <el-select
                  v-model="formop.productId"
                  clearable
                  placeholder="请选择"
                  class="input-w"
                  @change="handleChange"
                >
                  <el-option
                    v-for="item in productList"
                    :key="item.productId"
                    :label="item.name"
                    :value="item.productId"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="运营商" prop="operator">
                <el-radio-group
                  v-model="formop.operator"
                  @change="handleChangeOperator"
                >
                  <el-radio label="移动" value="1"></el-radio>
                  <el-radio label="联通" value="2"></el-radio>
                  <el-radio label="电信" value="3"></el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <div class="dialog-form-d"></div>
            <div class="dialog-form-tit">添加通道</div>
            <el-form
              ref="formChannel"
              label-width="110px"
              :model="channelform"
              :rules="rules"
            >
              <el-form-item label="通道类型" prop="channelType">
                <el-radio-group
                  v-remove-hidden
                  v-model="channelform.channelType"
                  @change="changeChannelType"
                >
                  <el-radio label="全网" value="1"></el-radio>
                  <el-radio label="省网" value="2"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否主备通道" prop="backup">
                <el-radio-group v-remove-hidden v-model="channelform.backup">
                  <el-radio label="主用通道" value="0"></el-radio>
                  <el-radio label="备用通道" value="1"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="添加通道" prop="channelCode">
                <el-select
                  v-model="channelform.channelCode"
                  placeholder="请选择"
                  filterable
                  class="input-w"
                >
                  <el-option
                    v-for="(item, index) in labelpassName"
                    :label="item.channelName"
                    :value="item.channelCode"
                    :disabled="item.disabled"
                    :key="index"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="权重" prop="weight">
                <el-input-number
                  class="input-w"
                  v-model="channelform.weight"
                  :min="1"
                  :max="100"
                  label="请输入权重"
                ></el-input-number>
              </el-form-item>
              <el-form-item>
                <el-button
                  style="width: 80px"
                  type="primary"
                  @click="addChannel('formChannel')"
                  >添加</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <div class="dialog-form-btn"></div>
          <div style="flex: 1">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
              <el-tab-pane label="主用通道" name="0"> </el-tab-pane>
              <el-tab-pane label="备用通道" name="1"> </el-tab-pane>
            </el-tabs>

            <el-table :data="tableData" border stripe>
              <el-table-column prop="channelCode" label="通道号">
              </el-table-column>
              <el-table-column label="通道类型">
                <template #default="scope">
                  <span v-if="scope.row.channelType == 1">全网</span>
                  <span v-if="scope.row.channelType == 2">省网</span>
                </template>
              </el-table-column>
              <el-table-column label="是否备用通道">
                <template #default="scope">
                  <span v-if="scope.row.backup == 1">备用通道</span>
                  <span v-if="scope.row.backup == 0">主用通道</span>
                </template>
              </el-table-column>
              <el-table-column label="权重">
                <template #default="scope">
                  <span>{{ scope.row.weight }}</span>
                </template>
              </el-table-column>
              <el-table-column label="占比率">
                <template #default="scope">
                  <span>{{ scope.row.proportion }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button
                    link
                    style="color: #f56c6c"
                    @click="deleteChannel(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- <div>
            <el-button type="primary" @click="addChannel">添加通道</el-button>
          </div> -->
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formopRef')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
    </div>
  </div>
</template>
  <script>
import Tooltip from "@/components/publicComponents/tooltip.vue";
import moment from "moment";
import common from "../../../../../assets/js/common";
import productData from "../../../../../utils/industrysCode";
import ChannelView from "../../../../publicComponents/ChannelView.vue";
export default {
  name: "channelGroupRule",
  components: {
    ChannelView,
    Tooltip,
  },
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      titleMap: {
        add: "创建规则",
        edit: "编辑规则",
      },
      activeName: "0",
      dialogStatus: "", //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        ruleName: "",
        operator: "",
        productId: "",
        currentPage: 1,
        pageSize: 10,
      },
      ChannelData: "",
      tabelAlllist: {
        //存储查询数据
        ruleName: "",
        operator: "",
        productId: "",
        currentPage: 1,
        pageSize: 10,
      },
      tag: "",
      productoldVal: "",
      operatoroldVal: "",
      options: [],
      productList: [],
      labelpassName: [], //通道列表
      //添加列表
      formop: {
        ruleName: "", //用户名
        operator: "1", //运营商
        productId: "1", //产品ID
        channelCode: "",
        backup: "",
        weight: 1,
      },
      channelform: {
        channelType: "1",
        channelCode: "",
        backup: "0",
        weight: 1,
      }, //通道号
      items: [],
      tableData: [],
      id: "", //列表行id
      rules: {
        ruleName: [
          {
            required: true,
            message: "规则名称不能为空",
            trigger: "change",
          },
        ],
        productId: [
          {
            required: true,
            message: "请选择产品",
            trigger: "change",
          },
        ],
        operator: [
          { required: true, message: "请选择运营商", trigger: "change" },
        ],
        channelCode: [
          { required: true, message: "请选择通道", trigger: "change" },
        ],
        backup: [
          { required: true, message: "请选择通道类型", trigger: "change" },
        ],
        weight: [{ required: true, message: "请输入权重", trigger: "change" }],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    };
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingchannelgroup/rule/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    Reload(val) {
      this.$refs.formInline.resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    // handedTime(e) {
    //   this.formop.startTime = moment(e).format("YYYY-MM-DD hh:mm:ss");
    // },
    handleChange(e) {
      this.tag = 2;
      this.getChannelList(
        this.formop.operator,
        e,
        this.channelform.channelType
      );
      if (this.items.length > 0) {
        this.handelclear();
      }
    },
    handleChangeOperator(e) {
      this.tag = 1;
      this.getChannelList(
        e,
        this.formop.productId,
        this.channelform.channelType
      );
      if (this.items.length > 0) {
        this.handelclear();
      }
    },
    detailsChannel(item) {
      this.ChannelData = item;
      this.$refs.ChannelRef.ChannelClick();
    },
    handelclear(val) {
      this.$confirm("此操作将清除已选择的通道, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          localStorage.setItem("formop", JSON.stringify(this.formop));
          this.items = [];
          this.setArr();
        })
        .catch(() => {
          let obj = JSON.parse(localStorage.getItem("formop"));
          if (this.tag == 1) {
            this.formop.operator = obj.operator;
          } else if (this.tag == 2) {
            this.formop.productId = obj.productId;
          }
          this.getChannelList(
            obj.operator,
            obj.productId,
            this.channelform.channelType
          );
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    changeChannelType(e) {
      this.getChannelList(this.formop.operator, this.formop.productId, e);
    },
    getChannelList(operator, productId, channelType) {
      let api = `v3/operatingchannelgroup/channelList?operatorType=${operator}&productId=${productId}&channelType=${channelType}`;
      window.api.get(window.path.omcs + api, {}, (res) => {
        if (res.code == 200) {
          this.labelpassName = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    funProportionSum(arr) {
      try {
        const total = arr.reduce((sum, value) => {
          if (value.channelType == 1 && typeof value.weight === "number") {
            return sum + value.weight; // 直接返回累加值
          }
          return sum; // 默认返回当前累加值
        }, 0);
        // 检查 total 是否为数字
        if (isNaN(total)) {
          throw new Error("计算总和时出错，结果不是数字");
        }
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].channelType == 1) {
            arr[i].proportion = ((arr[i].weight / total) * 100).toFixed(2);
          }
        }
        return arr;
      } catch (error) {
        console.error("发生错误：", error.message);
      }
    },
    addChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = JSON.stringify(this.channelform);
          let data = JSON.parse(obj);
          for (let i = 0; i < this.items.length; i++) {
            if (
              this.items[i].backup == data.backup &&
              this.items[i].channelCode == data.channelCode
            ) {
              this.$message.warning("通道号重复，请重新选择");
              return false;
            }
          }
          const newItem = {
            id: new Date().getTime(), 
            ...data,
          };
          this.items.push(newItem);
          this.setArr();
          this.$refs[formName].resetFields();
          this.getChannelList(
            this.formop.operator,
            this.formop.productId,
            this.channelform.channelType
          );
        }
      });
    },
    handleClick(tab, event) {
      this.activeName = tab.index;
      this.setArr();
    },
    setArr() {
      let strData = JSON.stringify(this.items);
      let data = JSON.parse(strData);
      this.tableData = data.filter((item) => {
        if (item.backup == this.activeName) {
          return item;
        }
      });
      // console.log(this.tableData, "this.tableData");
      this.funProportionSum(this.tableData);
    },
    deleteChannel(index, row) {
      this.$confirm("此操作将删除该通道, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          let codeIndex = this.items.findIndex(
            (item) => item.channelCode === row.channelCode && item.id === row.id
          );
          if (codeIndex !== -1) {
            this.items.splice(codeIndex, 1);
          }
          this.setArr();
          // this.tableData = [];
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == "add") {
            let data = {
              ruleName: this.formop.ruleName,
              operator: this.formop.operator,
              productId: this.formop.productId,
              items: this.items,
            };
            window.api.post(
              window.path.omcs + "operatingchannelgroup/rule",
              data,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.dialogFormVisible = false;
                  this.gettableLIst();
                } else {
                  this.$message({
                    message: res.msg,
                    type: "warning",
                  });
                }
              }
            );
          } else {
            //编辑
            let data = {
              ruleName: this.formop.ruleName,
              operator: this.formop.operator,
              productId: this.formop.productId,
              items: this.items,
              id: this.id,
            };
            this.$confirms.confirmation(
              "put",
              "确认执行此操作吗？",
              window.path.omcs + "operatingchannelgroup/rule",
              data,
              (res) => {
                if (res.code == 200) {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "success",
                  // });
                  this.dialogFormVisible = false;
                  this.gettableLIst();
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "error",
                  // });
                }
              }
            );
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    addopt() {
      this.dialogFormVisible = true;
      this.dialogStatus = "add";
      this.getChannelList(this.formop.operator, this.formop.productId, 1);
    },

    detailsRow(val) {
      // console.log(val);
      //编辑
      this.dialogFormVisible = true;
      this.dialogStatus = "edit";
      this.id = val.id;
      window.api.get(
        window.path.omcs + "operatingchannelgroup/rule/" + val.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.formop.ruleName = res.data.ruleName;
            this.formop.operator = res.data.operator + "";
            this.formop.productId = res.data.productId + "";
            this.items = res.data.items;
            this.setArr();
            this.getChannelList(this.formop.operator, this.formop.productId, 1);
          } else {
            this.$message.error(res.msg);
          }
        }
      );
    },
    //操作状态功能（启用停用）
    delState(index, val) {
      this.$confirms.confirmation(
        "delete",
        "确认执行此操作吗？",
        window.path.omcs + "operatingchannelgroup/rule/" + val.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: "success",
            // });
            this.dialogFormVisible = false;
            this.gettableLIst();
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: "error",
            // });
          }
        }
      );
    },
    copyState(row) {
      this.$confirms.confirmation(
        "post",
        "此操作将复制该规则，是否继续？",
        window.path.omcs + "operatingchannelgroup/rule/copy",
        {
          id: row.id,
        },
        (res) => {
          if (res.code == 200) {
            this.gettableLIst();
          }
        }
      );
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.productList = productData.productList;
        this.gettableLIst();
        this.getChannelList(
          this.formop.operator,
          this.formop.productId,
          this.channelform.channelType
        );
        localStorage.setItem("formop", JSON.stringify(this.formop));
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.productList = productData.productList;
      this.gettableLIst();
      this.getChannelList(
        this.formop.operator,
        this.formop.productId,
        this.channelform.channelType
      );
      localStorage.setItem("formop", JSON.stringify(this.formop));
    });
  },
  mounted() {
    const $table = this.$refs.tableRef;
    const $toolbar = this.$refs.toolbarRef;
    if ($table && $toolbar) {
      $table.connect($toolbar);
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formopRef.resetFields();
        this.$refs.formChannel.resetFields();
        this.activeName = "0";
        this.items = [];
        this.setArr();
      }
    },
    // 'formop.productId'(val, oldVal){
    //   this.tag = 2
    //   this.productoldVal = oldVal
    // },
    // 'formop.operator'(val, oldVal){
    //   this.tag = 1
    //   this.operatoroldVal = oldVal
    // }
  },
};
</script>
  <style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.input-w {
  width: 300px;
}
.item_tit {
  width: 100px;
  height: 30px;
  border: 1px solid #e5e5e5;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  margin-right: 10px;
}
.dialog-form {
  display: flex;
  width: 100%;
}
.dialog-form-btn {
  border-right: 1px #ccc dashed;
  margin: 0 20px;
}
.dialog-form-d {
  border-bottom: 1px #ccc dashed;
  margin: 8px 0;
}
.dialog-form-tit {
  color: #333;
  font-size: 12px;
  font-weight: bold;
  background: #f2f6fc;
  padding: 8px;
}
</style>
    
<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
    