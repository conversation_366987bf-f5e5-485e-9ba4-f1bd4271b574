<template>
  <div class="container_left">
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            label-width="82px"
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item
              label="发送类型"
              prop="timingStatus"
            >
              <el-select
                v-model="formInline.timingStatus"
                clearable
                placeholder="请选择类型"
                class="input-w"
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="未执行" value="1"></el-option>
                <el-option label="正在执行" value="2"></el-option>
                <el-option label="取消" value="3"></el-option>
                <el-option label="超时未执行" value="4"></el-option>
                <el-option label="执行完成" value="5"></el-option>
                <el-option label="执行失败" value="6"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="用户名" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="消息id" prop="msgid">
              <el-input
                v-model="formInline.msgid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="formInline.title"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="发送时间" label-width="80px" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="true"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>

            <div>
              <el-button type="primary" plain style="" @click.prevent="Query()"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>

        <div class="Mail-table" style="padding-bottom: 40px;">
          <!-- <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
            @selection-change="handelSelection"
          > -->

          <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
              <el-button
                type="danger"
                style="margin-right: 10px"
                @click="batchDeletion"
                v-if="selectId.length"
                >批量取消</el-button
          >
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef"
            id="RMSinterfaceTask"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableDataObj.tableData"
            @checkbox-all="handelSelection"
            @checkbox-change="handelSelection">

            <vxe-column type="checkbox" width="50"></vxe-column>
            <vxe-column field="用户名" title="用户名" width="150">
              <template v-slot="scope">
                <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.userName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.userName}}
                                    </span>
                                </el-popover> -->
                <div>
                  {{ scope.row.userName }}
                </div>
              </template>
            </vxe-column>
            <vxe-column width="120" field="发送ID" title="发送ID">
              <template v-slot="scope">
                <div>
                  {{ scope.row.id }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="消息ID" title="消息ID" width="200">
              <template v-slot="scope">
                <div>
                  {{ scope.row.msgid }}
                  <!-- <el-icon 
                    style="
                      color: #409eff;
                      cursor: pointer;
                      margin: 0 5px;
                      font-size: 14px;
                    "
                  ><DocumentCopy /></el-icon> -->
                  <CopyTemp :content="scope.row.msgid" />
                </div>
              </template>
            </vxe-column>

            <vxe-column field="标题" title="标题">
              <template v-slot="scope">
                <div
                  style="color: #409eff; cursor: pointer"
                  @click="View(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.title"
                    :content="scope.row.title"
                    className="wrapper-text"
                    effect="light"
                  ></Tooltip>
                </div>
              </template>
            </vxe-column>
            <!-- <vxe-column  field="预览" title="预览" >
                                <template #default="scope" >
                                    <span style="cursor: pointer;color: #16A589;" @click="View(scope.row)"><i class="el-icon-picture"></i>预览</span>
                                </template>
                            </vxe-column> -->
            <vxe-column field="文件名/手机号" title="文件名/手机号">
              <template v-slot="scope">
                <div
                  v-if="scope.row.filePath"
                  style="cursor: pointer; color: #16a589"
                  @click="download(scope.row)"
                  >{{ scope.row.fileOriginalName }}</div
                >
                <div
                  v-else
                  :title="scope.row.mobile"
                  style="
                    max-width: 300px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: block;
                  "
                  >{{ scope.row.mobile }}</div
                >
              </template>
            </vxe-column>
            <vxe-column field="发送时间" title="发送时间" width="170">
              <template v-slot="scope">
                <div>{{ scope.row.sendTime }}</div>
              </template>
            </vxe-column>
            <vxe-column field="发送状态" title="发送状态" width="100">
              <template v-slot="scope">
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 1"
                  type="info"
                  effect="dark"
                  
                >
                  未执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 2"
                  type="warning"
                  effect="dark"
                  
                >
                  正在执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 3"
                  type="info"
                  effect="dark"
                  
                >
                  取消
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 4"
                  type="danger"
                  effect="dark"
                  
                >
                  超时未执行
                </el-tag>
                <el-tag
                  :disable-transitions="true"
                  v-if="scope.row.timingStatus == 5"
                  type="success"
                  effect="dark"
                  
                >
                  执行完成
                </el-tag>
                <el-tag
                  :disable-transitions="true" v-if="scope.row.timingStatus == 6" type="danger" effect="dark">
                  执行失败
                </el-tag>
                <!-- <span v-if="scope.row.timingStatus==1">未执行</span>
                                    <span v-else-if="scope.row.timingStatus==2">正在执行</span>
                                    <span v-else-if="scope.row.timingStatus==3">取消</span>
                                    <span style="color: red;" v-else-if="scope.row.timingStatus==4">超时未执行</span>
                                    <span v-else-if="scope.row.timingStatus==5">执行完成</span> -->
              </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" width="120">
              <template v-slot="scope">
                <el-button
                  link
                  v-if="scope.row.timingStatus == 1"
                  style="color: red; margin-left: 0px"
                  @click="cancel(scope.row)"
                  ><el-icon><CircleCloseFilled /></el-icon> 取消</el-button
                >
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            > -->

          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </div>

            <!-- </el-col>
          </template> -->
        </div>
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in viewData"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png' ||
                    item.media == 'jpeg'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                ></audio>
                <div style="white-space: pre-line" v-html="item.txt"></div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    DatePlugin,
    TableTem,
    UserLists,
    Tooltip,
    CopyTemp
  },
  name: 'RMSinterfaceTask',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      name: 'RMSinterfaceTask',
      userFlag: false,
      nameover: '',
      // 定时时间
      datePluginValueList: {
        //日期参数配置
        type: 'datetime',
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7
          },
        },
        defaultTime: '', //默认起始时刻
        datePluginValue: '',
      },
      sendTime: '',
      taskSmsId: '',
      dialogVisible: false,
      viewData: [], // 查看内容
      title: '',
      dialogVisibleTime: false,
      //复选框值
      selectId: '',
      // 搜索数据
      formInline: {
        timingStatus: '',
        msgid: '',
        title: '',
        userName: '',
        beginTime: moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ],
        flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        timingStatus: '',
        msgid: '',
        title: '',
        userName: '',
        beginTime: moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          moment().format('YYYY-MM-DD HH:mm:ss'),
        ],
        flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        'post',
        '确定取消定时发送？',
        window.path.omcs + 'consumertimingvideo/cancel',
        { ids: this.selectId },
        (res) => {
          this.InquireList()
        }
      )
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
      }
      this.selectId = selectId //批量操作选中id
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumertimingvideo/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 预览
    View(val) {
      this.viewData = val.contents
      this.title = val.title
      this.dialogVisible = true
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        'post',
        '确定取消定时视频短信？',
        window.path.cpus + 'consumertimingvideo/cancel',
        { ids: [val.id] },
        (res) => {
          this.InquireList()
        }
      )
    },
    // 下载
    download(val) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          'M+': this.getMonth() + 1,
          'd+': this.getDate(),
          'h+': this.getHours(),
          'm+': this.getMinutes(),
          's+': this.getSeconds(),
          'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        }
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + '').substr(4 - RegExp.$1.length)
          )
        for (var i in args) {
          var n = args[i]
          if (new RegExp('(' + i + ')').test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
            )
        }
        return format
      }
      var that = this
      filedownload()
      function filedownload() {
        fetch(
          that.API.cpus +
            'v3/file/download?fileName=' +
            val.fileOriginalName +
            '&group=group1&path=' +
            val.filePath.slice(7),
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
          }
        )
          .then((res) => res.blob())
          .then((data) => {
            let blobUrl = window.URL.createObjectURL(data)
            download(blobUrl)
          })
      }
      function download(blobUrl) {
        var a = document.createElement('a')
        a.style.display = 'none'
        a.download =
          '(' +
          new Date().format('YYYY-MM-DD HH:mm:ss') +
          ') ' +
          val.fileOriginalName
        a.href = blobUrl
        a.click()
      }
    },
    // 查询
    Query() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      ;(this.formInline.time = []), (this.formInline.beginTime = '')
      this.formInline.endTime = ''
      ;(this.formInline.time1 = []), (this.formInline.startTime = '')
      this.formInline.stopTime = ''
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + ' 00:00:00'
        this.formInline.stopTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.startTime = ''
        this.formInline.stopTime = ''
      }
    },
    // 定时时间
    handledatepluginVal: function (val1, val2) {
      //日期
      this.sendTime = val1
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
    // handleCopy(name,event){
    //     clip(name, event)
    // },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.el-scrollbar__wrap {
  margin-bottom: 0px !important;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>