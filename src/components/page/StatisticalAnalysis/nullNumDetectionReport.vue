<template>
  <div>
    <div class="Top_title">空号检测报表</div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名称" label-width="80px" prop="userName">
              <el-input
                v-model="formInline.userName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="批次号" label-width="80px" prop="batchNo">
              <el-input
                v-model="formInline.batchNo"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="提交时间" label-width="80px" prop="time">
              <el-date-picker
                class="input-w"
                v-model="formInline.time"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="false"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              label="检测状态"
              label-width="80px"
              prop="submitStatus"
            >
              <el-select
                v-model="formInline.submitStatus"
                placeholder="请选择"
                class="input-w"
              >
                <el-option label="请选择" value=""></el-option>
                <el-option label="提交成功" value="1"></el-option>
                <el-option label="检测中" value="2"></el-option>
                <el-option label="检测完成" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>
        <div class="sensitive-fun" style="margin-top: 10px">
          <!-- <span class="sensitive-list-header">空号检测记录</span> -->
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <table-tem
            :tableDataObj="tableDataObj"
            @handelOptionButton="handelOptionButton"
          ></table-tem>
          <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            >
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              >
              </el-pagination>
            </el-col>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
export default {
  name: 'EmptyNumberReport',
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      // 搜索数据
      formInline: {
        batchNo: '',
        userName: '',
        submitStatus: '',
        begTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        userName: '',
        batchNo: '',
        submitStatus: '',
        begTime: '',
        endTime: '',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: 'userName',
            showName: '用户名称',
            fixed: false,
          },
          {
            prop: 'batchNo',
            showName: '批次号',
            fixed: false,
          },
          {
            prop: 'submitDate',
            showName: '提交时间',
            width: '140',
            fixed: false,
          },
          {
            prop: 'submitStatus',
            showName: '检测状态',
            width: '100',
            fixed: false,
            showCondition: {
              condition: '1',
            },
            formatData: function (val) {
              if (val == 1) {
                return (val = '提交成功')
              } else if (val == 2) {
                return (val = '检测中')
              } else {
                return (val = '检测完成')
              }
            },
          },
          {
            prop: 'reportStatus',
            showName: '报表状态',
            width: '100',
            fixed: false,
            showCondition: {
              condition: '1',
            },
            formatData: function (val) {
              if (val == 1) {
                return (val = '未生成')
              } else if (val == 2) {
                return (val = '生成中')
              } else {
                return (val = '完成')
              }
            },
          },
          {
            prop: 'reason',
            showName: '数据处理描述',
            fixed: false,
          },

          {
            prop: 'phoneSize',
            showName: '提交号码数',
            fixed: false,
          },
          {
            prop: 'succSize',
            showName: '在网正常',
            fixed: false,
          },
          {
            prop: 'failSize',
            // showName:'不在网异常（空号，暂停服务，过期）',
            showName: '不在网异常',
            fixed: false,
          },
          {
            prop: 'stopSize',
            // showName:'在网异常（停机，关机，不在服务区）',
            showName: '在网异常',
            fixed: false,
          },
          {
            prop: 'riskSize',
            showName: '反欺诈',
            fixed: false,
          },
          {
            prop: 'silenceSize',
            showName: '沉默号',
            fixed: false,
          },
          {
            prop: 'unknownSize',
            showName: '未知状态',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '160', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          // {
          //     optionName:'查看详情',
          //     type:'',
          //     size:'mini',
          //     optionMethod:'querys',
          //     icon:'el-icon-setting'
          // },
          {
            optionName: '导出',
            type: '',
            size: 'mini',
            optionMethod: 'exportNums',
            icon: 'el-icon-download',
          },
        ],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.wmcs + 'phonecheck/phoneList',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    handelOptionButton(val) {
      //查看详情跳转
      if (val.methods == 'querys') {
        //查看详情跳转
        this.$router.push({
          name: 'detailsList',
          query: { num: val.row.batchNo, id: val.row.userId },
        })
      } else if (val.methods == 'exportNums') {
        //导出
        if (val.row.fileGroup != '' && val.row.reportStatus == 3) {
          // 时间过滤
          Date.prototype.format = function (format) {
            var args = {
              'M+': this.getMonth() + 1,
              'd+': this.getDate(),
              'h+': this.getHours(),
              'm+': this.getMinutes(),
              's+': this.getSeconds(),
              'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
              S: this.getMilliseconds(),
            }
            if (/(y+)/.test(format))
              format = format.replace(
                RegExp.$1,
                (this.getFullYear() + '').substr(4 - RegExp.$1.length)
              )
            for (var i in args) {
              var n = args[i]
              if (new RegExp('(' + i + ')').test(format))
                format = format.replace(
                  RegExp.$1,
                  RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
                )
            }
            return format
          }
          var that = this
          filedownload()
          function filedownload() {
            fetch(
              that.API.wmcs +
                'phonecheck/emptyphonedownload/' +
                val.row.batchNo,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  Authorization:
                    'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
                },
                // body: JSON.stringify({
                //     batchNo: val.row.batchNo,
                // })
              }
            )
              .then((res) => res.blob())
              .then((data) => {
                let blobUrl = window.URL.createObjectURL(data)
                download(blobUrl)
              })
          }
          function download(blobUrl) {
            var a = document.createElement('a')
            a.style.display = 'none'
            a.download =
              '(' +
              new Date().format('YYYY-MM-DD HH:mm:ss') +
              ') ' +
              '空号检测.zip'
            a.href = blobUrl
            a.click()
          }

          // this.$File.export(window.path.cpus +'mobileCheck/download',{batchNo:val.row.batchNo},`空号检测报表.zip`);
        } else if (val.row.fileGroup == '' && val.row.reportStatus == 3) {
          this.$message.warning('接口提交的数据不可导出！')
        } else if (val.row.fileGroup != '' && val.row.reportStatus != 3) {
          this.$message.warning('报表状态未完成不可导出！')
        } else {
          this.$message.warning('此数据不可导出！')
        }
      }
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.begTime = ''
      this.formInline.endTime = ''
      Object.assign(this.formInlines, this.formInline)
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.begTime = val[0] + ' 00:00:00'
        this.formInline.endTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.begTime = ''
        this.formInline.endTime = ''
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
  },
  created() {},
  activated() {
    this.InquireList()
  },
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
