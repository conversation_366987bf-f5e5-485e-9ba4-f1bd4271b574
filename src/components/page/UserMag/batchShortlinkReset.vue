<template>
  <div class="container_left">
    <div class="crumbs">
      <el-page-header @back="goBack" :content="title"> </el-page-header>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="queryForm"
        >
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              v-model="formInline.taskName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="任务类型" prop="action">
                          <el-select v-model="formInline.action" clearable placeholder="不限" class="input-w">
                              <el-option label="不限" value=""></el-option>
                              <el-option label="管理商" value="12"></el-option>
                              <el-option label="子用户" value="13"></el-option>
                              <el-option label="终端" value="14"></el-option>
                              <el-option label="线上终端" value="22"></el-option>
                          </el-select>
                      </el-form-item>
                      <el-form-item label="创建时间" prop="time">
                          <el-date-picker class="input-time" v-model="formInline.time" value-format="YYYY-MM-DD"
                              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                              @change="handeTime">
                          </el-date-picker>
                      </el-form-item> -->
          <el-form-item>
            <el-button type="primary" plain style="" @click="Query"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reload('queryForm')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table Mail-table1">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button @click="addUserStopTask" type="primary">新增</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="batchShortlinkReset"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <!-- <vxe-column type="selection"> </vxe-column> -->
          <vxe-column field="任务名称" title="任务名称" width="170">
            <template #default="scope">
              <div
                style="color: #409eff; cursor: pointer"
                @click="handleDetail(scope.row)"
              >
                <Tooltip
                  v-if="scope.row.taskName"
                  :content="scope.row.taskName"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="目标对象" title="目标对象">
            <template #default="scope">
              <div>
                {{ scope.row.targetStr }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作类型" title="操作类型">
            <template #default="scope">
              <div>
                {{ scope.row.actionStr }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态" width="130">
            <template #default="scope">
              <div>
                <el-tag
                  :disable-transitions="true" type="info" effect="dark" v-if="scope.row.status == '0'"
                  >待执行</el-tag
                >
                <el-tag
                  :disable-transitions="true"
                  type="warning"
                  effect="dark"
                  v-else-if="scope.row.status == '1'"
                  >执行中</el-tag
                >
                <el-tag
                  :disable-transitions="true"
                  type="success"
                  effect="dark"
                  v-else-if="scope.row.status == '2'"
                  >执行完成</el-tag
                >
                <el-tag
                  :disable-transitions="true"
                  type="danger"
                  effect="dark"
                  v-else-if="scope.row.status == '3'"
                  >执行失败</el-tag
                >
                <el-tooltip
                  v-if="scope.row.status == '3'"
                  class="item"
                  effect="dark"
                  :content="scope.row.statusReason"
                  placement="top-start"
                >
                  <i
                    style="color: #f56c6c; font-size: 18px; margin-left: 10px"
                    class="el-icon-warning"
                  ></i>
                </el-tooltip>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="总匹配数" title="总匹配数">
            <template #default="scope">
              <div>
                {{ scope.row.total }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            field="已处理数"
            title="已处理数"
          >
            <template #default="scope">
              <div>
                {{ scope.row.completeCount }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="处理成功数" title="处理成功数">
            <template #default="scope">
              <div>
                {{ scope.row.success }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            field="处理时长（毫秒）"
            title="处理时长（毫秒）"
            width="130"
          >
            <template #default="scope">
              <div>
                {{ scope.row.costTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            field="创建时间"
            title="创建时间"
            width="170"
          >
            <template #default="scope">
              <div>
                {{ scope.row.createdTimeStr }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template #default="scope">
              <div>
                {{ scope.row.createdUser }}
              </div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInline.currentPage"
            :page-size="formInline.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
        <!-- 新增任务弹窗开始 -->
        <el-dialog
          title="新增任务 "
          v-model="dialogVisible"
          width="750px"
          :before-close="handleClose"
        >
          <el-form
            :rules="rules"
            ref="batchForm"
            :model="batchForm"
            label-width="100px"
          >
            <el-form-item label="任务名称" prop="taskName">
              <el-input class="input-w" v-model="batchForm.taskName"></el-input>
            </el-form-item>

            <el-form-item label="有效期" prop="resetTime">
              <el-date-picker
                :picker-options="pickerOptions"
                v-model="batchForm.updateValue.resetTime"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="文件上传" prop="fileList">
              <el-upload
                class="upload-demo"
                :action="actionUrl"
                :headers="token"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                :before-upload="beforeAvatarUpload"
                :limit="1"
                :file-list="batchForm.updateValue.fileList"
              >
                <div style="display: flex">
                  <el-button size="small" type="primary">点击上传</el-button>
                  <a
                    style="margin-left: 10px"
                    href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/e56a609a1589c9452b66d6489f578560"
                    target="_blank"
                    rel="noopener noreferrer"
                    >模版下载</a
                  >
                </div>

                <!-- <div slot="tip" class="el-upload__tip">
                    
                  </div> -->
              </el-upload>
            </el-form-item>
            <div>
              <div style="color: red">筛选条件</div>
              <el-form-item label="公司名" prop="filter.compId">
                <el-select
                  ref="optionRef"
                  class="input-w"
                  v-model="batchForm.filter.compId"
                  clearable
                  filterable
                  remote
                  :remote-method="remoteMethod"
                  :loading="loadingcomp"
                  placeholder="请选择公司名称"
                  @change="bindChange"
                >
                  <el-option
                    v-for="(item, index) in compNamelist"
                    :key="index"
                    :label="item.company"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="用户名" prop="filter.consumerNameArr">
                <el-transfer
                  :titles="['待选用户', '已选用户']"
                  v-model="batchForm.filter.consumerNameArr"
                  :data="userData"
                  @change="handleChange"
                ></el-transfer>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="备注" prop="remark">
                <el-input
                  class="input-time"
                  type="textarea"
                  v-model="batchForm.remark"
                ></el-input>
              </el-form-item>
            </div>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="dialogVisible = false">取 消</el-button>
              <el-button type="primary" @click="submitBatchForm('batchForm')"
                >执行</el-button
              >
            </div>
          </template>
        </el-dialog>

        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>
  <script>
import Tooltip from "@/components/publicComponents/tooltip.vue";
import clip from "../../../utils/clipboard";
import moment from "moment";
export default {
  name: "batchUserShop",
  components: {
    Tooltip,
  },
  data() {
    // const generateData = _ => {
    //     const data = [];
    //     for (let i = 1; i <= 15; i++) {
    //         data.push({
    //             key: i,
    //             label: `备选项 ${i}`,
    //         });
    //     }
    //     return data;
    // };
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      actionUrl: window.path.cpus + 'v3/file/upload',
      isFirstEnter: false,
      dialogVisible: false,
      title: "",
      token: {},
      formInline: {
        taskName: "", //任务名称
        action: "", //任务类型
        // time: [],
        // createBeginTime: "",
        // createEndTime: "",
        pageSize: 10,
        currentPage: 1,
      },
      localData: {},
      batchForm: {
        taskName: "", //任务名称
        action: "", //任务类型
        remark: "", //备注
        filter: {
          // time: '',
          // roleId: "",//角色ID
          consumerNameArr: [], //用户名
          compId: "", //公司名
          consumerStatus: "0", //账号状态
          // consumerStatus: "",//账号状态
          // service: "",//所属客服
          // ext: "",//扩展号
          // sales: "",//所属销售
          // createName: "",//创建人
          // smsChargeback: "",//扣费方式
          // smsPayMethod: "",//付费方式
          // createBeginTime: "",//开始的时间
          // createEndTime: "",//结束的时间
          // certificate: "",//是否认证
          // cooperateStatus: "",//合作状态
        },
        updateValue: {
          resetTime: "", //有效期
          fileList: [], //文件列表
          fileUrl: "", //文件地址
        },
      },
      services: [], //客服列表
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000;
        },
      },
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "change" },
        ],
        "updateValue.resetTime": [
          { required: true, message: "有效期不能为空", trigger: "change" },
        ],
        "updateValue.fileList": [
          { required: true, message: "文件不能为空", trigger: "change" },
        ],
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
        total: 0, //总条数
      },
      compNamelist: [],
      loadingcomp: false,
      userData: [],
    };
  },
  watch: {
    //监听弹窗显示隐藏
    dialogVisible(val) {
      if (!val) {
        //关闭弹窗时重置表单
        this.$refs["batchForm"].resetFields();
        this.batchForm.action = this.localData.code;
        this.batchForm.filter.consumerNameArr = [];
        this.userData = [];
        this.compNamelist = [];
        // this.batchForm.action = this.$route.query.action;
      }
    },
  },
  methods: {
    //返回上一页
    goBack() {
      this.$router.push("/batchaaAction");
    },
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
            // this.services = res.data;
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.compNamelist = [];
        this.searchAccount();
      }
    },
    getUserList(compId, status) {
      window.api.get(
        window.path.omcs +
          `operatinguser/users/${compId}?consumerStatus=${status}`,
        {},
        (res) => {
          if (res.code == 200) {
            this.userData = res.data.map((item) => {
              return {
                key: item.consumerName,
                label: item.consumerName,
              };
            });
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }
          // this.services = res.data;
        }
      );
    },
    handleAction(e) {
      // let consumerStatus = e == 0 ? '1' : '0';
      // this.userData = []
      // this.batchForm.filter.consumerNameArr = []
      // this.batchForm.filter.consumerStatus = consumerStatus
      // console.log(this.batchForm.filter.compId, 'this.batchForm.filter.compId');
      // if (this.batchForm.filter.compId) {
      //     console.log(1111);
      //     this.getUserList(this.batchForm.filter.compId, consumerStatus)
      // }
    },
    bindChange(e) {
      if (e) {
        this.getUserList(e, this.batchForm.filter.consumerStatus);
      }
    },
    handleChange(e) {
      // console.log(e, 'eee');
      // this.batchForm.filter.consumerNameArr = e
      // console.log(this.userData, 'e')
    },
    //获取列表数据
    getTableData() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operating/client/batch/task/page",
        this.formInline,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    //查询
    Query() {
      this.getTableData();
    },
    //重置
    Reload(formName) {
      this.$refs[formName].resetFields(); //清空查询表单
      this.formInline.action = this.localData.code;
      this.getTableData();
    },
    //复制
    handleCopy(name, event) {
      clip(name, event);
    },
    //时间格式化
    handeTime(val) {
      if (val) {
        this.batchForm.filter.createBeginTime = moment(val[0]).format(
          "YYYY-MM-DD "
        );
        this.batchForm.filter.createEndTime = moment(val[1]).format(
          "YYYY-MM-DD "
        );
      } else {
        this.batchForm.filter.createBeginTime = "";
        this.batchForm.filter.createEndTime = "";
      }
    },
    // handleSelectionChange() {

    // },
    //-----翻页操作
    handleSizeChange(size) {
      this.formInline.pageSize = size;
      this.getTableData();
    },
    handleCurrentChange: function (currentPage) {
      this.formInline.currentPage = currentPage;
      this.getTableData();
    },
    handleClose() {
      this.dialogVisible = false;
    },
    //获取客服列表
    getServices() {
      window.api.get(
        window.path.upms + "user/selectAllSalesOrService",
        { flag: 2 },
        (res) => {
          this.services = res.data;
        }
      );
    },
    // handleRadioChange(val){
    //     if
    // },
    //新增任务弹窗
    addUserStopTask() {
      this.dialogVisible = true;
      this.batchForm.taskName = "批量短链续期-" + new Date().getTime();
      this.getServices();
    },
    beforeAvatarUpload(file) {
      const siJPGGIF = file.name.split(".")[1];
      const fileType = ["txt"];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning("上传文件只能是txt格式!");
        return false;
      }
    },
    handleRemove(file, fileList) {
      this.batchForm.updateValue.fileList = [];
      this.batchForm.updateValue.fileUrl = "";
    },
    handleSuccess(res, file, fileList) {
      if (res.code == 200) {
        this.batchForm.updateValue.fileList = fileList;
        this.batchForm.updateValue.fileUrl = res.data.fullpath;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    //提交表单
    submitBatchForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.batchForm));
          if (this.userData.length == params.filter.consumerNameArr.length) {
            params.filter.consumerNameArr = [];
          }
          params.filter = JSON.stringify(params.filter);
          params.updateValue.resetTime = moment(
            params.updateValue.resetTime
          ).format("YYYY-MM-DD HH:mm:ss");
          delete params.updateValue.fileList;
          params.updateValue = JSON.stringify(params.updateValue);
          console.log(params, "params");
          this.$confirms.confirmation(
            "post",
            "是否确认执行此操作？",
            window.path.omcs + "operating/client/batch/task/create",
            params,
            (res) => {
              this.$message.success("任务创建成功");
              this.dialogVisible = false;
              this.getTableData();
            }
          );
          // window.api.post(window.path.omcs + "operating/client/batch/task/create", params, (res) => {
          //     if (res.code == 200) {
          //         this.$message.success('任务创建成功');
          //         this.dialogVisible = false;
          //         this.getTableData();
          //     } else {
          //         this.$message.error(res.msg);
          //     }
          // });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //查看详情
    handleDetail(row) {
      this.$router.push({
        path: "/batchUserList",
        query: {
          id: row.id,
          action: row.target,
        },
      });
    },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.token = {
        Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
      };
      this.localData = JSON.parse(localStorage.getItem("batchAction"));
      this.formInline.action = this.localData.code;
      this.batchForm.action = this.localData.code;
      this.title = this.localData.title;
      this.getTableData();
    });

    // this.getSales()
    // this.getChannel()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  // activated() {
  //     if (this.$route.meta.isBack || !this.isFirstEnter) {
  //         this.$nextTick(() => {
  //             this.localData = JSON.parse(localStorage.getItem('batchAction'));
  //             this.formInline.action = this.localData.code;
  //             this.batchForm.action = this.localData.code;
  //             this.title = this.localData.title
  //             this.getTableData();
  //         });
  //     } else {
  //         this.$route.meta.isBack = false;
  //         this.isFirstEnter = false;
  //     }
  // },
};
</script>
  <style lang="less" scoped>
.fillet {
  margin-top: 18px;
}

:deep(.el-table__row) {
  height: 48px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
    
    
    