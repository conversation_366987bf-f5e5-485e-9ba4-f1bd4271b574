<template>
  <div class="container_left">
    <!-- <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          >
          <i class="iconfont icon-xiaolian"></i>5G企业注册</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div> -->
    <div style="padding: 10px">
      <el-form label-width="80px" :model="ruleForm" :inline="true" ref="ruleForm" class="demo-ruleForm">
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="customerName">
          <el-input class="input-w" v-model="ruleForm.customerName"></el-input>
        </el-form-item>
        <el-form-item label="企业ID" prop="customerId">
          <el-input class="input-w" v-model="ruleForm.customerId"></el-input>
        </el-form-item>
        <el-form-item label="通道" prop="channelId">
          <el-select class="input-w" v-model="ruleForm.channelId" placeholder="选择通道" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通道状态" prop="channelStatusArr">
          <el-select class="input-w" v-model="ruleForm.channelStatusArr" multiple placeholder="请选择">
            <el-option label="待注册" value="0"> </el-option>
            <el-option label="通道注册中" value="10"> </el-option>
            <el-option label="通道注册失败" value="20"> </el-option>
            <el-option label="通道注册成功" value="30"> </el-option>
            <el-option label="分配服务代码中" value="40"> </el-option>
            <el-option label="分配服务代码失败" value="45"> </el-option>
            <el-option label="分配服务代码成功" value="48"> </el-option>
            <el-option label="变更中" value="52"> </el-option>
            <el-option label="正常" value="50"> </el-option>
            <el-option label="变更失败" value="55"> </el-option>
            <el-option label="通道暂停运营" value="60"> </el-option>
            <!-- <el-option label="注销中" value="-1"> </el-option>
                <el-option label="注销失败" value="-2"> </el-option>
                <el-option label="已注销" value="-3"> </el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="创建人" prop="createdUser">
          <el-input class="input-w" v-model="ruleForm.createdUser"></el-input>
        </el-form-item>
        <el-form-item label="创建时间" prop="time1">
          <el-date-picker :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName" v-model="ruleForm.time1"
            type="datetimerange" @change="timeD" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            align="right">
          </el-date-picker>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')">查询</el-button>
          <el-button plain type="primary" @click="resetForm('ruleForm')">重置</el-button>
        </div>
      </el-form>
    </div>
    <div style="padding: 10px;">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        stripe
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
        @selection-change="handleSelectionChange"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
          <div style="display: flex">
            <div>
              <el-button type="primary" @click="addUser">新增企业注册</el-button>
              <el-button :disabled="!customerIdList.length" type="primary" @click="allBacth">一键报备</el-button>
              <el-button type="primary" @click="batchImport">批量导入</el-button>
              <el-button type="primary" @click="channelSync">通道同步</el-button>
            </div>
            <div style="margin: 0 10px; display: flex; ">
              <el-upload v-bind:data="{
                FoldPath: '上传目录',
                SecretKey: '安全验证',
              }" :show-file-list="false" :action="batchAction" :headers="token" :limit="1" name="zipFile"
                :file-list="batchFileList" :before-upload="(file) => {
                  return beforeAvatarUploadAtt(file, 'batch')
                }
                  " :on-remove="(file) => {
                    return handleRemoveAtt(file, 'batch')
                  }
                    " :on-success="(response, fileList) => {
                      return handleSuccessAtt(response, fileList, 'batch', 'excel')
                    }
                      " multiple>
                <el-button type="primary">批量上传企业合同</el-button>
              </el-upload>
              <el-upload style="margin-left: 10px;" :show-file-list="false"  :action="shuomingAction" :headers="token" :limit="1" :file-list="shuomingFileList"
                :before-upload="handleShuomingUpload" :on-remove="handleShuomingRemove" :on-success="handleShuomingSuccess" :on-error="handleShuomingError">
                <el-button size="default" type="primary">批量导入授权书</el-button>
                <!-- <div slot="tip" class="el-upload__tip">仅支持上传zip格式并大小限制50mb</div> -->
              </el-upload>
            </div>
          </div>
        </template>
      </vxe-toolbar>

      <vxe-table ref="tableRef" id="5gAuditCertification" border stripe show-overflow :custom-config="customConfig"
        :column-config="{ resizable: true }" :row-config="{ isHover: true }" :cell-config="{ height: 60 }"
        min-height="100" v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;" :data="tableDataObj.tableData"
        @checkbox-all="handleSelectionChange" @checkbox-change="handleSelectionChange">

        <vxe-column type="checkbox" width="50"> </vxe-column>
        <vxe-column field="用户名称" title="用户名称" width="150">
          <template #default="scope">
            <div class="wrapper-text">
              {{ scope.row.username }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                    @click="handleCopy(scope.row.username, $event)"></i> -->
              <CopyTemp :content="scope.row.username" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="企业ID" title="企业ID" width="180">
          <template #default="scope">
            <div>
              {{ scope.row.customerId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                    @click="handleCopy(scope.row.customerId, $event)"></i> -->
              <CopyTemp :content="scope.row.customerId" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="企业名称" title="企业名称" width="200">
          <template #default="scope">
            <Tooltip v-if="scope.row.customerName" :content="scope.row.customerName" className="wrapper-text"
              effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">
                  {{ scope.row.customerName }}
                </div> -->
          </template>
        </vxe-column>
        <!-- <vxe-column field="联系人" title="联系人" width="120">
              <template #default="scope">
                <Tooltip v-if="scope.row.customerContactPerson" :content="scope.row.customerContactPerson"
                  className="wrapper-text" effect="light">
                </Tooltip>
              </template>
            </vxe-column> -->
        <vxe-column field="联系人电话" title="联系人电话" width="140">
          <template #default="scope">
            <Tooltip v-if="scope.row.contactMaskPhone" :content="scope.row.contactMaskPhone" className="wrapper-text"
              effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">{{ scope.row.contactMaskPhone }}</div> -->
          </template>
        </vxe-column>
        <!-- <vxe-column field="联系人邮箱" title="联系人邮箱">
              <template #default="scope">
                <div class="wrapper-text">{{ scope.row.contactPersonEmail }}</div>
              </template>
            </vxe-column> -->
        <vxe-column field="扩展码" title="扩展码" width="140">
          <template #default="scope">
            <Tooltip v-if="scope.row.code" :content="scope.row.code" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <div class="wrapper-text">{{ scope.row.code }}</div> -->
          </template>
        </vxe-column>

        <vxe-column field="移动" title="移动" width="80">
          <template #default="scope">
            <div>
              <!-- <i class="iconfont icon-yidong" style="color: #409eff;font-size: 20px;margin: 0 5px"></i>
              <el-tag :type="scope.row.ydStatus == 0 ? 'danger' : scope.row.ydStatus == 1 ? 'success' : 'warning'"
                effect="light">
                {{ scope.row.ydStatus == 0 ? '不可用' : scope.row.ydStatus == 1 ? '可用' : '等待中' }}
              </el-tag>
              <el-tooltip class="item" effect="dark"
                :content="scope.row.ydStatus == 0 ? '不可用' : scope.row.ydStatus == 1 ? '可用' : '等待中'"
                placement="top-start">
               
              </el-tooltip> -->
              <el-tooltip v-if="scope.row.ydStatus == 0" effect="dark" content="不可用" placement="top">
                <span class="circle"></span>
              </el-tooltip>
              <el-tooltip v-else-if="scope.row.ydStatus == 1" effect="dark" content="可用" placement="top">
                <span class="circle-active"></span>
              </el-tooltip>
              <el-tooltip v-else effect="dark" content="等待中" placement="top">
                <span class="circle_a1"></span>
              </el-tooltip>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="联通" title="联通" width="80">
          <template #default="scope">
            <!-- <div style="display: flex;align-items: center;">
              <i class="iconfont icon-liantong" style="color: #F56C6C;font-size: 20px;margin: 0 5px"></i>
              <el-tag :type="scope.row.ltStatus == 0 ? 'danger' : scope.row.ltStatus == 1 ? 'success' : 'warning'"
                effect="light">
                {{ scope.row.ltStatus == 0 ? '不可用' : scope.row.ltStatus == 1 ? '可用' : '等待中' }}
              </el-tag>
            </div>
            <el-tooltip class="item" effect="dark"
              :content="scope.row.ltStatus == 0 ? '不可用' : scope.row.ltStatus == 1 ? '可用' : '等待中'" placement="top-start">
              <i class="iconfont icon-liantong" :style="{
                fontSize: '20px',
                margin: '0 5px',
                color: scope.row.ltStatus == 0 ? '#ccd5db' : scope.row.ltStatus == 1 ? '#F56C6C' : '#ccd5db'
              }"></i>
            </el-tooltip> -->
            <el-tooltip v-if="scope.row.ltStatus == 0" effect="dark" content="不可用" placement="top">
              <span class="circle"></span>
            </el-tooltip>
            <el-tooltip v-else-if="scope.row.ltStatus == 1" effect="dark" content="可用" placement="top">
              <span class="circle-active"></span>
            </el-tooltip>
            <el-tooltip v-else effect="dark" content="等待中" placement="top">
              <span class="circle_a1"></span>
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="电信" title="电信" width="80">
          <template #default="scope">
            <!-- <div style="display: flex;align-items: center;">
              <i class="iconfont icon-dianxin" style="color: #409eff;font-size: 20px;margin: 0 5px"></i>
              <el-tag :type="scope.row.dxStatus == 0 ? 'danger' : scope.row.dxStatus == 1 ? 'success' : 'warning'"
                effect="light">
                {{ scope.row.dxStatus == 0 ? '不可用' : scope.row.dxStatus == 1 ? '可用' : '等待中' }}
              </el-tag>
            </div>
            <el-tooltip class="item" effect="dark"
              :content="scope.row.dxStatus == 0 ? '不可用' : scope.row.dxStatus == 1 ? '可用' : '等待中'" placement="top-start">
              <i class="iconfont icon-dianxin" :style="{
                fontSize: '20px',
                margin: '0 5px',
                color: scope.row.dxStatus == 0 ? '#ccd5db' : scope.row.dxStatus == 1 ? '#409eff' : '#ccd5db'
              }"></i>
            </el-tooltip> -->
            <el-tooltip v-if="scope.row.dxStatus == 0" effect="dark" content="不可用" placement="top">
              <span class="circle"></span>
            </el-tooltip>
            <el-tooltip v-else-if="scope.row.dxStatus == 1" effect="dark" content="可用" placement="top">
              <span class="circle-active"></span>
            </el-tooltip>
            <el-tooltip v-else effect="dark" content="等待中" placement="top">
              <span class="circle_a1"></span>
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column field="创建人" title="创建人" width="140">
          <template #default="scope">
            <div class="wrapper-text">{{ scope.row.createdUser }}</div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="100">
          <template #default="scope">
            <p class="wrapper-text" v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, '{y}-{m}-{d}') }}
            </p>
            <p class="wrapper-text" v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time">{{
                  moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss")
                }}</div> -->
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" min-width="240" fixed="right">
          <template #default="scope">
            <div>
              <el-button link style="color: #16a589; margin-left: 0px;" @click="checked(scope.row)"><i
                  class="iconfont icon-jibenxinxi"></i> 基本信息</el-button>
              <el-button link style="color: #409eff; margin-left: 10px" @click="FgRegister(scope.row)">5G注册</el-button>
              <el-button link style="color: #409eff; margin-left: 10px"
                @click="addChatbot(scope.row)">新增chatbot</el-button>
            </div>
            <div>
              <el-button v-if="
                scope.row.ydStatus == 0 &&
                scope.row.ltStatus == 0 &&
                scope.row.dxStatus == 0
              " link style="color: red" @click="delCustomer(scope.row)">
                <i class="iconfont icon-shanchu1"></i> 删除
              </el-button>
            </div>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="paginationBox">
        <el-pagination style="text-align: right; padding: 10px" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="基本信息" v-model="dialogVisible" width="1150px" :before-close="handleClose"
      :close-on-click-modal="false">
      <div>
        <div style="
            width: 100%;
            margin-left: 110px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
          ">
          <span>运营商</span>
          <el-checkbox-group style="margin-left: 20px" v-model="operationType" @change="handOperatorsType">
            <el-checkbox value="yd"> 移动 </el-checkbox>
            <el-checkbox value="lt"> 联通 </el-checkbox>
            <el-checkbox value="dx"> 电信 </el-checkbox>
          </el-checkbox-group>
        </div>
        <el-form :model="registerFrom" :inline="true" label-width="180px" :rules="rules" :ref="refName"
          style="widtn: 100%" class="demo-ruleForm">
          <div class="title_info">5G企业主体</div>
          <div>
            <el-form-item label="5G企业名称" prop="customerName">
              <el-input class="classInput" v-model="registerFrom.customerName"></el-input>
            </el-form-item>
            <el-form-item v-if="ydFlag" label="移动扩展号" prop="code">
              <el-input class="classInput" maxlength="6" show-word-limit v-model="registerFrom.code"
                @change="verifyCide('1', registerFrom.code)"></el-input>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="联通扩展号" prop="ltCode">
              <el-input class="classInput" maxlength="6" show-word-limit v-model="registerFrom.ltCode"
                @change="verifyCide('2', registerFrom.ltCode)"></el-input>
            </el-form-item>
            <el-form-item v-if="dxFlag" label="电信扩展号" prop="dxCode">
              <el-input class="classInput" maxlength="6" show-word-limit v-model="registerFrom.dxCode"
                @change="verifyCide('3', registerFrom.dxCode)"></el-input>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="企业介绍" prop="introduce">
              <el-input class="classInput" v-model="registerFrom.introduce"></el-input>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="公司详情地址" prop="businessAddress">
              <el-input class="classInput" v-model="registerFrom.businessAddress"></el-input>
            </el-form-item>
            <el-form-item v-if="ydFlag" label="统一社会信用代码" prop="unifySocialCreditCodes">
              <el-input class="classInput" v-model="registerFrom.unifySocialCreditCodes"></el-input>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="办公电话" prop="workPhone">
              <el-input class="classInput" v-model="registerFrom.workPhone"></el-input>
            </el-form-item>
            <el-form-item v-if="ltFlag || dxFlag" label="区域" prop="officeCode">
              <el-select class="classInput" v-model="registerFrom.officeCode" placeholder="请选择区域"
                @change="provinces(registerFrom.officeCode)">
                <el-option v-for="item in office" :key="item.id" :label="item.regionName" :value="item.regionCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="ltFlag || dxFlag" label="省份" prop="province">
              <el-select class="classInput" v-model="registerFrom.province" placeholder="请选择省份" @change="citys">
                <el-option v-for="item in province" :key="item.id" :label="item.provinceName"
                  :value="item.provinceCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="ltFlag || dxFlag" label="城市" prop="city">
              <el-select class="classInput" v-model="registerFrom.city" placeholder="请选择城市" @change="handCity">
                <el-option v-for="item in city" :key="item.id" :label="item.cityName" :value="item.cityCode">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="ltFlag || dxFlag" label="区" prop="area">
              <el-input class="classInput" v-model="registerFrom.area"></el-input>
            </el-form-item>
          </div>
          <div class="title_info">实名主体</div>
          <div>
            <el-form-item label="实名主体" prop="compName">
              <el-select class="classInput" ref="optionRef" v-model="registerFrom.compName" clearable filterable remote
                :remote-method="remoteMethod" :loading="loadingcomp" placeholder="请选择主体名称" @change="bindChange">
                <el-option v-for="(item, index) in compNamelist" :key="index" :label="item.company" :value="item.company +
                  '+' +
                  item.id +
                  '+' +
                  item.principalName +
                  '+' +
                  item.principalIdCard
                  ">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="title_info">
            <!-- <span> 联系人信息 </span> -->
            <span>客户账号: {{ registerFrom.username }}</span>
          </div>
          <div>
            <!-- <el-form-item label="客户账号" prop="username">
                  <el-input class="classInput" readonly v-model="registerFrom.username"></el-input>
                </el-form-item>
                <el-form-item  label=" ">
                 <div> </div>
                </el-form-item> -->
            <!-- <el-form-item label="姓名" prop="legalName">
                  <el-input class="classInput" v-model="registerFrom.legalName"></el-input>
                </el-form-item> -->
            <!-- <el-form-item label="客户联系人" prop="customerContactPerson">
                  <el-input disabled class="classInput" v-model="registerFrom.customerContactPerson"></el-input>
                </el-form-item> -->
            <!-- <el-form-item label="身份证号" prop="legalIdentification">
                  <el-input class="classInput" v-model="registerFrom.legalIdentification"></el-input>
                </el-form-item> -->
            <!-- <el-form-item label="联系人身份证号" prop="contactPersonCard">
                  <el-input disabled class="classInput" v-model="registerFrom.contactPersonCard"></el-input>
                </el-form-item> -->
            <el-form-item label="联系人电话" prop="contactPersonPhone">
              <el-input class="classInput" v-model="registerFrom.contactPersonPhone"></el-input>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="联系人邮箱" prop="contactPersonEmail">
              <el-input class="classInput" v-model="registerFrom.contactPersonEmail"></el-input>
            </el-form-item>
          </div>

          <!-- <el-form-item label="企业ID" prop="compId">
                <el-input class="classInput" disabled v-model="registerFrom.compId"></el-input>
              </el-form-item> -->
          <div class="title_info" v-if="ltFlag || dxFlag">证件信息</div>
          <div>
            <el-form-item v-if="ltFlag || dxFlag" label="营业执照" prop="businessLicense">
              <div style="width: 305px">
                <el-upload :action="action" :headers="token" list-type="picture" :limit="1"
                  :file-list="businessLicenseFile" :on-remove="handleRemove" :before-upload="beforeAvatarUpload"
                  :on-success="handleSuccess">
                  <el-button size="default" type="primary">点击上传</el-button>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="公司logo" prop="serviceIcon">
              <div style="width: 305px">
                <el-upload :action="action" :headers="token" list-type="picture" :limit="2" :file-list="serviceIconFlie"
                  :on-remove="handleRemovepIcon" :before-upload="beforeAvatarUpload" :on-success="handleSuccesspIcon">
                  <el-button size="default" type="primary">点击上传</el-button>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="联系人身份证正面照" prop="contactPersonIdentityFron">
              <div style="width: 305px">
                <el-upload :action="action" :headers="token" list-type="picture" :limit="1" :file-list="PersonFileFront"
                  :on-remove="handleRemovep" :before-upload="beforeAvatarUpload" :on-success="handleSuccessp">
                  <el-button size="default" type="primary">点击上传</el-button>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item v-if="ltFlag" label="联系人身份证背面照" prop="contactPersonIdentityBack">
              <div style="width: 305px">
                <el-upload :action="action" :headers="token" list-type="picture" :limit="1" :file-list="PersonFileBack"
                  :on-remove="handleRemovepback" :before-upload="beforeAvatarUpload" :on-success="handleSuccesspback">
                  <el-button size="default" type="primary">点击上传</el-button>
                </el-upload>
              </div>
            </el-form-item>
            <!-- <el-form-item v-if="ltFlag" label="法人身份证正面照" prop="identificationFrontPic">
                  <div style="width: 305px;">
                    <el-upload :action="action" :headers="token" list-type="picture" :limit="1"
                      :file-list="identificationFrontPicFile" :on-remove="handleRemoveF" :before-upload="beforeAvatarUpload"
                      :on-success="handleSuccessF">
                      <el-button size="default" type="primary">点击上传</el-button>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item v-if="ltFlag" label="法人身份证背面照" prop="identificationBackPic">
                  <div style="width: 305px;">
                    <el-upload :action="action" :headers="token" list-type="picture" :limit="1"
                      :file-list="identificationBackPicFile" :on-remove="handleRemoveB" :before-upload="beforeAvatarUpload"
                      :on-success="handleSuccessB">
                      <el-button size="default" type="primary">点击上传</el-button>
                    </el-upload>
                  </div>
                </el-form-item> -->
          </div>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="register5g(refName)">保存</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog 
      v-model="userVisible" 
      width="900px" 
      :before-close="handleClose"
      class="enterprise-dialog"
      :show-close="false"
    >
      <!-- 自定义头部 -->
      <template #header>
        <div class="dialog-header">
          <div class="header-left">
            <el-icon class="header-icon"><HomeFilled /></el-icon>
            <span class="header-title">新增企业注册</span>
          </div>
          <el-button 
            text 
            class="close-btn"
            @click="closeUserDialog"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div class="enterprise-form-container">
        <el-form 
          :model="userForm" 
          :rules="userRules" 
          ref="userForm" 
          :label-width="120"
          class="enterprise-form"
        >
          <!-- 基本信息卡片 -->
          <div class="form-section">
            <div class="section-header">
              <el-icon class="section-icon"><User /></el-icon>
              <span class="section-title">基本信息</span>
            </div>
            
            <div class="form-grid">
              <el-form-item label="用户名称" prop="username" class="form-item">
                <el-input 
                  v-model="userForm.username" 
                  placeholder="请输入用户名称"
                  class="custom-input"
                >
                  <template #prefix>
                    <el-icon><UserFilled /></el-icon>
                  </template>
                </el-input>
              </el-form-item>
              
              <el-form-item label="主体类型" prop="subjectType" class="form-item">
                <el-select 
                  v-model="userForm.subjectType" 
                  @change="handleSubjectTypeChange" 
                  placeholder="请选择主体类型"
                  class="custom-select"
                >
                  <el-option label="直客" :value="1">
                    <div class="option-item">
                      <span class="option-text">直客</span>
                      <span class="option-desc">直接客户</span>
                    </div>
                  </el-option>
                  <el-option label="非直客" :value="2">
                    <div class="option-item">
                      <span class="option-text">非直客</span>
                      <span class="option-desc">间接客户</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>

          <!-- 主体信息卡片 -->
          <div class="form-section">
            <div class="section-header">
              <el-icon class="section-icon"><OfficeBuilding /></el-icon>
              <span class="section-title">主体信息</span>
              <el-tag 
                :type="userForm.subjectType === 1 ? 'primary' : 'success'" 
                size="small"
                class="section-tag"
              >
                {{ userForm.subjectType === 1 ? '直客模式' : '非直客模式' }}
              </el-tag>
            </div>
            
            <div class="form-grid">
              <!-- 直客显示实名主体 -->
              <el-form-item 
                v-if="userForm.subjectType === 1" 
                label="实名主体" 
                prop="compName" 
                class="form-item full-width"
              >
                <el-select 
                  ref="optionRef" 
                  v-model="userForm.compName" 
                  clearable 
                  filterable 
                  remote
                  allow-create 
                  default-first-option
                  :remote-method="remoteMethod" 
                  :loading="loadingcomp" 
                  @change="handelChange" 
                  placeholder="🔍 请选择或输入实名主体"
                  class="custom-select"
                >
                  <el-option 
                    v-for="(item, index) in compNamelist" 
                    :key="index" 
                    :label="item.company" 
                    :value="item.company + '+' + item.id + '+' + item.principalName + '+' + item.principalIdCard + '+' + (item.qualificationNum || '')"
                  >
                    <div class="company-option">
                      <div class="company-name">{{ item.company }}</div>
                      <div class="company-info">负责人: {{ item.principalName }}</div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              
              <!-- 非直客显示客户名称 -->
              <el-form-item 
                v-if="userForm.subjectType === 2" 
                label="客户名称" 
                prop="customerName" 
                class="form-item full-width"
              >
                <el-select 
                  ref="customerRef" 
                  v-model="userForm.customerName" 
                  clearable 
                  filterable 
                  remote
                  allow-create 
                  default-first-option
                  :remote-method="remoteCustomerMethod" 
                  :loading="loadingCustomer" 
                  @change="handelCustomerChange" 
                  placeholder="🔍 请选择或输入客户名称"
                  class="custom-select"
                >
                  <el-option 
                    v-for="(item, index) in customerNamelist" 
                    :key="index" 
                    :label="item.companyName" 
                    :value="item.companyName + '+' + item.companyNum"
                  >
                    <div class="customer-option">
                      <div class="customer-name">{{ item.companyName }}</div>
                      <!-- <div class="customer-id">ID: {{ item.companyNum }}</div> -->
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="社会信用代码" prop="unifySocialCreditCodes" class="form-item full-width">
                                 <el-input 
                   v-model="userForm.unifySocialCreditCodes"
                   placeholder="选择实名主体或客户后自动填入，也可手动输入" 
                   class="custom-input"
                 >
                  <template #prefix>
                    <el-icon><Document /></el-icon>
                  </template>
                  <template #suffix>
                    <el-tooltip 
                      content="此字段可根据选择的实名主体或客户自动回填，如未回填可手动输入" 
                      placement="top"
                    >
                      <el-icon class="help-icon"><QuestionFilled /></el-icon>
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>
            </div>
          </div>

          <!-- 附件信息卡片 -->
          <div class="form-section">
            <div class="section-header">
              <el-icon class="section-icon"><Folder /></el-icon>
              <span class="section-title">营业执照</span>
              <div class="status-indicator">
                <el-icon 
                  v-if="userForm.businessLicense" 
                  class="status-success"
                ><CircleCheckFilled /></el-icon>
                <el-icon 
                  v-else-if="userForm.subjectType === 1" 
                  class="status-required"
                ><WarningFilled /></el-icon>
                <el-icon 
                  v-else 
                  class="status-optional"
                ><InfoFilled /></el-icon>
              </div>
            </div>
            
            <el-form-item 
              prop="businessLicense" 
              class="upload-form-item"
            >
              <div class="modern-upload-container">
                <div class="upload-area">
                  <el-upload
                    :action="action"
                    :headers="token"
                    :file-list="businessLicenseFileList"
                    :limit="1"
                    list-type="picture-card"
                    :on-success="handleBusinessLicenseSuccess"
                    :on-remove="handleBusinessLicenseRemove"
                    :before-upload="beforeBusinessLicenseUpload"
                    accept="image/*,.pdf"
                    class="custom-upload"
                    drag
                  >
                    <div class="upload-content">
                      <el-icon class="upload-icon"><Upload /></el-icon>
                      <div class="upload-text">点击或拖拽上传</div>
                    </div>
                  </el-upload>
                </div>
                
                <div class="upload-info">
                  <div class="status-message">
                    <el-icon 
                      v-if="userForm.subjectType === 1 && !userForm.businessLicense" 
                      class="status-icon required"
                    ><WarningFilled /></el-icon>
                    <el-icon 
                      v-else-if="userForm.businessLicense" 
                      class="status-icon success"
                    ><CircleCheckFilled /></el-icon>
                    <el-icon 
                      v-else 
                      class="status-icon optional"
                    ><InfoFilled /></el-icon>
                    
                    <span 
                      v-if="userForm.subjectType === 1"
                      :class="['status-text', userForm.businessLicense ? 'success' : 'required']"
                    >
                      {{ userForm.businessLicense ? '✅ 已获取营业执照' : '⚠️ 直客模式默认自动上传营业执照' }}
                    </span>
                    <span v-else class="status-text optional">
                      📎 非直客模式可选择上传营业执照
                    </span>
                  </div>
                  
                  <div class="upload-tips">
                    <div class="tip-item">
                      <el-icon><Picture /></el-icon>
                      <span>支持 JPG、PNG、PDF 格式</span>
                    </div>
                    <div class="tip-item">
                      <el-icon><Files /></el-icon>
                      <span>文件大小不超过 5MB</span>
                    </div>
                    <!-- <div class="tip-item">
                      <el-icon><Lock /></el-icon>
                      <span>文件将加密存储</span>
                    </div> -->
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      
      <!-- 自定义底部 -->
      <template #footer>
        <div class="dialog-footer">
          <el-button 
            @click="closeUserDialog"
            class="cancel-btn"
          >
            取消
          </el-button>
          <el-button 
            type="primary" 
            @click="quiteUser('userForm')"
            class="submit-btn"
          >
            确认创建
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="新增chatbot" v-model="chatbotVisible" width="45%" :before-close="handleClose">
      <el-form :inline="true" :model="chatbotFrom" :rules="rulesc" ref="chatbotFrom" label-width="130px"
        class="demo-chatbotFrom">
        <div>
          <el-form-item label="ChatBot头像" prop="logoUrl">
            <div style="display: flex">
              <div v-if="!logoFlag" style="display: flex; position: relative">
                <img :src="chatbotFrom.logoUrl" alt="" width="150px" height="150px" />
                <el-button style="
                    height: 30px;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    margin-right: -80px;
                  " :icon="ElIconPlus" type="primary" @click="Removec">上传</el-button>
              </div>
              <div v-else>
                <div style="display: flex">
                  <el-upload :action="action" :headers="token" list-type="picture-card" :limit="1"
                    :file-list="fileListc" :before-upload="beforeAvatarUploadc" :on-remove="handleRemovec"
                    :on-success="handleSuccessc">
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </el-upload>
                  <div class="photo_active">
                    <div></div>
                    <div>
                      <p>图片尺寸：400*400！</p>
                      <p>图片大小：不能超过50k！</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <el-form-item label="用户名称" prop="username">
          <el-input disabled :maxlength="20" style="width: 240px" show-word-limit
            v-model="chatbotFrom.username"></el-input>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input disabled :maxlength="20" style="width: 240px" show-word-limit
            v-model="chatbotFrom.customerName"></el-input>
        </el-form-item>
        <el-form-item label="ChatBot名称" prop="chatbotName">
          <template #label>
            <span style="display: flex; align-items: center;">
              <span>ChatBot名称</span>
              <el-tooltip effect="dark" content="针对具备5G消息能力的终端，在终端的消息列表及聊天页面顶端展示" placement="top">
                <!-- <el-icon style="color: #409eff; margin-left: 5px"
                  ><el-icon-info
                /></el-icon> -->
                <el-icon style="color: #409eff; margin-left: 5px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input :maxlength="20" style="width: 240px" show-word-limit v-model="chatbotFrom.chatbotName"></el-input>
        </el-form-item>

        <el-form-item label="签名" prop="autoGraph">
          <template #default:label>
            <span style="display: inline-block">
              <span>签名</span>
              <el-tooltip effect="dark" content="针对回落5G行业消息（标准版）的终端，在每条消息内容前携带此内容" placement="top">
                <!-- <el-icon style="color: #409eff; margin-left: 5px"
                  ><el-icon-info
                /></el-icon> -->
                <el-icon style="color: #409eff; margin-left: 5px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input :maxlength="20" show-word-limit style="width: 240px" placeholder="不能超过20个字符"
            v-model="chatbotFrom.autoGraph"></el-input>
        </el-form-item>
        <el-form-item label="服务描述" prop="description">
          <el-input :maxlength="166" show-word-limit type="textarea" placeholder="请输入服务描述"
            style="width: 240px; height: 80px" v-model="chatbotFrom.description"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input :maxlength="50" show-word-limit style="width: 240px" placeholder="不能超过50个字符"
            v-model="chatbotFrom.email"></el-input>
        </el-form-item>
        <el-form-item label="chatbot主页地址" prop="website">
          <el-input placeholder="请输入chatbot主页地址" style="width: 240px" v-model="chatbotFrom.website"></el-input>
        </el-form-item>
        <el-form-item label="chatbot办公地址" prop="address">
          <el-input placeholder="请输入chatbot办公地址" style="width: 240px" v-model="chatbotFrom.address"></el-input>
        </el-form-item>
        <el-form-item label="经度" prop="longitude">
          <el-input placeholder="请输入经度" style="width: 240px" v-model="chatbotFrom.longitude"></el-input>
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <template #default:label>
            <span style="display: inline-block">
              <span>纬度</span>
              <el-tooltip effect="dark" content="请到相关网站查询您公司所在经纬度，便于支持类似“附近”功能" placement="top">
                <!-- <el-icon style="color: #409eff; margin-left: 5px"
                  ><el-icon-info
                /></el-icon> -->
                <el-icon style="color: #409eff; margin-left: 5px">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </span>
          </template>
          <el-input placeholder="请输入纬度" style="width: 240px" v-model="chatbotFrom.latitude"></el-input>
        </el-form-item>
        <el-form-item label="服务电话" prop="callbackPhone">
          <el-input :maxlength="21" show-word-limit placeholder="请输入服务电话" style="width: 240px"
            v-model="chatbotFrom.callbackPhone"></el-input>
        </el-form-item>
        <el-form-item label="服务条款" prop="terms">
          <el-input placeholder="请输入服务条款" style="width: 240px" v-model="chatbotFrom.terms"></el-input>
        </el-form-item>
        <el-form-item label="营销短信示例" prop="yxSmsExample">
          <el-input type="textarea" maxlength="140" show-word-limit placeholder="请输入营销短信示例" style="width: 240px"
            v-model="chatbotFrom.yxSmsExample"></el-input>
        </el-form-item>
        <el-form-item label="通知短信示例" prop="hySmsExample">
          <el-input type="textarea" maxlength="140" show-word-limit placeholder="请输入通知短信示例" style="width: 240px"
            v-model="chatbotFrom.hySmsExample"></el-input>
        </el-form-item>
        <el-form-item label="附件上传" prop="attachment">
          <el-upload :action="action" :headers="token" :limit="1" :file-list="attachmentList"
            :before-upload="beforeAvatarUploadAtt" :on-remove="handleRemoveAtt" :on-success="handleSuccessAtt" multiple>
            <el-button size="default" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
          </el-upload>
        </el-form-item>
        <el-form-item label="电信5G授权书" prop="authorizationLetterUrl">
          <el-upload :action="action" :headers="token" :limit="1" :file-list="authorizationLetterList"
            :before-upload="beforeAvatarUploadAuthLetter" :on-remove="handleRemoveAuthLetter" :on-success="handleSuccessAuthLetter" multiple>
            <el-button size="default" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">支持上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式文件，且不超过5MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="chatbotVisible = false">取 消</el-button>
          <el-button type="primary" @click="quiteBot('chatbotFrom')">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="一键报备" v-model="registerVisible" width="30%" :before-close="handleClose">
      <el-form :model="registerList" :inline="true" :rules="rules" label-width="70px" ref="registerList"
        class="demo-form-inline">
        <el-form-item label="通道" prop="channelId">
          <el-select v-model="registerList.channelId" placeholder="选择通道" clearable @change="handChannel"
            class="input-w">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="源通道" prop="sourceChannelId">
          <el-select v-model="registerList.sourceChannelId" placeholder="选择通道" clearable class="input-w">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="registerVisible = false">取 消</el-button>
          <el-button type="primary" @click="registerQuite('registerList')">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="批量导入" v-model="importVisible" width="30%" :before-close="handleClose">
      <el-form :model="importList" :rules="rules" label-width="90px" ref="importList" class="demo-form-inline">
        <el-form-item label="通道" prop="channelJsonStr">
          <el-select v-model="importList.channelJsonStr" placeholder="选择通道" multiple clearable
            @change="handbatchChannel" style="width: 300px">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="实名主体" prop="compName">
              <el-select style="width: 300px" ref="optionRef" v-model="importList.compName" clearable filterable remote
                :remote-method="remoteMethod" :loading="loadingcomp" @change="handelChange1" placeholder="请选择主体名称">
                <el-option v-for="(item, index) in compNamelist" :key="index" :label="item.company"
                  :value="item.company + '+' + item.id">
                </el-option>
              </el-select>
            </el-form-item> -->
        <el-form-item label="导入文件" prop="file">
          <el-upload v-bind:data="{
            FoldPath: '上传目录',
            SecretKey: '安全验证',
            channelJsonStr: JSON.stringify(this.channelJson),
            // compName: importList.compName,
            // compId: importList.compId
          }" :action="importAction" :headers="token" :limit="1" :file-list="importFileList" :before-upload="(file) => {
            return beforeAvatarUploadAtt(file, 'import')
          }
            " :on-remove="(file) => {
              return handleRemoveAtt(file, 'import')
            }
              " :on-success="(response, fileList) => {
                return handleSuccessAtt(response, fileList, 'import')
              }
                " multiple>
            <el-button size="default" type="primary">点击上传</el-button>

            <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
          </el-upload>
          <a href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/f0a37bfd2fa5a2618d82270a309e1785"
            target="_blank" rel="noopener noreferrer">模板下载</a>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importVisible = false">取 消</el-button>
          <!-- <el-button type="primary" @click="handelbatchImport('importList')"
              >确 定</el-button
            > -->
        </span>
      </template>
    </el-dialog>
    <el-dialog title="通道同步" v-model="channelSyncVisible" width="30%" :before-close="handleClose">
      <el-form :model="channelForm" :rules="rules" label-width="90px" ref="channelForm" class="demo-form-inline">
        <el-form-item label="通道" prop="channelJsonStr">
          <el-select v-model="channelForm.channelJsonStr" placeholder="选择通道" multiple clearable
            @change="handbatchChannelSync" style="width: 300px">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="导入文件" prop="file">
          <el-upload v-bind:data="{
            FoldPath: '上传目录',
            SecretKey: '安全验证',
            channels: JSON.stringify(this.channelJson),
            // compName: importList.compName,
            // compId: importList.compId
          }" :action="syncAction" :headers="token" :limit="1" :file-list="syncFileList" :before-upload="(file) => {
            return beforeAvatarUploadAtt(file, 'import')
          }
            " :on-remove="(file) => {
              return handleRemoveAtt(file, 'sync')
            }
              " :on-success="(response, fileList) => {
                return handleSuccessAtt(response, fileList, 'sync')
              }
                " multiple>
            <el-button size="default" type="primary">点击上传</el-button>

            <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
          </el-upload>
          <!-- <a href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/f0a37bfd2fa5a2618d82270a309e1785"
            target="_blank" rel="noopener noreferrer">模板下载</a> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="channelSyncVisible = false">取 消</el-button>
          <!-- <el-button type="primary" @click="handelbatchImport('importList')"
              >确 定</el-button
            > -->
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
import { 
  // <el-icon><CircleClose /></el-icon>
  HomeFilled, Close, CircleClose, User, UserFilled, Flag, House, OfficeBuilding,
  Document, QuestionFilled, Folder, CircleCheckFilled, WarningFilled,
  InfoFilled, Upload, Picture, Files, Lock, Check, Plus
} from '@element-plus/icons-vue'
export default {
  data() {
    // var id = sessionStorage.getItem('id')
    var coderules = (rule, value, callback) => {
      if (value) {
        let reg = /[^\d]/g
        if (reg.test(value)) {
          callback(new Error('输入格式错误'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入扩展码！'))
      }
    }
    var longitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/
        if (!reg.test(value)) {
          callback(new Error('小数点后最多七位，请重新输入'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入经度'))
      }
    }
    var latitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/
        if (!reg.test(value)) {
          callback(new Error('小数点后最多七位，请重新输入'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请输入纬度'))
      }
    }
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      fileList: [],
      fileListc: [],
      importFileList: [],
      batchFileList: [],
      PersonFileFront: [],
      PersonFileBack: [],
      businessLicenseFile: [],
      shuomingFileList: [],
      // identificationFrontPicFile: [],
      // identificationBackPicFile: [],
      customerIdList: [],
      serviceIconFlie: [],
      syncFileList: [],
      action: window.path.cpus + 'v3/file/uploadFile',
      importAction: window.path.fiveWeb + '/operator/customer/batchAddCustomer',
      batchAction: window.path.fiveWeb + '/operator/customer/batchUpload',
      syncAction: window.path.fiveWeb + '/operator/customer/batchDeclareChannel',
      shuomingAction: window.path.fiveWeb + "/operator/auto/importCusotmerInfo",
      token: {},
      status: '',
      dialogVisible: false,
      userVisible: false,
      registerVisible: false,
      chatbotVisible: false,
      importVisible: false,
      channelSyncVisible: false,
      codeFlag: true,
      title: '',
      refName: 'registerFrom',
      city: [],
      province: [],
      office: [],
      customerIds: '',
      industrys: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      ruleForm: {
        customerName: '',
        username: '',
        channelId: '',
        customerId: '',
        createdUser: '',
        channelStatusArr: [],
        time1: [],
        createdTimeStart: '',
        createdTimeEnd: '',
        currentPage: 1,
        pageSize: 10,
      },
      userForm: {
        username: '',
        customerName: '',
        compId: '',
        compName: '',
        customerContactPerson: '',
        contactPersonCard: '',
        unifySocialCreditCodes: '',
        subjectType: 1, // 1是直客，2是非直客
        businessLicense: '', // 营业执照附件
      },
      tabelAlllist: {
        customerName: '',
        username: '',
        channelId: '',
        customerId: '',
        createdUser: '',
        channelStatusArr: [],
        time1: [],
        createdTimeStart: '',
        createdTimeEnd: '',
        currentPage: 1,
        pageSize: 10,
      },
      checkList: '',
      channel: [],
      ydFlag: true,
      ltFlag: false,
      dxFlag: false,
      operationType: ['yd'],
      registerFrom: {
        compId: '',
        compName: '',
        operationSupport: '',
        username: '',
        customerId: '',
        customerStatus: '', //状态
        customerName: '', //客户名称
        // customerContactPerson: "", //客户联系人
        contactPersonPhone: '', //联系人电话
        contactPersonEmail: '', //联系人邮箱
        remarkText: '', //备注
        officeCode: '', //区域
        code: '', //移动扩展码
        dxCode: '', //电信扩展码
        ltCode: '', //联通扩展码
        audit: '', //初审通过与不通过
        province: '', //省
        area: '', //区
        businessAddress: '', //公司详情地址
        businessLicense: '', //企业营业执照图片
        city: '', //市
        contactPersonCard: '', //联系人身份证号
        // identificationBackPic: "", //法人身份证背面照
        // identificationFrontPic: "", //法人身份证正面照
        introduce: '', //企业介绍
        // legalIdentification: "", //法人身份证号
        // legalName: "", //法人名称
        contactPersonIdentity: '', //联系人身份证
        serviceIcon: '', //公司logo
        workPhone: '', //办公电话
        contactPersonIdentityFron: '',
        contactPersonIdentityBack: '',
        unifySocialCreditCodes: '', //统一社会信用代码
      },
      userRules: {
        username: [
          { required: true, message: '用户名称不可为空', trigger: 'change' },
        ],
        subjectType: [
          { required: true, message: '请选择主体类型', trigger: 'change' },
        ],
        customerName: [
          { 
            required: true, 
            message: '客户名称不可为空', 
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.userForm.subjectType === 2 && !value) {
                callback(new Error('客户名称不可为空'))
              } else {
                callback()
              }
            }
          },
        ],
        compName: [
          { 
            required: true, 
            message: '请选择实名主体', 
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.userForm.subjectType === 1 && !value) {
                callback(new Error('请选择实名主体'))
              } else {
                callback()
              }
            }
          },
        ],
        unifySocialCreditCodes: [
          { required: true, message: '社会信用代码不可为空', trigger: 'change' },
        ],
        // businessLicense: [
        //   { 
        //     required: true, 
        //     message: '请上传营业执照', 
        //     trigger: 'change',
        //     validator: (rule, value, callback) => {
        //       if (this.userForm.subjectType === 1 && !this.userForm.businessLicense) {
        //         callback(new Error('直客模式需要上传营业执照'))
        //       } else {
        //         callback()
        //       }
        //     }
        //   },
        // ],
      },
      logoFlag: true,
      chatbotFrom: {
        username: '',
        customerName: '',
        userId: '',
        customerId: '',
        chatbotName: '', //chatbot名称
        autoGraph: '', //签名
        bubbleColor: '#FFFFFF', //气泡颜色
        // ext: "", //扩展号
        // industry: "", //实际下发行业
        // category: "", //行业类型
        callbackPhone: '', //服务电话
        // cityCode: "", //地市编码
        // officeCode: "", //区编码
        // provinceCode: "", //省份编码
        // ext: "", //扩展号
        logoUrl: '', //头像
        email: '', //邮箱
        description: '', //描述信息
        terms: '', //服务条款
        website: '', //chatbot主页地址
        longitude: '', //经度
        latitude: '', //纬度
        address: '', //chatbot办公地址
        attachment: '',
        authorizationLetterUrl: '', //电信5G授权书
        yxSmsExample: '', //营销短信示例
        hySmsExample: '', //通知短信示例
        // debugWhiteAddress: "", //调试白名单
        statusOf: '新增chatbot信息',
      },
      attachmentList: [],
      authorizationLetterList: [], // 电信5G授权书文件列表
      rulesc: {
        chatbotName: [
          { required: true, message: '请输入chatbot名称', trigger: 'blur' },
        ],
        yxSmsExample: [
          { required: true, message: '请输入营销短信示例', trigger: 'blur' },
        ],
        hySmsExample: [
          { required: true, message: '请输入通知短信示例', trigger: 'blur' },
        ],
        logoUrl: [
          { required: true, message: '请上传chatbot头像', trigger: 'blur' },
        ],
        // attachment: [
        //   { required: true, message: "请上传附件", trigger: "blur" },
        // ],
        // unifySocialCreditCodes: [
        //   { required: true, message: "统一社会信用代码不能为空", trigger: "blur" },
        // ],
        // industry: [
        //   { required: true, message: "请选择实际下发行业", trigger: "blur" },
        // ],
        // bubbleColor: [
        //   { required: true, message: "请选择气泡颜色", trigger: "blur" },
        // ],
        autoGraph: [{ required: true, message: '请输入签名', trigger: 'blur' }],
        // callbackPhone: [
        //   { required: true, message: "请输入服务电话", trigger: "blur" },
        // ],
        // cityCode: [
        //   { required: true, message: "请选择地市编码", trigger: "blur" },
        // ],
        // officeCode: [
        //   { required: true, message: "请选择区域", trigger: "blur" },
        // ],
        // terms: [{ required: true, message: "请输入服务条款", trigger: "blur" }],
        // website: [
        //   { required: true, message: "请输入chatbot主页地址", trigger: "blur" },
        // ],
        // longitude: [{ required: true, validator: longitude, trigger: "blur" }],
        // latitude: [{ required: true, validator: latitude, trigger: "blur" }],
        // address: [
        //   { required: true, message: "chatbot办公地址", trigger: "blur" },
        // ],
        // provinceCode: [
        //   { required: true, message: "请选择省份编码", trigger: "blur" },
        // ],
        // ext: [{ required: true, validator: coderules, trigger: "blur" }],
        // email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
        description: [
          { required: true, message: '描述信息不能为空', trigger: 'blur' },
        ],
        debugWhiteAddress: [
          { required: true, message: '调试白名单不能为空', trigger: 'blur' },
        ],
      },
      rules: {
        code: [
          { required: true, validator: coderules, trigger: 'change' },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        ltCode: [
          { required: true, validator: coderules, trigger: 'change' },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        dxCode: [
          { required: true, validator: coderules, trigger: 'change' },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        channelId: [
          { required: true, message: '请选择通道', trigger: 'change' },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        sourceChannelId: [
          { required: true, message: '请选择原通道', trigger: 'change' },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        customerName: [
          { required: true, message: '客户名称不可为空', trigger: 'change' },
        ],
        customerContactPerson: [
          { required: true, message: '客户联系人不可为空', trigger: 'change' },
        ],
        contactPersonCard: [
          {
            required: true,
            message: '联系人身份证号不可为空',
            trigger: 'change',
          },
        ],
        contactPersonPhone: [
          { required: true, message: '联系人电话不可为空', trigger: 'change' },
        ],
        // officeCode: [
        //   { required: true, message: "请选择区域", trigger: "change" },
        // ],
        province: [
          { required: true, message: '请选择省份', trigger: 'change' },
        ],
        city: [{ required: true, message: '请选择市区', trigger: 'change' }],
        area: [{ required: true, message: '请输入区', trigger: 'change' }],
        businessAddress: [
          {
            required: true,
            message: '公司详情地址不能为空',
            trigger: 'change',
          },
        ],
        introduce: [
          { required: true, message: '企业介绍不能为空', trigger: 'change' },
        ],
        // legalName: [
        //   { required: true, message: "法人名称不能为空", trigger: "change" },
        // ],
        unifySocialCreditCodes: [
          {
            required: true,
            message: '统一社会信用代码不能为空',
            trigger: 'blur',
          },
        ],
        // legalIdentification: [
        //   {
        //     required: true,
        //     message: "法人身份证号不能为空",
        //     trigger: "change",
        //   },
        // ],
        contactPersonEmail: [
          { required: true, message: '联系人邮箱不能为空', trigger: 'change' },
        ],
        workPhone: [
          { required: true, message: '办公电话不能为空', trigger: 'change' },
        ],
        contactPersonIdentityFron: [
          {
            required: true,
            message: '请上传联系人身份证正面照',
            trigger: 'change',
          },
        ],
        contactPersonIdentityBack: [
          {
            required: true,
            message: '请上传联系人身份证反面照',
            trigger: 'change',
          },
        ],
        businessLicense: [
          { required: true, message: '请上传企业营业执照', trigger: 'change' },
        ],
        // identificationBackPic: [
        //   {
        //     required: true,
        //     message: "请上传法人身份证背面照",
        //     trigger: "change",
        //   },
        // ],
        identificationFrontPic: [
          {
            required: true,
            message: '请上传法人身份证正面照',
            trigger: 'change',
          },
        ],
        serviceIcon: [
          {
            required: true,
            message: '请上传公司logo',
            trigger: 'change',
          },
        ],
        compId: [
          { required: true, message: '请选择主体名称', trigger: 'change' },
        ],
        // compName: [
        //   { required: true, message: '请选择主体名称', trigger: 'change' },
        // ],
        // channelJsonStr: [
        //   {
        //     required: true,
        //     message: "请选择通道",
        //     trigger: "change",
        //   },
        // ],
      },
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      registerList: {
        channelId: '',
        channelName: '',
        channelExtCode: '', //通道扩展码
        customerIdList: [],
        operator: '',
        sourceChannelId: '',
      },
      importList: {
        channelJsonStr: [],
        file: '',
        compId: '',
        compName: '',
      },
      channelForm: {
        channelJsonStr: [],
        file: '',
        compId: '',
        compName: '',
      },
      channelJson: [],
      compNamelist: [],
      loadingcomp: false,
      customerNamelist: [], // 客户名称列表
      loadingCustomer: false, // 客户名称加载状态
      businessLicenseFileList: [], // 营业执照附件列表
      codeFlag: true,
      ElIconPlus: "",
    }
  },
  components: {
    Tooltip,
    CopyTemp,
    // 图标组件
    HomeFilled,
    Close,
    CircleClose,
    User,
    UserFilled,
    Flag,
    OfficeBuilding,
    House,
    Document,
    QuestionFilled,
    Folder,
    CircleCheckFilled,
    WarningFilled,
    InfoFilled,
    Upload,
    Picture,
    Files,
    Lock,
    Check,
    Plus,
    // ElIconLxEmoji,
    // ElIconTickets,
    // ElIconDelete,
    // ElIconPlus,
    // ElIconInfo,
  },
  name: 'AuditCertification5G',
  created() {
    // if (this.$route.query.username) {
    //   this.ruleForm.username = this.$route.query.username;
    //   Object.assign(this.tabelAlllist, this.ruleForm);
    // }
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getchatList()
      this.getIndustrys()
      this.channel5GList()
      this.region()
      this.token = {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      }
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        // if (this.$route.query.username) {
        //   this.ruleForm.username = this.$route.query.username;
        //   Object.assign(this.tabelAlllist, this.ruleForm);
        // }
        this.getchatList()
        this.getIndustrys()
        this.channel5GList()
        this.region()
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
              })
            }
            // this.services = res.data;
          }
        )
      } catch (error) {
        console.log(error, 'error')
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      console.log(query, 'query')
      if (query !== '') {
        this.loadingcomp = true
        this.searchAccount(query)
        this.loadingcomp = false
      } else {
        this.compNamelist = []
        this.searchAccount()
      }
    },
    bindChange(val) {
      if (val) {
        this.registerFrom.compName = val.split('+')[0]
        this.registerFrom.compId = val.split('+')[1]
        // this.registerFrom.customerContactPerson = val.split('+')[2]
        // this.registerFrom.contactPersonCard = val.split('+')[3]
        // if (val.split('+')[1] != 'null') {
        //   this.registerFrom.compId = val.split('+')[1]
        // } else {
        //   this.registerFrom.compId = ''
        // }
      } else {
        this.searchAccount()
        this.registerFrom.compName = ''
        this.registerFrom.compId = ''
      }
    },
    handelChange(val) {
      if (val) {
        // 检查是否是从选项中选择的（包含+分隔符）还是手动输入的
        if (val.includes('+')) {
          // 从下拉选项中选择
          this.userForm.compName = val.split('+')[0]
          this.userForm.compId = val.split('+')[1]
          this.userForm.customerContactPerson = val.split('+')[2]
          this.userForm.contactPersonCard = val.split('+')[3]
          // 自动回填社会信用代码（使用qualificationNum字段）
          this.userForm.unifySocialCreditCodes = val.split('+')[4] || ''
          
          // 直客模式：获取实名主体的营业执照
          if (this.userForm.subjectType === 1) {
            this.userForm.customerName = val.split('+')[0]
            // 从选中的实名主体数据中获取营业执照
            const selectedCompany = this.compNamelist.find(item => item.id == val.split('+')[1])
            if (selectedCompany && selectedCompany.businessLicense) {
              this.userForm.businessLicenseFastDfs = window.path.imgU + selectedCompany.businessLicense
              // 如果有现有附件，显示在文件列表中
              this.businessLicenseFileList = [{
                name: '营业执照',
                url: window.path.imgU + selectedCompany.businessLicense
              }]
            }
          }
        } else {
          // 手动输入的实名主体
          this.userForm.compName = val
          this.userForm.compId = ''
          this.userForm.customerContactPerson = ''
          this.userForm.contactPersonCard = ''
          // 手动输入时不清空社会信用代码，允许用户自己输入
          // 直客：实名主体赋值到客户名称
          if (this.userForm.subjectType === 1) {
            this.userForm.customerName = val
            // 手动输入时清空营业执照，需要用户重新上传
            this.userForm.businessLicense = ''
            this.businessLicenseFileList = []
          }
        }
      } else {
        this.searchAccount()
        this.userForm.compName = ''
        this.userForm.compId = ''
        this.userForm.unifySocialCreditCodes = ''
        this.userForm.businessLicense = ''
        this.userForm.businessLicenseFastDfs = ''
        this.businessLicenseFileList = []
        if (this.userForm.subjectType === 1) {
          this.userForm.customerName = ''
        }
      }
    },
    handelChange1(val) {
      if (val) {
        this.importList.compName = val.split('+')[0]
        if (val.split('+')[1] != 'null') {
          this.importList.compId = val.split('+')[1]
        } else {
          this.importList.compId = ''
        }
      } else {
        this.searchAccount()
        this.importList.compName = ''
        this.importList.compId = ''
      }
    },
    // 处理主体类型变化
    handleSubjectTypeChange(val) {
      // 清空相关字段
      this.userForm.compName = ''
      this.userForm.compId = ''
      this.userForm.customerName = ''
      this.userForm.unifySocialCreditCodes = ''
      this.userForm.customerContactPerson = ''
      this.userForm.contactPersonCard = ''
      this.userForm.businessLicense = ''
      this.userForm.businessLicenseFastDfs = ''
      
      // 清空列表数据
      this.compNamelist = []
      this.customerNamelist = []
      this.businessLicenseFileList = []
    },
    // 客户名称远程搜索
    remoteCustomerMethod(query) {
      console.log(query, 'customer query')
      if (query !== '') {
        this.loadingCustomer = true
        this.searchCustomer(query)
        this.loadingCustomer = false
      } else {
        this.customerNamelist = []
        this.searchCustomer()
      }
    },
    // 搜索客户名称
    searchCustomer(val) {
      try {
        // 使用新的客户搜索接口
        window.api.get(
          window.path.fiveWeb + `/operator/customer/queryCompanyList?companyName=${val || ''}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.customerNamelist = res.data || []
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
              })
            }
          }
        )
      } catch (error) {
        console.log(error, 'customer search error')
      }
    },
    // 处理客户名称选择变化
    handelCustomerChange(val) {
      if (val) {
        // 检查是否是从选项中选择的（包含+分隔符）还是手动输入的
        if (val.includes('+')) {
          // 从下拉选项中选择
          this.userForm.customerName = val.split('+')[0]
          // 自动回填社会信用代码（使用companyNum字段）
          this.userForm.unifySocialCreditCodes = val.split('+')[1] || ''
        } else {
          // 手动输入的客户名称
          this.userForm.customerName = val
          // 手动输入时不清空社会信用代码，允许用户自己输入
        }
      } else {
        this.searchCustomer()
        this.userForm.customerName = ''
        this.userForm.unifySocialCreditCodes = ''
      }
    },
    // 营业执照上传成功处理
    handleBusinessLicenseSuccess(res, file) {
      if (res.code == 200) {
        if(this.userForm.subjectType == 1){
          this.userForm.businessLicenseFastDfs = res.data
        } else {
          this.userForm.businessLicense = res.data
        }
        this.$message.success('营业执照上传成功')
      } else {
        this.$message.error(res.msg || '上传失败')
      }
    },
    // 营业执照移除处理
    handleBusinessLicenseRemove(file, fileList) {
      if(this.userForm.subjectType == 1){
        this.userForm.businessLicenseFastDfs = ''
      } else {
        this.userForm.businessLicense = ''
      }
      this.businessLicenseFileList = []
    },
    // 营业执照上传前验证
    beforeBusinessLicenseUpload(file) {
      const isValidType = file.type.startsWith('image/') || file.type === 'application/pdf'
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('只支持上传图片或PDF格式的文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      return true
    },
    // handleCopy(name, event) {
    //   clip(name, event)
    // },
    /**获取通道*/
    getchatList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.fiveWeb + '/operator/customer/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    /**获取行业类别列表*/
    getIndustrys() {
      window.api.get(
        window.path.omcs + 'operatingindustrycategories/selectAllIndustry',
        {},
        (res) => {
          this.industrys = res.data
        }
      )
    },
    /**获取通道*/
    channel5GList() {
      window.api.get(
        window.path.omcs + 'operatingchannelinfo/channel5GList',
        {},
        (res) => {
          this.channel = res.data
        }
      )
    },
    handChannel(e) {
      if (e) {
        this.channel.forEach((item) => {
          if (e == item.channelCode) {
            this.registerList.channelName = item.channelName
            this.registerList.channelExtCode = item.spPortExtend
            this.registerList.operator = item.operatorsType
          }
        })
      } else {
        this.registerList.channelName = ''
        this.registerList.operator = ''
        this.registerList.channelExtCode = ''
      }
    },
    handbatchChannel(e) {
      let arr = []
      arr = this.channel.map((item) => {
        return {
          channelId: item.channelCode,
          channelName: item.channelName,
          channelJsonStr: item.spPortExtend,
          channelOperator: item.operatorsType,
        }
      })
      function getSame(arr1, arr2) {
        return [...new Set(arr1)].filter((item) =>
          arr2.includes(item.channelId)
        )
      }
      this.channelJson = getSame(arr, e)
    },
    handbatchChannelSync(e) {
      let arr = []
      arr = this.channel.map((item) => {
        return {
          channelId: item.channelCode,
          channelName: item.channelName,
          // channelJsonStr: item.spPortExtend,
          channelOperator: item.operatorsType,
        }
      })
      function getSame(arr1, arr2) {
        return [...new Set(arr1)].filter((item) =>
          arr2.includes(item.channelId)
        )
      }
      this.channelJson = getSame(arr, e)
    },
    registerQuite(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.registerList.customerIdList = this.customerIdList
          if (
            this.registerList.channelId == this.registerList.sourceChannelId
          ) {
            this.$message({
              message: '请勿同通道操作！',
              type: 'warning',
            })
          } else {
            this.$confirms.confirmation(
              'post',
              '是否确认一键报备',
              window.path.fiveWeb + '/operator/customer/convenientAddChannel',
              this.registerList,
              (res) => {
                if (res.code == 200) {
                  this.getchatList()
                  this.customerIdList = []
                  this.registerVisible = false
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'error',
                  // })
                }
              }
            )
          }
        }
      })
    },
    handCity(code) {
      this.registerFrom.city = code
        ? this.city.find((ele) => ele.cityCode == code).cityName
        : ''
    },
    citys(code) {
      let name = code
        ? this.province.find((ele) => ele.provinceCode == code).provinceName
        : ''
      // console.log(name,'name');
      window.api.get(window.path.fiveWeb + '/city/list/' + code, {}, (res) => {
        this.city = res.data
        this.registerFrom.province = name
      })
    },
    provinces(code) {
      window.api.get(
        window.path.fiveWeb + '/province/list?regionCode=' + code,
        {
          //   regionCode:this.ruleForm.officeCode
        },
        (res) => {
          this.province = res.data
        }
      )
    },
    region() {
      window.api.post(
        window.path.fiveWeb + '/region/page',
        {
          currentPage: 1,
          pageSize: 10,
        },
        (res) => {
          this.office = res.data.records
        }
      )
    },
    addUser() {
      this.clearUserFormCache() // 打开弹窗前先清除缓存
      this.userVisible = true
    },
    allBacth() {
      this.registerVisible = true
    },
    batchImport() {
      this.importVisible = true
    },
    channelSync() {
      this.channelSyncVisible = true
    },
    handleSelectionChange(row) {
      if (row.records.length > 0) {
        let arr = []
        row.records.forEach(item => {
          arr.push(item.customerId)
        })
        this.customerIdList = arr
      } else {
        this.customerIdList = []
      }
    },
    sumbitFr() {
      window.api.post(
        window.path.fiveWeb + '/operator/customer/register',
        this.userForm,
        (res) => {
          if ((res.code == 200)) {
            this.getchatList()
            this.closeUserDialog() // 使用专门的关闭方法清除缓存
            this.$message({
              message: '企业注册成功',
              type: 'success',
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        }
      )
    },
    quiteUser(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.fiveWeb + '/operator/customer/exits',
            {
              customerName: this.userForm.customerName,
            },
            (res) => {
              if (res.code == 200) {
                if (res.data == '1') {
                  this.$confirm('客户名称存在重复，确认开启5G消息?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                  })
                    .then(() => {
                      this.sumbitFr()
                    })
                    .catch(() => {
                      this.$message({
                        type: 'info',
                        message: '已取消操作',
                      })
                    })
                } else {
                  this.$confirm('确认开启5G消息?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                  })
                    .then(() => {
                      this.sumbitFr()
                    })
                    .catch(() => {
                      this.$message({
                        type: 'info',
                        message: '已取消操作',
                      })
                    })
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
          // this.$confirms.confirmation(
          //   "post",
          //   "确认开启5G消息",
          //   window.path.fiveWeb + "/operator/customer/exits",
          //   {
          //     customerName:this.userForm.customerName
          //   },
          //   (res) => {
          //     if (res.code == 200) {
          //       if(res.data == '1'){

          //       }
          //       // this.getchatList();
          //       // this.userVisible = false;
          //     } else {
          //       this.$message({
          //         message: res.msg,
          //         type: "error",
          //       });
          //     }
          //   }
          // );
        }
      })
    },
    timeD(e) {
      if (e) {
        this.ruleForm.createdTimeStart = moment(this.ruleForm.time1[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.ruleForm.createdTimeEnd = moment(this.ruleForm.time1[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.ruleForm.createdTimeStart = ''
        this.ruleForm.createdTimeEnd = ''
      }
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getchatList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getchatList()
    },
    changeOperation(val) {
      if (val.indexOf('yd') == -1) {
        this.ydFlag = false
      } else {
        this.ydFlag = true
      }
      if (val.indexOf('lt') == -1) {
        this.ltFlag = false
      } else {
        this.ltFlag = true
      }
      if (val.indexOf('dx') == -1) {
        this.dxFlag = false
      } else {
        this.dxFlag = true
      }
    },
    handOperatorsType(val) {
      this.changeOperation(val)
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.ruleForm.username = ''
      this.ruleForm.createdTimeStart = ''
      this.ruleForm.createdTimeEnd = ''
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getchatList()
    },
    time1(val) {
      this.registerFrom.contractValidDate = moment(
        this.registerFrom.contractValidDate
      ).format('YYYY-MM-DD')
    },
    time2(val) {
      this.registerFrom.contractInvalidDate = moment(
        this.registerFrom.contractInvalidDate
      ).format('YYYY-MM-DD')
    },
    time3(val) {
      this.registerFrom.contractRenewDate = moment(
        this.registerFrom.contractRenewDate
      ).format('YYYY-MM-DD')
    },
    chatbotAdd() {
      this.dialogVisible = true
      this.title = '5G消息注册'
    },
    // handelchatName(val) {
    //   this.chatbotFrom.autoGraph = val;
    // },
    addChatbot(row) {
      this.chatbotVisible = true
      this.chatbotFrom.username = row.username
      this.chatbotFrom.userId = row.userId
      this.chatbotFrom.customerName = row.customerName
      this.chatbotFrom.customerId = row.customerId
    },
    quiteBot(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '确认添加chatbot！',
            window.path.fiveWeb + '/operator/chatbot/add',
            this.chatbotFrom,
            (res) => {
              if (res.code == 200) {
                this.getchatList()
                this.chatbotVisible = false
                this.$router.push({
                  path: '/chatbotAudit',
                })
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        }
      })
    },
    // handelbatchImport(formop) {
    //   this.$refs[formop].validate((valid) => {
    //     if (valid) {
    //       this.$confirms.confirmation(
    //         "post",
    //         "确认批量导入！",
    //         window.path.fiveWeb + "/operator/customer/batchAddCustomer",
    //         {
    //           channelJsonStr: JSON.stringify(this.channelJson),
    //           file: this.importList.file,
    //         },
    //         (res) => {
    //           if (res.code == 200) {
    //             // this.getchatList();
    //             this.importVisible = false;
    //           } else {
    //             this.$message({
    //               message: res.msg,
    //               type: "error",
    //             });
    //           }
    //         }
    //       );
    //     }
    //   });
    // },
    FgRegister(row) {
      localStorage.setItem('customerList', JSON.stringify(row))
      this.$router.push({
        path: '/5gChannel',
        query: { customerId: row.customerId, username: row.username },
      })
    },
    delCustomer(row) {
      this.$confirms.confirmation(
        'delete',
        '是否确认删除企业？',
        window.path.fiveWeb + '/operator/customer/deleteById/' + row.customerId,
        {},
        (res) => {
          if (res.code == 200) {
            this.getchatList()
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    verifyCide(opeartion, val) {
      console.log(opeartion, 'opeartion')
      console.log(val, 'val')
      window.api.post(
        window.path.fiveWeb + '/operator/customer/codeNotExits',
        {
          code: val,
          id: this.customerIds,
          channelOperator: opeartion,
        },
        (res) => {
          if (res.data) {
            this.codeFlag = true
          } else {
            this.codeFlag = false
            this.$message.error('扩展码已经存在')
          }
        }
      )
    },
    register5g(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.codeFlag) {
            this.registerFrom.operationSupport = this.operationType.join(',')
            this.registerFrom.contactPersonIdentity =
              this.registerFrom.contactPersonIdentityFron +
              ',' +
              this.registerFrom.contactPersonIdentityBack
            this.$confirms.confirmation(
              'put',
              '同步更新客户通道信息',
              window.path.fiveWeb + '/operator/customer',
              this.registerFrom,
              (res) => {
                if (res.code == 200) {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "success",
                  // });
                  this.getchatList()
                  this.dialogVisible = false
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'error',
                  // })
                }
              }
            )
          } else {
            this.$message.error('扩展码已经存在')
          }

          // window.api.post(
          //   window.path.fiveWeb + "/operator/customer/codeNotExits",
          //   {
          //     code: this.registerFrom.code,
          //     id: this.customerIds,
          //   },
          //   (res) => {
          //     if (res.data) {
          //     } else {
          //       this.$message.error("扩展码已经存在");
          //     }
          //   }
          // );
        }
      })
    },
    // 清除企业注册表单缓存
    clearUserFormCache() {
      // 重置表单数据
      this.userForm = {
        username: '',
        customerName: '',
        compId: '',
        compName: '',
        customerContactPerson: '',
        contactPersonCard: '',
        unifySocialCreditCodes: '',
        subjectType: 1, // 1是直客，2是非直客
        businessLicense: '', // 营业执照附件
        businessLicenseFastDfs: '', // 营业执照附件
      }
      
      // 清空下拉选择框的数据列表
      this.compNamelist = []
      this.customerNamelist = []
      this.businessLicenseFileList = []
      
      // 清空加载状态
      this.loadingcomp = false
      this.loadingCustomer = false
      
      // 重置表单验证状态
      if (this.$refs.userForm) {
        this.$refs.userForm.clearValidate()
      }
    },
    // 关闭企业注册弹窗
    closeUserDialog() {
      this.clearUserFormCache()
      this.userVisible = false
    },
    handleClose() {
      this.dialogVisible = false
      this.chatbotVisible = false
      this.closeUserDialog() // 使用专门的关闭方法
      this.registerVisible = false
      this.importVisible = false
      this.channelSyncVisible = false
    },
    checked(row) {
      this.dialogVisible = true
      Object.assign(this.registerFrom, row)
      if (this.registerFrom.operationSupport) {
        this.operationType = this.registerFrom.operationSupport.split(',')
      }
      this.changeOperation(this.operationType)
      if (this.registerFrom.contactPersonIdentity) {
        let list = this.registerFrom.contactPersonIdentity.split(',')
        this.registerFrom.contactPersonIdentityFron = list[0]
        this.registerFrom.contactPersonIdentityBack = list[1]
        //  console.log(list,list);
        let personList1 = {
          name: '01',
          url: this.registerFrom.contactPersonIdentityFron,
        }
        let personList2 = {
          name: '02',
          url: this.registerFrom.contactPersonIdentityBack,
        }
        this.PersonFileFront = [personList1]
        this.PersonFileBack = [personList2]
      }
      if (this.registerFrom.businessLicense) {
        this.businessLicenseFile = [
          { name: '营业执照', url: this.registerFrom.businessLicense },
        ]
      }
      // if (this.registerFrom.identificationFrontPic) {
      //   this.identificationFrontPicFile = [
      //     {
      //       name: "身份证正面照",
      //       url: this.registerFrom.identificationFrontPic,
      //     },
      //   ];
      // }
      // if (this.registerFrom.identificationBackPic) {
      //   this.identificationBackPicFile = [
      //     {
      //       name: "身份证反面照",
      //       url: this.registerFrom.identificationBackPic,
      //     },
      //   ];
      // }
      if (this.registerFrom.serviceIcon) {
        this.serviceIconFlie = [
          {
            name: '公司logo',
            url: this.registerFrom.serviceIcon,
          },
        ]
      }
      this.customerIds = row.customerId
      if (row.ltCode) {
        this.registerFrom.ltCode = row.ltCode
      } else {
        this.registerFrom.ltCode = ''
      }
      if (row.dxCode) {
        this.registerFrom.dxCode = row.dxCode
      } else {
        this.registerFrom.dxCode = ''
      }
      window.api.get(
        window.path.fiveWeb + '/province/list?regionCode=',
        {
          //   regionCode:this.ruleForm.officeCode
        },
        (res) => {
          this.province = res.data
        }
      )
      // this.registerFrom.customerId = row.customerId;
      // this.registerFrom.username = row.username;
      // this.registerFrom.customerName = row.customerName;
      // this.registerFrom.code = row.code;
      // this.registerFrom.customerContactPerson = row.customerContactPerson;
      // this.registerFrom.contactPersonPhone = row.contactPersonPhone;
      // this.registerFrom.contactPersonEmail = row.contactPersonEmail;
    },
    audit(row) {
      window.api.get(
        window.path.fiveWeb + '/customer/' + row.customerId,
        {},
        (res) => {
          if (res.code == 200) {
            this.$confirms.confirmation(
              'post',
              '正在提交审核中',
              window.path.fiveWeb + '/customer/toPreAudit',
              res.data,
              (ress) => { }
            )
          }
        }
      )
    },
    handleRemove(file, fileList) {
      this.registerFrom.businessLicense = ''
    },
    handleSuccess(res, fileList) {
      this.registerFrom.businessLicense = res.data
    },
    // handleRemoveF(file, fileList) {
    //   this.registerFrom.identificationFrontPic = "";
    // },
    // handleSuccessF(res, fileList) {
    //   this.registerFrom.identificationFrontPic = res.data;
    // },
    // handleRemoveB(file, fileList) {
    //   this.registerFrom.identificationBackPic = "";
    // },
    // handleSuccessB(res, fileList) {
    //   this.registerFrom.identificationBackPic = res.data;
    // },
    handleRemovepIcon(file, fileList) {
      this.registerFrom.serviceIcon = ''
    },
    handleSuccesspIcon(res, fileList) {
      this.registerFrom.serviceIcon = res.data
    },
    handleRemovep(file, fileList) {
      this.registerFrom.contactPersonIdentityFron = ''
    },
    handleSuccessp(res, fileList) {
      // this.fileList.push(res.data);
      this.registerFrom.contactPersonIdentityFron = res.data
    },
    handleRemovepback(file, fileList) {
      this.registerFrom.contactPersonIdentityBack = ''
    },
    handleSuccesspback(res, fileList) {
      // this.fileList.push(res.data);
      this.registerFrom.contactPersonIdentityBack = res.data
    },
    beforeAvatarUpload(file) {
      const siJPGGIF = file.name.split('.')[1]
      const isLt5M = file.size / 1024 / 1024 < 10
      const fileType = ['jpg', 'jpeg', 'gif', 'png']
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning('上传头像图片只能是 jpg、jpeg、gif 、png格式!')
        return false
      }
      if (!isLt5M) {
        this.$message.warning('上传头像图片大小不能超过 10MB!')
        return false
      }

      // return isJPG && isLt2M;
    },
    handleRemovec(file, fileList) {
      this.chatbotFrom.logoUrl = ''
    },
    Removec() {
      this.chatbotFrom.logoUrl = ''
      this.logoFlag = true
    },
    // handlePictureCardPreviewc(file) {
    //   this.dialogVisible = true;
    // },
    beforeAvatarUploadc(file) {
      // console.log(file,'file');
      const siJPGGIF = file.name.split('.')[1]
      const is4kb = file.size
      // const isLt5M = Math.round(file.size/1024*100)/100 < 4 //单位为KB;
      function objectSize(size) {
        var divisor = 1
        var unit = 'bytes'
        if (size >= 1024 * 1024) {
          divisor = 1024 * 1024
          unit = 'MB'
        } else if (size >= 1024) {
          divisor = 1024
          unit = 'KB'
        }
        if (divisor == 1) return size + ' ' + unit

        return (size / divisor).toFixed(2) < 50
      }
      let flagSize = objectSize(is4kb)
      if (siJPGGIF != 'jpg' && siJPGGIF != 'jpeg' && siJPGGIF != 'png') {
        this.$message.warning('上传头像图片只能是 jpeg 、png格式!')
        return false
      }
      if (!flagSize) {
        this.$message.warning('上传缩略图大小不能超过50kb！')
        return false
      }

      // return isJPG && isLt2M;
    },
    handleSuccessc(res, fileList) {
      if (res.code == 200) {
        this.chatbotFrom.logoUrl = res.data
      } else {
        this.$message({
          message: res.msg,
          type: 'error',
        })
      }
    },
    beforeAvatarUploadAtt(file, type) {
      const siJPGGIF = file.name.split('.')[file.name.split('.').length - 1]
      if (type == 'batch') {
        const fileType = ['zip']
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning('上传文件只能是zip格式!')
          return false
        }
      } else if (type == 'import') {
        const fileType = ['xlsx']
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning('上传文件只能是xlsx格式!')
          return false
        }
      } else {
        const isLt5M = file.size / 1024 / 1024 < 5
        const fileType = [
          'jpg',
          'jpeg',
          'gif',
          'pdf',
          'doc',
          'docx',
          'rar',
          'zip',
        ]
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning(
            '上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!'
          )
          return false
        }
        if (!isLt5M) {
          this.$message.warning('上传文件大小不能超过 5MB!')
          return false
        }
      }
    },
    handleSuccessAtt(res, fileList, type, fileType) {
      let name = fileList.name.substring(0, fileList.name.indexOf('.'))
      if (type == 'batch') {
        if (fileType == 'excel') {
          // res 后端返回的文件base64
          const link = document.createElement('a')
          link.href = 'data:application/octet-stream;base64,' + res.data
          // link1.download = name
          link.download = name + '.xlsx' //下载后文件名
          link.click()
        } else {
          const blob = new Blob([res], {
            type: 'application/octet-stream;charset=utf-8',
          })
          let link = document.createElement('a')
          let href = window.URL.createObjectURL(blob) //下载链接
          link.href = href
          link.text = '下载'
          link.download = name + '.txt' //下载后文件名
          document.body.appendChild(link)
          link.click() //点击下载
          document.body.removeChild(link) //下载完成移除元素
          window.URL.revokeObjectURL(href)
        }
        this.batchFileList = []
        this.$message({
          message: '下载成功！',
          type: 'success',
        })
      } else if (type == 'import') {
        if (res.code == 200) {
          const link = document.createElement('a')
          link.href = 'data:application/octet-stream;base64,' + res.data
          // link1.download = name
          link.download = name + '.txt' //下载后文件名
          link.click()
          this.$message({
            message: '下载成功！',
            type: 'success',
          })
          this.getchatList()
          // this.importList.file = res.data;
          // this.getchatList();
          // this.importVisible = false;
          // this.$message({
          //   message: res.msg,
          //   type: "success",
          // });
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      } else if (type == 'sync') {
        if (res.code == 200) {
          this.$message({
            message: res.msg,
            type: 'success',
          })
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      } else {
        if (res.code == 200) {
          this.chatbotFrom.attachment = res.data
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      }
    },
    handleRemoveAtt(file, type) {
      if (type == 'import') {
        this.importList.file = ''
      } else if (type == 'batch') {
        this.batchFileList = []
      } else if (type == 'sync') {
        this.syncFileList = []
      } else {
        this.chatbotFrom.attachment = ''
      }
    },
    // 电信5G授权书上传前验证
    beforeAvatarUploadAuthLetter(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const isLt5M = file.size / 1024 / 1024 < 5;
      const fileType = [
        "jpg",
        "jpeg",
        "gif",
        "pdf",
        "doc",
        "docx",
        "rar",
        "zip",
        "png",
      ];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning(
          "上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip、png格式!"
        );
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传文件大小不能超过 5MB!");
        return false;
      }
    },
    // 电信5G授权书上传成功
    handleSuccessAuthLetter(res, fileList) {
      if (res.code == 200) {
        this.chatbotFrom.authorizationLetterUrl = res.data;
        this.$message({
          message: "电信5G授权书上传成功！",
          type: "success",
        });
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    // 电信5G授权书移除
    handleRemoveAuthLetter(file) {
      this.chatbotFrom.authorizationLetterUrl = "";
      this.authorizationLetterList = [];
    },
    handleShuomingUpload(file) {
      console.log(file)
      //仅支持上传zip格式并大小限制50mb
      const isZip = file.name.split('.').pop() === 'zip';
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isZip) {
        this.$message.warning('仅支持上传zip格式!');
        return false;
      }
      if (!isLt50M) {
        this.$message.warning('上传文件大小不能超过 50MB!');
        return false;
      }
    },
    handleShuomingRemove(file) {
      console.log(file)
      this.shuomingFileList = []
    },  
    handleShuomingSuccess(res, file) {
      console.log(res, file)
      if(res.code == 200) {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        this.getchatList();
      } else {
        this.$message({
          message: res.msg,
          type: 'error'
        })
      }
      this.shuomingFileList = []
    },
    handleShuomingError(err, file, fileList) {
      console.log(err, file, fileList)
      this.$message({
        message: '上传失败',
        type: 'error'
      })
    }
    
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs['registerFrom'].resetFields()
        this.operationType = ['yd']
        this.changeOperation(this.operationType)
        this.PersonFileFront = []
        this.PersonFileBack = []
        this.businessLicenseFile = []
        this.identificationFrontPicFile = []
        this.identificationBackPicFile = []
        this.serviceIconFlie = []
        this.compNamelist = []
      }
    },
    userVisible(val) {
      if (!val) {
        this.$refs['userForm'].resetFields()
        this.compNamelist = []
      }
    },
    chatbotVisible(val) {
      if (!val) {
        this.$refs['chatbotFrom'].resetFields()
        this.chatbotFrom.logoUrl = ''
        this.chatbotFrom.attachment = ''
        this.chatbotFrom.authorizationLetterUrl = ''
        this.chatbotFrom.customerId = ''
        this.fileListc = []
        this.attachmentList = []
        this.authorizationLetterList = []
      }
    },
    registerVisible(val) {
      if (!val) {
        this.$refs['registerList'].resetFields()
        // this.customerIdList = []
        this.registerList.customerIdList = []
        this.registerList.channelName = ''
        this.registerList.operator = ''
        this.registerList.channelExtCode = ''
      }
    },
    importVisible(val) {
      if (!val) {
        this.$refs['importList'].resetFields()
        // this.customerIdList = []
        this.importList.file = []
        this.importFileList = []
        this.channelJson = []
        this.compNamelist = []
      }
    },
    channelSyncVisible(val) {
      if (!val) {
        this.$refs['channelForm'].resetFields()
        // this.customerIdList = []
        this.channelForm.file = ''
        this.channelJson = []
        this.syncFileList = []

      }
    }
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}

.detail {
  width: 100%;
  display: flex;
}

.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.items {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: 1px solid #ccc;
}

.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}

.classInput {
  width: 300px;
}

.circle {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: red;
  margin: 0 15px;
}

.circle-active {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #16a598;
  margin: 0 15px;
}

.circle_a1 {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e6a23c;
  margin: 0 15px;
}

.photo_active {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 12px;
  color: red;
}

.title_info {
  height: 35px;
  line-height: 35px;
  background: #f2f6fc;
  margin-left: 95px;
  padding: 0 18px;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  font-weight: 800;
}
</style>

<style>
.el-upload-list__item-name {
  width: 150px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-tabs__content {
  overflow: visible !important;
}

/* ================== 企业注册对话框美化样式 ================== */

/* 对话框整体样式 */
.enterprise-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.enterprise-dialog :deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.enterprise-dialog :deep(.el-dialog__body) {
  padding: 0;
}

/* 对话框头部样式 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #409EFF;
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  color: #fff;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px;
  width: 32px;
  height: 32px;
  padding: 0;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

/* 表单容器样式 */
.enterprise-form-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 400px;
}

.enterprise-form {
  max-width: 100%;
}

/* 表单分组样式 */
.form-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f6f8ff 0%, #f1f5ff 100%);
  border-bottom: 1px solid #e8ecf3;
  position: relative;
}

.section-icon {
  font-size: 18px;
  color: #4f46e5;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.section-tag {
  margin-left: auto;
  border-radius: 6px;
  font-size: 12px;
}

.status-indicator {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.status-success {
  color: #10b981;
  font-size: 18px;
}

.status-required {
  color: #ef4444;
  font-size: 18px;
}

.status-optional {
  color: #6b7280;
  font-size: 18px;
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.form-item {
  margin-bottom: 0 !important;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

/* 表单项样式优化 */
.enterprise-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  line-height: 1.5;
}

.custom-input {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.custom-input :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.custom-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.custom-select {
  width: 100%;
  border-radius: 8px;
}

.custom-select :deep(.el-select__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.custom-select :deep(.el-select__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-select :deep(.el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 选择器选项样式 */
.option-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.option-text {
  font-weight: 500;
  color: #1e293b;
}

.option-desc {
  font-size: 12px;
  color: #64748b;
}

.company-option,
.customer-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.company-name,
.customer-name {
  font-weight: 500;
  color: #1e293b;
}

.company-info,
.customer-id {
  font-size: 12px;
  color: #64748b;
}

.help-icon {
  color: #6b7280;
  cursor: help;
  transition: color 0.2s ease;
}

.help-icon:hover {
  color: #4f46e5;
}

/* 上传组件现代化样式 */
.upload-form-item {
  margin-bottom: 0 !important;
}

.modern-upload-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.upload-area {
  flex-shrink: 0;
}

.custom-upload :deep(.el-upload) {
  width: 120px !important;
  height: 120px !important;
  border-radius: 12px !important;
  border: 2px dashed #cbd5e1 !important;
  background: white !important;
  transition: all 0.3s ease !important;
}

.custom-upload :deep(.el-upload:hover) {
  border-color: #4f46e5 !important;
  background: #f8faff !important;
}

.custom-upload :deep(.el-upload-dragger) {
  width: 100% !important;
  height: 100% !important;
  border: none !important;
  border-radius: 12px !important;
  background: transparent !important;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 8px;
}

.upload-icon {
  font-size: 28px;
  color: #6b7280;
  transition: color 0.3s ease;
}

.custom-upload:hover .upload-icon {
  color: #4f46e5;
}

.upload-text {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.upload-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  background: white;
  border-left: 4px solid #e5e7eb;
}

.status-message .status-icon.required {
  color: #ef4444;
}

.status-message .status-icon.success {
  color: #10b981;
}

.status-message .status-icon.optional {
  color: #6b7280;
}

.status-text.required {
  color: #dc2626;
  font-weight: 500;
}

.status-text.success {
  color: #059669;
  font-weight: 500;
}

.status-text.optional {
  color: #6b7280;
}

.upload-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #64748b;
}

.tip-item .el-icon {
  color: #94a3b8;
  font-size: 14px;
}

/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.cancel-btn {
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 500;
  border: 1px solid #d1d5db;
  color: #6b7280;
  background: white;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  border-color: #9ca3af;
  color: #4b5563;
  background: #f9fafb;
}

.submit-btn {
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 500;
  background: #409EFF;
  border: none;
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enterprise-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
  
  .modern-upload-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .dialog-header {
    padding: 16px 20px;
  }
  
  .enterprise-form-container {
    padding: 16px;
  }
}

/* 动画效果 */
.form-section {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>