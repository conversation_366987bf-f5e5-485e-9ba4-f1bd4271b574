<template>
  <div>
    <div class="OuterFrame fillet" style="padding-bottom: 60px">
      <div class="Top_title" style="display: flex; align-items: center">
        <div  @click="goBack()" style="display: flex; align-items: center;cursor: pointer; color: #16a589">
          <el-icon><ArrowLeftBold /></el-icon>
          <span
            style="display: inline-block; padding-right: 10px"
           
          >
            返回</span
          >
        </div>
        |
        <span style="margin-left: 10px;">{{ productVal }}配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span
        ><span style="margin-right: 20px">{{ jsonData.compName }}</span>
        <div class="styleProduct">
          <span>产品剩余条数：</span
          ><span style="margin-right: 20px">{{ productRow.num }}</span>
        </div>
        <div class="styleProduct">
          <span class="basicInfo-title">账号状态：</span>

          <span class="basicInfo-title">{{ DisabledState || "启用" }}</span>
        </div>
        <div class="styleProduct" style="display: flex;align-items: center;">
          <span class="basicInfo-title">操作：</span>
          <el-switch
            v-model="Enable"
            @change="switchProduct"
            active-color="rgb(22,160,133)"
            inactive-color="#ccc"
            active-text="启用"
            inactive-text="停用"
          >
          </el-switch>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  // components: {
  //   ElIconArrowLeft,
  // },
  name: "productSetUp",
  data() {
    return {
      isFirstEnter: false,
      productVal: "",
      jsonData: {
        consumerName: "",
        compName: "",
      }, //用户资料
      userId: "",
      productRow: {},
      Enable: true, //启用停用
      DisabledState: "",
    };
  },
  methods: {
    //返回
    goBack() {
      // this.$router.push({ path: 'UserDetail',query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({
        path: "UserDetail",
        query: { id: this.$route.query.id },
      });
    },
    // 用户信息
    getBasicData() {
      window.api.get(
        window.path.omcs + "operatinguser/" + this.userId,
        {},
        (res) => {
          this.jsonData.consumerName = res.data.consumerName;
          this.jsonData.compName = res.data.compName;
          window.api.get(
            window.path.recharge +
              "manager/balance/" +
              this.jsonData.consumerName +
              "/" +
              sessionStorage.getItem("productSetUp"),
            {},
            (res) => {
              this.productRow = res.data;
              if (res.data.status == 0) {
                this.Enable = true;
              } else {
                this.Enable = false;
                if (res.data.status == 1) {
                  this.DisabledState = "用户停用";
                } else if (res.data.status == 2) {
                  this.DisabledState = "运营停用";
                }
              }
            }
          );
        }
      );
    },
    switchProduct(val) {
      if (!val) {
        this.$confirm("确认停用产品?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            window.api.post(
              window.path.recharge +
                "manager/close/" +
                sessionStorage.getItem("productSetUp") +
                "/" +
                this.jsonData.consumerName,
              {},
              (res) => {
                if (res.data.code == 200) {
                  if (res.data.status == 0) {
                    this.Enable = true;
                  } else {
                    this.Enable = false;
                    if (res.data.status == 1) {
                      this.DisabledState = "用户停用";
                    } else if (res.data.status == 2) {
                      this.DisabledState = "运营停用";
                    }
                  }
                }
              }
            );
          })
          .catch(() => {
            this.Enable = !val;
          });
      } else {
        this.$confirm("确认启用产品?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            window.api.post(
              window.path.recharge +
                "manager/open/" +
                sessionStorage.getItem("productSetUp") +
                "/" +
                this.jsonData.consumerName,
              {},
              (res) => {
                if (res.data.code == 200) {
                  if (res.data.status == 0) {
                    this.Enable = true;
                  } else {
                    this.Enable = false;
                    if (res.data.status == 1) {
                      this.DisabledState = "用户停用";
                    } else if (res.data.status == 2) {
                      this.DisabledState = "运营停用";
                    }
                  }
                }
              }
            );
          })
          .catch(() => {
            this.Enable = !val;
          });
      }
    },
  },
  created() {
    this.isFirstEnter = true;
    if (sessionStorage.getItem("productSetUp") == 1) {
      this.productVal = "SMS短信";
    } else if (sessionStorage.getItem("productSetUp") == 2) {
      this.productVal = "MMS彩信";
    } else if (sessionStorage.getItem("productSetUp") == 3) {
      this.productVal = "RMS视频短信";
    } else if (sessionStorage.getItem("productSetUp") == 4) {
      this.productVal = "IMS国际短信";
    } else if (sessionStorage.getItem("productSetUp") == 5) {
      this.productVal = "闪验";
    } else if (sessionStorage.getItem("productSetUp") == 6) {
      this.productVal = "语音验证码";
    } else if (sessionStorage.getItem("productSetUp") == 7) {
      this.productVal = "语音通知";
    } else if (sessionStorage.getItem("productSetUp") == 9) {
      this.productVal = "5G短信";
    }
    this.userId = this.$route.query.id;
    this.getBasicData();
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        if (sessionStorage.getItem("productSetUp") == 1) {
          this.productVal = "SMS短信";
        } else if (sessionStorage.getItem("productSetUp") == 2) {
          this.productVal = "MMS彩信";
        } else if (sessionStorage.getItem("productSetUp") == 3) {
          this.productVal = "RMS视频短信";
        } else if (sessionStorage.getItem("productSetUp") == 4) {
          this.productVal = "IMS国际短信";
        } else if (sessionStorage.getItem("productSetUp") == 5) {
          this.productVal = "闪验";
        } else if (sessionStorage.getItem("productSetUp") == 6) {
          this.productVal = "语音验证码";
        } else if (sessionStorage.getItem("productSetUp") == 7) {
          this.productVal = "语音通知";
        } else if (sessionStorage.getItem("productSetUp") == 9) {
          this.productVal = "5G短信";
        }
        this.userId = this.$route.query.id;
        this.getBasicData();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  watch: {
    // 账号启用停用
    Enable(val) {},
  },
};
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}
.styleProduct {
  margin-top: 20px;
}
</style>
