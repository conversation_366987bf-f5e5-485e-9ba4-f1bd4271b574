<template>
  <div>
    <!-- 实名明细 -->
    <el-dialog
      :title="
        signatureData.productId == '1' ? '短信签名明细' : '视频短信签名明细'
      "
      v-model="autonymVisible"
      :before-close="handleClose"
      width="800px"
    >
      <div class="table-box">
        <el-tabs v-model="operator" type="card" @tab-change="handleChange">
          <el-tab-pane name="1">
            <template #label
              ><i class="iconfont icon-yidong" style="color: #409eff;margin-right: 8px;"></i>
              移动</template
            >
          </el-tab-pane>
          <el-tab-pane name="2">
            <template  #label
              ><i class="iconfont icon-liantong" style="color: #f56c6c;margin-right: 8px;"></i>
              联通</template
            >
          </el-tab-pane>
          <el-tab-pane label="电信" name="3">
            <template  #label
              ><i class="iconfont icon-dianxin" style="color: #0c36f2;margin-right: 8px;"></i>
              电信</template
            >
          </el-tab-pane>
        </el-tabs>
      </div>

      <el-form
        label-width="80px"
        :inline="true"
        :model="checkForm"
        class="demo-form-inline"
        ref="checkForm"
      >
        <el-form-item label="通道号" prop="channelCode">
          <el-input
            v-model="checkForm.channelCode"
            placeholder
            class="input-time"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain style @click="searchAutonym"
            >查询</el-button
          >
          <el-button
            type="primary"
            plain
            style
            @click="resetAutonym('checkForm')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loading"
        :data="autonymData"
        style="width: 100%"
        border
        max-height="400px"
      >
        <el-table-column label="用户名称" prop="">
          <template #default="scope">
            <span> {{ signatureData.userName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="产品名称" prop="">
          <template #default="scope">
            <span v-if="signatureData.productId == '1'">短信</span>
            <span v-if="signatureData.productId == '3'">视频短信</span>
          </template>
        </el-table-column>
        <el-table-column label="签名" prop="">
          <template #default="scope">
            <span> {{ signatureData.signatoryId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通道号" prop="channelCode"></el-table-column>
        <el-table-column label="子端口">
          <template #default="scope">
            <span> {{ scope.row.spCode }}</span>
          </template>
        </el-table-column>
        <el-table-column label="扩展号">
          <template #default="scope">
            <span> {{ scope.row.ext }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可用状态">
          <template #default="scope">
            <el-tag
:disable-transitions="true" type="success" v-if="scope.row.status == 1">可用</el-tag>
            <el-tag
:disable-transitions="true" type="danger" v-else-if="scope.row.status == 2"
              >未生成</el-tag
            >
            <el-tag
:disable-transitions="true" type="warning" v-else-if="scope.row.status == 0"
              >实名中</el-tag
            >
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
  
  <script>
export default {
  name: "SignatureDetail",
  props: {
    signatureData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      autonymVisible: false,
      loading: false,
      signatoryId: "",
      checkForm: {
        channelCode: "",
        userId: "",
      },
      autonymData: [],
      operator: "1",
    };
  },
  methods: {
    handelCheck(row) {
      //   this.checkForm.userId = row.userId;
      //   this.signatoryId = row.signature;
      this.handleClick();
    },
    searchAutonym() {
      this.handleClick();
    },
    resetAutonym() {
      this.checkForm.channelCode = "";
      this.handleClick();
    },
    handleChange(e) {
      this.operator = String(e)
      this.handleClick()
    },
    handleClick() {
      try {
        this.autonymVisible = true;
        this.loading = true;
        window.api.post(
          window.path.omcs + "consumerSignature/queryRealNameInfo",
          {
            userId: this.signatureData.userId,
            signature: this.signatureData.signatoryId,
            operator: this.operator,
            channelCode: this.checkForm.channelCode,
            productId: this.signatureData.productId,
          },
          (res) => {
            if (res.code == 200) {
              this.loading = false;
              this.autonymData = res.data;
            } else {
              this.loading = false;
              this.autonymData = [];
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          },
          (err) => {
            this.loading = false;
            this.$emit("handleClose", false);
            console.log(err, "error");
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    handleClose() {
      this.autonymVisible = false;
      this.$emit("handleClose", this.autonymVisible);
    },
  },
  created() {
    console.log(this.signatureData, "props");
    this.handleClick();
  },
  watch: {
    autonymVisible(val) {
      if (!val) {
        this.autonymData = [];
        this.operator = "1";
        this.checkForm.userId = "";
        this.checkForm.channelCode = "";
      }
    },
  },
};
</script>
  
  <style scoped>
.table-box :deep(.el-tabs__item) {
  width: 150px;
  text-align: center;
}
</style>