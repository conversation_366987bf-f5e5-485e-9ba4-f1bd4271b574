<template>
  <div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="formInline.userName" placeholder class="input-w"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain style @click="Query">查询</el-button>
            <el-button type="primary" plain style @click="Reload('formInline')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">新增</el-button>
            <el-button type="danger" style="margin-left: 15px" v-if="selectId.length > 0"
              @click="delAll">批量删除</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="Unknownagain" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="100"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;" :data="tableDataObj.tableData" @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="ID" title="ID">
            <template v-slot="scope">
              <div>{{ scope.row.id }}</div>
            </template>
          </vxe-column>
          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div style="color: #16a589; cursor: pointer">{{ scope.row.userName }}</div>
            </template>
          </vxe-column>

          <vxe-column field="失败代码" title="失败代码">
            <template v-slot="scope">
              <div>{{ scope.row.failureCode }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="是否更换扩展码">
                <template #default="scope">
                  <span style="color: #16a589" v-if="scope.row.changeExt == 0">
                    否</span 
                  >
                  <span style="color: #f56c6c" v-else>是</span>
                </template>
              </vxe-column> -->
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>
                {{ scope.row.createName }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>
                {{ scope.row.createTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="240" fixed="right">
            <template v-slot="scope">
              <el-button link style="color: #16a589" @click="detailsRow(scope.row)"><el-icon>
                  <EditPen />
                </el-icon>&nbsp;编辑</el-button>
              <el-button link style="color: #16a589" @click="detailsShow(scope.row)"><el-icon>
                  <Tickets />
                </el-icon>&nbsp;查看明细</el-button>
              <!-- <el-button type="text"  @click="handleReject(scope.$index, scope.row)"><i class="el-icon-success"></i>&nbsp;报告</el-button> -->
              <el-button link style="color: #f56c6c" @click="delRow(scope.row)"><el-icon>
                  <Delete />
                </el-icon>&nbsp;删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增运营商 -->
      <el-dialog :title="titleMap[dialogStatus]" v-model="dialogFormVisible" :close-on-click-modal="false"
        width="720px">
        <el-form :model="formop" :rules="rules" ref="formop" label-width="110px">
          <div v-if="dialogStatus == 'add'">
            <el-form-item label="公司名" prop="compId">
              <el-select ref="optionRef" class="input-w" v-model="formop.compId" clearable filterable remote
              :remote-method="remoteMethod" :loading="loadingcomp" placeholder="请选择公司名称" @change="bindChange">
              <el-option v-for="(item, index) in compNamelist" :key="index" :label="item.company" :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户名" prop="consumerNameArr">
            <el-transfer filterable filter-placeholder="请输入用户名" :titles="['待选用户', '已选用户']" v-model="formop.consumerNameArr" :data="userData"
              @change="handleChange"></el-transfer>
          </el-form-item>
          </div>
          <div v-if="dialogStatus == 'edit'">
            <el-form-item label="用户名" prop="userName">
              <el-input disabled v-model="formop.userName" class="input-w" autocomplete="off"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="失败代码" prop="failureCode">
            <el-input v-model="formop.failureCode" class="input-w" autocomplete="off"></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')">提 交</el-button>
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增运营商结束 -->
      <el-dialog title="用户失败补发明细" v-model="dialogDetailVisible" :close-on-click-modal="false" width="50%">
        <el-table v-loading="tableDShow.loading2" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable" border :data="tableDShow.tableData"
          style="width: 100%">
          <el-table-column type="selection" width="46"></el-table-column>
          <el-table-column label="ID">
            <template v-slot="scope">{{ scope.row.id }}</template>
          </el-table-column>
          <el-table-column label="用户名">
            <template v-slot="scope">
              <span style="color: #16a589; cursor: pointer">{{ scope.row.username }}</span>
            </template>
          </el-table-column>
          <el-table-column label="通道号">
            <template v-slot="scope">{{ scope.row.channelId }}</template>
          </el-table-column>
          <el-table-column label="失败代码">
            <template v-slot="scope">{{ scope.row.failureCode }}</template>
          </el-table-column>
          <el-table-column label="扩展码">
            <template v-slot="scope">
              <span>{{ scope.row.oldExt }}</span>
            </template>
          </el-table-column>
          <el-table-column label="补发扩展码">
            <template v-slot="scope">
              <span>{{ scope.row.newExt }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">
              <span>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
                }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!-- <div style="margin: 10px;"> -->
        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChanges" @current-change="handleCurrentChanges"
            :current-page="detailShows.currentPage" :page-size="detailShows.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDShow.total"></el-pagination>
        </div>

        <template v-slot:footer>
          <div class="dialog-footer">
            <!-- <el-button type="primary" @click="submitForm('formop')">提 交</el-button> -->
            <!-- <el-button @click="dialogDetailVisible = false">取 消</el-button> -->
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
import moment from 'moment'
import { deepClone } from '@/utils/helpers'
export default {
  components: {
    TableTem
  },
  name: 'Unknownagain',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      titleMap: {
        add: '添加用户自动补发',
        edit: '编辑用户自动补发',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      dialogDetailVisible: false,
      id: '', //失败代码补发id
      username: '',
      tableRow: '', //当前行列表数据
      selectId: '',
      formop: {
        //表单数据
        userName: '',
        failureCode: '',
        // changeExt: '',
        compId: '',
        consumerNameArr: [],
      },
      rules: {
        //验证规则
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          {
            min: 1,
            max: 50,
            message: '长度在 1 到 50 个字符',
            trigger: ['blur', 'change'],
          },
          // {
          //   pattern: /^[\u4e00-\u9fa5]+$/,
          //   message: "含有非法字符（只能输入汉字！）"
          // }
        ],
        failureCode: [
          { required: true, message: '请输入失败代码', trigger: 'blur' },
          {
            // min: 1,
            // max: 50,
            // message: "长度在 1 到 50 个字符",
            trigger: ['blur', 'change'],
          },
          // {
          //   pattern: /^[\u4e00-\u9fa5]+$/,
          //   message: "含有非法字符（只能输入汉字！）"
          // }
        ],
      },
      tabelAlllist: {
        //存储查询数据
        userName: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      detailShows: {
        userName: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      formInline: {
        //查询数据
        userName: '',
        // orderNum: "",
        // operator:'',
        // bank:"",
        // dataCreat: "",
        // beginTime: "",
        // endTime: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDShow: {
        total: 0,
        loading2: false,
        tableData: [],
      },
      tableDataObj: {
        //列表数据
        currentPage: '1',
        pageSize: '10',
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'id',
            showName: 'ID',
            fixed: false,
          },
          {
            prop: 'userName',
            showName: '用户名',
            fixed: false,
          },
          {
            prop: 'failureCode',
            showName: '失败代码',
            fixed: false,
          },
          {
            prop: 'changeExt',
            showName: '是否切换扩展',
            fixed: false,

            formatData: function (val) {
              return val == '0' ? '否' : '是'
            },
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: 140,
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-edit',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'el-icon-delete',
            color: '#f56c6c',
          },
        ],
      },
      pldel: false,
      compNamelist: [],
      loadingcomp: false,
      userData: [],
      consumerStatus: '0',
      consumerNameArr: [],
    }
  },
  methods: {
    //公司名远程搜索
    searchAccount(val) {
      try {
        window.api.get(
          window.path.omcs + `operatinguser/companyInfo?companyName=${val || ''}`,
          {},
          (res) => {
            if (res.code == 200) {
              this.compNamelist = res.data
            } else {
              this.$message({
                message: res.msg,
                type: 'error',
              })
            }
            // this.services = res.data;
          }
        )
      } catch (error) {
        console.log(error, 'error')
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loadingcomp = true
        this.searchAccount(query)
        this.loadingcomp = false
      } else {
        this.compNamelist = []
        this.searchAccount()
      }
    },
    bindChange(e) {
      if (e) {
        this.getUserList(e, this.consumerStatus)
      }
    },
    handleChange(e) {
      // console.log(e, 'eee');
      // console.log(this.userData, 'e')
      // this.batchForm.filter.consumerNameArr = e
    },
    // filterMethod(query, item){
    //   console.log(query, item, 'query, item');
      
    //   // return item.initial.toLowerCase().includes(query.toLowerCase())
    // },
    getUserList(compId, status) {
      window.api.get(
        window.path.omcs +
        `operatinguser/users/${compId}?consumerStatus=${status}`,
        {},
        (res) => {
          if (res.code == 200) {
            this.userData = res.data.map((item) => {
              return {
                key: item.consumerName,
                label: item.consumerName,
              }
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
          // this.services = res.data;
        }
      )
    },
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'v3/operatingusercodereissue/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          // this.tableDataObj.tableData.forEach((item)=>{
          //  item.changeExt = item.changeExt+""
          // })
          // console.log(this.tableDataObj.tableData,'this.tableDataObj.tableData');
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    handleSizeChanges(size) {
      //分页每一页的有几条
      this.detailShows.pageSize = size
      this.detailList()
    },
    handleCurrentChanges: function (currentPage) {
      //分页的第几页
      this.detailShows.currentPage = currentPage
      this.detailList()
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    Query() {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handelSelection(row) {
      //列表复选框的值
      if (row.records.length > 0) {
        let arr = []
        row.records.forEach(item => {
          arr.push(item.id)
        })
        this.selectId = arr.join(',')
      } else {
        this.selectId = ""
      }
    },
    Reload() {
      //重置
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //导出 （发送记录）
    export1() {
      let aa = {}
      Object.assign(aa, this.tabelAlllist)
      // aa.flag = this.flag;
      // aa.sendBeginTime = this.Times.beginTime;
      // aa.sendEndTime = this.Times.endTime;
      // aa.isDownload = 1;
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatingRechargeConsumer/export',
          aa,
          '充值记录报表.xlsx'
        )
      }
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          const formopClone = deepClone(this.formop)
          
          if (this.dialogStatus == 'add') {
            //添加
            formopClone.userName = formopClone.consumerNameArr.join(',')
          delete formopClone.consumerNameArr
          delete formopClone.compId
            this.$confirms.confirmation(
              'post',
              '确认执行此操作吗？',
              window.path.omcs + 'v3/operatingusercodereissue',
              formopClone,
              () => {
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            )
            // window.api.get(
            //   window.path.omcs +
            //     'v3/operatingusercodereissue/checkUserName/' +
            //     this.formop.userName,
            //   {},
            //   (res) => {
            //     if (res.code != 200) {
            //       this.$message({
            //         message: res.msg,
            //         type: 'warning',
            //       })
            //     } else {

            //     }
            //   }
            // )
          } else {
            //编辑
            formopClone.id = this.id
            formopClone.userName = formopClone.userName
            delete formopClone.consumerNameArr
            delete formopClone.compId
            this.$confirms.confirmation(
                'put',
                '确认执行此操作吗？',
                window.path.omcs + 'v3/operatingusercodereissue',
                formopClone,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            //验证唯一
            // if (this.formop.userName == this.tableRow.userName) {
            //   this.$confirms.confirmation(
            //     'put',
            //     '确认执行此操作吗？',
            //     window.path.omcs + 'v3/operatingusercodereissue',
            //     this.formop,
            //     () => {
            //       this.dialogFormVisible = false
            //       this.gettableLIst()
            //     }
            //   )
            // } else {
              
            // }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    addopt() {
      //添加
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    delAll() {
      // window.api.get(
      //   window.path.omcs+"operatingindustrycategories/checkNoteInCodeCount/" +
      //     this.selectId,
      //   {},
      //   res => {
      //     if (res.code == 400) {
      //       this.$message({
      //         message: "行业类别已被使用，不可以删除！",
      //         type: "warning"
      //       });
      //     } else {
      //批量删除

      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'v3/operatingusercodereissue/' + this.selectId,
        {},
        () => {
          this.gettableLIst()
        }
      )

      //     }
      //   }
      // );
    },

    detailsShow(val) {
      console.log(val, 'val')
      this.dialogDetailVisible = true
      this.tableDShow.loading2 = true
      this.username = val.userName
      this.detailList()
    },
    detailList() {
      window.api.post(
        window.path.omcs + 'operatingclientextreplace/page',
        {
          currentPage: this.detailShows.currentPage,
          pageSize: this.detailShows.pageSize,
          username: this.username,
        },
        (res) => {
          // console.log(res);
          this.tableDShow.loading2 = false
          this.tableDShow.tableData = res.data.records
          this.tableDShow.total = res.data.total
        }
      )
    },
    detailsRow(val) {
      //编辑
      // console.log(val,'val');
      const { changeExt } = val
      // console.log(changeExt,'changeExt');
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      window.api.get(
        window.path.omcs + 'v3/operatingusercodereissue/' + val.id,
        {},
        (res) => {
          this.$nextTick(() => {
            this.$refs.formop.resetFields()
            Object.assign(this.formop, res.data)
            // this.formop.changeExt = changeExt + "";
          })
        }
      )
    },
    //删除
    delRow: function (val) {
      // console.log(val,'val');
      //删除验证
      // window.api.get(window.path.omcs+'operatingoperatorinfo/checkChannelIsUsed/'+val.row.id,
      // {},
      // res=>{
      //     if (res.code==400) {
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }else{

      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'v3/operatingusercodereissue/' + val.id,
        {},
        () => {
          this.gettableLIst()
        }
      )

      //     }
      // })
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }

      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
    // rouTz(val){
    //   this.$router.push({ path: '/UserDetail', query: { id: val.id}})
    // },
  },
  mounted() { },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // tabelAlllist: {
    //   handler() {
    //     this.gettableLIst();
    //   },
    //   deep: true, //深度监听
    //   immediate: true,
    // },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
        this.userData = []
        // this.formop.changeExt = "";
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter>div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.sensitive {}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
