<template>
  <div class="login_cell_phone">
    <div class="OuterFrame fillet" style="height: 100%">
      <div>
        <el-form
          :inline="true"
          ref="formInline"
          :model="formInline"
          class="demo-form-inline"
          label-width="100px"
        >
          <!-- <el-form-item label="用户名称"   prop="username">
                                <el-input v-model="formInline.username" placeholder="" class="input-w"></el-input>
                            </el-form-item> -->
          <!-- <el-form-item label="发送内容"  prop="content">
                                <el-input v-model="formInline.content" placeholder="" class="input-w"></el-input>
                            </el-form-item>
                            <el-form-item label="手机号"  prop="mobile">
                                <el-input v-model="formInline.mobile" placeholder="" class="input-w"></el-input>
                            </el-form-item>
                        
                            <el-form-item label="发送时间"  prop="time">
                                <el-date-picker
                                    v-model="formInline.time"
                                    type="datetimerange"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                    range-separator="至"
                                    @change="timeClick"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </el-form-item> -->
          <!-- <el-form-item>
                                <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
                                <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
                            </el-form-item> -->
        </el-form>
      </div>
      <div class="boderbottom"></div>
      <div class="Mail-table" style="padding-bottom: 40px;">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="ManualAuditWebTask"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名" title="用户名">
            <template v-slot="scope">
              <div
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
                >{{ scope.row.username }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="未审数量" title="未审数量">
            <template v-slot="scope">
              <div>{{ scope.row.unCheckNum }}</div>
            </template>
          </vxe-column>
          <vxe-column field="已审数量" title="已审数量">
            <template v-slot="scope">
              <div>{{ scope.row.checkNum }}</div>
            </template>
          </vxe-column>
          <vxe-column field="免审条数" title="免审条数">
            <template v-slot="scope">
              <div>{{ scope.row.smsAuditNum }}</div>
            </template>
          </vxe-column>
          <vxe-column field="短信黑词验证" title="短信黑词验证">
            <template v-slot="scope">
              <div style="color: #f00" v-if="scope.row.smsIsBlackWords == 1"
                >验证</div
              >
              <div v-else-if="scope.row.smsIsBlackWords == 2">不验证</div>
            </template>
          </vxe-column>
          <vxe-column field="处理状态" title="处理状态">
            <template v-slot="scope">
              <div style="color: #f00" v-if="scope.row.processStatus == 1"
                >处理中</div
              >
              <div
                style="color: #409eff"
                v-else-if="scope.row.processStatus == 0"
                >空闲</div
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                            </el-pagination>
                        </el-col> -->
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'webTask',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      userFlag: false,
      isFirstEnter: false,
      nameover: '',
      ChannelData: '', //传递通道值
      name: 'webTask',
      formInline: {
        username: '',
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.get(
        window.path.omcs + 'operatingclientsmssimilar/webTask/similarUsers',
        {},
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.tableData = res.data
          }

          // this.tableDataObj.tablecurrent.total=res.data.total
        }
      )
    },
    // 查询
    ListSearch() {
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.InquireList()
    },
    rouTz(val) {
      let obj = {
        username: val.username,
        isTiming: '1',
        sendType: '2',
        status: '0',
        matchSimilar: '2',
        beginTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment().subtract(-7, 'days').format('YYYY-MM-DD HH:mm:ss'),
      }
      localStorage.setItem('webTask', JSON.stringify(obj))
      sessionStorage.setItem('path', '/ProcessingQueue')
      this.$router.push('/ProcessingQueue')
    },
    // phoneClick(index,row){
    //     window.api.post(window.path.upms+'/generatekey/decryptMobile',{
    //         keyId:row.keyId,
    //         smsInfoId:row.smsInfoId,
    //         cipherMobile:row.cipherMobile
    //     },res=>{
    //         // console.log(res);
    //         if (res.code == 200) {
    //             this.tableDataObj.tableData[index].mobile = res.data;
    //         } else {
    //             this.$message({
    //             message: res.msg,
    //             type: "warning",
    //             });
    //         }
    //         // this.tableDataObj.tableData[index].mobile=res.data
    //     })
    // },
    // 分页
    // handleSizeChange(size) {
    //     this.formInlines.pageSize = size;
    // },
    // handleCurrentChange: function(currentPage){
    //     this.formInlines.currentPage = currentPage;
    // },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
    // this.InquireList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        // this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
