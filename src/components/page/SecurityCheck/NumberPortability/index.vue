<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model="formInline.mobile" placeholder></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain style @click="Query">查询</el-button>
            <el-button type="primary" plain style @click="Reload('formInline')">重置</el-button>
            <!-- <el-select class="input-w" v-model="formInline.forwardOperator">
                                <el-option label="移动" value="1">
                                </el-option>
                                <el-option label="联通" value="2">
                                </el-option>
                                <el-option label="电信" value="3">
                                </el-option>
                            </el-select> -->
          </el-form-item>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">添加携号转网</el-button>
            <el-button type="primary" @click="handelSyn">同步缓存</el-button>
            <el-button type="primary" @click="openImportDialog">导入</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table ref="tableRef" id="NumberPortability" border stripe :custom-config="customConfig"
          :column-config="{ resizable: true }" :row-config="{ isHover: true }" min-height="1"
          v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;" :data="tableDataObj.tableData">

          <!-- <vxe-column type="selection" width="46"></vxe-column> -->
          <vxe-column field="手机号" title="手机号">
            <template v-slot="{ row, rowIndex }">
              <div v-if="pIndex == rowIndex">{{
                row.mobile || "-"
                }}</div>
              <div v-else style="cursor: pointer; color: #16a589" @click="phoneClickTable(rowIndex, row)">{{
                row.maskMobile || "-" }}</div>
            </template>
          </vxe-column>
          <vxe-column field="省份" title="省份">
            <template v-slot="scope">
              <div>{{ scope.row.province }}</div>
            </template>
          </vxe-column>
          <vxe-column field="原始运营商" title="原始运营商">
            <template v-slot="scope">
              <div v-if="scope.row.originalOperator == 1" style="display: flex; align-items: center">
                <i class="iconfont icon-yidong" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div v-else-if="scope.row.originalOperator == 2" style="display: flex; align-items: center">
                <i class="iconfont icon-liantong" style="font-size: 16px; color: #f56c6c"></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div v-else-if="scope.row.originalOperator == 3" style="display: flex; align-items: center">
                <i class="iconfont icon-dianxin" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">电信</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="转网运营商" title="转网运营商">
            <template v-slot="scope">
              <div v-if="scope.row.forwardOperator == 1" style="display: flex; align-items: center">
                <i class="iconfont icon-yidong" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div v-else-if="scope.row.forwardOperator == 2" style="display: flex; align-items: center">
                <i class="iconfont icon-liantong" style="font-size: 16px; color: #f56c6c"></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div v-else-if="scope.row.forwardOperator == 3" style="display: flex; align-items: center">
                <i class="iconfont icon-dianxin" style="font-size: 16px; color: #409eff"></i>
                <span style="margin-left: 5px">电信</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="有效期" title="有效期" width="170">
            <template v-slot="scope">
              <div v-if="scope.row.expireDate">{{
                moment(scope.row.expireDate).format('YYYY-MM-DD HH:mm:ss')
                }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>{{ scope.row.createTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="更新时间" title="更新时间" width="170">
            <template v-slot="scope">
              <div>{{ scope.row.updateTime }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="150" fixed="right">
            <template v-slot="scope">
              <el-button link style="margin-left: 0px; color: #409eff;"
                @click="detailsRow(scope.$index, scope.row)">编辑</el-button>
              <el-button link style="margin-left: 10px; color: red"
                @click="delState(scope.$index, scope.row)">删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="1" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增账单 -->
      <el-dialog :title="titleMap[dialogStatus]" v-model="dialogFormVisible" :close-on-click-modal="false"
        width="520px">
        <el-form label-width="110px" :model="formop" :rules="rules" ref="formop" style="padding: 0 28px 0 20px">
          <el-form-item label="手机号" prop="mobile">
            <el-input class="input-w" v-model="formop.mobile" placeholder="" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="转网运营商" prop="forwardOperator">
            <el-select class="input-w" v-model="formop.forwardOperator" placeholder="请选择">
              <el-option label="移动" value="1"> </el-option>
              <el-option label="联通" value="2"> </el-option>
              <el-option label="电信" value="3"> </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')">提 交</el-button>
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog title="导入示例" v-model="importDialogVisible" :close-on-click-modal="false" width="520px">
        <el-upload class="upload-demo" :action="actionUrl" :headers="headers" :on-success="handleUploadSuccess"
          :on-error="handleUploadError" :on-progress="handleProgress" :on-remove="handleRemove"
          :before-upload="beforeUpload" :limit="1" :file-list="fileList" accept=".xlsx,.xls">
          <el-button size="default" type="primary">
            <el-icon><upload-filled /></el-icon>选择文件上传
          </el-button>
          <template #tip>
            <div class="el-upload__tip">只能上传 xlsx、xls 格式的Excel文件，且不超过10MB</div>
            <div><a href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/da807e269974323e09697aa7daa12bc0">下载导入模板</a></div>
          </template>
        </el-upload>
        <div v-if="Progressflag" style="text-align: center; font-size: 35px">
          <el-icon class="is-loading">
            <loading />
          </el-icon>
        </div>
        <template #footer>
          <el-button @click="importDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="importTemplate">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import getNoce from "../../../../plugins/getNoce";
export default {
  name: 'billSetting',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      titleMap: {
        add: '添加携号转网',
        edit: '编辑携号转网',
      },
      pIndex: -1,
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        username: '',
        mobile: '',
        forwardOperator: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        username: '',
        mobile: '',
        forwardOperator: '',
        currentPage: 1,
        pageSize: 10,
      },
      options: [],
      //添加列表
      formop: {
        mobile: '', //手机号
        forwardOperator: '', //转网运营商
      },
      id: '', //列表行id
      rules: {
        mobile: [
          {
            required: true,
            message: '手机号不能为空',
            trigger: 'change',
          },
        ],
        forwardOperator: [
          {
            required: true,
            message: '转网运营商不能为空',
            trigger: 'change',
          },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
      importDialogVisible: false,
      fileList: [],
      actionUrl: window.path.omcs + 'operatingnumberportabilitydetail/upload',
      headers: {},
      Progressflag: false,
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.pIndex = -1;
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingnumberportabilitydetail/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
        }
      )
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      window.api.post(
        window.path.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handedTime(e) {
      this.formop.startTime = moment(e).format('YYYY-MM-DD hh:mm:ss')
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.omcs + 'operatingnumberportabilitydetail',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.omcs + 'operatingnumberportabilitydetail',
              this.formop,
              (res) => {
                // if (res.code == 400) {
                //     this.$message({
                //         message: res.msg,
                //         type: "warning",
                //     });
                // } else {
                //     this.$message({
                //         message: res.msg,
                //         type: "success",
                //     });
                // }
                this.dialogFormVisible = false
                this.gettableLIst()
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    handelSyn() {
      this.$confirms.confirmation(
        'post',
        '确认执行此操作吗？',
        window.path.omcs + 'operatingnumberportabilitydetail/syn',
        {},
        (res) => {
          // if (res.code == 400) {
          //     this.$message({
          //         message: res.msg,
          //         type: "warning",
          //     });
          // } else {
          //     this.$message({
          //         message: res.msg,
          //         type: "success",
          //     });
          // }
          this.gettableLIst()
        }
      )
    },
    detailsRow(index, val) {
      // console.log(val);
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })
      // this.formop.status = val.status+""
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确认执行此操作吗？',
        window.path.omcs + 'operatingnumberportabilitydetail/' + val.id,
        {},
        (res) => {
          // if (res.code == 400) {
          //     this.$message({
          //         message: res.msg,
          //         type: "warning",
          //     });
          // } else {
          //     this.$message({
          //         message: res.msg,
          //         type: "success",
          //     });
          // }
          this.gettableLIst()
        }
      )
    },
    async openImportDialog() {
      const nonce = await getNoce.useNonce();
      this.importDialogVisible = true;
      this.fileList = [];
      this.headers = {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        'Once': nonce,
      }
    },
    importTemplate() {
      this.importDialogVisible = false;
      this.gettableLIst();
    },
    handleUploadSuccess(res, file) {
      console.log('上传结果', res);
      this.Progressflag = false;
      if (res.code === 200) {
        this.$message({
          message: res.msg,
          type: 'success',
        })
        this.gettableLIst();

      } else {
        this.$message({
          message: res.msg || '导入失败',
          type: 'warning',
        })
      }
    },
    handleUploadError(error) {
      this.Progressflag = false;
      this.$message({
        message: '上传失败：' + error,
        type: 'warning',
      })
    },
    handleProgress() {
      this.Progressflag = true;
    },
    handleRemove(file, fileList) {
      console.log('文件移除', file, fileList);
      this.fileList = [];
    },
    beforeUpload(file) {
      console.log(file, 'file');

      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message({
          message: '只能上传Excel文件!',
          type: 'warning',
        })
        return false;
      }
      if (!isLt10M) {
        this.$message({
          message: '文件大小不能超过10MB!',
          type: 'warning',
        })
        return false;
      }
      return true;
    },
  },
    activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
    this.options = JSON.parse(localStorage.getItem('list'))
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.id = ''
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter>div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.input-w {
  width: 300px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
