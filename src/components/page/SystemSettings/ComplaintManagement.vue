<template>
  <div class="complaint-management">
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">
          <el-icon class="title-icon"><DocumentCopy /></el-icon>
          数据工单管理
        </h2>
        <!-- <p class="page-description">管理系统投诉信息，支持文件上传和处理跟踪</p> -->
      </div>
    </div>

    <div class="content-container">
      <!-- 查询区域 -->
      <div class="search-section">
        <el-card class="search-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Search /></el-icon>
              <span>查询条件</span>
            </div>
          </template>
          
          <el-form 
            ref="searchFormRef" 
            :model="searchForm" 
            :inline="true" 
            label-width="auto"
            class="search-form">
            <el-form-item label="创建人" prop="createUser">
              <el-input 
                v-model="searchForm.createUser" 
                placeholder="请输入创建人"
                clearable
                style="width: 200px" />
            </el-form-item>
            
            <el-form-item label="任务类型" prop="taskType">
              <el-select 
                v-model="searchForm.taskType" 
                placeholder="请选择任务类型"
                clearable
                style="width: 200px">
                <el-option
                  v-for="item in taskTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-form-item>
            <el-form-item label="处理状态" prop="status">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择处理状态"
                clearable
                style="width: 200px">
                <el-option label="处理中" value="1" />
                <el-option label="处理成功" value="2" />
                <el-option label="处理失败" value="3" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" prop="timeRange">
              <el-date-picker
                v-model="searchForm.timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 350px" />
            </el-form-item>
            
            <el-form-item class="search-buttons">
              <el-button type="primary" @click="handleSearch" :loading="tableLoading">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button @click="handleReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-section">
        <el-card class="action-card" shadow="never">
          <el-button type="primary" @click="showAddDialog" :icon="Plus">
            创建数据工单
          </el-button>
        </el-card>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <el-card class="table-card" shadow="never">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon><List /></el-icon>
                <span>投诉列表</span>
                <el-tag v-if="tableData.total > 0" type="info" size="small">
                  共 {{ tableData.total }} 条
                </el-tag>
              </div>
            </div>
          </template>

          <vxe-table
            ref="tableRef"
            :data="tableData.records"
            :loading="tableLoading"
            border
            stripe
            max-height="600"
            align="center"
            :row-config="{ isHover: true }"
            :column-config="{ resizable: true }"
            :scroll-y="{ enabled: true }"
            class="modern-table">
            
            <vxe-column field="id" title="ID" width="80" fixed="left">
              <template #default="{ row }">
                <el-tag size="small" type="info">#{{ row.id }}</el-tag>
              </template>
            </vxe-column>
            <vxe-column field="taskTitle" title="任务标题" width="120">
              <template #default="{ row }">
                <span>{{ row.taskTitle || '-' }}</span>
              </template>
            </vxe-column>
            <vxe-column field="taskType" title="任务类型" width="120">
              <template #default="{ row }">
                <el-tag 
                  :type="getTaskTypeTag(row.taskType)" 
                  size="small" 
                  effect="dark">
                  {{ getTaskTypeLabel(row.taskType) }}
                </el-tag>
              </template>
            </vxe-column>
            
            
            
            
            
            <vxe-column field="originalFile" title="原始文件" width="150">
              <template #default="{ row }">
                <div v-if="row.originalFile" class="file-info">
                  <el-button 
                    type="primary" 
                    link 
                    size="small"
                    @click="downloadFile(row.originalFile,'1',row.originalFileName)">
                    <el-icon><Download /></el-icon>
                    下载文件
                  </el-button>
                </div>
                <span v-else class="no-file">-</span>
              </template>
            </vxe-column>
            <vxe-column field="status" title="处理状态" width="100">
              <template #default="{ row }">
                <el-tag 
                  :type="getStatusTag(row.status)" 
                  size="small" 
                  effect="dark">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </vxe-column>
            
            <vxe-column field="processedFile" title="处理文件" width="150">
              <template #default="{ row }">
                <div v-if="row.processedFile" class="file-info">
                  <el-button 
                    type="success" 
                    link 
                    size="small"
                    @click="downloadFile(row.processedFile,'2',row.processedFile)">
                    <el-icon><Download /></el-icon>
                    下载文件
                  </el-button>
                </div>
                <span v-else class="no-file">-</span>
              </template>
            </vxe-column>
            
            <vxe-column field="message" title="错误原因" min-width="200">
              <template #default="{ row }">
                <div class="message-content">
                  <el-tooltip 
                    v-if="row.message && row.message.length > 50"
                    :content="row.message" 
                    placement="top">
                    <span>{{ row.message.substring(0, 50) }}...</span>
                  </el-tooltip>
                  <span v-else>{{ row.message || '-' }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="createUser" title="创建人" width="120">
              <template #default="{ row }">
                <div class="user-info">
                  <el-avatar size="small" :src="getUserAvatar(row.createUser)">
                    {{ row.createUser?.charAt(0)?.toUpperCase() }}
                  </el-avatar>
                  <span class="user-name">{{ row.createUser || '-' }}</span>
                </div>
              </template>
            </vxe-column>
            <vxe-column field="createTime" title="创建时间" width="160">
              <template #default="{ row }">
                <div class="time-info">
                  <div class="date">{{ formatDate(row.createTime) }}</div>
                  <div class="time">{{ formatTime(row.createTime) }}</div>
                </div>
              </template>
            </vxe-column>
            
            <vxe-column field="updateTime" title="更新时间" width="160">
              <template #default="{ row }">
                <div class="time-info" v-if="row.updateTime">
                  <div class="date">{{ formatDate(row.updateTime) }}</div>
                  <div class="time">{{ formatTime(row.updateTime) }}</div>
                </div>
                <span v-else>-</span>
              </template>
            </vxe-column>
            
            <vxe-column title="操作" width="120" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-tooltip content="删除" placement="top">
                    <el-button 
                      type="danger" 
                      size="small"
                      link
                      @click="handleDelete(row)"
                      :loading="deleteLoading === row.id">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </template>
            </vxe-column>
          </vxe-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="searchForm.currentPage"
              v-model:page-size="searchForm.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="tableData.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange" />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 新增投诉弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="新增投诉"
      width="600px"
      :close-on-click-modal="false"
      class="modern-dialog">
      <el-form
        ref="addFormRef"
        :model="addForm"
        :rules="addFormRules"
        label-width="100px"
        class="add-form">
        <el-form-item label="任务标题" prop="taskTitle">
          <el-input
            v-model="addForm.taskTitle"
            placeholder="请输入任务标题"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select 
            v-model="addForm.taskType" 
            placeholder="请选择任务类型"
            style="width: 100%">
            <el-option
              v-for="item in taskTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        
        <!-- 预留投诉内容，如需启用请取消注释 -->
        <!--
        <el-form-item label="投诉内容" prop="message" class="full-width-item">
          <el-input
            v-model="addForm.message"
            type="textarea"
            :rows="4"
            placeholder="请输入投诉内容"
            maxlength="500"
            show-word-limit />
        </el-form-item>
        -->
        
        <el-form-item label="上传文件" prop="originalFile" class="full-width-item">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :limit="1"
            :auto-upload="true"
            accept=".xlsx">
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              选择文件
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传 .xlsx 文件，大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleAdd"
            :loading="addLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import getNoce from '@/plugins/getNoce.js'
import {
  DocumentCopy,
  Search,
  Refresh,
  Plus,
  List,
  Download,
  Delete,
  Upload,
  CircleCheckFilled,
  CircleCloseFilled
} from '@element-plus/icons-vue'
import moment from 'moment'

// 定义组件名称
defineOptions({
  name: 'ComplaintManagement'
})

// 响应式数据
const searchFormRef = ref(null)
const addFormRef = ref(null)
const tableRef = ref(null)

// 搜索表单
const searchForm = reactive({
  createUser: '',
  taskType: '',
  timeRange: [],
  startTime: '',
  endTime: '',
  status: '',
  currentPage: 1,
  pageSize: 10
})

// 新增表单
const addForm = reactive({
  taskType: '',
  taskTitle: '',
//   message: '',
  createUser: '', // 从用户信息获取
  originalFile: '',
  originalFileName: ''
})

// 表格数据
const tableData = reactive({
  records: [],
  total: 0,
  current: 0,
  pages: 0,
  size: 0
})

// 状态管理
const tableLoading = ref(false)
const addLoading = ref(false)
const deleteLoading = ref(null)
const addDialogVisible = ref(false)

// 文件上传
const fileList = ref([])
const uploadUrl = computed(() => window.path.omcs + 'api/complaint/upload')
const uploadHeaders = computed(() => ({
  Authorization: 'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN')
}))

// 表单验证规则
const addFormRules = reactive({
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  taskTitle: [
    { required: true, message: '请输入任务标题', trigger: 'blur' }
  ],
  originalFile: [
    { required: true, message: '请上传投诉文件(.xlsx)', trigger: 'change' }
  ]
  // message 字段如需启用，请在此添加校验规则
})

// 任务类型统一配置
const taskTypeOptions = [
  { label: '运营商投诉', value: '运营商投诉', tag: 'danger' }
]

// 获取任务类型标签样式
const getTaskTypeTag = (type) => {
  const item = taskTypeOptions.find(o => o.value === type)
  return item ? item.tag : 'info'
}

// 获取任务类型标签文本
const getTaskTypeLabel = (type) => {
  const item = taskTypeOptions.find(o => o.value === type)
  return item ? item.label : type
}

// 获取状态标签样式 （1处理中 2处理成功 3处理失败）
const statusMap = {
  '1': { label: '处理中', tag: 'warning' },
  '2': { label: '处理成功', tag: 'success' },
  '3': { label: '处理失败', tag: 'danger' }
}

const getStatusTag = (status) => {
  return statusMap[status]?.tag || 'info'
}

const getStatusLabel = (status) => {
  return statusMap[status]?.label || '处理中'
}

// 获取用户头像
const getUserAvatar = (username) => {
  // 这里可以根据实际需求返回用户头像URL
  return null
}

// 格式化日期
const formatDate = (time) => {
  return time ? moment(time).format('YYYY-MM-DD') : '-'
}

// 格式化时间
const formatTime = (time) => {
  return time ? moment(time).format('HH:mm:ss') : '-'
}

// 处理时间范围变化
const handleTimeRangeChange = () => {
  if (searchForm.timeRange && searchForm.timeRange.length === 2) {
    searchForm.startTime = searchForm.timeRange[0]
    searchForm.endTime = searchForm.timeRange[1]
  } else {
    searchForm.startTime = ''
    searchForm.endTime = ''
  }
}

// 查询数据
const fetchData = async () => {
  try {
    tableLoading.value = true
    
    // 处理时间范围
    handleTimeRangeChange()
    
    const params = {
      createUser: searchForm.createUser || undefined,
      taskType: searchForm.taskType || undefined,
      startTime: searchForm.startTime || undefined,
      endTime: searchForm.endTime || undefined,
      status: searchForm.status || undefined,
      currentPage: searchForm.currentPage,
      pageSize: searchForm.pageSize
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || params[key] === '') {
        delete params[key]
      }
    })
    
    window.api.post(
      window.path.omcs + 'api/complaint/page',
      params,
      (res) => {
        if (res.code === 200) {
          Object.assign(tableData, res.data)
        } else {
          ElMessage.error(res.msg || '查询失败')
        }
        tableLoading.value = false
      },
      (error) => {
        ElMessage.error('网络请求失败')
        tableLoading.value = false
      }
    )
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败')
    tableLoading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchForm.currentPage = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    createUser: '',
    taskType: '',
    timeRange: [],
    startTime: '',
    endTime: '',
    status: '',
    currentPage: 1,
    pageSize: 10
  })
  fetchData()
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  searchForm.currentPage = 1
  fetchData()
}

const handleCurrentChange = (page) => {
  searchForm.currentPage = page
  fetchData()
}

// 显示新增弹窗
const showAddDialog = () => {
  // 获取当前用户信息
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  addForm.createUser = userInfo.username || ''
  
  addDialogVisible.value = true
}

// 文件上传处理
const beforeUpload = (file) => {
  // 仅允许上传 Excel (.xlsx) 文件，大小不超过 10MB
  const allowedTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
  const isAllowedType = allowedTypes.includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAllowedType) {
    ElMessage.error('只能上传 .xlsx 格式的Excel文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response, file) => {
  if (response.code === 200) {
    addForm.originalFile = response.data.url || response.data
    addForm.originalFileName = file.name
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.msg || '文件上传失败')
  }
}

const handleUploadError = (error) => {
  ElMessage.error('文件上传失败')
  console.error('Upload error:', error)
}

// 新增投诉
const handleAdd = () => {
  addFormRef.value?.validate((valid) => {
    if (valid) {
      addLoading.value = true
      
      const params = {
        taskType: addForm.taskType,
        createUser: addForm.createUser,
        originalFile: addForm.originalFile,
        originalFileName: addForm.originalFileName,
        taskTitle: addForm.taskTitle
        // message 字段如启用，请解除注释
        // message: addForm.message
      }
      
      window.api.post(
        window.path.omcs + 'api/complaint/add',
        params,
        (res) => {
          if (res.code === 200) {
            ElMessage.success('投诉提交成功')
            addDialogVisible.value = false
            resetAddForm()
            fetchData()
          } else {
            ElMessage.error(res.msg || '提交失败')
          }
          addLoading.value = false
        },
        (error) => {
          ElMessage.error('网络请求失败')
          addLoading.value = false
        }
      )
    }
  })
}

// 重置新增表单
const resetAddForm = () => {
  addFormRef.value?.resetFields()
  Object.assign(addForm, {
    taskType: '',
    // message: '',
    createUser: '',
    originalFile: '',
    originalFileName: ''
  })
  fileList.value = []
}

// 删除投诉
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确认删除投诉 #${row.id} 吗？此操作不可恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    deleteLoading.value = row.id
    
    window.api.delete(
      window.path.omcs + `api/complaint/${row.id}`,
      {},
      (res) => {
        if (res.code === 200) {
          ElMessage.success('删除成功')
          fetchData()
        } else {
          ElMessage.error(res.msg || '删除失败')
        }
        deleteLoading.value = null
      },
      (error) => {
        ElMessage.error('网络请求失败')
        deleteLoading.value = null
      }
    )
  }).catch(() => {
    // 用户取消删除
  })
}

// 下载文件
const downloadFile = async (fileUrl,type,name) => {
  if (!fileUrl) {
    ElMessage.warning('文件不存在')
    return
  }
  try {
    const nonce = await getNoce.useNonce()
    const downloadUrl = `${window.path.omcs}api/complaint/download?ossUrl=${encodeURIComponent(fileUrl)}`
    const res = await axios({
      method: 'get',
      url: downloadUrl,
      responseType: 'blob',
      headers: {
        Authorization: 'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
        Once: nonce
      }
    })
    //获取文件名
    // const fileType = fileUrl.split('.').pop()
    let title = ''
    if(type == '1'){
      title = name
    }else{
      title = name.substring(name.lastIndexOf('/') + 1)
    }   
    const blob = new Blob([res.data], { type: 'application/octet-stream;charset=utf-8' })
    const disposition = res.headers['content-disposition'] || ''
    let filename = title
    const matches = /filename\*=UTF-8''(.+)$/.exec(disposition)
    if (matches && matches[1]) {
      filename = decodeURIComponent(matches[1])
    }
    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)
    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败', error)
    ElMessage.error('下载失败')
  }
}

// 监听弹窗关闭
const handleDialogClose = () => {
  resetAddForm()
}

// 组件挂载
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.complaint-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.header-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 32px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  font-size: 32px;
}

.page-description {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-section,
.action-section,
.table-section {
  width: 100%;
}

.search-card,
.action-card,
.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #303133;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-form {
  margin: 0;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.search-buttons {
  margin-left: auto;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
  min-height: 200px;
}

.modern-table :deep(.vxe-table--border) {
  border: 1px solid #ebeef5;
}

.modern-table :deep(.vxe-header--column) {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.modern-table :deep(.vxe-body--row:hover) {
  background: #f0f9ff;
}

.modern-table :deep(.vxe-table--body-wrapper) {
  max-height: 600px;
  overflow-y: auto;
}

.modern-table :deep(.vxe-table--empty-block) {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
}

.message-content {
  text-align: left;
  line-height: 1.5;
}

.file-info {
  display: flex;
  justify-content: center;
}

.no-file {
  color: #c0c4cc;
}

.result-icon {
  font-size: 18px;
}

.result-icon.success {
  color: #67c23a;
}

.result-icon.error {
  color: #f56c6c;
}

.result-pending {
  color: #c0c4cc;
}

.time-info {
  text-align: center;
}

.time-info .date {
  font-weight: 500;
  color: #303133;
}

.time-info .time {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding: 16px 0;
}

.modern-dialog :deep(.el-dialog) {
  border-radius: 12px;
}

.modern-dialog :deep(.el-dialog__header) {
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

.modern-dialog :deep(.el-dialog__title) {
  font-weight: 600;
  color: #303133;
}

.add-form {
  padding: 24px;
}

.add-form :deep(.el-form-item) {
  margin-bottom: 24px;
}

.upload-demo :deep(.el-upload) {
  display: block;
  width: 100%;
}

.upload-demo :deep(.el-upload-dragger) {
  width: 100%;
}

.dialog-footer {
  padding: 16px 24px;
  text-align: right;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

.full-width-item :deep(.el-form-item__content) {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .complaint-management {
    padding: 12px;
  }
  
  .header-content {
    padding: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .search-form :deep(.el-form-item) {
    width: 100%;
    margin-right: 0;
  }
  
  .modern-table {
    font-size: 12px;
  }
  
  .modern-table :deep(.vxe-table--body-wrapper) {
    max-height: 400px;
  }
}

/* 动画效果 */
.search-card,
.action-card,
.table-card {
  transition: all 0.3s ease;
}

.search-card:hover,
.action-card:hover,
.table-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .complaint-management {
    background: #1a1a1a;
  }
  
  .header-content {
    background: linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%);
  }
}
</style> 