<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          label-width="82px"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="通道组名称" prop="channelGroupName">
            <el-input
              v-model="formInline.channelGroupName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道组状态" prop="channelGroupState">
            <el-select
              v-model="formInline.channelGroupState"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道组类型" prop="productId">
            <el-select
              v-model="formInline.productId"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option
                v-for="item in productList"
                :key="item.productId"
                :label="item.name"
                :value="item.productId"
              ></el-option>
              <!-- <el-option label="彩信通道" value="2"></el-option>
                            <el-option label="视频短信" value="3"></el-option>
                            <el-option label="国际短信" value="4"></el-option>
                            <el-option label="语音验证码" value="6"></el-option>
                            <el-option label="语音通知" value="7"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item label="通道类型" prop="productId">
            <el-select
              v-model="formInline.channelType"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="主通道" value="1"></el-option>
              <el-option label="备用通道" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道号" prop="channelCode">
            <el-input
              v-model="formInline.channelCode"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="5g通道" prop="excludeChannelGroup">
            <el-select
              v-model="formInline.excludeChannelGroup"
              clearable
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="启用" :value="false"></el-option>
              <el-option label="不启用" :value="true"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item>
                      
                    </el-form-item> -->
        </el-form>
      </div>
      <div>
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload"
          >重置</el-button
        >
        <el-button type="primary" plain style="" @click="addtongdao"
          >添加通道组</el-button
        >
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->

        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
          @save="save"
        >
          <template #tableButtons>
            <el-button type="primary" @click="delPasss" v-if="selectId.length > 0"
              >批量删除通道</el-button
            >
            <el-button type="primary" @click="addPasss" v-if="selectId.length > 0"
              >批量添加通道</el-button
            >
            <el-button
              type="primary"
              @click="updatePasss"
              v-if="selectId.length > 0"
              >批量修改通道</el-button
            >
          </template>
        </table-tem>
        <!--分页-->
        <!-- <el-col
          :xs="24"
          :sm="24"
          :md="24"
          :lg="24"
          class="page"
          slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right"
        > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

        <!-- </el-col> -->
        <!-- 表格和分页结束 -->
      </div>

      <!-- 添加通道组 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="addUserDialog"
        :width="titleWidth[titWidth]"
        :close-on-click-modal="false"
      >
        <el-steps :active="addUserSteps" simple style="margin-bottom: 26px">
          <el-step title="基础信息填写">
            <template #icon>
              <el-icon><EditPen /></el-icon>
            </template>
          </el-step>
          <el-step title="通道配置">
            <template #icon>
              <el-icon><UploadFilled /></el-icon>
            </template>
          </el-step>
        </el-steps>
        <!-- 步骤1内容 -->
        <div v-if="addUserSteps == 1">
          <el-form
            :model="addUserStepForm.formData"
            :rules="addUserStepForm.formRule"
            ref="addUserStepForm"
            label-width="120px"
            style="padding: 0 40px 0 20px"
          >
            <el-form-item label="通道组名称" prop="channelGroupName">
              <el-input
                v-model="addUserStepForm.formData.channelGroupName"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="channelGroupState">
              <el-select
                v-model="addUserStepForm.formData.channelGroupState"
                placeholder="请选择"
                style="width: 100%"
                :disabled="flags"
              >
                <el-option label="启用" value="1"></el-option>
                <el-option label="停用" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="通道组类型" prop="productId">
              <el-select
                v-model="addUserStepForm.formData.productId"
                placeholder="请选择"
                style="width: 100%"
                :disabled="flags"
              >
                <el-option
                  v-for="item in productList"
                  :key="item.productId"
                  :label="item.name"
                  :value="item.productId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="启用5g通道"
              style="width: 360px"
              prop="excludeChannelGroup"
            >
              <el-radio-group
                v-remove-hidden
                v-model="addUserStepForm.formData.excludeChannelGroup"
              >
                <el-radio :value="false">启用</el-radio>
                <el-radio :value="true">不启用</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-form-item label="黑号验证级别" prop="blackLevelCheck" v-if="addUserStepForm.formData.isBlackCheck==1">
                    <el-checkbox-group v-model="addUserStepForm.formData.blackLevelCheck">
                        <el-checkbox label="1" >一级</el-checkbox>
                        <el-checkbox label="2" >二级</el-checkbox>
                        <el-checkbox label="3" >营销黑号库</el-checkbox>
                    </el-checkbox-group>
                </el-form-item> -->
            <el-form-item label="描述" prop="remark">
              <el-input
                type="textarea"
                v-model="addUserStepForm.formData.remark"
              ></el-input>
            </el-form-item>

            <div style="text-align: center">
              <el-button
                @click="addUserDialog = false"
                style="width: 100px; padding: 9px 0"
                >取消</el-button
              >
              <el-button
                type="primary"
                @click="submitForm('addUserStepForm')"
                style="width: 100px; padding: 9px 0"
                >下一步</el-button
              >
            </div>
          </el-form>
        </div>
        <div v-else-if="addUserSteps == 2">
          <el-form
            :model="addUserStepForms.formData"
            :rules="addUserStepForms.formRule"
            ref="addUserStepForms"
            label-width="100px"
          >
            <el-form-item
              style="
                height: 100px;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #eaeaea;
              "
              class="yunyingshang"
            >
              <div
                style="
                  height: 100px;
                  float: left;
                  width: 100px;
                  font-size: 100px;
                  color: #16a589;
                "
              >
                <el-icon><EditPen /></el-icon>
              </div>
              <div style="height: 100px; margin-left: 20px">
                <span>{{ this.addUserStepForm.formData.channelGroupName }}</span
                ><br />
                <span>{{ this.addUserStepForm.formData.remark }}</span
                ><br />
                <span
                  >更新日期：{{ this.updateTime }} &emsp;&emsp;添加人：{{
                    this.userName
                  }}</span
                >
              </div>
            </el-form-item>

            <el-form-item
              label="通道组管理"
              style="border-radius: 5px; border: 1px solid #eaeaea;"
            >
              <div style="height: 35px; width: 100%"></div>
              <el-tabs
                v-model="addUserStepForms.formData.downAddress"
                @tab-click="handleClick"
                style="margin-left: -80px; margin-right: 20px; width: 100%"
              >
                <el-tab-pane label="移动" name="first">
                  <div class="boderbottom">
                    <el-button
                      type="primary"
                      size="default"
                      style=""
                      @click="addpass(1)"
                      >添加</el-button
                    >
                    <span style="margin-left: 10px">主备通道：</span>
                    <el-radio-group
                      v-remove-hidden
                      v-model="backupy"
                      @change="changebackupy"
                    >
                      <el-radio value="0">主用通道</el-radio>
                      <el-radio value="1">备用通道</el-radio>
                    </el-radio-group>
                  </div>
                  <div style="margin-bottom: 40px">
                      <el-table
                        border
                        :data="tableDataObjs.tableDatas"
                        v-loading="tableDataObjs.loading2"
                        style="width: 100%; margin-bottom: 20px"
                      >
                        <!-- <el-table-column
                          v-for="(item, index) in tableDataObjs.tableLabels"
                          :key="index"
                          :label="item.showName"
                          :fixed="item.fixed"
                          :width="item.width"
                        >
                          <template #default="props">
                            {{
                              props.row[item.prop]
                            }}
                          </template>
                        </el-table-column> -->
                        <el-table-column label="编号">
                          <template #default="props">
                            <span>{{ props.row.channelCode }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="通道类型">
                          <template #default="props">
                           <div>
                            <span v-if="props.row.channelType == 1">全网</span>
                            <span v-else-if="props.row.channelType == 2">省网</span>
                           </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="是否为备用通道">
                          <template #default="props">
                           <div>
                            <span v-if="props.row.backup == 0">主用通道</span>
                            <span v-else-if="props.row.backup == 1">备用通道</span>
                           </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="权重">
                          <template #default="props">
                            <span>{{ props.row.weight }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="占比率">
                          <template #default="props">
                            <span>{{ props.row.proportion }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          v-if="tableDataObjs.tableOptions"
                          fixed="right"
                          :width="200"
                        >
                          <template #default="scope">
                            <div
                              class="elButton"
                              type="text"
                              v-for="(
                                item, index
                              ) in tableDataObjs.tableOptions"
                              :key="index"
                              :style="{ color: item.color }"
                              @click="
                                handels(
                                  item.optionMethod,
                                  scope.row,
                                  scope.$index
                                )
                              "
                            >
                              <i :class="item.icon"></i>{{ item.optionName }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                                        <el-pagination class="page_bottom" @size-change="handleSizeChanges" @current-change="handleCurrentChanges" :current-page="tableDataObjs.currentPage" :page-size="tableDataObjs.pageSize" :page-sizes="[3]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObjs.total"></el-pagination>
                                    </el-col> -->
                  </div>
                </el-tab-pane>
                <el-tab-pane label="联通" name="second">
                  <div class="boderbottom">
                    <el-button
                      type="primary"
                      size="default"
                      style=""
                      @click="addpass(2)"
                      >添加</el-button
                    >
                    <span style="margin-left: 10px">主备通道：</span>
                    <el-radio-group
                      v-remove-hidden
                      v-model="backupt"
                      @change="changebackuly"
                    >
                      <el-radio value="0">主用通道</el-radio>
                      <el-radio value="1">备用通道</el-radio>
                    </el-radio-group>
                  </div>
                  <div style="margin-bottom: 40px">
                      <el-table
                        border
                        :data="tableDataObjs.tableDatas2"
                        v-loading="tableDataObjs.loading2"
                        style="width: 100%; margin-bottom: 20px"
                      >
                        <!-- <el-table-column
                          v-for="(item, index) in tableDataObjs.tableLabels"
                          :key="index"
                          :label="item.showName"
                          :fixed="item.fixed"
                          :width="item.width"
                        >
                          <template #default="props">
                            {{
                              props.row[item.prop]
                            }}
                          </template>
                        </el-table-column> -->
                        <el-table-column label="编号">
                          <template #default="props">
                            <span>{{ props.row.channelCode }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="通道类型">
                          <template #default="props">
                           <div>
                            <span v-if="props.row.channelType == 1">全网</span>
                            <span v-else-if="props.row.channelType == 2">省网</span>
                           </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="是否为备用通道">
                          <template #default="props">
                           <div>
                            <span v-if="props.row.backup == 0">主用通道</span>
                            <span v-else-if="props.row.backup == 1">备用通道</span>
                           </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="权重">
                          <template #default="props">
                            <span>{{ props.row.weight }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="占比率">
                          <template #default="props">
                            <span>{{ props.row.proportion }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          v-if="tableDataObjs.tableOptions"
                          fixed="right"
                          :width="200"
                        >
                          <template #default="scope">
                            <div
                              class="elButton"
                              type="text"
                              v-for="(
                                item, index
                              ) in tableDataObjs.tableOptions"
                              :key="index"
                              :style="{ color: item.color }"
                              @click="
                                handels(
                                  item.optionMethod,
                                  scope.row,
                                  scope.$index
                                )
                              "
                            >
                              <i :class="item.icon"></i>{{ item.optionName }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                                            <el-pagination class="page_bottom" @size-change="handleSizeChanges2" @current-change="handleCurrentChanges2"  :current-page="tableDataObjs.currentPage2" :page-size="tableDataObjs.pageSize2"   :page-sizes="[3]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObjs.total2">
                                            </el-pagination>
                                        </el-col> -->
                  </div>
                </el-tab-pane>
                <el-tab-pane label="电信" name="third">
                  <div class="boderbottom">
                    <el-button
                      type="primary"
                      size="default"
                      style=""
                      @click="addpass(3)"
                      >添加</el-button
                    >
                    <span style="margin-left: 10px">主备通道：</span>
                    <el-radio-group
                      v-remove-hidden
                      v-model="backupd"
                      @change="changebackupd"
                    >
                      <el-radio value="0">主用通道</el-radio>
                      <el-radio value="1">备用通道</el-radio>
                    </el-radio-group>
                  </div>
                  <div style="margin-bottom: 40px">
                      <el-table
                        border
                        :data="tableDataObjs.tableDatas3"
                        v-loading="tableDataObjs.loading2"
                        style="width: 100%; margin-bottom: 20px"
                      >
                        <!-- <el-table-column
                          v-for="(item, index) in tableDataObjs.tableLabels"
                          :key="index"
                          :label="item.showName"
                          :fixed="item.fixed"
                          :width="item.width"
                        >
                          <template #default="props">
                            {{
                              props.row[item.prop]
                            }}
                          </template>
                        </el-table-column> -->
                        <el-table-column label="编号">
                          <template #default="props">
                            <span>{{ props.row.channelCode }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="通道类型">
                          <template #default="props">
                           <div>
                            <span v-if="props.row.channelType == 1">全网</span>
                            <span v-else-if="props.row.channelType == 2">省网</span>
                           </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="是否为备用通道">
                          <template #default="props">
                           <div>
                            <span v-if="props.row.backup == 0">主用通道</span>
                            <span v-else-if="props.row.backup == 1">备用通道</span>
                           </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="权重">
                          <template #default="props">
                            <span>{{ props.row.weight }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="占比率">
                          <template #default="props">
                            <span>{{ props.row.proportion }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          v-if="tableDataObjs.tableOptions"
                          fixed="right"
                          :width="200"
                        >
                          <template #default="scope">
                            <div
                              class="elButton"
                              type="text"
                              v-for="(
                                item, index
                              ) in tableDataObjs.tableOptions"
                              :key="index"
                              :style="{ color: item.color }"
                              @click="
                                handels(
                                  item.optionMethod,
                                  scope.row,
                                  scope.$index
                                )
                              "
                            >
                              <i :class="item.icon"></i>{{ item.optionName }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                                        <el-pagination class="page_bottom" @size-change="handleSizeChanges3" @current-change="handleCurrentChanges3"  :current-page="tableDataObjs.currentPage3" :page-size="tableDataObjs.pageSize3"   :page-sizes="[3]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObjs.total3">
                                        </el-pagination>
                                    </el-col> -->
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-form-item>

            <div style="text-align: center">
              <el-button
                @click="addUserDialog = false"
                style="width: 100px; padding: 9px 0"
                >取消</el-button
              >
              <el-button
                @click="beforeStep()"
                style="width: 100px; padding: 9px 0"
                >上一步</el-button
              >
              <el-button
                type="primary"
                @click="submitForm('addUserStepForms')"
                style="width: 100px; padding: 9px 0"
                >提交</el-button
              >
            </div>
          </el-form>
        </div>
      </el-dialog>

      <!-- 添加运营商通道 -->
      <el-dialog
        title="通道添加"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="formopRule"
          ref="formop"
          label-width="140px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="通道类型" prop="channelType">
            <!-- <el-select
              v-model="formop.channelType"
              placeholder="请选择通道类型"
              filterable
              class="input-w"
              @change="changeChannelType"
            >
              <el-option label="全网" value="1"></el-option>
              <el-option label="省网" value="2"></el-option>
            </el-select> -->
            <el-radio-group
              v-remove-hidden
              v-model="formop.channelType"
              @change="changeChannelType"
            >
              <el-radio value="1">全网</el-radio>
              <el-radio value="2">省网</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否为备用通道" prop="backup">
            <!-- <el-select
              v-model="formop.backup"
              placeholder="请选择"
              class="input-w"
              @change="changeBackup"
            >
              <el-option label="否" value="0"></el-option>
              <el-option label="是" value="1"></el-option>
            </el-select> -->
            <el-radio-group
              v-remove-hidden
              v-model="formop.backup"
              @change="changeBackup"
            >
              <el-radio value="0">主用通道</el-radio>
              <el-radio v-if="formop.channelType == '1'" value="1"
                >备用通道</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="添加通道" prop="channelCode">
            <el-select
              v-model="formop.channelCode"
              placeholder="请选择"
              filterable
              class="input-w"
            >
              <el-option
                v-for="(item, index) in labelpassName"
                :label="item.channelName"
                :value="item.channelCode"
                :disabled="item.disabled"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="
              (formop.channelType == '1' && formop.backup == '0') ||
              formop.backup == '1'
            "
            label="权重"
            prop="weight"
          >
            <el-input-number
              class="input-w"
              v-model="formop.weight"
              :min="1"
              :max="100"
              label="请输入权重"
            ></el-input-number>
            <!-- <el-input v-model="formop.weight" placeholder="请输入权重"></el-input> -->
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="primary" @click="submitForms('formop')"
            >提 交</el-button
          >
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </template>
      </el-dialog>

      <!-- 批量添加通道 -->
      <el-dialog
        title="通道添加"
        v-model="dialogFormVisibles"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formops"
          :rules="formopRules"
          ref="formops"
          label-width="160px"
          style="padding: 0 28px 0 20px"
        >
          <!-- <el-form-item label="选择通道类型" prop="channelType">
            <el-select v-model="formops.channelType" @change="changeAllchannel" placeholder="请选择" filterable style="width:100%">
               <el-option label="移动" value="1"></el-option>
                <el-option label="联通" value="2"></el-option>
                <el-option label="电信" value="3"></el-option>
            </el-select>
          </el-form-item>
         <el-form-item label="选择通道名称" prop="channelCode">
            <el-select v-model="formops.channelCode" placeholder="请选择" filterable style="width:100%">
               <el-option
                v-for="(item,index) in channelAllGroups"                 
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item> -->

          <el-form-item label="移动添加通道：" prop="yd">
            <el-select
              v-model="formops.yd"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsyd"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="移动添加备用通道：" prop="ydBk">
            <el-select
              v-model="formops.ydBk"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsyd"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="联通添加通道：" prop="lt">
            <el-select
              v-model="formops.lt"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupslt"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="联通添加备用通道：" prop="ltBk">
            <el-select
              v-model="formops.ltBk"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupslt"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="电信添加通道：" prop="dx">
            <el-select
              v-model="formops.dx"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsdx"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="电信添加备用通道：" prop="dxBk">
            <el-select
              v-model="formops.dxBk"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsdx"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="primary" @click="submitFormspl('formops')"
            >提 交</el-button
          >
          <el-button @click="dialogFormVisibles = false">取 消</el-button>
        </template>
      </el-dialog>

      <!-- 批量修改通道 -->
      <el-dialog
        title="修改通道"
        v-model="dialogFormVisiblesupdate"
        :close-on-click-modal="false"
        width="560px"
      >
        <el-form
          :model="formopsup"
          :rules="formopRulesup"
          ref="formopsup"
          label-width="180px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="当前通道" prop="passNum">
            <!-- <el-input v-model="formop.passNum" autocomplete="off" ></el-input> -->
            <span>{{ this.formInline.channelCode }}</span>
          </el-form-item>

          <!-- <el-form-item label="修改的通道类型" prop="channelType">
            <el-select v-model="formopsup.channelType" @change="changeAllchannel" placeholder="请选择" filterable style="width:100%">
               <el-option label="移动" value="1"></el-option>
                <el-option label="联通" value="2"></el-option>
                <el-option label="电信" value="3"></el-option>
                <el-option label="三网" value="4"></el-option>
            </el-select>
          </el-form-item> -->

          <!-- <el-form-item label="修改后的通道" prop="channelCode">
            <el-select v-model="formopsup.channelCode" placeholder="请选择通道名称" filterable style="width:100%">
               <el-option
                v-for="(item,index) in channelAll"                 
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item> -->

          <el-form-item label="移动修改后的通道：" prop="yd">
            <el-select
              v-model="formopsup.yd"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsyd"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="移动修改后的备用通道：" prop="ydBk">
            <el-select
              v-model="formopsup.ydBk"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsyd"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="联通修改后的通道：" prop="lt">
            <el-select
              v-model="formopsup.lt"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupslt"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="联通修改后的备用通道：" prop="ltBk">
            <el-select
              v-model="formopsup.ltBk"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupslt"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="电信修改后的通道：" prop="dx">
            <el-select
              v-model="formopsup.dx"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsdx"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="电信修改后的备用通道：" prop="dxBk">
            <el-select
              v-model="formopsup.dxBk"
              placeholder="请选择"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in channelAllGroupsdx"
                :label="item.channelName"
                :value="item.channelCode"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="primary" @click="submitFormsplup('formopsup')"
            >提 交</el-button
          >
          <el-button @click="dialogFormVisiblesupdate = false">取 消</el-button>
        </template>
      </el-dialog>

      <!-- 批量删除通道 -->
      <el-dialog
        title="批量删除通道"
        v-model="dialogFormVisiblesdel"
        :close-on-click-modal="false"
        width="560px"
      >
        <el-form
          :model="formopsdel"
          :rules="formopRulesdel"
          ref="formopsdel"
          label-width="180px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="当前删除的通道号是：" prop="passNum">
            <!-- <el-input v-model="formop.passNum" autocomplete="off" ></el-input> -->
            <span>{{ this.formInline.channelCode }}</span>
          </el-form-item>

          <el-form-item label="选择通道删除类型：" prop="mainChannel">
            <el-checkbox-group v-model="formopsdel.mainChannel">
              <el-checkbox label="1">移动</el-checkbox>
              <el-checkbox label="2">联通</el-checkbox>
              <el-checkbox label="3">电信</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="选择备用通道删除类型：" prop="bkChannel">
            <el-checkbox-group v-model="formopsdel.bkChannel">
              <el-checkbox label="1">移动</el-checkbox>
              <el-checkbox label="2">联通</el-checkbox>
              <el-checkbox label="3">电信</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="primary" @click="submitFormspldel('formopsdel')"
            >提 交</el-button
          >
          <el-button @click="dialogFormVisiblesdel = false">取 消</el-button>
        </template>
      </el-dialog>
    </div>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>
<script>
import ChannelView from "@/components/publicComponents/ChannelView.vue";
import TableTem from "@/components/publicComponents/TableTem.vue";
import productData from "@/utils/industrysCode.js";
export default {
  name: "ArtificialMag",
  components: {
    ChannelView,
    TableTem
  },
  data() {
    return {
      isFirstEnter: false,
      ChannelData: "", //传递通道值
      selectId: "", //批量操作选中id
      userName: "",
      updateTime: "",
      productList: [],
      labelpassName: [], //通道标签
      channelAllGroups: [], //通道
      channelAllGroupsyd: [], //通道
      channelAllGroupslt: [], //通道
      channelAllGroupsdx: [], //通道
      channelAll: [], //修改返回的通道列别
      channelGroup: [],
      channelGroup2: [],
      channelGroup3: [],
      channelGroupId: "", //编辑id
      channelGroupName: "", //编辑储存的名字
      operatorType: "", //运营商 类型
      backupy: "0", //是否为备用通道
      backupt: "0", //是否为备用通道
      backupd: "0", //是否为备用通道
      //运营商列表数据
      tableDataObjs: {
        loading2: false,
        total: 0,
        total2: 0,
        total3: 0,
        baseArray: [],
        baseArray2: [],
        baseArray3: [],
        currentPage: 1,
        pageSize: 3,
        currentPage2: 1,
        pageSize2: 3,
        currentPage3: 1,
        pageSize3: 3,
        tableDatas: [],
        tableDatas2: [],
        tableDatas3: [],
        tableLabels: [
          {
            prop: "channelCode",
            showName: "编号",
            fixed: false,
          },
          {
            prop: "channelType",
            showName: "通道类型",
            fixed: false,
            formatData: function (val) {
              if (val == 1) {
                return (val = "全网");
              } else if (val == 2) {
                return (val = "省网");
              }
            },
          },
          {
            prop: "backup",
            showName: "是否为备用通道",
            fixed: false,
            formatData: function (val) {
              if (val == "0") {
                return (val = "主用通道");
              } else if (val == "1") {
                return (val = "备用通道");
              }
            },
          },
          {
            prop: "weight",
            showName: "权重",
            fixed: false,
          },

          {
            prop: "proportion",
            showName: "占比率",
            fixed: false,
            formatData: function (val) {
              if (!val) {
                return "-";
              } else {
                return val + "%";
              }
            },
          },
          // {
          // prop:"channelName",
          // showName:'通道名称',
          // fixed:false,
          // width:"200px"
          // },
          // {
          // prop:"labelNames",
          // showName:'通道属性标签',
          // fixed:false

          // }
        ],
        tableOptions: [
          {
            optionName: "删除",
            type: "",
            size: "default",
            optionMethod: "dellogs",
            icon: "el-icon-delete",
            color: "#f56c6c",
          },
        ],
      },
      dialogStatus: "", //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      dialogFormVisibles: false, //新增通道弹出框显示隐藏
      dialogFormVisiblesupdate: false, //新增修改通道弹出框显示隐藏
      dialogFormVisiblesdel: false, //批量删除通道的弹出框
      //弹框标题
      titleMap: {
        add: "添加通道组",
        edit: "编辑通道组",
      },
      titWidth: "", //标题宽度
      titleWidth: {
        sty1: "600px",
        sty2: "900px",
      },
      dialogVisible: false, //弹出框显示隐藏
      active: 0,
      flags: "", //编辑禁止
      addUserSteps: 1, //添加用户步骤---数字
      addUserDialog: false, //弹窗显示
      //查询数据
      formInline: {
        channelGroupName: "",
        channelGroupState: "",
        productId: "",
        channelType: "1",
        excludeChannelGroup: "",
        // channelName:"",
        channelCode: "",
        currentPage: 1,
        pageSize: 10,
      },
      //查询赋值数据
      tabelAlllist: {
        channelGroupName: "",
        channelGroupState: "",
        excludeChannelGroup: "",
        productId: "",
        channelType: "1",
        // channelName:"",
        channelCode: "",
        currentPage: 1,
        pageSize: 10,
      },
      //批量删除通道
      formopsdel: {
        mainChannel: [],
        bkChannel: [],
      },
      //批量删除通道的验证
      formopRulesdel: {
        type: [{ required: true, message: "请选择通道", trigger: "change" }],
      },
      //添加运营商通道
      formop: {
        channelType: "1",
        channelCode: "",
        weight: "",
        backup: "0",
      },
      //添加通道的验证
      formopRule: {
        channelCode: [
          { required: true, message: "请选择通道", trigger: "change" },
        ],
        backup: [
          { required: true, message: "请选择通道类型", trigger: "change" },
        ],
        weight: [{ required: true, message: "请输入权重", trigger: "change" }],
      },
      //批量添加通道
      formops: {
        yd: "",
        lt: "",
        dx: "",
        ydBk: "",
        ltBk: "",
        dxBk: "",
        groupIds: "",
      },
      //批量修改通道
      formopsup: {
        yd: "",
        lt: "",
        dx: "",
        ydBk: "",
        ltBk: "",
        dxBk: "",
        groupIds: "",
      },
      //批量修改通道的验证
      formopRulesup: {
        channelCode: [
          { required: true, message: "请选择通道", trigger: "change" },
        ],
        channelType: [
          { required: true, message: "请选择通道类型", trigger: "change" },
        ],
      },
      //批量添加通道的验证
      formopRules: {
        channelCode: [
          { required: true, message: "请选择通道", trigger: "change" },
        ],
        channelType: [
          { required: true, message: "请选择通道类型", trigger: "change" },
        ],
      },
      addUserStepForm: {
        //弹窗步骤1的数据和验证
        formData: {
          channelGroupName: "",
          channelGroupState: "",
          isBlackCheck: "2",
          channelGroupInfoYd: [],
          channelGroupInfoLt: [],
          channelGroupInfoDx: [],
          // blackLevelCheck:["1","2","3"],
          productId: "",
          excludeChannelGroup: false,
          remark: "",
        },
        formRule: {
          channelGroupName: [
            { required: true, message: "通道组名称不能为空", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "长度在 1 到 20 个字符",
              trigger: ["blur", "change"],
            },
          ],
          excludeChannelGroup: [
            {
              required: true,
              message: "请选择是否启用5g通道",
              trigger: "blur",
            },
          ],
          // isBlackCheck: [
          //     { required: true, message: "请选择是否验证黑号", trigger: "change" }
          // ],
          // blackLevelCheck: [
          //     { required: true, message: "请选择黑号验证级别", trigger: "change" }
          // ],
          channelGroupState: [
            { required: true, message: "请选择通道组状态", trigger: "change" },
          ],
          productId: [
            { required: true, message: "请选择通道组类型", trigger: "change" },
          ],
          remark: [
            { required: false, message: "输入描述", trigger: "change" },
            {
              min: 1,
              max: 100,
              message: "长度在 1 到 100 个字符",
              trigger: ["blur", "change"],
            },
          ],
        },
      },
      addUserStepForms: {
        //弹窗步骤2的数据和验证
        formData: {
          downAddress: "first",
        },
        formRule: {},
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        custom: true,
        total: 0,
        id: "ArtificialMag",
        tableData: [],
        tableLabel: [
          {
            prop: "channelGroupId",
            showName: "通道组编号",
            width: "90",
          },
          {
            prop: "channelGroupName",
            showName: "通道组名称",
            width: "100",
          },
          {
            prop: "productId",
            showName: "产品类型",
            width: "100",
            formatData: function (val) {
              for (let i = 0; i < productData.productList.length; i++) {
                if (productData.productList[i].productId == val) {
                  return (val = productData.productList[i].name);
                }
              }
              // if (val == 1) {
              //     return (val = "短信产品");
              // }
              // if (val == 2) {
              //     return (val = "彩信产品");
              // }
              // if (val == 3) {
              //     return (val = "视频短信");
              // }
            },
          },
          // {
          // prop:"ydChannelCount",
          // showName:'移动通道个数',
          // showColorTag: {
          //     color: "#3BB19C"
          // }
          // },{
          // prop:"ltChannelCount",
          // showName:'联通通道个数',
          // showColorTag: {
          //     color: "#3BB19C"
          // }
          // },{
          // prop:"dxChannelCount",
          // showName:'电信通道个数',
          // showColorTag: {
          //     color: "#3BB19C"
          // }
          // },
          {
            prop: "channelGroupYd",
            showName: "移动主通道",
            Channel: true,
            width: "140",
          },
          {
            prop: "channelGroupYdBk",
            showName: "移动备用通道",
            Channel: true,
            width: "140",
          },
          {
            prop: "channelGroupLt",
            showName: "联通主通道",
            Channel: true,
            width: "140",
          },
          {
            prop: "channelGroupLtBk",
            showName: "联通备用通道",
            Channel: true,
            width: "140",
          },
          {
            prop: "channelGroupDx",
            showName: "电信主通道",
            Channel: true,
            width: "140",
          },
          {
            prop: "channelGroupDxBk",
            showName: "电信备用通道",
            Channel: true,
            width: "140",
          },
          // {
          // prop:"productUsedCount",
          // showName:'配置用户数',
          // width:'90',
          // fixed:false
          // },
          {
            prop: "excludeChannelGroup",
            showName: "5g通道",
            width: "90",
            // showCondition: {
            //   condition: "2",
            // },
            formatData: function (val) {
              if (val === true) {
                return (val = "不启用");
              }
              if (val === false) {
                return (val = "启用");
              }
            },
          },
          {
            prop: "channelGroupState",
            showName: "通道组状态",
            width: "90",
            // showCondition: {
            //   condition: "2",
            // },
            formatData: function (val) {
              if (val == 1) {
                return (val = "启用");
              }
              if (val == 2) {
                return (val = "停用");
              }
            },
          },
          {
            prop: "remark",
            showName: "描述",
            width: "180",
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "240", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [],
        conditionOption: [
          {
            contactCondition: "channelGroupState", //关联的表格属性
            contactData: "1", //关联的表格属性-值
            optionName: "停用", //按钮的显示文字
            optionMethod: "stop", //按钮的方法
            icon: "el-icon-circle-close-outline", //按钮图标
            optionButtonColor: "orange", //按钮颜色
            otherOptionName: "启用", //其他条件的按钮显示文字
            otherOptionMethod: "start", //其他条件的按钮方法
            otherIcon: "el-icon-circle-check-outline", //其他条件按钮的图标
            optionOtherButtonColor: "#16A589", //其他条件按钮的颜色
          },
          {
            contactCondition: "channelGroupState", //关联的表格属性
            contactData: "1", //关联的表格属性-值
            optionName: "编辑", //按钮的显示文字
            optionMethod: "details", //按钮的方法
            icon: "EditPen", //按钮图标
            optionButtonColor: "#16A589", //按钮颜色
          },
          {
            contactCondition: "channelGroupState", //关联的表格属性
            contactData: "2", //关联的表格属性-值
            optionName: "删除", //按钮的显示文字
            optionMethod: "dellog", //按钮的方法
            icon: "el-icon-delete", //按钮图标
            optionButtonColor: "#f56c6c", //按钮颜色
          },
          {
            contactCondition: "channelGroupState", //关联的表格属性
            contactData: "1", //关联的表格属性-值
            optionName: "用户列表",
            optionMethod: "usersty",
            icon: "Sort",
            optionButtonColor: "#16A589", //按钮颜色
          },
        ],
      },
    };
  },
  methods: {
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].channelGroupId);
      }
      this.selectId = selectId.join(","); //批量操作选中id
    },
    //批量删除通道弹出框
    delPasss() {
      if (this.formInline.channelCode == "") {
        this.$message({
          message: "请搜索具体的通道号",
          type: "warning",
        });
      } else {
        this.dialogFormVisiblesdel = true;
      }
    },
    //批量删除通道
    submitFormspldel() {
      let td = this.formopsdel.mainChannel.join(",");
      let tdby = this.formopsdel.bkChannel.join(",");
      //删除
      this.$confirms.confirmation(
        "post",
        "删除通道：" +
          this.formInline.channelCode +
          ",是否确认从通道组里删除？",
        window.path.omcs + "v3/operatingchannelgroup/delGroupChannel",
        {
          groupIds: this.selectId,
          // channelCode:this.formInline.channelCode,
          delChannelCode: this.formInline.channelCode,
          mainChannel: td,
          bkChannel: tdby,
        },
        () => {
          this.dialogFormVisiblesdel = false;
          this.gettableLIst();
        }
      );
    },
    //批量添加通道
    addPasss() {
      // if (this.formInline.channelCode == '') {
      //     this.$message({
      //         message: "请搜索具体的通道号",
      //         type: "warning"
      //     });
      // } else {
      this.dialogFormVisibles = true;
      // }

      //移动通道
      window.api.get(
        window.path.omcs + "operatingchannelinfo/list/1",
        {},
        (res) => {
          this.channelAllGroupsyd = res;
        }
      );
      //联通通道
      window.api.get(
        window.path.omcs + "operatingchannelinfo/list/2",
        {},
        (res) => {
          this.channelAllGroupslt = res;
        }
      );
      //电信通道
      window.api.get(
        window.path.omcs + "operatingchannelinfo/list/3",
        {},
        (res) => {
          this.channelAllGroupsdx = res;
        }
      );
    },
    //选中运营商类型改变
    changeAllchannel(val) {
      if (val) {
        this.formops.channelCode = "";
        window.api.get(
          window.path.omcs + "v3/operatingchannelinfo/list/" + val,
          {},
          (res) => {
            this.channelAllGroups = res;
          }
        );
      }
    },
    //批量修改通道
    updatePasss() {
      if (this.formInline.channelCode != "") {
        // window.api.post(
        //     window.path.omcs + "operatingchannelgroup/getChannelsByChannel",
        //     {
        //         channelCode:this.formInline.channelCode,
        //         channelName:this.formInline.channelName
        //     },
        //     res => {
        //         this.channelAll = res.data;
        //     }
        // );
        //移动通道
        window.api.get(
          window.path.omcs + "operatingchannelinfo/list/1",
          {},
          (res) => {
            this.channelAllGroupsyd = res;
          }
        );
        //联通通道
        window.api.get(
          window.path.omcs + "operatingchannelinfo/list/2",
          {},
          (res) => {
            this.channelAllGroupslt = res;
          }
        );
        //电信通道
        window.api.get(
          window.path.omcs + "operatingchannelinfo/list/3",
          {},
          (res) => {
            this.channelAllGroupsdx = res;
          }
        );
        this.dialogFormVisiblesupdate = true;
      } else {
        this.$message({
          message: "请搜索具体的通道号或通道名称",
          type: "warning",
        });
      }
      //  Object.assign(this.formop ,this.group);
    },
    //获取当前时间
    getNowFormatDate() {
      var date = new Date();
      var seperator1 = "-";
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var strDate = date.getDate();
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }
      var currentdate = year + seperator1 + month + seperator1 + strDate;
      return currentdate;
    },
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "v3/operatingchannelgroup/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.total;
          this.tableDataObj.tableData = res.records;
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    //表格切换
    handleClick(tab, event) {
      console.log("addUserStepForms.formData.downAddress", this.addUserStepForms.formData.downAddress)
      // console.log(tab);
      // console.log(event);
      this.tableDataObjs.currentPage = 1;
      this.tableDataObjs.pageSize = 3;
      this.tableDataObjs.currentPage2 = 1;
      this.tableDataObjs.pageSize2 = 3;
      this.tableDataObjs.currentPage3 = 1;
      this.tableDataObjs.pageSize3 = 3;
      this.setArr();
    },
    //获取通道
    getChannelList(val, type) {
      // backup
      let api = "";
      if (type) {
        api = `v3/operatingchannelgroup/channelList?operatorType=${val}&productId=${this.addUserStepForm.formData.productId}&channelType=${type}`;
      } else {
        api = `v3/operatingchannelgroup/channelList?operatorType=${val}&productId=${this.addUserStepForm.formData.productId}`;
      }
      window.api.get(window.path.omcs + api, {}, (res) => {
        if (
          this.tableDataObjs.baseArray.length == 0 &&
          this.tableDataObjs.baseArray2.length == 0 &&
          this.tableDataObjs.baseArray3.length == 0
        ) {
          this.labelpassName = res.data;
        } else {
          if (val == 1) {
            let arrYd = this.tableDataObjs.baseArray.filter((item) => {
              if (item.backup == this.formop.backup) {
                return item;
              }
            });
            for (var i = 0; i < res.data.length; i++) {
              for (var k = 0; k < arrYd.length; k++) {
                if (res.data[i].channelCode == arrYd[k].channelCode) {
                  res.data[i].disabled = true;
                }
              }
            }
            this.labelpassName = res.data;
          } else if (val == 2) {
            let arrLt = this.tableDataObjs.baseArray2.filter((item) => {
              if (item.backup == this.formop.backup) {
                return item;
              }
            });
            // let arrLt = this.tableDataObjs.baseArray2;
            for (var i = 0; i < res.data.length; i++) {
              for (var k = 0; k < arrLt.length; k++) {
                if (res.data[i].channelCode == arrLt[k].channelCode) {
                  res.data[i].disabled = true;
                }
              }
            }
            this.labelpassName = res.data;
          } else {
            let arrDx = this.tableDataObjs.baseArray3.filter((item) => {
              if (item.backup == this.formop.backup) {
                return item;
              }
            });
            // let arrDx = this.tableDataObjs.baseArray3;
            for (var i = 0; i < res.data.length; i++) {
              for (var k = 0; k < arrDx.length; k++) {
                if (res.data[i].channelCode == arrDx[k].channelCode) {
                  res.data[i].disabled = true;
                }
              }
            }
            this.labelpassName = res.data;
          }
        }
        this.formop.val = val;
      });
    },
    changeChannelType(e) {
      if (e != 1) {
        this.formop.weight = "";
        this.formop.backup = "0";
      }
      this.formop.channelCode = "";
      this.getChannelList(this.operatorType, e);
    },
    changeBackup(e) {
      if (e != 1) {
        this.formop.weight = "";
      }
      this.formop.channelCode = "";
      this.getChannelList(this.operatorType, this.formop.channelType);
    },
    //通道添加
    addpass(val) {
      this.dialogFormVisible = true;
      this.operatorType = val;
      this.getChannelList(this.operatorType, this.formop.channelType);
      // this.channelGroup = [];
      // 获取通道标签
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val;
      this.$refs.ChannelRef.ChannelClick();
    },
    // 查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    //重置
    Reload() {
      this.$refs.formInline.resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    handleSizeChanges(size) {
      //分页每一页的有几条
      this.tableDataObjs.pageSize = size;
      this.gettableLIst();
      this.setArr(this.tableDataObjs.pageSize, this.tableDataObjs.currentPage);
    },
    handleCurrentChanges: function (currentPage) {
      //分页的第几页
      this.tableDataObjs.currentPage = currentPage;
      this.gettableLIst();
      this.setArr(this.tableDataObjs.pageSize, this.tableDataObjs.currentPage);
    },
    handleSizeChanges2(size) {
      //分页每一页的有几条
      this.tableDataObjs.pageSize2 = size;
      this.setArr(
        this.tableDataObjs.pageSize2,
        this.tableDataObjs.currentPage2
      );
    },
    handleCurrentChanges2: function (currentPage) {
      //分页的第几页
      this.tableDataObjs.currentPage2 = currentPage;
      this.setArr(
        this.tableDataObjs.pageSize2,
        this.tableDataObjs.currentPage2
      );
    },
    handleSizeChanges3(size) {
      //分页每一页的有几条
      this.tableDataObjs.pageSize3 = size;
      this.setArr(
        this.tableDataObjs.pageSize3,
        this.tableDataObjs.currentPage3
      );
    },
    handleCurrentChanges3: function (currentPage) {
      //分页的第几页
      this.tableDataObjs.currentPage3 = currentPage;
      this.setArr(
        this.tableDataObjs.pageSize3,
        this.tableDataObjs.currentPage3
      );
    },
    //----------------------------------------------------------
    //添加通道组
    addtongdao() {
      this.addUserDialog = true;
      this.dialogStatus = "add";
      this.titWidth = "sty1";
      this.flags = false;
    },

    funProportionSum(arr) {
      try {
        const total = arr.reduce((sum, value) => {
          if (value.channelType == 1 && typeof value.weight === "number") {
            return sum + value.weight; // 直接返回累加值
          }
          return sum; // 默认返回当前累加值
        }, 0);
        // 检查 total 是否为数字
        if (isNaN(total)) {
          throw new Error("计算总和时出错，结果不是数字");
        }
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].channelType == 1) {
            arr[i].proportion = ((arr[i].weight / total) * 100).toFixed(2);
          }
        }
        return arr;
      } catch (error) {
        console.error("发生错误：", error.message);
      }
    },
    //set分页数组
    setArr(size, currentPage) {
      // let len;
      // if (this.addUserStepForms.formData.downAddress == 'first') {
      //     len = this.tableDataObjs.baseArray.length;//基础数据的个数移动
      // } else if(this.addUserStepForms.formData.downAddress == 'second'){
      //     len = this.tableDataObjs.baseArray2.length;//基础数据的个数联通
      // }else if(this.addUserStepForms.formData.downAddress == 'third'){
      //     len = this.tableDataObjs.baseArray3.length;//基础数据的个数电信
      // }
      // let n = size||3; //每页行数
      // let p = len % n === 0 ? len / n : Math.floor( (len / n) + 1 );//总页数

      // this.tableDataObjs.total = this.tableDataObjs.baseArray.length;
      // this.tableDataObjs.total2 = this.tableDataObjs.baseArray2.length;
      // this.tableDataObjs.total3 = this.tableDataObjs.baseArray3.length;

      // let temp = this.tableDataObjs.baseArray.slice((currentPage-1||0)*n, (currentPage-1||0)*n+n<len?(currentPage-1||0)*n+n:len);
      // let temp2 = this.tableDataObjs.baseArray2.slice((currentPage-1||0)*n, (currentPage-1||0)*n+n<len?(currentPage-1||0)*n+n:len);
      // let temp3 = this.tableDataObjs.baseArray3.slice((currentPage-1||0)*n, (currentPage-1||0)*n+n<len?(currentPage-1||0)*n+n:len);
      // this.tableDataObjs.tableDatas =temp
      // this.tableDataObjs.tableDatas2 =temp2
      // this.tableDataObjs.tableDatas3 =temp3
      // let sumy = 0;
      // let suml = 0;
      // let sumd = 0;
      // for (let i = 0; i < this.tableDataObjs.baseArray.length; i++) {
      //   if (this.tableDataObjs.baseArray[i].backup == 1 && this.tableDataObjs.baseArray[i].channelType == 1) {
      //         sumy += this.tableDataObjs.baseArray[i].weight;
      //         this.tableDataObjs.baseArray[i].proportion = ((this.tableDataObjs.baseArray[i].weight / sumy)*100).toFixed(2);
      //   }
      // }

      // this.tableDataObjs.tableDatas = this.funProportionSum(this.tableDataObjs.baseArray)
      let arrY = this.tableDataObjs.baseArray.filter((item) => {
        if (item.backup == this.backupy) {
          return item;
        }
      });
      this.tableDataObjs.tableDatas = this.funProportionSum(arrY);
      let arrL = this.tableDataObjs.baseArray2.filter((item) => {
        if (item.backup == this.backupt) {
          return item;
        }
      });
      this.tableDataObjs.tableDatas2 = this.funProportionSum(arrL);
      let arrD = this.tableDataObjs.baseArray3.filter((item) => {
        if (item.backup == this.backupd) {
          return item;
        }
      });
      this.tableDataObjs.tableDatas3 = this.funProportionSum(arrD);
    },
    changebackupy(e) {
      // this.formop.backup = e;
      if (e == 1) {
        let arrY = this.tableDataObjs.baseArray.filter((item) => {
          if (item.backup == 1) {
            return item;
          }
        });
        this.tableDataObjs.tableDatas = this.funProportionSum(arrY);
      } else {
        let arrY = this.tableDataObjs.baseArray.filter((item) => {
          if (item.backup == 0) {
            return item;
          }
        });
        this.tableDataObjs.tableDatas = this.funProportionSum(arrY);
      }
    },
    changebackuly(e) {
      // this.formop.backup = e;
      if (e == 1) {
        let arrL = this.tableDataObjs.baseArray2.filter((item) => {
          if (item.backup == 1) {
            return item;
          }
        });
        this.tableDataObjs.tableDatas2 = this.funProportionSum(arrL);
      } else {
        let arrL = this.tableDataObjs.baseArray2.filter((item) => {
          if (item.backup == 0) {
            return item;
          }
        });
        this.tableDataObjs.tableDatas2 = this.funProportionSum(arrL);
      }
    },
    changebackupd(e) {
      // this.formop.backup = e;
      if (e == 1) {
        let arrD = this.tableDataObjs.baseArray3.filter((item) => {
          if (item.backup == 1) {
            return item;
          }
        });
        this.tableDataObjs.tableDatas3 = this.funProportionSum(arrD);
      } else {
        let arrD = this.tableDataObjs.baseArray3.filter((item) => {
          if (item.backup == 0) {
            return item;
          }
        });
        this.tableDataObjs.tableDatas3 = this.funProportionSum(arrD);
      }
    },
    //添加通道列表
    submitForms(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          window.api.get(
            window.path.omcs +
              "v3/operatingchannelgroup/channelList?channelCode=" +
              this.formop.channelCode,
            {},
            (res) => {
              if (res.data[0].operatorsType == 1) {
                let yda = res.data[0];
                yda.backup = this.formop.backup;
                yda.channelType = this.formop.channelType;
                yda.weight = this.formop.weight;
                console.log(yda, "移动");
                //移动
                if (this.tableDataObjs.baseArray.length >= 1) {
                  this.tableDataObjs.baseArray.push(yda);
                  this.setArr();
                } else {
                  this.channelGroup.push(yda);
                  this.tableDataObjs.baseArray = this.channelGroup;
                  this.setArr();
                }
              } else if (res.data[0].operatorsType == 2) {
                let lta = res.data[0];
                lta.backup = this.formop.backup;
                lta.channelType = this.formop.channelType;
                lta.weight = this.formop.weight;
                //联通
                if (this.tableDataObjs.baseArray2.length >= 1) {
                  this.tableDataObjs.baseArray2.push(lta);
                  this.setArr();
                } else {
                  this.channelGroup2.push(lta);
                  this.tableDataObjs.baseArray2 = this.channelGroup2;
                  this.setArr();
                }
              } else if (res.data[0].operatorsType == 3) {
                let dxa = res.data[0];
                dxa.backup = this.formop.backup;
                dxa.channelType = this.formop.channelType;
                dxa.weight = this.formop.weight;
                //电信
                if (this.tableDataObjs.baseArray3.length >= 1) {
                  this.tableDataObjs.baseArray3.push(dxa);
                  this.setArr();
                } else {
                  this.channelGroup3.push(dxa);
                  this.tableDataObjs.baseArray3 = this.channelGroup3;
                  this.setArr();
                }
              } else {
                //三网
                if (this.addUserStepForms.formData.downAddress == "first") {
                  //三网中的移动
                  let yda = res.data[0];
                  yda.backup = this.formop.backup;
                  yda.channelType = this.formop.channelType;
                  yda.weight = this.formop.weight;
                  console.log(yda, "移动");

                  if (this.tableDataObjs.baseArray.length >= 1) {
                    this.tableDataObjs.baseArray.push(yda);
                    this.setArr();
                  } else {
                    this.channelGroup.push(yda);
                    this.tableDataObjs.baseArray = this.channelGroup;
                    this.setArr();
                  }
                } else if (
                  this.addUserStepForms.formData.downAddress == "second"
                ) {
                  //三网中的联通
                  let lta = res.data[0];
                  lta.backup = this.formop.backup;
                  lta.channelType = this.formop.channelType;
                  lta.weight = this.formop.weight;
                  if (this.tableDataObjs.baseArray2.length >= 1) {
                    this.tableDataObjs.baseArray2.push(lta);
                    this.setArr();
                  } else {
                    this.channelGroup2.push(lta);
                    this.tableDataObjs.baseArray2 = this.channelGroup2;
                    this.setArr();
                  }
                } else {
                  let dxa = res.data[0];
                  dxa.backup = this.formop.backup;
                  dxa.channelType = this.formop.channelType;
                  dxa.weight = this.formop.weight;
                  if (this.tableDataObjs.baseArray3.length >= 1) {
                    //三网中的电信
                    this.tableDataObjs.baseArray3.push(dxa);
                    this.setArr();
                  } else {
                    this.channelGroup3.push(dxa);
                    this.tableDataObjs.baseArray3 = this.channelGroup3;
                    this.setArr();
                  }
                }
              }
              this.dialogFormVisible = false;
            }
          );
        }
      });
    },
    //批量添加通道列表
    submitFormspl(formops) {
      this.$refs[formops].validate((valid) => {
        if (valid) {
          this.formops.groupIds = this.selectId;
          this.$confirms.confirmation(
            "post",
            "确认此操作吗？",
            window.path.omcs + "v3/operatingchannelgroup/addGroupChannel",
            this.formops,
            () => {
              this.gettableLIst();
              this.dialogFormVisibles = false;
            }
          );
        }
      });
    },
    //批量修改通道列表
    submitFormsplup(formopsup) {
      this.$refs[formopsup].validate((valid) => {
        if (valid) {
          this.formopsup.groupIds = this.selectId;
          // this.formopsup.oldChannelName = this.formInline.channelName;
          this.formopsup.oldChannelCode = this.formInline.channelCode;
          this.$confirms.confirmation(
            "post",
            "确认此操作吗？",
            window.path.omcs + "v3/operatingchannelgroup/batchupdateChannel",
            this.formopsup,
            () => {
              this.gettableLIst();
              this.dialogFormVisiblesupdate = false;
            }
          );
        }
      });
    },
    //通道添加和编辑 提交
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (val == "addUserStepForm") {
          //提交form1表单
          if (valid) {
            if (this.dialogStatus == "edit") {
              if (
                this.addUserStepForm.formData.channelGroupName ==
                this.channelGroupName
              ) {
                this.addUserSteps = 2;
                this.titWidth = "sty2";
              } else {
                window.api.get(
                  window.path.omcs +
                    "v3/operatingchannelgroup/checkName/" +
                    this.addUserStepForm.formData.channelGroupName,
                  {},
                  (res) => {
                    if (res.code == 400) {
                      this.$message({
                        message: res.msg,
                        type: "warning",
                      });
                    } else {
                      this.addUserSteps = 2;
                      this.titWidth = "sty2";
                    }
                  }
                );
              }
            } else {
              window.api.get(
                window.path.omcs +
                  "v3/operatingchannelgroup/checkName/" +
                  this.addUserStepForm.formData.channelGroupName,
                {},
                (res) => {
                  if (res.code == 400) {
                    this.$message({
                      message: res.msg,
                      type: "warning",
                    });
                  } else {
                    this.addUserSteps = 2;
                    this.titWidth = "sty2";
                  }
                }
              );
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        } else if (val == "addUserStepForms") {
          //提交form2表单
          if (valid) {
            if (
              this.tableDataObjs.tableDatas.length < 1 ||
              this.tableDataObjs.tableDatas2.length < 1 ||
              this.tableDataObjs.tableDatas3.length < 1
            ) {
              this.$message({
                message: "每一项至少添加一个通道！",
                type: "warning",
              });
            } else {
              //通道id
              // let channelGroupYd = [], channelGroupLt = [], channelGroupDx = [];
              //通道标签
              // let channelGroupYdLabelName=[], channelGroupLtLabelName=[] ,channelGroupDxLabelName=[] ;
              //通道名称
              // let channelGroupYdName=[],channelGroupLtName=[],channelGroupDxName=[];
              // for (let i = 0; i < this.tableDataObjs.baseArray.length; i++) {
              //     channelGroupYd.push(this.tableDataObjs.baseArray[i].channelCode + ":" + this.tableDataObjs.baseArray[i].backup);
              // }
              // for (let j = 0; j < this.tableDataObjs.baseArray2.length; j++) {
              //     channelGroupLt.push(this.tableDataObjs.baseArray2[j].channelCode + ":" + this.tableDataObjs.baseArray2[j].backup);
              // }
              // for (let k = 0; k < this.tableDataObjs.baseArray3.length; k++) {
              //     channelGroupDx.push(this.tableDataObjs.baseArray3[k].channelCode + ":" + this.tableDataObjs.baseArray3[k].backup);
              // }
              this.addUserStepForm.formData.channelGroupInfoYd =
                this.tableDataObjs.baseArray.map((item) => {
                  return {
                    channelCode: item.channelCode,
                    backup: item.backup,
                    channelType: item.channelType,
                    weight: item.weight,
                  };
                });
              this.addUserStepForm.formData.channelGroupInfoLt =
                this.tableDataObjs.baseArray2.map((item) => {
                  return {
                    channelCode: item.channelCode,
                    backup: item.backup,
                    channelType: item.channelType,
                    weight: item.weight,
                  };
                });
              this.addUserStepForm.formData.channelGroupInfoDx =
                this.tableDataObjs.baseArray3.map((item) => {
                  return {
                    channelCode: item.channelCode,
                    backup: item.backup,
                    channelType: item.channelType,
                    weight: item.weight,
                  };
                });
              // this.addUserStepForm.formData.channelGroupYd = channelGroupYd.join(',')
              // this.addUserStepForm.formData.channelGroupLt = channelGroupLt.join(',')
              // this.addUserStepForm.formData.channelGroupDx = channelGroupDx.join(',')

              if (this.dialogStatus == "edit") {
                this.addUserStepForm.formData.channelGroupId =
                  this.channelGroupId;
                let aa = {};
                aa = Object.assign(aa, this.addUserStepForm.formData);
                // aa.blackLevelCheck = aa.blackLevelCheck.join(',')
                this.$confirms.confirmation(
                  "put",
                  "确认此操作吗？",
                  window.path.omcs + "v3/operatingchannelgroup",
                  aa,
                  () => {
                    this.gettableLIst();
                    this.addUserDialog = false;
                  }
                );
              } else {
                let aa = {};
                aa = Object.assign(aa, this.addUserStepForm.formData);
                // aa.blackLevelCheck = aa.blackLevelCheck.join(',')
                this.$confirms.confirmation(
                  "post",
                  "确认此操作吗？",
                  window.path.omcs + "v3/operatingchannelgroup",
                  aa,
                  () => {
                    this.gettableLIst();
                    this.addUserDialog = false;
                  }
                );
              }
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        } else {
          if (valid) {
            console.log("submit!!");
          } else {
            console.log("error submit!!");
            return false;
          }
        }
      });
    },
    beforeStep() {
      //上一步
      this.addUserSteps = 1;
      this.titWidth = "sty1";
    },
    delState: function (val, index) {
      //操作状态功能（启用停用）
      this.$confirms.confirmation(
        "put",
        "确认执行启用或停用操作吗？",
        window.path.omcs + "v3/operatingchannelgroup",
        {
          channelGroupId: val.row.channelGroupId,
          channelGroupState: index,
        },
        () => {
          this.gettableLIst();
        }
      );
    },
    delRow(val) {
      //删除
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据, 是否继续？",
        window.path.omcs + "v3/operatingchannelgroup/" + val.row.channelGroupId,
        {},
        () => {
          this.gettableLIst();
        }
      );
    },
    //编辑
    detailsForm(val) {
      this.addUserDialog = true;
      this.dialogStatus = "edit";
      this.titWidth = "sty1";
      this.flags = true;
      this.channelGroupId = val.row.channelGroupId;
      window.api.get(
        window.path.omcs +
          "v3/operatingchannelgroup/selectById/" +
          this.channelGroupId,
        {},
        (res) => {
          // if (res.data.blackLevelCheck == []) {
          //     this.addUserStepForm.formData.blackLevelCheck = [];
          // } else {
          //     this.addUserStepForm.formData.blackLevelCheck = res.data.blackLevelCheck;
          // }
          this.addUserStepForm.formData.isBlackCheck = "2";
          this.userName = res.data.updateName;
          this.updateTime = res.data.updateTime;
          this.$nextTick(() => {
            this.$refs.addUserStepForm.resetFields();
            Object.assign(this.addUserStepForm.formData, res.data); //编辑框赋值
            if (res.data.excludeChannelGroup === null) {
              this.addUserStepForm.formData.excludeChannelGroup = true;
            } else {
              this.addUserStepForm.formData.excludeChannelGroup =
                res.data.excludeChannelGroup;
            }
            this.addUserStepForm.formData.productId = val.row.productId;
            this.tableDataObjs.baseArray = res.data.channelGroupInfoYd;
            this.tableDataObjs.total = this.tableDataObjs.baseArray.length;
            this.tableDataObjs.baseArray2 = res.data.channelGroupInfoLt;
            this.tableDataObjs.total2 = this.tableDataObjs.baseArray2.length;
            this.tableDataObjs.baseArray3 = res.data.channelGroupInfoDx;
            this.tableDataObjs.total3 = this.tableDataObjs.baseArray3.length;
            this.setArr();
          });
        }
      );
      this.channelGroupName = val.row.channelGroupName;

      // let channelGroup = [], channelGroup2 = [], channelGroup3 = [];
      // //移动通道赋值
      // let channelGroupYd, channelGroupYdBk, channelGroupYdLabelName, channelGroupYdName;
      // if (val.row.channelGroupYd != null && val.row.channelGroupYd != '') {
      //     channelGroupYd = val.row.channelGroupYd.split(",");
      //     // 移动主通道的赋值
      //     for (let i = 0; i < channelGroupYd.length; i++) {
      //         channelGroup.push({
      //             channelCode: channelGroupYd[i], backup: '1'
      //             // labelNames:channelGroupYdLabelName[i],
      //             // channelName:channelGroupYdName[i]
      //         })
      //     }
      // } else {
      //     channelGroupYd = [];
      // }
      // if (val.row.channelGroupYdBk != null && val.row.channelGroupYdBk != '') {
      //     channelGroupYdBk = val.row.channelGroupYdBk.split(",");
      //     // 移动备用通道赋值
      //     for (let i = 0; i < channelGroupYdBk.length; i++) {
      //         channelGroup.push({
      //             channelCode: channelGroupYdBk[i], backup: '2'
      //             // labelNames:channelGroupYdLabelName[i],
      //             // channelName:channelGroupYdName[i]
      //         })
      //     }
      // } else {
      //     channelGroupYdBk = [];
      // }
      // //改
      // this.tableDataObjs.baseArray = channelGroup;
      // this.setArr();
      // this.tableDataObjs.total = this.tableDataObjs.baseArray.length;

      // //联通通道赋值
      // let channelGroupLt, channelGroupLtBk, channelGroupLtLabelName, channelGroupLtName;
      // if (val.row.channelGroupLt != null && val.row.channelGroupLt != '') {
      //     channelGroupLt = val.row.channelGroupLt.split(",");
      //     //联通主通道
      //     for (let k = 0; k < channelGroupLt.length; k++) {
      //         channelGroup2.push({
      //             channelCode: channelGroupLt[k], backup: '1'
      //             // labelNames:channelGroupLtLabelName[k],
      //             // channelName:channelGroupLtName[k]
      //         })
      //     }
      // } else {
      //     channelGroupLt = [];
      // }

      // if (val.row.channelGroupLtBk != null && val.row.channelGroupLtBk != '') {
      //     channelGroupLtBk = val.row.channelGroupLtBk.split(",");
      //     //联通备用通道
      //     for (let k = 0; k < channelGroupLtBk.length; k++) {
      //         channelGroup2.push({
      //             channelCode: channelGroupLtBk[k], backup: '2'
      //             // labelNames:channelGroupLtLabelName[k],
      //             // channelName:channelGroupLtName[k]
      //         })
      //     }
      // } else {
      //     channelGroupLtBk = [];
      // }

      // // if (val.row.channelGroupLtLabelName != null ) {
      // //      channelGroupLtLabelName = val.row.channelGroupLtLabelName.split(";");
      // // }else{
      // //     channelGroupLtLabelName = [];
      // // }
      // // if ( val.row.channelGroupLtName != null ) {
      // //      channelGroupLtName = val.row.channelGroupLtName.split(",");
      // // }else{
      // //     channelGroupLtName = [];
      // // }
      // this.tableDataObjs.baseArray2 = channelGroup2;
      // this.setArr();
      // this.tableDataObjs.total2 = this.tableDataObjs.baseArray2.length;

      // //电信通道赋值
      // let channelGroupDx, channelGroupDxBk, channelGroupDxLabelName, channelGroupDxName;
      // if (val.row.channelGroupDx != null && val.row.channelGroupDx != '') {
      //     channelGroupDx = val.row.channelGroupDx.split(",");
      //     //电信主通道
      //     for (let j = 0; j < channelGroupDx.length; j++) {
      //         channelGroup3.push({
      //             channelCode: channelGroupDx[j], backup: '1'
      //             // labelNames:channelGroupDxLabelName[j],
      //             // channelName:channelGroupDxName[j]
      //         })
      //     }
      // } else {
      //     channelGroupDx = [];
      // }
      // if (val.row.channelGroupDxBk != null && val.row.channelGroupDxBk != '') {
      //     channelGroupDxBk = val.row.channelGroupDxBk.split(",");
      //     //电信备用通道
      //     for (let j = 0; j < channelGroupDxBk.length; j++) {
      //         channelGroup3.push({
      //             channelCode: channelGroupDxBk[j], backup: '2'
      //             // labelNames:channelGroupDxLabelName[j],
      //             // channelName:channelGroupDxName[j]
      //         })
      //     }
      // } else {
      //     channelGroupDxBk = [];
      // }
      // // if (val.row.channelGroupDxLabelName != null ) {
      // //      channelGroupDxLabelName = val.row.channelGroupDxLabelName.split(";");
      // // }else{
      // //     channelGroupDxLabelName = [];
      // // }
      // // if ( val.row.channelGroupDxName != null ) {
      // //      channelGroupDxName = val.row.channelGroupDxName.split(",");
      // // }else{
      // //     channelGroupDxName = [];
      // // }

      // this.tableDataObjs.baseArray3 = channelGroup3;
      // this.setArr();
      // this.tableDataObjs.total3 = this.tableDataObjs.baseArray3.length;
    },
    handels(val1, val2, index) {
      this.$confirm("此操作将删除当前数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (val1 == "dellogs") {
            if (this.addUserStepForms.formData.downAddress == "first") {
              for (
                let index = 0;
                index < this.tableDataObjs.baseArray.length;
                index++
              ) {
                if (
                  this.tableDataObjs.baseArray[index].channelCode ==
                    val2.channelCode &&
                  this.tableDataObjs.baseArray[index].backup == val2.backup
                ) {
                  this.tableDataObjs.baseArray.splice(index, 1);
                }
              }
              // this.tableDataObjs.baseArray.splice(index, 1);
              // this.tableDataObjs.tableDatas.splice(index, 1);
              // this.tableDataObjs.total = this.tableDataObjs.baseArray.length;
              this.setArr();
            } else if (this.addUserStepForms.formData.downAddress == "second") {
              for (
                let index = 0;
                index < this.tableDataObjs.baseArray2.length;
                index++
              ) {
                if (
                  this.tableDataObjs.baseArray2[index].channelCode ==
                    val2.channelCode &&
                  this.tableDataObjs.baseArray2[index].backup == val2.backup
                ) {
                  this.tableDataObjs.baseArray2.splice(index, 1);
                }
              }
              // this.tableDataObjs.baseArray2.splice(index, 1);
              // this.tableDataObjs.tableDatas2.splice(index, 1);
              // this.tableDataObjs.total2 = this.tableDataObjs.baseArray2.length;
              this.setArr();
            } else {
              for (
                let index = 0;
                index < this.tableDataObjs.baseArray3.length;
                index++
              ) {
                if (
                  this.tableDataObjs.baseArray3[index].channelCode ==
                    val2.channelCode &&
                  this.tableDataObjs.baseArray3[index].backup == val2.backup
                ) {
                  this.tableDataObjs.baseArray3.splice(index, 1);
                }
              }
              // this.tableDataObjs.baseArray3.splice(index, 1);
              // this.tableDataObjs.tableDatas3.splice(index, 1);
              // this.tableDataObjs.total3 = this.tableDataObjs.baseArray3.length;
              this.setArr();
            }
          }
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    handelOptionButton: function (val) {
      if (val.methods == "details") {
        this.detailsForm(val);
      }
      if (val.methods == "start") {
        this.delState(val, 1);
      }
      if (val.methods == "stop") {
        if (val.row.productUsedCount > 0) {
          this.$message({
            message: "此通道含有正在使用的用户，不能停用此通道！",
            type: "warning",
          });
        } else {
          this.delState(val, 2);
        }
      }
      if (val.methods == "dellog") {
        this.delRow(val);
      }
      if (val.methods == "usersty") {
        if (val.row.productUsedCount == 0) {
          this.$message({
            message: "用户配置数为0，不能迁移通道！",
            type: "warning",
          });
        } else {
          this.$router.push({
            path: "/userList",
            query: {
              channelGroupId: val.row.channelGroupId,
            },
          });
        }
      }
    },
  },
  mounted() {
    window.api.get(window.path.upms + "user/userInfo", null, (res) => {
      // this.userName = res.data.username;
    });
  },
  activated() {
    // console.log(11);
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.productList = productData.productList;
        this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.productList = productData.productList;
      this.gettableLIst();
    });
  },
  // computed(){ },
  watch: {
    //监听查询框对象的变化
    // tabelAlllist: {
    //     handler() {
    //         this.gettableLIst();
    //     },
    //     deep:true,//深度监听
    //     immediate: true
    // },

    //添加运营商通道关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields();
        this.operatorType = "";
      }
    },
    //批量添加通道关闭
    dialogFormVisibles(val) {
      if (val == false) {
        this.$refs.formops.resetFields();
      }
    },
    //批量删除弹出框
    dialogFormVisiblesdel(val) {
      if (val == false) {
        this.$refs.formopsdel.resetFields();
      }
    },
    //批量修改通道关闭
    dialogFormVisiblesupdate(val) {
      if (val == false) {
        this.$refs.formopsup.resetFields();
      }
    },
    //监听是否验证黑号
    // "addUserStepForm.formData.isBlackCheck":function(val){
    //     if (val!=1) {
    //         this.addUserStepForm.formData.blackLevelCheck = [];
    //     }
    // },
    //通道添加 编辑关闭
    addUserDialog(val) {
      if (val == false) {
        this.titWidth = "sty1";
        this.addUserSteps = 1;
        this.tableDataObjs.tableDatas = [];
        this.tableDataObjs.tableDatas2 = [];
        this.tableDataObjs.tableDatas3 = [];
        this.tableDataObjs.baseArray = [];
        this.tableDataObjs.baseArray2 = [];
        this.tableDataObjs.baseArray3 = [];
        this.tableDataObjs.total = 0;
        this.tableDataObjs.total2 = 0;
        this.tableDataObjs.total3 = 0;
        this.channelGroup = [];
        this.channelGroup2 = [];
        this.channelGroup3 = [];
        this.addUserStepForms.formData.downAddress = "first";
        this.userName = "";
        this.updateTime = "";
        this.$nextTick(() => {
          this.$refs.addUserStepForm.resetFields();
          for (let k in this.addUserStepForm.formData) {
            this.addUserStepForm.formData[k] = "";
          }
          this.addUserStepForm.formData.isBlackCheck = "2";
          // this.addUserStepForm.formData.blackLevelCheck = ["1","2","3"];
        });
        for (let k in this.addUserStepForm.formData) {
          this.addUserStepForm.formData[k] = "";
        }
      }
    },
  },
};
</script>
<style>
.yunyingshang .el-form-item__content {
  margin-left: 10px !important;
}
</style>

<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 20px 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.sensitive {
  /* border-bottom: 1px solid #ccc; */
  /* margin: 40px 0 20px 10px; */
  margin-top: 16px;
}

/* .boderbottom{
    border-bottom: 1px solid rgb(212, 211, 211);
    height: 50px;
} */
</style>

