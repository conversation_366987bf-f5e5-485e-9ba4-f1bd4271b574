import { ElMessage, ElMessageBox } from "element-plus";

function addIp(data) {
    ElMessageBox.confirm(
        '确定要新增IP吗？ ',
        '提示',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        // fun();
        window.api.post(
            window.path.omcs + "operatorClientIp",
            data,
            (res) => {
                if (res.code == 200) {
                    //   dialogVisible.value = false;
                    ElMessage.success("新增成功");
                    //   emit("handleClose",dialogVisible.value);
                    // gettableLIst();
                } else {
                    ElMessage.error(res.msg);
                }
            }
        );
    }).catch((err) => {
        ElMessage({
            type: 'info',
            message: '取消操作',
        });
    })
}

export { addIp }