# 📋 通道重启计划 - channelId格式更新

## 🎯 更新说明

将submitForm方法中的批量创建逻辑从多次API调用改为单次API调用，通道ID以逗号分隔的字符串形式提交。

## 🔧 技术变更

### 修改前（多次API调用）
```javascript
// 为每个选中的通道分别创建计划
const promises = form.channelIds.map(channelId => {
    const submitData = {
        channelId: channelId,  // 单个通道ID
        beginTime: form.beginTime,
        endTime: form.endTime,
        nextTime: form.nextTime,
        connectNum: form.connectNum,
        maxNum: form.maxNum,
        status: form.status
    };

    return new Promise((resolve, reject) => {
        window.api.post(window.path.omcs + 'operatingchannelrestarttask', submitData, 
            (res) => res.code == 200 ? resolve(res) : reject(new Error(res.msg)),
            (error) => reject(error)
        );
    });
});

// 等待所有请求完成
await Promise.all(promises);
```

### 修改后（单次API调用）
```javascript
// 新增操作：将多个通道ID以逗号分隔的形式提交
const submitData = {
    channelId: form.channelIds.join(','), // 将数组转换为逗号分隔的字符串
    beginTime: form.beginTime,
    endTime: form.endTime,
    nextTime: form.nextTime,
    connectNum: form.connectNum,
    maxNum: form.maxNum,
    status: form.status
};

window.api.post(window.path.omcs + 'operatingchannelrestarttask', submitData, (res) => {
    if (res.code == 200) {
        getRestartPlanList();
        ElMessage.success('成功创建通道重启计划');
        dialogVisible.value = false;
    } else {
        ElMessage.error(res.msg);
    }
}, (error) => {
    console.error('添加失败:', error);
    ElMessage.error('添加失败，请稍后重试');
});
```

## 📊 数据格式变化

### API请求数据格式

#### **单个通道**
```javascript
{
  "channelId": "CH001",
  "beginTime": "2024-01-01 10:00:00",
  "endTime": "2024-01-01 18:00:00",
  "nextTime": "2024-01-02 10:00:00",
  "connectNum": 100,
  "maxNum": 200,
  "status": 0
}
```

#### **多个通道（逗号分隔）**
```javascript
{
  "channelId": "CH001,CH002,CH003",  // 多个通道ID以逗号分隔
  "beginTime": "2024-01-01 10:00:00",
  "endTime": "2024-01-01 18:00:00",
  "nextTime": "2024-01-02 10:00:00",
  "connectNum": 100,
  "maxNum": 200,
  "status": 0
}
```

## ✅ 优势

### 1. 性能提升
- **减少网络请求**: 从N次请求减少到1次请求
- **降低服务器压力**: 避免并发请求对服务器的冲击
- **提升响应速度**: 单次请求响应更快

### 2. 简化逻辑
- **移除Promise.all**: 不需要处理多个异步请求
- **简化错误处理**: 只需要处理单个请求的成功/失败
- **减少代码复杂度**: 逻辑更加清晰简单

### 3. 用户体验
- **统一反馈**: 所有通道的创建结果统一反馈
- **简化提示**: 不需要显示具体创建了多少个计划
- **一致性**: 与编辑操作保持一致的处理方式

## 🔄 实现细节

### 数组转字符串
```javascript
// 将通道ID数组转换为逗号分隔的字符串
const channelIdString = form.channelIds.join(',');

// 示例：
// form.channelIds = ['CH001', 'CH002', 'CH003']
// channelIdString = 'CH001,CH002,CH003'
```

### 后端处理
后端需要支持接收逗号分隔的channelId字符串，并解析为多个通道进行批量处理。

```javascript
// 后端解析示例（伪代码）
const channelIds = req.body.channelId.split(',');
channelIds.forEach(channelId => {
    // 为每个通道创建重启计划
    createRestartPlan({
        ...req.body,
        channelId: channelId.trim()
    });
});
```

## 📋 注意事项

1. **后端兼容性**: 确保后端API支持接收逗号分隔的channelId字符串
2. **数据验证**: 后端需要验证channelId字符串的格式和有效性
3. **错误处理**: 如果某个通道创建失败，需要明确的错误信息
4. **事务处理**: 考虑是否需要事务处理，确保数据一致性

## 🚀 测试建议

### 1. 功能测试
- 测试单个通道的创建
- 测试多个通道的批量创建
- 测试空通道数组的处理
- 测试无效通道ID的处理

### 2. 边界测试
- 测试大量通道的批量创建
- 测试包含特殊字符的通道ID
- 测试重复通道ID的处理

### 3. 错误测试
- 测试网络异常情况
- 测试后端返回错误的处理
- 测试部分通道创建失败的情况

---

**更新完成**: submitForm方法现在使用逗号分隔的channelId字符串进行批量创建，提升了性能和用户体验。
