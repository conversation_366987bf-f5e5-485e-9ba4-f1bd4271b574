<template>
  <div style="background: #fff; padding: 15px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon>
          智能通道组管理</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline">
          <el-form-item label="通道组名称" label-width="82px">
            <el-input
              v-model="formInline.user"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道组状态" label-width="82px">
            <el-select
              v-model="formInline.state"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道组类型" label-width="82px">
            <el-select
              v-model="formInline.type"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="短信" value="1"></el-option>
              <el-option label="国际短信" value="2"></el-option>
              <el-option label="彩信" value="3"></el-option>
              <el-option label="语音" value="4"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style="" @click="Query">查询</el-button>
        <el-button type="primary" plain style="" @click="Reload"
          >重置</el-button
        >
      </div>
      <div class="Signature-search-fun">
        <!-- <span class="Signature-list-header">智能通道列表</span>            -->
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
        ></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="1"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tableData.length"
            >
            </el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>

      <!-- 编辑弹框 -->
      <el-dialog title="编辑短信签名" v-model="SigDialogVisible" width="520px">
        <el-form :model="form" label-width="80px" style="padding-right: 14px">
          <el-form-item label="签名内容">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="签名类型" class="sig-type">
            <el-radio-group v-model="form.resource">
              <el-radio label="1"
                ><span class="sig-type-title-tips">公司名全称或简称：</span
                >须提供营业执照截图</el-radio
              >
              <el-radio label="2"
                ><span class="sig-type-title-tips">APP名全称或简称：</span
                >须提供任一应用商店的下载链接与该应用商店的后台管理截图</el-radio
              >
              <el-radio label="3"
                ><span class="sig-type-title-tips"
                  >工信部备案的网站名全称或简称：</span
                >提供域名备案服务商的后台备案截图</el-radio
              >
              <el-radio label="4"
                ><span class="sig-type-title-tips"
                  >公众号或小程序名全称或简称：</span
                >须提供公众号（小程序）微信开放平台截图</el-radio
              >
              <el-radio label="5"
                ><span class="sig-type-title-tips">商标全称或简称：</span
                >须提供商标注册证书截图</el-radio
              >
              <el-radio label="6"
                ><span class="sig-type-title-tips">其他</span></el-radio
              >
            </el-radio-group>
          </el-form-item>
          <span class="audit-criteria"
            ><el-icon><el-icon-document /></el-icon> 查看审核标准</span
          >
          <el-form-item label="备注内容">
            <el-input
              type="textarea"
              v-model="form.desc"
              placeholder="请输入备注内容"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="SigDialogVisible = false"
              >确 定</el-button
            >
            <el-button @click="SigDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 编辑弹框 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
export default {
  components: {
    TableTem,
    ElIconLxEmoji,
    ElIconDocument,
  },
  name: 'ChannellMag',
  data() {
    return {
      SigDialogVisible: false, //弹出框显示隐藏
      form: {
        name: '',
        resource: '',
        desc: '',
      },
      formInline: {
        user: '',
        state: '',
        type: '',
      },
      tableDataObj: {
        //列表数据
        tableData: [
          {
            date: '512000',
            names: '国际短信通道组00001',
            chanpin: '国际短信 ',
            yidong: '5',
            liantong: '5',
            dianxin: '8',
            peizhi: '15',
            tongdao: '启用',
            miaoshu: '0000',
          },
        ],
        tableLabel: [
          {
            prop: 'date',
            showName: '通道组编号',
            fixed: false,
          },
          {
            prop: 'names',
            showName: '通道组名称',
            fixed: false,
          },
          {
            prop: 'chanpin',
            showName: '产品类型',
            fixed: false,
          },
          {
            prop: 'yidong',
            showName: '移动通道个数',
            fixed: false,
          },
          {
            prop: 'liantong',
            showName: '联通通道个数',
            fixed: false,
          },
          {
            prop: 'dianxin',
            showName: '电信通道个数',
            fixed: false,
          },
          {
            prop: 'peizhi',
            showName: '配置用户数',
            fixed: false,
          },
          {
            prop: 'tongdao',
            showName: '通道组状态',
            fixed: false,
          },
          {
            prop: 'miaoshu',
            showName: '描述',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '240', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-success',
          },
          {
            optionName: '启用',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-success',
          },
          {
            optionName: '用户迁移设置',
            type: '',
            size: 'mini',
            optionMethod: 'usersty',
            icon: 'el-icon-error',
          },
        ],
      },
    }
  },
  methods: {
    Query() {},
    Reload() {},
    detailsRow() {
      this.SigDialogVisible = true
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow()
      }
      if (val.methods == 'usersty') {
        this.$router.push('/userList')
      }
    },
    handleSizeChange(size) {
      this.pagesize = size
    },
    handleCurrentChange: function (currentPage) {
      this.currentPage = currentPage
    },
  },
  watch: {},
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Signature-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
