import { createRouter, createWebHashHistory } from 'vue-router'

// 导入所有路由模块
import dashboardRoutes from './modules/dashboard'
import userRoutes from './modules/user'
import operationRoutes from './modules/operation'
import auditRoutes from './modules/audit'
import channelRoutes from './modules/channel'
import securityRoutes from './modules/security'
import systemRoutes from './modules/system'
import statisticsRoutes from './modules/statistics'
import taskRoutes from './modules/task'
import complaintRoutes from './modules/complaint'
import messageRoutes from './modules/message'
import fiveGRoutes from './modules/fiveG'
import yuexinRoutes from './modules/yuexin'
import shopRoutes from './modules/shop'
import sendTaskRoutes from './modules/sendTask'
import financeRoutes from './modules/finance'
import pushRoutes from './modules/push'
import errorRoutes from './modules/errors'

// 合并所有子路由
const childRoutes = [
  ...dashboardRoutes,
  ...userRoutes,
  ...operationRoutes,
  ...auditRoutes,
  ...channelRoutes,
  ...securityRoutes,
  ...systemRoutes,
  ...statisticsRoutes,
  ...taskRoutes,
  ...complaintRoutes,
  ...messageRoutes,
  ...fiveGRoutes,
  ...yuexinRoutes,
  ...shopRoutes,
  ...sendTaskRoutes,
  ...financeRoutes,
  ...pushRoutes,
]

// 基础路由配置
const routes = [
  {
    path: '/',
    redirect: '/home',
    meta: {
      requireAuth: true,
    },
  },
  {
    path: '/',
    component: () => import('@/components/common/Home.vue'),
    meta: { title: '自述文件' },
    children: childRoutes,
  },
  ...errorRoutes,
  {
    path: '/:catchAll(.*)',
    name: "NotFound",
    redirect: '/404',
  },
]

export default createRouter({
  history: createWebHashHistory(),
  routes,
}) 