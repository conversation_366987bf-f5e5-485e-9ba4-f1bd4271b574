<template>
    <div>
        <!-- 查询条件 -->
        <div>
            <el-form label-width="82px" :inline="true" ref="formRef" :model="queryForm"
                :rules="queryRules" class="demo-form-inline">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="queryForm.username" placeholder="请输入用户名" class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="发送时间" prop="timeRange">
                    <el-date-picker 
                        :shortcuts="pickerOptions.shortcuts"
                        v-model="queryForm.timeRange" 
                        type="datetimerange" 
                        @change="handleTimeChange"
                        range-separator="至" 
                        start-placeholder="开始日期" 
                        end-placeholder="结束日期" 
                        align="right"
                        value-format="YYYY-MM-DD HH:mm:ss">
                    </el-date-picker>
                </el-form-item>
                <div style="margin-bottom: 18px">
                    <el-button type="primary" plain @click="handleSearch">查询</el-button>
                    <el-button type="primary" plain @click="handleReset">重置</el-button>
                </div>
            </el-form>
        </div>
        
        <!-- 失败统计列表 -->
        <div class="Mail-table" style="padding-bottom: 40px">
            <div style="margin-bottom: 10px; font-weight: bold; font-size: 16px;">
                失败统计列表
                <span v-if="tableData.total > 0" style="font-size: 12px; color: #666; margin-left: 10px;">
                    (共 {{ tableData.total }} 条记录)
                </span>
            </div>
            <el-table v-loading="tableData.loading" border :data="tableData.list" style="width: 100%">
                <el-table-column label="错误码" width="150" align="center">
                    <template #default="{ row }">
                        <span style="font-weight: bold; color: #e74c3c;">{{ row.originalCode }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="失败原因" min-width="200">
                    <template #default="{ row }">
                        <el-tooltip v-if="row.reason && row.reason.length > 30" 
                            :content="row.reason" placement="top">
                            <span>{{ row.reason.substring(0, 30) + '...' }}</span>
                        </el-tooltip>
                        <span v-else>{{ row.reason || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="失败数量" width="120" align="center">
                    <template #default="{ row }">
                        <span style="font-weight: bold; color: #f39c12;">{{ row.num }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="失败比例" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getPercentTagType(row.percent)" effect="dark">
                            {{ row.percent }}%
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
            
            <!-- 无数据提示 -->
            <!-- <div v-if="!tableData.loading && tableData.list.length === 0" 
                style="text-align: center; padding: 40px; color: #999;">
                <el-empty description="暂无失败统计数据" />
            </div> -->
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onActivated } from 'vue'
import { ElMessage } from 'element-plus'
import moment from 'moment'

// 定义组件名称
defineOptions({
  name: 'RmsErrorCode'
})

// 引用
const formRef = ref(null)

// 查询表单
const queryForm = reactive({
  username: '',
  beginTime: '',
  endTime: '',
  timeRange: []
})

// 表单验证规则
const queryRules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  timeRange: [
    { required: true, message: '请选择发送时间', trigger: 'change' }
  ]
})

// 时间选择器配置
const pickerOptions = reactive({
  shortcuts: [
    {
      text: '最近一周',
      value: [moment().subtract(7, 'day').toDate(), new Date()]
    },
    {
      text: '最近一个月', 
      value: [moment().subtract(30, 'day').toDate(), new Date()]
    },
    {
      text: '最近三个月',
      value: [moment().subtract(90, 'day').toDate(), new Date()]
    }
  ]
})

// 表格数据
const tableData = reactive({
  list: [],
  total: 0,
  loading: false
})

// 数据加载状态
const isFirstEnter = ref(true)

// 根据失败比例获取标签类型
const getPercentTagType = (percent) => {
  const num = parseFloat(percent)
  if (num >= 80) return 'danger'
  if (num >= 50) return 'warning'
  if (num >= 20) return 'info'
  return 'success'
}

// 处理时间变化
const handleTimeChange = (val) => {
  if (val && val.length === 2) {
    queryForm.beginTime = val[0]
    queryForm.endTime = val[1]
  } else {
    queryForm.beginTime = ''
    queryForm.endTime = ''
  }
}

// 搜索
const handleSearch = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      getFailureData()
    }
  })
}

// 重置
const handleReset = () => {
  formRef.value?.resetFields()
  Object.assign(queryForm, {
    username: '',
    beginTime: '',
    endTime: '',
    timeRange: []
  })
  // 重置后不自动查询，需要用户手动点击查询
}

// 获取失败统计数据
const getFailureData = () => {
  if (!queryForm.username) {
    ElMessage.warning('请输入用户名')
    return
  }
  
  if (!queryForm.beginTime || !queryForm.endTime) {
    ElMessage.warning('请选择发送时间')
    return
  }

  tableData.loading = true
  
  const params = {
    username: queryForm.username,
    beginTime: queryForm.beginTime,
    endTime: queryForm.endTime
  }

  window.api.post(
    window.path.omcs + 'statistics/sendVideoInfo/searchData',
    params,
    (res) => {
      tableData.loading = false
      if (res.code == 200) {
        tableData.list = res.data || []
        tableData.total = res.data ? res.data.length : 0
        
        if (tableData.list.length === 0) {
          ElMessage.info('暂无失败统计数据')
        }
      } else {
        ElMessage.error(res.msg || '获取数据失败')
        tableData.list = []
        tableData.total = 0
      }
    },
    (err) => {
      tableData.loading = false
      ElMessage.error('网络请求失败')
      tableData.list = []
      tableData.total = 0
    }
  )
}

// 组件挂载时初始化
onMounted(() => {
  isFirstEnter.value = true
  // 不自动加载数据，需要用户输入用户名后查询
})

// 组件激活时处理（优化tab切换）
onActivated(() => {
  if (!isFirstEnter.value) {
    // 如果不是第一次进入，且有查询条件，则刷新数据
    if (queryForm.username && queryForm.beginTime && queryForm.endTime) {
      getFailureData()
    }
  } else {
    isFirstEnter.value = false
  }
})
</script>

<style scoped></style>