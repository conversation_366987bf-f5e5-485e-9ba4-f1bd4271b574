<template>
  <div>
    <div class="Top_title">短信补发</div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="用户名" label-width="80px" prop="clientName">
              <el-input
                v-model="formInline.clientName"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="消息ID" label-width="80px" prop="msgid">
              <el-input
                v-model="formInline.msgid"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="发送内容" label-width="80px" prop="content">
              <el-input
                v-model="formInline.content"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号码" label-width="80px" prop="mobile">
              <el-input
                v-model="formInline.mobile"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="通道ID" label-width="80px" prop="channelId">
              <el-input
                v-model="formInline.channelId"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" label-width="80px" prop="smsStatus">
              <el-select v-model="formInline.smsStatus" class="input-w">
                <el-option label="待返回" value="3"></el-option>
                <el-option label="发送失败" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="失败代码"
              label-width="80px"
              prop="originalCode"
            >
              <el-input
                v-model="formInline.originalCode"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="发送时间" label-width="80px" prop="time1">
              <el-date-picker
                class="input-w"
                v-model="formInline.time1"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                :clearable="false"
                @change="getTimeOperating1"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="boderbottom">
          <el-button type="primary" plain style="" @click="ListSearch"
            >查询</el-button
          >
          <el-button type="primary" plain style="" @click="Reset('formInline')"
            >重置</el-button
          >
        </div>
        <div class="sensitive-fun">
          <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">短信补发列表</span> -->
          <el-button type="primary" @click="Reissue('条件补发')"
            >条件补发</el-button
          >
          <el-button type="primary" @click="Reissues('条件去除')"
            >条件去除</el-button
          >
          <el-button
            v-if="valueReissueId.length > 0"
            type="primary"
            @click="Reissue('短信补发')"
            >批量补发</el-button
          >
          <el-button
            v-if="valueReissueId.length > 0"
            type="primary"
            @click="Reissues('批量去除')"
            >批量去除</el-button
          >
        </div>
        <div class="Mail-table" style="padding-bottom: 40px; margin-top: 10px">
          <!-- 表格和分页开始 -->
          <table-tem
            :tableDataObj="tableDataObj"
            @handelOptionButton="handelOptionButton"
            @handelSelection="handelSelection"
          ></table-tem>
          <!--分页-->
          <template v-slot:pagination>
            <el-col
              :xs="24"
              :sm="24"
              :md="24"
              :lg="24"
              class="page"
              style="background: #fff; padding: 10px 0; text-align: right"
            >
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="formInlines.currentPage"
                :page-size="formInlines.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableDataObj.tablecurrent.total"
              >
              </el-pagination>
            </el-col>
          </template>
          <!-- 表格和分页结束 -->
        </div>
      </div>
      <!-- 批量补发 -->
      <el-dialog
        :title="roleDialog"
        v-model="addCharacterPopUps"
        width="25%"
        :close-on-click-modal="false"
        :before-close="handleClose1"
      >
        <div class="addC" style="padding-right: 50px">
          <el-form
            :inline="true"
            ref="formAdd"
            :rules="rules"
            :model="formAdd"
            class="demo-form-inline"
          >
            <el-form-item label="选择通道" label-width="100px" prop="aisle">
              <el-select
                v-model="formAdd.aisle"
                placeholder="请选择"
                style="width: 280px"
              >
                <!-- <el-option label="智能通道组" value="0"></el-option> -->
                <el-option label="人工通道组" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-if="formAdd.aisle == '1'"
              label="通道组名称"
              label-width="100px"
              prop="ChannelSelection"
            >
              <el-select
                v-model="formAdd.ChannelSelection"
                style="width: 280px"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in options"
                  :key="item.channelGroupId"
                  :label="item.channelGroupName"
                  :value="item.channelGroupId"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button
              @click="addCharacterPopUps = false"
              v-if="StatusT == true"
              >取 消</el-button
            >
            <el-button type="primary" v-if="StatusT == true" @click="RoleADD()"
              >确 定</el-button
            >
            <el-button type="primary" v-if="StatusT == false" :loading="true"
              >操作进行中，请稍等</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
export default {
  name: 'SmSReplacement',
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      rules: {
        aisle: [{ required: true, message: '通道不能为空', trigger: 'change' }],
        ChannelSelection: [
          { required: true, message: '通道组不能为空', trigger: 'change' },
        ],
      },
      formAdd: {
        ChannelSelection: '',
        // 选择通道类型
        aisle: '1',
      },
      roleDialog: '',
      // 查询列表数据
      formInline: {
        clientName: '',
        msgid: '',
        content: '',
        mobile: '',
        channelId: '',
        originalCode: '',
        smsStatus: '2',
        sendBeginTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00',
        sendEndTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59',
        time1: [
          new Date().toLocaleDateString().split('/').join('-'),
          new Date().toLocaleDateString().split('/').join('-'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      //储存查询列表数据
      formInlines: {
        clientName: '',
        msgid: '',
        content: '',
        mobile: '',
        channelId: '',
        originalCode: '',
        smsStatus: '2',
        sendBeginTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00',
        sendEndTime:
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59',
        time1: [
          new Date().toLocaleDateString().split('/').join('-'),
          new Date().toLocaleDateString().split('/').join('-'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储二次弹出框状态
      StatusT: true,
      // 存储通道
      options: [],
      // 存储iD
      valueReissueId: '',
      addCharacterPopUps: false,
      //角色列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabelExpand: [{ prop: 'content', showName: '短信内容' }],
        tableLabel: [
          {
            prop: 'clientName',
            showName: '用户名',
            fixed: false,
          },
          {
            prop: 'mobile',
            showName: '手机号',
            width: '120',
            fixed: false,
          },
          {
            prop: 'chargeNum',
            showName: '计费条数',
            width: '70',
            fixed: false,
          },
          {
            prop: 'msgid',
            showName: '消息ID',
            fixed: false,
          },
          {
            prop: 'channelId',
            showName: '通道ID',
            width: '80',
            fixed: false,
          },
          {
            prop: 'ext',
            showName: '扩展号',
            width: '100',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '提交时间',
            fixed: false,
          },
          {
            prop: 'sendTime',
            showName: '发送时间',
            fixed: false,
          },
          {
            prop: 'smsStatus',
            showName: '状态',
            fixed: false,
            width: '80',
            showCondition: {
              condition: '2',
            },
            formatData: function (val) {
              return val == '3' ? '待返回' : '发送失败'
            },
          },
          {
            prop: 'originalCode',
            showName: '状态代码',
            fixed: false,
          },
          {
            prop: 'reportTime',
            showName: '回执时间',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          height: 600, //是否固定表头
          isExpand: true, //是否是折叠的
          isDefaultExpand: true,
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '80', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: '', //关联的表格属性
            contactData: '', //关联的表格属性-值
            optionName: '补发', //按钮的显示文字
            optionMethod: 'Reissue', //按钮的方法
            icon: 'el-icon-circle-close-outline', //按钮图标
            optionButtonColor: '', //按钮颜色
            otherOptionName: '补发', //其他条件的按钮显示文字
            otherOptionMethod: 'Reissue', //其他条件的按钮方法
            otherIcon: 'el-icon-circle-check-outline', //其他条件按钮的图标
            optionOtherButtonColor: '', //其他条件按钮的颜色
          },
        ],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.sirs + 'smsreplacement/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.total = res.total
        }
      )
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      this.formInline.sendBeginTime =
        new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00'
      this.formInline.sendEndTime =
        new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59'
      Object.assign(this.formInlines, this.formInline)
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].smsInfoId)
      }
      this.valueReissueId = selectId.join(',')
    },
    // 补发
    Reissue(val) {
      if (val == '条件补发' && !this.formInline.clientName) {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '条件补发需先填写用户名',
        })
      } else {
        this.roleDialog = val
        this.addCharacterPopUps = true
      }
    },
    // 条件去除
    Reissues(val) {
      if (val == '条件去除' && !this.formInline.clientName) {
        this.$message({
          type: 'error',
          duration: '2000',
          message: '条件去除需先填写用户名',
        })
      } else if (val == '批量去除') {
        this.$confirms.confirmation(
          'post',
          '确定以当前条件去除?',
          window.path.sirs + 'smsreplacement/batchreplacement',
          {
            smsInfoIds: this.valueReissueId,
            isRealSend: '2',
          },
          (res) => {
            this.InquireList()
          }
        )
      } else {
        this.formInline.isRealSend = '2'
        // this.formInline.pageSize=null
        this.$confirms.confirmation(
          'post',
          '确定以当前条件去除?',
          window.path.sirs + 'smsreplacement/conditionsreplacement',
          this.formInline,
          (res) => {
            this.InquireList()
          }
        )
      }
    },
    // 关闭弹出层
    handleClose1(done) {
      this.addCharacterPopUps = false
    },
    // 提交
    RoleADD() {
      this.$refs['formAdd'].validate((valid) => {
        if (valid) {
          if (this.roleDialog == '短信补发') {
            if (this.formAdd.aisle == '0') {
              this.formAdd.ChannelSelection = '0'
            }
            this.$confirm('确定短信补发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.StatusT = false
                window.api.post(
                  window.path.sirs + 'smsreplacement/batchreplacement',
                  {
                    smsInfoIds: this.valueReissueId,
                    channelGroupId: this.formAdd.ChannelSelection,
                    isRealSend: '1',
                  },
                  (res) => {
                    this.StatusT = true
                    this.addCharacterPopUps = false
                    this.InquireList()
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除',
                })
              })
            // this.$confirms.confirmation('post','确定短信补发?',window.path.sirs+'smsreplacement/batchreplacement?ids='+this.valueReissueId+"&channelGroupId="+this.formAdd.ChannelSelection,{},res =>{
            //     this.addCharacterPopUps=false
            //     this.InquireList();
            // })
          } else if (this.roleDialog == '条件补发') {
            let objform = {}
            Object.assign(objform, this.formInline)
            if (this.formAdd.aisle == '0') {
              this.formAdd.ChannelSelection = '0'
            }
            // objform.pageSize = null
            objform.channelGroupId = this.formAdd.ChannelSelection + ''
            objform.isRealSend = '1'
            this.$confirm('确定以当前条件补发?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
            })
              .then(() => {
                this.StatusT = false
                window.api.post(
                  window.path.sirs + 'smsreplacement/conditionsreplacement',
                  objform,
                  (res) => {
                    this.StatusT = true
                    this.addCharacterPopUps = false
                    this.InquireList()
                    if (res.code == 200) {
                      this.$message({
                        type: 'success',
                        message: '操作成功!',
                      })
                    } else {
                      this.$message({
                        type: 'error',
                        message: res.msg,
                      })
                    }
                  }
                )
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消删除',
                })
              })
            // this.$confirms.confirmation('post','确定以当前条件补发?',window.path.sirs+'smsreplacement/conditionsreplacement',objform
            // ,res =>{
            //     this.addCharacterPopUps=false
            //     // this.InquireList();
            // })
          }
        } else {
          return false
        }
      })
    },
    // 用户操作
    handelOptionButton: function (val) {
      if (val.methods == 'Reissue') {
        this.roleDialog = '短信补发'
        this.valueReissueId = val.row.smsInfoId
        this.addCharacterPopUps = true
      }
    },
    // 发送时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.sendBeginTime = val[0] + ' 00:00:00'
        this.formInline.sendEndTime = val[1] + ' 23:59:59'
      } else {
        this.formInline.sendBeginTime =
          new Date().toLocaleDateString().split('/').join('-') + ' 00:00:00'
        this.formInline.sendEndTime =
          new Date().toLocaleDateString().split('/').join('-') + ' 23:59:59'
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
    },
  },
  created() {
    window.api.post(
      window.path.sirs + 'smsreplacement/allsmschannelgroup',
      {},
      (res) => {
        this.options = res.data
      }
    )
  },
  watch: {
    // 监听弹出层关闭
    addCharacterPopUps: function (val) {
      if (val == false) {
        this.valueReissueId = ''
        this.formAdd.ChannelSelection = ''
        this.formAdd.aisle = ''
        this.InquireList()
        this.$refs.formAdd.resetFields() //清空表单
      }
    },
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
</style>
