<template>
  <div class="login-wrap" :style="{ backgroundImage: `url(${loginBg})` }">
    <Login1 v-show="loginFlag" />
    <div v-show="!loginFlag" class="ms-login">
      <div class="ms-title">助通科技后台管理系统</div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="0px"
        class="ms-content"
      >
        <el-form-item prop="codeUsername">
          <el-input v-model.trim="ruleForm.codeUsername" placeholder="用户名">
            <template v-slot:prepend>
              <el-icon><User /></el-icon>
            </template>
            <!-- <el-button slot="prepend" icon="el-icon-lx-people"></el-button> -->
          </el-input>
        </el-form-item>
        <el-form-item
          prop="codePhone"
          :rules="
            filter_rules({
              required: true,
              type: 'mobile',
              message: '请输入正确手机号',
            })
          "
        >
          <el-input
            placeholder="手机号"
            v-model.trim="ruleForm.codePhone"
            @input="ncrj"
          >
            <!-- <el-button slot="prepend" icon="el-icon-phone"></el-button> -->
            <template v-slot:prepend>
              <el-icon><PhoneFilled /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :class="!ncflag ? 'ncrj' : ''" prop="nc">
          <div style="height: 30px">
            <div id="nc"></div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="verify"
          prop="codeObtain"
          style="position: relative"
        >
          <el-input
            style="width: 235px"
            placeholder="验证码"
            @keyup.enter="submitForm('ruleForm')"
            v-model.trim="ruleForm.codeObtain"
          >
            <!-- <el-button slot="prepend" icon="el-icon-info"></el-button> -->
            <template v-slot:prepend>
              <el-icon><InfoFilled /></el-icon>
            </template>
            <template v-slot:append v-if="nmb == 120">
              <el-button
                style="
                  background: #409eff;
                  color: #fff;
                  height: 32px;
                  position: absolute;
                  left: 13px;
                "
                @click="getPhoneCode(ruleForm.codePhone)"
                >获取验证码</el-button
              >
            </template>
            <template v-slot:append v-else>
              <el-button
                disabled
                style="
                  background: #eee;
                  color: #555;
                  height: 32px;
                  position: absolute;
                  left: 13px;
                "
                >重新获取{{ nmb }}</el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="password_ji" @change="handelremberName"
            >记住用户名</el-checkbox
          >
        </el-form-item>
        <div class="login-btn">
          <el-button type="primary" @click="submitForm('ruleForm')"
            >登录</el-button
          >
        </div>
        <p class="login-tips">
          Tips : 请填写正确的用户名和手机号。
          <span style="margin-left: 20px" @click="passwordLogin()"
            >密码登录</span
          >
        </p>
        <p class="login-tips" style="color: #409eff;cursor: pointer;" @click="signQuery()"
            >签名实名查询入口>></p
          >
      </el-form>
    </div>
    <div class="version">
      <span style="color: rgb(238, 238, 238, 0.32)">{{ version }}</span>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import axios from 'axios'
import loginBg from '../../assets/images/login-bg.jpg'
import Login1 from './login1.vue'
import bus from '../common/bus'
import { version } from '../../utils/version'
export default {
  components: {
    Login1,
  },
  data: function () {
    var validateUsername = (rule, value, callback) => {
      var myregex = /^[0-9A-Za-z_]+$/
      if (value) {
        if (myregex.test(value)) {
          callback()
        } else {
          callback('用户名格式仅支持 大小写英文、数字、_')
        }
      } else {
        callback('请输入用户名')
      }
    }
    var ruleCode = (rule, value, callback) => {
      var myregex = /^[0-9]*$/
      if (value) {
        if (myregex.test(value)) {
          callback()
        } else {
          callback('请输入正确验证码')
        }
      } else {
        callback('验证码不能为空')
      }
    }
    return {
      loginBg,
      nmb: 120,
      password_ji: false,
      ClickTrue: true,
      ncflag: false,
      verify: false,
      token: '',
      loginFlag: true,
      ruleForm: {
        codeUsername: '',
        codePhone: '',
        codeObtain: '',
      },
      version: version,
      rules: {
        codeUsername: [
          {
            required: true,
            validator: validateUsername,
            trigger: ['change', 'blur'],
          },
        ],
        codeObtain: [
          { required: true, validator: ruleCode, trigger: ['change', 'blur'] },
        ],
      },
      registerData: {
        appKey: '',
        phone: '',
        scene: '',
        sessionId: '',
        sig: '',
        token: '',
        username: '',
      },
    }
  },
  created() {
    $on(bus, 'loginPh', (target) => {
      // console.log(target,'000');
      this.loginFlag = target
    })
    this.getCodeUsername()
  },
  mounted() {
    let pwdFlag = localStorage.getItem('password_rember')
    this.password_ji = JSON.parse(pwdFlag)
    this.init()
    // this.h5()
  },
  methods: {
    ncrj() {
      // console.log(this.registerForms.phone,'lll');
      this.registerData.phone = this.ruleForm.codePhone
      this.ncflag = true
      // console.log(a.length);
      // if(phone.length == 11){

      // }else{
      //   this.ncflag = false
      //   this.verify = false
      // }
      // if(/^[1-9][\d]*$/.test(this.registerForms.phone)){
      //   clearTimeout(this.timer);
      //   this.timer = setTimeout(() => {
      //       this.ncflag = true
      //   }, 200);
      // }else {
      //   this.$message({
      //     message: "请输入正确手机号",
      //     type: "warning",
      //   });
      // }
      // if(){

      // }
      // this.ncflag = true
    },
    //人机验证
    init() {
      // 实例化nc
      var that = this
      // 实例化nc
      AWSC.use('nc', function (state, module) {
        // 初始化
        window.nc = module.init({
          // 应用类型标识。它和使用场景标识（scene字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的appkey字段值，请务必正确填写。
          appkey: 'FFFF0N00000000009C2B',
          //使用场景标识。它和应用类型标识（appkey字段）一起决定了滑动验证的业务场景与后端对应使用的策略模型。您可以在人机验证控制台的配置管理页签找到对应的scene值，请务必正确填写。
          scene: 'nc_register',
          // 声明滑动验证需要渲染的目标ID。
          renderTo: 'nc',
          // 添加 passive 选项
          passive: true,
          //前端滑动验证通过时会触发该回调参数。您可以在该回调参数中将会话ID（sessionId）、签名串（sig）、请求唯一标识（token）字段记录下来，随业务请求一同发送至您的服务端调用验签。
          success: function (data) {
            // window.console && console.log(data.sessionId);
            // window.console && console.log(data.sig);
            // window.console && console.log(data.token);
            that.registerData.appKey = this.appkey
            that.registerData.phone = that.ruleForm.codePhone
            that.registerData.scene = this.scene
            that.registerData.sessionId = data.sessionId
            that.registerData.sig = data.sig
            that.registerData.token = data.token
            that.registerData.username = that.ruleForm.codeUsername
            that.verify = true //  拖动状态，判断滑块是否拖动完成
            // window.nc.reset();
            // that.ncflag = false
            // console.log(that.registerData);
          },
          // 滑动验证失败时触发该回调参数。
          fail: function (failCode) {
            window.console && console.log(failCode, 'code')
          },
          // 验证码加载出现异常时触发该回调参数。
          error: function (errorCode) {
            window.console && console.log(errorCode)
          },
        })
      })
    },
    //获取cookie
    getCookie: function () {
      if (document.cookie.length > 0) {
        var arr = document.cookie.split('; ') //这里显示的格式需要切割一下自己可输出看下 // console.log(arr)
        // console.log(arr,'arr');
        for (var i = 0; i < arr.length; i++) {
          var arr2 = arr[i].split('=') //再次切割 //判断查找相对应的值
          if (arr2[0] == 'userName') {
            this.ruleForm.codeUsername = arr2[1] //保存到保存数据的地方
            // console.log(this.username);
            // console.log(this.username );
          } else if (arr2[0] == 'userPwd') {
            this.ruleForm.codePhone = arr2[1]
          }
        }
        if (this.ruleForm.codePhone != '') {
          this.ncflag = true
        }
        // this.init();
      }
    },
    setCookie(c_name, c_pwd, exdays) {
      var exdate = new Date()
      exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays) //保存的天数
      document.cookie =
        'userName=' + c_name + ';path=/;expires=' + exdate.toLocaleString()
      document.cookie =
        'userPwd=' + c_pwd + ';path=/;expires=' + exdate.toLocaleString()
    },
    //删除cookie
    clearCookie: function () {
      this.setCookie('', '', -1) //修改2值都为空，天数为负1天就好了
    },
    passwordLogin() {
      this.loginFlag = true
    },
    signQuery() {
      this.$router.push('/signatureList')
    },
    getPhoneCode(val) {
      if (this.ClickTrue) {
        if (val && /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(val)) {
          this.ClickTrue = false
          axios({
            method: 'post',
            url: window.path.upms + 'code/smsCode',
            headers: {},
            // data:{
            //     username:this.ruleForm.codeUsername,
            //     phone:this.ruleForm.codePhone
            // },
            data: this.registerData,
            withCredentials: false,
          }).then((res) => {
            if (res.data.code == 200) {
              --this.nmb
              const timer = setInterval((res) => {
                --this.nmb
                if (this.nmb < 1) {
                  this.nmb = 120
                  this.ClickTrue = true
                  clearInterval(timer)
                }
              }, 1000)
              this.$message({
                type: 'success',
                duration: 2000,
                message: '验证码已发送至手机!',
              })
            } else {
              this.ClickTrue = true
              this.ncflag = true
              window.nc.reset()
              if (res.data.msg == '验证码未失效，请失效后再次申请') {
                --this.nmb
                const timer = setInterval((res) => {
                  --this.nmb
                  if (this.nmb < 1) {
                    this.nmb = 120
                    clearInterval(timer)
                  }
                }, 1000)
              }
              this.$message({
                type: 'error',
                duration: 2000,
                message: res.data.msg,
              })
            }
          })
        } else {
          this.$message({
            message: '请输入正确用户名和手机号',
            type: 'warning',
          })
        }
      } else {
        this.$message({
          message: '请先填写正确用户名与手机号',
          type: 'warning',
        })
      }
    },
    //记住用户名
    handelremberName(val) {
      localStorage.setItem('password_rember', val)
      if (val) {
        let formName = {
          name: this.ruleForm.codeUsername,
          phone: this.ruleForm.codePhone,
        }
        localStorage.setItem('formName', JSON.stringify(formName))
      } else {
        localStorage.removeItem('formName')
      }
    },
    getCodeUsername() {
      let objName = JSON.parse(localStorage.getItem('formName'))
      if (objName) {
        this.ruleForm.codeUsername = objName.name
        this.ruleForm.codePhone = objName.phone
        this.ncflag = true
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.password_ji) {
            localStorage.setItem('password_rember', this.password_ji)
            let formName = {
              name: this.ruleForm.codeUsername,
              phone: this.ruleForm.codePhone,
            }
            localStorage.setItem('formName', JSON.stringify(formName))
          } else {
            localStorage.setItem('password_rember', this.password_ji)
            localStorage.removeItem('formName')
          }
          // if (this.password_ji == true) {
          //   localStorage.setItem("password_rember", this.password_ji);
          //   this.setCookie(
          //     this.ruleForm.codeUsername,
          //     this.ruleForm.codePhone,
          //     7
          //   );
          // } else {
          //   this.clearCookie();
          // }
          // localStorage.setItem("ms_username", this.ruleForm.username);
          axios({
            method: 'post',
            url: window.path.api + 'auth/mobile/token',
            headers: {
              Authorization: 'Basic cGlnOnBpZw==',
            },
            params: {
              mobile:
                this.ruleForm.codePhone +
                '@' +
                this.ruleForm.codeObtain +
                '@' +
                this.ruleForm.codeUsername,
            },
            withCredentials: false,
          }).then((res) => {
            localStorage.removeItem("nonce_list");
            //  localStorage.setItem('ms_username',this.ruleForm.username);
            window.nc.reset()
            this.ruleForm.codeObtain = ''
            let d = new Date()
            d.setTime(d.getTime() + 1000 * 60 * 240)
            let expires = 'expires=' + d.toUTCString()
            this.token = res.data.access_token
            document.cookie =
              'ZTADMIN_TOKEN=' +
              res.data.access_token +
              ';path=/' +
              ';expires=' +
              expires
            axios({
              method: 'get',
              url: window.path.upms + 'user/info',
              headers: {
                Authorization: 'Bearer ' + res.data.access_token,
              },
              withCredentials: false,
            }).then((ress) => {
              
              if (
                ress.data.data.roles[0] == 'ROLE_MC' || //管理商
                ress.data.data.roles[0] == 'ROLE_SU' || //子用户
                ress.data.data.roles[0] == 'ROLE_EU' || //终端用户
                ress.data.data.roles[0] == 'ROLE_EUW' //线上终端
              ) {
                this.$message({
                  message: '用户名输入有误！',
                  type: 'warning',
                })
              } else {
                this.$router.push('/')
                sessionStorage.setItem('path', '/home')
                // localStorage.setItem("appVersion",version);
                // let path = sessionStorage.getItem("path");
                // if (path) {
                //   this.$router.push(path);
                // } else {
                //   this.$router.push("/");
                // }
              }
            })
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
}
</script>

<style scoped>
.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  /* background-image: url(../../assets/images/login-bg.jpg); */
  background-size: 100%;
}
.ms-title {
  width: 100%;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  color: #fff;
  border-bottom: 1px solid #ddd;
}
.ms-login {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 350px;
  margin: -190px 0 0 -175px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
  overflow: hidden;
}
.ms-content {
  padding: 30px 30px;
}
.login-btn {
  text-align: center;
}
.login-btn button {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
}
.login-tips {
  font-size: 12px;
  line-height: 30px;
  color: #fff;
}
.version {
  position: absolute;
  bottom: 1%;
  left: 10%;
  margin-left: -150px;
}
.ncrj {
  display: none;
}


:deep(.el-input__wrapper) {
  padding: 1px;
}
:deep(.el-input__inner) {
  padding: 0 12px;
}
</style>

<style>
#nc_1_wrapper {
  width: 100% !important;
}
</style>
