// 阅信相关路由
export default [
  {
    path: '/yuexinAudit',
    meta: { title: '阅信模板', keepAlive: true, isBack: false },
    component: () => import('@/components/page/yuexin/yuexinTemplateAudit.vue'),
  },
  {
    path: '/yuexinTemplateChannel',
    meta: { title: '模板渠道管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/yuexin/yuexinTemplateChannel.vue'),
  },
  {
    path: '/GeneratingRecords',
    meta: { title: '阅信短链', keepAlive: true, isBack: false },
    component: () => import('@/components/page/yuexin/yuexinLink.vue'),
  },
  {
    path: '/linkRefund',
    meta: { title: '链接返还', keepAlive: true, isBack: false },
    component: () => import('@/components/page/yuexin/yuexinLinkRefund.vue'),
  },
  {
    path: '/yuexinSendTask',
    meta: { title: '阅信发送任务', keepAlive: true, isBack: false },
    component: () => import('@/components/page/yuexin/yuexinSendTask.vue'),
  },
] 