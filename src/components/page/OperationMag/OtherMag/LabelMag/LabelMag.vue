<template>
  <div>
    <div class="Top_title">
      <span>标签管理</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="标签属性" label-width="80px" prop="attributeId">
            <el-select
              v-model="formInline.attributeId"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option
                v-for="(item, index) in labelName"
                :label="item.attributeName"
                :value="item.attributeId"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="标签内容" label-width="80px" prop="content">
            <el-input
              v-model="formInline.content"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="标签状态" label-width="80px" prop="labelStatus">
            <el-select
              v-model="formInline.labelStatus"
              placeholder="请选择"
              class="input-w"
            >
              <el-option label="全部" value></el-option>
              <el-option label="停用" value="2"></el-option>
              <el-option label="启用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建人" label-width="80px" prop="createName">
            <el-input
              v-model="formInline.createName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" label-width="80px" prop="dataCreat">
            <el-date-picker
              class="input-w"
              v-model="formInline.dataCreat"
              type="daterange"
              value-format="YYYY-MM-DD"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="query()">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
        <el-button type="primary" plain style @click="download"
          >导出数据</el-button
        >
      </div>
      <div class="Signature-search-fun">
        <!-- <span class="Signature-list-header">标签列表</span> -->
        <el-button type="primary" style="margin-left: 15px" @click="addopt"
          >创建标签</el-button
        >
        <el-button
          type="danger"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delAll"
          >批量删除</el-button
        >
        <el-button
          type="primary"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delStatus(1)"
          >批量启用</el-button
        >
        <el-button
          type="warning"
          style="margin-left: 15px"
          v-if="selectId.length > 0"
          @click="delStatus(2)"
          >批量停用</el-button
        >
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        ></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增标签 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="110px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="标签属性" prop="attributeId">
            <el-select
              v-model="formop.attributeId"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in labelName"
                :label="item.attributeName"
                :value="item.attributeId"
                :key="index"
              ></el-option>
              <!-- <el-option label="其他" value="其他" v-if="dialogStatus=='add'"></el-option> -->
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="标签属性名称" prop="attributeName" v-if="formop.attributeId == '其他'">
                <el-input v-model="formop.attributeName" autocomplete="off"></el-input>
              </el-form-item> -->
          <el-form-item label="标签内容" prop="labelContent">
            <el-input
              v-model="formop.labelContent"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="标签状态" prop="labelStatus">
            <el-select
              v-model="formop.labelStatus"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="启用" value="1"></el-option>
              <el-option label="停用" value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增标签商结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
export default {
  name: 'LabelMag',
  components: {
    TableTem,
  },
  data() {
    return {
      titleMap: {
        add: '添加标签',
        edit: '编辑标签',
      },
      dialogStatus: '', //新增编辑标题
      steats: [], //批量停启用状态
      //  SigDialogVisible:false,//弹出框显示隐藏
      dialogFormVisible: false, //新增弹出框显示隐藏
      formInline: {
        //列表查询条件
        dataCreat: [],
        createName: '',
        content: '',
        labelStatus: '',
        attributeId: '',
        beginTime: '',
        endTime: '',
        isDown: 2,
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //列表查询储存
        dataCreat: [],
        createName: '',
        content: '',
        labelStatus: '',
        attributeId: '',
        beginTime: '',
        endTime: '',
        isDown: 2,
        currentPage: 1,
        pageSize: 10,
      },
      labelId: '', //列表行id
      selectId: '', //选中列表id
      tableRow: '', //当前行列表数据
      formop: {
        labelContent: '',
        attributeName: '',
        labelStatus: '',
        attributeId: '',
      },
      rules: {
        attributeId: [
          { required: true, message: '请选择标签属性', trigger: 'change' },
        ],
        attributeName: [
          { required: true, message: '请输入标签属性名称', trigger: 'blur' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
          {
            pattern: /^[\u4e00-\u9fa5]+$/,
            message: '含有非法字符（只能输入汉字！）',
          },
        ],
        labelContent: [
          { required: true, message: '请输入标签内容', trigger: 'blur' },
          {
            min: 1,
            max: 15,
            message: '长度在 1 到 15 个字符',
            trigger: 'blur',
          },
          {
            pattern: /^(?!-)(?!.*?-$)[\u4e00-\u9fa5-]+$/,
            message: '含有非法字符（只能输入汉字和-不能以-开头和结尾！）',
          },
        ],
        labelStatus: [
          { required: true, message: '请选择标签状态', trigger: 'change' },
        ],
      },
      labelName: [],
      tableDataObj: {
        //列表数据
        tableData: [],
        loading2: false,
        //分页参数
        total: 0,
        tableLabel: [
          {
            //
            prop: 'labelId',
            showName: 'ID',
            fixed: false,
            width: '70px',
          },
          {
            prop: 'attributeName',
            showName: '标签属性',
            fixed: false,
          },
          {
            prop: 'labelContent',
            showName: '标签内容',
            fixed: false,
          },
          {
            prop: 'labelStatus',
            showName: '标签状态',
            fixed: false,
            width: '72px',
            formatData: function (val) {
              return val == '1' ? '启用' : '停用'
            },
            showCondition: {
              condition: '2',
            },
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
            width: '150px',
          },
        ],
        tableStyle: {
          isSelection: true, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '160', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        conditionOption: [
          {
            contactCondition: 'labelStatus', //关联的表格属性
            contactData: '1', //关联的表格属性-值
            optionName: '停用', //按钮的显示文字
            optionMethod: 'stop', //按钮的方法
            icon: 'el-icon-circle-close-outline', //按钮图标
            optionButtonColor: 'orange', //按钮颜色
            otherOptionName: '启用', //其他条件的按钮显示文字
            otherOptionMethod: 'start', //其他条件的按钮方法
            otherIcon: 'el-icon-circle-check-outline', //其他条件按钮的图标
            optionOtherButtonColor: '#16A589', //其他条件按钮的颜色
          },
          {
            contactCondition: 'labelStatus', //关联的表格属性
            contactData: '1', //关联的表格属性-值
            optionName: '编辑', //按钮的显示文字
            optionMethod: 'details', //按钮的方法
            icon: 'el-icon-edit', //按钮图标
            optionButtonColor: '#16A589', //按钮颜色
          },
          {
            contactCondition: 'labelStatus', //关联的表格属性
            contactData: '2', //关联的表格属性-值
            optionName: '删除', //按钮的显示文字
            optionMethod: 'dellog', //按钮的方法
            icon: 'el-icon-delete', //按钮图标
            optionButtonColor: '#f56c6c', //按钮颜色
          },
        ],
        tableOptions: [],
      },
    }
  },
  methods: {
    //------------------------ 列表信息 ---------------------------------
    gettableLIst() {
      //获取列表数据
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatinglabel/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
    },
    //------------------------ 列表信息 ---------------------------------
    //------------------------ 操作列表 ---------------------------------

    query() {
      //查询功能
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime =
          this.moment(val[1]).format('YYYY-MM-DD ') + '23:59:59'
      } else {
        this.formInline.beginTime = ''
        this.formInline.endTime = ''
      }
    },
    // 导出数据
    download() {
      let aa = Object.assign({}, this.tabelAlllist)
      aa.isDown = 1
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: '列表无数据，不可导出！',
          type: 'warning',
        })
      } else {
        this.$File.export(
          window.path.omcs + 'operatinglabel/page',
          aa,
          '标签报表.xlsx'
        )
      }
    },
    Reload() {
      //重置
      this.$refs.formInline.resetFields()
      this.formInline.beginTime = ''
      this.formInline.endTime = ''
      Object.assign(this.tabelAlllist, this.formInline)
    },
    //------------------------ 操作列表 ---------------------------------
    getLabel() {
      //获取标签类型
      window.api.post(
        window.path.omcs + 'operatinglabelattribute/page',
        {
          pageSize: 100,
          currentPage: 1,
        },
        (res) => {
          this.labelName = res.records
        }
      )
    },
    submitForm(formop) {
      //提交弹框 （新增编辑）
      this.$refs.formop.validate((valid) => {
        if (valid) {
          if (this.formop.attributeId == '其他') {
            window.api.get(
              window.path.omcs +
                'operatinglabelattribute/exists/' +
                this.formop.attributeName,
              {},
              (res) => {
                if (res == false) {
                  this.$message({
                    message: '标签属性名称重复，重新输入！',
                    type: 'warning',
                  })
                } else {
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs + 'operatinglabelattribute',
                    {
                      attributeName: this.formop.attributeName,
                    },
                    () => {
                      window.api.post(
                        window.path.omcs + 'operatinglabelattribute/page',
                        {
                          attributeName: this.formop.attributeName,
                        },
                        (res) => {
                          this.formop.attributeId = res.records[0].attributeId
                          window.api.post(
                            window.path.omcs + 'operatinglabel/save',
                            this.formop,
                            (res) => {
                              this.dialogFormVisible = false
                              this.gettableLIst()
                            }
                          )
                        }
                      )
                    }
                  )
                }
              }
            )
          } else {
            if (this.dialogStatus == 'add') {
              window.api.get(
                window.path.omcs +
                  'operatinglabel/exists/' +
                  this.formop.attributeId +
                  '/' +
                  this.formop.labelContent,
                {},
                (res) => {
                  if (res == false) {
                    this.$message({
                      message: '标签内容重复，重新输入！',
                      type: 'warning',
                    })
                  } else {
                    this.$confirms.confirmation(
                      'post',
                      '确认执行此操作吗？',
                      window.path.omcs + 'operatinglabel/save',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  }
                }
              )
            } else {
              //编辑
              this.formop.labelId = this.labelId
              if (
                this.formop.attributeId == this.tableRow.attributeId &&
                this.formop.labelContent == this.tableRow.labelContent
              ) {
                this.$confirms.confirmation(
                  'post',
                  '确认执行此操作吗？',
                  window.path.omcs + 'operatinglabel/update',
                  this.formop,
                  () => {
                    this.dialogFormVisible = false
                    this.gettableLIst()
                  }
                )
              } else {
                window.api.get(
                  window.path.omcs +
                    'operatinglabel/exists/' +
                    this.formop.attributeId +
                    '/' +
                    this.formop.labelContent,
                  {},
                  (res) => {
                    if (res == false) {
                      this.$message({
                        message: '标签内容重复，重新输入！',
                        type: 'warning',
                      })
                    } else {
                      this.$confirms.confirmation(
                        'post',
                        '确认执行此操作吗？',
                        window.path.omcs + 'operatinglabel/update',
                        this.formop,
                        () => {
                          this.dialogFormVisible = false
                          this.gettableLIst()
                        }
                      )
                    }
                  }
                )
              }
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      //新增
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
      this.getLabel()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      let steats = []

      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].labelId)
        steats.push(val[i].labelStatus)
      }
      this.steats = steats
      this.selectId = selectId.join(',')
    },
    //编辑
    detailsRow(val) {
      window.api.get(
        window.path.omcs +
          'operatinglabel/checkIsUsedByTemplateChannel/' +
          val.row.labelId,
        {},
        (res) => {
          if (res.code != 400) {
            //编辑
            this.dialogFormVisible = true
            this.dialogStatus = 'edit'
            this.labelId = val.row.labelId
            this.tableRow = val.row
            this.$nextTick(() => {
              this.$refs.formop.resetFields()
              Object.assign(this.formop, val.row) //编辑框赋值
            })
          } else {
            this.$message({
              message: res.data,
              type: 'warning',
            })
          }
        }
      )
    },
    delRow: function (val) {
      //删除
      this.$confirms.confirmation(
        'delete',
        '此操作将永久删除该数据, 是否继续？',
        window.path.omcs + 'operatinglabel/' + val.row.labelId,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    delState: function (val, index) {
      if (index == 2) {
        window.api.get(
          window.path.omcs +
            'operatinglabel/checkIsUsedByTemplateChannel/' +
            val.row.labelId,
          {},
          (res) => {
            if (res.code != 400) {
              //操作状态功能（停用）
              this.$confirms.confirmation(
                'put',
                '确认执行启用或停用操作吗？',
                window.path.omcs +
                  'operatinglabel/updateStatusBatch?ids=' +
                  val.row.labelId +
                  '&labelStatus=' +
                  index,
                {},
                () => {
                  this.gettableLIst()
                }
              )
            } else {
              this.$message({
                message: res.data,
                type: 'warning',
              })
            }
          }
        )
      } else {
        //操作状态功能（启用停用）
        this.$confirms.confirmation(
          'put',
          '确认执行启用或停用操作吗？',
          window.path.omcs +
            'operatinglabel/updateStatusBatch?ids=' +
            val.row.labelId +
            '&labelStatus=' +
            index,
          {},
          () => {
            this.gettableLIst()
          }
        )
      }
    },
    handelOptionButton: function (val) {
      //列表后按钮操作
      if (val.methods == 'details') {
        this.detailsRow(val)
      }
      if (val.methods == 'start') {
        this.delState(val, 1)
      }
      if (val.methods == 'stop') {
        this.delState(val, 2)
      }
      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },

    delAll() {
      let flag = true
      for (let i = 0; i < this.steats.length; i++) {
        if (this.steats[i] != 2) {
          flag = false
          break
        }
      }
      if (flag) {
        //批量删除
        this.$confirms.confirmation(
          'delete',
          '此操作将永久删除该数据, 是否继续？',
          window.path.omcs + 'operatinglabel/deleteBatch/' + this.selectId,
          {},
          () => {
            this.gettableLIst()
          }
        )
      } else {
        this.$message({
          message: '选择项中包括启用状态，需重新选择删除项！',
          type: 'warning',
        })
      }
    },
    delStatus(val) {
      //批量启用停用
      if (val == 1) {
        //启用
        this.$confirms.confirmation(
          'put',
          '此操作将启用所选数据, 是否继续？',
          window.path.omcs +
            'operatinglabel/updateStatusBatch?ids=' +
            this.selectId +
            '&labelStatus=' +
            val,
          {},
          () => {
            this.gettableLIst()
          }
        )
      } else if (val == 2) {
        //停用
        window.api.get(
          window.path.omcs +
            'operatinglabel/checkIsUsedByTemplateChannel/' +
            this.selectId,
          {},
          (res) => {
            if (res.code != 400) {
              this.$confirms.confirmation(
                'put',
                '此操作将停用所选数据, 是否继续？',
                window.path.omcs +
                  'operatinglabel/updateStatusBatch?ids=' +
                  this.selectId +
                  '&labelStatus=' +
                  val,
                {},
                () => {
                  this.gettableLIst()
                }
              )
            } else {
              this.$message({
                message: res.data,
                type: 'warning',
              })
            }
          }
        )
      }
    },
  },
  mounted() {
    this.getLabel()
  },
  watch: {
    //监听查询框对象的变化
    tabelAlllist: {
      handler() {
        this.gettableLIst()
      },
      deep: true, //深度监听
      immediate: true,
    },
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields() //清空表单
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
