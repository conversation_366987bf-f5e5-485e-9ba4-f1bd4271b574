#!/usr/bin/env node

/**
 * 打包分析工具
 * 用于分析代码分割和Tree Shaking的效果
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class BundleAnalyzer {
  constructor() {
    this.distPath = path.join(__dirname, '../dist')
    this.statsPath = path.join(__dirname, '../bundle-stats.json')
  }

  /**
   * 分析打包结果
   */
  async analyzeBuild() {
    console.log('🔍 开始分析打包结果...\n')

    try {
      // 1. 检查dist目录是否存在
      if (!fs.existsSync(this.distPath)) {
        console.log('📦 dist目录不存在，正在构建...')
        await this.buildProject()
      }

      // 2. 分析文件大小
      const fileAnalysis = this.analyzeFiles()
      
      // 3. 分析代码分割效果
      const chunkAnalysis = this.analyzeChunks()
      
      // 4. 生成报告
      const report = this.generateReport(fileAnalysis, chunkAnalysis)
      
      // 5. 输出报告
      this.printReport(report)
      
      // 6. 保存报告
      this.saveReport(report)

      return report
    } catch (error) {
      console.error('❌ 分析失败:', error.message)
      throw error
    }
  }

  /**
   * 构建项目
   */
  async buildProject() {
    try {
      console.log('正在构建项目...')
      execSync('npm run build:prod', { 
        stdio: 'inherit',
        cwd: path.dirname(__dirname)
      })
      console.log('✅ 构建完成\n')
    } catch (error) {
      throw new Error('构建失败: ' + error.message)
    }
  }

  /**
   * 分析文件大小
   */
  analyzeFiles() {
    const files = this.getAllFiles(this.distPath)
    const analysis = {
      total: 0,
      js: { count: 0, size: 0, files: [] },
      css: { count: 0, size: 0, files: [] },
      assets: { count: 0, size: 0, files: [] },
      vendors: { count: 0, size: 0, files: [] }
    }

    files.forEach(file => {
      const stats = fs.statSync(file)
      const size = stats.size
      const relativePath = path.relative(this.distPath, file)
      const ext = path.extname(file)
      
      analysis.total += size

      const fileInfo = {
        path: relativePath,
        size: size,
        sizeFormatted: this.formatSize(size)
      }

      if (ext === '.js') {
        analysis.js.count++
        analysis.js.size += size
        analysis.js.files.push(fileInfo)
        
        // 识别vendor文件
        if (relativePath.includes('vendor-')) {
          analysis.vendors.count++
          analysis.vendors.size += size
          analysis.vendors.files.push(fileInfo)
        }
      } else if (ext === '.css') {
        analysis.css.count++
        analysis.css.size += size
        analysis.css.files.push(fileInfo)
      } else {
        analysis.assets.count++
        analysis.assets.size += size
        analysis.assets.files.push(fileInfo)
      }
    })

    // 排序文件（按大小降序）
    Object.keys(analysis).forEach(key => {
      if (analysis[key].files) {
        analysis[key].files.sort((a, b) => b.size - a.size)
      }
    })

    return analysis
  }

  /**
   * 分析代码分割效果
   */
  analyzeChunks() {
    const jsFiles = this.getAllFiles(path.join(this.distPath, 'static/js'))
    const chunks = {
      entry: [],
      vendors: [],
      pages: [],
      others: []
    }

    jsFiles.forEach(file => {
      const fileName = path.basename(file)
      const stats = fs.statSync(file)
      const size = stats.size
      
      const chunkInfo = {
        name: fileName,
        size: size,
        sizeFormatted: this.formatSize(size)
      }

      if (fileName.includes('vendor-')) {
        chunks.vendors.push(chunkInfo)
      } else if (fileName.includes('page-')) {
        chunks.pages.push(chunkInfo)
      } else if (fileName.startsWith('index.')) {
        chunks.entry.push(chunkInfo)
      } else {
        chunks.others.push(chunkInfo)
      }
    })

    // 排序
    Object.keys(chunks).forEach(key => {
      chunks[key].sort((a, b) => b.size - a.size)
    })

    return chunks
  }

  /**
   * 生成报告
   */
  generateReport(fileAnalysis, chunkAnalysis) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalSize: fileAnalysis.total,
        totalSizeFormatted: this.formatSize(fileAnalysis.total),
        fileCount: fileAnalysis.js.count + fileAnalysis.css.count + fileAnalysis.assets.count,
        jsSize: fileAnalysis.js.size,
        jsSizeFormatted: this.formatSize(fileAnalysis.js.size),
        cssSize: fileAnalysis.css.size,
        cssSizeFormatted: this.formatSize(fileAnalysis.css.size),
        vendorSize: fileAnalysis.vendors.size,
        vendorSizeFormatted: this.formatSize(fileAnalysis.vendors.size)
      },
      files: fileAnalysis,
      chunks: chunkAnalysis,
      optimization: this.analyzeOptimization(fileAnalysis, chunkAnalysis),
      recommendations: this.generateRecommendations(fileAnalysis, chunkAnalysis)
    }

    return report
  }

  /**
   * 分析优化效果
   */
  analyzeOptimization(fileAnalysis, chunkAnalysis) {
    const optimization = {
      codeSplitting: {
        enabled: chunkAnalysis.vendors.length > 0,
        vendorChunks: chunkAnalysis.vendors.length,
        pageChunks: chunkAnalysis.pages.length
      },
      vendorSeparation: {
        separated: fileAnalysis.vendors.size > 0,
        vendorRatio: fileAnalysis.vendors.size / fileAnalysis.js.size,
        largestVendor: fileAnalysis.vendors.files[0]?.sizeFormatted || 'N/A'
      },
      assetOptimization: {
        totalAssets: fileAnalysis.assets.count,
        assetSize: fileAnalysis.assets.size,
        assetSizeFormatted: this.formatSize(fileAnalysis.assets.size)
      }
    }

    return optimization
  }

  /**
   * 生成优化建议
   */
  generateRecommendations(fileAnalysis, chunkAnalysis) {
    const recommendations = []

    // 检查大文件
    const largeFiles = []
    fileAnalysis.js.files.forEach(file => {
      if (file.size > 1024 * 1024) { // 大于1MB
        largeFiles.push(file)
      }
    })

    if (largeFiles.length > 0) {
      recommendations.push({
        type: 'large-files',
        severity: 'high',
        message: `发现 ${largeFiles.length} 个大于1MB的JS文件`,
        details: largeFiles.map(f => `${f.path} (${f.sizeFormatted})`),
        suggestion: '考虑进一步拆分这些大文件或使用懒加载'
      })
    }

    // 检查vendor chunk大小
    const totalVendorSize = chunkAnalysis.vendors.reduce((sum, chunk) => sum + chunk.size, 0)
    if (totalVendorSize > 2 * 1024 * 1024) { // 大于2MB
      recommendations.push({
        type: 'large-vendors',
        severity: 'medium',
        message: `Vendor chunks总大小为 ${this.formatSize(totalVendorSize)}`,
        suggestion: '考虑按需导入第三方库或使用更细粒度的vendor分割'
      })
    }

    // 检查CSS大小
    if (fileAnalysis.css.size > 500 * 1024) { // 大于500KB
      recommendations.push({
        type: 'large-css',
        severity: 'medium',
        message: `CSS总大小为 ${fileAnalysis.css.sizeFormatted}`,
        suggestion: '考虑CSS代码分割或移除未使用的CSS'
      })
    }

    // 检查代码分割效果
    if (chunkAnalysis.vendors.length === 0) {
      recommendations.push({
        type: 'no-code-splitting',
        severity: 'high',
        message: '未发现vendor代码分割',
        suggestion: '启用代码分割以优化加载性能'
      })
    }

    return recommendations
  }

  /**
   * 打印报告
   */
  printReport(report) {
    console.log('📊 打包分析报告\n')
    console.log('=' * 50)
    
    // 总览
    console.log('\n📈 构建总览:')
    console.log(`  总大小: ${report.summary.totalSizeFormatted}`)
    console.log(`  文件数量: ${report.summary.fileCount}`)
    console.log(`  JS大小: ${report.summary.jsSizeFormatted}`)
    console.log(`  CSS大小: ${report.summary.cssSizeFormatted}`)
    console.log(`  Vendor大小: ${report.summary.vendorSizeFormatted}`)

    // 代码分割效果
    console.log('\n🔄 代码分割效果:')
    console.log(`  Vendor chunks: ${report.optimization.codeSplitting.vendorChunks} 个`)
    console.log(`  Page chunks: ${report.optimization.codeSplitting.pageChunks} 个`)
    console.log(`  Vendor分离率: ${(report.optimization.vendorSeparation.vendorRatio * 100).toFixed(1)}%`)

    // 最大的文件
    console.log('\n📦 最大的JS文件 (Top 5):')
    report.files.js.files.slice(0, 5).forEach((file, index) => {
      console.log(`  ${index + 1}. ${file.path} - ${file.sizeFormatted}`)
    })

    // 优化建议
    if (report.recommendations.length > 0) {
      console.log('\n💡 优化建议:')
      report.recommendations.forEach((rec, index) => {
        const severity = rec.severity === 'high' ? '🔴' : rec.severity === 'medium' ? '🟡' : '🟢'
        console.log(`  ${severity} ${rec.message}`)
        console.log(`     建议: ${rec.suggestion}`)
      })
    } else {
      console.log('\n✅ 打包已优化，未发现明显问题')
    }

    console.log('\n' + '=' * 50)
  }

  /**
   * 保存报告
   */
  saveReport(report) {
    try {
      fs.writeFileSync(
        this.statsPath, 
        JSON.stringify(report, null, 2),
        'utf8'
      )
      console.log(`\n📄 详细报告已保存到: ${this.statsPath}`)
    } catch (error) {
      console.warn('保存报告失败:', error.message)
    }
  }

  /**
   * 获取所有文件
   */
  getAllFiles(dir) {
    let files = []
    
    if (!fs.existsSync(dir)) return files
    
    const items = fs.readdirSync(dir)
    
    items.forEach(item => {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)
      
      if (stat.isDirectory()) {
        files = files.concat(this.getAllFiles(fullPath))
      } else {
        files.push(fullPath)
      }
    })
    
    return files
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

/**
 * 比较两次构建的差异
 */
class BuildComparator {
  constructor() {
    this.currentStatsPath = path.join(__dirname, '../bundle-stats.json')
    this.previousStatsPath = path.join(__dirname, '../bundle-stats-previous.json')
  }

  compare() {
    if (!fs.existsSync(this.currentStatsPath)) {
      console.log('❌ 当前构建报告不存在，请先运行分析')
      return
    }

    if (!fs.existsSync(this.previousStatsPath)) {
      console.log('ℹ️ 没有历史构建数据，跳过对比')
      return
    }

    try {
      const current = JSON.parse(fs.readFileSync(this.currentStatsPath, 'utf8'))
      const previous = JSON.parse(fs.readFileSync(this.previousStatsPath, 'utf8'))

      console.log('\n📊 构建对比结果:')
      
      const sizeDiff = current.summary.totalSize - previous.summary.totalSize
      const sizeChange = sizeDiff > 0 ? '+' : ''
      const sizePercentChange = ((sizeDiff / previous.summary.totalSize) * 100).toFixed(2)
      
      console.log(`  总大小变化: ${sizeChange}${this.formatSize(sizeDiff)} (${sizeChange}${sizePercentChange}%)`)
      
      const jsSizeDiff = current.summary.jsSize - previous.summary.jsSize
      const jsChange = jsSizeDiff > 0 ? '+' : ''
      const jsPercentChange = ((jsSizeDiff / previous.summary.jsSize) * 100).toFixed(2)
      
      console.log(`  JS大小变化: ${jsChange}${this.formatSize(jsSizeDiff)} (${jsChange}${jsPercentChange}%)`)

    } catch (error) {
      console.error('对比失败:', error.message)
    }
  }

  formatSize(bytes) {
    const analyzer = new BundleAnalyzer()
    return analyzer.formatSize(Math.abs(bytes))
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'analyze'

  if (command === 'analyze') {
    const analyzer = new BundleAnalyzer()
    await analyzer.analyzeBuild()
  } else if (command === 'compare') {
    const comparator = new BuildComparator()
    comparator.compare()
  } else {
    console.log('用法:')
    console.log('  node bundle-analyzer.js analyze  - 分析当前构建')
    console.log('  node bundle-analyzer.js compare  - 对比构建变化')
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('执行失败:', error.message)
    process.exit(1)
  })
}

module.exports = { BundleAnalyzer, BuildComparator } 