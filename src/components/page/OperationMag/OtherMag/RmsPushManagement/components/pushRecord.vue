<template>
  <div class="login_cell_phone">
    <div class="OuterFrame fillet" style="height: 100%">
      <div>
        <el-form
          :inline="true"
          ref="formInline"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item label="用户名称" prop="clientName">
            <el-input
              v-model="formInline.clientName"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="消息ID" prop="msgid">
            <el-input
              v-model="formInline.msgid"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="发送时间" prop="time">
            <el-date-picker
              v-model="formInline.time"
              type="datetimerange"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              range-separator="至"
              @change="timeClick"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="ListSearch"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reset('formInline')"
              >重置</el-button
            >
            <el-button type="primary" plain style="" @click="Reportpush()"
              >状态报告推送</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="boderbottom"></div>
      <div class="sensitive-fun">
        <!-- <span class="sensitive-list-header">状态推送列表</span> -->
      </div>
      <div class="Mail-table" style="padding-bottom: 40px;">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="RmsPushManagementPushRecord"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 13px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名称" title="用户名称" width="120px">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="videoId" title="videoId" width="70px">
            <template v-slot="scope">
              <div>
                {{ scope.row.videoId }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="msgId" title="msgId" width="200px">
            <template v-slot="scope">
              <div>
                {{ scope.row.msgid }}
                <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i> -->
                <CopyTemp :content="scope.row.msgid" />
              </div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号" width="110px">
            <template v-slot="{row, rowIndex}">
              <div
                style="color: #16a589; cursor: pointer"
                @click="tableContent(row, rowIndex)"
              >
                <span>{{ row.mobile }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="视频短信标题" title="视频短信标题" width="200px">
            <template v-slot="scope">
              <div
                style="color: #409eff; cursor: pointer"
                @click="View(scope.row)"
              >
                <Tooltip
                  v-if="scope.row.title"
                  :content="scope.row.title"
                  className="wrapper-text"
                  effect="light"
                ></Tooltip>
              </div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="预览">
                            <template #default="scope">
                                <el-button type="text" @click="View(scope.row)"><i
                                        class="el-icon-picture"></i>&nbsp;预览</el-button>
                            </template>
                        </vxe-column> -->
          <vxe-column field="计费数" title="计费数" width="80px">
            <template v-slot="scope">
              <div>
                {{ scope.row.chargeNum }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="发送时间" title="发送时间" width="100px">
            <template v-slot="scope">
              <p v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="回执时间" title="回执时间" width="100px">
            <template v-slot="scope">
              <p v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.reportTime">
                {{ $parseTime(scope.row.reportTime, '{h}:{i}:{s}') }}
              </p>
            </template>
          </vxe-column>
          <vxe-column field="发送状态" title="发送状态" width="90px">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 1" type="success" effect="dark">
                成功
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 2"
                type="danger"
                effect="dark"
              >
                失败
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="info"
                effect="dark"
              >
                待返回
              </el-tag>
              <!-- <span v-if="scope.row.status == 1">成功</span>
                                <span v-else-if="scope.row.status == 2">失败</span>
                                <span v-else-if="scope.row.status == 3">待返回</span> -->
            </template>
          </vxe-column>
          <vxe-column field="下载状态" title="下载状态" width="90px">
            <template v-slot="scope">
              <div>
                <span v-if="scope.row.downloadStatus == '1000'">下载成功</span>
                <span v-else-if="scope.row.downloadStatus == '1001'"
                  >下载失败</span
                >
                <span v-else>未知</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="90">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.originalCode"
                :content="scope.row.originalCode"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="扩展号" title="扩展号" width="90px">
            <template v-slot="scope">
              <div>
                {{ scope.row.ext }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="发送通道号" title="发送通道号" width="85px">
            <template v-slot="scope">
              <div
                @click="save(scope.row.channelId)"
                style="color: rgb(22, 165, 137); cursor: pointer"
              >
                {{ scope.row.channelId }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="提交Ip" title="提交Ip" width="110px">
            <template v-slot="scope">
              <div>
                {{ scope.row.ip }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源" width="140px">
            <template v-slot="scope">
              <div>
                {{ scope.row.source }}
              </div>
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formInlines.currentPage"
            :page-size="formInlines.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in viewData"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png' ||
                    item.media == 'jpeg'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                ></audio>
                <div style="white-space: pre-line">
                  {{ item.txt }}
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
    <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
  </div>
</template>

<script>
import ChannelView from '@/components/publicComponents/ChannelView.vue'
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../../../../utils/clipboard'
import moment from 'moment'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  name: 'ProcessingQueue',
  components: {
    DatePlugin,
    TableTem,
    ChannelView,
    UserLists,
    Tooltip,
    CopyTemp,
  },
  data() {
    return {
      customConfig: {
        storage: true
      },
      userFlag: false,
      isFirstEnter: false,
      // 预览
      viewData: '',
      indexVeew: 1,
      title: '',
      dialogVisible: false,
      nameover: '',
      ChannelData: '', //传递通道值
      name: 'ProcessingQueue',
      //复选框值
      selectId: '',
      // 搜索数据
      formInline: {
        clientName: '',
        content: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        msgid: '',
        flag: '5',
        isDownload: 2,
        pushRecord: 1,
        productId: '3',
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        clientName: '',
        content: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        msgid: '',
        flag: '5',
        isDownload: 2,
        pushRecord: 1,
        productId: '3',
        time: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    }
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'statistics/sendVideoInfo/page',
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.tablecurrent.total = res.data.total
        }
      )
    },
    // 状态报告推送
    Reportpush() {
      if (
        this.formInlines.clientName != '' &&
        this.formInlines.time.length != 0
      ) {
        this.$confirms.confirmation(
          'post',
          '确定推送？',
          window.path.omcs + 'servicepushrecord/save',
          this.formInlines,
          (res) => {
            console.log(res, 'res')
            this.InquireList()
          }
        )
      } else {
        this.$message({
          message: '请填写用户名与时间',
          type: 'warning',
        })
      }
    },
    // 监听子组件传递方法
    save(val) {
      this.ChannelData = val
      this.$refs.ChannelRef.ChannelClick()
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields()
      ;(this.formInline.time = [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      ]),
        (this.formInline.sendBeginTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'))
      this.formInline.sendEndTime = moment()
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      Object.assign(this.formInlines, this.formInline)
      this.InquireList()
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    // 定时时间
    timeClick(val) {
      if (val) {
        this.formInline.sendBeginTime = this.moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        // console.log(this.formInline.sendBeginTime,'ll');
        this.formInline.sendEndTime = this.moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline.sendBeginTime = ''
        this.formInline.sendEndTime = ''
      }
    },
    // 预览
    View(val) {
      this.indexVeew = 1
      this.viewData = val.contents
      this.title = val.title
      this.dialogVisible = true
    },
    // handleCopy(name,event){
    //     clip(name, event)
    // },
    // 点击手机号
    tableContent(row, index) {
      window.api.post(
        window.path.upms + '/generatekey/decryptMobile',
        {
          keyId: row.keyId,
          smsInfoId: row.decryptMobile,
          cipherMobile: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          }
          // this.tableDataObj.tableData[index].mobile=res.data
          // this.tableDataObj1.tableData[index].mobile=res.data
        }
      )
      //  window.api.get(window.path.omcs + "smsMessage/decrypt?smsInfoId="+val,{},res=>{
      //     if(res.code==200){
      //         this.tableDataObj.tableData[index].mobile=res.data[0]
      //     }else{
      //         this.$message({
      //             message: res.msg,
      //             type: 'warning'
      //         });
      //     }
      // })
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage
      this.InquireList()
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
    })
    // this.InquireList()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        // this.InquireList()
      },
      deep: true,
      immediate: true,
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>
