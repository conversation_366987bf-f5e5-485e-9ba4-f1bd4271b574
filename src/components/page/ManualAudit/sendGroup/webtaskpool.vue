<template>
  <div style="background: #fff; padding: 15px">
    <!-- <div class="crumbs">
      
        </div> -->
    <div class="Top_title" style="display: flex;">
      <span style="
          display: flex;
          align-items: center;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        " @click="goBack()"><el-icon>
          <ArrowLeft />
        </el-icon> 返回</span>|
      <span style="margin-left: 10px;">号码池管理</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
          <el-form-item label="用户名称" label-width="80px" prop="userName">
            <el-input v-model="formInline.userName" placeholder class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="标签" label-width="80px" prop="labelName">
            <el-input v-model="formInline.labelName" placeholder class="input-w"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')">重置</el-button>
      </div>
      <div class="Signature-search-fun" style="margin: 10px 0">
        <el-button type="primary" @click="addopt">创建任务</el-button>
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!--分页-->
        <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
          border :stripe="true" :data="tableDataObj.tableData" style="width: 100%" @selection-change="handelSelection">
          <el-table-column type="selection" width="46"></el-table-column>
          <el-table-column label="用户名称">
            <template v-slot="scope">
              <span>{{ scope.row.userName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模板名称">
            <template v-slot="scope">
              <span>{{ scope.row.tempName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模板ID">
            <template v-slot="scope">
              <span>{{ scope.row.tempId }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模板内容" width="500">
            <template v-slot="scope">
              <span>{{ scope.row.content }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标签">
            <template v-slot="scope">
              <span>{{ scope.row.labelName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="文件下载" width="90">
            <template v-slot="scope">
              <a :href="imgUrl + scope.row.filePath" rel="noopener noreferrer">{{ scope.row.fileOriginalName }}</a>
            </template>
          </el-table-column>
          <el-table-column label="总行数" width="90">
            <template v-slot="scope">
              <span>{{ scope.row.totalNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="已处理行数" width="90">
            <template v-slot="scope">
              <span>{{ scope.row.startNum }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="创建人">
                <template #default="scope">
                  <span>{{ scope.row.createName }}</span>
                </template>
              </el-table-column> -->
          <el-table-column label="创建时间" width="90">
            <template v-slot="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template v-slot="scope">
              <el-button link v-if="scope.row.startNum == 0" style="color: #409eff; margin-left: 2px;"
                @click="detailsRow(scope.$index, scope.row)"><el-icon>
                  <EditPen />
                </el-icon>&nbsp;修改</el-button>
              <el-button link style="margin: 0px 2px; color: red" @click="delState(scope.$index, scope.row)"><el-icon>
                  <Delete />
                </el-icon>&nbsp;删除</el-button>
              <el-button link style="margin: 0px 2px; color: #409eff;" @click="handelSend(scope.row)">发送计划</el-button>
              <el-button link style="margin: 0px 2px; color: #409eff;" @click="settingState(scope.row)">发送明细</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="1" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </div>

        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 创建任务 -->
      <el-dialog :title="titleMap[dialogStatus]" v-model="dialogFormVisible" :close-on-click-modal="false" width="45%">
        <el-form :model="formop" :rules="rules" ref="formop" style="padding: 0 28px 0 20px;">
          <el-form-item label="用户名称" label-width="80px" prop="userName">
            <el-input :disabled="formInline.groupId ? true : false" v-model="formop.userName" placeholder=""
              autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="标签" label-width="80px" prop="labelName">
            <el-input v-model="formop.labelName" placeholder="" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="发送方式" label-width="80px" prop="sendType">
            <el-radio-group v-model="formop.sendType" @change="changeSendType">
              <el-radio :value="1">模板</el-radio>
              <el-radio :value="0">自定义</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="formop.sendType == 1" ref="tempId" label="选择模板" label-width="80px" prop="tempId">
            <el-popover :visible="visible" placement="right" width="950" trigger="click">
              <el-form :inline="true" :model="tempList" class="demo-form-inline" ref="tempList">
                <el-form-item label="模板名称" label-width="80px" prop="temName">
                  <el-input v-model="tempList.temName" placeholder class="input-w"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" plain style @click="QueryT">查询</el-button>
                  <el-button type="primary" plain style @click="ReloadT('tempList')">重置</el-button>
                </el-form-item>
              </el-form>
              <el-table max-height="300" border :data="tempObj.tempData">
                <el-table-column width="90" property="temId" label="ID"></el-table-column>
                <el-table-column width="90" label="模板类型">
                  <template v-slot="scope">
                    <span v-if="scope.row.temType == '1'">验证码</span>
                    <span v-else-if="scope.row.temType == '2'">通知</span>
                    <span v-else-if="scope.row.temType == '3'">营销推广</span>
                  </template>
                </el-table-column>
                <el-table-column width="90" property="temName" label="模板名称"></el-table-column>
                <el-table-column label="模板内容">
                  <template v-slot="scope">
                    <div v-html="scope.row.temContent"></div>
                  </template>
                </el-table-column>
                <el-table-column label="字数" width="90">
                  <template v-slot="scope">
                    <span>{{ scope.row.temContent.length }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" fixed="right">
                  <template v-slot="scope">
                    <el-button link @click="checkTem(scope.row)"><el-icon style="color: #409eff;">
                        <CirclePlusFilled />
                      </el-icon>&nbsp;选择</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination class="page_bottom" @size-change="handleSizeChangeT" @current-change="handleCurrentChangeT"
                :current-page="1" :page-size="tempList.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" :total="tempObj.total"></el-pagination>
              <template #reference>
                <el-button @click="visible = true"><el-icon>
                    <Refresh />
                  </el-icon>选择模板</el-button>
              </template>
            </el-popover>
            <div v-if="formop.tempId != ''">
              <span>模板ID:</span><span>{{ formop.tempId }} </span><el-icon @click="delTemp"
                style="cursor: pointer;margin-left：10px">
                <CircleCloseFilled />
              </el-icon>
            </div>
          </el-form-item>
          <el-form-item  v-if="formop.sendType == 0" type="primary" label="模板内容" label-width="80px" prop="content">
            <el-input class="custom-textarea" v-model="formop.content" placeholder="" type="textarea"
              autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item ref="uploadElement" label="文件上传" label-width="80px" prop="filePath">
            <el-upload v-bind:data="{
              FoldPath: '上传目录',
              SecretKey: '安全验证',
            }" :action="actionPath" :headers="token" :limit="1" :file-list="batchFileList" :before-upload="(file) => {
                  return beforeAvatarUploadAtt(file, 'batch')
                }
                " :on-remove="(file) => {
                  return handleRemoveAtt(file, 'batch')
                }
                " :on-success="(response, fileList) => {
                  return handleSuccessAtt(response, fileList, 'batch')
                }
                " multiple>
              <el-button type="primary">文件上传</el-button>

              <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
            </el-upload>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')">提 交</el-button>
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 号码发送 -->
      <el-dialog title="发送计划" v-model="dialogVisible" width="30%" :close-on-click-modal="false"
        :before-close="handleClose">
        <el-tabs tab-position="left" v-model="activeName">
          <el-tab-pane label="手动发送" name="1">
            <div v-if="activeName == '1'">
              <el-form :model="sendList" :rules="rules" ref="sendList" :style="{ padding: '0 28px 0 20px' }">
                <el-form-item label="用户名称" label-width="80px" prop="username">
                  <el-input disabled v-model="sendList.username" placeholder="" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="标签" label-width="80px" prop="labelName">
                  <el-input disabled v-model="sendList.labelName" placeholder="" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item v-if="sendList.tempId" label="模板名称" label-width="80px" prop="tempName">
                  <el-input disabled v-model="sendList.tempName" placeholder="" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item v-if="sendList.tempId" label="模板名ID" label-width="80px" prop="tempId">
                  <el-input disabled v-model="sendList.tempId" placeholder="" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="模板内容" label-width="80px" prop="content">
                  <el-input class="custom-textarea" disabled v-model="sendList.content" type="textarea" placeholder="" autocomplete="off">

                  </el-input>
                </el-form-item>
                <el-form-item label="发送时间" label-width="80px" prop="isTiming">
                  <el-radio-group v-model="sendList.isTiming">
                    <el-radio value="0">立即发送</el-radio>
                    <el-radio value="1">定时发送</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="sendList.isTiming == 1" label="定时时间" label-width="80px" prop="sendTime">
                  <!-- <date-plugin
                    class="Mail-search-date"
                    :datePluginValueList="datePluginValueList"
                    @handledatepluginVal="handledatepluginVal"
                  ></date-plugin> -->
                  <el-date-picker v-model="sendList.sendTime"  :disabled-date="datePluginValueList.pickerOptions.disabledDate" @change="handledatepluginVal" type="datetime" placeholder="请选择时间"
                     />
                </el-form-item>
                <!-- <el-form-item
                v-if="sendList.isTiming == 1"
                label="定时时间"
                label-width="80px"
                prop="sendTime"
              >
              </el-form-item> -->
                <el-form-item label="号码数" label-width="80px" prop="mobileNum">
                  <el-input class="custom-textarea" type="textarea" v-model="sendList.mobileNum" placeholder=""
                    autocomplete="off"></el-input>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="自动发送" name="2">
            <div v-if="activeName == '2'">
              <el-form :model="sendList" :rules="rules" ref="sendList" :style="{ padding: '0 28px 0 20px' }">
                <el-form-item label="用户名称" label-width="80px" prop="username">
                  <el-input disabled v-model="sendList.username" placeholder="" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="文件上传" label-width="80px" prop="fileList">
                  <el-upload class="upload-demo" :action="actionPath" :headers="token" :on-success="handleSuccess"
                    :on-remove="handleRemove" :before-upload="beforeAvatarUpload" :limit="1"
                    :file-list="sendList.fileList">
                    <el-button size="default" type="primary">点击上传</el-button>
                    
                  </el-upload>
                  <div class="el-upload__tip">
                      <a :style="{ marginLeft: '10px' }"
                        href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/d9b39e7827bea68c45d2529204c624e8"
                        target="_blank" rel="noopener noreferrer">模版下载</a>
                    </div>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>

        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="sendNumer('sendList')">发送</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue' //日期
import {deepClone} from '@/utils/helpers'
// import TableTem from "../../../../publicComponents/TableTem";
// import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import moment from 'moment'
export default {
  data() {
    return {
      imgUrl: window.path.imgU,
      actionPath: window.path.cpus + 'v3/file/upload',
      isFirstEnter: false,
      dialogVisible: false,
      visible: false,
      activeName: "1",
      token: {},
      batchFileList: [],
      token: {},
      fileList: [],
      titleMap: {
        add: '创建任务',
        edit: '修改任务',
      },
      id: '',
      //新增编辑标题
      dialogStatus: '',
      //新增弹出框显示隐藏
      dialogFormVisible: false,
      //查询表单
      formInline: {
        labelName: '', //标签
        userName: '', //用户名
        tempId: '', //模板ID
        groupId: '', //分组ID
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        labelName: '', //标签
        userName: '', //用户名
        tempId: '', //模板ID
        groupId: '', //分组ID
        currentPage: 1,
        pageSize: 10,
      },
      //添加列表
      formop: {
        userName: '', //用户名
        groupId: '', //分组id
        tempName: '', //模板名称
        tempId: '', //模板id
        content: '', //模板内容
        sendType: 1, //发送方式
        labelName: '', //标签
        filePath: '', //文件路径
        fileOriginalName: '', //文件源路径
        totalNum: '', //总行数
      },
      tempList: {
        clientName: '',
        temName: '',
        currentPage: 1,
        pageSize: 10,
      },
      tempObj: {
        tempData: [],
        total: 0,
        loading2: false,
      },
      sendList: {
        id: '',
        isTiming: '0',
        mobileNum: '',
        sendTime: '',
        startNum: '',
        username: '',
        content: '',
        userId: '',
        tempName: '', //模板名称
        tempId: '', //模板id
        labelName: '', //标签
        fileList: [], //文件列表
        filePath: "", //文件路径
      },
      datePluginValueList: {
        //日期参数配置
        type: 'datetime',
        value: [],
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7
          },
        },
        defaultTime: '', //默认起始时刻
        datePluginValue: '',
      },
      rules: {
        tempId: [
          {
            required: true,
            message: '请选择模板',
            trigger: 'change',
          },
        ],
        content: [
          {
            required: true,
            message: '模板内容不能为空',
            trigger: 'change',
          },
        ],
        // labelName: [
        //   {
        //     required: true,
        //     message: "标签不能为空",
        //     trigger: "change",
        //   },
        // ],
        filePath: [
          {
            required: true,
            message: '请上传文件',
            trigger: 'change',
          },
        ],
        userName: [
          {
            required: true,
            message: '用户名称不用为空',
            trigger: 'change',
          },
        ],
        isTiming: [
          {
            required: true,
            message: '请选择发送时间',
            trigger: 'change',
          },
        ],
        sendTime: [
          {
            required: true,
            message: '请选择定时时间',
            trigger: 'change',
          },
        ],
        mobileNum: [
          {
            required: true,
            message: '发送号码不能为空',
            trigger: 'change',
          },
        ],
        fileList: [
          {
            required: true,
            message: "请上传文件",
            trigger: "change",
          },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      }
    }
  },
  components: {
    DatePlugin
  },
  name: 'webtaskgroup',
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerwebtaskpool/page',
        this.tabelAlllist,
        (res) => {
          //   console.log(res);
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    getTempList() {
      this.tempObj.loading2 = true
      window.api.post(window.path.omcs + 'template/page', this.tempList, (res) => {
        this.tempObj.total = res.total
        this.tempObj.tempData = res.records
        this.tempObj.loading2 = false
      })
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    handleSizeChangeT(size) {
      //改变每页数量触发事件
      this.tempList.pageSize = size
      this.getTempList()
    },
    handleCurrentChangeT: function (currentPage) {
      //改变页数触发事件
      this.tempList.currentPage = currentPage
      this.getTempList()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    QueryT() {
      //   Object.assign(this.tabelAlllist, this.formInline);
      this.getTempList()
    },
    //列表复选框的值
    handelSelection(val) {
      //   console.log(val);
      //   let selectId = [];
      //   // let wordsCount = [];
      //   for (let i = 0; i < val.length; i++) {
      //     selectId.push(val[i].id);
      //     // wordsCount.push(val[i].wordsCount);
      //   }
      //   this.selectId = selectId.join(","); //批量操作选中id
      //   // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    ReloadT(val) {
      this.$refs.tempList.resetFields()
      //   Object.assign(this.tabelAlllist, this.formInline);
      this.getTempList()
    },
    changeSendType(val) {
      if (val == 0) {
        this.formop.content = ''
        this.formop.tempId = ''
        this.formop.tempName = ''
      }else{
        this.formop.content = ''
      }
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          const formopClone = deepClone(this.formop)
          delete formopClone.sendType
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.omcs + 'consumerwebtaskpool',
              formopClone,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id;
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.omcs + 'consumerwebtaskpool',
              formopClone,
              (res) => {
                if (res.code == 200) {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'warning',
                  // })
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    settingState(row) {
      //   console.log(row);
      this.$router.push({
        path: '/webSendRecord',
        query: {
          poolId: row.id,
        },
      })
    },
    goBack() {
      this.$router.push({
        path: '/webtaskgroup',
      })
    },
    checkTem(row) {
      this.formop.tempId = row.temId
      this.formop.tempName = row.temName
      this.visible = false
      this.$refs.tempId.clearValidate('path')
    },
    delTemp() {
      this.formop.tempId = ''
      this.formop.tempName = ''
      this.formop.content = ''
    },
    addopt() {
      this.dialogFormVisible = true
      this.flagPut = false
      this.dialogStatus = 'add'
    },

    detailsRow(index, val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      if (val.tempId) {
        this.formop.tempId = val.tempId
        this.formop.sendType = 1
      }else{
        this.formop.sendType = 0
      }
      this.formop.tempName = val.tempName
      this.formop.content = val.content
      this.formop.filePath = val.filePath
      this.formop.fileOriginalName = val.fileOriginalName
      this.formop.totalNum = val.totalNum
      this.formop.labelName = val.labelName
      this.batchFileList = [
        { name: val.fileOriginalName, url: val.fileOriginalName },
      ]
      //   this.$nextTick(() => {
      //     this.$refs.formop.resetFields();
      //     Object.assign(this.formop, val); //编辑框赋值
      //   });
    },
    //操作状态功能（删除）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除此任务？',
        window.path.omcs + 'consumerwebtaskpool/' + val.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
    },
    handelSend(row) {
      this.sendList.id = row.id
      this.sendList.startNum = row.startNum
      this.sendList.username = row.userName
      this.sendList.userId = row.userId
      this.sendList.tempName = row.tempName
      this.sendList.tempId = row.tempId
      this.sendList.labelName = row.labelName
      this.sendList.content = row.content
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    handledatepluginVal: function (val1) {
      //日期
      //   console.log(val1, "ll");
      this.sendList.sendTime = moment(val1).format('YYYY-MM-DD HH:mm:ss')
      // console.log(this.sendList.sendTime,'this.sendList.sendTime');
      
    },
    beforeAvatarUpload(file) {
      const siJPGGIF = file.name.split(".")[1];
      const fileType = ["xlsx"];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning("上传文件只能是xlsx格式!");
        return false;
      }
    },
    // handleClick(){},
    handleRemove(file, fileList) {
      this.sendList.fileList = [];
      this.sendList.filePath = "";
    },
    handleSuccess(res, file, fileList) {
      if (res.code == 200) {
        this.sendList.fileList = fileList;
        this.sendList.filePath = res.data.fullpath;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    sendNumer(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.activeName == "1") {
            console.log(1);
            let falgss = false
            if (this.sendList.isTiming === '1') {
              //判断发送时间为定时时间
              if (this.sendList.sendTime) {
                //如果是定时时间 ，定时时间范围必填
                let nowTiem = new Date(this.sendList.sendTime).getTime()
                if (nowTiem < Date.now() + 1800000) {
                  falgss = false
                  this.datePluginValueList.datePluginValue = ''
                  this.sendList.sendTime = ''
                  this.$message({
                    message: '定时时间应大于当前时间30分钟，需重新设置！',
                    type: 'warning',
                  })
                } else {
                  falgss = true
                }
              } else {
                falgss = false
                this.$message({
                  message: '选择定时时间！',
                  type: 'warning',
                })
              }
            } else {
              falgss = true
            }
            if (falgss) {
              let data = {
                id: this.sendList.id,
                startNum: this.sendList.startNum,
                mobileNum: this.sendList.mobileNum,
                isTiming: this.sendList.isTiming,
                sendTime: this.sendList.sendTime,
                username: this.sendList.username,
                userId: this.sendList.userId,
              };
              this.$confirms.confirmation(
                'post',
                '确定执行此操作!',
                window.path.omcs + 'consumerSms/taskPools/send',
                data,
                () => {
                  this.gettableLIst()
                  this.dialogVisible = false
                  this.$router.push({
                    path: '/webSendRecord',
                    query: {
                      poolId: this.sendList.id,
                    },
                  })
                }
              )
            } else {
              let data = {
                id: this.sendList.id,
                startNum: this.sendList.startNum,
                filePath: this.sendList.filePath,
                userName: this.sendList.username,
              };
              this.$confirms.confirmation(
                "post",
                "确定执行此操作!",
                window.path.omcs + "consumerSms/taskPools/uploadSend",
                data,
                () => {
                  this.gettableLIst();
                  this.dialogVisible = false;
                  this.$router.push({
                    path: "/webSendRecord",
                    query: {
                      poolId: this.sendList.id,
                    },
                  });
                }
              );
            }
          } else {
            return false;
          }
        }
      })
    },
    beforeAvatarUploadAtt(file, type) {
      const siJPGGIF = file.name.split('.')[file.name.split('.').length - 1]
      if (type == 'batch') {
        const fileType = ['xlsx']
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning('上传文件只能是xlsx格式!')
          return false
        }
      } else {
        const isLt5M = file.size / 1024 / 1024 < 5
        const fileType = [
          'jpg',
          'jpeg',
          'gif',
          'pdf',
          'doc',
          'docx',
          'rar',
          'zip',
        ]
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning(
            '上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!'
          )
          return false
        }
        if (!isLt5M) {
          this.$message.warning('上传文件大小不能超过 5MB!')
          return false
        }
      }
    },
    handleSuccessAtt(res, fileList, type) {
      if (type == 'batch') {
        if (res.code == 200) {
          this.formop.filePath = res.data.fullpath
          this.formop.fileOriginalName = res.data.fileName
          this.formop.totalNum = res.data.total
          this.$refs.uploadElement.clearValidate('path')
          this.$message({
            message: res.msg,
            type: 'success',
          })
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      }
    },
    handleRemoveAtt(file, type) {
      if (type == 'batch') {
        this.formop.filePath = ''
        this.formop.fileOriginalName = ''
        this.formop.totalNum = ''
      }
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        const { groupId, userName } = this.$route.query
        this.tabelAlllist.groupId = groupId
        this.formInline.groupId = groupId
        this.tempList.clientName = userName
        this.formop.userName = userName
        this.formop.groupId = groupId
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
        this.gettableLIst()
        this.getTempList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    const { groupId, userName } = this.$route.query
    this.tabelAlllist.groupId = groupId
    this.formInline.groupId = groupId
    this.tempList.clientName = userName
    this.formop.userName = userName
    this.formop.groupId = groupId
    this.token = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    this.gettableLIst()
    this.getTempList()
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        // this.formop.idx = "";
        // this.formop.name = "";
        this.$refs.formop.resetFields()
        this.formop.tempId = ''
        // this.formop.id = ""
        this.formop.tempName = ''
        this.formop.content = ''
        this.formop.filePath = ''
        this.formop.fileOriginalName = ''
        this.formop.totalNum = ''
        this.formop.labelName = ''
        this.batchFileList = []
        this.formop.sendType = 1
        // this.id = ""
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$refs.sendList.resetFields()
        this.sendList.id = "";
        this.sendList.filePath = "";
        this.sendList.fileList = [];
        this.sendList.content = ''
        this.activeName = "1";
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter>div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.custom-textarea :deep(.el-textarea__inner) {
  height: 100px;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
</style>
