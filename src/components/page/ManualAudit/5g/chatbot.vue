<template>
  <div class="container_left">
    <div style="padding: 10px">
      <el-form label-width="95px" :model="ruleForm" :inline="true" ref="ruleForm" class="demo-ruleForm">
        <el-form-item label="用户名称" prop="username">
          <el-input class="input-w" v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="customerName">
          <el-input class="input-w" v-model="ruleForm.customerName"></el-input>
        </el-form-item>
        <el-form-item label="企业ID" prop="customerId">
          <el-input class="input-w" v-model="ruleForm.customerId"></el-input>
        </el-form-item>
        <el-form-item label="ChatBot名称" prop="chatbotName">
          <el-input class="input-w" v-model="ruleForm.chatbotName"></el-input>
        </el-form-item>
        <el-form-item label="ChatBotID" prop="chatbotId">
          <el-input class="input-w" v-model="ruleForm.chatbotId"></el-input>
        </el-form-item>
        <el-form-item label="签名" prop="autoGraph">
          <el-input class="input-w" v-model="ruleForm.autoGraph"></el-input>
        </el-form-item>
        <el-form-item label="通道" prop="channelId">
          <el-select class="input-w" v-model="ruleForm.channelId" placeholder="选择通道" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通道状态" prop="channelStatusArr">
          <el-select class="input-w" v-model="ruleForm.channelStatusArr" multiple placeholder="请选择">
            <el-option label="新增审核中" value="10"> </el-option>
            <el-option label="新增失败" value="15"> </el-option>
            <el-option label="新增成功" value="20"> </el-option>
            <el-option label="白名单审核中" value="22"> </el-option>
            <el-option label="白名单审核不通过" value="24"> </el-option>
            <el-option label="调试" value="26"> </el-option>
            <el-option label="上线审核中" value="28"> </el-option>
            <el-option label="上线不通过" value="30"> </el-option>
            <el-option label="已上线" value="32"> </el-option>
            <el-option label="已停用" value="40"> </el-option>
            <!-- <el-option label="注销中" value="-1"> </el-option>
            <el-option label="注销失败" value="-2"> </el-option>
            <el-option label="已注销" value="-3"> </el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-input class="input-w" v-model="ruleForm.reason"></el-input>
        </el-form-item>
        <el-form-item label="创建人" prop="createdUser">
          <el-input class="input-w" v-model="ruleForm.createdUser"></el-input>
        </el-form-item>
        <el-form-item label="创建时间" prop="time1">
          <el-date-picker v-model="ruleForm.time1" type="datetimerange" :picker-options="pickerOptions" @change="timeD"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" align="right">
          </el-date-picker>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')">查询</el-button>
          <el-button plain type="primary" @click="resetForm('ruleForm')">重置</el-button>
        </div>
      </el-form>
    </div>
    <!-- <div style="padding: 0 10px">
      <el-dropdown split-button type="primary" @command="handleClick">
        批量操作
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item :disabled="!chatbotIdList.length" command="baobei"
              >一键报备</el-dropdown-item
            >
            <el-dropdown-item command="shangjia">一键上架</el-dropdown-item>
            <el-dropdown-item command="shangxian">一键上线</el-dropdown-item>
            <el-dropdown-item command="tingyong">一键停用</el-dropdown-item>
            <el-dropdown-item command="shixin">视信回落</el-dropdown-item>
            <el-dropdown-item command="daoru">批量导入</el-dropdown-item>
            <el-dropdown-item command="daochu">批量导出</el-dropdown-item>
          </el-dropdown-menu>
        </template>
</el-dropdown> -->
    <!-- <el-button
        :disabled="!chatbotIdList.length"
        type="primary"
        @click="allBacth"
        >一键报备</el-button
      >
      <el-tooltip
        class="item"
        effect="dark"
        content="调试状态下可一键上架"
        placement="top"
      >
        <el-button type="primary" @click="allChannelBacth('一键上架')"
          >一键上架</el-button
        >
      </el-tooltip>
      <el-button type="primary" @click="allChannelBacth('一键上线')"
        >一键上线</el-button
      >
      <el-tooltip
        class="item"
        effect="dark"
        content="上线状态下可一键停用"
        placement="top"
      >
        <el-button type="danger" @click="allChannelBacth('一键停用')"
          >一键停用</el-button
        >
      </el-tooltip>
      <el-button type="primary" @click="receiptVideo">视信回落</el-button>
      <el-button type="primary" @click="batchImport">批量导入</el-button>
      <el-button type="primary" @click="batchderive">批量导出</el-button> -->
    <!-- </div> -->
    <div style="padding: 0 10px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
        @selection-change="handleSelectionChange"
      > -->

      <vxe-toolbar ref="toolbarRef" custom="">
        <template #buttons>
          <el-dropdown split-button type="primary" @command="handleClick">
            批量操作
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :disabled="!chatbotIdList.length" command="baobei">一键报备</el-dropdown-item>
                <el-dropdown-item command="shangjia">一键上架</el-dropdown-item>
                <el-dropdown-item command="shangxian">一键上线</el-dropdown-item>
                <el-dropdown-item command="tingyong">一键停用</el-dropdown-item>
                <el-dropdown-item command="shixin">视信回落</el-dropdown-item>
                <el-dropdown-item command="daoru">批量导入</el-dropdown-item>
                <el-dropdown-item command="logo">批量logo导入</el-dropdown-item>
                <el-dropdown-item command="daochu">批量导出</el-dropdown-item>
                <!-- <el-dropdown-item command="shuoming">批量授权书导入</el-dropdown-item> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </vxe-toolbar>
      <vxe-table ref="tableRef" id="chatbot" border stripe show-overflow :custom-config="customConfig"
        :column-config="{ resizable: true }" :row-config="{ isHover: true }" :cell-config="{height: 80}" min-height="1"
        v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;" :data="tableDataObj.tableData" @checkbox-all="handleSelectionChange"
        @checkbox-change="handleSelectionChange">

        <vxe-column type="checkbox" width="50"> </vxe-column>
        <vxe-column field="logo" title="logo" width="100">
          <template #default="scope">
            <img v-if="scope.row.logoUrl" :src="scope.row.logoUrl" width="50px" />
          </template>
        </vxe-column>
        <vxe-column field="用户名称" title="用户名称" width="110px">
          <template #default="scope">
            <div class="wrapper-text">
              {{ scope.row.username }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                @click="handleCopy(scope.row.username, $event)"></i> -->
              <CopyTemp :content="scope.row.username" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="企业ID" title="企业ID" width="180">
          <template #default="scope">
            <div>
              {{ scope.row.customerId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                @click="handleCopy(scope.row.customerId, $event)"></i> -->
              <CopyTemp :content="scope.row.customerId" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="企业名称" title="企业名称" width="120px">
          <template #default="scope">
            <Tooltip v-if="scope.row.customerName" :content="scope.row.customerName" className="wrapper-text"
              effect="light">
            </Tooltip>
          </template>
        </vxe-column>
        <vxe-column field="chatbotID" title="chatbotID" width="200px">
          <template #default="scope">
            <div>
              {{ scope.row.chatbotId }}
              <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                @click="handleCopy(scope.row.chatbotId, $event)"></i> -->
              <CopyTemp :content="scope.row.chatbotId" />
            </div>
          </template>
        </vxe-column>
        <vxe-column field="chatbot名称" title="chatbot名称" width="120px">
          <template #default="scope">
            <Tooltip v-if="scope.row.chatbotName" :content="scope.row.chatbotName" className="wrapper-text"
              effect="light">
            </Tooltip>
          </template>
        </vxe-column>
        <vxe-column field="签名" title="签名" width="120px">
          <template #default="scope">
            <Tooltip v-if="scope.row.autoGraph" :content="scope.row.autoGraph" className="wrapper-text" effect="light">
            </Tooltip>
          </template>
        </vxe-column>
        <vxe-column field="回落视信" title="回落视信" width="120px">
          <template #default="scope">
            <el-tag :disable-transitions="true" type="success" v-if="scope.row.receiptVideoTag == '1'"
              effect="dark">开启</el-tag>
            <el-tag :disable-transitions="true" type="error" v-else-if="scope.row.fallbackType == '0'"
              effect="dark">关闭</el-tag>
          </template>
        </vxe-column>
        <vxe-column field="移动" title="移动" width="80">
          <template #default="scope">
            <el-tooltip v-if="scope.row.ydStatus == 0" effect="dark" content="不可用" placement="top">
              <div>
                <span class="circle"></span>
              </div>
            </el-tooltip>
            <el-tooltip v-else-if="scope.row.ydStatus == 1" effect="dark" content="可用" placement="top">
              <span class="circle-active"></span>
            </el-tooltip>
            <el-tooltip v-else effect="dark" content="等待中" placement="top">
              <span class="circle_a1"></span>
            </el-tooltip>
            <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row.ydStatus == 0 ? '不可用' : scope.row.ydStatus == 1 ? '可用' : '等待中'"
              placement="top-start"
            >
              <i 
                class="iconfont icon-yidong"
                :style="{
                  fontSize: '20px',
                  margin: '0 5px',
                  color: scope.row.ydStatus == 0 ? '#ccd5db' : scope.row.ydStatus == 1 ? '#409eff' : '#ccd5db'
                }"
              ></i>
            </el-tooltip> -->
          </template>
        </vxe-column>
        <vxe-column field="联通" title="联通" width="80">
          <template #default="scope">
            <el-tooltip v-if="scope.row.ltStatus == 0" effect="dark" content="不可用" placement="top">
              <div>
                <span class="circle"></span>
              </div>
            </el-tooltip>
            <el-tooltip v-else-if="scope.row.ltStatus == 1" effect="dark" content="可用" placement="top">
              <span class="circle-active"></span>
            </el-tooltip>
            <el-tooltip v-else effect="dark" content="等待中" placement="top">
              <span class="circle_a1"></span>
            </el-tooltip>
            <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row.ltStatus == 0 ? '不可用' : scope.row.ltStatus == 1 ? '可用' : '等待中'"
              placement="top-start"
            >
              <i 
                class="iconfont icon-liantong"
                :style="{
                  fontSize: '20px',
                  margin: '0 5px',
                  color: scope.row.ltStatus == 0 ? '#ccd5db' : scope.row.ltStatus == 1 ? '#F56C6C' : '#ccd5db'
                }"
              ></i>
            </el-tooltip> -->
          </template>
        </vxe-column>
        <vxe-column field="电信" title="电信" width="80">
          <template #default="scope">
            <el-tooltip v-if="scope.row.dxStatus == 0" effect="dark" content="不可用" placement="top">
              <div>
                <span class="circle"></span>
              </div>
            </el-tooltip>
            <el-tooltip v-else-if="scope.row.dxStatus == 1" effect="dark" content="可用" placement="top">
              <span class="circle-active"></span>
            </el-tooltip>
            <el-tooltip v-else effect="dark" content="等待中" placement="top">
              <span class="circle_a1"></span>
            </el-tooltip>
            <!-- <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row.dxStatus == 0 ? '不可用' : scope.row.dxStatus == 1 ? '可用' : '等待中'"
              placement="top-start"
            >
              <i 
                class="iconfont icon-dianxin"
                :style="{
                  fontSize: '20px',
                  margin: '0 5px',
                  color: scope.row.dxStatus == 0 ? '#ccd5db' : scope.row.dxStatus == 1 ? '#409eff' : '#ccd5db'
                }"
              ></i>
            </el-tooltip> -->
          </template>
        </vxe-column>
        <vxe-column field="审核状态" title="审核状态" width="120">
          <template #default="scope">
            <div class="item_status" style="
                background: #f6f6f6;
                color: #999999;
                border: 1px solid #dddddd;
              " v-if="scope.row.status == 0">
              待提交
            </div>
            <div class="item_status" style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              " v-if="scope.row.status == 5">
              初审中
            </div>
            <div class="item_status" style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              " v-if="scope.row.status == 30">
              审核通过
            </div>
            <div class="item_status" style="
                background: #ffe7e7;
                color: #ff6666;
                border: 1px solid #fac2c2;
              " v-if="scope.row.status == 20">
              审核失败
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建人" title="创建人" width="120px">
          <template #default="scope">
            <div class="wrapper-text">
              {{ scope.row.createdUser }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="100">
          <template #default="scope">
            <p class="wrapper-text" v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, "{y}-{m}-{d}") }}
            </p>
            <p class="wrapper-text" v-if="scope.row.createdTime">
              {{ $parseTime(scope.row.createdTime, "{h}:{i}:{s}") }}
            </p>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" min-width="240" fixed="right">
          <template #default="scope">
            <div style="display: flex; flex-wrap: wrap; height: 80px; align-content: space-evenly;">
              <el-button v-if="scope.row.status == 30" link style="color: #16a589; margin-left: 10px"
                @click="checked(scope.row)"><el-icon>
                  <SuccessFilled />
                </el-icon> 查看资料</el-button>

              <el-button v-else link style="color: #e6a23c; margin-left: 10px" @click="checked(scope.row)"><el-icon>
                  <WarningFilled />
                </el-icon>资料审核</el-button>
              <el-button v-if="
                roleId == 19 ||
                roleId == 3 ||
                roleId == 4 ||
                roleId == 6 ||
                roleId == 7
              " style="margin-left: 10px; color: #409eff;" link @click="editBot(scope.row)">
                <el-icon>
                  <EditPen />
                </el-icon>chatbot编辑</el-button>
              <el-button link style="color: #409eff; margin-left: 10px" @click="testwhiteList(scope.row)">
                <el-icon>
                  <Position />
                </el-icon>chatbot报备</el-button>
            </div>
          </template>
        </vxe-column>
      </vxe-table>

      <div class="paginationBox">
        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
          :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="chatbot资料详情" v-model="dialogVisible" width="1120px" :before-close="handleClose">
      <el-form :model="checkList" :inline="true" :rules="rules" ref="checkList" class="demo-ruleForm">
        <el-descriptions title="基本信息" size="default" border>
          <el-descriptions-item label-class-name="my-label" label="头像">
            <img v-if="checkList.logoUrl" :src="checkList.logoUrl" alt="" width="50px" />
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="签名">
            {{ checkList.autoGraph || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="chatbot主页地址">
            <Tooltip v-if="checkList.website" :content="checkList.website" className="wrapper-text-t" effect="light">
            </Tooltip>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="用户名称">{{
            checkList.username || "-"
            }}</el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="服务条款">
            <Tooltip v-if="checkList.terms" :content="checkList.terms" className="wrapper-text-t" effect="light">
            </Tooltip>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="chatbot办公地址">
            <Tooltip v-if="checkList.address" :content="checkList.address" className="wrapper-text-t" effect="light">
            </Tooltip>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="chatBot名称">{{ checkList.chatbotName || "-"
            }}</el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="服务描述">
            <Tooltip v-if="checkList.description" :content="checkList.description" className="wrapper-text-t"
              effect="light">
            </Tooltip>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="经度">
            {{ checkList.longitude || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="chatbotID">
            {{ checkList.chatbotId || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="服务电话">
            {{ checkList.callbackPhone || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="纬度">
            {{ checkList.latitude || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label-class-name="my-label" label="联系人邮箱">
            {{ checkList.email || "-" }}
          </el-descriptions-item>
          <template v-if="
            checkList.status != 0 &&
            checkList.status != 5 &&
            checkList.status != 20
          ">
            <el-descriptions-item label-class-name="my-label" label="审核原因">
              <Tooltip v-if="checkList.auditReason" :content="checkList.auditReason" className="wrapper-text-t"
                effect="light">
              </Tooltip>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label" label="审核人">
              {{ checkList.auditUser || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label" label="审核时间">
              <span v-if="checkList.auditTime">{{
                moment(checkList.auditTime).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label" label="附件">
              <a @click="down(checkList.attachment)" v-if="checkList.attachment" href="javascript:void(0)"
                rel="noopener noreferrer">附件下载</a>
              <span v-else>-</span>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div style="margin: 18px 0" v-if="
          checkList.status == 0 ||
          checkList.status == 5 ||
          checkList.status == 20
        ">
          <el-form-item v-if="checkList.attachment" label="附件:">
            <a @click="down(checkList.attachment)" v-if="checkList.attachment" href="javascript:void(0)"
              rel="noopener noreferrer">附件下载</a>
          </el-form-item>
          <el-form-item label="审核原因:" prop="reason">
            <div>
              <el-input style="width: 250px; height: 70px" :maxlength="20" show-word-limit type="textarea"
                placeholder="不能超过20个字符" v-model="checkList.reason"></el-input>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="
          checkList.status == 0 ||
          checkList.status == 5 ||
          checkList.status == 20
        " type="primary" @click="audit(checkList, 1, '通过', 'checkList')">
          通过</el-button>
        <el-button v-if="
          checkList.status == 0 ||
          checkList.status == 5 ||
          checkList.status == 20
        " type="danger" style="margin-left: 10px" @click="audit(checkList, 2, '不通过', 'checkList')">不通过</el-button>
      </template>
    </el-dialog>
    <el-dialog title="修改chatbot" v-model="chatbotVisible" width="45%" :before-close="handleClose">
      <el-steps :active="steps" simple>
        <el-step title="修改chatbot" icon="el-icon-edit"></el-step>
        <el-step title="修改回落视信" icon="el-icon-edit"></el-step>
      </el-steps>
      <div v-if="steps == 1" style="margin-top: 10px">
        <el-form :inline="true" :model="chatbotFrom" :rules="rulesc" ref="chatbotFrom" label-width="130px"
          class="demo-chatbotFrom">
          <div>
            <el-form-item label="ChatBot头像" prop="logoUrl">
              <div style="display: flex">
                <div v-if="!logoFlag" style="display: flex; position: relative">
                  <img :src="chatbotFrom.logoUrl" alt="" width="70px" height="70px" />
                  <el-button style="
                      height: 30px;
                      position: absolute;
                      bottom: 0;
                      right: 0;
                      margin-right: -80px;
                    " type="primary" @click="Removec"><el-icon style="margin-right: 4px;">
                      <Plus />
                    </el-icon>上传</el-button>
                </div>
                <div v-else>
                  <div style="display: flex">
                    <el-upload :action="action" :headers="token" list-type="picture-card" :limit="1"
                      :file-list="fileListc" :before-upload="beforeAvatarUploadc" :on-remove="handleRemovec"
                      :on-success="handleSuccessc">
                      <i class="el-icon-plus"></i>
                    </el-upload>
                    <div class="photo_active">
                      <div></div>
                      <div>
                        <p>图片尺寸：400*400！</p>
                        <p>图片大小：不能超过50k！</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item label="用户名称" prop="username">
            <el-input disabled :maxlength="20" style="width: 240px" show-word-limit
              v-model="chatbotFrom.username"></el-input>
          </el-form-item>
          <el-form-item prop="chatbotName">
            <template #label style="display: inline-block">
              <div style="display: flex; align-items: center;">
                <span>ChatBot名称</span>
                <el-tooltip effect="dark" content="针对具备5G消息能力的终端，在终端的消息列表及聊天页面顶端展示" placement="top">
                  <el-icon style="color: #409eff; margin-left: 5px">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input :maxlength="20" style="width: 240px" show-word-limit v-model="chatbotFrom.chatbotName"></el-input>
          </el-form-item>

          <el-form-item label="签名" prop="autoGraph">
            <template #label style="display: inline-block">
              <div style="display: flex; align-items: center;">
                <span>签名</span>
                <el-tooltip effect="dark" content="针对回落5G行业消息（标准版）的终端，在每条消息内容前携带此内容" placement="top">
                  <el-icon style="color: #409eff; margin-left: 5px">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input :maxlength="20" show-word-limit style="width: 240px" placeholder="不能超过20个字符"
              v-model="chatbotFrom.autoGraph"></el-input>
          </el-form-item>
          <el-form-item label="移动扩展号" prop="ext">
            <el-input onInput="value=value.replace(/[^\d]/g,'')" style="width: 240px" :maxlength="8" show-word-limit
              placeholder="最大8位数" v-model="chatbotFrom.ext"></el-input>
          </el-form-item>
          <el-form-item label="联通扩展号" prop="ltExt">
            <el-input onInput="value=value.replace(/[^\d]/g,'')" style="width: 240px" :maxlength="8" show-word-limit
              placeholder="最大8位数" v-model="chatbotFrom.ltExt"></el-input>
          </el-form-item>
          <el-form-item label="电信扩展号" prop="dxExt">
            <el-input onInput="value=value.replace(/[^\d]/g,'')" style="width: 240px" :maxlength="8" show-word-limit
              placeholder="最大8位数" v-model="chatbotFrom.dxExt"></el-input>
          </el-form-item>
          <el-form-item label="服务描述" prop="description">
            <el-input :maxlength="166" show-word-limit type="textarea" placeholder="请输入服务描述"
              style="width: 240px; height: 80px" v-model="chatbotFrom.description"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input :maxlength="50" show-word-limit style="width: 240px" placeholder="不能超过50个字符"
              v-model="chatbotFrom.email"></el-input>
          </el-form-item>
          <el-form-item label="chatbot主页地址" prop="website">
            <el-input placeholder="请输入chatbot主页地址" style="width: 240px" v-model="chatbotFrom.website"></el-input>
          </el-form-item>
          <el-form-item label="chatbot办公地址" prop="address">
            <el-input placeholder="请输入chatbot办公地址" style="width: 240px" v-model="chatbotFrom.address"></el-input>
          </el-form-item>
          <el-form-item label="经度" prop="longitude">
            <el-input placeholder="请输入经度" style="width: 240px" v-model="chatbotFrom.longitude"></el-input>
          </el-form-item>
          <el-form-item label="纬度" prop="latitude">
            <template #label>
              <div style="display: flex; align-items: center;">
                <span>纬度</span>
                <el-tooltip effect="dark" content="请到相关网站查询您公司所在经纬度，便于支持类似“附近”功能" placement="top">
                  <el-icon style="color: #409eff; margin-left: 5px">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-input placeholder="请输入纬度" style="width: 240px" v-model="chatbotFrom.latitude"></el-input>
          </el-form-item>
          <el-form-item label="服务电话" prop="callbackPhone">
            <el-input :maxlength="21" show-word-limit placeholder="请输入服务电话" style="width: 240px"
              v-model="chatbotFrom.callbackPhone"></el-input>
          </el-form-item>
          <el-form-item label="服务条款" prop="terms">
            <el-input placeholder="请输入服务条款" style="width: 240px" v-model="chatbotFrom.terms"></el-input>
          </el-form-item>
          <el-form-item label="营销短信示例" prop="yxSmsExample">
            <el-input type="textarea" maxlength="140" show-word-limit placeholder="请输入营销短信示例" style="width: 240px"
              v-model="chatbotFrom.yxSmsExample"></el-input>
          </el-form-item>
          <el-form-item label="通知短信示例" prop="hySmsExample">
            <el-input type="textarea" maxlength="140" show-word-limit placeholder="请输入通知短信示例" style="width: 240px"
              v-model="chatbotFrom.hySmsExample"></el-input>
          </el-form-item>
          <el-form-item label="附件上传" prop="attachment">
            <el-upload :action="action" :headers="token" :limit="1" :file-list="attachmentList"
              :before-upload="beforeAvatarUploadAtt" :on-remove="handleRemoveAtt" :on-success="handleSuccessAtt"
              multiple>
              <el-button size="default" type="primary">点击上传</el-button>
              <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
            </el-upload>
          </el-form-item>
          <el-form-item label="电信5G授权书" prop="authorizationLetterUrl">
            <el-upload :action="action" :headers="token" :limit="1" :file-list="authorizationLetterList"
              :before-upload="beforeAvatarUploadAuthLetter" :on-remove="handleRemoveAuthLetter" :on-success="handleSuccessAuthLetter"
              multiple>
              <el-button size="default" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">支持上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式文件，且不超过5MB</div>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
      <div v-if="steps == 2" style="margin-top: 10px">
        <el-form :inline="false" :model="chatbotFrom" :rules="rulesc" ref="chatbotFrom" label-width="130px"
          class="demo-chatbotFrom">
          <div style="display: flex;">
            <div>
              <el-form-item label="文件上传" prop="receiptVideoUrl">
                <el-upload ref="upload" :action="fallbackAction" :headers="token" :limit="1" list-type="picture-card"
                  v-bind:data="{
                    FoldPath: '上传目录',
                    SecretKey: '安全验证',
                    chatbotId: chatbotFrom.chatbotId,
                  }" :auto-upload="false" :file-list="fileListr" :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload" :on-remove="handleRemove" :on-change="handleAvatarChange">
                  <i class="el-icon-plus"></i>
                  <template #tip class="el-upload__tip">
                    <!-- 只能上传jpg文件，且不超过800kb -->
                  </template>
                </el-upload>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="标题" prop="receiptVideoTitle">
                <el-input type="text" placeholder="请输入内容" v-model="chatbotFrom.receiptVideoTitle" maxlength="16"
                  show-word-limit>
                </el-input>
              </el-form-item>
              <el-form-item label="是否开启" prop="receiptVideoTag">
                <el-radio-group v-model="chatbotFrom.receiptVideoTag">
                  <el-radio value="1">开启</el-radio>
                  <el-radio value="0">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <template #footer class="dialog-footer">
        <el-button @click="chatbotVisible = false">取 消</el-button>
        <el-button v-if="steps > 1" type="primary" @click="prevStep('chatbotFrom')">上一步</el-button>
        <el-button v-if="steps < 2" type="primary" @click="nextStep('chatbotFrom')">下一步</el-button>
        <el-button v-if="steps == 2" type="primary" @click="quiteBot('chatbotFrom')">确 定</el-button>
      </template>
    </el-dialog>
    <!-- 一键报备 -->
    <el-dialog title="一键报备" v-model="registerVisible" width="30%" :before-close="handleClose">
      <el-form :model="registerList" :inline="true" :rules="rules" label-width="70px" ref="registerList"
        class="demo-form-inline">
        <el-form-item label="通道" prop="channelId">
          <el-select v-model="registerList.channelId" placeholder="选择通道" clearable @change="handChannel">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="源通道" prop="sourceChannelId">
          <el-select v-model="registerList.sourceChannelId" placeholder="选择通道" clearable>
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="registerVisible = false">取 消</el-button>
        <el-button type="primary" @click="registerQuite('registerList')">确 定</el-button>
      </template>
    </el-dialog>
    <!-- 一键上架 -->
    <el-dialog :title="onLineTitle" v-model="channelOnlineVisible" width="30%" :before-close="handleClose">
      <el-form :model="channelFrom" :inline="true" :rules="rules" label-width="70px" ref="channelFrom"
        class="demo-form-inline">
        <el-form-item label="通道" prop="channelId">
          <el-select v-model="channelFrom.channelId" placeholder="选择通道" clearable class="input-w">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="channelOnlineVisible = false">取 消</el-button>
        <el-button type="primary" @click="channelOnline('channelFrom')">确 定</el-button>
      </template>
    </el-dialog>
    <el-dialog title="批量导入" v-model="importVisible" width="30%" :before-close="handleClose">
      <el-form :model="importList" :rules="rules" label-width="70px" ref="importList" class="demo-form-inline">
        <el-form-item label="通道" prop="channelJsonStr">
          <el-select v-model="importList.channelJsonStr" placeholder="选择通道" multiple clearable
            @change="handbatchChannel" style="width: 300px">
            <el-option v-for="item in channel" :key="item.channelCode" :label="item.channelName"
              :value="item.channelCode"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="导入文件" prop="file">
          <el-upload :action="importAction" :headers="token" :limit="1" v-bind:data="{
            FoldPath: '上传目录',
            SecretKey: '安全验证',
            channelJsonStr: JSON.stringify(this.channelJson),
          }" :file-list="importFileList" :before-upload="(file) => {
                return beforeAvatarUploadAtt(file, 'import');
              }
              " :on-remove="(file) => {
                return handleRemoveAtt(file, 'import');
              }
              " :on-success="(response, fileList) => {
                return handleSuccessAtt(response, fileList, 'import');
              }
              " multiple>
            <el-button size="default" type="primary">点击上传</el-button>
            <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
          </el-upload>
          <a href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/3071770a545d49edf02329a0d52ffc2b"
            target="_blank" rel="noopener noreferrer">模板下载</a>
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="importVisible = false">取 消</el-button>
        <!-- <el-button type="primary" @click="handelbatchImport('importList')"
          >确 定</el-button
        > -->
      </template>
    </el-dialog>
    <el-dialog title="视信回落" v-model="receiptVideoVisible" width="30%" :before-close="handleClose">
      <el-form :model="videoFrom" :rules="rules" label-width="90px" ref="videoRef" class="demo-form-inline">
        <el-form-item label="是否开启" prop="operatorType">
          <el-radio-group v-model="videoFrom.operatorType">
            <el-radio value="1">开启</el-radio>
            <el-radio value="0">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件上传" prop="fileList">
          <el-upload class="upload-demo" ref="videoUpload" :file-list="videoFrom.fileList" :action="videoAction"
            :headers="token" :limit="1" v-bind:data="{
              FoldPath: '上传目录',
              SecretKey: '安全验证',
              operatorType: videoFrom.operatorType,
            }" :auto-upload="false" :on-success="handleVideoSuccess" :before-upload="(file) => {
                return beforeAvatarUploadAtt(file, 'import');
              }
              " :on-remove="(file) => {
                return handleRemoveAtt(file, 'video');
              }
              ">
            <el-button size="default" type="primary">点击上传</el-button>
            <template #tip class="el-upload__tip">只能上传xlsx文件</template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="receiptVideoVisible = false">取 消</el-button>
        <el-button type="primary" @click="handelReceiptVideo('videoRef')">确 定</el-button>
      </template>
    </el-dialog>
    <el-dialog title="批量导入" v-model="logoImportVisible" width="30%" :before-close="handleClose">
      <el-form :model="logoFrom" :rules="rules" label-width="70px" ref="logoFrom" class="demo-form-inline">
        <el-form-item label="logo上传" prop="fileList">
         <el-upload :action="logoAction" :headers="token" :limit="1" :file-list="logoFrom.fileList"
              :before-upload="handleLogoUpload" :on-remove="handleLogoRemove" :on-success="handleLogoSuccess" :on-error="handleLogoError">
              <el-button size="default" type="primary">点击上传</el-button>
            </el-upload>  
            <a style="margin-left: 10px;" href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/e2201773e17c1748fb0aeea2c1dde98e"
                rel="noopener noreferrer">模板下载</a>
        </el-form-item>
      </el-form>
      <template #footer class="dialog-footer">
        <el-button @click="logoImportVisible = false">取 消</el-button>
        <el-button type="primary" @click="handelLogoImport('logoFrom')">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import { mapState, mapMutations, mapActions } from "vuex";
import getNoce from '../../../../plugins/getNoce';
// import clip from '../../../../utils/clipboard'
import axios from "axios";
import CopyTemp from "@/components/publicComponents/CopyTemp.vue";
export default {
  name: "chatbotAudit",
  components: {
    Tooltip,
    CopyTemp
  },
  data() {
    var longitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/;
        if (!reg.test(value)) {
          callback(new Error("小数点后最多七位，请重新输入"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入经度"));
      }
    };
    var latitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/;
        if (!reg.test(value)) {
          callback(new Error("小数点后最多七位，请重新输入"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入纬度"));
      }
    };
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      status: "",
      dialogVisible: false,
      registerVisible: false,
      whiteDialogVisible: false,
      chatbotVisible: false,
      importVisible: false,
      channelOnlineVisible: false,
      receiptVideoVisible: false,
      logoImportVisible: false,
      // uploadType: '',
      fileListr: [],
      steps: 1,
      onLineTitle: "",
      whiteList: "",
      reason: "",
      codeFlag: true,
      action: window.path.cpus + "v3/file/uploadFile",
      importAction: window.path.fiveWeb + "/operator/chatbot/batchAddChatbot",
      fallbackAction: window.path.fiveWeb + "/operator/chatbot/upload",
      videoAction:
        window.path.fiveWeb + "/operator/chatbot/batchOpenOrCloseReceiptVideo",
      logoAction: window.path.fiveWeb + "/operator/auto/importChatbotInfo",
      token: {},
      actionFlag: true,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      videoFrom: {
        operatorType: "0", //0关闭，1开启
        fileList: [],
      },
      logoFrom: {
        fileList: [],
        // fileList1: [],
      },
      ruleForm: {
        chatbotName: "",
        customerName: "",
        chatbotId: "",
        customerId: "",
        username: "",
        autoGraph: "",
        channelId: "",
        createdUser: "",
        reason: "",
        createdTimeStart: "",
        createdTimeEnd: "",
        time1: [],
        channelStatusArr: [],
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        chatbotName: "",
        customerName: "",
        chatbotId: "",
        customerId: "",
        channelId: "",
        autoGraph: "",
        username: "",
        createdUser: "",
        reason: "",
        createdTimeStart: "",
        createdTimeEnd: "",
        time1: [],
        channelStatusArr: [],
        currentPage: 1,
        pageSize: 10,
      },
      checkList: {
        reason: "",
      },
      logoFlag: true,
      chatbotFrom: {
        username: "",
        userId: "",
        chatbotName: "", //chatbot名称
        autoGraph: "", //签名
        bubbleColor: "#FFFFFF", //气泡颜色
        // ext: "", //扩展号
        ext: "", //移动扩展号
        ltExt: "", //联通扩展号
        dxExt: "", //电信扩展号
        // industry: "", //实际下发行业
        // category: "", //行业类型
        callbackPhone: "", //服务电话
        // cityCode: "", //地市编码
        // officeCode: "", //区编码
        // provinceCode: "", //省份编码
        // ext: "", //扩展号
        logoUrl: "", //头像
        email: "", //邮箱
        description: "", //描述信息
        terms: "", //服务条款
        website: "", //chatbot主页地址
        longitude: "", //经度
        latitude: "", //纬度
        address: "", //chatbot办公地址
        attachment: "",
        authorizationLetterUrl: "", //电信5G授权书
        yxSmsExample: "", //营销短信示例
        hySmsExample: "", //通知短信示例
        receiptVideoUrl: "", //回执视频地址
        receiptVideoTag: "0", //回执视频标签
        receiptVideoTitle: "", //回执视频标题
        // customerContactPerson: "", //客户联系人
        // contactPersonPhone: "", //联系人电话
        // debugWhiteAddress: "", //调试白名单
        statusOf: "新增chatbot信息",
      },
      attachmentList: [],
      rulesc: {
        chatbotName: [
          { required: true, message: "请输入chatbot名称", trigger: "blur" },
        ],
        logoUrl: [
          { required: true, message: "请上传chatbot头像", trigger: "blur" },
        ],
        yxSmsExample: [
          { required: true, message: "请输入营销短信示例", trigger: "blur" },
        ],
        hySmsExample: [
          { required: true, message: "请输入通知短信示例", trigger: "blur" },
        ],
        // attachment: [
        //   { required: true, message: "请上传附件", trigger: "blur" },
        // ],
        // category: [
        //   { required: true, message: "请选择行业类型", trigger: "blur" },
        // ],
        // industry: [
        //   { required: true, message: "请选择实际下发行业", trigger: "blur" },
        // ],
        // bubbleColor: [
        //   { required: true, message: "请选择气泡颜色", trigger: "blur" },
        // ],
        autoGraph: [{ required: true, message: "请输入签名", trigger: "blur" }],
        // callbackPhone: [
        //   { required: true, message: "请输入服务电话", trigger: "blur" },
        // ],
        // cityCode: [
        //   { required: true, message: "请选择地市编码", trigger: "blur" },
        // ],
        // officeCode: [
        //   { required: true, message: "请选择区编码", trigger: "blur" },
        // ],
        // terms: [{ required: true, message: "请输入服务条款", trigger: "blur" }],
        // website: [
        //   { required: true, message: "请输入chatbot主页地址", trigger: "blur" },
        // ],
        // longitude: [{ required: true, validator: longitude, trigger: "blur" }],
        // latitude: [{ required: true, validator: latitude, trigger: "blur" }],
        // address: [
        //   { required: true, message: "chatbot办公地址", trigger: "blur" },
        // ],
        // provinceCode: [
        //   { required: true, message: "请选择省份编码", trigger: "blur" },
        // ],
        ext: [
          {
            type: "number",
            required: true,
            message: "移动扩展号不能为空",
            trigger: "blur",
            transform: (value) => Number(value),
          },
        ],
        ltExt: [
          {
            type: "number",
            required: true,
            message: "联通扩展号不能为空",
            trigger: "blur",
            transform: (value) => Number(value),
          },
        ],
        dxExt: [
          {
            type: "number",
            required: true,
            message: "电信扩展号不能为空",
            trigger: "blur",
            transform: (value) => Number(value),
          },
        ],
        // email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
        description: [
          { required: true, message: "描述信息不能为空", trigger: "blur" },
        ],
        debugWhiteAddress: [
          { required: true, message: "调试白名单不能为空", trigger: "blur" },
        ],
      },
      detial: {},
      rules: {
        // code: [
        //   { required: true, validator: coderules, trigger: "blur" },
        //   { max: 4, message: "扩展码最多至4位", trigger: "blur" },
        // ],
        reason: [
          { required: true, message: "请输填写备注", trigger: "change" },
        ],
        channelId: [
          { required: true, message: "请选择通道", trigger: "change" },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        sourceChannelId: [
          { required: true, message: "请选择原通道", trigger: "change" },
          // { max: 8, message: "扩展码最多至8位", trigger: "change" },
        ],
        operatorType: [
          { required: true, message: "请选择是否开启", trigger: "change" },
        ],
        // fileList: [
        //   { required: true, message: "请上传文件", trigger: "change" },
        // ],
        // channelJsonStr: [
        //   {
        //     required: true,
        //     message: "请选择通道",
        //     trigger: "change",
        //   },
        // ],
        // customerContactPerson: [
        //   { required: true, message: "请输入客户联系人", trigger: "change" },
        // ],
        // contactPersonPhone: [
        //   { required: true, message: "请输入联系人电话", trigger: "change" },
        // ],
        // contactPersonEmail: [
        //   { required: true, message: "请输入联系人邮箱", trigger: "change" },
        // ],
      },
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      office: [],
      city: [],
      province: [],
      fileListc: [],
      authorizationLetterList: [], // 电信5G授权书文件列表
      importFileList: [],
      channel: [],
      fileName: "",
      chatbotIdList: [],
      registerList: {
        channelId: "",
        channelName: "",
        chatbotIdList: [],
        operator: "",
      },
      channelFrom: {
        channelId: "",
      },
      importList: {
        channelJsonStr: [],
        file: "",
      },
      channelJson: [],
    };
  },
  created() {
    // if(this.$route.query.username){
    //   this.ruleForm.username = this.$route.query.username
    //    Object.assign(this.tabelAlllist, this.ruleForm);
    // }
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.token = {
        Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
      };
      this.getchatList();
      this.channel5GList();
      // this.region();
    });
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        //     if(this.$route.query.username){
        //   this.ruleForm.username = this.$route.query.username
        //    Object.assign(this.tabelAlllist, this.ruleForm);
        // }
        this.token = {
          Authorization: "Bearer" + this.$common.getCookie("ZTADMIN_TOKEN"),
        };
        this.getchatList();
        this.channel5GList();
        // this.region();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    // region() {
    //   window.api.post(
    //     window.path.fiveWeb + "/region/page",
    //     {
    //       currentPage: 1,
    //       pageSize: 10,
    //     },
    //     (res) => {
    //       this.office = res.data.records;
    //     }
    //   );
    // },
    // citys(code) {
    //   window.api.get(window.path.fiveWeb + "/city/list/" + code, {}, (res) => {
    //     this.city = res.data;
    //   });
    // },
    // provinces(code) {
    //   window.api.get(window.path.fiveWeb + "/province/list/" + code, {}, (res) => {
    //     this.province = res.data;
    //   });
    // },
    /**获取通道*/
    channel5GList() {
      window.api.get(
        window.path.omcs + "operatingchannelinfo/channel5GList",
        {},
        (res) => {
          this.channel = res.data;
        }
      );
    },
    // handleCopy(name, event) {
    //   clip(name, event)
    // },
    timeD(e) {
      if (e) {
        this.ruleForm.createdTimeStart = moment(this.ruleForm.time1[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.ruleForm.createdTimeEnd = moment(this.ruleForm.time1[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      } else {
        this.ruleForm.createdTimeStart = "";
        this.ruleForm.createdTimeEnd = "";
      }
    },
    //一键上架
    handleSelectionChange(row) {
      if (row.records.length > 0) {
        let arr = []
        row.records.forEach(item => {
          arr.push(item.customerId)
        })
        this.chatbotIdList = arr
      } else {
        this.chatbotIdList = []
      }
    },
    handleClick(command) {
      // console.log(command,'command');
      if (command == "baobei") {
        this.allBacth();
      } else if (command == "shangjia") {
        this.allChannelBacth("一键上架");
      } else if (command == "shangxian") {
        this.allChannelBacth("一键上线");
      } else if (command == "tingyong") {
        this.allChannelBacth("一键停用");
      } else if (command == "daoru") {
        this.batchImport();
      } else if (command == "daochu") {
        this.batchderive();
      } else if (command == "shixin") {
        this.receiptVideo();
      } else if (command == "logo") {
        this.logoImport('logo');
      }
    },
    logoImport(type) {
      this.logoImportVisible = true;
      // this.uploadType = type;
    },
    allBacth() {
      this.registerVisible = true;
    },
    allChannelBacth(tit) {
      this.channelOnlineVisible = true;
      this.onLineTitle = tit;
    },
    batchImport() {
      this.importVisible = true;
    },
    receiptVideo() {
      this.receiptVideoVisible = true;
    },
    async batchderive() {
      const nonce = await getNoce.useNonce();
      let that = this;
      this.$confirm("是否确认批量导出chatbot?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          axios({
            method: "post",
            url: window.path.fiveWeb + "/operator/chatbot/export",
            data: this.tabelAlllist,
            headers: {
              "Content-Type": "application/json",
              Authorization:
                "Bearer " + window.common.getCookie("ZTADMIN_TOKEN"),
              'Once': nonce,
            },
            responseType: "blob",
          })
            .then(function (res) {
              let timeStamp = new Date().getTime();
              const blob = new Blob([res.data], {
                type: "application/octet-stream;charset=utf-8",
              });
              let link = document.createElement("a");
              let href = window.URL.createObjectURL(blob); //下载链接
              link.href = href;
              link.text = "下载";
              link.download = timeStamp + ".xlsx"; //下载后文件名
              document.body.appendChild(link);
              link.click(); //点击下载
              document.body.removeChild(link); //下载完成移除元素
              window.URL.revokeObjectURL(href);
              that.$message({
                message: "下载成功",
                type: "success",
              });
            })
            .catch((err) => {
              console.log(err, "err");
              that.$message({
                message: "下载失败",
                type: "error",
              });
            });
        })
        .catch((err) => {
          console.log(err, "err");
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handChannel(e) {
      if (e) {
        this.channel.forEach((item) => {
          if (e == item.channelCode) {
            this.registerList.channelName = item.channelName;
            this.registerList.operator = item.operatorsType;
          }
        });
      } else {
        this.registerList.channelName = "";
        this.registerList.operator = "";
      }
    },
    handbatchChannel(e) {
      let arr = [];
      arr = this.channel.map((item) => {
        return {
          channelId: item.channelCode,
          channelName: item.channelName,
          channelOperator: item.operatorsType,
        };
      });
      function getSame(arr1, arr2) {
        return [...new Set(arr1)].filter((item) =>
          arr2.includes(item.channelId)
        );
      }
      this.channelJson = getSame(arr, e);
    },
    registerQuite(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.registerList.chatbotIdList = this.chatbotIdList;
          if (
            this.registerList.channelId == this.registerList.sourceChannelId
          ) {
            this.$message({
              message: "请勿同通道操作！",
              type: "warning",
            });
          } else {
            this.$confirms.confirmation(
              "post",
              "是否确认一键报备",
              window.path.fiveWeb + "/operator/chatbot/convenientAddChannel",
              this.registerList,
              (res) => {
                if (res.code == 200) {
                  this.getchatList();
                  this.chatbotIdList = [];
                  this.registerVisible = false;
                  // this.$message({
                  //   message: res.msg,
                  //   type: "success",
                  // });
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "error",
                  // });
                }
              }
            );
          }
        }
      });
    },
    channelOnline(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.onLineTitle == "一键上架") {
            this.$confirms.confirmation(
              "post",
              `是否确认${this.onLineTitle}`,
              window.path.fiveWeb + "/operator/chatbot/batchArrive",
              this.channelFrom,
              (res) => {
                if (res.code == 200) {
                  this.getchatList();
                  this.channelOnlineVisible = false;
                  // this.$message({
                  //   message: res.msg,
                  //   type: "success",
                  // });
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "error",
                  // });
                }
              }
            );
          } else if (this.onLineTitle == "一键上线") {
            this.$confirms.confirmation(
              "post",
              `是否确认${this.onLineTitle}`,
              window.path.fiveWeb + "/operator/chatbot/batchOnline",
              this.channelFrom,
              (res) => {
                if (res.code == 200) {
                  this.getchatList();
                  this.channelOnlineVisible = false;
                  // this.$message({
                  //   message: res.msg,
                  //   type: "success",
                  // });
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "error",
                  // });
                }
              }
            );
          } else {
            this.$confirms.confirmation(
              "post",
              `是否确认${this.onLineTitle}`,
              window.path.fiveWeb + "/operator/chatbot/batchPause",
              this.channelFrom,
              (res) => {
                if (res.code == 200) {
                  this.getchatList();
                  this.channelOnlineVisible = false;
                  // this.$message({
                  //   message: res.msg,
                  //   type: "success",
                  // });
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: "error",
                  // });
                }
              }
            );
          }
        }
      });
    },
    //列表数据
    getchatList() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.fiveWeb + "/operator/chatbot/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    checkContent(row) {
      this.dialogVisible = true;
      this.detial = row;
    },
    handleClose() {
      this.dialogVisible = false;
      this.chatbotVisible = false;
      this.registerVisible = false;
      this.importVisible = false;
      this.receiptVideoVisible = false;
      this.logoImportVisible = false;
      // 重置授权书文件列表
      this.authorizationLetterList = [];
    },
    //分页
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size;
      this.getchatList();
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage;
      this.getchatList();
    },
    //查询
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm);
      this.getchatList();
    },
    //重置
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.ruleForm.username = "";
      this.ruleForm.createdTimeStart = "";
      this.ruleForm.createdTimeEnd = "";
      Object.assign(this.tabelAlllist, this.ruleForm);
      this.getchatList();
    },
    //详情
    checked(row) {
      // console.log(row,'ll');
      this.dialogVisible = true;
      this.whiteList = row.debugWhiteAddress;
      this.reason = row.auditReason;
      Object.assign(this.checkList, row);
      if (!this.checkList.bubbleColor) {
        this.checkList.bubbleColor = "#FFFFFF";
      }
      // this.provinces(row.officeCode);
      // this.citys(row.provinceCode);
    },
    //报备
    testwhiteList(row) {
      // console.log(row,'localStorage');
      localStorage.setItem("chatbotList", JSON.stringify(row));
      this.$router.push({
        path: "/chatbotChannel",
        query: { chatbotId: row.chatbotId, username: row.username },
      });
      // debugger
      // localStorage.setItem('chatbotList',JSON.stringify(row))
    },

    // handelchatName(val) {
    //   this.chatbotFrom.autoGraph = val;
    // },
    //编辑
    editBot(row) {
      this.chatbotVisible = true;
      let dataStr = JSON.stringify(row);
      this.chatbotFrom = JSON.parse(dataStr);
      // this.chatbotFrom = row
      if (row.receiptVideoTag) {
        this.chatbotFrom.receiptVideoTag = row.receiptVideoTag + "";
      } else {
        this.chatbotFrom.receiptVideoTag = "0";
      }
      if (row.receiptVideoTitle) {
        this.chatbotFrom.receiptVideoTitle = row.receiptVideoTitle;
      } else {
        this.chatbotFrom.receiptVideoTitle = "";
      }
      if (row.receiptVideoUrl) {
        this.fileListr = [
          {
            name: row.receiptVideoUrl.split("/")[
              row.receiptVideoUrl.split("/").length - 1
            ],
            url: row.receiptVideoUrl,
          },
        ];
      } else {
        this.fileListr = [];
      }
      if (row.attachment) {
        let a = row.attachment.split("/");
        this.fileName = a[a.length - 1];
        this.attachmentList[0] = {
          name: a[a.length - 1],
          url: row.attachment,
        };
      }

      // 处理电信5G授权书
      if (row.authorizationLetterUrl) {
        let authLetterPath = row.authorizationLetterUrl.split("/");
        this.authorizationLetterList[0] = {
          name: authLetterPath[authLetterPath.length - 1],
          url: row.authorizationLetterUrl,
        };
      } else {
        this.authorizationLetterList = [];
      }

      if (this.chatbotFrom.logoUrl == "") {
        this.logoFlag = true;
      } else {
        this.logoFlag = false;
      }
    },
    prevStep() {
      this.steps--;
    },
    nextStep(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.steps++;
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: "请检查输入项是否有空缺",
          });
        }
      });
    },
    saveChatbot() {
      this.$confirms.confirmation(
        "put",
        "确认修改chatbot！",
        window.path.fiveWeb + "/operator/chatbot/edit",
        this.chatbotFrom,
        (res) => {
          if (res.code == 200) {
            this.getchatList();
            this.chatbotVisible = false;
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: "error",
            // });
          }
        }
      );
    },
    quiteBot(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.actionFlag) {
            console.log(1);

            this.saveChatbot();
          } else {
            if (this.fileListr.length > 0) {
              console.log(2);

              this.$refs.upload.submit();
            } else {
              console.log(3);

              this.saveChatbot();
            }
          }
        }
      });
    },
    down(url) {
      // window.location.href = url
      window.open(`${url}?response-content-type=application%2Foctet-stream`);
      // let aTag = document.createElement('a')
      // let content = url
      // let blob = new Blob([content])
      // aTag.download = this,this.fileListc
      // aTag.href = URL.createObjectURL(blob)
      // aTag.click()

      // // 不再使用时需释放createObjectURL创建得对象
      // URL.revokeObjectURL(blob)

      // document.location()
    },
    audit(row, val, tit, formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            `确认审核${tit}该chatbot资料吗？`,
            window.path.fiveWeb + "/operator/chatbot/audit",
            {
              audit: val,
              chatbotId: row.chatbotId,
              debugWhiteAddress: this.whiteList,
              reason: this.checkList.reason,
            },
            (res) => {
              if (res.code == 200) {
                this.getchatList();
                this.dialogVisible = false;
              } else {
                this.dialogVisible = true;
              }
            }
          );
        }
      });
    },
    handleRemovec(file, fileList) {
      this.chatbotFrom.logoUrl = "";
    },
    Removec() {
      this.chatbotFrom.logoUrl = "";
      this.logoFlag = true;
    },
    // handlePictureCardPreviewc(file) {
    //   this.dialogVisible = true;
    // },
    beforeAvatarUploadc(file) {
      // console.log(file,'file');
      const siJPGGIF = file.name.split(".")[1];
      const is4kb = file.size;
      // const isLt5M = Math.round(file.size/1024*100)/100 < 4 //单位为KB;
      function objectSize(size) {
        var divisor = 1;
        var unit = "bytes";
        if (size >= 1024 * 1024) {
          divisor = 1024 * 1024;
          unit = "MB";
        } else if (size >= 1024) {
          divisor = 1024;
          unit = "KB";
        }
        if (divisor == 1) return size + " " + unit;

        return (size / divisor).toFixed(2) < 50;
      }
      let flagSize = objectSize(is4kb);
      if (siJPGGIF != "jpg" && siJPGGIF != "jpeg" && siJPGGIF != "png") {
        this.$message.warning("上传头像图片只能是 jpeg 、png格式!");
        return false;
      }
      if (!flagSize) {
        this.$message.warning("上传缩略图大小不能超过50kb！");
        return false;
      }

      // return isJPG && isLt2M;
    },
    handleSuccessc(res, fileList) {
      if (res.code == 200) {
        this.chatbotFrom.logoUrl = res.data;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove() {
      this.chatbotFrom.receiptVideoUrl = "";
      this.fileListr = [];
    },
    handleAvatarSuccess(res, fileList) {
      if (res.code == 200) {
        this.chatbotFrom.receiptVideoUrl = res.data;
        this.saveChatbot();
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleAvatarChange(file, fileList) {
      this.fileListr = fileList;
      this.actionFlag = false;
    },
    beforeAvatarUpload(file, type) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const isLt200KB = file.size / 1024 / 1024 < 1.8;
      // const fileType = ["jpg"];
      // if (fileType.indexOf(siJPGGIF) == -1) {
      //   this.$message.warning("上传文件只能是jpg格式!");
      //   return false;
      // }
      if (!isLt200KB) {
        this.$message.warning("上传文件大小不能超过 1.8MB!");
        return false;
      }
    },
    beforeAvatarUploadAtt(file, type) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      if (type != "import") {
        const isLt5M = file.size / 1024 / 1024 < 5;
        const fileType = [
          "jpg",
          "jpeg",
          "gif",
          "pdf",
          "doc",
          "docx",
          "rar",
          "zip",
        ];
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning(
            "上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!"
          );
          return false;
        }
        if (!isLt5M) {
          this.$message.warning("上传文件大小不能超过 5MB!");
          return false;
        }
      } else {
        const fileType = ["xlsx"];
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning("上传文件只能是xlsx格式!");
          return false;
        }
      }
    },
    handleSuccessAtt(res, fileList, type) {
      let name = fileList.name.substring(0, fileList.name.indexOf("."));
      if (res.code == 200) {
        if (type == "import") {
          const link = document.createElement("a");
          link.href = "data:application/octet-stream;base64," + res.data;
          // link1.download = name
          link.download = name + ".txt"; //下载后文件名
          link.click();
          this.$message({
            message: "下载成功！",
            type: "success",
          });
        } else {
          this.chatbotFrom.attachment = res.data;
        }
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemoveAtt(file, type) {
      if (type == "import") {
        this.importList.file = "";
      } else if (type == "video") {
        this.videoFrom.fileList = [];
      } else {
        this.chatbotFrom.attachment = "";
      }
    },
    // 电信5G授权书上传前验证
    beforeAvatarUploadAuthLetter(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const isLt5M = file.size / 1024 / 1024 < 5;
      const fileType = [
        "jpg",
        "jpeg",
        "gif",
        "pdf",
        "doc",
        "docx",
        "rar",
        "zip",
        "png",
      ];
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning(
          "上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip、png格式!"
        );
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传文件大小不能超过 5MB!");
        return false;
      }
    },
    // 电信5G授权书上传成功
    handleSuccessAuthLetter(res, fileList) {
      if (res.code == 200) {
        this.chatbotFrom.authorizationLetterUrl = res.data;
        this.$message({
          message: "电信5G授权书上传成功！",
          type: "success",
        });
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    // 电信5G授权书移除
    handleRemoveAuthLetter(file) {
      this.chatbotFrom.authorizationLetterUrl = "";
      this.authorizationLetterList = [];
    },
    handleVideoSuccess(res, fileList) {
      if (res.code == 200) {
        this.receiptVideoVisible = false;
        this.getchatList();
      }
    },
    handelReceiptVideo(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // alert("submit!");
          this.$refs.videoUpload.submit();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleLogoUpload(file) {
      console.log(file, 'file');
      //仅支持上传zip格式并大小限制50mb
      const isZip = file.name.split('.').pop() === 'zip';
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isZip) {
        this.$message.warning('仅支持上传zip格式!');
        return false;
      }
      if (!isLt50M) {
        this.$message.warning('上传文件大小不能超过 50MB!');
        return false;
      }
    },
    handleLogoRemove(file) {
      console.log(file, 'file');
      this.logoFrom.fileList = []
    },
    handleLogoSuccess(res, fileList) {
      console.log(res, fileList, 'res, fileList');
      if(res.code == 200) {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
      }else{
        this.$message({
          message: res.msg,
          type: 'error'
        })
      }
    },
    handleLogoError(err, file, fileList) {
      console.log(err, file, fileList)
      this.$message({
        message: '上传失败',
        type: 'error'
      })
    },
    handelLogoImport(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.logoImportVisible = false;
          this.getchatList();
        }
      });
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.whiteList = "";
      }
    },
    chatbotVisible(val) {
      if (!val) {
        this.chatbotFrom.attachment = "";
        this.attachmentList = [];
        this.chatbotFrom.receiptVideoUrl = "";
        this.chatbotFrom.receiptVideoTag = "0";
        this.fileListr = [];
        this.steps = 1;
        this.actionFlag = true;
      }
    },
    registerVisible(val) {
      if (!val) {
        this.$refs["registerList"].resetFields();
        // this.customerIdList = []
        this.registerList.chatbotIdList = [];
        this.registerList.channelName = "";
        this.registerList.operator = "";
      }
    },
    channelOnlineVisible(val) {
      if (!val) {
        this.$refs["channelFrom"].resetFields();
      }
    },
    importVisible(val) {
      if (!val) {
        this.$refs["importList"].resetFields();
        // this.customerIdList = []
        this.importList.file = [];
        this.importFileList = [];
        this.channelJson = [];
      }
    },
    receiptVideoVisible(val) {
      if (!val) {
        this.$refs["videoRef"].resetFields();
        this.videoFrom.fileList = [];
      }
    },
    logoImportVisible(val) {
      if (!val) {
        this.logoFrom.fileList = [];
        // this.logoFrom.fileList1 = [];
      }
    },
  },
};
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}

.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  /* margin-left: 30px; */
  /* border-bottom: 1px solid #eee; */
  line-height: 33px;
  display: flex;
  justify-content: space-between;
  /* margin-top: 5px; */
}

.mean-itemj {
  width: 90%;
  /* height: 30px; */
  padding: 10px 0;
  /* margin-left: 30px; */
  border-bottom: 1px solid #eee;
  /* line-height: 35px; */
  display: flex;
  justify-content: space-between;
  /* margin-top: 5px; */
}

.mean-itemjs {
  width: 90%;
  /* height: 30px; */
  padding: 10px 0;
  /* margin-left: 30px; */
  border-bottom: 1px solid #eee;
  /* line-height: 33px; */
  display: flex;
  justify-content: space-between;
}

.mean-items {
  width: 90%;
  /* height: 45px;
  padding: 10px 0; */
  /* margin-left: 30px; */
  /* border-bottom: 1px solid #eee; */
  line-height: 33px;
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  /* margin-top: 5px; */
}

.circle {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: red;
  margin: 0 15px;
}

.circle-active {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #16a598;
  margin: 0 15px;
}

.circle_a1 {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e6a23c;
  margin: 0 15px;
}

.photo_active {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 12px;
  color: red;
}

.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>
<style>
.el-textarea__inner {
  height: 100%;
}

.my-label {
  width: 180px;
}

/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>