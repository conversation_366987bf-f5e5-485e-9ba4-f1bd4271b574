<template>
  <div class="container_left">
    <el-page-header @back="goBack" content="短链任务明细"> </el-page-header>

    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="省份" prop="province">
            <el-input
              v-model="formInline.province"
              placeholder="请输入省份"
              class="input-w"
            ></el-input>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div style="margin: 10px 0">
        <el-button type="primary" @click="handlExport">导出 </el-button>
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->
          <el-table-column label="用户名">
            <template v-slot="scope">{{ scope.row.username }}</template>
          </el-table-column>
          <el-table-column label="省份">
            <template v-slot="scope">{{ scope.row.province }}</template>
          </el-table-column>
          <el-table-column label="长链">
            <template v-slot="scope">
              <a
                style="color: #409eff"
                :href="scope.row.originalUrl"
                target="_blank"
                rel="noopener noreferrer"
                >{{ scope.row.originalUrl }}</a
              >
            </template>
          </el-table-column>
          <el-table-column label="短链">
            <template v-slot="scope">
              <a
                style="color: #409eff"
                :href="scope.row.shortUrl"
                target="_blank"
                rel="noopener noreferrer"
                >{{ scope.row.shortUrl }}</a
              >
            </template>
          </el-table-column>
          <el-table-column label="点击数量">
            <template v-slot="scope">
              <span
                style="color: #409eff; cursor: pointer"
                v-if="scope.row.clickNum != 0"
                @click="handleClickNum(scope.row)"
                >{{ scope.row.clickNum }}</span
              >
              <span v-else>{{ scope.row.clickNum }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
        <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import TableTem from "../../../publicComponents/TableTem.vue";
import Tooltip from "@/components/publicComponents/tooltip.vue";
import moment from "moment";
import axios from "axios";
import getNoce from "../../../../plugins/getNoce";
export default {
  name: "shortStatistics",
  components: {
    TableTem,
    Tooltip,
  },
  data() {
    var numberRules = (rule, value, callback) => {
      if (!/^([0-9][0-9]{0,3}|100)$/.test(value)) {
        return callback(new Error("请输入0-100的数字"));
      } else {
        callback();
      }
    };
    return {
      isFirstEnter: false,
      formInline: {
        taskId: "",
        province: "",
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        taskId: "",
        province: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    };
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.omcs + "operatingshortlinkinfo/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.tableData = res.data.records;
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.gettableLIst();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.gettableLIst();
    },
    Query() {
      //查询
      this.formInline.taskId = this.$route.query.id;
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    Reload() {
      this.$refs["formInline"].resetFields();
      this.formInline.taskId = this.$route.query.id;
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    goBack() {
      this.$router.push({
        path: "/shortTask",
      });
    },
    handleClickNum(row) {
      this.$router.push({
        path: "/shorCodetStatistics",
        query: { shortCode: row.shortCode, id: this.$route.query.id },
      });
    },
    async handlExport() {
      let that = this;
      const nonce = await getNoce.useNonce();
      this.$confirm("是否确认导出短链明显数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          axios({
            method: "post",
            url: window.path.omcs + "operatingshortlinkinfo/export",
            data: this.tabelAlllist,
            headers: {
              "Content-Type": "application/json",
              Authorization:
                "Bearer " + window.common.getCookie("ZTADMIN_TOKEN"),
              Once: nonce,
            },
            responseType: "blob",
          })
            .then(function (res) {
              let timeStamp = new Date().getTime();
              const blob = new Blob([res.data], {
                type: "application/octet-stream;charset=utf-8",
              });
              let link = document.createElement("a");
              let href = window.URL.createObjectURL(blob); //下载链接
              link.href = href;
              link.text = "下载";
              link.download = timeStamp + ".xlsx"; //下载后文件名
              document.body.appendChild(link);
              link.click(); //点击下载
              document.body.removeChild(link); //下载完成移除元素
              window.URL.revokeObjectURL(href);
              that.$message({
                message: "下载成功",
                type: "success",
              });
            })
            .catch((err) => {
              console.log(err, "err");
              that.$message({
                message: "下载失败",
                type: "error",
              });
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    // edit(val) {
    //   this.titleMap = '编辑'
    //   this.dialogFormVisible = true
    //   //清除编辑默认值
    //   this.$nextTick(() => {
    //     this.$refs.formop.resetFields();
    //     Object.assign(this.formop, val);
    //     this.expireTime = val.expireTime
    //     this.formop.hyOpen += ""
    //     // console.log(this.formop,'for');
    //     this.formop.productId += ''
    //   })
    // },
    // handleTime(val) {
    //     if (val) {
    //         this.formop.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
    //         this.formop.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
    //     } else {
    //         this.formop.startTime = ''
    //         this.formop.endTime = ''
    //     }
    // },
  },
  created() {
    this.isFirstEnter = true;
    this.$nextTick(() => {
      this.tabelAlllist.taskId = this.$route.query.id;
      this.gettableLIst();
    });
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.tabelAlllist.taskId = this.$route.query.id;
        this.gettableLIst();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
};
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.borders {
  width: 100%;
  border-bottom: 1px dashed #555;
  margin: 10px 15px;
}
</style>
