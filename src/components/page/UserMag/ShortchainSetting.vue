<template>
  <div>
    <div class="OuterFrame">
      <div class="Top_title" style="display: flex; align-items: center">
        <div
          @click="goBack()"
          style="
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #16a589;
          "
        >
          <el-icon><ArrowLeftBold /></el-icon>
          <span style="display: inline-block; padding-right: 10px"> 返回</span>
        </div>
        |
        <span style="margin-left: 10px;">短链用户配置</span>
      </div>
      <div style="padding: 20px 20px; line-height: 40px">
        <span>用户名：</span
        ><span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span
        ><span style="margin-right: 20px">{{ jsonData.compName }}</span>
        <!-- <div class="styleProduct">
                    <span class="basicInfo-title">短链开启状态：</span>
                    
                    <span class="basicInfo-title" v-if="shortFrom.open==1">关闭</span>
                    <span v-else>开启</span>
                </div> -->
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="短链设置" name="shortRef">
          <el-form
            :inline="true"
            :model="shortFrom"
            class="demo-form-inline"
            label-width="180px"
            ref="shortRef"
            :rules="rules"
          >
            <el-form-item prop="productOpen">
              <template #label>
                <div style="display: flex; align-items: center">
                  <span style="color: #000; font-size: 16px"> 短链服务 </span>
                  <el-tooltip
                    effect="dark"
                    content="开启短链服务后，用户将可以在客户端使用短链转换，通过短链访问到产品或服务。关闭短链服务后，用户将无法在客户端使用短链转换。"
                    placement="top-start"
                  >
                    <el-icon style="color: #409eff; font-size: 16px"
                      ><InfoFilled
                    /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-radio-group v-model="shortFrom.productOpen">
                <el-radio :value="0">开启</el-radio>
                <el-radio :value="1">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <div>
              <el-form-item prop="open">
                <template #label>
                  <div style="display: flex; align-items: center">
                    <span style="color: #000; font-size: 16px"> 短链明细 </span>
                    <el-tooltip
                      effect="dark"
                      content="开启短链明细后，用户将可以点击访问次数查看短链详情。关闭短链明细后，用户将无法查看短链详情。"
                      placement="top-start"
                    >
                      <el-icon style="color: #409eff; font-size: 16px"
                        ><InfoFilled
                      /></el-icon>
                    </el-tooltip>
                  </div>
                </template>

                <el-radio-group v-model="shortFrom.open">
                  <el-radio :value="0">开启</el-radio>
                  <el-radio :value="1">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
            <div>
              <el-form-item prop="validTime">
                <span slot="label" style="color: #000; font-size: 16px">
                  有效期（单位/天）
                </span>
                <el-input-number
                  class="input-w inputNumber"
                  :min="undefined"
                  :max="99999999"
                  step-strictly
                  v-model="shortFrom.validTime"
                  placeholder="请输入有效期，需为正整数"
                ></el-input-number>
              </el-form-item>
              <span style="color: #909399; font-size: 14px"
                >默认有效期为30天</span
              >
            </div>
            <div style="margin-top: 20px">
              <el-form-item label=" ">
                <el-button
                  style="width: 150px"
                  type="primary"
                  @click="submitForm(shortRef)"
                  >提交</el-button
                >
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <!-- <div class="btnsubmit">
               
            </div> -->
    </div>
  </div>
</template>
<script setup>
import { reactive } from "vue";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const shortRef = ref(null);
const isFirstEnter = ref(false);
const roleId = ref("");
const jsonData = reactive({
  consumerName: "",
  compName: "",
});
const activeName = ref("shortRef");
const shortFrom = reactive({
  open: 1,
  userId: "",
  productOpen: 1,
  validTime: undefined,
  userName: "",
});
const rules = reactive({
  open: [
    {
      required: true,
      message: "请选择是否开启短链查看次数",
      trigger: "change",
    },
  ],
  productOpen: [
    { required: true, message: "请选择产品状态", trigger: "change" },
  ],
});
onMounted(() => {
  shortFrom.userId = $router.currentRoute.value.query.id;
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo) {
    roleId.value = userInfo.roleId;
  }
  getBasicData();
});
const goBack = () => {
  $router.push({
    path: "UserDetail",
    query: {
      id: $router.currentRoute.value.query.id,
    },
  });
};
const getBasicData = () => {
  try {
    window.api.get(
      window.path.omcs + "operatinguser/" + shortFrom.userId,
      {},
      (res) => {
        if (res.code === 200) {
          jsonData.consumerName = res.data.consumerName;
          jsonData.compName = res.data.compName;
          window.api.get(
            window.path.omcs +
              "operatinguser/userShortLinkInfo/" +
              $router.currentRoute.value.query.id +
              "?userName=" +
              res.data.consumerName,
            {},
            (row) => {
              if (row.code == 200) {
                shortFrom.open = row.data.open;
                shortFrom.productOpen = row.data.productOpen;
                shortFrom.validTime = row.data.validTime || undefined;
                if (row.data.productOpen !== null) {
                  shortFrom.productOpen = row.data.productOpen;
                } else {
                  shortFrom.productOpen = 1;
                }
              }
            }
          );
        }
      }
    );
  } catch (error) {
    console.error(error);
  }
};
const submitForm = (formEl) => {
  try {
    formEl.validate((valid, fields) => {
      if (valid) {
        shortFrom.userName = jsonData.consumerName;
        ElMessageBox.confirm("确认修改短链配置？", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            window.api.put(
              window.path.omcs + "operatinguser/userShortLinkInfo",
              shortFrom,
              (res) => {
                if (res.code == 200) {
                  ElMessage({
                    type: "success",
                    message: res.msg,
                  });
                  goBack();
                } else {
                  ElMessage({
                    type: "error",
                    message: res.msg,
                  });
                }
              }
            );
          })
          .catch(() => {
            ElMessage({
              type: "info",
              message: "操作取消",
            });
          });
      } else {
        console.error(fields);
      }
    });
  } catch (error) {
    console.error(error);
  }
};
</script>
<!-- <script>
// const validTimeValidator = (rule, value, callback) => {
//     if (value === 0) {
//         callback(new Error('有效期需为大于0的正整数'));
//     } else if (value) {
//         if (value < 0) {
//             callback(new Error('有效期需为大于0的正整数'));
//         }
//         if (String(value).indexOf(".") !== -1) {
//             callback(new Error('有效期需为大于0的正整数'));
//         }
//         callback()
//     } else {
//       callback(new Error('有效期不能为空'));
//     }
// }
export default {
  data() {
    return {
      isFirstEnter: false,
      jsonData: {
        consumerName: "",
        compName: "",
      }, //用户资料
      //  productRow:{},
      activeName: "shortRef",
      shortFrom: {
        open: 1,
        userId: "",
        productOpen: 1,
        validTime: undefined,
        userName: "",
      },
      rules: {
        open: [
          {
            required: true,
            message: "请选择是否开启短链查看次数",
            trigger: "change",
          },
        ],
        productOpen: [
          { required: true, message: "请选择产品状态", trigger: "change" },
        ],
        // validTime: [
        //     { required: true, message: '请输入有效期，需为正整数', trigger: 'change' },
        // ]
      },
    };
  },
  created() {
    this.isFirstEnter = true;
    this.shortFrom.userId = this.$route.query.id;
    // this.getShortInfo()
    this.getBasicData();
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.shortFrom.userId = this.$route.query.id;
        // this.getShortInfo()
        this.getBasicData();
      });
    } else {
      this.$route.meta.isBack = false;
      this.isFirstEnter = false;
    }
  },
  deactivated() {
    this.activeName = "shortRef";
  },
  methods: {
    goBack() {
      //  this.$router.push({ path: 'UserDetail',query: { id:this.$route.query.id,ids:this.$route.query.ids}})
      this.$router.push({
        path: "UserDetail",
        query: { id: this.$route.query.id },
      });
    },
    // 用户信息
    getBasicData() {
      window.api.get(
        window.path.omcs + "operatinguser/" + this.$route.query.id,
        {},
        (res) => {
          if (res.code === 200) {
            this.jsonData.consumerName = res.data.consumerName;
            this.jsonData.compName = res.data.compName;
            window.api.get(
              window.path.omcs +
                "operatinguser/userShortLinkInfo/" +
                this.$route.query.id +
                "?userName=" +
                res.data.consumerName,
              {},
              (row) => {
                if (row.code == 200) {
                  this.shortFrom.open = row.data.open;
                  this.shortFrom.productOpen = row.data.productOpen;
                  this.shortFrom.validTime = row.data.validTime || "";
                  if (row.data.productOpen !== null) {
                    this.shortFrom.productOpen = row.data.productOpen;
                  } else {
                    this.shortFrom.productOpen = 1;
                  }
                }
              }
            );
          }
        }
      );
    },
    // getShortInfo() {

    // },
    submitForm(val) {
      this.$refs[val].validate((valid, value) => {
        if (valid) {
          this.shortFrom.userName = this.jsonData.consumerName;
          this.$confirms.confirmation(
            "put",
            "确认修改短链配置？",
            window.path.omcs + "operatinguser/userShortLinkInfo",
            this.shortFrom,
            (res) => {
              if (res.code === 200) {
                this.goBack();
              }
              //返回用户管理页面
            }
          );
        }
      });
    },
  },
};
</script> -->

<style scoped>
.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
  /* box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); */
}

.btnsubmit {
  /* float: right; */
  /* margin: 5px 30px; */
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
<style lang="less">
.el-input-number__decrease,
.el-input-number__increase {
  display: none;
}

.inputNumber {
  .el-input__inner {
    padding-left: 12px;
    text-align: left;
  }
}
</style>