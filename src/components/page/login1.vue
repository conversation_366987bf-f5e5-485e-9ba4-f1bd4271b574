<template>
  <div>
    <div class="ms-login">
      <div class="ms-title">助通科技后台管理系统</div>
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="0px"
        class="ms-content"
      >
        <el-form-item prop="username">
          <el-input v-model="ruleForm.codeUsername" placeholder="用户名">
            <!-- <el-button slot="prepend" icon="el-icon-lx-people"></el-button> -->
            <template v-slot:prepend>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="codePhone">
          <el-input
            type="password"
            placeholder="密码"
            v-model="ruleForm.password"
          >
            <!-- <el-button slot="prepend" icon="el-icon-phone"></el-button> -->
            <template v-slot:prepend>
              <el-icon><PhoneFilled /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="codePhone" style="position: relative">
          <el-input
            style="width: 210px"
            placeholder="验证码"
            @keyup.enter="submitForm('ruleForm')"
            v-model="ruleForm.code"
          >
          </el-input>
          <img
            @click="getcode()"
            :src="ruleForm.imgSrc"
            alt=""
            style="height: 32px; position: absolute; right: 0;"
          />
        </el-form-item>
        <div class="login-btn">
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
            @keyup.enter="submitForm('ruleForm')"
            >登录</el-button
          >
        </div>
        <p class="login-tips">
          Tips : 用户名和密码。<span style="margin-left: 20px" @click="phone()"
            >手机号登录</span
          >
        </p>
      </el-form>
    </div>
  </div>
</template>

<script>
import { $on, $off, $once, $emit } from '../../../utils/gogocodeTransfer'
import axios from 'axios'
import { version } from '../../utils/version'
import bus from '../common/bus'
export default {
  data() {
    return {
      nmb: 120,
      ClickTrue: true,
      token: '',
      ruleForm: {
        codeUsername: '',
        codePhone: '',
        codeObtain: '',
        password: '',
        imgSrc: '',
        code: '',
        randomStr: '',
      },
      rules: {
        codeUsername: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      },
    }
  },
  methods: {
    getcode() {
      let times = new Date().getTime()
      this.ruleForm.imgSrc = window.path.api + 'code?randomStr=' + times //线上
      this.ruleForm.randomStr = times.toString()
    },
    phone() {
      $emit(bus, 'loginPh', false)
    },
    submitForm(formName) {
      // console.log(11);
      this.$refs[formName].validate((valid) => {
        var grant_type = 'password'
        var scope = 'server'
        if (valid) {
          axios({
            method: 'post',
            url: window.path.api + 'auth/oauth/token',
            headers: {
              Authorization: 'Basic cGlnOnBpZw==',
            },
            params: {
              username: this.ruleForm.codeUsername,
              code: this.ruleForm.code,
              password: this.ruleForm.password,
              randomStr: this.ruleForm.randomStr,
              grant_type: grant_type,
              scope: scope,
            },
            withCredentials: false,
          })
            .then((res) => {
              localStorage.removeItem("nonce_list");
              this.ruleForm.code = ''
              let d = new Date()
              d.setTime(d.getTime() + 100 * 60 * 240)
              let expires = 'expires=' + d.toUTCString()
              document.cookie =
                'ZTADMIN_TOKEN=' +
                res.data.access_token +
                ';path=/' +
                ';expires=' +
                expires
              axios({
                method: 'get',
                url: window.path.upms + 'user/info',
                headers: {
                  Authorization: 'Bearer ' + res.data.access_token,
                },
                withCredentials: false,
              }).then((ress) => {
                if (
                  ress.data.data.roles[0] == 'ROLE_MC' || //子用户
                  ress.data.data.roles[0] == 'ROLE_SU' || //子用户
                  ress.data.data.roles[0] == 'ROLE_EU' || //终端用户
                  ress.data.data.roles[0] == 'ROLE_EUW' //线上终端
                ) {
                  this.$message({
                    message: '用户名输入有误！',
                    type: 'warning',
                  })
                } else {
                  this.$router.push('/')
                  sessionStorage.setItem('path', '/home')
                  // localStorage.setItem("appVersion",version);
                  // let path = sessionStorage.getItem("path");
                  // if (path) {
                  //   this.$router.push(path);
                  // } else {
                  //   this.$router.push("/");
                  // }
                }
              })
            })
            .catch((err) => {
              this.getcode()
            })

          // axios({
          //     method: "post",
          //     url: window.path.upms + "code/checkIp",
          //     data: {
          //     username: this.ruleForm.codeUsername,
          //     },
          //     withCredentials: false,
          // }).then(res=>{
          //     if(res.data.code == 200){
          //         axios({
          //             method:'post',
          //             url:window.path.api + 'auth/oauth/token',
          //             headers:{
          //                 Authorization: "Basic cGlnOnBpZw==",
          //             },
          //             params:{
          //                 username:this.ruleForm.codeUsername,
          //                 code:this.ruleForm.code,
          //                 password:this.ruleForm.password,
          //                 randomStr:this.ruleForm.randomStr,
          //                 grant_type: grant_type,
          //                 scope: scope,
          //             },
          //             withCredentials: false,
          //         }).then(res=>{
          //             this.ruleForm.code = ""
          //             let d = new Date()
          //             d.setTime(d.getTime() + 100 * 60 * 240)
          //             let expires = "expires=" + d.toUTCString()
          //             document.cookie = "ZTADMIN_TOKEN=" + res.data.access_token + ";path=/" + ";expires=" + expires
          //             axios({
          //                 method : "get",
          //                 url : window.path.upms + "user/info",
          //                 headers :{
          //                     Authorization: "Bearer " + res.data.access_token,
          //                 },
          //                 withCredentials: false,
          //             }).then(ress=>{
          //                 if(ress.data.data.roles[0] == "ROLE_MC" || //子用户
          //                    ress.data.data.roles[0] == "ROLE_SU" || //子用户
          //                    ress.data.data.roles[0] == "ROLE_EU" || //终端用户
          //                    ress.data.data.roles[0] == "ROLE_EUW" //线上终端
          //                 ){
          //                     this.$message({
          //                         message: "用户名输入有误！",
          //                         type: "warning",
          //                     });
          //                 }else{
          //                     let path = sessionStorage.getItem("path");
          //                     if (path) {
          //                         this.$router.push(path);
          //                     }else{
          //                         this.$router.push('/');
          //                     }
          //                 }
          //             })
          //         })
          //         .catch((err)=>{
          //              this.getcode();
          //         })
          //     }
          // })
        }
      })
    },
  },
  created() {
    this.getcode()
  },
  emits: ['loginPh'],
}
</script>

<style scoped>
.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url(../../assets/images/login-bg.jpg);
  background-size: 100%;
}
.ms-title {
  width: 100%;
  line-height: 50px;
  text-align: center;
  font-size: 20px;
  color: #fff;
  border-bottom: 1px solid #ddd;
}
.ms-login {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 350px;
  margin: -190px 0 0 -175px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.3);
  overflow: hidden;
}
.ms-content {
  padding: 30px 30px;
}
.login-btn {
  text-align: center;
}
.login-btn button {
  width: 100%;
  height: 36px;
  margin-bottom: 10px;
}
.login-tips {
  font-size: 12px;
  line-height: 30px;
  color: #fff;
}

:deep(.el-input__wrapper) {
  padding: 1px;
}
:deep(.el-input__inner) {
  padding: 0 12px;
}
</style>
