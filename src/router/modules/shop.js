// 商城相关路由
export default [
  {
    path: '/MenuManage',
    meta: { title: '栏目管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/shop/MenuManage.vue'),
  },
  {
    path: '/MenuSetting',
    meta: { title: '栏目-关联商品', keepAlive: true, isBack: false, hideTag: true },
    component: () => import('@/components/page/shop/MenuSetting.vue'),
  },
  {
    path: '/OrderManage',
    meta: { title: '订单管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/shop/OrderManage.vue'),
  },
  {
    path: '/ShopManage',
    meta: { title: '商品管理', keepAlive: true, isBack: false },
    component: () => import('@/components/page/shop/ShopManage.vue'),
  },
  {
    path: '/ShopSetting',
    meta: { title: '栏目关联商品', keepAlive: true, isBack: false },
    component: () => import('@/components/page/shop/ShopSetting.vue'),
  },
  {
    path: '/ShopUser',
    meta: { title: '线上终端客户列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/shop/ShopUserList.vue'),
  },
  {
    path: '/ShopMarketUser',
    meta: { title: '线上终端客户列表', keepAlive: true, isBack: false },
    component: () => import('@/components/page/shop/market/ShopUserList.vue'),
  },
] 