<template>
  <div>
    <div class="OuterFrame fillet">
      <div class="Top_title" style="display: flex; align-items: center">
        <div @click="goBack()" style="
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #16a589;
          ">
          <el-icon>
            <ArrowLeftBold />
          </el-icon>
          <span style="display: inline-block; padding-right: 10px"> 返回</span>
        </div>
        |
        <span style="margin-left: 10px">短信配置</span>
      </div>
      <div style="padding: 20px 20px">
        <span>用户名：</span>
        <span style="margin-right: 20px">{{ jsonData.consumerName }}</span>
        <span>公司名：</span>
        <span>{{ jsonData.compName }}</span>
      </div>
      <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="通道设置" name="channelRef">
          <div style="display: flex;">
            <el-form style="width: 40%;" :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule"
              ref="channelRef" :label-width="screenWidth <= 768 ? '120px' : '180px'" :class="{'mobile-form': screenWidth <= 768}">
              <el-form-item label="发送签名限制" prop="smsIsSignatureReport" key="smsIsSignatureReport">
                <el-radio-group v-model="addUserStep2Form.formData.smsIsSignatureReport">
                  <el-radio value="0">自定义签名</el-radio>
                  <el-radio value="1" :style="screenWidth <= 768 ? 'margin-left: 0; display: block; margin-top: 8px;' : 'margin-left: 10px;'">报备签名</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="运营商下发" prop="passOperator" key="passOperator">
                <el-checkbox-group v-model="passOperatorData" :class="{'mobile-checkbox-group': screenWidth <= 768}">
                  <el-checkbox label="移动" value="1" />
                  <el-checkbox label="联通" value="2" />
                  <el-checkbox label="电信" value="3" />
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="区域限制" prop="excludeProvince" key="excludeProvince">
                <el-select class="input-w-2" :style="screenWidth <= 768 ? 'width: 100% !important' : 'width: 100%'" v-model="addUserStep2Form.formData.excludeProvince"
                  placeholder="请选择" multiple>
                  <el-option v-for="item in options" :key="item.provincialId" :label="item.provincial"
                    :value="item.provincial">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="行业通道组名称" prop="smsProductId" key="smsProductId">
                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.smsProductId" filterable
                  placeholder="请选择通道名称" @change="handleSmsChannelChange">
                  <el-option v-for="(item, index) in channel" :label="item.channelGroupName"
                    :value="item.channelGroupId" :key="index"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="验证码通道组名称" prop="yzmProductId" key="yzmProductId">
                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.yzmProductId" filterable
                  placeholder="请选择通道名称" @change="handleYzmChannelChange">
                  <el-option v-for="(item, index) in channel" :label="item.channelGroupName"
                    :value="item.channelGroupId" :key="index"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="营销通道组名称" prop="yxProductId" key="yxProductId">
                <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.yxProductId" filterable
                  placeholder="请选择通道名称" @change="handleYxChannelChange">
                  <el-option v-for="(item, index) in channel" :label="item.channelGroupName"
                    :value="item.channelGroupId" :key="index"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="开启IP短信" prop="smsIsMobileAttribute" key="smsIsMobileAttribute">
                <el-radio-group v-model="addUserStep2Form.formData.smsIsMobileAttribute">
                  <el-radio value="1">是</el-radio>
                  <el-radio value="2" :style="screenWidth <= 768 ? 'margin-left: 0; display: block; margin-top: 8px;' : 'margin-left: 10px;'">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
                <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(channelRef)">提交</el-button>
              </el-form-item>
            </el-form>
            <div v-if="roleId != 3" style="flex: 1; overflow-x: auto;" :style="screenWidth < 1000 ? 'margin-top: 20px;' : ''">
              <div>
                <el-descriptions v-if="smsChannelListist.length > 0" class="margin-top" 
                  title="行业通道组" 
                  :column="screenWidth < 1200 ? (screenWidth < 800 ? 1 : 2) : 3"
                  border>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        通道组名称
                      </div>
                    </template>
                    <div class="channel-item">{{ smsChannelListist[0].channelGroupName }}</div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        移动主通道
                      </div>
                    </template>
                    <div v-if="smsChannelListist[0].channelGroupYd" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in smsChannelListist[0].channelGroupYd.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        移动备用通道
                      </div>
                    </template>
                    <div v-if="smsChannelListist[0].channelGroupYdBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in smsChannelListist[0].channelGroupYdBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        联通主通道
                      </div>
                    </template>
                    <div v-if="smsChannelListist[0].channelGroupLt" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in smsChannelListist[0].channelGroupLt.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        联通备用通道
                      </div>
                    </template>
                    <div v-if="smsChannelListist[0].channelGroupLtBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in smsChannelListist[0].channelGroupLtBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        电信主通道
                      </div>
                    </template>
                    <div v-if="smsChannelListist[0].channelGroupDx" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in smsChannelListist[0].channelGroupDx.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        电信备用通道
                      </div>
                    </template>
                    <div v-if="smsChannelListist[0].channelGroupDxBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in smsChannelListist[0].channelGroupDxBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                    </span>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
                <div v-else>
                  <el-empty :image-size="50" description="行业通道组暂无数据" />
                </div>
              </div>
              <div style="margin-top: 10px;">
                <el-descriptions v-if="yzmChannelListist.length > 0" class="margin-top" 
                  title="验证码通道组" 
                  :column="screenWidth < 1200 ? (screenWidth < 800 ? 1 : 2) : 3"
                  border>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        通道组名称
                      </div>
                    </template>
                    <div class="channel-item">{{ yzmChannelListist[0].channelGroupName }}</div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        移动主通道
                      </div>
                    </template>
                    <div v-if="yzmChannelListist[0].channelGroupYd" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yzmChannelListist[0].channelGroupYd.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        移动备用通道
                      </div>
                    </template>
                    <div v-if="yzmChannelListist[0].channelGroupYdBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yzmChannelListist[0].channelGroupYdBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        联通主通道
                      </div>
                    </template>
                    <div v-if="yzmChannelListist[0].channelGroupLt" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yzmChannelListist[0].channelGroupLt.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        联通备用通道
                      </div>
                    </template>
                    <div v-if="yzmChannelListist[0].channelGroupLtBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yzmChannelListist[0].channelGroupLtBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        电信主通道
                      </div>
                    </template>
                    <div v-if="yzmChannelListist[0].channelGroupDx" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yzmChannelListist[0].channelGroupDx.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        电信备用通道
                      </div>
                    </template>
                    <div v-if="yzmChannelListist[0].channelGroupDxBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yzmChannelListist[0].channelGroupDxBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
                <div v-else>
                  <el-empty :image-size="50" description="验证码通道组暂无数据" />
                </div>
              </div>
              <div style="margin-top: 10px;">
                <el-descriptions v-if="yxChannelListist.length > 0" class="margin-top" 
                  title="营销通道组" 
                  :column="screenWidth < 1200 ? (screenWidth < 800 ? 1 : 2) : 3" 
                  border>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        通道组名称
                      </div>
                    </template>
                    <div class="channel-item">{{ yxChannelListist[0].channelGroupName }}</div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        移动主通道
                      </div>
                    </template>
                    <div v-if="yxChannelListist[0].channelGroupYd" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yxChannelListist[0].channelGroupYd.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        移动备用通道
                      </div>
                    </template>
                    <div v-if="yxChannelListist[0].channelGroupYdBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yxChannelListist[0].channelGroupYdBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        联通主通道
                      </div>
                    </template>
                    <div v-if="yxChannelListist[0].channelGroupLt" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yxChannelListist[0].channelGroupLt.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        联通备用通道
                      </div>
                    </template>
                    <div v-if="yxChannelListist[0].channelGroupLtBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yxChannelListist[0].channelGroupLtBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item v-if="screenWidth >= 1000">
                    <template #label>
                      <div class="cell-item">
                        电信主通道
                      </div>
                    </template>
                    <div v-if="yxChannelListist[0].channelGroupDx" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yxChannelListist[0].channelGroupDx.split(',')" :key="item"
                          @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template #label>
                      <div class="cell-item">
                        电信备用通道
                      </div>
                    </template>
                    <div v-if="yxChannelListist[0].channelGroupDxBk" class="channel-item">
                      <span style="color: #67C23A; margin-right: 8px; cursor: pointer; display: inline-block;"
                        v-for="item in yxChannelListist[0].channelGroupDxBk.split(',')" :key="item"
                        @click="handleChannelClick(item)">
                        {{ item }}
                      </span>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
                <div v-else>
                  <el-empty :image-size="50" description="营销通道组暂无数据" />
                </div>
              </div>
            </div>
          </div>


        </el-tab-pane>
        <el-tab-pane label="审核设置" name="auditRef">
          <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="auditRef"
            :label-width="screenWidth <= 768 ? '120px' : '180px'" :class="{'mobile-form': screenWidth <= 768}" key="audit">
            <div style="display: flex">
              <div>
                <el-form-item label="计费条数免审设置" prop="smsIsAudit">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsAudit">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="免审计费条数" prop="smsAuditNum" v-if="addUserStep2Form.formData.smsIsAudit == 1">
                  <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.smsAuditNum" :min="1"
                    :max="50000"></el-input-number>
                </el-form-item>
                <el-form-item label="免审模板类型" prop="similarType">
                  <el-radio-group v-model="addUserStep2Form.formData.similarType">
                    <el-radio value="1">用户</el-radio>
                    <el-radio value="2" style="margin-left: 10px">公司主体</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="敏感词审核" prop="smsIsBlackWords">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsBlackWords">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="敏感词是否进审核" style="width: 360px" prop="smsIsBlackWordsFail">
                  <el-radio-group :disabled="addUserStep2Form.formData.smsIsBlackWords == '1'
                    ? false
                    : true
                    " v-model="addUserStep2Form.formData.smsIsBlackWordsFail">
                    <el-radio value="0">进入审核</el-radio>
                    <el-radio value="1" style="margin-left: 10px">直接失败</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="用户上行加黑" prop="smsIsRepliesBlack">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsRepliesBlack">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="加黑关键词" prop="smsRepliesBlackWords"
                  v-if="addUserStep2Form.formData.smsIsRepliesBlack == 1">
                  <el-input type="textarea" :rows="4" show-word-limit resize="none" class="input-w-3"
                    placeholder=" 多个用逗号(,)隔开" v-model="addUserStep2Form.formData.smsRepliesBlackWords"></el-input>
                </el-form-item>
                <el-form-item label="检测用户黑号" style="width: 360px" prop="smsIsPlatformBlack">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsPlatformBlack">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="检测短信内容中号码" style="width: 360px" prop="smsIsMobileCheck">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsMobileCheck">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="0" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div style="margin-left: 20px">
                <p style="font-size: 14px; color: #909399; margin-bottom: 8px">
                  tips:允许短信发送时间段设置，无设置默认是全天发送，如需设置，请选择开始时间和结束时间，定时范围0~24点区间
                </p>
                <div style="display: flex">
                  <el-form-item label-width="140px" label="行业发送" prop="hySendStartHour" key="hySendStartHour">
                    <el-select class="input-w-2" v-model="addUserStep2Form.formData.hySendStartHour"
                      placeholder="请选择开始时间" clearable>
                      <el-option v-for="item in timeList" :key="item.hour" :label="item.name" :value="item.hour">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="10px" label="" prop="hySendEndHour" key="hySendEndHour">
                    <el-select class="input-w-2" v-model="addUserStep2Form.formData.hySendEndHour" placeholder="请选择结束时间"
                      clearable>
                      <el-option v-for="item in timeList" :key="item.hour" :label="item.name" :value="item.hour">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div style="display: flex">
                  <el-form-item label-width="140px" label="营销发送" prop="yxSendStartHour" key="yxSendStartHour">
                    <el-select class="input-w-2" v-model="addUserStep2Form.formData.yxSendStartHour"
                      placeholder="请选择开始时间" clearable>
                      <el-option v-for="item in timeList" :key="item.hour" :label="item.name" :value="item.hour">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label-width="10px" label="" prop="yxSendEndHour" key="yxSendEndHour">
                    <el-select class="input-w-2" v-model="addUserStep2Form.formData.yxSendEndHour" placeholder="请选择结束时间"
                      clearable>
                      <el-option v-for="item in timeList" :key="item.hour" :label="item.name" :value="item.hour">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label-width="140px" prop="trustedSendNum" key="trustedSendNum">
                    <template #label style="display: inline-block">
                      <div style="display: flex; align-items: center">
                        <span>行业发送量管控</span>
                        <el-tooltip placement="top">
                          <template #content>
                            <div class="tips">
                              <div>管控时间段：凌晨 00:00-08:00</div>
                            </div>
                          </template>
                          <el-icon style="color: #409eff; margin-left: 5px">
                            <InfoFilled />
                          </el-icon>
                        </el-tooltip>
                      </div>
                    </template>
                    <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.trustedSendNum" :min="0"
                      :max="99999999"></el-input-number>
                  </el-form-item>
                </div>
              </div>
            </div>
            <el-form-item>
              <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
              <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(auditRef)">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane v-if="roleId == 3 || roleId == 4" label="黑号设置" name="blackauditRef">
          <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="blackauditRef"
            :label-width="screenWidth <= 768 ? '120px' : '180px'" :class="{'mobile-form': screenWidth <= 768}" key="audit">
            <div style="display: flex">
              <div>
                <el-form-item label="检测外部黑号" style="width: 360px" prop="smsIsExternalBlack">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsExternalBlack">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="
                  addUserStep2Form.formData.smsIsExternalBlack == '1' &&
                  (roleId == 3 || roleId == 4)
                " label="外部黑号渠道" prop="smsExternalBlackSource">
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-model="smsExternalBlackSourceData">
                    <el-checkbox v-for="(item, index) in externalList" :key="index" :label="item.name"
                      :value="item.value" />
                  </el-checkbox-group>
                </el-form-item>
                <!-- 验证码 -->
                <el-form-item label="检测验证码黑号" style="width: 360px" prop="smsIsUserYzmBlack">
                  <el-radio-group v-if="roleId == 3 || roleId == 4"
                    v-model="addUserStep2Form.formData.smsIsUserYzmBlack">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                  <el-radio-group v-else v-model="addUserStep2Form.formData.smsIsUserYzmBlack" disabled>
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="黑号级别" prop="smsUserYzmBlackLevel1"
                  v-if="addUserStep2Form.formData.smsIsUserYzmBlack == 1">
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-model="addUserStep2Form.formData.smsUserYzmBlackLevel1" @change="handelYzmBlack"
                    v-if="roleId == 3 || roleId == 4">
                    <el-checkbox style="margin-left: 0" v-for="(item, index) in level" :key="index + 'a'"
                      :label="item.label" :value="item.value" />
                  </el-checkbox-group>
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-else v-model="addUserStep2Form.formData.smsUserYzmBlackLevel1" disabled>
                    <el-checkbox style="margin-left: 0" v-for="(item, index) in level" :key="index + 'a'"
                      :label="item.label" :value="item.value" />
                  </el-checkbox-group>
                </el-form-item>
                <!-- 行业 -->
                <el-form-item label="检测行业黑号" style="width: 360px" prop="smsIsUserHyBlack">
                  <el-radio-group v-if="roleId == 3 || roleId == 4"
                    v-model="addUserStep2Form.formData.smsIsUserHyBlack">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                  <el-radio-group v-else v-model="addUserStep2Form.formData.smsIsUserHyBlack" disabled>
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="黑号级别" prop="smsUserHyBlackLevel1"
                  v-if="addUserStep2Form.formData.smsIsUserHyBlack == 1">
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-model="addUserStep2Form.formData.smsUserHyBlackLevel1" @change="handelHymBlack"
                    v-if="roleId == 3 || roleId == 4">
                    <el-checkbox style="margin-left: 0" v-for="(item, index) in level" :key="index + 'a'"
                      :label="item.label" :value="item.value" />
                  </el-checkbox-group>
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-else v-model="addUserStep2Form.formData.smsUserHyBlackLevel1" disabled>
                    <el-checkbox style="margin-left: 0" v-for="(item, index) in level" :key="index + 'a'"
                      :label="item.label" :value="item.value" />
                    <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
                  </el-checkbox-group>
                </el-form-item>
                <!-- 营销 -->
                <el-form-item label="检测营销黑号" style="width: 360px" prop="smsIsUserBlack">
                  <el-radio-group v-if="roleId == 3 || roleId == 4" v-model="addUserStep2Form.formData.smsIsUserBlack">
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                  <el-radio-group v-else v-model="addUserStep2Form.formData.smsIsUserBlack" disabled>
                    <el-radio value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="黑号级别" prop="smsUserHyBlackLevel1"
                  v-if="addUserStep2Form.formData.smsIsUserBlack == 1">
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-model="addUserStep2Form.formData.smsUserBlackLevel1" v-if="roleId == 3 || roleId == 4">
                    <el-checkbox style="margin-left: 0" v-for="(item, index) in level" :key="index + 'a'"
                      :label="item.label" :value="item.value" />
                    <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
                  </el-checkbox-group>
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-else v-model="addUserStep2Form.formData.smsUserBlackLevel1" disabled>
                    <el-checkbox style="margin-left: 0" v-for="(item, index) in level" :key="index + 'a'"
                      :label="item.label" :value="item.value" />
                    <!-- <el-checkbox style="margin-left: 0" label="1">一级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="2">二级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="3">三级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="4">四级</el-checkbox>
                <el-checkbox style="margin-left: 0" label="5">五级</el-checkbox> -->
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </div>
            <el-form-item>
              <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
              <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(blackauditRef)">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="参数设置" name="parameterRef">
          <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="parameterRef"
            :label-width="screenWidth <= 768 ? '120px' : '140px'" :class="{'mobile-form': screenWidth <= 768}" key="parameter">
            <div style="display: flex">
              <div>
                <el-form-item label="扩展号" prop="ext">
                  <el-input style="width: 224px" v-model="addUserStep2Form.formData.ext"></el-input>
                </el-form-item>
                <!-- <el-form-item label="营销扩展号" prop="extYx">
                  <el-input
                    style="width: 224px"
                    v-model="addUserStep2Form.formData.extYx"
                  ></el-input>
                </el-form-item> -->
                <el-form-item label="默认签名" prop="sign">
                  <el-select style="width: 224px" v-model="addUserStep2Form.formData.sign" placeholder="请选择签名" clearable
                    @change="handelChangeSign">
                    <el-option v-for="item in signList" :label="item.signature" :value="item.signature"></el-option>
                  </el-select>
                  <!-- <div style="width: 200px; position: relative">
                    <el-input
                      class="input-w-3"
                      placeholder="签名格式为【签名】"
                      v-model="addUserStep2Form.formData.sign"
                    ></el-input>
                    <span style="position: absolute; top: 2px; left: -5px"
                      >【</span
                    >
                    <span style="position: absolute; top: 2px; right: 0px"
                      >】</span
                    >
                  </div> -->
                </el-form-item>
                <el-form-item label="签名位置" prop="signSetting">
                  <el-radio-group v-model="addUserStep2Form.formData.signSetting">
                    <el-radio style="margin-left: 10px" value="1">前置</el-radio>
                    <el-radio value="2" style="margin-left: 10px">后置</el-radio>
                    <el-radio value="3" style="margin-left: 10px">无限制</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="签名序号" prop="smsIsSignatureIdx">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsSignatureIdx">
                    <el-radio value="0" style="margin-left: 10px">带序号</el-radio>
                    <el-radio value="1" style="margin-left: 10px">不带序号</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="自定义扩展" prop="smsIsSignatureExtension">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsSignatureExtension">
                    <el-radio value="0" style="margin-left: 10px">支持</el-radio>
                    <el-radio value="1" style="margin-left: 10px">不支持</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="支持个性化设置" prop="isIndividualization">
                  <el-radio-group v-model="addUserStep2Form.formData.isIndividualization" class="input-w">
                    <el-radio style="margin-left: 10px" value="1">是</el-radio>
                    <el-radio value="2" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="追加短信内容" prop="smsIsAppendContent">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsAppendContent">
                    <el-radio value="1" style="margin-left: 10px">是</el-radio>
                    <el-radio value="0" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="addUserStep2Form.formData.smsIsAppendContent == '1'" label="短信内容"
                  prop="appendContent">
                  <el-input class="input-w-3" placeholder="短信内容"
                    v-model="addUserStep2Form.formData.appendContent"></el-input>
                </el-form-item>

                <!-- <el-form-item label="是否抹自定义扩展字段" prop="smsIsSignatureExtension">
              <el-select
                clearable
                class="input-w-2"
                v-model="addUserStep2Form.formData.smsIsSignatureExtension"
                filterable
                placeholder="是否抹自定义扩展字段"
              >
                <el-option
                  label="是"
                  value="1"
                ></el-option>
                <el-option
                  label="否"
                  value="0"
                ></el-option>
              </el-select>
            </el-form-item> -->
              </div>
              <div v-if="
                clinetroleIds == '12' ||
                clinetroleIds == '14' ||
                clinetroleIds == '22'
              ">
                <p style="
                    padding: 0 0 18px 0px;
                    font-weight: bold;
                    font-size: 14px;
                  ">
                  其他设置
                </p>
                <el-form-item v-if="clinetroleIds == '12'" label="签名共享子账号" prop="signatureShareToSubAccount">
                  <el-radio-group v-model="addUserStep2Form.formData.signatureShareToSubAccount
                    ">
                    <el-radio value="1" style="margin-left: 10px">共享</el-radio>
                    <el-radio value="0" style="margin-left: 10px">不共享</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="回执推送账户" prop="reportTarget" v-if="clinetroleIds == '12'">
                  <el-radio-group v-model="addUserStep2Form.formData.reportTarget">
                    <el-radio value="0">本账户</el-radio>
                    <el-radio value="1" style="margin-left: 10px">子账户</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="扣费账户" prop="balanceTarget" v-if="clinetroleIds == '12'">
                  <el-radio-group v-model="addUserStep2Form.formData.balanceTarget">
                    <el-radio value="0">本账户</el-radio>
                    <el-radio value="1" style="margin-left: 10px">子账户</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="加密方式" prop="encryptType" v-if="
                  clinetroleIds == '12' ||
                  clinetroleIds == '14' ||
                  clinetroleIds == '22'
                ">
                  <el-select clearable class="input-w-2" v-model="addUserStep2Form.formData.encryptType"
                    placeholder="请选择加密方式">
                    <el-option label="oracle加密" value="1"></el-option>
                    <el-option label="AES加密" value="2"></el-option>
                    <el-option label="简单AES" value="3"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="秘钥" prop="secretKey" v-if="
                  secretKeyShow && addUserStep2Form.formData.encryptType &&
                  (clinetroleIds == '12' ||
                    clinetroleIds == '14' ||
                    clinetroleIds == '22')
                ">
                  <el-input class="input-w-3" placeholder="请输入秘钥"
                    v-model="addUserStep2Form.formData.secretKey"></el-input>
                </el-form-item>
                <el-form-item label="偏移量" prop="secretIv" v-if="
                  secretIvShow &&
                  (clinetroleIds == '12' ||
                    clinetroleIds == '14' ||
                    clinetroleIds == '22')
                ">
                  <el-input class="input-w-3" placeholder="请输入偏移量"
                    v-model="addUserStep2Form.formData.secretIv"></el-input>
                </el-form-item>
              </div>
            </div>
            <el-form-item>
              <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
              <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(parameterRef)">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="计费设置" name="billingRef">
          <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="billingRef"
            :label-width="screenWidth <= 768 ? '120px' : '160px'" :class="{'mobile-form': screenWidth <= 768}" key="billing">
            <el-form-item label="计费方式" prop="smsChargeback">
              <el-radio-group v-model="addUserStep2Form.formData.smsChargeback">
                <el-radio disabled value="1">提交计费</el-radio>
                <el-radio disabled value="2" style="margin-left: 10px">成功计费</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="付费方式" prop="smsPayMethod">
              <el-radio-group v-model="addUserStep2Form.formData.smsPayMethod">
                <el-radio disabled value="1">预付费</el-radio>
                <el-radio disabled value="2" style="margin-left: 10px">后付费</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="允许超发" prop="smsIsRepliesBlack" v-if="addUserStep2Form.formData.smsPayMethod == 1">
              <el-radio-group v-model="addUserStep2Form.formData.smsIsOvershoot">
                <el-radio :disabled="roleId != '3' &&
                  roleId != '6' &&
                  roleId != '7' &&
                  roleId != '8'
                  " value="0">是</el-radio>
                <el-radio :disabled="roleId != '3' &&
                  roleId != '6' &&
                  roleId != '7' &&
                  roleId != '8'
                  " value="1" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="trustedDailyCommit">
              <template #label style="display: inline-block">
                <div style="display: flex; align-items: center">
                  <span>每日提交发送限制</span>
                  <!-- <el-tooltip placement="top">
                        <template #content>
                          <div class="tips">
                            <div>tips：每日提交发送限制只生效于后付费账号。</div>
                          </div>
                        </template>
      <el-icon style="color: #409eff; margin-left: 5px">
        <InfoFilled />
      </el-icon>
      </el-tooltip> -->
                </div>
              </template>
              <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.trustedDailyCommit" :min="0"
                :max="999999999" label="账号每日成功发送限制"></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.trustedDailyCommit"></el-input> -->
            </el-form-item>
            <el-form-item label="每日成功发送限制" prop="dailyLimited">
              <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited" :min="0"
                :max="999999999" label="账号每日成功发送限制"></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input> -->
            </el-form-item>
            <el-form-item label="每月成功发送限制" prop="monthLimited">
              <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.monthLimited" :min="0"
                :max="999999999" label="账号每日成功发送限制"></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.dailyLimited"></el-input> -->
            </el-form-item>
            <el-form-item>
              <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
              <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(billingRef)">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="状态设置" name="stateRef">
          <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="stateRef"
            :label-width="screenWidth <= 768 ? '120px' : '180px'" :class="{'mobile-form': screenWidth <= 768}" key="state">
            <div style="display: flex">
              <div>
                <el-form-item label="通道错误码" prop="smsIsChannelCode">
                  <el-radio-group v-model="addUserStep2Form.formData.smsIsChannelCode">
                    <el-radio value="1">原始错误码</el-radio>
                    <el-radio value="2" style="margin-left: 10px">错误码分类</el-radio>
              </el-radio-group>
            </el-form-item>
                <el-form-item prop="longReportType">
                  <template #label style="display: inline-block">
                    <div style="display: flex; align-items: center">
                      <span>长短信回执</span>
                      <el-tooltip placement="top">
                        <template #content>
                          <div class="tips">
                            tips：
                            <div>全部成功：一条成功为全部成功;</div>
                            <div>全部失败：一条失败为全部失败;</div>
                            <div>不处理：不处理，原始回执返回;</div>
                          </div>
                        </template>
                        <el-icon style="color: #409eff; margin-left: 5px">
                          <InfoFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </template>
                  <el-radio-group v-model="addUserStep2Form.formData.longReportType">
                    <el-radio value="2">全部成功</el-radio>
                    <el-radio value="1" style="margin-left: 10px">全部失败</el-radio>
                    <el-radio value="3" style="margin-left: 10px">不处理</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="消息报告" prop="smsReportLevel">
                  <el-radio-group v-model="addUserStep2Form.formData.smsReportLevel" class="input-w">
                    <el-radio value="1">不接收报告</el-radio>
                    <el-radio value="2" style="margin-left: 10px">批量推送</el-radio>
                    <el-radio value="3" style="margin-left: 10px">主动抓取</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="推送方式" prop="smsPushType"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"><!-- 勾选批量推送时出现 -->
                  <el-radio-group v-model="addUserStep2Form.formData.smsPushType" class="input-w">
                    <el-radio value="1">V5平台推送方式</el-radio>
                    <el-radio value="2" style="margin-left: 10px">融合平台推送方式</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="推送请求头" prop=""
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"><!-- 勾选批量推送时出现 -->
                  <el-radio-group v-model="addUserStep2Form.formData.pushHeader">
                    <el-radio value="-1">无</el-radio>
                    <el-radio style="margin-left: 10px" value="0">1个</el-radio>
                    <el-radio style="margin-left: 10px" value="1">2个</el-radio>
                    <el-radio style="margin-left: 10px" value="2">3个</el-radio>
                    <el-radio style="margin-left: 10px" value="3">4个</el-radio>
                    <el-radio style="margin-left: 10px" value="4">5个</el-radio>
                  </el-radio-group>
                </el-form-item>
                <div v-if="addUserStep2Form.formData.pushHeader != '-1'">
                  <div style="display: flex; margin-left: 70px">
                    <el-form-item label="headKey" prop="headKey" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headKey"
                        @change="handelChange"></el-input>
                    </el-form-item>
                    <el-form-item label="headValue" prop="headValue" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headValue"
                        @change="handelChangeValue"></el-input>
                    </el-form-item>
                  </div>
                  <div v-if="
                    addUserStep2Form.formData.pushHeader == '1' ||
                    addUserStep2Form.formData.pushHeader == '2' ||
                    addUserStep2Form.formData.pushHeader == '3' ||
                    addUserStep2Form.formData.pushHeader == '4'
                  " style="display: flex; margin-left: 70px">
                    <el-form-item label="headKey" prop="headKey1" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headKey1"
                        @change="handelChange"></el-input>
                    </el-form-item>
                    <el-form-item label="headValue" prop="headValue1" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headValue1"
                        @change="handelChangeValue"></el-input>
                    </el-form-item>
                  </div>
                  <div v-if="
                    addUserStep2Form.formData.pushHeader == '2' ||
                    addUserStep2Form.formData.pushHeader == '3' ||
                    addUserStep2Form.formData.pushHeader == '4'
                  " style="display: flex; margin-left: 70px">
                    <el-form-item label="headKey" prop="headKey2" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headKey2"
                        @change="handelChange"></el-input>
                    </el-form-item>
                    <el-form-item label="headValue" prop="headValue2" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headValue2"
                        @change="handelChangeValue"></el-input>
                    </el-form-item>
                  </div>
                  <div v-if="
                    addUserStep2Form.formData.pushHeader == '3' ||
                    addUserStep2Form.formData.pushHeader == '4'
                  " style="display: flex; margin-left: 70px">
                    <el-form-item label="headKey" prop="headKey3" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headKey3"
                        @change="handelChange"></el-input>
                    </el-form-item>
                    <el-form-item label="headValue" prop="headValue3" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headValue3"
                        @change="handelChangeValue"></el-input>
                    </el-form-item>
                  </div>
                  <div v-if="addUserStep2Form.formData.pushHeader == '4'" style="display: flex; margin-left: 70px">
                    <el-form-item label="headKey" prop="headKey4" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headKey4"
                        @change="handelChange"></el-input>
                    </el-form-item>
                    <el-form-item label="headValue" prop="headValue4" label-width="100px"
                      v-if="addUserStep2Form.formData.smsReportLevel == 2">
                      <el-input style="width: 150px" v-model="addUserStep2Form.formData.headValue4"
                        @change="handelChangeValue"></el-input>
                    </el-form-item>
                  </div>
                </div>

                <el-form-item v-if="addUserStep2Form.formData.smsReportLevel != 1" label="仅接收一条回执"
                  prop="isReceiptDetails">
                  <el-radio-group v-model="addUserStep2Form.formData.isReceiptDetails">
                    <el-radio value="2">是</el-radio>
                    <el-radio value="1" style="margin-left: 10px">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="签名实名推送方式" prop="pushRealNameType">
                  <el-radio-group v-model="addUserStep2Form.formData.pushRealNameType" class="input-w">
                    <el-radio value="0">不推送</el-radio>
                    <el-radio value="1" style="margin-left: 10px">合并推送</el-radio>
                    <el-radio value="2" style="margin-left: 10px">单独推送</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item v-if="addUserStep2Form.formData.pushRealNameType == 1" label="运营商" prop="mergeOperator">
                  <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-model="addUserStep2Form.formData.mergeOperator">
                    <el-checkbox style="margin-left: 0" label="移动" value="1"></el-checkbox>
                    <el-checkbox style="margin-left: 0" label="联通" value="2"></el-checkbox>
                    <el-checkbox style="margin-left: 0" label="电信" value="3"></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="回复推送数量" prop="smsRepliesPackageSize"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"><!-- 勾选批量推送时出现 -->
                  <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.smsRepliesPackageSize" :min="0"
                    :max="999999999" label="v5默认150，融合默认400"></el-input-number>
                </el-form-item>
                <el-form-item label="回复抓取数量" prop="smsRepliesPackageSize"
                  v-if="addUserStep2Form.formData.smsReportLevel == 3"><!-- 勾选批量推送时出现 -->
                  <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.smsRepliesPackageSize" :min="0"
                    :max="999999999" label="v5默认150，融合默认400"></el-input-number>
                </el-form-item>
                <el-form-item label="状态推送数量" prop="smsStrepliesPackageSize"
                  v-if="addUserStep2Form.formData.smsReportLevel == 2"><!-- 勾选批量推送时出现 -->
                  <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.smsStrepliesPackageSize"
                    :min="0" :max="999999999" label="v5默认150，融合默认400"></el-input-number>
                </el-form-item>
                <el-form-item label="状态抓取数量" prop="smsStrepliesPackageSize"
                  v-if="addUserStep2Form.formData.smsReportLevel == 3"><!-- 勾选批量推送时出现 -->
                  <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.smsStrepliesPackageSize"
                    :min="0" :max="999999999" label="v5默认150，融合默认400"></el-input-number>
                </el-form-item>
                <el-form-item label="上行地址" prop="smsRepliesUrl"><!-- 勾选批量推送时出现 -->
                  <div>
                    <span v-if="!smsRepliesShow" style="color: #303133" class="cursor-pointer" @dblclick="
                      handleDoubleClick(
                        addUserStep2Form.formData.smsRepliesUrl,
                        $event
                      )
                      ">{{ addUserStep2Form.formData.smsRepliesUrl }}</span>
                    <el-input v-if="smsRepliesShow" style="width: 200px" type="textarea"
                      v-model="addUserStep2Form.formData.smsRepliesUrl"></el-input>
                    <el-icon v-if="!smsRepliesShow" style="
                        margin-left: 10px;
                        cursor: pointer;
                        color: #409eff;
                        font-size: 18px;
                      " @click="smsRepliesShow = true">
                      <EditPen />
                    </el-icon>
                  </div>
                  <p style="width: 345px; margin-left: 5px">
                    <span style="font-size: 12px">注：获取回复短信的地址</span>(以http/https开头)
                  </p>
                </el-form-item>
                <el-form-item label="下行地址" prop="smsReportUrl"><!-- 勾选批量推送时出现 -->
                  <div>
                    <span v-if="!smsReportShow" class="cursor-pointer" @dblclick="
                      handleDoubleClick(
                        addUserStep2Form.formData.smsReportUrl,
                        $event
                      )
                      " style="color: #303133">{{ addUserStep2Form.formData.smsReportUrl }}</span>
                    <el-input v-if="smsReportShow" style="width: 200px" type="textarea"
                      v-model="addUserStep2Form.formData.smsReportUrl"></el-input>
                    <el-icon v-if="!smsReportShow" style="
                        margin-left: 10px;
                        margin-right: 5px;
                        cursor: pointer;
                        color: #409eff;
                        font-size: 18px;
                      " @click="smsReportShow = true">
                      <EditPen />
                    </el-icon>
                  </div>
                  <p style="width: 345px">
                    <span style="font-size: 12px">注：获取状态报告的地址</span>(以http/https开头)
                  </p>
                </el-form-item>
                <el-form-item label="模板审核推送地址" prop="smsTemplateUrl"><!-- 勾选批量推送时出现 -->
                  <span v-if="!smsTemplateShow" class="cursor-pointer" @dblclick="
                    handleDoubleClick(
                      addUserStep2Form.formData.smsTemplateUrl,
                      $event
                    )
                    " style="color: #303133">{{ addUserStep2Form.formData.smsTemplateUrl }}</span>
                  <el-input v-if="smsTemplateShow" style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.smsTemplateUrl"></el-input>
                  <el-icon v-if="!smsTemplateShow" style="
                      margin-left: 10px;
                      margin-right: 5px;
                      cursor: pointer;
                      color: #409eff;
                      font-size: 18px;
                    " @click="smsTemplateShow = true">
                    <EditPen />
                  </el-icon>
                </el-form-item>
                <el-form-item label="签名审核推送地址" prop="smsSignatureUrl"><!-- 勾选批量推送时出现 -->
                  <!-- <el-input style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.smsSignatureUrl"></el-input> -->
                  <span v-if="!smsSignatureShow" class="cursor-pointer" @dblclick="
                    handleDoubleClick(
                      addUserStep2Form.formData.smsSignatureUrl,
                      $event
                    )
                    " style="color: #303133">{{ addUserStep2Form.formData.smsSignatureUrl }}</span>
                  <el-input v-if="smsSignatureShow" style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.smsSignatureUrl"></el-input>
                  <el-icon v-if="!smsSignatureShow" style="
                      margin-left: 10px;
                      margin-right: 5px;
                      cursor: pointer;
                      color: #409eff;
                      font-size: 18px;
                    " @click="smsSignatureShow = true">
                    <EditPen />
                  </el-icon>
                </el-form-item>
                <el-form-item label="实名状态合并推送地址" prop="mergePushUrl"><!-- 勾选批量推送时出现 -->
                  <!-- <el-input style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.mergePushUrl"></el-input> -->
                  <span v-if="!mergePushUrlShow" class="cursor-pointer" @dblclick="
                    handleDoubleClick(
                      addUserStep2Form.formData.mergePushUrl,
                      $event
                    )
                    " style="color: #303133">{{ addUserStep2Form.formData.mergePushUrl }}</span>
                  <el-input v-if="mergePushUrlShow" style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.mergePushUrl"></el-input>
                  <el-icon v-if="!mergePushUrlShow" style="
                      margin-left: 10px;
                      margin-right: 5px;
                      cursor: pointer;
                      color: #409eff;
                      font-size: 18px;
                    " @click="mergePushUrlShow = true">
                    <EditPen />
                  </el-icon>
                </el-form-item>
                <el-form-item label="短信进审核推送地址" prop="smsAuditUrl"><!-- 勾选批量推送时出现 -->
                  <span v-if="!smsAuditShow" class="cursor-pointer" @dblclick="
                    handleDoubleClick(
                      addUserStep2Form.formData.smsAuditUrl,
                      $event
                    )
                    " style="color: #303133">{{ addUserStep2Form.formData.smsAuditUrl }}</span>
                  <el-input v-if="smsAuditShow" style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.smsAuditUrl"></el-input>
                  <el-icon v-if="!smsAuditShow" style="
                      margin-left: 10px;
                      margin-right: 5px;
                      cursor: pointer;
                      color: #409eff;
                      font-size: 18px;
                    " @click="smsAuditShow = true">
                    <EditPen />
                  </el-icon>
                </el-form-item>
                <el-form-item label="短链白名单推送地址" prop="slDomainReportUrl"><!-- 勾选批量推送时出现 -->
                  <span v-if="!slDomainReportShow" class="cursor-pointer" @dblclick="
                    handleDoubleClick(
                      addUserStep2Form.formData.slDomainReportUrl,
                      $event
                    )
                    " style="color: #303133">{{ addUserStep2Form.formData.slDomainReportUrl }}</span>
                  <el-input v-if="slDomainReportShow" style="width: 200px" placeholder="" type="textarea"
                    v-model="addUserStep2Form.formData.slDomainReportUrl"></el-input>
                  <el-icon v-if="!slDomainReportShow" style="
                      margin-left: 10px;
                      margin-right: 5px;
                      cursor: pointer;
                      color: #409eff;
                      font-size: 18px;
                    " @click="slDomainReportShow = true">
                    <EditPen />
                  </el-icon>
                </el-form-item>
              </div>
            </div>
            <el-form-item>
              <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
              <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(stateRef)">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="防轰炸设置1.0" name="bombRef">
          <el-form :model="addUserStep2Form.formData" :rules="addUserStep2Form.formRule" ref="bombRef"
            :label-width="screenWidth <= 768 ? '120px' : '180px'" :class="{'mobile-form': screenWidth <= 768}" key="bomb">
            <el-form-item label="24h号码内容去重" prop="smsIsContentSingle">
              <el-radio-group v-model="smsIsContentSingleflag" @change="change">
                <el-radio value="1">是</el-radio>
                <el-radio value="2" style="margin-left: 10px">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="验证码24h限制（次）" prop="overrunCode">
              <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.overrunCode" :min="0"
                :max="999999999" label="验证码24h限制（次）"></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.overrunCode"></el-input> -->
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item label="行业24h限制（次）" prop="overrunIndustry">
              <el-input-number class="input-w-3" v-model="addUserStep2Form.formData.overrunIndustry" :min="0"
                :max="999999999" label="行业24h限制（次）"></el-input-number>
              <!-- <el-input class="input-w-3" v-model="addUserStep2Form.formData.overrunIndustry"></el-input> -->
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item v-if="
              roleId == 3 ||
              roleId == 4 ||
              roleId == 6 ||
              roleId == 7 ||
              roleId == 8
            " label="营销24h限制（次）" prop="overrunMarket">
              <!-- <el-input :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false" class="input-w-3"
                    v-model="addUserStep2Form.formData.overrunMarket"></el-input> -->
              <el-input-number :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false
                " class="input-w-3" v-model="addUserStep2Form.formData.overrunMarket" :min="0" :max="999999999"
                label="营销24h限制（次）"></el-input-number>
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item v-if="
              roleId == 3 ||
              roleId == 4 ||
              roleId == 6 ||
              roleId == 7 ||
              roleId == 8
            " label="营销7*24h限制（次）" prop="overrunTotalMarket">
              <!-- <el-input class="input-w-3" :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false"
                    v-model="addUserStep2Form.formData.overrunTotalMarket"></el-input> -->
              <el-input-number :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false
                " class="input-w-3" v-model="addUserStep2Form.formData.overrunTotalMarket" :min="0" :max="999999999"
                label="营销7*24h限制（次）"></el-input-number>
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item v-if="
              roleId == 3 ||
              roleId == 4 ||
              roleId == 6 ||
              roleId == 7 ||
              roleId == 8
            " label="营销15*24h限制（次）" prop="overrunFifteenMarket">
              <!-- <el-input class="input-w-3" :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false"
                    v-model="addUserStep2Form.formData.overrunFifteenMarket"></el-input> -->
              <el-input-number :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false
                " class="input-w-3" v-model="addUserStep2Form.formData.overrunFifteenMarket" :min="0" :max="999999999"
                label="营销15*24h限制（次）"></el-input-number>
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item v-if="
              roleId == 3 ||
              roleId == 4 ||
              roleId == 6 ||
              roleId == 7 ||
              roleId == 8
            " label="营销30*24h限制（次）" prop="overrunThirtyMarket">
              <!-- <el-input class="input-w-3" :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false"
                    v-model="addUserStep2Form.formData.overrunThirtyMarket"></el-input> -->
              <el-input-number :disabled="roleId == 6 || roleId == 7 || roleId == 8 ? true : false
                " class="input-w-3" v-model="addUserStep2Form.formData.overrunThirtyMarket" :min="0" :max="999999999"
                label="营销30*24h限制（次）"></el-input-number>
              <span style="color: #aaa; margin-left: 5px">0代表无限制</span>
            </el-form-item>
            <el-form-item>
              <el-button :style="screenWidth <= 768 ? 'width: 100%; margin-bottom: 10px;' : 'width: 120px;'" @click="goBack()">返回</el-button>
              <el-button type="primary" :style="screenWidth <= 768 ? 'width: 100%;' : 'margin-left: 20px; width: 120px;'" @click="submitForm(bombRef)">提交</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="防轰炸设置2.0" name="bombRef2">
          <div class="Signature-search-fun" style="margin: 10px 0">
            <el-button type="primary" @click="addopt">新增防轰炸</el-button>
          </div>
          <div class="Mail-table">
            <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
              element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
              ref="multipleTable" border :stripe="true" :data="tableDataObj.tableData" style="width: 100%">
              <!-- <el-table-column type="selection" width="46"></el-table-column> -->
              <el-table-column label="账号">
                <template #default="scope">
                  <i v-if="!scope.row.id" class="iconfont icon-guan"
                    style="font-size: 18px; color: #812c7a; cursor: pointer"></i>
                  <span>{{ scope.row.username }}</span>
                </template>
              </el-table-column>
              <el-table-column label="短信类型">
                <template #default="scope">
                  <span v-if="scope.row.smsType == '1'">验证码</span>
                  <span v-else-if="scope.row.smsType == '2'">通知</span>
                  <span v-else-if="scope.row.smsType == '3'">营销推广</span>
                </template>
              </el-table-column>
              <el-table-column label="运营商">
                <template #default="scope">
                  <span v-if="scope.row.operator == '1'">移动</span>
                  <span v-else-if="scope.row.operator == '2'">联通</span>
                  <span v-else-if="scope.row.operator == '3'">电信</span>
                  <span v-else-if="scope.row.operator == '4'">全网</span>
                </template>
              </el-table-column>
              <el-table-column label="时长">
                <template #default="scope">
                  <span>{{ scope.row.unitLength }}</span>
                </template>
              </el-table-column>
              <el-table-column label="时间单位">
                <template #default="scope">
                  <span v-if="scope.row.unit == 'MINUTE'">分钟 </span>
                  <span v-else-if="scope.row.unit == 'HOUR'">小时</span>
                  <span v-else-if="scope.row.unit == 'DAY'">天</span>
                  <span v-else-if="scope.row.unit == 'WEEK'">周</span>
                  <span v-else-if="scope.row.unit == 'MONTH'">月</span>
                </template>
              </el-table-column>
              <el-table-column label="发送条数">
                <template #default="scope">
                  <span>{{ scope.row.num }}</span>
                </template>
              </el-table-column>
              <el-table-column label="创建人">
                <template #default="scope">
                  <span>{{ scope.row.createName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="创建时间">
                <template #default="scope">
                  <span>{{
                    moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button v-if="scope.row.id" type="primary" link style="margin-left: 0px"
                    @click="detailsRow(scope.$index, scope.row)">编辑</el-button>
                  <el-button v-if="scope.row.id" link style="margin-left: 10px; color: red"
                    @click="delState(scope.$index, scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="float: right; margin-top: 10px">
              <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="1" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
            </div>

            <!-- 表格和分页结束 -->
          </div>
        </el-tab-pane>
        <el-tab-pane label="黑名单规则管理" name="clientRuleRef">
          <div v-if="activeName == 'clientRuleRef'">
            <clientRule :dataObj="{ username: jsonData.consumerName, productId: '1' }"></clientRule>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog title="短信配置" v-model="dialogVisible" width="30%" :before-close="handleClose">
      <el-form :model="ruleForm" :rules="rules1" ref="ruleFormRef" label-width="70px" class="demo-ruleForm">
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="ruleForm.remark"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button v-if="!subLoding" type="primary" @click="submitSetting(ruleFormRef)">确 定</el-button>
          <el-button v-else type="primary" :loading="subLoding" disabled>稍 等</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="titleMap[dialogStatus]" v-model="dialogFormVisible" :close-on-click-modal="false"
      :before-close="handelClose" width="520px">
      <el-form label-width="110px" :model="formop" :rules="rules" ref="formopRef" :class="{'mobile-form': screenWidth <= 768}" style="padding: 0 28px 0 20px">
        <el-form-item label="账号" prop="username">
          <el-input disabled class="input-w" v-model="formop.username" placeholder="" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="短信类型" prop="smsType">
          <el-select :disabled="id == '' ? false : true" class="input-w" v-model="formop.smsType">
            <el-option label="验证码" value="1"> </el-option>
            <el-option label="通知" value="2"> </el-option>
            <el-option label="营销推广" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运营商" prop="operator">
          <el-select :disabled="id == '' ? false : true" class="input-w" v-model="formop.operator">
            <el-option label="移动" value="1"> </el-option>
            <el-option label="联通" value="2"> </el-option>
            <el-option label="电信" value="3"> </el-option>
            <el-option label="全网" value="4"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间单位" prop="unit">
          <el-select :disabled="id == '' ? false : true" class="input-w" v-model="formop.unit">
            <el-option label="分钟" value="MINUTE"> </el-option>
            <el-option label="小时" value="HOUR"> </el-option>
            <el-option label="天" value="DAY"> </el-option>
            <el-option label="周" value="WEEK"> </el-option>
            <el-option label="月" value="MONTH"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时长" prop="unitLength">
          <el-input-number style="width: 220px" v-model="formop.unitLength" :min="0" :max="999999999"
            label="时长"></el-input-number>
          <!-- <el-input style="width: 220px;" v-model="formop.unitLength" placeholder="" autocomplete="off"></el-input> -->
        </el-form-item>
        <el-form-item label="限制条数" prop="num">
          <el-input-number style="width: 220px" v-model="formop.num" :min="0" :max="999999999"
            label="限制条数"></el-input-number>
          <!-- <el-input class="input-w" v-model="formop.num" placeholder="" autocomplete="off"></el-input> -->
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForAntibombing(formopRef)">提 交</el-button>
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  <ChannelView ref="ChannelRef" :ChannelData="ChannelData"></ChannelView>
</template>

<script setup>
import industrysCode from "../../../utils/industrysCode";
import clientRule from "./components/clientRuleManagement.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import clip from "../../../utils/clipboard";
import { nextTick, ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import ChannelView from "@/components/publicComponents/ChannelView.vue";
const $router = useRouter();
const { meta } = $router.currentRoute.value;
const ChannelRef = ref(null);
const activeName = ref("channelRef");
const channelRef = ref(null);
const auditRef = ref(null);
const blackauditRef = ref(null);
const parameterRef = ref(null);
const billingRef = ref(null);
const stateRef = ref(null);
const bombRef = ref(null);
const bombRef2 = ref(null);
const clientRuleRef = ref(null);
const formopRef = ref(null);
const ruleFormRef = ref(null);
const screenWidth = ref(window.innerWidth);

// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  addUserStep2Form.formData.userId = $router.currentRoute.value.query.id;
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo) {
    roleId.value = userInfo.roleId;
  }
  handleEdit();
  getInfo();
  // getChannel();
  getProvincial();
  getBasicData();
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});

const checkBlackWords = (rule, value, callback) => {
  if (value == "") {
    return callback(new Error("加黑关键字不能为空"));
  } else {
    console.log(value.length);
    if (value.length <= 200) {
      var reg =
        /^[\u4e00-\u9fa5\a-zA-Z0-9]+(\,([\u4e00-\u9fa5\a-zA-Z0-9])+){0,200}$/gi;
      if (!reg.test(value)) {
        return callback(
          new Error("加黑关键词包括数字、汉字、字母、多个由英文逗号隔开")
        );
      } else {
        var arr = value.split(",");
        var arr2 = [];
        for (var i = 0; i < arr.length; i++) {
          if (arr2.indexOf(arr[i]) < 0) {
            arr2.push(arr[i]);
          }
        }
        if (arr2.length != arr.length) {
          return callback(new Error("不允许重复"));
        } else {
          callback();
        }
      }
    }
    return callback(new Error("加黑关键字不能多于200个字"));
  }
};
const checkSignature = (rule, value, callback) => {
  var reg = /^(\d+,?)+$/;
  if (value) {
    if (!reg.test(value)) {
      callback(new Error("扩展号由1-12位数字组成"));
    } else {
      window.api.get(
        window.path.omcs + "operatingSignature/checkClientExt",
        { ext: value, userId: addUserStep2Form.formData.userId },
        (res) => {
          if (res.data == true) {
            callback(new Error("此扩展号已存在"));
          } else {
            callback();
          }
        }
      );
    }
  } else {
    return callback(new Error("请输入扩展号"));
  }
  // if (!/^\d{1,12}$/.test(value)) {
  //   callback(new Error("扩展号由1-12位数字组成"));
  // } else {

  // }
};
const roleId = ref("");
const clinetroleIds = ref("");
const passOperatorData = ref([]);
const options = ref([]);
const channel = ref([]);
const smsRepliesShow = ref(false);
const smsReportShow = ref(false);
const smsTemplateShow = ref(false);
const smsSignatureShow = ref(false);
const mergePushUrlShow = ref(false);
const smsAuditShow = ref(false);
const slDomainReportShow = ref(false);
const dialogVisible = ref(false);
const subLoding = ref(false);
const secretKeyShow = ref(false);
const secretIvShow = ref(false);
const dialogFormVisible = ref(false);
const smsChannelListist = ref([]);
const yzmChannelListist = ref([]);
const yxChannelListist = ref([]);
const ChannelData = ref('');
const timeList = industrysCode.timeList;
const level = ref([
  {
    label: "一级",
    value: "1",
    disabled: false,
  },
  {
    label: "二级",
    value: "2",
    disabled: false,
  },
  {
    label: "三级",
    value: "3",
    disabled: false,
  },
  {
    label: "四级",
    value: "4",
    disabled: false,
  },
  {
    label: "五级",
    value: "5",
    disabled: false,
  },
]);
const externalList = ref([
  {
    name: "先锋",
    value: "XF",
  },
  // {
  //   name: "棱镜",
  //   value: "LJ",
  // },
  {
    name: "河南利加",
    value: "HNLJ",
  },
  {
    name: "棱镜实时",
    value: "LJ_NEW",
  },
  {
    name: "泰迪熊",
    value: "TDX",
  },
  {
    name: "贰伍叁",
    value: "EWS",
  },
]);
const dialogStatus = ref("");
const id = ref("");
const titleMap = reactive({
  add: "添加防轰炸设置",
  edit: "编辑防轰炸设置",
});
const smsExternalBlackSourceData = ref([]);
const smsIsContentSingleflag = ref("2");
const ruleForm = reactive({
  remark: "",
});
const rules1 = reactive({
  remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
});
const addUserStep2Form = reactive({
  //弹窗步骤2的数据和验证
  formData: {
    userId: "",
    smsPayMethod: "1",
    smsPrice: "",
    smsProductId: "",
    excludeProvince: [],
    yzmProductId: "",
    smsChargeback: "1",
    smsIsMobileAttribute: "2",
    yxProductId: "",
    // flag: '5',
    ext: "",
    // extYx: "",
    smsReportLevel: "1",
    pushRealNameType: "0",
    mergeOperator: [],
    smsRepliesUrl: "",
    smsReportUrl: "",
    smsRepliesPackageSize: 0,
    smsStrepliesPackageSize: 0,
    smsTemplateUrl: "",
    smsSignatureUrl: "",
    mergePushUrl: "",
    smsAuditUrl: "", //短信进审核推送地址
    slDomainReportUrl: "", //SL域名回执推送地址
    // 防轰炸
    overrunCode: 0,
    overrunIndustry: 0,
    overrunMarket: 0,
    overrunTotalMarket: 0,
    smsIsContentSingle: false,
    // smsSelect:'',
    businessTypes: "1",
    interfacePassword: "******", // 接口密码
    loginPassWord: "******", // 账号密码
    // ifExt: '1', // 生成扩展码：1，手动输入扩展码：2
    custom: "1", //是否显示自定义发送operatinguse
    isIndividualization: "2", //是否个性化设置
    smsIsOvershoot: "0", //预付费是否允许超发
    smsIsAudit: "1", //是否审核
    similarType: "1", //相似类型
    sign: "",
    smsIsAppendContent: "0",
    signatureShareToSubAccount: "0", //签名共享给子账户
    appendContent: "",
    smsAuditNum: 1, //审核号码数
    smsIsRepliesBlack: "2", //是否加黑
    smsRepliesBlackWords: "TD,t,T,td", //加黑关键词
    smsIsUserBlack: "1", //是否验证黑号
    smsIsExternalBlack: "2",
    smsIsBlackWordsFail: "0",
    smsIsPlatformBlack: "",
    smsIsMobileCheck: "0",
    smsUserBlackLevel1: ["1", "2", "3"], //黑号验证级别存储
    smsExternalBlackSource: [], //外部黑名单渠道
    passOperator: [], //下发运营商
    smsIsSignatureReport: "0",
    smsUserBlackLevel: "", //黑号验证级别存储
    // isNumberPortability: "0", //携号转网
    smsIsChannelCode: "1", //返回错误通道码
    isReceiptDetails: "1", //长短信回执
    encryptType: "", //加密方式
    secretKey: "", //秘钥
    secretIv: "", //偏移量
    mobileMask: "1", //掩码展示
    smsIsBlackWords: "1", //敏感词是否审核
    reportTarget: "1", //回执推送账户
    // splitDb:'1',
    balanceTarget: "1", //扣费账户
    smsPushType: "2", //推送方式
    signSetting: "1", //短信签名位置
    reportPushSetting: "0", //回执推送
    replyPushSetting: "0", //回复推送
    reportCrawlSetting: "0", //回执抓取
    replyCrawlSetting: "0", //回复抓取
    smsIsSignatureExtension: "",
    smsIsSignatureIdx: "0", //账号签名序号设置
    smsIsUserYzmBlack: "1", //验证验证码系统黑号
    smsUserYzmBlackLevel: "", //验证验证码黑号级别
    smsUserYzmBlackLevel1: ["1", "2", "3"],
    smsIsUserHyBlack: "1", //验证行业系统黑号
    smsUserHyBlackLevel: "", //验证行业黑号级别
    smsUserHyBlackLevel1: ["1", "2", "3"],
    overrunFifteenMarket: 0, //营销15*25h
    overrunThirtyMarket: 0, //营销30*25h
    longReportType: "2", //长短信回执类型
    // smsIsEmptyNumber: "2",//是否允许空号
    // smsEmptyNumberSource: "",
    smsPushHeader: [
      {
        headKey: "",
        headValue: "",
      },
    ],
    pushHeader: "0",
    headKey: "",
    headValue: "",
    headKey1: "",
    headValue1: "",
    headKey2: "",
    headValue2: "",
    headKey3: "",
    headValue3: "",
    headKey4: "",
    headValue4: "",
    dailyLimited: 0, //每日限量
    monthLimited: 0, //每月限量
    trustedDailyCommit: 0, //每日提交数量管控
    hySendStartHour: "", //行业发送开始时间
    hySendEndHour: "", //行业发送结束时间
    yzmSendStartHour: "", //验证码发送开始时间
    yzmSendEndHour: "", //验证码发送结束时间
    trustedSendNum: undefined, //凌晨发送数量管控
    // pushHeader:{
    //   headKey:"",
    //   headValue:""
    // },
    // pushHeader2:{
    //   headKey:"",
    //   headValue:""
    // },
    // pushHeader3:{
    //   headKey:"",
    //   headValue:""
    // },
    // pushHeader4:{
    //   headKey:"",
    //   headValue:""
    // },
    // pushHeader5:{
    //   headKey:"",
    //   headValue:""
    // },
    // splitRatio:'',
    // provinceRatio:'',
    // remark:'', //备注
  },
  formRule: {
    // sign: [{ required: false, validator: sign, trigger: "change" }],
    sign: [
      { required: false, message: "该输入项为必填项!", trigger: "blur" },
      {
        min: 2,
        max: 16,
        message: "长度在 2 到 16 个字符",
        trigger: ["blur", "change"],
      },
      // {
      //   pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
      //   message: "不能使用空格和特殊符号" - + = * & % # @ ~"等",
      // },
    ],
    smsPayMethod: [
      { required: true, message: "请选择付费方式", trigger: "change" },
    ],
    longReportType: [
      {
        required: true,
        message: "请选择长短信回执类型",
        trigger: "change",
      },
    ],
    similarType: [
      {
        required: true,
        message: "请选择自动审核相似度模版类型",
        trigger: "change",
      },
    ],
    smsPrice: [
      { required: false, message: "请输入用户单价", trigger: "change" },
      {
        pattern:
          /^(([1-9][0-9]{0,1})|(([0]\.\d{1,5}|[1-9][0-9]{0,1}\.\d{1,5})))$/gi,
        message: "用户单价为2位整数或5位以内小数",
      },
    ],
    smsIsMobileAttribute: [
      { required: true, message: "请选择机型", trigger: "change" },
    ],
    // excludeProvince: [
    //   { required: true, message: "请选择省份", trigger: "blur" },
    // ],
    smsIsSignatureReport: [
      { required: true, message: "发送签名限制必填", trigger: "blur" },
    ],
    smsPushType: [
      { required: true, message: "推送方式必填！", trigger: "blur" },
    ],
    appendContent: [
      { required: true, message: "短信内容不能为空", trigger: "blur" },
    ],

    // headKey: [
    //   { required: true, message: "headKey不能为空！", trigger: "blur" },
    // ],
    // headValue: [
    //   { required: true, message: "headValue不能为空！", trigger: "blur" },
    // ],
    reportTarget: [
      { required: true, message: "回执推送账户必填！", trigger: "blur" },
    ],
    balanceTarget: [
      { required: true, message: "扣费账户必填！", trigger: "blur" },
    ],
    smsIsBlackWords: [
      {
        required: true,
        message: "请选择敏感词是否审核",
        trigger: "blur",
      },
    ],
    smsIsRepliesBlack: [
      { required: true, message: "请选择是否加黑", trigger: "blur" },
    ],
    smsIsUserBlack: [
      {
        required: true,
        message: "请选择是否验证营销系统黑号",
        trigger: "change",
      },
    ],
    smsIsBlackWordsFail: [
      {
        required: true,
        message: "请选择敏感词是否进审核",
        trigger: "change",
      },
    ],
    smsIsUserYzmBlack: [
      {
        required: true,
        message: "请选择验证码系统黑号是否验证",
        trigger: "change",
      },
    ],
    smsIsUserHyBlack: [
      {
        required: true,
        message: "请选择是否验证行业系统黑号",
        trigger: "change",
      },
    ],
    smsUserBlackLevel1: [
      {
        required: true,
        message: "请选择营销黑号验证级别",
        trigger: "change",
      },
    ],
    smsUserYzmBlackLevel1: [
      {
        required: true,
        message: "请选择验证码黑号验证级别",
        trigger: "change",
      },
    ],
    smsUserHyBlackLevel1: [
      {
        required: true,
        message: "请选择行业黑号验证级别",
        trigger: "change",
      },
    ],
    smsIsExternalBlack: [
      {
        required: true,
        message: "请选择是否验证外部黑名单",
        trigger: "change",
      },
    ],
    smsIsPlatformBlack: [
      {
        required: true,
        message: "请选择是否验证用户黑号",
        trigger: "change",
      },
    ],
    smsIsMobileCheck: [
      {
        required: true,
        message: "请选择是否检测短信内容中号码",
        trigger: "change",
      },
    ],
    isIndividualization: [
      {
        required: true,
        message: "请选择是否个性化设置",
        trigger: "blur",
      },
    ],
    ext: [{ required: true, validator: checkSignature, trigger: ["blur"] }],
    // extYx: [
    //   {
    //     required: true,
    //     validator: checkSignature,
    //     trigger: ["blur"],
    //   },
    // ],
    smsRepliesUrl: [
      {
        required: false,
        message: "请输入上行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    smsReportUrl: [
      {
        required: false,
        message: "请输入下行地址",
        trigger: ["change", "blur"],
      },
      {
        min: 1,
        max: 255,
        message: "长度在 255 个字符以内",
        trigger: ["blur", "change"],
      },
    ],
    smsProductId: [
      { required: true, message: "请选择通道", trigger: "change" },
    ],
    yzmProductId: [
      { required: true, message: "请选择通道", trigger: "change" },
    ],
    yxProductId: [{ required: true, message: "请选择通道", trigger: "change" }],
    smsChargeback: [
      { required: true, message: "请选择计费方式", trigger: "change" },
    ],
    // smsSelect:[
    //     {required:true,message:'请选择通道名称',trigger:'change'}
    // ],
    smsRepliesBlackWords: [
      { required: true, validator: checkBlackWords, trigger: "change" },
    ],
    smsIsAudit: [
      { required: true, message: "请选择是否审核", trigger: "blur" },
    ],
    // isNumberPortability: [
    //   { required: true, message: "请选择是否携号转网", trigger: "blur" },
    // ],
    smsIsChannelCode: [
      {
        required: true,
        message: "请选择是否返回通道错误码",
        trigger: "blur",
      },
    ],
    isReceiptDetails: [
      {
        required: true,
        message: "请选择是否接收长短信回执",
        trigger: "blur",
      },
    ],
    secretKey: [{ required: true, message: "请输入秘钥", trigger: "blur" }],
    secretIv: [{ required: true, message: "请输入偏移量", trigger: "blur" }],
    mobileMask: [
      { required: true, message: "请选择是否掩码展示", trigger: "blur" },
    ],
    smsAuditNum: [
      { required: true, message: "请输入审核条数", trigger: "change" },
    ],
    signSetting: [
      { required: true, message: "请选择签名位置", trigger: "blur" },
    ],
    reportPushSetting: [
      {
        required: true,
        message: "请选择回执推送方式",
        trigger: "change",
      },
    ],
    replyPushSetting: [
      {
        required: true,
        message: "请选择回复推送方式",
        trigger: "change",
      },
    ],
    reportCrawlSetting: [
      {
        required: true,
        message: "请选择回执抓取方式",
        trigger: "change",
      },
    ],
    replyCrawlSetting: [
      {
        required: true,
        message: "请选择回复抓取方式",
        trigger: "change",
      },
    ],
    smsIsSignatureIdx: [
      {
        required: true,
        message: "请选择签名扩展设置",
        trigger: "change",
      },
    ],
    signatureShareToSubAccount: [
      {
        required: true,
        message: "请选择是否共享签名给子账户",
        trigger: "change",
      },
    ],
    pushRealNameType: [
      {
        required: true,
        message: "请选择是否推送实名认证状态",
        trigger: "change",
      },
    ],
    mergeOperator: [
      {
        required: true,
        message: "请选择运营商",
        trigger: "change",
      },
    ],
    // smsIsEmptyNumber: [
    //   {
    //     required: true,
    //     message: "请选择是否检测空号",
    //     trigger: "change",
    //   }
    // ],
    // smsEmptyNumberSource: [
    //   {
    //     required: true,
    //     message: "请选择检测空号渠道",
    //     trigger: "change",
    //   }
    // ],
    // splitRatio:[
    //     {required:false,validator:splitRatio,trigger:'change'},
    // ],
    // provinceRatio:[
    //     {required:false,trigger:['change','blur']},
    //     { min: 1, max: 200, message: '长度在 200 个字符以内', trigger: ['blur','change'] },
    // ],
    // remark:[
    //     {required:false,trigger:['change','blur']},
    //     { min: 1, max: 70, message: '长度在 70 个字符以内', trigger: ['blur','change'] },
    // ],
  },
});
const tabelAlllist = reactive({
  //存储查询数据
  username: "",
  // ip: "",
  currentPage: 1,
  pageSize: 10,
});
const formop = reactive({
  username: "", //账号
  operator: "", //运营商
  smsType: "", //短信类型1:验证码；2：通知；3；营销推广
  num: 0, //发送量
  unit: "", //颗粒度 小时、天、周、月
  unitLength: 1, //时长
});
const rules = reactive({
  username: [
    {
      required: true,
      message: "账号不能为空",
      trigger: "change",
    },
  ],
  operator: [
    {
      required: true,
      message: "请选择运营商",
      trigger: "change",
    },
  ],
  smsType: [
    {
      required: true,
      message: "请选择短信类型",
      trigger: "change",
    },
  ],
  num: [
    {
      required: true,
      message: "限制条数不能为空",
      trigger: "change",
    },
  ],
  unit: [
    {
      required: true,
      message: "请选择时间单位",
      trigger: "change",
    },
  ],
  unitLength: [
    {
      required: true,
      message: "时长不能为空",
      trigger: "change",
    },
  ],
});
const jsonData = reactive({
  consumerName: "", //账号
  compName: "", //公司名称
});
const handleSmsChannelChange = (val) => {
  smsChannelListist.value = channel.value.filter(item => item.channelGroupId == val);
}
const handleYzmChannelChange = (val) => {
  yzmChannelListist.value = channel.value.filter(item => item.channelGroupId == val);
}
const handleYxChannelChange = (val) => {
  yxChannelListist.value = channel.value.filter(item => item.channelGroupId == val);
}
const handleChannelClick = (val) => {
  ChannelData.value = val;
  ChannelRef.value.ChannelClick()
}
const getInfo = () => {
  window.api.get(
    window.path.omcs +
    "operatinguser/userInfo/" +
    $router.currentRoute.value.query.id,
    {},
    (res) => {
      if (res.code == 200) {
        clinetroleIds.value = res.data.roleId;
      }
    }
  );
};
const getChannel = (data) => {
  window.api.get(
    window.path.omcs + "v3/operatingchannelgroup/getAllSMSGroup",
    {},
    (res) => {
      if (res.code == 200) {
        channel.value = res.data;
        smsChannelListist.value = res.data.filter(item => item.channelGroupId == data.smsProductId);
        yzmChannelListist.value = res.data.filter(item => item.channelGroupId == data.yzmProductId);
        yxChannelListist.value = res.data.filter(item => item.channelGroupId == data.yxProductId);
      }
    }
  );
};
const getProvincial = () => {
  window.api.get(
    window.path.omcs + "operatingchannelprovincial/list",
    {},
    (res) => {
      if (res.code == 200) {
        options.value = res.data;
      }
    }
  );
};
const signList = ref([]);
const getSignList = (consumerName) => {
  window.api.get(
    window.path.omcs + "operatingsignature/list/" + consumerName,
    {},
    (res) => {
      if (res.code == 200) {
        signList.value = res.data;
      }
    }
  );
};
const handelChangeSign = (val) => {
  if (!val) {
    addUserStep2Form.formData.sign = ''
  }
}
const getBasicData = () => {
  window.api.get(
    window.path.omcs + "operatinguser/" + addUserStep2Form.formData.userId,
    {},
    (res) => {
      jsonData.consumerName = res.data.consumerName;
      jsonData.compName = res.data.compName;
      tabelAlllist.username = res.data.consumerName;
      formop.username = res.data.consumerName;
      formop.operator = res.data.operator;
      gettableLIst();
      getSignList(res.data.consumerName);
    }
  );
};
const tableDataObj = reactive({
  loading2: false,
  tableData: [],
  total: 0,
});
const gettableLIst = () => {
  tableDataObj.loading2 = true;
  window.api.post(
    window.path.omcs + "consumerclientoverrun/page",
    tabelAlllist,
    (res) => {
      if (res.code == 200) {
        tableDataObj.total = res.data.total;
        tableDataObj.tableData = res.data.records;
        tableDataObj.loading2 = false;
      }
    }
  );
};
const handleEdit = () => {
  try {
    window.api.get(
      window.path.omcs +
      "operatinguser/userSmsInfo/" +
      $router.currentRoute.value.query.id,
      {},
      (res) => {
        if (res.code == 200) {
          getChannel(res.data);
          for (let i in res.data) {
            if (!(res.data[i] == null)) {
              res.data[i] += "";
            }
          }
          res.data["smsUserBlackLevel1"] =
            res.data.smsUserBlackLevel.split(",");
          res.data["smsUserYzmBlackLevel1"] =
            res.data.smsUserYzmBlackLevel.split(",");
          res.data["smsUserHyBlackLevel1"] =
            res.data.smsUserHyBlackLevel.split(",");
          let obj = {};
          Object.assign(obj, res.data);
          obj.pushHeader = "0";
          obj.headKey = "";
          obj.headValue = "";
          obj.headKey1 = "";
          obj.headValue1 = "";
          obj.headKey2 = "";
          obj.headValue2 = "";
          obj.headKey3 = "";
          obj.headValue3 = "";
          obj.headKey4 = "";
          obj.headValue4 = "";
          addUserStep2Form.formData = obj;

          for (let i in addUserStep2Form.formData) {
            if (
              i == "overrunCode" ||
              i == "overrunFifteenMarket" ||
              i == "overrunIndustry" ||
              i == "overrunMarket" ||
              i == "overrunThirtyMarket" ||
              i == "overrunTotalMarket" ||
              i == "smsAuditNum" ||
              i == "dailyLimited" ||
              i == "monthLimited" ||
              i == "trustedDailyCommit" ||
              i == "smsProductId" ||
              i == "yzmProductId" ||
              i == "yxProductId" ||
              i == "trustedSendNum" ||
              i == "smsUserBlackLevel" ||
              i == "smsRepliesPackageSize" ||
              i == "smsStrepliesPackageSize"
            ) {
              if (addUserStep2Form.formData[i] != null) {
                addUserStep2Form.formData[i] = parseInt(res.data[i]);
              }
            }
          }
          if (obj.smsPushHeader) {
            addUserStep2Form.formData.smsPushHeader = JSON.parse(
              res.data.smsPushHeader
            );
            // addUserStep2Form.formData.smsPushHeader = res.data.smsPushHeader;
            if (addUserStep2Form.formData.smsPushHeader.length == 1) {
              addUserStep2Form.formData.pushHeader = "0";
              addUserStep2Form.formData.headKey =
                addUserStep2Form.formData.smsPushHeader[0].headKey;
              addUserStep2Form.formData.headValue =
                addUserStep2Form.formData.smsPushHeader[0].headValue;
            } else if (addUserStep2Form.formData.smsPushHeader.length == 2) {
              addUserStep2Form.formData.pushHeader = "1";
              addUserStep2Form.formData.headKey =
                addUserStep2Form.formData.smsPushHeader[0].headKey;
              addUserStep2Form.formData.headValue =
                addUserStep2Form.formData.smsPushHeader[0].headValue;
              addUserStep2Form.formData.headKey1 =
                addUserStep2Form.formData.smsPushHeader[1].headKey;
              addUserStep2Form.formData.headValue1 =
                addUserStep2Form.formData.smsPushHeader[1].headValue;
            } else if (addUserStep2Form.formData.smsPushHeader.length == 3) {
              addUserStep2Form.formData.pushHeader = "2";
              addUserStep2Form.formData.headKey =
                addUserStep2Form.formData.smsPushHeader[0].headKey;
              addUserStep2Form.formData.headValue =
                addUserStep2Form.formData.smsPushHeader[0].headValue;
              addUserStep2Form.formData.headKey1 =
                addUserStep2Form.formData.smsPushHeader[1].headKey;
              addUserStep2Form.formData.headValue1 =
                addUserStep2Form.formData.smsPushHeader[1].headValue;
              addUserStep2Form.formData.headKey2 =
                addUserStep2Form.formData.smsPushHeader[2].headKey;
              addUserStep2Form.formData.headValue2 =
                addUserStep2Form.formData.smsPushHeader[2].headValue;
            } else if (addUserStep2Form.formData.smsPushHeader.length == 4) {
              addUserStep2Form.formData.pushHeader = "3";
              addUserStep2Form.formData.headKey =
                addUserStep2Form.formData.smsPushHeader[0].headKey;
              addUserStep2Form.formData.headValue =
                addUserStep2Form.formData.smsPushHeader[0].headValue;
              addUserStep2Form.formData.headKey1 =
                addUserStep2Form.formData.smsPushHeader[1].headKey;
              addUserStep2Form.formData.headValue1 =
                addUserStep2Form.formData.smsPushHeader[1].headValue;
              addUserStep2Form.formData.headKey2 =
                addUserStep2Form.formData.smsPushHeader[2].headKey;
              addUserStep2Form.formData.headValue2 =
                addUserStep2Form.formData.smsPushHeader[2].headValue;
              addUserStep2Form.formData.headKey3 =
                addUserStep2Form.formData.smsPushHeader[3].headKey;
              addUserStep2Form.formData.headValue3 =
                addUserStep2Form.formData.smsPushHeader[3].headValue;
            } else if (addUserStep2Form.formData.smsPushHeader.length == 5) {
              addUserStep2Form.formData.pushHeader = "4";
              addUserStep2Form.formData.headKey =
                addUserStep2Form.formData.smsPushHeader[0].headKey;
              addUserStep2Form.formData.headValue =
                addUserStep2Form.formData.smsPushHeader[0].headValue;
              addUserStep2Form.formData.headKey1 =
                addUserStep2Form.formData.smsPushHeader[1].headKey;
              addUserStep2Form.formData.headValue1 =
                addUserStep2Form.formData.smsPushHeader[1].headValue;
              addUserStep2Form.formData.headKey2 =
                addUserStep2Form.formData.smsPushHeader[2].headKey;
              addUserStep2Form.formData.headValue2 =
                addUserStep2Form.formData.smsPushHeader[2].headValue;
              addUserStep2Form.formData.headKey3 =
                addUserStep2Form.formData.smsPushHeader[3].headKey;
              addUserStep2Form.formData.headValue3 =
                addUserStep2Form.formData.smsPushHeader[3].headValue;
              addUserStep2Form.formData.headKey4 =
                addUserStep2Form.formData.smsPushHeader[4].headKey;
              addUserStep2Form.formData.headValue4 =
                addUserStep2Form.formData.smsPushHeader[4].headValue;
            } else if (addUserStep2Form.formData.smsPushHeader.length == 0) {
              addUserStep2Form.formData.pushHeader = "-1";
              addUserStep2Form.formData.headKey = "";
              addUserStep2Form.formData.headValue = "";
              addUserStep2Form.formData.headKey1 = "";
              addUserStep2Form.formData.headValue1 = "";
              addUserStep2Form.formData.headKey2 = "";
              addUserStep2Form.formData.headValue2 = "";
              addUserStep2Form.formData.headKey3 = "";
              addUserStep2Form.formData.headValue3 = "";
              addUserStep2Form.formData.headKey4 = "";
              addUserStep2Form.formData.headValue4 = "";
            }
          } else {
            addUserStep2Form.formData.pushHeader = "0";
            addUserStep2Form.formData.smsPushHeader = [
              {
                headKey: "",
                headValue: "",
              },
            ];
            addUserStep2Form.formData.headKey =
              addUserStep2Form.formData.smsPushHeader[0].headKey;
            addUserStep2Form.formData.headValue =
              addUserStep2Form.formData.smsPushHeader[0].headValue;
          }
          if (res.data.excludeProvince) {
            nextTick(() => {
              addUserStep2Form.formData.excludeProvince =
                res.data.excludeProvince.split(",");
            });
          } else {
            nextTick(() => {
              addUserStep2Form.formData.excludeProvince = [];
            });
          }
          if (res.data.sign) {
            addUserStep2Form.formData.sign = res.data.sign;
          } else {
            addUserStep2Form.formData.sign = "";
          }
          if (res.data.smsIsExternalBlack) {
            addUserStep2Form.formData.smsIsExternalBlack =
              res.data.smsIsExternalBlack;
          } else {
            addUserStep2Form.formData.smsIsExternalBlack = "2";
          }
          if (res.data.smsIsContentSingle == "false") {
            smsIsContentSingleflag.value = "2";
          } else {
            smsIsContentSingleflag.value = "1";
          }
          if (res.data.smsRepliesBlackWords == null) {
            addUserStep2Form.formData.smsRepliesBlackWords = "TD,t,T,td";
          }
          if (res.data.isReceiptDetails == null) {
            addUserStep2Form.formData.isReceiptDetails = "1";
          }
          if (res.data.smsIsSignatureIdx) {
            addUserStep2Form.formData.smsIsSignatureIdx =
              res.data.smsIsSignatureIdx;
          } else {
            addUserStep2Form.formData.smsIsSignatureIdx = "0";
          }
          if (res.data.smsIsAppendContent) {
            addUserStep2Form.formData.smsIsAppendContent =
              res.data.smsIsAppendContent;
          } else {
            addUserStep2Form.formData.smsIsAppendContent = "0";
          }
          if (res.data.smsIsMobileAttribute) {
            addUserStep2Form.formData.smsIsMobileAttribute =
              res.data.smsIsMobileAttribute;
          } else {
            addUserStep2Form.formData.smsIsMobileAttribute = "2";
          }
          if (res.data.smsIsChannelCode) {
            addUserStep2Form.formData.smsIsChannelCode =
              res.data.smsIsChannelCode;
          } else {
            addUserStep2Form.formData.smsIsChannelCode = "1";
          }
          if (res.data.smsIsBlackWordsFail) {
            addUserStep2Form.formData.smsIsBlackWordsFail =
              res.data.smsIsBlackWordsFail;
          } else {
            addUserStep2Form.formData.smsIsBlackWordsFail = "0";
          }
          if (res.data.signatureShareToSubAccount) {
            addUserStep2Form.formData.signatureShareToSubAccount =
              res.data.signatureShareToSubAccount;
          } else {
            addUserStep2Form.formData.signatureShareToSubAccount = "0";
          }
          if (res.data.smsExternalBlackSource) {
            smsExternalBlackSourceData.value =
              res.data.smsExternalBlackSource.split(",");
            let list = ["XF", "HNLJ", "LJ_NEW", "TDX", "EWS"];
            let sortList = smsExternalBlackSourceData.value.map((item) => {
              return item;
            });
            let smsExternalBlackSources = sortList.concat(
              list.filter(function (v) {
                return !(sortList.indexOf(v) > -1);
              })
            );
            externalList.value = smsExternalBlackSources.map((item) => {
              return {
                name:
                  item == "XF"
                    ? "先锋"
                    : // : item == "LJ"
                    item == "HNLJ"
                      ? "河南利加"
                      : item == "LJ_NEW"
                        ? "棱镜实时"
                        : item == "TDX"
                          ? "泰迪熊"
                          : item == "EWS"
                            ? "贰伍叁"
                            : "",
                value: item,
              };
            });
          } else {
            smsExternalBlackSourceData.value = [];
            externalList.value = [
              {
                name: "先锋",
                value: "XF",
              },
              // {
              //   name: "棱镜",
              //   value: "LJ",
              // },
              {
                name: "河南利加",
                value: "HNLJ",
              },
              {
                name: "棱镜实时",
                value: "LJ_NEW",
              },
              {
                name: "泰迪熊",
                value: "TDX",
              },
              {
                name: "贰伍叁",
                value: "EWS",
              },
            ];
          }
          if (res.data.passOperator) {
            passOperatorData.value = res.data.passOperator.split(",");
          } else {
            passOperatorData.value = [];
          }
          if (res.data.sign) {
            addUserStep2Form.formData.sign = res.data.sign
            // .replace(/【/g, "")
            // .replace(/】/g, "");
          } else {
            addUserStep2Form.formData.sign = "";
          }
          if (res.data.hySendStartHour) {
            addUserStep2Form.formData.hySendStartHour =
              res.data.hySendStartHour;
          } else {
            addUserStep2Form.formData.hySendStartHour = "";
          }
          if (res.data.hySendEndHour) {
            addUserStep2Form.formData.hySendEndHour = res.data.hySendEndHour;
          } else {
            addUserStep2Form.formData.hySendEndHour = "";
          }
          if (res.data.yxSendStartHour) {
            addUserStep2Form.formData.yxSendStartHour =
              res.data.yxSendStartHour;
          } else {
            addUserStep2Form.formData.yxSendStartHour = "";
          }
          if (res.data.yxSendEndHour) {
            addUserStep2Form.formData.yxSendEndHour = res.data.yxSendEndHour;
          } else {
            addUserStep2Form.formData.yxSendEndHour = "";
          }
          if (res.data.longReportType) {
            addUserStep2Form.formData.longReportType = res.data.longReportType;
          } else {
            addUserStep2Form.formData.longReportType = "2";
          }
          if (res.data.smsRepliesUrl) {
            smsRepliesShow.value = false;
          } else {
            smsRepliesShow.value = true;
          }
          if (res.data.smsReportUrl) {
            smsReportShow.value = false;
          } else {
            smsReportShow.value = true;
          }
          if (res.data.smsTemplateUrl) {
            smsTemplateShow.value = false;
          } else {
            smsTemplateShow.value = true;
          }
          if (res.data.smsSignatureUrl) {
            smsSignatureShow.value = false;
          } else {
            smsSignatureShow.value = true;
          }
          if (res.data.smsAuditUrl) {
            smsAuditShow.value = false;
          } else {
            smsAuditShow.value = true;
          }
          if (res.data.slDomainReportUrl) {
            slDomainReportShow.value = false;
          } else {
            slDomainReportShow.value = true;
          }
          if (res.data.mergePushUrl) {
            mergePushUrlShow.value = false;
          } else {
            mergePushUrlShow.value = true;
          }
          if (res.data.pushRealNameType) {
            addUserStep2Form.formData.pushRealNameType =
              res.data.pushRealNameType + "";
          } else {
            addUserStep2Form.formData.pushRealNameType = "0";
          }
          if (res.data.trustedSendNum) {
            addUserStep2Form.formData.trustedSendNum =
              res.data.trustedSendNum * 1;
          } else {
            addUserStep2Form.formData.trustedSendNum = undefined;
          }
          if (res.data.mergeOperator) {
            addUserStep2Form.formData.mergeOperator =
              res.data.mergeOperator.split(",");
          } else {
            addUserStep2Form.formData.mergeOperator = [];
          }
          if (res.data.smsIsMobileCheck) {
            addUserStep2Form.formData.smsIsMobileCheck =
              res.data.smsIsMobileCheck;
          } else {
            addUserStep2Form.formData.smsIsMobileCheck = "0";
          }
        }
      }
    );
  } catch (error) { }
};
const goBack = () => {
  $router.push({
    path: "UserDetail",
    query: {
      id: $router.currentRoute.value.query.id,
    },
  });
};
const handleClick = (tab, event) => { };
const handelYzmBlack = () => { };
const handelHymBlack = () => { };
const handelChange = (value) => { };
const handelChangeValue = (value) => { };
const change = (val) => {
  if (val == "1") {
    addUserStep2Form.formData.smsIsContentSingle = true;
  } else {
    addUserStep2Form.formData.smsIsContentSingle = false;
  }
};
const submitForm = (formEl) => {
  formEl.validate((valid, fields) => {
    if (valid) {
      if (addUserStep2Form.formData.smsReportLevel == 1) {
        addUserStep2Form.formData.smsRepliesPackageSize = "";
        addUserStep2Form.formData.smsStrepliesPackageSize = "";
      }
      addUserStep2Form.formData.smsUserBlackLevel =
        addUserStep2Form.formData.smsUserBlackLevel1.join(",");
      addUserStep2Form.formData.smsUserYzmBlackLevel =
        addUserStep2Form.formData.smsUserYzmBlackLevel1.join(",");
      addUserStep2Form.formData.smsUserHyBlackLevel =
        addUserStep2Form.formData.smsUserHyBlackLevel1.join(",");
      let header = {
        headKey: addUserStep2Form.formData.headKey,
        headValue: addUserStep2Form.formData.headValue,
      };
      let header1 = {
        headKey: addUserStep2Form.formData.headKey1,
        headValue: addUserStep2Form.formData.headValue1,
      };
      let header2 = {
        headKey: addUserStep2Form.formData.headKey2,
        headValue: addUserStep2Form.formData.headValue3,
      };
      let header3 = {
        headKey: addUserStep2Form.formData.headKey3,
        headValue: addUserStep2Form.formData.headValue3,
      };

      let header4 = {
        headKey: addUserStep2Form.formData.headKey3,
        headValue: addUserStep2Form.formData.headValue3,
      };
      let arr = [header, header1, header2, header3, header4];
      if (addUserStep2Form.formData.pushHeader == "0") {
        addUserStep2Form.formData.smsPushHeader = arr.slice(0, 1);
      } else if (addUserStep2Form.formData.pushHeader == "1") {
        addUserStep2Form.formData.smsPushHeader = arr.slice(0, 2);
      } else if (addUserStep2Form.formData.pushHeader == "2") {
        addUserStep2Form.formData.smsPushHeader = arr.slice(0, 3);
      } else if (addUserStep2Form.formData.pushHeader == "3") {
        addUserStep2Form.formData.smsPushHeader = arr.slice(0, 4);
      } else if (addUserStep2Form.formData.pushHeader == "4") {
        addUserStep2Form.formData.smsPushHeader = arr.slice(0, 5);
      } else if (addUserStep2Form.formData.pushHeader == "-1") {
        addUserStep2Form.formData.smsPushHeader = [];
      }
      if (smsExternalBlackSourceData.value) {
        addUserStep2Form.formData.smsExternalBlackSource =
          smsExternalBlackSourceData.value.join(",");
        // if(Object.prototype.toString.call(addUserStep2Form.formData.smsExternalBlackSource) === '[object Array]'){
        //   console.log(addUserStep2Form.formData.smsExternalBlackSource,'iiiiii');
        //   addUserStep2Form.formData.smsExternalBlackSource = addUserStep2Form.formData.smsExternalBlackSource.join(',')
        // }
      }
      if (passOperatorData.value) {
        addUserStep2Form.formData.passOperator =
          passOperatorData.value.join(",");
      }
      const { hySendStartHour, hySendEndHour, yxSendStartHour, yxSendEndHour } =
        addUserStep2Form.formData;
      if (
        ((hySendStartHour && hySendEndHour) ||
          (!hySendStartHour && !hySendEndHour)) &&
        ((yxSendStartHour && yxSendEndHour) ||
          (!yxSendStartHour && !yxSendEndHour))
      ) {
        dialogVisible.value = true;
      } else {
        ElMessage({
          type: "warning",
          message: "请确认设置发送时间是否正确!",
        });
      }
    } else {
      return false;
    }
  });
};
const handleDoubleClick = (text, event) => {
  clip(text, event);
};
const handleClose = () => {
  dialogVisible.value = false;
};
const submitSetting = (formEl) => {
  formEl.validate((valid, fields) => {
    if (valid) {
      subLoding.value = true;
      addUserStep2Form.formData.remark = ruleForm.remark;
      let data = JSON.parse(JSON.stringify(addUserStep2Form.formData));
      // if (data.sign) {
      //   data.sign = `【${data.sign}】`;
      // }
      data.excludeProvince = data.excludeProvince.join(",");
      data.mergeOperator = data.mergeOperator.join(",");
      window.api.put(
        window.path.omcs + "operatinguser/userSmsInfo",
        data,
        (res) => {
          if (res.code == 200) {
            //返回用户管理页面
            subLoding.value = false;
            dialogVisible.value = false;
            goBack();
          } else {
            subLoding.value = false;
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    } else {
      return false;
    }
  });
};
const handleSizeChange = (size) => {
  tabelAlllist.pageSize = size;
  gettableLIst();
};
const handleCurrentChange = (currentPage) => {
  tabelAlllist.currentPage = currentPage;
  gettableLIst();
};
const addopt = () => {
  dialogFormVisible.value = true;
  dialogStatus.value = "add";
};
const submitForAntibombing = (formEl) => {
  formEl.validate((valid, fields) => {
    if (valid) {
      if (dialogStatus.value == "add") {
        window.api.post(
          window.path.omcs + "consumerclientoverrun",
          formop,
          (res) => {
            if (res.code == 200) {
              ElMessage({
                type: "success",
                message: res.msg,
              });
              dialogFormVisible.value = false;
              gettableLIst();
              getBasicData();
            } else {
              ElMessage({
                message: res.msg,
                type: "warning",
              });
            }
          }
        );
      } else {
        formop.id = id.value;
        ElMessageBox.confirm("确认执行此操作吗？", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            window.api.put(
              window.path.omcs + "consumerclientoverrun",
              formop,
              (res) => {
                if (res.code == 200) {
                  dialogFormVisible.value = false;
                  gettableLIst();
                  ElMessage({
                    type: "success",
                    message: res.msg,
                  });
                } else {
                  ElMessage({
                    type: "error",
                    message: res.msg,
                  });
                }
              }
            );
          })
          .catch(() => {
            ElMessage({
              type: "info",
              message: "操作取消",
            });
          });
      }
    } else {
      return false;
    }
  });
};
const handelClose = () => {
  dialogFormVisible.value = false;
};
const detailsRow = (index, val) => {
  dialogFormVisible.value = true;
  dialogStatus.value = "edit";
  id.value = val.id;
  nextTick(() => {
    formopRef.value.resetFields();
    Object.assign(formop, val); //编辑框赋值
    formop.smsType = formop.smsType + "";
    formop.operator = formop.operator + "";
  });
};
const delState = (index, val) => {
  ElMessageBox.confirm("确认执行此操作吗？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      window.api.delete(
        window.path.omcs + "consumerclientoverrun?ids=" + val.id,
        {},
        (res) => {
          if (res.code == 200) {
            gettableLIst();
            ElMessage({
              type: "success",
              message: "删除成功",
            });
          } else {
            ElMessage({
              type: "error",
              message: res.msg,
            });
          }
        }
      );
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作取消",
      });
    });
};
watch(
  () => addUserStep2Form.formData.encryptType,
  (val) => {
    if (val == 1) {
      secretKeyShow.value = true;
      secretIvShow.value = false;
    } else if (val == 2) {
      secretKeyShow.value = true;
      secretIvShow.value = true;
    } else {
      secretKeyShow.value = true;
      secretIvShow.value = false;
      // addUserStep2Form.formData.secretKey = "";
      // addUserStep2Form.formData.secretIv = "";
    }
  }
);
watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      ruleFormRef.value.resetFields();
    }
  }
);
watch(
  () => dialogFormVisible.value,
  (val) => {
    if (!val) {
      id.value = "";
      formopRef.value.resetFields();
    }
  }
);
</script>

<style scoped>
.tips {
  font-size: 12px;
  /* color: #F56C6C; */
}

.OuterFrame {
  padding: 20px;
  background-color: #fff;
  border-radius: 5px;
}

.input-w {
  width: 345px !important;
}

.el-radio-group .el-radio {
  margin-right: 0px;
}

.fillet {
  padding: 20px;
}

.cursor-pointer {
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  /* cursor: pointer; */
}

.input-w-2 {
  width: 200px !important;
}

.input-w-3 {
  width: 200px !important;
}

.input-w-4 {
  width: 190px !important;
}

.input-w-sm {
  width: 126px !important;
}

.input-w-f {
  width: 80px !important;
}

.float-div-1 {
  float: left;
}

.float-div-2 {
  float: left;
}

.float-div-3 {
  float: left;
}

.clear-div {
  clear: both;
}

.red {
  color: red;
}
.cell-item{
  width: 90px;
  font-weight: bold;
  white-space: nowrap;
}
.channel-item{
  min-width: 150px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  word-break: break-all;
}

/* 响应式样式 */
@media screen and (max-width: 1400px) {
  .el-descriptions {
    font-size: 12px;
  }
  
  .channel-item span {
    margin-bottom: 5px;
  }
}

@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}

.flex-column {
  flex-direction: column !important;
}

@media screen and (max-width: 1000px) {
  .cell-item {
    width: 80px;
  }
  
  .el-form-item {
    margin-bottom: 12px;
  }
}

@media screen and (max-width: 800px) {
  .el-descriptions {
    margin-bottom: 15px;
  }
  
  :deep(.el-descriptions__title) {
    font-size: 14px;
    margin-bottom: 8px;
  }
  
  :deep(.el-descriptions__body) {
    background-color: #fafafa;
  }
  
  .channel-item span {
    padding: 2px 4px;
    border-radius: 3px;
    background-color: rgba(103, 194, 58, 0.1);
  }
}

/* 响应式布局样式 */
.flex-column {
  flex-direction: column !important;
}

/* 大屏布局 */
@media screen and (min-width: 1200px) {
  /* 三列布局，默认样式 */
}

/* 中屏布局 */
@media screen and (max-width: 1200px) and (min-width: 1000px) {
  /* 两列布局 */
}

/* 小屏布局 */
@media screen and (max-width: 1000px) {
  /* 垂直布局，通道组卡片在表单下方 */
  .el-form-item {
    margin-bottom: 12px;
  }
}

@media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
}

/* 移动端表单样式优化 */
.mobile-form :deep(.el-form-item__label) {
  font-size: 13px;
  line-height: 1.4;
  padding-bottom: 4px;
}

.mobile-form :deep(.el-form-item) {
  margin-bottom: 15px;
}

.mobile-form :deep(.el-select) {
  width: 150px!important;
}

.mobile-form :deep(.el-input-number) {
  width: 100% !important;
}

.mobile-form :deep(.el-input__inner) {
  height: 36px;
  line-height: 36px;
}

.mobile-form :deep(.el-checkbox) {
  margin-right: 10px;
  margin-left: 0 !important;
  margin-bottom: 5px;
}

.mobile-checkbox-group {
  display: flex;
  flex-wrap: wrap;
}

@media screen and (max-width: 768px) {
  .input-w-2, .input-w-3 {
    width: 100% !important;
  }
  
  .el-select {
    width: 100% !important;
  }
  
  :deep(.el-form-item__content) {
    width: 100%;
  }
  
  :deep(.el-select-dropdown__item) {
    font-size: 14px;
    padding: 0 10px;
  }
  
  :deep(.el-radio) {
    margin-right: 5px;
  }
  
  :deep(.el-form--inline .el-form-item) {
    margin-right: 0;
  }
}

/* @media screen and (max-width: 1845px) {
  .float-div-3 {
    float: none;
    clear: both;
  }
} */

/* 移动端Table适配 */
@media screen and (max-width: 768px) {
  .Mail-table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .el-table {
    min-width: 600px;  /* 确保表格内容在移动端能完整显示 */
  }

  .Signature-search-fun {
    display: flex;
    justify-content: center;
    margin: 15px 0;
  }
  
  .Signature-search-fun button {
    width: 100%;
    max-width: 200px;
  }
  
  .page_bottom {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
}

/* 对话框移动端适配 */
@media screen and (max-width: 768px) {
  :deep(.el-dialog__body) {
    padding: 15px 10px;
  }
  
  :deep(.el-dialog__footer) {
    padding: 10px;
  }
  
  :deep(.el-dialog__header) {
    padding: 15px 10px;
  }
  
  :deep(.dialog-footer) {
    display: flex;
    flex-direction: column;
  }
  
  :deep(.dialog-footer .el-button) {
    margin-left: 0 !important;
    margin-bottom: 10px;
    width: 100%;
  }
  :deep(.el-select__wrapper ) {
    width: 120px !important;
  }
}
</style>