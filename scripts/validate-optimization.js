#!/usr/bin/env node

/**
 * 代码分割和Tree Shaking优化验证脚本
 * 验证所有优化措施的实施效果
 */

const fs = require('fs')
const path = require('path')
const { BundleAnalyzer } = require('./bundle-analyzer')

class OptimizationValidator {
  constructor() {
    this.projectRoot = path.dirname(__dirname)
    this.srcPath = path.join(this.projectRoot, 'src')
    this.configPath = path.join(this.projectRoot, 'vite.config.js')
    this.packagePath = path.join(this.projectRoot, 'package.json')
  }

  /**
   * 运行完整验证
   */
  async validateAll() {
    console.log('🔍 开始验证代码分割和Tree Shaking优化...\n')

    const results = {
      timestamp: new Date().toISOString(),
      viteConfig: await this.validateViteConfig(),
      lazyLoading: await this.validateLazyLoading(),
      treeShaking: await this.validateTreeShaking(),
      bundleOptimization: await this.validateBundleOptimization(),
      components: await this.validateComponents()
    }

    // 生成总体评分
    results.score = this.calculateScore(results)
    
    // 打印结果
    this.printResults(results)
    
    // 保存结果
    this.saveResults(results)

    return results
  }

  /**
   * 验证Vite配置优化
   */
  async validateViteConfig() {
    console.log('📋 验证Vite配置优化...')
    
    const validation = {
      passed: 0,
      total: 0,
      details: []
    }

    try {
      const configContent = fs.readFileSync(this.configPath, 'utf8')
      
      // 检查代码分割配置
      validation.total++
      if (configContent.includes('manualChunks')) {
        validation.passed++
        validation.details.push('✅ 已配置手动代码分割')
      } else {
        validation.details.push('❌ 未发现手动代码分割配置')
      }

      // 检查CSS代码分割
      validation.total++
      if (configContent.includes('cssCodeSplit: true')) {
        validation.passed++
        validation.details.push('✅ 已启用CSS代码分割')
      } else {
        validation.details.push('❌ 未启用CSS代码分割')
      }

      // 检查chunk大小限制
      validation.total++
      if (configContent.includes('chunkSizeWarningLimit') && !configContent.includes('50000000')) {
        validation.passed++
        validation.details.push('✅ 已设置合理的chunk大小警告限制')
      } else {
        validation.details.push('❌ chunk大小警告限制设置不合理')
      }

      // 检查依赖预构建优化
      validation.total++
      if (configContent.includes('optimizeDeps')) {
        validation.passed++
        validation.details.push('✅ 已配置依赖预构建优化')
      } else {
        validation.details.push('❌ 未配置依赖预构建优化')
      }

      // 检查vendor分离配置
      validation.total++
      if (configContent.includes('vendor-vue') && configContent.includes('vendor-element')) {
        validation.passed++
        validation.details.push('✅ 已配置vendor库分离')
      } else {
        validation.details.push('❌ 未配置vendor库分离')
      }

    } catch (error) {
      validation.details.push(`❌ 读取配置文件失败: ${error.message}`)
    }

    validation.score = Math.round((validation.passed / validation.total) * 100)
    return validation
  }

  /**
   * 验证懒加载工具
   */
  async validateLazyLoading() {
    console.log('🔄 验证懒加载功能...')
    
    const validation = {
      passed: 0,
      total: 0,
      details: []
    }

    // 检查懒加载工具文件
    const lazyLoadPath = path.join(this.srcPath, 'utils/lazyLoad.js')
    validation.total++
    if (fs.existsSync(lazyLoadPath)) {
      validation.passed++
      validation.details.push('✅ 懒加载工具文件存在')
      
      try {
        const content = fs.readFileSync(lazyLoadPath, 'utf8')
        
        // 检查关键功能
        validation.total++
        if (content.includes('createLazyComponent')) {
          validation.passed++
          validation.details.push('✅ createLazyComponent函数已实现')
        } else {
          validation.details.push('❌ createLazyComponent函数未实现')
        }

        validation.total++
        if (content.includes('SmartPreloader')) {
          validation.passed++
          validation.details.push('✅ SmartPreloader类已实现')
        } else {
          validation.details.push('❌ SmartPreloader类未实现')
        }

        validation.total++
        if (content.includes('importLibrary')) {
          validation.passed++
          validation.details.push('✅ 动态库导入功能已实现')
        } else {
          validation.details.push('❌ 动态库导入功能未实现')
        }

      } catch (error) {
        validation.details.push(`❌ 读取懒加载工具失败: ${error.message}`)
      }
    } else {
      validation.details.push('❌ 懒加载工具文件不存在')
    }

    // 检查加载状态组件
    const loadingPath = path.join(this.srcPath, 'components/common/Loading.vue')
    validation.total++
    if (fs.existsSync(loadingPath)) {
      validation.passed++
      validation.details.push('✅ Loading组件存在')
    } else {
      validation.details.push('❌ Loading组件不存在')
    }

    const errorPath = path.join(this.srcPath, 'components/common/LoadError.vue')
    validation.total++
    if (fs.existsSync(errorPath)) {
      validation.passed++
      validation.details.push('✅ LoadError组件存在')
    } else {
      validation.details.push('❌ LoadError组件不存在')
    }

    validation.score = Math.round((validation.passed / validation.total) * 100)
    return validation
  }

  /**
   * 验证Tree Shaking优化
   */
  async validateTreeShaking() {
    console.log('🌳 验证Tree Shaking优化...')
    
    const validation = {
      passed: 0,
      total: 0,
      details: []
    }

    // 检查Tree Shaking工具文件
    const treeShakingPath = path.join(this.srcPath, 'utils/treeShaking.js')
    validation.total++
    if (fs.existsSync(treeShakingPath)) {
      validation.passed++
      validation.details.push('✅ Tree Shaking工具文件存在')
      
      try {
        const content = fs.readFileSync(treeShakingPath, 'utf8')
        
        validation.total++
        if (content.includes('optimizedElementImports')) {
          validation.passed++
          validation.details.push('✅ Element Plus按需导入配置已实现')
        } else {
          validation.details.push('❌ Element Plus按需导入配置未实现')
        }

        validation.total++
        if (content.includes('optimizedLodashImports')) {
          validation.passed++
          validation.details.push('✅ Lodash按需导入配置已实现')
        } else {
          validation.details.push('❌ Lodash按需导入配置未实现')
        }

        validation.total++
        if (content.includes('createOptimizedECharts')) {
          validation.passed++
          validation.details.push('✅ ECharts按需导入功能已实现')
        } else {
          validation.details.push('❌ ECharts按需导入功能未实现')
        }

      } catch (error) {
        validation.details.push(`❌ 读取Tree Shaking工具失败: ${error.message}`)
      }
    } else {
      validation.details.push('❌ Tree Shaking工具文件不存在')
    }

    // 检查package.json中的sideEffects配置
    try {
      const packageContent = JSON.parse(fs.readFileSync(this.packagePath, 'utf8'))
      validation.total++
      if (packageContent.sideEffects === false || Array.isArray(packageContent.sideEffects)) {
        validation.passed++
        validation.details.push('✅ package.json中已配置sideEffects')
      } else {
        validation.details.push('ℹ️ 建议在package.json中配置sideEffects以优化Tree Shaking')
      }
    } catch (error) {
      validation.details.push(`❌ 读取package.json失败: ${error.message}`)
    }

    validation.score = Math.round((validation.passed / validation.total) * 100)
    return validation
  }

  /**
   * 验证打包优化效果
   */
  async validateBundleOptimization() {
    console.log('📦 验证打包优化效果...')
    
    const validation = {
      passed: 0,
      total: 0,
      details: [],
      bundleAnalysis: null
    }

    try {
      // 运行打包分析
      const analyzer = new BundleAnalyzer()
      
      // 检查是否有构建输出
      if (fs.existsSync(analyzer.distPath)) {
        const analysis = await analyzer.analyzeBuild()
        validation.bundleAnalysis = analysis.summary

        // 检查代码分割效果
        validation.total++
        if (analysis.optimization.codeSplitting.vendorChunks > 0) {
          validation.passed++
          validation.details.push(`✅ 发现 ${analysis.optimization.codeSplitting.vendorChunks} 个vendor chunks`)
        } else {
          validation.details.push('❌ 未发现vendor代码分割')
        }

        // 检查vendor分离效果
        validation.total++
        if (analysis.optimization.vendorSeparation.vendorRatio > 0.1) {
          validation.passed++
          validation.details.push(`✅ Vendor分离率: ${(analysis.optimization.vendorSeparation.vendorRatio * 100).toFixed(1)}%`)
        } else {
          validation.details.push('❌ Vendor分离效果不明显')
        }

        // 检查是否有过大的chunk
        validation.total++
        const hasLargeChunks = analysis.recommendations.some(rec => rec.type === 'large-files')
        if (!hasLargeChunks) {
          validation.passed++
          validation.details.push('✅ 未发现过大的JS文件')
        } else {
          validation.details.push('⚠️ 发现过大的JS文件，建议进一步优化')
        }

      } else {
        validation.details.push('ℹ️ 未找到构建输出，跳过打包分析')
      }

    } catch (error) {
      validation.details.push(`❌ 打包分析失败: ${error.message}`)
    }

    validation.score = Math.round((validation.passed / validation.total) * 100)
    return validation
  }

  /**
   * 验证组件优化
   */
  async validateComponents() {
    console.log('🧩 验证组件优化...')
    
    const validation = {
      passed: 0,
      total: 0,
      details: []
    }

    // 检查路由懒加载
    const routerPath = path.join(this.srcPath, 'router')
    validation.total++
    if (fs.existsSync(routerPath)) {
      try {
        const files = fs.readdirSync(routerPath, { withFileTypes: true })
        const hasModularRoutes = files.some(file => 
          file.isDirectory() && file.name === 'modules'
        )
        
        if (hasModularRoutes) {
          validation.passed++
          validation.details.push('✅ 路由已模块化')
        } else {
          validation.details.push('ℹ️ 路由尚未模块化，建议考虑拆分')
        }
      } catch (error) {
        validation.details.push(`❌ 检查路由失败: ${error.message}`)
      }
    } else {
      validation.details.push('❌ 路由目录不存在')
    }

    // 检查大型组件是否使用懒加载
    const componentsPath = path.join(this.srcPath, 'components')
    validation.total++
    try {
      const largeComponents = this.findLargeComponents(componentsPath)
      if (largeComponents.length > 0) {
        validation.details.push(`ℹ️ 发现 ${largeComponents.length} 个大型组件，建议使用懒加载:`)
        largeComponents.forEach(comp => {
          validation.details.push(`   - ${comp.path} (${comp.sizeFormatted})`)
        })
      } else {
        validation.passed++
        validation.details.push('✅ 未发现过大的组件文件')
      }
    } catch (error) {
      validation.details.push(`❌ 检查组件失败: ${error.message}`)
    }

    validation.score = Math.round((validation.passed / validation.total) * 100)
    return validation
  }

  /**
   * 查找大型组件
   */
  findLargeComponents(dir, threshold = 50 * 1024) { // 50KB
    const largeComponents = []
    
    function scanDir(currentDir) {
      const items = fs.readdirSync(currentDir)
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory()) {
          scanDir(fullPath)
        } else if (item.endsWith('.vue') && stat.size > threshold) {
          largeComponents.push({
            path: path.relative(this.srcPath, fullPath),
            size: stat.size,
            sizeFormatted: this.formatSize(stat.size)
          })
        }
      })
    }

    scanDir(dir)
    return largeComponents.sort((a, b) => b.size - a.size)
  }

  /**
   * 计算总体评分
   */
  calculateScore(results) {
    const weights = {
      viteConfig: 0.3,
      lazyLoading: 0.2,
      treeShaking: 0.3,
      bundleOptimization: 0.15,
      components: 0.05
    }

    let totalScore = 0
    let totalWeight = 0

    Object.keys(weights).forEach(key => {
      if (results[key] && typeof results[key].score === 'number') {
        totalScore += results[key].score * weights[key]
        totalWeight += weights[key]
      }
    })

    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0
  }

  /**
   * 打印验证结果
   */
  printResults(results) {
    console.log('\n' + '='.repeat(60))
    console.log('📊 代码分割和Tree Shaking优化验证报告')
    console.log('='.repeat(60))

    // 总体评分
    const scoreColor = results.score >= 80 ? '🟢' : results.score >= 60 ? '🟡' : '🔴'
    console.log(`\n${scoreColor} 总体评分: ${results.score}/100`)

    // 各项详情
    console.log('\n📋 详细结果:')
    
    Object.entries(results).forEach(([key, result]) => {
      if (key === 'timestamp' || key === 'score') return
      
      const emoji = result.score >= 80 ? '✅' : result.score >= 60 ? '⚠️' : '❌'
      console.log(`\n${emoji} ${this.getDisplayName(key)}: ${result.score}% (${result.passed}/${result.total})`)
      
      result.details.forEach(detail => {
        console.log(`   ${detail}`)
      })
    })

    // 打包分析摘要
    if (results.bundleOptimization.bundleAnalysis) {
      const analysis = results.bundleOptimization.bundleAnalysis
      console.log('\n📦 打包分析摘要:')
      console.log(`   总大小: ${analysis.totalSizeFormatted}`)
      console.log(`   JS大小: ${analysis.jsSizeFormatted}`)
      console.log(`   Vendor大小: ${analysis.vendorSizeFormatted}`)
    }

    // 优化建议
    console.log('\n💡 优化建议:')
    this.generateOptimizationSuggestions(results).forEach(suggestion => {
      console.log(`   ${suggestion}`)
    })

    console.log('\n' + '='.repeat(60))
  }

  /**
   * 获取显示名称
   */
  getDisplayName(key) {
    const names = {
      viteConfig: 'Vite配置优化',
      lazyLoading: '懒加载功能',
      treeShaking: 'Tree Shaking优化',
      bundleOptimization: '打包优化效果',
      components: '组件优化'
    }
    return names[key] || key
  }

  /**
   * 生成优化建议
   */
  generateOptimizationSuggestions(results) {
    const suggestions = []

    if (results.viteConfig.score < 80) {
      suggestions.push('🔧 完善Vite配置，确保所有优化选项都已启用')
    }

    if (results.treeShaking.score < 80) {
      suggestions.push('🌳 优化Tree Shaking配置，使用按需导入减少包大小')
    }

    if (results.bundleOptimization.score < 60) {
      suggestions.push('📦 优化代码分割策略，减少大型chunk的产生')
    }

    if (results.lazyLoading.score < 80) {
      suggestions.push('🔄 完善懒加载工具，提高组件加载效率')
    }

    if (suggestions.length === 0) {
      suggestions.push('🎉 所有优化都已实施，继续保持！')
    }

    return suggestions
  }

  /**
   * 保存验证结果
   */
  saveResults(results) {
    try {
      const reportPath = path.join(this.projectRoot, 'optimization-report.json')
      fs.writeFileSync(reportPath, JSON.stringify(results, null, 2))
      console.log(`\n📄 详细报告已保存到: ${reportPath}`)
    } catch (error) {
      console.warn('保存报告失败:', error.message)
    }
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

// 主函数
async function main() {
  try {
    const validator = new OptimizationValidator()
    const results = await validator.validateAll()
    
    // 根据评分设置退出码
    process.exit(results.score >= 60 ? 0 : 1)
  } catch (error) {
    console.error('验证失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = OptimizationValidator 