<template>
  <div class="container_left">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-icon><el-icon-lx-emoji /></el-icon
          >阅信解析配置</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="padding: 10px">
      <el-form
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
        label-width="80px"
      >
        <el-form-item label="模板ID" prop="xsmsTemplateId">
          <el-input
            class="input-w"
            v-model="ruleForm.xsmsTemplateId"
            placeholder="模板ID"
          ></el-input>
        </el-form-item>
        <el-form-item label="生成批次" prop="batchNo">
          <el-input
            class="input-w"
            v-model="ruleForm.batchNo"
            placeholder="生成批次"
          ></el-input>
        </el-form-item>

        <el-form-item label="短链状态" prop="statusArr">
          <el-select
            class="input-w"
            v-model="ruleForm.statusArr"
            multiple
            placeholder="请选择"
          >
            <el-option label="生成中" value="0"> </el-option>
            <el-option label="生效中" value="1"> </el-option>
            <el-option label="生成失败" value="2"> </el-option>
            <el-option label="已失效" value="3"> </el-option>
            <el-option label="确认失败" value="4"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标记状态" prop="label">
          <el-select
            class="input-w"
            v-model="ruleForm.label"
            clearable
            placeholder="请选择"
          >
            <el-option label="全部" value=""> </el-option>
            <el-option label="已标记" value="1"> </el-option>
            <el-option label="未标记" value="0"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="短链" prop="url">
          <el-input
            class="input-w"
            v-model="ruleForm.url"
            placeholder="阅信链接/AIM链接"
          ></el-input>
        </el-form-item>
        <el-form-item label-width="110px" label="短链创建时间" prop="value1">
          <el-date-picker
            :default-time="['00:00:00', '23:59:59']"
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handelDate"
          >
          </el-date-picker>
        </el-form-item>
        <div>
          <el-button plain type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button plain type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </div>
      </el-form>
    </div>
    <!-- 1 -->
    <div style="padding: 0 10px"></div>
    <div style="padding: 0px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        height="600"
        border
        :data="tableDataObj.tableData"
        :span-method="objectSpanMethod"
        :header-cell-style="{
          background: '#eef1f6',
          color: '#606266',
        }"
      >
        >
        <el-table-column label="短链创建时间" width="120">
          <template v-slot="scope">
            <p>{{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</p>
            <p>{{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</p>
            <!-- <div class="item_time" v-if="scope.row.createTime">{{
                                    moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="短链生成批次" width="120">
          <template v-slot="scope">
            <span>{{ scope.row.batchNo }}</span>
            <img
              v-if="scope.row.label == 0"
              src="../../../../../assets/images/start.png"
              alt=""
              @click="handelLable(scope.row)"
              width="13px"
              style="margin-left: 5px; cursor: pointer"
            />
            <img
              v-if="scope.row.label == 1"
              src="../../../../../assets/images/start_active.png"
              alt=""
              @click="handelLable(scope.row)"
              width="13px"
              style="margin-left: 5px; cursor: pointer"
            />
          </template>
        </el-table-column>
        <el-table-column label="chatbotId" width="180">
          <template v-slot="scope">{{ scope.row.chatbotId }}</template>
        </el-table-column>
        <el-table-column label="Chatbot名称" width="140">
          <template v-slot="scope">{{ scope.row.chatbotName }}</template>
        </el-table-column>
        <el-table-column label="模板名称" width="140">
          <template v-slot="scope">{{ scope.row.xsmsTemplateName }}</template>
        </el-table-column>
        <el-table-column label="模板ID" width="180">
          <template v-slot="scope">{{ scope.row.xsmsTemplateId }}</template>
        </el-table-column>
        <el-table-column label="短链状态" width="140">
          <template v-slot="scope">
            <div
              class="item_status"
              style="
                background: #f8eddd;
                color: #ff8a00;
                border: 1px solid #ffd8b4;
              "
              v-if="scope.row.linkStatus == 0"
            >
              生成中
            </div>
            <div
              class="item_status"
              style="
                background: #f5fff4;
                color: #00c159;
                border: 1px solid #a6edb1;
              "
              v-if="scope.row.linkStatus == 1"
            >
              生效中
            </div>
            <el-tooltip
              v-if="scope.row.linkStatus == 2"
              effect="dark"
              placement="top-start"
            >
              <template v-slot:content>
                <div>
                  <div>
                    原因：<span v-if="scope.row.reason">{{
                      scope.row.reason
                    }}</span>
                    <span v-else>--</span>
                  </div>
                </div>
              </template>
              <div
                class="item_status"
                style="
                  background: #ffe7e7;
                  color: #ff6666;
                  border: 1px solid #fac2c2;
                "
              >
                生成失败
              </div>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.linkStatus == 3"
              effect="dark"
              placement="top-start"
            >
              <template v-slot:content>
                <div>
                  <div>
                    原因：<span v-if="scope.row.reason">{{
                      scope.row.reason
                    }}</span>
                    <span v-else>--</span>
                  </div>
                </div>
              </template>
              <div
                class="item_status"
                style="
                  background: #ffe7e7;
                  color: #ff6666;
                  border: 1px solid #fac2c2;
                "
              >
                已失效
              </div>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.linkStatus == 4"
              effect="dark"
              placement="top-start"
            >
              <template v-slot:content>
                <div>
                  <div>
                    原因：<span v-if="scope.row.reason">{{
                      scope.row.reason
                    }}</span>
                    <span v-else>--</span>
                  </div>
                </div>
              </template>
              <div
                class="item_status"
                style="
                  background: #ffe7e7;
                  color: #ff6666;
                  border: 1px solid #fac2c2;
                "
              >
                确认失败
              </div>
            </el-tooltip>
            <!-- <span v-if="scope.row.linkStatus == 0">生成中</span>
                            <span style="color: #409eff" v-if="scope.row.linkStatus == 1">生效中</span>
                            <span style="color: red" v-if="scope.row.linkStatus == 2">生成失败</span>
                            <span style="color: red" v-if="scope.row.linkStatus == 3">已失效</span>
                            <span style="color: red" v-if="scope.row.linkStatus == 4">确认失败</span> -->
          </template>
        </el-table-column>
        <el-table-column label="来源" width="120">
          <template v-slot="scope">
            <span v-if="scope.row.source == 0">汇总</span>
            <span
              style="color: #409eff; cursor: pointer"
              @click="ssoLogin(scope.row)"
              v-else-if="scope.row.source == 1"
              >阅信</span
            >
            <span v-else-if="scope.row.source == 2">AIM</span>
          </template>
        </el-table-column>
        <el-table-column label="链接" width="140">
          <template v-slot="scope">
            <a
              v-if="scope.row.url"
              :href="'https://' + scope.row.url"
              target="_blank"
              rel="noopener noreferrer"
              >{{ scope.row.url }}</a
            >
          </template>
        </el-table-column>
        <el-table-column label="解析次数上限" width="140">
          <template v-slot="scope">
            <span v-if="scope.row.showTimes == null">-</span>
            <span v-else-if="scope.row.showTimes == 0 || scope.row.showTimes">{{
              scope.row.showTimes
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已解析次数" width="140">
          <template v-slot="scope">
            <div v-if="scope.row.used == null">-</div>
            <div v-else-if="scope.row.used == 0 || scope.row.used">
              <span>{{ scope.row.used }}</span>
              <!-- <el-input v-else v-model="usedTimes" @change="handelInput" placeholder="请输入内容"></el-input> -->
              <el-button
                v-if="
                  (scope.row.linkStatus == 1 || scope.row.linkStatus == 3) &&
                  scope.row.source == 1
                "
                style="margin-left: 5px"
                type="text"
                @click="handelUpdate(scope.row, scope.$index)"
                >更新
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="剩余解析次数" width="140">
          <template v-slot="scope">
            <span v-if="scope.row.available == null">-</span>
            <span v-else-if="scope.row.available == 0 || scope.row.available">{{
              scope.row.available
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="解析次数更新时间" width="120">
          <template v-slot="scope">
            <!-- <div class="item_time" v-if="scope.row.updateTime">{{
                                    moment(scope.row.updateTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
            <p v-if="scope.row.updateTime">
              {{ $parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.updateTime">
              {{ $parseTime(scope.row.updateTime, '{h}:{i}:{s}') }}
              <el-tooltip
                v-if="scope.row.needUpdate == 1 && scope.row.source == 1"
                effect="dark"
                content="待维护阅信解析次数"
                placement="top"
              >
                <el-icon style="margin-left: 5px; color: red; font-size: 16px"
                  ><el-icon-warning
                /></el-icon>
              </el-tooltip>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="到期时间" width="120">
          <template v-slot="scope">
            <p v-if="scope.row.expireTime">
              {{ $parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}
            </p>
            <p v-if="scope.row.expireTime">
              {{ $parseTime(scope.row.expireTime, '{h}:{i}:{s}') }}
            </p>
            <!-- <div class="item_time" v-if="scope.row.expireTime">{{
                                    moment(scope.row.expireTime).format("YYYY-MM-DD HH:mm:ss")
                            }}</div> -->
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template v-slot="scope">
            <el-button
              v-if="scope.row.linkStatus == 2"
              type="text"
              style="color: #409eff; margin-left: 0px"
              @click="regenerate(scope.row)"
              >重新生成</el-button
            >
            <el-button
              v-if="scope.row.linkStatus == 2"
              type="text"
              style="color: #f56c6c; margin-left: 10px"
              @click="identificationFfailure(scope.row)"
            >
              确认失败
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <div></div>
        <div style="margin: 10px">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      title="修改阅信已解析次数"
      v-model="dialogEditNum"
      width="30%"
      :before-close="handleClose"
    >
      <el-descriptions title="基本信息" size="medium" :column="2">
        <el-descriptions-item label="短链生成批次：">{{
          updateList.batchNo
        }}</el-descriptions-item>
        <el-descriptions-item label="模板名称:">{{
          updateList.xsmsTemplateName
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信链接:">{{
          updateList.xsmsUrl
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信解析数上限：">
          <span v-if="updateList.xsmsShowTimes == null">-</span>
          <span
            v-else-if="
              updateList.xsmsShowTimes == 0 || updateList.xsmsShowTimes
            "
            >{{ updateList.xsmsShowTimes }}</span
          >
        </el-descriptions-item>
        <el-descriptions-item label="当前已解析数：">{{
          updateList.xsmsUsed
        }}</el-descriptions-item>
        <el-descriptions-item label="阅信更新时间：">
          <span v-if="updateList.xsmsExpireTime">{{
            moment(updateList.xsmsExpireTime).format('YYYY-MM-DD HH:mm:ss')
          }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <div
        style="margin: 10px 0; font-size: 16px; font-weight: 800; color: #000"
      >
        修改次数
      </div>
      <el-form :model="formInline" class="demo-form-inline">
        <el-form-item label="阅信已解析次数">
          <el-input
            style="width: 200px"
            v-model="formInline.usedTimes"
            placeholder="请输入解析次数"
          ></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-checkbox v-model="formInline.checked"
            >确认以上信息无误</el-checkbox
          >
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="dialogEditNum = false">取 消</el-button>
          <el-button
            type="primary"
            :disabled="!formInline.checked"
            @click="handelInput"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- <el-dialog
          :title="'模板审核' + '-' + title"
          v-model="dialogVisible"
          width="60%"
          :before-close="handleClose"
        >
          <viewXsmsTemo :list="detail" />
           <div style="display: flex;margin: 10px 0;" v-if="detail.status == 0 || detail.status == 5">
            <el-form
            :model="reasonFrom"
            :inline="true"
            :rules="rules"
            ref="reasonFrom"
            class="demo-reasonFrom"
          >
          <el-form-item label="审核原因：" prop="reason">
            <el-input
              :maxlength="20"
              show-word-limit
              type="textarea"
              placeholder="不能超过20个字符"
              style="width: 500px"
              v-model="reasonFrom.reason"
            ></el-input>
            </el-form-item>
          </el-form>
          </div>
          <div
            v-else
            style="
              width: 100%;
              background: #f2f5fa;
              line-height: 30px;
              display: flex;
              margin-top: 10px;
            "
          >
            <div style="padding: 5px">
              <div>
                <span>审核状态：</span>
                <span v-if="detail.status == 0">未审核</span>
                <span style="color: red" v-if="detail.status == 5">审核中</span>
                <span style="color: red" v-if="detail.status == 10"
                  >运营商审核中</span
                >
                <span style="color: #409eff" v-if="detail.status == 20"
                  >审核通过</span
                >
                <span style="color: red" v-if="detail.status == 30"
                  >审核失败</span
                >
              </div>
              <div>
                <span>审核人：</span>
                <span>{{detail.auditUser}}</span>
              </div>
            </div>
            <div style="padding: 5px; margin-left: 30px">
              <div>
                <span>审核原因：</span>
                <span>{{detail.auditReason}}</span>
              </div>
              <div>
                <span>审核时间：</span>
                <span v-if="detail.auditTime">{{
                  moment(detail.auditTime).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
              </div>
            </div>
          </div>
          <template #footer>
            <el-button @click="dialogVisible = false">关闭</el-button>
            <el-button
              v-if="detail.status == 0 || detail.status == 5"
              type="primary"
              @click="audit(detail, 1, '通过','reasonFrom')"
            >
              通过</el-button
            >
            <el-button
              v-if="detail.status == 0 || detail.status == 5"
              type="danger"
              style="margin-left: 10px"
              @click="audit(detail, 2, '不通过','reasonFrom')"
              >不通过</el-button
            >
          </template>
        </el-dialog> -->
  </div>
</template>

<script>
import moment from 'moment'
import { debounce } from '@/utils/debounce'
export default {
  components: {
    ElIconLxEmoji,ElIconWarning,
  },
  data() {

return {
status: '',
title: '',
reason: '',
usedTimes: '',
xsmsUrl: '',
clientHeight: document.documentElement.clientHeight,
tableHeight: 500,
usedTimesIndex: null,
dialogVisible: false,
dialogEditNum: false,
codeFlag: true,
formInline: {
usedTimes: '',
checked: false,
},
value1: [],
ruleForm: {
// value1: [],
statusArr: [],
startTime: '',
endTime: '',
label: '',
xsmsTemplateId: '',
batchNo: '',
// status: '',
url: '',
currentPage: 1,
pageSize: 10,
},
reasonFrom: {
reason: '',
},
rules: {
reason: [
{ required: true, message: '审核原因不能为空', trigger: 'blur' },
],
},
tabelAlllist: {
// value1: [],
statusArr: [],
startTime: '',
endTime: '',
label: '',
xsmsTemplateId: '',
batchNo: '',
// status: '',
url: '',
currentPage: 1,
pageSize: 10,
},
detail: {},
updateList: {},
tableDataObj: {
loading2: false,
tableData: [],
total: 0,
},
isFirstEnter: false,
}
},
name: 'xsmsSetting',
created() {
this.isFirstEnter = true
//  初始值，自己定义
this.tableHeight = this.clientHeight - 126
this.$nextTick(() => {
this.getchatList()
})
},
activated() {
if (this.$route.meta.isBack || !this.isFirstEnter) {
this.$nextTick(() => {
//  初始值，自己定义
this.tableHeight = this.clientHeight - 126
this.getchatList()
})
} else {
this.$route.meta.isBack = false
this.isFirstEnter = false
}
},
mounted() {
//  重置浏览器高度
window.onresize = () => {
this.clientHeight = document.documentElement.clientHeight
}
},
methods: {
objectSpanMethod({ row, column, rowIndex, columnIndex }) {
if (columnIndex < 7 || columnIndex == 14) {
if (rowIndex % 3 === 0) {
  return {
    rowspan: 3,
    colspan: 1,
  }
} else {
  return {
    rowspan: 0,
    colspan: 0,
  }
}
}
},
getchatList() {
this.tableDataObj.loading2 = true
// this.tabelAlllist.status = this.tabelAlllist.shortStatus.join(',')
window.api.post(
window.path.fiveWeb + '/operator/link/page',
this.tabelAlllist,
(res) => {
  this.tableDataObj.tableData = res.data.records
  this.tableDataObj.total = res.data.total
  this.tableDataObj.loading2 = false
  // console.log(res, "res");
}
)
},
getRowClass({ rowIndex, columnIndex }) {
// if (columnIndex == 11||columnIndex == 12||columnIndex == 13||columnIndex == 14||columnIndex == 15) {
//     return "background:#EEFBEE;";
// }else if(columnIndex == 6||columnIndex == 7||columnIndex == 8||columnIndex == 9||columnIndex == 10){
//     return "background:#EDF4FF;";
// }
},
handelLable(row) {
this.$confirms.confirmation(
'put',
'该操作将标记次链接！',
window.path.fiveWeb + '/operator/link/label/' + row.id,
{
  // id: row.id,
},
(res) => {
  if (res.code == 200) {
    this.getchatList()
  }
}
)
},
ssoLogin(row) {
this.$router.push({
path: '/chatbotChannel',
query: { chatbotId: row.chatbotId },
})
},
regenerate(row) {
this.$confirms.confirmation(
'post',
'该操作将重新生成链接！',
window.path.fiveWeb + '/operator/link/retry',
{
  id: row.id,
},
(res) => {
  if (res.code == 200) {
    this.getchatList()
  }
}
)
},
identificationFfailure(row) {
this.$confirms.confirmation(
'put',
'是否确认失败？',
window.path.fiveWeb + '/operator/link/confirmFail',
{
  id: row.id,
},
(res) => {
  if (res.code == 200) {
    this.getchatList()
  }
}
)
},
handleClose() {
this.dialogVisible = false
this.dialogEditNum = false
},
handelDate(val) {
if (val) {
this.ruleForm.startTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
this.ruleForm.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
} else {
this.ruleForm.startTime = ''
this.ruleForm.endTime = ''
}
},
handelUpdate(row, index) {
this.usedTimesIndex = index
this.xsmsUrl = row.url
this.updateList = row
this.dialogEditNum = true
},
handelInput() {
window.api.put(
window.path.fiveWeb + '/operator/xsmsamiurl/updateAvailableTimes',
{
  usedTimes: this.formInline.usedTimes,
  xsmsUrl: this.xsmsUrl,
},
(res) => {
  if (res.code == 200) {
    this.getchatList()
    // this.formInline.usedTimes = ''
    this.dialogEditNum = false
    this.$message({
      message: res.msg,
      type: 'success',
    })
  } else {
    this.$message({
      message: res.msg,
      type: 'error',
    })
  }
  // console.log(res, "res");
}
)
},
handleSizeChange(size) {
this.tabelAlllist.pageSize = size
this.getchatList()
},
handleCurrentChange(currentPage) {
this.tabelAlllist.currentPage = currentPage
this.getchatList()
},
submitForm() {
Object.assign(this.tabelAlllist, this.ruleForm)
this.getchatList()
},
resetForm() {
this.$refs.ruleForm.resetFields()
this.ruleForm.startTime = ''
this.ruleForm.endTime = ''
Object.assign(this.tabelAlllist, this.ruleForm)
this.value1 = []
this.getchatList()
},
checked(row) {
this.dialogVisible = true
this.registerFrom = row
this.registerFrom.renewed = row.renewed + ''
this.registerFrom.industryTypeCode = row.industryTypeCode * 1
this.status = row.customerStatus
//   console.log(this.registerFrom );
if (row.code) {
this.codeFlag = true
} else {
this.codeFlag = false
}
},
audit(row, val, tit, from) {
this.$refs[from].validate((valid) => {
if (valid) {
  this.$confirms.confirmation(
    'post',
    `确认审核${tit}模板吗？`,
    window.path.fiveWeb + '/operator/xsmstemplate/audit',
    {
      audit: val,
      reason: this.reasonFrom.reason,
      templateId: row.id,
    },
    (res) => {
      if (res.code == 200) {
        this.dialogVisible = false
        this.getchatList()
      }
    }
  )
} else {
  console.log('error submit!!')
  return false
}
})
},
// 防抖
getTableHeight: debounce(function () {
// 给table高度赋值
this.tableHeight = this.clientHeight - 126
}, 300),
},
watch: {
dialogEditNum(val) {
if (!val) {
this.formInline.usedTimes = ''
}
},
dialogVisible(val) {
if (!val) {
this.reason = ''
this.reasonFrom.reason = ''
// this.$refs['reasonFrom'].resetFields();
// this.detail = {};
if (this.detail.status == 0 || this.detail.status == 5) {
  this.$refs['reasonFrom'].resetFields()
}
}
},
//  监听高度变化
clientHeight(newValue, oldValue) {
if (newValue) {
this.getTableHeight()
}
},
}
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
</style>

<style>
/* .el-upload--text{
  width: 80px;
  height: 32px;
  border: none;
} */
</style>
