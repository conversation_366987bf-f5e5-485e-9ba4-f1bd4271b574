<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="sensitiveCondition.title"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" prop="auditStatus">
            <el-select
              v-model="sensitiveCondition.auditStatus"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="time1">
            <el-date-picker
              class="input-time"
              v-model="sensitiveCondition.time1"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande1"
            >
            </el-date-picker>
          </el-form-item>
          <div style="margin-bottom: 18px">
            <el-button type="primary" plain @click.prevent="Query()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive">
        <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">彩信审核列表</span> -->
        <!-- <el-button type="primary"  @click="establishSig()">创建签名</el-button> -->
        <el-button
          type="primary"
          @click="sensitiveBatchDel()"
          v-if="remarkObj.formData.ids.length != 0"
          >批量通过</el-button
        >
        <el-button
          type="danger"
          @click="sensitiveBatchSet()"
          v-if="remarkObj.formData.ids.length != 0"
          >批量驳回</el-button
        >
      </div>
      <div class="sensitive-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="46"></el-table-column>
          <!-- <el-table-column label="签名ID" width="80">
                            <template #default="scope">{{ scope.row.signatureId }}</template>
                        </el-table-column> -->
          <el-table-column label="用户名称">
            <template v-slot="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.username"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;">
                                     {{ scope.row.username}}
                                    </span>
                                </el-popover> -->
              {{ scope.row.username }}
            </template>
          </el-table-column>
          <el-table-column label="彩信标题">
            <template v-slot="scope">
              <div
                style="color: #409eff; cursor: pointer"
                @click="View(scope.row)"
              >
                <Tooltip
                  v-if="scope.row.title"
                  :content="scope.row.title"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </div>

              <!-- {{ scope.row.title }} -->
            </template>
          </el-table-column>
          <!-- <el-table-column label="预览">
                            <template #default="scope">
                                <el-button type="text" @click="View(scope.row)"><i
                                        class="el-icon-picture"></i>&nbsp;预览</el-button>
                            </template>
                        </el-table-column> -->
          <!-- <el-table-column label="截图" width="80">
                            <template #default="scope">
                                <el-button type="text" @click="lookUp(scope.$index, scope.row)"><i class="el-icon-picture"></i>&nbsp;查看</el-button>
                            </template>
                        </el-table-column> -->
          <el-table-column label="敏感词">
            <template v-slot="scope">
              <span style="color: rgb(245, 108, 108)">{{
                scope.row.prompt
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="提交号码数（总行数）" width="140">
            <template v-slot="scope">
              <span>{{ scope.row.totalNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="有效号码（有效行数）" width="140">
            <template v-slot="scope">
              <span>{{ scope.row.effectiveNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="无效号码（无效行）" width="140">
            <template v-slot="scope">
              <span>{{ scope.row.invalidNum }}</span>
            </template>
          </el-table-column>
          <el-table-column label="发送时间" width="100">
            <template v-slot="scope">
              <p v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.sendTime">
                {{ $parseTime(scope.row.sendTime, '{h}:{i}:{s}') }}
              </p>
              <!-- {{ scope.row.sendTime}} -->
            </template>
          </el-table-column>
          <el-table-column label="文件名/手机号">
            <template v-slot="scope">
              <span
                v-if="scope.row.filePath"
                style="cursor: pointer; color: #16a589"
                @click="download(scope.row)"
                >{{ scope.row.fileOriginalName }}</span
              >
              <span
                v-else
                :title="scope.row.mobile"
                style="
                  max-width: 150px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  display: block;
                "
                >{{ scope.row.mobile }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="100">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                v-if="scope.row.auditStatus == '1'"
                type="info"
                effect="dark"
              >
                待审核
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-if="scope.row.auditStatus == '2'"
                type="success"
                effect="dark"
              >
                审核通过
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.auditStatus == '3'"
                type="danger"
                effect="dark"
              >
                审核不通过
              </el-tag>
              <!-- <span v-if="scope.row.auditStatus == '1'">待审核</span>
                                <span v-else-if="scope.row.auditStatus == '2'" style="color:#16A589">审核通过</span>
                                <span v-else style="color:#f56c6c">审核不通过</span> -->
            </template>
          </el-table-column>
          <el-table-column
            v-if="sensitiveConObj.auditStatus != 1"
            prop="auditUsername"
            label="审核人"
          ></el-table-column>
          <el-table-column
            v-if="sensitiveConObj.auditStatus != 1"
            label="审核时间"
            width="100"
          >
            <template v-slot="scope">
              <p v-if="scope.row.auditTime">
                {{ $parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.auditTime">
                {{ $parseTime(scope.row.auditTime, '{h}:{i}:{s}') }}
              </p>
              <!-- {{ scope.row.sendTime}} -->
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template v-slot="scope">
              <!-- <el-button type="text" style="color:#f56c6c;margin-left:0px;" @click="handleEdit(scope.$index, scope.row)"><i class="el-icon-delete"></i>&nbsp;删除</el-button> -->
              <el-button
                type="text"
                style="margin-left: 0px"
                v-if="scope.row.auditStatus == '1'"
                @click="handleAdopt(scope.$index, scope.row)"
                ><el-icon><el-icon-success /></el-icon>&nbsp;通过</el-button
              >
              <el-button
                type="text"
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.auditStatus == '1'"
                @click="handleReject(scope.$index, scope.row)"
                ><el-icon><el-icon-error /></el-icon>&nbsp;驳回</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹窗（查看图片） -->
      <!-- <el-dialog :title='titleS' v-model="imagesshow" width="600px" :close-on-click-modal = false> 
                    <template  >
                        <el-carousel :interval="5000" arrow="always" :autoplay=false style="height:560px">
                            <el-carousel-item style="height:560px" v-for="(item,index) in imgUrl" :key='index'>
                                <img style="width:100%;max-height:580px" :src='item' alt="">
                            </el-carousel-item>
                        </el-carousel>
                    </template>
                </el-dialog> -->
      <!-- 弹框 （审核 通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="500px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="80px"
          style="padding: 0 28px"
        >
          <el-form-item label="拒绝原因" prop="auditReason">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" v-model="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../assets/images/sendmobel.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span
                  style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  "
                  >{{ title }}</span
                >
              </div>
              <div
                style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                "
                v-for="(item, index) in viewData"
                :key="index"
              >
                <img
                  v-if="
                    item.media == 'jpg' ||
                    item.media == 'gif' ||
                    item.media == 'png'
                  "
                  :src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  style="width: 235px"
                  class="avatar video-avatar"
                  ref="avatar"
                />
                <video
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'video'"
                  style="width: 235px"
                  class="avatar video-avatar"
                  controls="controls"
                ></video>
                <audio
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"
                  v-if="item.type == 'audio'"
                  style="width: 235px"
                  autoplay="autoplay"
                  controls="controls"
                  preload="auto"
                ></audio>
                <div style="white-space: pre-line" v-html="item.txt"></div>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div style="padding: 10px 60px">
          <div
            v-for="(item, index) in viewData"
            :key="index"
            style="display: inline-block; margin: 0 15px"
          >
            <span
              v-if="item.media"
              style="cursor: pointer; color: #16a589"
              @click="downloadView(item.mediaPath)"
              >{{ item.media }}素材下载</span
            >
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    TableTem,
    FileUpload,
    UserLists,
    Tooltip
  },
  name: 'MMSAudit',
  data() {
    return {
      // 预览
      viewData: '',
      indexVeew: 1,
      title: '',
      dialogVisible: false,
      userFlag: false,
      nameover: '',
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      remarkshow: false,
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          ids: [],
        },
        formRule: {
          auditReason: [
            { required: true, message: '请输入拒绝原因', trigger: 'blur' },
            {
              min: 1,
              max: 70,
              message: '长度在 1 到 70 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      idArrs: [], // 驳回的id
      title: '',
      signatureFrom: {
        title: '',
        formData: {
          signature: '',
          clientName: '',
          signatureType: '1',
          remark: '',
          imgUrl: '',
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: '该输入项为必填项!', trigger: 'blur' },
            {
              min: 1,
              max: 20,
              message: '长度在 1 到 20 个字符',
              trigger: 'blur',
            },
          ],
          signatureType: [
            { required: true, message: '请选择签名类型', trigger: 'change' },
          ],
          imgUrl: [
            { required: true, message: '请选择上传图片', trigger: 'change' },
          ],
        },
        signature: '', //签名
        signatureId: '', //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imagesshow: false,
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        username: '',
        title: '',
        auditStatus: '1',
        time1: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        username: '',
        title: '',
        auditStatus: '1',
        time1: [],
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
      isFirstEnter: false,
    }
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumertaskmms/auditPage',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    Query() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      this.sensitiveCondition.auditStartTime = ''
      this.sensitiveCondition.auditEndTime = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
      this.getTableDtate()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
      this.getTableDtate()
    },
    // 素材下载------------------------------
    downloadView(val) {
      this.downloadAjax(window.path.imgU + 'group1/' + val)
    },
    ajax(url, callback, options) {
      window.URL = window.URL || window.webkitURL
      var xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      if (options.responseType) {
        xhr.responseType = options.responseType
      }
      xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
          callback(xhr)
        }
      }
      xhr.send()
    },
    downloadAjax(urls) {
      let innerurl = urls // 文件地址
      var name = urls.replace(/(.*\/)*([^.]+).*/gi, '$2')
      this.ajax(
        innerurl,
        function (xhr) {
          let filename = name + '.' + innerurl.replace(/(.*\.)/, '')
          let content = xhr.response
          let a = document.createElement('a')
          let blob = new Blob([xhr.response])
          let url = window.URL.createObjectURL(blob)
          a.href = url
          a.download = filename
          a.click()
          window.URL.revokeObjectURL(url)
        },
        {
          responseType: 'blob',
        }
      )
    },
    // 素材下载------------------------------

    // 下载
    download(val) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          'M+': this.getMonth() + 1,
          'd+': this.getDate(),
          'h+': this.getHours(),
          'm+': this.getMinutes(),
          's+': this.getSeconds(),
          'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        }
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + '').substr(4 - RegExp.$1.length)
          )
        for (var i in args) {
          var n = args[i]
          if (new RegExp('(' + i + ')').test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
            )
        }
        return format
      }
      var that = this
      filedownload()
      function filedownload() {
        fetch(
          that.API.cpus +
            'v3/file/download?fileName=' +
            val.fileOriginalName +
            '&group=group1&path=' +
            val.filePath.slice(7),
          {
            method: 'get',
            headers: {
              'Content-Type': 'application/json',
              Authorization:
                'Bearer ' + window.common.getCookie('ZTADMIN_TOKEN'),
            },
            // body: JSON.stringify({
            //     batchNo: val.row.batchNo,
            // })
          }
        )
          .then((res) => res.blob())
          .then((data) => {
            let blobUrl = window.URL.createObjectURL(data)
            download(blobUrl)
          })
      }
      function download(blobUrl) {
        var a = document.createElement('a')
        a.style.display = 'none'
        a.download =
          '(' +
          new Date().format('YYYY-MM-DD HH:mm:ss') +
          ') ' +
          val.fileOriginalName
        a.href = blobUrl
        a.click()
      }
    },
    //通过
    handleAdopt(index, row) {
      this.$confirms.confirmation(
        'post',
        '确认当条审核？',
        window.path.omcs + 'consumertaskmms/audit',
        { ids: row.id, auditStatus: 2 },
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //批量通过
    sensitiveBatchDel() {
      let status = true
      //判断选项中是否有审核过的状态
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != '1') {
          status = false
          break
        }
      }
      if (status) {
        this.$confirms.confirmation(
          'post',
          '确认当条审核?',
          window.path.omcs + 'consumertaskmms/audit',
          { ids: this.remarkObj.formData.ids.join(','), auditStatus: 2 },
          (res) => {
            this.getTableDtate()
            this.remarkObj.formData.ids = []
          }
        )
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //驳回
    handleReject(index, row) {
      this.remarkshow = true
      this.title = '驳回备注'
      this.remarkObj.formData.ids.push(row.id) //赋值ID
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let obj = {}
          obj.auditReason = this.remarkObj.formData.auditReason
          obj.ids = this.remarkObj.formData.ids.join(',')
          obj.auditStatus = 3
          this.$confirms.confirmation(
            'post',
            '确认当条驳回?',
            window.path.omcs + 'consumertaskmms/audit',
            obj,
            (res) => {
              this.remarkshow = false
              this.getTableDtate()
            }
          )
        } else {
          return false
        }
      })
    },
    //批量驳回
    sensitiveBatchSet() {
      let status = true
      //判断选项中是否有审核过的状态
      this.title = '批量驳回备注'
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != '1') {
          status = false
          break
        }
      }
      if (status) {
        this.remarkshow = true
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let auditStatus = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].id)
        //批量操作列表选中项的审核状态
        auditStatus.push(val[i].auditStatus)
      }
      this.auditStatus = auditStatus //选中项的审核状态
      this.remarkObj.formData.ids = selectId //批量通过，不通过的id数组
    },
    // 预览
    View(val) {
      this.indexVeew = 1
      this.viewData = val.contents
      this.title = val.title
      this.dialogVisible = true
    },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
    // this.getTableDtate();
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //监听弹框是否为操作正在进行中（禁止修改审核原因）
    operationFlag(val) {
      if (val == false) {
        this.disabledinput = true
      } else {
        this.disabledinput = false
      }
    },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.operationFlag = true //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.ids = [] //置空ID
          this.getTableDtate()
        }
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}

.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>
