<template>
  <div>
    <el-table
      v-loading="basicInfoTable.loading2"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.6)"
      ref="multipleTable"
      border
      :data="basicInfoTable.tableData"
      style="width: 100%"
    >
      <!-- <el-table-column
                            type="selection"
                            >
                            </el-table-column> -->
      <el-table-column label="用户名称" width="150px">
        <template v-slot="scope">
          <span>
            {{ scope.row.consumerName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" width="150px">
        <template v-slot="scope">
          <span>
            {{ scope.row.compName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="用户状态" width="150px">
        <template v-slot="scope">
          <span v-if="scope.row.compName == 1">
            <!-- {{ scope.row.compName}} -->
            停用
          </span>
          <span v-else>启动</span>
        </template>
      </el-table-column>
      <el-table-column label="用户类型" width="150px">
        <template v-slot="scope">
          <span v-if="scope.row.roleId == 12">
            <!-- {{ scope.row.compName}} -->
            管理商
          </span>
          <span v-else-if="scope.row.roleId == 14">终端用户</span>
          <span v-else-if="scope.row.roleId == 22">线上终端</span>
          <span v-else>子用户</span>
        </template>
      </el-table-column>
      <el-table-column label="行业" width="150px">
        <template v-slot="scope">
          <span>
            {{ scope.row.industryName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="150px">
        <template v-slot="scope">
          <span>
            {{ scope.row.createName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="150px">
        <template v-slot="scope">
          <span>
            {{ scope.row.createTime }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <!-- <table-tem  class="basicInfo" :tableDataObj="basicInfoTable" @handelOptionButton="handelOptionButton"></table-tem> -->
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
export default {
  components: {
    TableTem,
  },
  props: ['username'],
  data() {
    return {
      jsonData: {},
      basicInfoTable: {
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'consumerName',
            showName: '用户名称',
            fixed: false,
            width: '140',
            showColorTag: {
              color: '#16A589',
            },
          },
          // {
          //         prop:'phone',
          //         showName:'手机号',
          //         width:'140',
          //         fixed:false,
          // },
          {
            prop: 'compName',
            showName: '公司名称',
            fixed: false,
            width: '140',
          },
          {
            prop: 'consumerStatus',
            showName: '用户状态',
            fixed: false,
            formatData: () => {
              return this.jsonData.consumerStatus == 1 ? '停用' : '启用'
            },
          },
          {
            prop: 'roleId',
            showName: '用户类型',
            fixed: false,
            formatData: function (val) {
              let type = ''
              if (val == 12) {
                return (type = '管理商')
              } else if (val == 14) {
                return (type = '终端用户')
              } else if (val == 22) {
                return (type = '线上终端')
              } else {
                return (type = '子用户')
              }
              return type
            },
          },
          {
            prop: 'industryName',
            showName: '行业',
            fixed: false,
            width: '140',
          },
          { prop: 'createName', showName: '创建人', fixed: false },
          {
            prop: 'createTime',
            showName: '创建时间',
            fixed: false,
            width: '140',
          },
          // {prop:'email', showName:'邮箱', fixed:false,}
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '126', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        // tableOptions:[
        // ]
      },
    }
  },
  created() {
    this.getBasicData()
  },
  methods: {
    /**---------数据表格（基本信息）------ */
    getBasicData() {
      let formDatas = {}
      window.api.get(
        window.path.omcs + 'operatinguser/detail/' + this.username,
        formDatas,
        (res) => {
          console.log(res)
          // let jsonData=JSON.parse(res.data)[0];
          this.jsonData = res.data
          // console.log();
          this.parentId = res.data.clientId
          // console.log(this.parentId);
          // console.log(this.jsonData);
          // console.log(this.roleId);
          //基本信息表格数据
          this.basicInfoTable.tableData = [res.data]
          // this.corporateName = res.data.compName;

          // if(this.roleId == 12){
          //     // console.log(11);
          //     this.basicInfoTable.tableOptions=[];
          //     window.api.get(window.path.omcs + 'operatinguser/consumerClientCount?clientId='+res.data.userId,{},res=>{
          //         // if(res.code == 200){
          //         //     console.log(111111);
          //         // }
          //         // console.log(1111111);

          //         let option={
          //             optionName:'子用户( '+ res.data +' )',
          //             type:'',
          //             size:'mini',
          //             optionMethod:'getSubPage',
          //             icon:'el-icon-success'
          //         }
          //         this.basicInfoTable.tableOptions.push(option);
          //         // console.log(this.basicInfoTable.tableOptions);
          //     })

          // }else {
          //     // console.log(111);
          //     this.basicInfoTable.tableOptions=[];
          //     // delete this.basicInfoTable.tableOptions;
          // }
          // this.getRecentData()
        }
      )
    },
    handelOptionButton(val) {
      if (val.methods == 'getSubPage') {
        this.$router.push({
          path: 'ManagerSub',
          query: { Id: this.parentId, ids: this.clientId },
        })
      }
    },
  },
}
</script>
