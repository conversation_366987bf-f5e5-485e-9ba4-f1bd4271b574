import * as types from './mutation-types'

export default {
  [types.TEST](state, n) {
    // 变更状态
    state.count += n
  },
  [types.SET_USERNAME](state, msg) {
    state.userName = msg
  },
  [types.SET_USERID](state, msg) {
    state.userId = msg
  },
  [types.SET_ROLEID](state, msg) {
    state.roleId = msg
  },
  [types.SET_LOGURL](state, msg) {
    state.logUrl = msg
  },
  [types.SET_PORTRAITURL](state, msg) {
    state.portraitUrl = msg
  },
  [types.SET_REALNAME](state, msg) {
    // console.log(msg);
    state.realName = msg
  },
}
