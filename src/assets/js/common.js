//获取cookie
import axios from "axios"
import getNoce from '../../plugins/getNoce'
function getCookie(name) {
  var strcookie = document.cookie //获取cookie字符串
  var arrcookie = strcookie.split('; ') //分割
  //遍历匹配
  for (var i = 0; i < arrcookie.length; i++) {
    var arr = arrcookie[i].split('=')
    if (arr[0] == name) {
      return arr[1]
    }
  }
  return ''
}
function formatDate(val) {
  var date = new Date(val) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  var Y = date.getFullYear() + '-'
  var M =
    (date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1) + '-'
  var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
  var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
  var m =
    (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
  var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return Y + M + D + h + m + s
}
// 获取主域名
function getDomain() {
  var hostname = window.location.hostname
  var ip = hostname.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g)
  if (ip) {
    return ip
  }
  var domain = hostname.match(
    /([a-z0-9][a-z0-9\-]*?\.(?:com|cn|net|org|gov|info|la|cc|co|jp)(?:\.(?:cn|jp))?)$/
  )
  if (domain) {
    return domain[0]
  }
  return hostname
}
function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    console.warn('未传入正确的时间格式：new Date()!')
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/')
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
}
function hasIframe(htmlContent) {
  const iframeRegex = /<iframe.*?>/
  return iframeRegex.test(htmlContent)
}
function getMenuList() {
  if (localStorage.getItem('menu')) {
    let obj = JSON.parse(localStorage.getItem('menu'))
    return obj.name
  }
}
async function fetchSmsMessage(smsInfoId) {
  const nonce = await getNoce.useNonce();
  axios.defaults.headers.common['Authorization'] = 'Bearer ' + getCookie('ZTADMIN_TOKEN');
  axios.defaults.headers.common['Once'] = nonce;
  axios.defaults.baseURL = import.meta.env.VITE_APP_URL
  return axios.post('/gateway/operator-omcs/smsMessage/decryptContent', { smsInfoId }).then(res => {
    return res.data
  }).catch(error => {
    console.error('请求失败:', error);
    throw error; // 抛出错误
  });
}
function deleteList(arr, id) {
  arr = arr.filter(item => item.id !== id);
  return arr;
}
function batcnDeleteList(data, ids) {
  // return data.filter(item => {
  //   return !ids.includes();
  // });
  return data.filter(item => !ids.includes(String(item.id)));
  
}
export default {
  getCookie: function (name) {
    return getCookie(name)
  },
  formatDate: function (val) {
    return formatDate(val)
  },
  getDomain: function () {
    return getDomain()
  },
  parseTime: function (time, pattern) {
    return parseTime(time, pattern)
  },
  hasIframe: function (htmlContent) {
    return hasIframe(htmlContent)
  },
  getMenuList: function () {
    return getMenuList()
  },
  fetchSmsMessage: function (smsInfoId) {
    return fetchSmsMessage(smsInfoId)
  },
  deleteList: function (arr, id) {
    return deleteList(arr, id)
  },
  batcnDeleteList: function (arr, ids) {
    return batcnDeleteList(arr, ids)
  }
}
