<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="queryForm"
        >
          <el-form-item label="用户名" prop="consumerName">
            <el-input
              v-model="formInline.consumerName"
              @keyup.enter="Query()"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="80px" label="公司名" prop="compName">
            <el-input
              v-model="formInline.compName"
              @keyup.enter="Query()"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="80px" label="认证状态" prop="certificate">
            <el-select
              v-model="formInline.certificate"
              @keyup.enter="Query()"
              placeholder=""
              class="input-w"
            >
              <el-option label="未认证" value="0"></el-option>
              <el-option label="认证中" value="1"></el-option>
              <el-option label="已认证" value="2"></el-option>
              <!-- <el-option label="认证中" value="1"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item label-width="80px" label="注册时间" prop="time">
            <el-date-picker
              class="input-time"
              v-model="formInline.time"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handeTime"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style="" @click="Query"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              style=""
              @click="Reload('queryForm')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <!-- <div class="boderbottom" style="margin:10px 0">

                </div> -->
      <div class="Mail-table Mail-table1">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="marketShopUserList"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="用户名称" title="用户名称">
            <!-- 点击用户名 -->
            <template v-slot="scope">
              <div style="height: 45px; line-height: 45px">
                <div>
                  {{ scope.row.consumerName }}
                </div>
                <!-- <div>
                                        <span>{{ scope.row.remark }}</span>
                                    </div> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="公司名" title="公司名">
            <template v-slot="scope">
              <div>{{ scope.row.compName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="手机号" title="手机号">
            <template #default="{ row, rowIndex }">
              <div
                v-if="rowIndex != count || phone == ''"
                @click="linkTo(row,rowIndex)"
                style="color: #16a589; cursor: pointer"
                >查看手机号</div
              >
              <div v-else>{{ phone }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="余额" title="余额" min-width="120">
                            <template #default="scope">
                                <div style="display:flex">
                                    <div v-for="(item, index) in scope.row.balanceList" :key="index" @click="more(scope.row)">
                                        <el-tag
            :disable-transitions="true" v-if="item.name == '短信'"
                                            :style="'margin: 5px 0px 5px 10px;cursor: pointer;color:' + tagColor[index]">{{
                                                item.name
                                            }} : {{ item.num }} {{
        item.status == 0 ? "（开启）" : item.status == 1 ? "（用户关闭）" : "（运营关闭）" }}</el-tag>
                                    </div>
                                </div>
                            </template>
                        </vxe-column> -->
          <vxe-column field="认证状态" title="认证状态" width="200">
            <template v-slot="scope">
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.certificate == 0"
                type="info"
                effect="dark"
              >
                未认证
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.certificate == 1"
                type="warning"
                effect="dark"
              >
                认证中
              </el-tag>
              <el-tag
                :disable-transitions="true"
                v-if="scope.row.certificate == 2"
                type="success"
                effect="dark"
              >
                已认证
              </el-tag>

              <!-- <div v-if="scope.row.roleId == 22">
                                    <div>线上终端</div>
                                    <span style="color:red" v-if="scope.row.certificate == 0">(未认证)</span>
                                    <span style="color:red" v-if="scope.row.certificate == 1">(认证中)</span>
                                    <span style="color:#16a589" v-if="scope.row.certificate == 2">(已认证)</span>
                                </div> -->
            </template>
          </vxe-column>
          <!-- <vxe-column prop="createName" field="创建人" title="创建人">
                        </vxe-column> -->
          <vxe-column field="注册时间" title="注册时间">
            <template v-slot="scope">
              <!-- <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</p>
                                <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</p> -->
              <div>{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源">
            <template v-slot="scope">
              <div>{{ scope.row.source }}</div>
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0"
          > -->
        
        <div class="paginationBox">
          <el-pagination
            style="float: right"
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="searchInput.currentPage"
            :page-size="searchInput.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalSize"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <el-dialog
      :title="usreTitle"
      v-model="moreFlag"
      custom-class="way"
      width="30%"
    >
      <div>
        <div style="font-size: 18px; color: #000; margin: 10px">余额</div>
        <div v-for="(item, index) in balanceLists" :key="index">
          <el-tag
            :disable-transitions="true" :style="'margin: 5px 0px 5px 10px;color:' + tagColor[index]"
            >{{ item.name }} : {{ item.num }}
            {{
              item.status == 0
                ? '（开启）'
                : item.status == 1
                ? '（用户关闭）'
                : '（运营关闭）'
            }}</el-tag
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import { mapState, mapMutations, mapActions } from 'vuex'
import { ElMessage, ElMessageBox } from "element-plus";
import moment from 'moment'
// 引入时间戳转换
// import {formatDate} from '@/assets/js/date.js'
// var axios = require('axios')
import axios from 'axios'
export default {
  components: {
    TableTem,
    FileUpload
  },
  name: 'UserMag',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      tagColor: [
        '#67c23a',
        '#e6a23c',
        '#ff0000b3',
        '#909399',
        '#3c61e6d1',
        '#3cafe6bf',
        '#16A589',
        '#303133',
      ],
      checkuserClientId: 0,
      moreFlag: false,
      usreTitle: '',
      phone: [],
      count: null,

      balanceLists: [],
      // sales:[],//销售
      salesName: [], //销售
      formInline: {
        roleId: '22',
        consumerName: '',
        compName: '',
        consumerStatus: '',
        // sales:'',
        salesName: '',
        createName: '',
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD'),
          moment().format('YYYY-MM-DD'),
        ],
        createBeginTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
        createEndTime: moment().format('YYYY-MM-DD'),
        certificate: '0',
        pageSize: 10,
        currentPage: 1,
      },
      searchInput: {
        roleId: '22',
        consumerName: '',
        compName: '',
        consumerStatus: '',
        // sales:'',
        salesName: '',
        createName: '',
        time: [
          moment().subtract(7, 'days').format('YYYY-MM-DD'),
          moment().format('YYYY-MM-DD'),
        ],
        createBeginTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
        createEndTime: moment().format('YYYY-MM-DD'),
        certificate: '0',
        pageSize: 10,
        currentPage: 1,
      },
      totalSize: 0, //总条数
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  watch: {},
  methods: {
    more(row) {
      // console.log(row,'row');
      this.moreFlag = true
      this.balanceLists = row.balanceList
      this.usreTitle = row.consumerName
    },
    getTableData() {
      //获取列表数据
      this.phone = []
      let formDatas = this.searchInput
      this.tableDataObj.loading2 = true
      window.api.post(window.path.omcs + 'operatinguser/page', formDatas, (res) => {
        // console.log(res)
        this.tableDataObj.tableData = res.records
        // this.tableDataObj.tableData.forEach(item=>{
        //     item.phone = null
        // })
        this.totalSize = res.total
        this.tableDataObj.loading2 = false
      })
    },
    Query() {
      //查询
      for (let key in this.formInline) {
        this.searchInput[key] = this.formInline[key]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
    },
    Reload(formName) {
      //重置
      this.formInline.createEndTime = moment().format('YYYY-MM-DD')
      this.formInline.createBeginTime = moment()
        .subtract(7, 'days')
        .format('YYYY-MM-DD')
      this.formInline.cooperateStatus = ''
      this.$refs[formName].resetFields() //清空查询表单
      for (let k in this.formInline) {
        this.searchInput[k] = this.formInline[k]
      }
      this.searchInput.pageSize = 10
      this.searchInput.currentPage = 1
      this.getTableData()
    },
    handeTime: function (val) {
      //获取查询时间框的值
      // console.log(val)
      if (val) {
        this.formInline.createBeginTime = this.moment(val[0]).format(
          'YYYY-MM-DD '
        )
        this.formInline.createEndTime = this.moment(val[1]).format(
          'YYYY-MM-DD '
        )
      } else {
        this.formInline.createBeginTime = ''
        this.formInline.createEndTime = ''
      }
    },

    //-----翻页操作
    handleSizeChange(size) {
      this.searchInput.pageSize = size
      this.getTableData()
    },
    handleCurrentChange: function (currentPage) {
      this.searchInput.currentPage = currentPage
      this.getTableData()
    },
    linkTo(row, index) {
      // console.log(row);
      window.api.get(
        window.path.omcs + 'operatinguser/userPhone/' + row.userId,
        {},
        (res) => {
          this.count = index
          // this.tableDataObj.tableData.forEach((item,i)=>{
          //     if(i == index) item.phone = res.data

          // })
          if(res.code == 200){
            if(res.data){
              this.phone = res.data
            }else{
              this.phone = res.data
              ElMessage.error('该用户没有绑定手机号');
            }
          }else{
            ElMessage.error(res.msg);
          }
          
          // row.phone = res.data
        }
      )
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableData()
    })

    // this.getSales()
    // this.getChannel()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableData()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style scoped>
.selectedBox {
  width: 85px;
  text-align: center;
  height: 35px;
  line-height: 35px;
  border: 1px solid #1abc9c;
  position: relative;
}
.selectedBox:after {
  border-style: solid;
  right: 0;
  top: 0;
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border-width: 13px 0 0 13px;
  border-color: #1abc9c transparent transparent transparent;
}
.input-w-2 {
  width: 80% !important;
}
.red {
  color: red;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.mr-3 {
  margin-right: 3px;
}
.addUserDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}
</style>

<style>
.el-loading-spinner i {
  margin-top: -10px;
  margin-bottom: 10px;
  font-size: 30px;
}

.pro-style .el-checkbox {
  margin-right: 0 !important;
}

.Mail-table1 .el-table--small td {
  padding: 2px 0 !important;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
