<template>
  <div>
    <div class="Top_title">
      <span
        style="
          display: inline-block;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><el-icon><el-icon-arrow-left /></el-icon> 返回</span
      >|
      <span>空号检测详情表</span>
    </div>
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="sensiTag"
        class="demo-form-inline"
        label-width="82px"
        ref="sensiTag"
      >
        <el-form-item label="手机号码" label-width="82px" prop="phone">
          <el-input
            v-model="sensiTag.phone"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <div class="boderbottom" style="padding-left: 10px">
          <el-button type="primary" plain @click="queryForm()">查询</el-button>
          <el-button type="primary" plain @click="resetForm('sensiTag')"
            >重置</el-button
          >
        </div>
      </el-form>

      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header">空号检测详情列表</span> -->
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <table-tem :tableDataObj="tableDataObj"></table-tem>
      </div>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
export default {
  components: {
    TableTem,
    ElIconArrowLeft,
  },
  name: 'detailsList',
  data() {
    return {
      sensiTag: {
        //查询框的值
        phone: '',
        batchNo: '',
        userId: '',
      },
      tableDataObj: {
        //列表数据
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        loading2: false,
        tableData: [],
        tableLabel: [
          //列表表头
          { prop: 'checkTime', showName: '检测时间', fixed: false },
          { prop: 'phone', showName: '手机号码', fixed: false },
          { prop: 'status', showName: '返回值', fixed: false },
          {
            prop: 'status',
            showName: '状态备注',
            fixed: false,
            formatData: function (val) {
              if (val == 1) {
                return (val = '在网正常')
              } else if (val == 0) {
                return (val = '未知状态')
              } else if (val == 2) {
                return (val = '在网异常（关机、停机、不在服务区）')
              } else if (val == 3) {
                return (val = '不在网异常（空号,暂停服务，过期）')
              } else if (val == 4) {
                return (val = '反欺诈')
              }
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
    }
  },
  methods: {
    goBack() {
      this.$router.push({ name: 'nullNumDetectionReport' })
    },
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingStatisticalAnalysis/mobCheck/detailPage',
        this.sensiTag,
        (res) => {
          this.tableDataObj.tableData = [res.data]
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    queryForm() {
      this.getTableDtate()
    },
    //重置
    resetForm(formName) {
      this.sensiTag.phone = ''
      this.tableDataObj.tableData = []
    },
  },
  mounted() {
    this.sensiTag.batchNo = this.$route.query.num
    this.sensiTag.userId = this.$route.query.id
    this.getTableDtate()
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>
