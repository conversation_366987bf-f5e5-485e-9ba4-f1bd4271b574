<template>
  <div class="bag" style="background: #fff; padding: 10px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><el-page-header
            style="color: #16a589"
            @back="goBack"
            :content="username ? `5G注册--${username}` : '5G注册'"
          >
          </el-page-header
        ></el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <!-- <div style="padding: 0 20px">
      <el-button type="primary" @click="refresh"
        ><el-icon><RefreshRight /></el-icon>刷新</el-button
      >
      <el-button type="primary" @click="addChannel">添加通道</el-button>
    </div> -->
    <div style="padding: 20px">
      <!-- <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        stripe
        :data="tableDataObj.tableData"
      > -->

      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
          <el-button type="primary" @click="refresh"
            ><el-icon><RefreshRight /></el-icon>刷新</el-button
          >
          <el-button type="primary" @click="addChannel">添加通道</el-button>
        </template>
      </vxe-toolbar>

      <vxe-table
        ref="tableRef"
        id="chatbotChannel"
        border
        stripe
        :custom-config="customConfig"
        :column-config="{ resizable: true }"
        :row-config="{ isHover: true }"
        min-height="1"
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        style="font-size: 12px;"
        :data="tableDataObj.tableData">

        <vxe-column field="chatbot名称" title="chatbot名称" width="120">
          <template v-slot="scope">
            <div>
              {{ scope.row.chatbotName }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="chatbotID" title="chatbotID" width="180">
          <template v-slot="scope">
            <div>
              {{ scope.row.chatbotId }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="chatbot编号" title="chatbot编号" width="200">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.chatbotNum"
              :content="scope.row.chatbotNum"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
          </template>

          <!-- <template #default="scope">{{ scope.row.chatbotNum }}</template> -->
        </vxe-column>
        <vxe-column field="通道名称" title="通道名称" width="120">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.channelName"
              :content="scope.row.channelName"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- {{ scope.row.channelName }} -->
          </template>
        </vxe-column>
        <vxe-column field="通道编码" title="通道编码" width="80">
          <template v-slot="scope">
            <div>
              {{ scope.row.channelId }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="通道运营商" title="通道运营商" width="120">
          <template v-slot="scope">
            <div>
              <div
                v-if="scope.row.channelOperator == 1"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-yidong"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">移动</span>
              </div>
              <div
                v-else-if="scope.row.channelOperator == 2"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-liantong"
                  style="font-size: 16px; color: #f56c6c"
                ></i>
                <span style="margin-left: 5px">联通</span>
              </div>
              <div
                v-else-if="scope.row.channelOperator == 3"
                style="display: flex; align-items: center"
              >
                <i
                  class="iconfont icon-dianxin"
                  style="font-size: 16px; color: #409eff"
                ></i>
                <span style="margin-left: 5px">电信</span>
              </div>
              <div v-else></div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="短信端口号" title="短信端口号" width="180">
          <template v-slot="scope">
            <div>
              {{ scope.row.smsPort }}
            </div>
          </template>
        </vxe-column>
        <vxe-column field="流速" title="流速" width="80">
          <template v-slot="scope">
            <div>
              <span v-if="scope.row.concurrent">{{ scope.row.concurrent }}</span>
              <span v-else>-&nbsp;-</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="端口服务类型" title="端口服务类型" width="140">
          <template v-slot="scope">
            <div>
              <span v-if="scope.row.portServiceType == '1'">行业</span>
              <span v-else-if="scope.row.portServiceType == '2'">营销</span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="状态" title="状态" width="120">
          <template v-slot="scope">
            <div v-if="scope.row.updateStatus == 0">
              <span v-if="scope.row.status == 0">待报备</span>
              <span style="color: #f56c6c" v-else-if="scope.row.status == 10"
                >新增审核中</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == 15"
                >新增失败</span
              >
              <span style="color: #16a589" v-else-if="scope.row.status == 20"
                >新增成功</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == 22"
                >白名单审核中</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == 24"
                >白名单审核不通过</span
              >
              <span style="color: #16a589" v-else-if="scope.row.status == 26"
                >调试</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == 28"
                >上线审核中</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == 30"
                >上线不通过</span
              >
              <span style="color: #16a589" v-else-if="scope.row.status == 32"
                >上线</span
              >

              <span style="color: #ff0000" v-else-if="scope.row.status == 40"
                >停用</span
              >
              <span style="color: #f56c6c" v-else-if="scope.row.status == -1"
                >注销中</span
              >
              <span style="color: #ff0000" v-else-if="scope.row.status == -2"
                >注销失败</span
              >
              <span style="color: #16a589" v-else-if="scope.row.status == -3"
                >已注销</span
              >
            </div>
            <div v-else-if="scope.row.updateStatus != 0">
              <span style="color: #16a589" v-if="scope.row.updateStatus == 1"
                >变更中</span
              >
              <span
                style="color: #ff0000"
                v-else-if="scope.row.updateStatus == 2"
                >变更失败</span
              >
            </div>
          </template>
        </vxe-column>
        <vxe-column field="同步配置" title="同步配置" width="120">
          <template v-slot="scope">
            <el-icon v-if="scope.row.syncConfig == '1'" style="color: #67c23a; font-size: 22px"><SuccessFilled /></el-icon>
            <el-icon v-else style="color: red; font-size: 22px"><CircleCloseFilled /></el-icon>
            <!-- <span v-if="scope.row.syncConfig == '1'">是</span>
                <span v-else>否</span> -->
          </template>
        </vxe-column>
        <vxe-column field="缓存签名" title="缓存签名" width="120">
          <template v-slot="scope">
            <el-icon v-if="scope.row.cacheSign == '1'" style="color: #67c23a; font-size: 22px"><SuccessFilled /></el-icon>
            <el-icon v-else style="color: red; font-size: 22px"><CircleCloseFilled /></el-icon>
          </template>
        </vxe-column>
        <!-- <vxe-column field="" title="阅信单点登录" width="150">
              <template #default="scope">
                <a v-if="scope.row.ssoUrl" :href="scope.row.ssoUrl" target="_blank">创建阅信</a>
              </template>
            </vxe-column> -->

        <vxe-column field="原因" title="原因" width="150">
          <template v-slot="scope">
            <Tooltip
              v-if="scope.row.reason"
              :content="scope.row.reason"
              className="wrapper-text"
              effect="light"
            >
            </Tooltip>
            <!-- {{ scope.row.reason }} -->
          </template>
        </vxe-column>
        <vxe-column field="创建时间" title="创建时间" width="170">
          <template v-slot="scope">
            <div>{{
              moment(scope.row.createdTime).format('YYYY-MM-DD HH:mm:ss')
            }}</div>
          </template>
        </vxe-column>
        <vxe-column field="操作" title="操作" width="120" fixed="right">
          <template v-slot="scope">
              <el-button
                v-if="scope.row.status == 0 || scope.row.status == 15"
                @click="handleEdit(scope.row)"
                link
                style="margin-left: 10px; color: #409eff;"
                >编辑</el-button
              >
              <el-button
                v-if="scope.row.status == 0 || scope.row.status == 15"
                @click="declareChannel(scope.row)"
                link
                style="margin-left: 10px; color: #409eff;"
                >报备</el-button
              >
              <el-button
                v-if="
                  (scope.row.status == 20 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 24 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 26 && scope.row.updateStatus == 0)
                "
                @click="serviceCode(scope.row)"
                link
                style="margin-left: 10px; color: #409eff;"
                >白名单调试</el-button
              >
              <el-button
                v-if="
                  (scope.row.status == 26 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 30 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 40 && scope.row.updateStatus == 0)
                "
                @click="online(scope.row)"
                link
                style="margin-left: 10px; color: #409eff;"
                >申请上架</el-button
              >

              <el-button
                v-if="
                  (scope.row.status == 26 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 30 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 32 && scope.row.updateStatus == 0) ||
                  scope.row.updateStatus == 2
                "
                @click="handupload(scope.row)"
                link
                style="margin-left: 10px; color: #409eff;"
                >更新</el-button
              >
              <el-button
                v-if="
                  (scope.row.status == 26 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 32 && scope.row.updateStatus == 0) ||
                  (scope.row.status == 40 && scope.row.updateStatus == 0)
                "
                link
                style="color: #e6a23c; margin-left: 10px"
                @click="logout(scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon> 注销</el-button
              >
              <el-button
                v-if="scope.row.status == 40"
                link
              style="margin-left: 10px; color: #409eff;"
                @click="hangdelAgainOnline(scope.row, '1')"
                >启用</el-button
              >
              <el-button
                v-if="scope.row.status == 32"
                link
                style="color: red; margin-left: 10px"
                @click="hangdelAgainOnline(scope.row, '0')"
                >停用</el-button
              >
              <el-button
                v-if="
                  scope.row.status == 0 ||
                  scope.row.status == 15 ||
                  scope.row.status == -2 ||
                  scope.row.status == -3
                "
                link
                style="color: red; margin-left: 10px"
                @click="bindDelete(scope.row)"
                >删除</el-button
              >
              <el-button
                v-if="scope.row.status == 32"
                link
                style="color: #67c23a; margin-left: 10px"
                @click="copyChatbot(scope.row)"
                >复制</el-button
              >
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <el-dialog
      :title="addTitle"
      v-model="dialogVisible"
      width="70%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <!-- <span>这是一段信息</span> -->
      <el-form
        :model="formInline"
        :rules="rules"
        :inline="true"
        label-width="150px"
        ref="formInline"
        class="demo-form-inline"
      >
        <el-form-item v-if="!formInline.id" label="通道" prop="channelId">
          <el-select
            class="input-w"
            :disabled="formInline.id ? true : false"
            v-model="formInline.channelId"
            @change="handChannel"
            placeholder="选择通道"
          >
            <el-option
              v-for="item in channel"
              :key="item.channelId"
              :label="item.channelName"
              :value="item.channelId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="通道" prop="channelName">
          <el-input
            class="input-w"
            disabled
            v-model="formInline.channelName"
          ></el-input>
        </el-form-item>
        <div>
          <el-form-item
            v-if="formInline.channelOperator != ''"
            label="行业类型编码："
            prop="category"
          >
            <el-select
              v-if="formInline.channelOperator == '1'"
              style="width: 220px"
              filterable
              clearable
              class="input-w"
              v-model="formInline.category"
              @change="handleCategory"
              placeholder="请选择行业类别"
            >
              <el-option
                v-for="(item, index) in industrys"
                :label="item.industryName"
                :value="item.industryName"
                :key="index"
              ></el-option>
            </el-select>
            <el-select
              v-if="formInline.channelOperator == '2'"
              style="width: 220px"
              filterable
              clearable
              class="input-w"
              v-model="formInline.category"
              placeholder="请选择行业类别"
            >
              <el-option
                v-for="(item, index) in ltOption"
                :label="item.lable"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
            <el-select
              v-if="formInline.channelOperator == '3'"
              style="width: 220px"
              class="input-w"
              filterable
              clearable
              v-model="formInline.category"
              placeholder="请选择行业类别"
            >
              <el-option
                v-for="(item, index) in dxOption"
                :label="item.lable"
                :value="item.value"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="formInline.channelOperator == '1'"
            label="实际下发行业"
            prop="actualIndustry"
          >
            <el-select
              v-if="formInline.channelOperator == '1'"
              style="width: 220px"
              filterable
              clearable
              class="input-w"
              v-model="formInline.actualIndustry"
              placeholder="请选择行业类别"
            >
              <el-option label="党政军" value="1"> </el-option>
              <el-option label="民生" value="2"> </el-option>
              <el-option label="金融" value="3"> </el-option>
              <el-option label="物流" value="4"> </el-option>
              <el-option label="游戏" value="5"> </el-option>
              <el-option label="电商" value="6"> </el-option>
              <el-option label="微商（个人）" value="7"> </el-option>
              <el-option label="沿街商铺（中小）" value="8"> </el-option>
              <el-option label="企业（大型）" value="9"> </el-option>
              <el-option label="教育培训" value="10"> </el-option>
              <el-option label="房地产" value="11"> </el-option>
              <el-option label="医疗器械、药店" value="12"> </el-option>
              <el-option label="其他" value="13"> </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item v-if="formInline.channelOperator == '1'" label="端口服务类型" prop="portServiceType">
                <el-select style="width: 220px" clearable filterable class="input-w" v-model="formInline.portServiceType"
                  placeholder="请选择端口服务类别">
                  <el-option v-for="(item, index) in portTypeList" :label="item.name" :value="item.porType"
                    :key="index"></el-option>
                </el-select>
              </el-form-item> -->
          <el-form-item
            v-if="formInline.channelOperator == '1'"
            label="广告营销行业类型"
            prop="adMarketTndustryType"
          >
            <el-select
              style="width: 220px"
              filterable
              clearable
              class="input-w"
              v-model="formInline.adMarketTndustryType"
              placeholder="请选择广告营销行业类型"
            >
              <el-option
                v-for="(item, index) in adMarketingIndustryType"
                :label="item.name"
                :value="item.industryType"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="formInline.channelOperator == '1' && formInline.id"
            label="扩展号"
            prop="ext"
          >
            <el-input
              class="input-w"
              :maxlength="4"
              show-word-limit
              placeholder="支持4位数"
              v-model="formInline.ext"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="formInline.channelOperator == '2' && formInline.id"
            label="扩展号"
            prop="ext"
          >
            <el-input
              :disabled="status == 0 || status == 15 ? true : false"
              class="input-w"
              :maxlength="6"
              show-word-limit
              placeholder="最大6位数"
              v-model="formInline.ext"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="白名单" prop="whiteList">
            <el-input
              style="width: 400px; height: 80px"
              maxlength="239"
              show-word-limit
              type="textarea"
              v-model="formInline.whiteList"
              placeholder="填写白名单"
            ></el-input>
          </el-form-item>
        </div>

        <div style="display: flex; justify-content: space-between">
          <div></div>
          <div style="padding: 0 10px">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitChannel('formInline')"
              >提交</el-button
            >
          </div>
        </div>
        <div class="solid"></div>
        <div>基本信息</div>
        <el-form-item label="用户名称：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.username"
          ></el-input>
        </el-form-item>
        <el-form-item label="ChatBot名称：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.chatbotName"
          ></el-input>
        </el-form-item>
        <el-form-item label="chatbotD：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.chatbotId"
          ></el-input>
        </el-form-item>
        <el-form-item label="chatbot主页地址：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.website"
          ></el-input>
        </el-form-item>
        <el-form-item label="chatbot办公地址：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.address"
          ></el-input>
        </el-form-item>
        <el-form-item label="经度：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.longitude"
          ></el-input>
        </el-form-item>
        <el-form-item label="纬度：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.latitude"
          ></el-input>
        </el-form-item>
        <el-form-item label="签名：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.autoGraph"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="扩展码：">
              <el-input
                disabled
                style="width: 220px"
                v-model="chatbotList.ext"
              ></el-input>
            </el-form-item> -->
        <el-form-item label="服务电话：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.callbackPhone"
          ></el-input>
        </el-form-item>
        <el-form-item label="服务条款：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.terms"
          ></el-input>
        </el-form-item>
        <el-form-item label="服务描述：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系人邮箱：">
          <el-input
            disabled
            style="width: 220px"
            v-model="chatbotList.email"
          ></el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <!--        <el-button @click="dialogVisible = false">取 消</el-button>-->
          <!--        <el-button type="primary" @click="submitChannel('formInline')"-->
          <!--          >提交</el-button-->
          <!--        >-->
        </span>
      </template>
    </el-dialog>
    <!-- 白名单 -->
    <el-dialog
      title="调试白名单"
      v-model="whiteDialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :model="ruleForm"
        status-icon
        :rules="ruleFormrules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="白名单" prop="whiteList">
          <el-input
            maxlength="239"
            show-word-limit
            type="textarea"
            v-model="ruleForm.whiteList"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <!-- <span>这是一段信息</span> -->
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="whiteDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitWiteList">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 上架 -->
    <el-dialog
      title="申请上架"
      v-model="onlinedialog"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        :model="onlineForm"
        :rules="onlineRules"
        status-icon
        ref="onlineForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="上架说明" prop="explain">
          <el-input
            type="textarea"
            v-model="onlineForm.explain"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="文件上传" prop="url">
          <el-upload
            class="upload-demo"
            :action="action"
            :headers="token"
            :file-list="fileLists"
            :on-preview="handlePreview"
            :on-remove="handleRemoves"
            :on-success="handleSuccess"
            list-type="picture"
            :limit="1"
          >
            <el-button size="default" type="primary">上传图片</el-button>
          </el-upload>
          <!-- <el-input type="textarea" v-model="onlineForm.explain" autocomplete="off"></el-input> -->
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="onlinedialog = false">取 消</el-button>
          <el-button type="primary" @click="submitonline('onlineForm')"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-dialog
      title="信息对比"
      v-model="contrastVisible"
      width="75%"
      :before-close="CloseFn"
    >
      <div>
        <el-form
          :model="oldChatbotList"
          :rules="rules"
          :inline="true"
          label-width="180px"
          ref="formInlines"
          class="demo-form-inline"
        >
          <div v-if="oldChatbotList">
            <el-form-item label="用户名称：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.username == chatbotList.username ? '' : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.username"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.username != chatbotList.username"
                  effect="dark"
                  :content="oldChatbotList.username"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="ChatBot名称：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.chatbotName == chatbotList.chatbotName
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.chatbotName"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.chatbotName != chatbotList.chatbotName"
                  effect="dark"
                  :content="oldChatbotList.chatbotName"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="chatbotID：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.chatbotId == chatbotList.chatbotId
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.chatbotId"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.chatbotId != chatbotList.chatbotId"
                  effect="dark"
                  :content="oldChatbotList.chatbotId"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="chatbot主页地址：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.website == chatbotList.website ? '' : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.website"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.website != chatbotList.website"
                  effect="dark"
                  :content="oldChatbotList.website"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="chatbot办公地址：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.address == chatbotList.address ? '' : 'te'
                  "
                  disabled
                  type="textarea"
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.address"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.address != chatbotList.address"
                  effect="dark"
                  :content="oldChatbotList.address"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="经度：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.longitude == chatbotList.longitude
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.longitude"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.longitude != chatbotList.longitude"
                  effect="dark"
                  :content="oldChatbotList.longitude"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="纬度：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.latitude == chatbotList.latitude ? '' : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.latitude"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.latitude != chatbotList.latitude"
                  effect="dark"
                  :content="oldChatbotList.latitude"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="签名：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.autoGraph == chatbotList.autoGraph
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.autoGraph"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.autoGraph != chatbotList.autoGraph"
                  effect="dark"
                  :content="oldChatbotList.autoGraph"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <!-- <el-form-item label="扩展码：">
                  <div class="formItem">
                    <el-input
                      :class="oldChatbotList.ext == chatbotList.ext ? '' : 'te'"
                      disabled
                      show-word-limit
                      style="width: 220px"
                      v-model="chatbotList.ext"
                    ></el-input>
                    <el-tooltip
                      v-if="oldChatbotList.ext != chatbotList.ext"
                      effect="dark"
                      :content="oldChatbotList.ext"
                      placement="top-start"
                    >
                      <i
                        style="color: #e6a23c; margin: 0 10px"
                        class="el-icon-warning"
                      ></i>
                    </el-tooltip>
                  </div>
                </el-form-item> -->
            <el-form-item label="服务电话：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.callbackPhone == chatbotList.callbackPhone
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.callbackPhone"
                ></el-input>
                <el-tooltip
                  v-if="
                    oldChatbotList.callbackPhone != chatbotList.callbackPhone
                  "
                  effect="dark"
                  :content="oldChatbotList.callbackPhone"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="服务条款：">
              <div class="formItem">
                <el-input
                  :class="oldChatbotList.terms == chatbotList.terms ? '' : 'te'"
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.terms"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.terms != chatbotList.terms"
                  effect="dark"
                  :content="oldChatbotList.terms"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="服务描述：">
              <div class="formItem">
                <el-input
                  :class="
                    oldChatbotList.description == chatbotList.description
                      ? ''
                      : 'te'
                  "
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.description"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.description != chatbotList.description"
                  effect="dark"
                  :content="oldChatbotList.description"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="联系人邮箱：">
              <div class="formItem">
                <el-input
                  :class="oldChatbotList.email == chatbotList.email ? '' : 'te'"
                  disabled
                  show-word-limit
                  style="width: 220px"
                  v-model="chatbotList.email"
                ></el-input>
                <el-tooltip
                  v-if="oldChatbotList.email != chatbotList.email"
                  effect="dark"
                  :content="oldChatbotList.email"
                  placement="top-start"
                >
                  <el-icon style="color: #e6a23c; margin: 0 10px"><WarningFilled /></el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="contrastVisible = false">取 消</el-button>
          <el-button type="primary" @click="quiteUpdata">更新</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 复制 -->
    <el-dialog
      title="复制通道"
      v-model="copyVisible"
      width="30%"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        :model="copyForm"
        :rules="rules"
        :inline="true"
        label-width="140px"
        ref="copyForm"
        class="demo-form-inline"
      >
        <el-form-item label="目标通道" prop="targetChannelId">
          <el-select
            class="input-w"
            v-model="copyForm.targetChannelId"
            placeholder="选择通道"
          >
            <el-option
              v-for="item in channel"
              :key="item.channelId"
              :label="item.channelName"
              :value="item.channelId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="行业扩展码" prop="hyExtCode" key="hyExtCode">
          <el-input
            class="input-w"
            :maxlength="8"
            show-word-limit
            v-model="copyForm.hyExtCode"
            placeholder="请输入行业扩展码"
          ></el-input>
        </el-form-item>
        <el-form-item label="营销扩展码" prop="yxExtCode" key="yxExtCode">
          <el-input
            class="input-w"
            :maxlength="8"
            show-word-limit
            v-model="copyForm.yxExtCode"
            placeholder="请输入营销扩展码"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="端口服务类型" prop="portServiceType">
              <el-select style="width: 220px" clearable filterable class="input-w" v-model="copyForm.portServiceType"
                placeholder="请选择端口服务类别">
                <el-option v-for="(item, index) in portTypeList" :label="item.name" :value="item.porType"
                  :key="index"></el-option>
              </el-select>
            </el-form-item> -->
        <el-form-item
          label="广告营销行业类型"
          prop="adMarketTndustryType"
        >
          <el-select
            style="width: 220px"
            filterable
            clearable
            class="input-w"
            v-model="copyForm.adMarketTndustryType"
            placeholder="请选择广告营销行业类型"
          >
            <el-option
              v-for="(item, index) in adMarketingIndustryType"
              :label="item.name"
              :value="item.industryType"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <span class="dialog-footer">
          <el-button @click="copyVisible = false">取 消</el-button>
          <el-button type="primary" @click="copyChannel('copyForm')"
            >提交</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import Tooltip from '@/components/publicComponents/tooltip.vue'
import industrysCode from '../../../../utils/industrysCode'
export default {
  components: {
    Tooltip,
    // ElIconSuccess,
    // ElIconError,
    // ElIconWarning,
  },
  name: 'chatbotChannel',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      id: '',
      addTitle: '',
      action: window.path.fiveWeb + '/operator/material/attachment/upload',
      token: {},
      fileLists: [],
      portTypeList: industrysCode.portTypeList,
      adMarketingIndustryType: industrysCode.adMarketingIndustryType,
      chatbotList: {},
      oldChatbotList: {},
      dialogVisible: false,
      whiteDialogVisible: false,
      contrastVisible: false,
      onlinedialog: false,
      isFirstEnter: false,
      copyVisible: false,
      updateId: '',
      channelIdCopy: '',
      extCopy: '',
      copyShow: true,
      channelOperator: '1',
      formInline: {
        channelId: '', //通道ID
        category: '', //行业编码
        channelName: '', //通道名称
        channelOperator: '', //运营商
        chatbotId: '',
        whiteList: '', //白名单
        actualIndustry: '', //实际下发
        ext: '', //扩展号
        // portServiceType: '',//端口类型
        adMarketTndustryType: '', //广告营销行业类型
      },
      copyForm: {
        sourceId: '', //复制的通道ID
        targetChannelId: '', //目标通道ID
        hyExtCode: '', //行业扩展码
        yxExtCode: '', //营销扩展码
        // portServiceType:"",//端口服务类型
        // portServiceType:"",//端口服务类型
        adMarketTndustryType: '', //广告营销行业类型
        extCode: '', //扩展码
      },
      status: '',
      ruleForm: {
        whiteList: '',
        id: '',
      },
      ruleFormrules: {
        whiteList: [
          { required: true, message: '白名单不可为空', trigger: 'change' },
        ],
      },
      onlineForm: {
        id: '',
        explain: '',
        url: '',
      },
      onlineRules: {
        explain: [
          { required: true, message: '上架说明不能为空', trigger: 'change' },
        ],
        url: [{ required: true, message: '请上传文件', trigger: 'change' }],
      },
      rules: {
        channelId: [
          { required: true, message: '请选择通道', trigger: 'change' },
        ],
        ext: [{ required: true, message: '扩展号不能为空', trigger: 'change' }],
        category: [
          { required: true, message: '请选择行业类型编码', trigger: 'change' },
        ],
        whiteList: [
          { required: true, message: '白名单不可为空', trigger: 'change' },
        ],
        actualIndustry: [
          { required: true, message: '请选择实际下发行业', trigger: 'change' },
        ],
        targetChannelId: [
          {
            required: true,
            message: '请选择通道',
            trigger: ['change', 'blur'],
          },
        ],
        extCode: [
          {
            required: true,
            message: '扩展码不能为空',
            trigger: ['change', 'blur'],
          },
        ],
        // portServiceType: [
        //   { required: true, message: "请选择端口服务类型", trigger: ['change', 'blur'] }
        // ],
        adMarketTndustryType: [
          {
            required: true,
            message: '请选择广告营销行业类型',
            trigger: ['change', 'blur'],
          },
        ],
        hyExtCode: [
          {
            required: true,
            message: '行业扩展码不能为空',
            trigger: ['change', 'blur'],
          },
        ],
        yxExtCode: [
          {
            required: true,
            message: '营销扩展码不能为空',
            trigger: ['change', 'blur'],
          },
        ],
      },
      channel: [],
      ltOption: industrysCode.ltOption,
      dxOption: industrysCode.dxOption,
      industrys: [],
      office: [],
      city: [],
      province: [],
      tableDataObj: {
        tableData: [],
        loading2: false,
      },
      username: '',
    }
  },
  created() {
    this.id = this.$route.query.chatbotId
    this.username = this.$route.query.username
    this.chatbotList = JSON.parse(localStorage.getItem('chatbotList'))
    this.token = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    this.getList()
    this.channel5GList()
    this.getIndustrys()
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.id = this.$route.query.chatbotId
        this.username = this.$route.query.username
        this.chatbotList = JSON.parse(localStorage.getItem('chatbotList'))
        this.token = {
          Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
        }
        this.getList()
        this.channel5GList()
        this.getIndustrys()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    goBack() {
      this.$router.push({ path: '/chatbotAudit' })
    },
    /**获取行业类别列表-移动*/
    getIndustrys() {
      window.api.get(window.path.fiveWeb + '/industry/firstIndustry', {}, (res) => {
        this.industrys = res.data
      })
    },
    getList() {
      this.tableDataObj.loading2 = true
      window.api.get(
        window.path.fiveWeb + '/operator/chatbot/getChannelList/' + this.id,
        {},
        (res) => {
          this.tableDataObj.tableData = res.data
          this.tableDataObj.loading2 = false
        }
      )
    },
    /**获取通道*/
    // getAvailableChannelList
    channel5GList() {
      window.api.get(
        window.path.fiveWeb +
          '/operator/chatbot/getAllAvailableChannelList/' +
          this.id,  
        {},
        (res) => {
          this.channel = res.data
        }
      )
    },
    handChannel(e) {
      // console.log(e,'e');
      this.channel.forEach((item) => {
        if (e == item.channelId) {
          this.formInline.channelName = item.channelName
          // this.formInline.channelId = item.channelCode;
          this.formInline.channelOperator = item.channelOperator
        }
      })
    },
    handleCategory(e) {
      // this.industrys.forEach((item)=>{
      //   if(e == item.id){
      //     this.formInline.category = item.industryName
      //   }
      // })
    },
    addChannel() {
      this.dialogVisible = true
      this.addTitle = '添加通道'
      this.formInline.chatbotId = this.$route.query.chatbotId
      this.channel5GList()
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.formInline.id = row.id
      this.status = row.status
      this.addTitle = '编辑通道'
      this.formInline.channelId = row.channelId
      this.formInline.channelOperator = row.channelOperator
      this.formInline.channelName = row.channelName
      if (row.channelOperator == 1) {
        this.formInline.category = row.category
      } else {
        this.formInline.category = row.category + ''
      }
      this.formInline.ext = row.ext
      this.formInline.actualIndustry = row.actualIndustry
      this.formInline.whiteList = row.whiteList
      // this.formInline.portServiceType = row.portServiceType
      this.formInline.adMarketTndustryType = row.adMarketTndustryType
    },
    refresh() {
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
      this.whiteDialogVisible = false
      this.onlinedialog = false
      this.copyVisible = false
    },
    //添加通道
    submitChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.formInline.chatbotId = this.$route.query.chatbotId
          if (!this.formInline.id) {
            window.api.post(
              window.path.fiveWeb + '/operator/chatbot/addChannel',
              this.formInline,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogVisible = false
                  this.getList()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error',
                  })
                }
              }
            )
          } else {
            window.api.put(
              window.path.fiveWeb + '/operator/chatbot',
              this.formInline,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogVisible = false
                  this.getList()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error',
                  })
                }
              }
            )
          }
        }
      })
    },
    //报备通道
    declareChannel(row) {
      this.$confirms.confirmation(
        'post',
        '报备通道',
        window.path.fiveWeb + '/operator/chatbot/declareChannel/' + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.getList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    //调试白名单
    serviceCode(row) {
      this.whiteDialogVisible = true
      this.ruleForm.id = row.id
      if (row.whiteList) {
        this.ruleForm.whiteList = row.whiteList
      }
    },
    //注销
    logout(row) {
      this.$confirms.confirmation(
        'get',
        '注销此chatbot通道信息',
        window.path.fiveWeb + '/operator/chatbot/cancelChatbot/' + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.getList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    //复制
    copyChatbot(row) {
      this.copyVisible = true
      this.copyForm.sourceId = row.id
      this.channelIdCopy = row.channelId
      this.formInline.ext = row.ext
      this.channelOperator = row.channelOperator
      this.channel5GList()
    },
    // bingChange(e){
    //   this.copyForm.extCode = this.extCopy
    //   if(e == this.channelIdCopy){
    //     this.copyShow = true
    //   }else{
    //     this.copyShow = false
    //   }
    //   console.log(e,'e');
    // },
    copyChannel(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '复制chatbot通道！',
            window.path.fiveWeb + '/operator/chatbot/copyChatbot',
            this.copyForm,
            (res) => {
              if (res.code == 200) {
                // this.$message({
                //   message: res.msg,
                //   type: "success",
                // });
                this.getList()
                this.copyVisible = false
              }
              // else {
              //   this.$message({
              //     message: res.msg,
              //     type: "error",
              //   });
              // }
            }
          )
        }
      })
    },
    //重新上线
    hangdelAgainOnline(row, val) {
      let tit = ''
      if (val == 0) {
        tit = '确认是否停用chatbot通道信息'
      } else {
        tit = '重新上线chatbot通道信息'
      }
      this.$confirms.confirmation(
        'get',
        tit,
        window.path.fiveWeb + '/operator/chatbot/reOnline/' + row.id + '/' + val,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.getList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    CloseFn() {
      this.contrastVisible = false
    },
    //删除chatboot
    bindDelete(row) {
      this.$confirms.confirmation(
        'delete',
        '是否确认删除chatbot信息？',
        window.path.fiveWeb + '/operator/chatbot/deleteChatbotChannel/' + row.id,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.getList()
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    //更新客户信息
    quiteUpdata() {
      this.$confirms.confirmation(
        'get',
        '更新chatbot通道信息',
        window.path.fiveWeb +
          '/operator/chatbot/updateChatbotChannelInfo/' +
          this.updateId,
        {},
        (res) => {
          if (res.code == 200) {
            // this.$message({
            //   message: res.msg,
            //   type: 'success',
            // })
            this.getList()
            this.contrastVisible = false
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error',
            // })
          }
        }
      )
    },
    //更新
    handupload(row) {
      this.contrastVisible = true
      this.updateId = row.id
      if (row.snapshot) {
        this.oldChatbotList = JSON.parse(row.snapshot)
        console.log(this.oldChatbotList)
        this.provinces(this.oldChatbotList.officeCode)
        this.citys(this.oldChatbotList.provinceCode)
      }
    },
    submitWiteList() {
      // this.ruleForm.id = this.$route.query.chatbotId
      window.api.post(
        window.path.fiveWeb + '/operator/chatbot/submitWhiteList',
        this.ruleForm,
        (res) => {
          if (res.code == 200) {
            this.whiteDialogVisible = false
            this.$message({
              message: res.msg,
              type: 'success',
            })
            this.getList()
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        }
      )
    },
    online(row) {
      this.onlinedialog = true
      this.onlineForm.id = row.id
    },

    //申请上架
    submitonline(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.fiveWeb + '/operator/chatbot/online',
            this.onlineForm,
            (res) => {
              if (res.code == 200) {
                this.onlinedialog = false
                this.$message({
                  message: res.msg,
                  type: 'success',
                })
                this.getList()
              } else {
                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            }
          )
        }
      })
    },
    handleRemoves(file, fileList) {
      // console.log(file, fileList);
      this.onlineForm.url = ''
    },
    handlePreview(file) {
      // console.log(file);
    },
    handleSuccess(res, file, fileList) {
      if (res.code == 200) {
        this.onlineForm.url = res.data
      } else {
        this.$message.error(res.msg)
      }
    },
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs['formInline'].resetFields()
        this.formInline.channelName = ''
        this.formInline.channelOperator = ''
        this.formInline.channelCode = ''
        this.formInline.whiteList = ''
        this.formInline.channelId = ''
        this.formInline.id = ''
        this.formInline.ext = ''
        this.formInline.category = ''
        this.formInline.actualIndustry = ''
        this.formInline.adMarketTndustryType = ''
        // this.ruleForm.actualIndustry = ""
      }
    },
    onlinedialog(val) {
      if (!val) {
        this.$refs['onlineForm'].resetFields()
        this.onlineForm.id = ''
        this.onlineForm.url = ''
        this.fileLists = []
      }
    },
    whiteDialogVisible(val) {
      if (!val) {
        this.$refs['ruleForm'].resetFields()
        this.ruleForm.id = ''
        this.ruleForm.whiteList = ''
      }
    },
    copyVisible(val) {
      if (!val) {
        this.$refs['copyForm'].resetFields()
        this.copyForm.sourceId = ''
        this.copyForm.targetChannelId = ''
        this.copyForm.extCode = ''
      }
    },
  },
}
</script>

<style scoped>
.solid {
  width: 100%;
  height: 1px;
  border-bottom: 1px dashed #ccc;
  margin: 10px 0;
}
.te :deep(.el-input__inner) {
  color: red;
}
.formItem {
  width: 260px;
}
</style>

<style>
.el-textarea__inner {
  height: 100%;
}
.el-tabs__content {
  overflow: visible !important;
}
</style>