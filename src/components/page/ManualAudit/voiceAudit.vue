<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="sensitiveCondition"
          class="demo-form-inline"
          ref="sensitiveCondition"
        >
          <el-form-item label="用户名称" prop="username">
            <el-input
              v-model="sensitiveCondition.username"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input
              v-model="sensitiveCondition.content"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="审核状态" prop="auditStatus">
            <el-select
              v-model="sensitiveCondition.auditStatus"
              placeholder="请选择"
              clearable
              class="input-w"
            >
              <el-option label="待审核" value="1"></el-option>
              <el-option label="审核通过" value="2"></el-option>
              <el-option label="审核不通过" value="3"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="提交时间"  prop="time1">
                            <el-date-picker class="input-w"
                            v-model="sensitiveCondition.time1"
                            value-format="YYYY-MM-DD"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="hande1"
                            :picker-options="pickerOptions"
                            >
                            </el-date-picker>
                        </el-form-item>  -->
          <div>
            <el-button type="primary" plain @click.prevent="Query()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('sensitiveCondition')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button
              type="primary"
              @click="handleAdopt1()"
              v-if="remarkObj.formData.idArr.length != 0"
              >批量通过</el-button
            >
            <el-button
              type="danger"
              @click="sensitiveBatchSet()"
              v-if="remarkObj.formData.idArr.length != 0"
              >批量不通过</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="voiceAudit"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名" title="用户名" width="120">
            <template  #default="scope">
              <!-- <el-popover
                                    placement="top-start"
                                    width="700px"
                                    trigger="hover"
                                    @show='userList(scope.row,scope.$index)'>
                                    <UserLists v-if="userFlag&&scope.$index==nameover" :username="scope.row.userName"/>
                                    <span slot="reference" style="color:#16A589;cursor: pointer;" @click="rouTz(scope.row)">
                                     {{ scope.row.userName}}
                                    </span>
                                </el-popover> -->
              <div
                style="color: #16a589; cursor: pointer"
                @click="rouTz(scope.row)"
                >{{ scope.row.userName }}</div
              >
            </template>
          </vxe-column>
          <vxe-column field="内容" title="内容">
            <template  #default="scope">
              <div>{{ scope.row.content }}</div>
            </template>
          </vxe-column>
          <vxe-column v-if="KZNum != '1'" field="快照预览" title="快照预览" width="70">
            <template  #default="scope">
              <div
                class="demo-image__preview"
                style="cursor: pointer"
                @click="KZCK(scope.row.auditPic)"
              >
                <el-image
                  style="width: 50px; height: 50px"
                  :z-index="9999"
                  :src="
                    scope.row.auditPic
                      ? API.imgU + scope.row.auditPic.split(',')[0]
                      : ''
                  "
                  :preview-src-list="KFck.srcList"
                >
                  <template v-slot:error>
                    <div class="image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="敏感词" title="敏感词">
            <template  #default="scope">
              <div style="color: rgb(245, 108, 108)">{{
                scope.row.prompt
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="号码数" title="号码数">
            <template  #default="scope">
              <div>
                {{ scope.row.mobCount }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="sensitiveConObj.auditStatus != 1"
            field="审核人"
            title="审核人"
          >
            <template  #default="scope">
              <div>
                {{ scope.row.auditUsername }}
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="sensitiveConObj.auditStatus != 1"
            field="审核时间"
            title="审核时间"
            width="140"
          >
            <template  #default="scope">
              <div>
                {{ scope.row.auditTime }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="100">
            <template  #default="scope">
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
              </p>
              <p v-if="scope.row.createTime">
                {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
              </p>
              <!-- {{scope.row.createTime}} -->
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="180" fixed="right">
            <template  #default="scope">
              <el-button
                link
                style="margin-left: 0px; color: #409eff;"
                v-if="scope.row.auditStatus == '1'"
                @click="handleAdopt3(scope.$index, scope.row)"
                ><el-icon><SuccessFilled /></el-icon>&nbsp;审核通过</el-button
              >
              <el-button
                link
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.auditStatus != '1'"
                @click="ReviewRejected(scope.$index, scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;审核驳回</el-button
              >
              <el-button
                link
                style="color: #f56c6c; margin-left: 0px"
                v-if="scope.row.auditStatus == '1'"
                @click="handleReject(scope.$index, scope.row)"
                ><el-icon><CircleCloseFilled /></el-icon>&nbsp;审核不通过</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 100, 500, 1000]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>
        
          <!-- </el-col>
        </template> -->
      </div>
      <!-- 弹窗（查看图片） -->
      <el-dialog
        :title="titleS"
        v-model="imagesshow"
        width="600px"
        :close-on-click-modal="false"
      >
        <template>
          <el-carousel
            :interval="5000"
            arrow="always"
            :autoplay="false"
            style="height: 560px"
          >
            <el-carousel-item
              style="height: 560px"
              v-for="(item, index) in imgUrl"
              :key="index"
            >
              <img style="width: 100%; max-height: 580px" :src="item" alt="" />
            </el-carousel-item>
          </el-carousel>
        </template>
      </el-dialog>
      <!-- 弹框 （审核 通过） -->
      <el-dialog
        :title="title"
        v-model="remarkshow"
        width="600px"
        :close-on-click-modal="false"
        :show-close="false"
      >
        <el-form
          :model="remarkObj.formData"
          :rules="remarkObj.formRule"
          ref="remarkObjs"
          label-width="100px"
          style="padding: 0 28px"
        >
          <el-form-item label="不通过原因:" prop="labelCheckList">
            <el-checkbox-group v-model="remarkObj.formData.labelCheckList">
              <el-checkbox
                :label="item.id"
                v-for="(item, index) in labelCheckListArry"
                :key="index"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="备注:">
            <el-input
              type="textarea"
              :disabled="disabledinput"
              rows="4"
              resize="none"
              v-model="remarkObj.formData.auditReason"
            ></el-input>
          </el-form-item>
          <el-upload
            class="upload-demo"
            :action="actionUrl"
            :headers="token"
            :file-list="fileList1"
            :on-remove="handleRemove1"
            :on-success="handleAvatarSuccess1"
            :before-upload="beforeAvatarUpload1"
            list-type="picture"
          >
            <el-button size="default" type="primary">点击上传</el-button>
            <template v-slot:tip>
              <div class="el-upload__tip">请上传图片</div>
            </template>
          </el-upload>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              @click="remarkshow = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              v-if="operationFlag == true"
              type="primary"
              @click="handelAlertdd('remarkObjs')"
              >确 认</el-button
            >
            <el-button
              type="primary"
              v-if="operationFlag == false"
              :loading="true"
              >操作进行中，请稍等</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 上传图片 -->
      <el-dialog
        title="上传图片"
        v-model="uploadImage"
        width="530px"
        class="TemDialog"
        :close-on-click-modal="false"
      >
        <el-upload
          class="upload-demo"
          :action="actionUrl"
          :headers="token"
          :file-list="fileList1"
          :on-remove="handleRemove1"
          :on-success="handleAvatarSuccess1"
          :before-upload="beforeAvatarUpload1"
          list-type="picture"
        >
          <el-button size="default" type="primary">点击上传</el-button>
          <template v-slot:tip>
            <div class="el-upload__tip">请上传图片</div>
          </template>
        </el-upload>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="uploadReview">确 定</el-button>
            <el-button @click="uploadImage = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
import FileUpload from '@/components/publicComponents/FileUpload.vue' //文件上传
import TableTem from '@/components/publicComponents/TableTem.vue'
import UserLists from '@/components/publicComponents/userList.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'

export default {
  components: {
    TableTem,
    FileUpload,
    UserLists
  },
  name: 'voiceAudit',
  data() {
    var ext = (rule, value, callback) => {
      if (value == '' || value == null) {
        callback()
      } else {
        if (!/^\d{2,15}$/.test(value)) {
          return callback(new Error('请输入2-15位扩展号'))
        } else {
          callback()
        }
      }
    }
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      actionUrl: window.path.cpus + 'v3/file/upload',
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMinDate = minDate.getTime()
          if (maxDate) {
            this.pickerMinDate = ''
          }
        },
        disabledDate: (time) => {
          if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
            return false
          }
          if (this.pickerMinDate !== '') {
            const day30 = 2 * 24 * 3600 * 1000
            let maxTime = this.pickerMinDate
            if (maxTime > Date.now()) {
              maxTime = Date.now()
            }
            const minTime = this.pickerMinDate - day30
            return time.getTime() < minTime || time.getTime() > maxTime
          }
          return time.getTime() > Date.now()
        },
      },
      userFlag: false,
      nameover: '',
      // 预览
      viewData: '',
      indexVeew: 1,
      title: '',
      KZNum: '1',
      KFck: {
        url: '',
        srcList: [window.path.imgU],
      },
      uploadImage: false,
      token: {
        Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
      },
      fileList1: [],
      fileauditpic: [],
      fileImgs: [],
      rowId: '',
      imagesshow: false,
      //条件审核
      ConditionForm: {
        auditStatus: '2',
        auditReason: '',
        productId: '1',
        remark: '',
      },
      ConditionFormRulef: {
        auditReason: [
          {
            required: true,
            message: '请填写不通过原因',
            trigger: ['change', 'blur'],
          },
        ],
      },
      trueSize: '',
      trueFlog: false,
      auditTime: '',
      turnStatus: false, //是否驳回操作
      disabledinput: false, //是否禁用
      operationFlag: true, //操作正在进行中按钮是否存在
      remarkshow: false,
      labelCheckListArry: [],
      //驳回弹出框的值
      remarkObj: {
        formData: {
          auditReason: '',
          idArr: [],
          labelCheckList: [],
        },
        formRule: {
          labelCheckList: [
            {
              type: 'array',
              required: true,
              message: '请选择拒绝原因',
              trigger: 'change',
            },
          ],
        },
      },
      idArrs: [], // 驳回的id
      title: '',
      //上传文件格式
      fileStyle: {
        size: 5,
        type: 'img',
        style: ['jpg', 'jpeg', 'bmp', 'gif', 'png'],
      },
      del1: true, //关闭弹框时清空图片
      imgUrl: [
        // require('../../../assets/images/timg.jpg'),
        // require('../../../assets/images/qxf.png'),
        // require('../../../assets/images/timg.gif')
      ],
      //批量操作列表选中项的审核状态
      auditStatus: [],
      //查询条件的值
      sensitiveCondition: {
        username: '',
        content: '',
        time1: [],
        auditStatus: '1',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //赋值查询条件的值
      sensitiveConObj: {
        username: '',
        content: '',
        time1: [],
        auditStatus: '1',
        beginTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      titleS: '', //签名的类型
      isFirstEnter: false,
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
    // this.getTableDtate();
    // 查询拒绝原因标签
    window.api.get(window.path.omcs + 'servicerefusereason/tag', {}, (res) => {
      this.labelCheckListArry = res.data
    })
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'serviceauditvoice/page',
        this.sensitiveConObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    Query() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.KZNum = this.sensitiveConObj.auditStatus
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.sensitiveCondition.beginTime = ''
      this.sensitiveCondition.endTime = ''
      this.sensitiveCondition.auditStartTime = ''
      this.sensitiveCondition.auditEndTime = ''
      Object.assign(this.sensitiveConObj, this.sensitiveCondition) //把查询框的值赋给一个对象
      this.KZNum = '1'
      this.getTableDtate()
    },
    rouTz(val) {
      this.$router.push({ path: '/UserDetail', query: { id: val.userId } })
    },
    userList(row, index) {
      this.userFlag = true
      this.nameover = index
    },
    //快照查看
    KZCK(val) {
      if (val) {
        this.KFck.srcList = []
        let a = val.split(',')
        console.log(val.split(','))
        for (var i = 0; i < a.length; i++) {
          this.KFck.srcList.push(window.path.imgU + a[i])
        }
      }
    },
    // 审核上传图片
    handleRemove1(file, fileList) {
      this.fileauditpic = []
      for (let i = 0; i < fileList.length; i++) {
        this.fileauditpic.push(fileList[i].response.data.fullpath)
      }
    },
    //图片上传成功
    handleAvatarSuccess1(res, file) {
      this.fileauditpic.push(res.data.fullpath)
    },
    // 图片上传限制
    beforeAvatarUpload1(file) {
      const isJPG1 = file.type === 'image/png'
      const isJPG2 = file.type === 'image/jpeg'
      const isJPG3 = file.type === 'image/tiff'
      const isJPG4 = file.type === 'image/raw'
      const isJPG5 = file.type === 'image/bmp'
      if (!isJPG1 && !isJPG2 && !isJPG3 && !isJPG4 && !isJPG5) {
        this.$message.error('只允许上传图像')
        return false
      }
    },
    trueFlogShow(val) {
      this.trueSize = val
      this.trueFlog = true
    },
    //获取查询时间的开始时间和结束时间
    hande1: function (val) {
      if (val) {
        this.sensitiveCondition.beginTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.endTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.beginTime = ''
        this.sensitiveCondition.endTime = ''
      }
    },
    //获取查询时间的开始时间和结束时间
    hande2: function (val) {
      if (val) {
        this.sensitiveCondition.auditStartTime =
          this.moment(val[0]).format('YYYY-MM-DD') + ' 00:00:00'
        this.sensitiveCondition.auditEndTime =
          this.moment(val[1]).format('YYYY-MM-DD') + ' 23:59:59'
      } else {
        this.sensitiveCondition.auditStartTime = ''
        this.sensitiveCondition.auditEndTime = ''
      }
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size
      this.getTableDtate()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage
      this.getTableDtate()
    },
    //批量通过
    handleAdopt1() {
      this.rowId = this.remarkObj.formData.idArr
      this.uploadImage = true
    },
    //驳回审核
    handleAdopt2(index, row, status) {
      this.$confirms.confirmation(
        'post',
        '是否确认审核通过？',
        window.path.omcs + 'serviceauditvoice/audit',
        {
          overrule: true,
          auditStatus: 2,
          ids: row.id,
        },
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //审核通过todey
    handleAdopt3(index, row, status) {
      this.rowId = row.id
      this.uploadImage = true
    },
    uploadReview() {
      this.$confirms.confirmation(
        'post',
        '是否确认审核通过？',
        window.path.omcs + 'serviceauditvoice/audit',
        {
          overrule: false,
          auditStatus: 2,
          ids: this.rowId,
          auditPic: this.fileauditpic.join(','),
        },
        (res) => {
          this.uploadImage = false
          this.getTableDtate()
        }
      )
    },
    // 审核驳回通过
    turnDown(index, row, status) {
      this.idArrs = ''
      this.remarkObj.formData.idArr = ''
      this.remarkshow = true
      this.title = '驳回审核'
      this.idArrs = row.id //赋值ID
      this.turnStatus = status || false
    },
    //批量通过
    sensitiveBatchDel() {
      let status = true
      //判断选项中是否有审核过的状态
      for (let i = 0; i < this.auditStatus.length; i++) {
        if (this.auditStatus[i] != '1') {
          status = false
          break
        }
      }
      if (status) {
        this.$confirms.confirmation(
          'post',
          '此操作不可逆，是否继续?',
          window.path.omcs + 'serviceauditvoice/audit',
          {
            auditStatus: 2,
            ids: this.remarkObj.formData.idArr,
          },
          (res) => {
            this.getTableDtate()
            this.remarkObj.formData.idArr = []
          }
        )
      } else {
        this.$message({
          message: '选项中包含已审核项，需重新选择待审核项！',
          type: 'warning',
        })
      }
    },
    //驳回
    handleReject(index, row) {
      this.idArrs = ''
      this.remarkObj.formData.idArr = ''
      this.remarkshow = true
      this.title = '审核不通过'
      this.idArrs = row.id //赋值ID
    },
    //驳回（发送请求）
    handelAlertdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            'post',
            '此操作不可逆，是否继续?',
            window.path.omcs + 'serviceauditvoice/audit',
            {
              overrule: this.turnStatus || false,
              auditStatus: 3,
              ids:
                this.remarkObj.formData.idArr.length == 0
                  ? this.idArrs
                  : this.remarkObj.formData.idArr,
              remark: this.remarkObj.formData.auditReason,
              tag: this.remarkObj.formData.labelCheckList.join(','),
              auditPic: this.fileauditpic.join(','),
            },
            (res) => {
              this.getTableDtate()
              this.remarkshow = false
              this.remarkObj.formData.auditReason = ''
            }
          )
        } else {
          return false
        }
      })
    },
    //批量驳回
    sensitiveBatchSet() {
      this.title = '批量驳回备注'
      this.remarkshow = true
    },
    // 审核驳回
    ReviewRejected(index, val) {
      this.$confirms.confirmation(
        'post',
        '确认当条审核驳回?',
        window.path.omcs + 'serviceauditvoice/overrule/' + val.id,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let auditStatus = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
        //批量操作列表选中项的审核状态
        auditStatus.push(val.records[i].auditStatus)
      }
      this.auditStatus = auditStatus //选中项的审核状态
      this.remarkObj.formData.idArr = selectId.join(',') //批量通过，不通过的id数组
    },
  },
  mounted() {
    // 注册回车事件
    var _self = this
    document.onkeydown = function (e) {
      if (window.event == undefined) {
        var key = e.keyCode
      } else {
        var key = window.event.keyCode
      }
      if (key == 13) {
        _self.Query()
      }
    }
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
    //驳回弹出框是否关闭
    remarkshow(val) {
      if (this.$refs.remarkObjs) {
        if (val == false) {
          this.turnStatus = false
          this.operationFlag = true //操作进行中（按钮的显示）
          this.$refs.remarkObjs.resetFields()
          this.remarkObj.formData.idArr = [] //置空ID
          this.getTableDtate()
        }
      }
    },
    uploadImage(val) {
      if (val == false) {
        this.fileList1 = []
        this.fileauditpic = []
        this.rowId = ''
        this.getTableDtate()
      }
    },
  },
}
</script>

<style scoped>
.el-checkbox-group > .el-checkbox {
  margin-left: 30px;
}
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.divWidth > div {
  width: 100%;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
.sensitive {
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style>
.divWidth > div > div {
  width: 60%;
}
.el-table--small th {
  background: #f5f5f5;
}
.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
