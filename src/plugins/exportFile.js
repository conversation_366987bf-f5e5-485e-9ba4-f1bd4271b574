// 配置API接口地址
// var root = 'http://mix3test.ztinfo.cn/gateway'
// 引用axios
// var axios = require('axios')
import axios from 'axios'
// 自定义判断元素类型JS
function toType(obj) {
  return {}.toString
    .call(obj)
    .match(/\s([a-zA-Z]+)/)[1]
    .toLowerCase()
}
// 参数过滤函数
function filterNull(o) {
  for (var key in o) {
    if (o[key] === null) {
      delete o[key]
    }
    if (toType(o[key]) === 'string') {
      o[key] = o[key].trim()
    } else if (toType(o[key]) === 'object') {
      o[key] = filterNull(o[key])
    } else if (toType(o[key]) === 'array') {
      o[key] = filterNull(o[key])
    }
  }
  return o
}
// 时间过滤
Date.prototype.format = function (format) {
  var args = {
    'M+': this.getMonth() + 1,
    'd+': this.getDate(),
    'h+': this.getHours(),
    'm+': this.getMinutes(),
    's+': this.getSeconds(),
    'q+': Math.floor((this.getMonth() + 3) / 3), //quarter
    S: this.getMilliseconds(),
  }
  if (/(y+)/.test(format))
    format = format.replace(
      RegExp.$1,
      (this.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  for (var i in args) {
    var n = args[i]
    if (new RegExp('(' + i + ')').test(format))
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? n : ('00' + n).substr(('' + n).length)
      )
  }
  return format
}

function apiAxios(url, params, name, failure) {
  if (params) {
    params = filterNull(params)
  }
  let CancelToken = axios.CancelToken
  axios({
    method: 'post',
    url: url,
    data: params,
    // baseURL: root,
    responseType: 'blob',
    withCredentials: false,
    cancelToken: new CancelToken(function executor(c) {
      window.app.config.globalProperties.cancel = c
      // 这个参数 c 就是CancelToken构造函数里面自带的取消请求的函数，这里把该函数当参数用
    }),
  })
    .then(function (res) {
      let blob = new Blob([res.data], {
        type: 'application/zip',
      })
      // let blob = res.data
      let reader = new FileReader()
      reader.readAsDataURL(blob)
      reader.onload = (e) => {
        let a = document.createElement('a')
        a.download =
          '(' + new Date().format('YYYY-MM-DD HH:mm:ss') + ') ' + name
        a.href = e.target.result
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    })
    .catch(function (err) {
      let res = err
      if (err) {
        // window.alert('api error, HTTP CODE: ' + res)
      }
    })
}
// 返回在vue模板中的调用接口
export default {
  export: function (url, params, name, failure) {
    return apiAxios(url, params, name, failure)
  },
}
