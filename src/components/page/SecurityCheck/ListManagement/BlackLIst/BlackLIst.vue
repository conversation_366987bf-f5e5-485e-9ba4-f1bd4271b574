<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        label-width="82px"
        ref="formBlackList"
      >
        <el-form-item label="投诉类别" prop="blacklistType">
          <el-select
            v-model="formBlackList.blacklistType"
            clearable
            placeholder="请选择"
            class="input-w"
          >
            <el-option label="运营商投诉" value="1"></el-option>
            <el-option label="工信部投诉" value="2"></el-option>
            <el-option label="同行共享投诉" value="3"></el-option>
            <el-option label="上行内容加黑" value="4"></el-option>
            <el-option label="投诉专业户" value="6"></el-option>
            <el-option label="其他" value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="号码归属地" prop="blacklistCity">
          <el-input
            v-model="formBlackList.blacklistCity"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="blacklistPhone">
          <el-input
            v-model="formBlackList.blacklistPhone"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="黑号等级" prop="blacklistLevel">
          <el-select
            v-model="formBlackList.blacklistLevel"
            placeholder="请选择"
            clearable
            class="input-w"
          >
            <el-option label="一级" value="1"></el-option>
            <el-option label="二级" value="2"></el-option>
            <el-option label="三级" value="3"></el-option>
            <el-option label="四级" value="4"></el-option>
            <el-option label="五级" value="5"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="isDelete">
          <el-select
            v-model="formBlackList.isDelete"
            placeholder="请选择"
            clearable
            class="input-w"
          >
            <el-option label="启用" value="1"></el-option>
            <el-option label="已解除" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建人" prop="createName">
          <el-input
            v-model="formBlackList.createName"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            class="input-time"
            v-model="formBlackList.createTime"
            format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="hande"
          >
          </el-date-picker>
        </el-form-item>

        <!-- <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formBlackList.remark"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item> -->
        <div>
          <el-button type="primary" plain @click="quenryBlack()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
        </div>
      </el-form>

      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="blackListDialog = true"
              >新增黑名单</el-button
            >
            <el-button type="primary" @click="exportBlacks">导入</el-button>
            <el-button
              type="primary"
              @click="batchDeletion"
              v-if="
                selectId.length > 0 &&
                (roleId == '3' || roleId == '4' || roleId == '5')
              "
              >批量解除</el-button
            >
            <el-button
              type="primary"
              @click="batchqiyong"
              v-if="
                selectId.length > 0 &&
                (roleId == '3' || roleId == '4' || roleId == '5')
              "
              >批量启用</el-button
            >
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="BlackLIst"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column
            field="手机号码"
            title="手机号码"
            width="130"
          >
            <template v-slot="scope">
              <div>{{ scope.row.blacklistPhone }}</div>
            </template>
          </vxe-column>
          <vxe-column field="投诉类别" title="投诉类别" width="130">
            <template v-slot="scope">
              <div v-if="selectFlag != 0 && selectIndex == scope.$index">
                <el-select
                  v-model="blackListType"
                  clearable
                  @change="handBlacklistType(scope.row)"
                  placeholder="请选择"
                  class="input-w"
                >
                  <el-option label="运营商投诉" value="1"></el-option>
                  <el-option label="工信部投诉" value="2"></el-option>
                  <el-option label="同行共享投诉" value="3"></el-option>
                  <el-option label="上行内容加黑" value="4"></el-option>
                  <el-option label="投诉专业户" value="6"></el-option>
                  <el-option label="其他" value="5"></el-option>
                </el-select>
              </div>
              <div
                v-else
                class="black_type"
                @click="hangSelect(scope.row, scope.$index)"
              >
                <span v-if="scope.row.blacklistType == 1">运营商投诉</span>
                <span v-if="scope.row.blacklistType == 2">工信部投诉</span>
                <span v-if="scope.row.blacklistType == 3">同行共享投诉</span>
                <span v-if="scope.row.blacklistType == 4">上行内容加黑</span>
                <span v-if="scope.row.blacklistType == 5">其他</span>
                <span v-if="scope.row.blacklistType == 6">投诉专业户</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="黑号等级" title="黑号等级" width="80">
            <template v-slot="scope">
              <div v-if="scope.row.blacklistLevel == 1">一级</div>
              <div v-else-if="scope.row.blacklistLevel == 2">二级</div>
              <div v-else-if="scope.row.blacklistLevel == 3">三级</div>
              <div v-else-if="scope.row.blacklistLevel == 4">四级</div>
              <div v-else>五级</div>
            </template>
          </vxe-column>
          <vxe-column
            field="归属"
            title="归属"
            width="120"
          >
            <template v-slot="scope">
              <div>{{ scope.row.blacklistCity }}</div>
            </template>
          </vxe-column>
          <vxe-column
            field="创建人"
            title="创建人"
            width="130"
          >
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="状态" title="状态" width="80">
            <template v-slot="scope">
              <div v-if="scope.row.isDelete == 1">启用</div>
              <div v-else>已解除</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column
            field="备注"
            title="备注"
            min-width="200"
          >
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.remark"
                :content="scope.row.remark"
                className="wrapper-text"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="130" fixed="right">
            <!-- ||roleId == '6' || roleId == '7' || roleId == '8'
    ||roleId == '6' || roleId == '7' || roleId == '8' -->
            <template v-slot="scope">
              <el-button
                link
                v-if="
                  scope.row.isDelete == 1 &&
                  (roleId == '3' || roleId == '4' || roleId == '5')
                "
                style="color: #f56c6c"
                @click="delet(scope.row)"
                ><i class=""></i>&nbsp;解&nbsp;除</el-button
              >
              <el-button
                link
                v-else-if="
                  scope.row.isDelete == 2 &&
                  (roleId == '3' || roleId == '4' || roleId == '5')
                "
                style="color: #409eff;"
                @click="qiyong(scope.row)"
                >&nbsp;启&nbsp;用</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formBlackListObj.currentPage"
              :page-size="formBlackListObj.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.totalRow"
            >
            </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 弹窗（新增） -->
      <el-dialog
        title="新增黑名单"
        v-model="blackListDialog"
        width="520px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="blackList.formData"
          :rules="blackList.formRule"
          ref="blackList"
          label-width="96px"
        >
          <el-form-item
            label="手机号码"
            prop="blacklistPhone"
            :rules="
              filter_rules({
                required: true,
                type: 'mobile',
                message: '手机号码不能为空！',
              })
            "
          >
            <el-input v-model="blackList.formData.blacklistPhone"></el-input>
          </el-form-item>
          <el-form-item label="投诉类别" prop="blacklistType">
            <!-- <el-checkbox-group v-model="blackList.formData.blacklistTypes" >
                                <el-checkbox  label="1">运营商投诉</el-checkbox>
                                <el-checkbox  label="2">工信部投诉</el-checkbox>
                                <el-checkbox  label="5">其他</el-checkbox>
                                <el-checkbox  label="3">同行共享投诉</el-checkbox>
                                <el-checkbox  label="4">上行内容加黑</el-checkbox>
                            
                            </el-checkbox-group> -->
            <el-select
              v-model="blackList.formData.blacklistType"
              placeholder="请选择处理状态"
              style="width: 100%"
            >
              <el-option label="运营商投诉" value="1"></el-option>
              <el-option label="工信部投诉" value="2"></el-option>
              <el-option label="同行共享投诉" value="3"></el-option>
              <el-option label="上行内容加黑" value="4"></el-option>
              <el-option label="投诉专业户" value="6"></el-option>
              <el-option label="其他" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="黑号等级" prop="blacklistLevels">
            <el-checkbox-group v-model="blackList.formData.blacklistLevels">
              <el-checkbox value="1">一级</el-checkbox>
              <el-checkbox value="2">二级</el-checkbox>
              <el-checkbox value="3">三级</el-checkbox>
              <el-checkbox value="4">四级</el-checkbox>
              <el-checkbox value="5">五级</el-checkbox>
            </el-checkbox-group>
            <!-- <el-select v-model="blackList.formData.blacklistLevel" placeholder="请选择处理状态" style="width:100%;">
                                <el-option label="一级" value="1"></el-option>
                                <el-option label="二级" value="2"></el-option>
                            </el-select> -->
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="5"
              resize="none"
              v-model="blackList.formData.remark"
            ></el-input>
          </el-form-item>
          <el-form-item style="margin-top: 40px; text-align: right">
            <el-button
              class="footer-center-button"
              @click="blackListDialog = false"
              >取 消</el-button
            >
            <el-button
              class="footer-center-button"
              type="primary"
              @click="blacklist_ok_add('blackList')"
              >确 认</el-button
            >
          </el-form-item>
        </el-form>
      </el-dialog>
      <!-- 导入黑名单 弹窗 -->
      <el-dialog
        title="导入黑名单"
        v-model="SigDialogVisible"
        width="520px"
        :close-on-click-modal="false"
      >
        <el-form
          label-width="90px"
          style="padding-right: 14px"
          :model="blackList3.formData"
          :rules="blackList3.formRule"
          ref="signatureFrom"
        >
          <el-form-item label="投诉类别" prop="blacklistType">
            <el-select
              v-model="blackList3.formData.blacklistType"
              placeholder="请选择处理状态"
              style="width: 100%"
            >
              <el-option label="运营商投诉" value="1"></el-option>
              <el-option label="工信部投诉" value="2"></el-option>
              <el-option label="同行共享投诉" value="3"></el-option>
              <el-option label="上行内容加黑" value="4"></el-option>
              <el-option label="投诉专业户" value="6"></el-option>
              <el-option label="其他" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="黑号等级" prop="blacklistLevel">
            <el-select
              v-model="blackList3.formData.blacklistLevel"
              placeholder="请选择处理状态"
              style="width: 100%"
            >
              <el-option label="一级" value="1"></el-option>
              <el-option label="二级" value="2"></el-option>
              <el-option label="三级" value="3"></el-option>
              <el-option label="四级" value="4"></el-option>
              <el-option label="五级" value="5"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上传文件">
            <el-upload
              class="upload-demo"
              ref="uploading"
              :action="actionUrl"
              :on-remove="handleRemove"
              multiple
              :limit="1"
              :data="{
                blacklistType: blackList3.formData.blacklistType,
                blacklistLevel: blackList3.formData.blacklistLevel,
              }"
              :headers="header"
              :auto-upload="false"
              :on-success="handleAvatarSuccess"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :file-list="fileList"
            >
              <el-button size="default" type="primary">点击上传</el-button>
              <template v-slot:tip>
                <div class="el-upload__tip">文件格式：文件只支持.txt文本！</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="signature_add('signatureFrom')"
              >确 定</el-button
            >
            <el-button @click="SigDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import moment from 'moment'
import { mapState, mapMutations, mapActions } from 'vuex'
import Tooltip from '@/components/publicComponents/tooltip.vue'
export default {
  components: {
    TableTem,
    Tooltip
  },
  name: 'BlackLIst',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      actionUrl: window.path.omcs + 'operatingblacklist/upload',
      isFirstEnter: false,
      SigDialogVisible: false,
      fileList: [],
      blackListType: '',
      header: '',
      selectFlag: 0,
      selectIndex: 0,
      formBlackList: {
        //查询框的值
        blacklistCity: '',
        blacklistPhone: '',
        blacklistLevel: '',
        blacklistType: '',
        isDelete: '',
        createTime: [],
        createName: '',
        begTime: '',
        endTime: '',
        // remark:"",
        currentPage: 1,
        pageSize: 10,
      },
      formBlackListObj: {
        //copy查询框的值
        blacklistCity: '',
        blacklistPhone: '',
        blacklistLevel: '',
        blacklistType: '',
        isDelete: '',
        createTime: [],
        createName: '',
        begTime: '',
        endTime: '',
        // remark:"",
        currentPage: 1,
        pageSize: 10,
      },
      selectId: '', //批量操作选中id
      rowId: '', //操作行的id
      blackListDialog: false, //弹框显示隐藏
      blackList: {
        formData: {
          blacklistPhone: '',
          blacklistType: '',
          blacklistLevels: [],
          remark: '',
        },
        formRule: {
          blacklistType: [
            { required: true, message: '请选择投诉类型', trigger: 'change' },
          ],
          blacklistLevels: [
            { required: true, message: '请选择黑号等级', trigger: 'change' },
          ],
          remark: [
            {
              min: 1,
              max: 100,
              message: '长度在 1 到 100 个字符',
              trigger: 'blur',
            },
          ],
        },
      },
      blackList3: {
        formData: {
          blacklistType: '',
          blacklistLevel: '',
        },
        formRule: {
          blacklistType: [
            { required: true, message: '请选择投诉类型', trigger: 'change' },
          ],
          blacklistLevel: [
            { required: true, message: '请选择黑号等级', trigger: 'change' },
          ],
        },
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        tableData: [],
        title: '',
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //-------------   列表信息   --------------
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true //打开loading
      window.api.post(
        window.path.omcs + 'operatingblacklist/page',
        this.formBlackListObj,
        (res) => {
          this.tableDataObj.tableData = res.records
          this.tableDataObj.tablecurrent.totalRow = res.total
          this.tableDataObj.loading2 = false //关闭loading
        }
      )
    },
    //查询
    quenryBlack() {
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    hande: function (val) {
      if (val) {
        this.formBlackList.begTime = this.moment(val[0]).format('YYYY-MM-DD')
        this.formBlackList.endTime = this.moment(val[1]).format('YYYY-MM-DD')
      } else {
        this.formBlackList.begTime = ''
        this.formBlackList.endTime = ''
      }
    },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.formBlackListObj.pageSize = size
      this.getTableDtate()
    },
    //导入
    exportBlacks() {
      this.SigDialogVisible = true
    },
    //改变上传文件
    handleChange(file, fileList) {
      let endingCode = file.name //结尾字符
      this.endingName = endingCode.slice(
        endingCode.lastIndexOf('.') + 1,
        endingCode.length
      )
      let isStyle = false //文件格式
      let fileStyle = ['txt']
      for (let i = 0; i < fileStyle.length; i++) {
        if (this.endingName === fileStyle[i]) {
          isStyle = true
          break
        }
      }
      if (isStyle == true) {
        this.fileList = fileList
      } else {
        this.fileList = []
        this.$message({
          message: '只能上传.txt 结尾的文件！',
          type: 'warning',
        })
      }
    },
    hangSelect(row, index) {
      this.selectFlag = 1
      this.selectIndex = index
      this.blackListType = row.blacklistType
      console.log(row, 'row')
    },
    //移除上传文件
    handleRemove(file, fileList) {
      this.fileList = []
    },
    //上传成功
    handleAvatarSuccess(res, file) {
      console.log(res)
      this.SigDialogVisible = false
      if (res.code == 200) {
        this.$message({
          message: res.msg,
          type: 'success',
        })
        this.getTableDtate()
      } else {
        this.$message.error(res.data)
      }
    },
    //上传文件过多提示
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      )
    },
    //确定上传
    signature_add(formName) {
      this.$refs[formName].validate((valid) => {
        //表单数据验证
        if (valid) {
          if (this.fileList.length != 0) {
            this.$refs.uploading.submit()
          } else {
            this.$message({
              message: '选择上传文件！',
              type: 'warning',
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.formBlackListObj.currentPage = currentPage
      this.getTableDtate()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.formBlackList.begTime = ''
      this.formBlackList.endTime = ''
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].blacklistId)
      }
      this.selectId = selectId.join(',')
    },
    //-------------   列表信息   --------------

    //-------------   新增功能开始   --------------
    blacklist_ok_add(formName) {
      //提交
      this.$refs[formName].validate((valid) => {
        //表单数据验证
        if (valid) {
          this.$confirm('确定新增?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              let params = JSON.parse(JSON.stringify(this.blackList.formData))
              params.blacklistLevels = this.blackList.formData.blacklistLevels.join(',')
              window.api.post(
                window.path.omcs + 'operatingblacklist',
                params,
                (res) => {
                  if (res.code == 200) {
                    this.getTableDtate()
                    this.blackListDialog = false //隐藏弹窗
                    this.$message({
                      message: '操作完成',
                      type: 'success',
                    })
                  } else {
                    this.$message({
                      message: res.msg,
                      type: 'warning',
                    })
                  }
                }
              )
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作',
              })
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handBlacklistType(row) {
      console.log(row, 'row')
      window.api.post(
        window.path.omcs +
          'operatingblacklist/updateType/' +
          row.blacklistId +
          '/' +
          this.blackListType,
        {},
        (res) => {
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.getTableDtate()
            this.selectFlag = 0
          }
        }
      )
    },
    delet(row) {
      this.$confirms.confirmation(
        'delete',
        '此操作将解除状态, 是否继续?',
        window.path.omcs + 'operatingblacklist/' + row.blacklistId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    qiyong(row) {
      this.$confirms.confirmation(
        'put',
        '此操作将启用状态, 是否继续?',
        window.path.omcs + 'operatingblacklist/' + row.blacklistId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    // //-------------  新增功能结束  --------------
    batchDeletion() {
      //批量
      this.$confirms.confirmation(
        'delete',
        '此操作将解除状态, 是否继续?',
        window.path.omcs + 'operatingblacklist/' + this.selectId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    batchqiyong() {
      //批量
      this.$confirms.confirmation(
        'put',
        '此操作将启用状态, 是否继续?',
        window.path.omcs + 'operatingblacklist/' + this.selectId,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
  },
  mounted() {
    this.header = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  watch: {
    //监听查询框对象的变化
    // formBlackListObj: {
    //   handler() {
    //     this.getTableDtate();
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    blackListDialog(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.blackList.resetFields() //清空表单
      }
    },
    //监听导入弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.$refs.signatureFrom.resetFields() //清空表单
        this.fileList = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.black_type {
  cursor: pointer;
  color: #409eff;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>