<template>
  <div class="signature-container">
    <!-- 查询表单 -->
    <el-form :inline="true" :model="queryForm" :rules="rules" ref="queryFormRef" class="query-form">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="queryForm.username" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="签名" prop="signature">
        <el-input v-model="queryForm.signature" placeholder="请输入签名" clearable></el-input>
      </el-form-item>
      <el-form-item class="form-buttons">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 无数据提示 -->
    <el-empty v-if="!signatureData" v-loading="loading" description="暂无数据，请进行查询" class="empty-placeholder"></el-empty>
    
    <div v-else>
      <!-- 签名详情 -->
      <el-card class="signature-card">
        <template #header>
          <div class="card-header">
            <span>签名详情</span>
            <div v-if="queryTime" class="query-time">
              <el-icon><Clock /></el-icon>
              <span>查询时间：{{ queryTime }}</span>
            </div>
          </div>
        </template>
        
        <!-- 基础信息展示 -->
        <div class="signature-info-wrapper">
          <div class="basic-info-card">
            <div class="user-info">
              <div class="avatar-section">
                <el-icon size="32" color="#409eff"><User /></el-icon>
              </div>
              <div class="user-details">
                <div class="username">{{ signatureData.username }}</div>
                <div class="signature-display">
                  <span class="signature-label">签名：</span>
                  <span class="signature-content">{{ signatureData.signature }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 运营商状态卡片 -->
          <div class="operators-grid">
            <div class="operator-card" :class="{ 'success': signatureData.yd==1, 'danger': signatureData.yd==0 }">
              <div class="operator-header">
                <i class="iconfont icon-yidong operator-card-icon mobile-card-icon"></i>
                <span class="operator-name">中国移动</span>
              </div>
              <div class="operator-status-content">
                <div v-if="signatureData.yd==1 && signatureData.ydAvalibleType" class="available-types">
                  <div class="type-tags">
                    <el-tag 
                      v-for="type in signatureData.ydAvalibleType.split(',')" 
                      :key="type" 
                      type="success" 
                      size="default" 
                      effect="light"
                      class="type-tag"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
                <el-tag v-else :type="signatureData.yd==1 ? 'success' : 'danger'" size="default" effect="light">
                  {{ signatureData.yd==1 ? '已实名' : '未实名' }}
                </el-tag>
                <div v-if="signatureData.ydReason" class="status-reason">
                  <el-icon size="14"><Warning /></el-icon>
                  {{ signatureData.ydReason }}
                </div>
              </div>
            </div>
            
            <div class="operator-card" :class="{ 'success': signatureData.lt==1, 'danger': signatureData.lt==0 }">
              <div class="operator-header">
                <i class="iconfont icon-liantong operator-card-icon unicom-card-icon"></i>
                <span class="operator-name">中国联通</span>
              </div>
              <div class="operator-status-content">
                <div v-if="signatureData.lt==1 && signatureData.ltAvalibleType" class="available-types">
                  <div class="type-tags">
                    <el-tag 
                      v-for="type in signatureData.ltAvalibleType.split(',')" 
                      :key="type" 
                      type="success" 
                      size="default" 
                      effect="light"
                      class="type-tag"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
                <el-tag v-else :type="signatureData.lt==1 ? 'success' : 'danger'" size="default" effect="light">
                  {{ signatureData.lt==1 ? '已实名' : '未实名' }}
                </el-tag>
                <div v-if="signatureData.ltReason" class="status-reason">
                  <el-icon size="14"><Warning /></el-icon>
                  {{ signatureData.ltReason }}
                </div>
              </div>
            </div>
            
            <div class="operator-card" :class="{ 'success': signatureData.dx==1, 'danger': signatureData.dx==0 }">
              <div class="operator-header">
                <i class="iconfont icon-dianxin operator-card-icon telecom-card-icon"></i>
                <span class="operator-name">中国电信</span>
              </div>
              <div class="operator-status-content">
                <div v-if="signatureData.dx==1 && signatureData.dxAvalibleType" class="available-types">
                  <div class="type-tags">
                    <el-tag 
                      v-for="type in signatureData.dxAvalibleType.split(',')" 
                      :key="type" 
                      type="success" 
                      size="default" 
                      effect="light"
                      class="type-tag"
                    >
                      {{ type.trim() }}
                    </el-tag>
                  </div>
                </div>
                <el-tag v-else :type="signatureData.dx==1 ? 'success' : 'danger'" size="default" effect="light">
                  {{ signatureData.dx==1 ? '已实名' : '未实名' }}
                </el-tag>
                <div v-if="signatureData.dxReason" class="status-reason">
                  <el-icon size="14"><Warning /></el-icon>
                  {{ signatureData.dxReason }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 引流信息报备进展 -->
      <el-card class="drainage-card" v-if="signatureData && signatureData.drainageInfo">
        <template #header>
          <div class="card-header">
            <div class="header-left">
              <span>引流信息报备状态</span>
              <el-tag size="small" type="info" class="classification-tag">按运营商分类显示</el-tag>
            </div>
            <div v-if="queryTime" class="query-time">
              <el-icon><Clock /></el-icon>
              <span>查询时间：{{ queryTime }}</span>
            </div>
          </div>
        </template>
        
        <!-- 全局筛选区域 -->
        <div class="global-filter-section">
          
          <!-- 全局筛选条件 -->
          <div class="global-filters">
            <el-form :inline="true" class="global-filter-form">
              <el-form-item label="号码">
                <el-input 
                  v-model="globalFilters.phone" 
                  placeholder="精确匹配号码" 
                  clearable
                  style="width: 140px"
                />
              </el-form-item>
              <el-form-item label="域名链接" class="domain-item">
                <el-input 
                  v-model="globalFilters.domain" 
                  placeholder="模糊搜索域名/链接" 
                  clearable
                  style="width: 140px"
                />
              </el-form-item>
              <el-form-item label="状态">
                <el-select 
                  v-model="globalFilters.status" 
                  placeholder="选择状态" 
                  clearable
                  style="width: 120px"
                >
                  <el-option label="待报备" value="pending"></el-option>
                  <el-option label="报备中" value="processing"></el-option>
                  <el-option label="报备成功" value="approved"></el-option>
                  <el-option label="报备失败" value="rejected"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围" class="date-range-item">
                <el-date-picker
                  v-model="globalFilters.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 240px"
                  @change="handleDateRangeChange('global', $event)"
                />
              </el-form-item>
              <el-form-item>
                <el-button @click="clearFilters('global')">清空</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 横向运营商布局 -->
        <div class="drainage-content">
          <div class="operators-horizontal-layout">
            <!-- 中国移动 -->
            <div class="operator-section">
              <div class="operator-header-horizontal">
                <div class="operator-info">
                  <i class="iconfont icon-yidong operator-icon mobile-icon"></i>
                  <span class="operator-name">中国移动</span>
                  <el-badge 
                    :value="getFilteredOperatorBindings('mobile').length" 
                    :type="getOperatorBadgeType('mobile')"
                    :hidden="getFilteredOperatorBindings('mobile').length === 0"
                    class="operator-badge"
                  />
                </div>
                <div class="operator-actions">
                  <span class="summary-text">{{ getFilteredOperatorSummaryText('mobile') }}</span>
                  <el-button 
                    v-if="!filterVisible.mobile"
                    size="small" 
                    type="primary" 
                    plain
                    @click="toggleFilter('mobile')"
                    class="filter-toggle-btn"
                  >
                    <el-icon><Search /></el-icon>
                    筛选
                  </el-button>
                  <el-button 
                    v-else
                    size="small" 
                    type="info" 
                    plain
                    @click="toggleFilter('mobile')"
                    class="filter-toggle-btn"
                  >
                    <el-icon><ArrowUp /></el-icon>
                    隐藏筛选
                  </el-button>
                </div>
              </div>
              
              <!-- 筛选条件 -->
              <transition name="filter-slide">
                <div v-show="filterVisible.mobile" class="operator-filters">
                  <el-form :inline="true" size="small" class="filter-form">
                    <el-form-item label="号码">
                      <el-input 
                        v-model="filters.mobile.phone" 
                        placeholder="精确匹配号码" 
                        clearable
                        style="width: 140px"
                      />
                    </el-form-item>
                    <el-form-item label="链接">
                      <el-input 
                        v-model="filters.mobile.domain" 
                        placeholder="模糊搜索链接" 
                        clearable
                        style="width: 140px"
                      />
                    </el-form-item>
                    <el-form-item label="状态">
                      <el-select 
                        v-model="filters.mobile.status" 
                        placeholder="选择状态" 
                        clearable
                        size="small"
                        style="width: 120px"
                      >
                        <el-option label="待报备" value="pending"></el-option>
                        <el-option label="报备中" value="processing"></el-option>
                        <el-option label="报备成功" value="approved"></el-option>
                        <el-option label="报备失败" value="rejected"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="时间范围" class="date-range-item">
                      <el-date-picker
                        v-model="filters.mobile.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 240px"
                        @change="handleDateRangeChange('mobile', $event)"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button size="small" @click="clearFilters('mobile')">清空</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </transition>
              
              <div class="operator-content-horizontal">
                <div class="binding-grid-horizontal">
                  <div 
                    v-for="(bindingPair, index) in getFilteredOperatorBindings('mobile')"
                    :key="`mobile-${index}`"
                    class="binding-card-simple"
                    :class="getBindingCardClass(bindingPair)"
                  >
                    <!-- 简化的状态头部 -->
                    <div class="binding-header-simple">
                      <el-tag 
                        :type="getBindingStatusType(bindingPair.overallStatus)" 
                        size="small"
                        effect="light"
                      >
                        {{ getBindingStatusText(bindingPair.overallStatus) }}
                      </el-tag>
                      <span class="binding-time-simple">{{ bindingPair.submitTime }}</span>
                    </div>

                    <!-- 简化的内容区域 -->
                    <div class="binding-content-simple">
                      <div class="binding-row">
                        <span class="label-simple">{{ bindingPair.rawData && bindingPair.rawData.operator === 1 ? '链接' : '域名' }}:</span>
                        <span class="value-simple">{{ bindingPair.domain.name || '暂无' }}</span>
                      </div>
                      <div class="binding-row">
                        <span class="label-simple">号码:</span>
                        <span class="value-simple">{{ bindingPair.phone.number || '暂无' }}</span>
                      </div>
                      
                      <!-- 更新时间 -->
                      <div v-if="bindingPair.updateTime || (bindingPair.rawData && bindingPair.rawData.updateStatusTime)" class="binding-update-time">
                        <span class="update-time-simple">更新：{{ getDisplayUpdateTime(bindingPair) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <el-empty v-if="getFilteredOperatorBindings('mobile').length === 0" description="暂无符合条件的中国移动报备记录" :image-size="60" />
              </div>
            </div>

            <!-- 中国联通 -->
            <div class="operator-section">
              <div class="operator-header-horizontal">
                <div class="operator-info">
                  <i class="iconfont icon-liantong operator-icon unicom-icon"></i>
                  <span class="operator-name">中国联通</span>
                  <el-badge 
                    :value="getFilteredOperatorBindings('unicom').length" 
                    :type="getOperatorBadgeType('unicom')"
                    :hidden="getFilteredOperatorBindings('unicom').length === 0"
                    class="operator-badge"
                  />
                </div>
                <div class="operator-actions">
                  <span class="summary-text">{{ getFilteredOperatorSummaryText('unicom') }}</span>
                  <el-button 
                    v-if="!filterVisible.unicom"
                    size="small" 
                    type="primary" 
                    plain
                    @click="toggleFilter('unicom')"
                    class="filter-toggle-btn"
                  >
                    <el-icon><Search /></el-icon>
                    筛选
                  </el-button>
                  <el-button 
                    v-else
                    size="small" 
                    type="info" 
                    plain
                    @click="toggleFilter('unicom')"
                    class="filter-toggle-btn"
                  >
                    <el-icon><ArrowUp /></el-icon>
                    隐藏筛选
                  </el-button>
                </div>
              </div>
              
              <!-- 筛选条件 -->
              <transition name="filter-slide">
                <div v-show="filterVisible.unicom" class="operator-filters">
                  <el-form :inline="true" size="small" class="filter-form">
                    <el-form-item label="号码">
                      <el-input 
                        v-model="filters.unicom.phone" 
                        placeholder="精确匹配号码" 
                        clearable
                        style="width: 140px"
                      />
                    </el-form-item>
                    <el-form-item label="域名">
                      <el-input 
                        v-model="filters.unicom.domain" 
                        placeholder="模糊搜索域名" 
                        clearable
                        style="width: 140px"
                      />
                    </el-form-item>
                    <el-form-item label="状态">
                      <el-select 
                        v-model="filters.unicom.status" 
                        placeholder="选择状态" 
                        clearable
                        size="small"
                        style="width: 120px"
                      >
                        <el-option label="待报备" value="pending"></el-option>
                        <el-option label="报备中" value="processing"></el-option>
                        <el-option label="报备成功" value="approved"></el-option>
                        <el-option label="报备失败" value="rejected"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="时间范围" class="date-range-item">
                      <el-date-picker
                        v-model="filters.unicom.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 240px"
                        @change="handleDateRangeChange('unicom', $event)"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button size="small" @click="clearFilters('unicom')">清空</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </transition>
              
              <div class="operator-content-horizontal">
                <div class="binding-grid-horizontal">
                  <div 
                    v-for="(bindingPair, index) in getFilteredOperatorBindings('unicom')"
                    :key="`unicom-${index}`"
                    class="binding-card-simple"
                    :class="getBindingCardClass(bindingPair)"
                  >
                    <!-- 简化的状态头部 -->
                    <div class="binding-header-simple">
                      <el-tag 
                        :type="getBindingStatusType(bindingPair.overallStatus)" 
                        size="small"
                        effect="light"
                      >
                        {{ getBindingStatusText(bindingPair.overallStatus) }}
                      </el-tag>
                      <span class="binding-time-simple">{{ bindingPair.submitTime }}</span>
                    </div>

                    <!-- 简化的内容区域 -->
                    <div class="binding-content-simple">
                      <div class="binding-row">
                        <span class="label-simple">{{ bindingPair.rawData && bindingPair.rawData.operator === 1 ? '链接' : '域名' }}:</span>
                        <span class="value-simple">{{ bindingPair.domain.name || '暂无' }}</span>
                      </div>
                      <div class="binding-row">
                        <span class="label-simple">号码:</span>
                        <span class="value-simple">{{ bindingPair.phone.number || '暂无' }}</span>
                      </div>
                      
                      <!-- 更新时间 -->
                      <div v-if="bindingPair.updateTime || (bindingPair.rawData && bindingPair.rawData.updateStatusTime)" class="binding-update-time">
                        <span class="update-time-simple">更新：{{ getDisplayUpdateTime(bindingPair) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <el-empty v-if="getFilteredOperatorBindings('unicom').length === 0" description="暂无符合条件的中国联通报备记录" :image-size="60" />
              </div>
            </div>

            <!-- 中国电信 -->
            <div class="operator-section">
              <div class="operator-header-horizontal">
                <div class="operator-info">
                  <i class="iconfont icon-dianxin operator-icon telecom-icon"></i>
                  <span class="operator-name">中国电信</span>
                  <el-badge 
                    :value="getFilteredOperatorBindings('telecom').length" 
                    :type="getOperatorBadgeType('telecom')"
                    :hidden="getFilteredOperatorBindings('telecom').length === 0"
                    class="operator-badge"
                  />
                </div>
                <div class="operator-actions">
                  <span class="summary-text">{{ getFilteredOperatorSummaryText('telecom') }}</span>
                  <el-button 
                    v-if="!filterVisible.telecom"
                    size="small" 
                    type="primary" 
                    plain
                    @click="toggleFilter('telecom')"
                    class="filter-toggle-btn"
                  >
                    <el-icon><Search /></el-icon>
                    筛选
                  </el-button>
                  <el-button 
                    v-else
                    size="small" 
                    type="info" 
                    plain
                    @click="toggleFilter('telecom')"
                    class="filter-toggle-btn"
                  >
                    <el-icon><ArrowUp /></el-icon>
                    隐藏筛选
                  </el-button>
                </div>
              </div>
              
              <!-- 筛选条件 -->
              <transition name="filter-slide">
                <div v-show="filterVisible.telecom" class="operator-filters">
                  <el-form :inline="true" size="small" class="filter-form">
                    <el-form-item label="号码">
                      <el-input 
                        v-model="filters.telecom.phone" 
                        placeholder="精确匹配号码" 
                        clearable
                        style="width: 140px"
                      />
                    </el-form-item>
                    <el-form-item label="域名">
                      <el-input 
                        v-model="filters.telecom.domain" 
                        placeholder="模糊搜索域名" 
                        clearable
                        style="width: 140px"
                      />
                    </el-form-item>
                    <el-form-item label="状态">
                      <el-select 
                        v-model="filters.telecom.status" 
                        placeholder="选择状态" 
                        clearable
                        size="small"
                        style="width: 120px"
                      >
                        <el-option label="待报备" value="pending"></el-option>
                        <el-option label="报备中" value="processing"></el-option>
                        <el-option label="报备成功" value="approved"></el-option>
                        <el-option label="报备失败" value="rejected"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="时间范围" class="date-range-item">
                      <el-date-picker
                        v-model="filters.telecom.dateRange"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 240px"
                        @change="handleDateRangeChange('telecom', $event)"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button size="small" @click="clearFilters('telecom')">清空</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </transition>
              
              <div class="operator-content-horizontal">
                <div class="binding-grid-horizontal">
                  <div 
                    v-for="(bindingPair, index) in getFilteredOperatorBindings('telecom')"
                    :key="`telecom-${index}`"
                    class="binding-card-simple"
                    :class="getBindingCardClass(bindingPair)"
                  >
                    <!-- 简化的状态头部 -->
                    <div class="binding-header-simple">
                      <el-tag 
                        :type="getBindingStatusType(bindingPair.overallStatus)" 
                        size="small"
                        effect="light"
                      >
                        {{ getBindingStatusText(bindingPair.overallStatus) }}
                      </el-tag>
                      <span class="binding-time-simple">{{ bindingPair.submitTime }}</span>
                    </div>

                    <!-- 简化的内容区域 -->
                    <div class="binding-content-simple">
                      <div class="binding-row">
                        <span class="label-simple">{{ bindingPair.rawData && bindingPair.rawData.operator === 1 ? '链接' : '域名' }}:</span>
                        <span class="value-simple">{{ bindingPair.domain.name || '暂无' }}</span>
                      </div>
                      <div class="binding-row">
                        <span class="label-simple">号码:</span>
                        <span class="value-simple">{{ bindingPair.phone.number || '暂无' }}</span>
                      </div>
                      
                      <!-- 更新时间 -->
                      <div v-if="bindingPair.updateTime || (bindingPair.rawData && bindingPair.rawData.updateStatusTime)" class="binding-update-time">
                        <span class="update-time-simple">更新：{{ getDisplayUpdateTime(bindingPair) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <el-empty v-if="getFilteredOperatorBindings('telecom').length === 0" description="暂无符合条件的中国电信报备记录" :image-size="60" />
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, ArrowUp } from '@element-plus/icons-vue'
import axios from 'axios'
import getNoce from '../../../plugins/getNoce'

// 创建独立的 axios 实例
const instance = axios.create({
  baseURL: window.path.omcs,
  timeout: 10000
})

// 请求拦截器
instance.interceptors.request.use(
  async config => {
    const nonce = await getNoce.useNonce();
    config.headers['Once'] = nonce
    return config
  },
  error => {
    // console.log(error, 'error');
    
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response) {
      if (error.response.status == 403) {
        ElMessage.error(error.response.data.msg || error.response.data.message)
        localStorage.removeItem("nonce_list");
      }else{
        ElMessage.error(error.response.data.msg || error.response.data.message)
      }
    }
    // ElMessage.error('请求失败，请稍后重试')
    return Promise.reject(error)
  }
)

// 表单引用
const queryFormRef = ref(null)

// 查询表单
const queryForm = reactive({
  username: '',
  signature: ''
})

// 表单验证规则
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  signature: [
    { required: true, message: '请输入签名', trigger: 'blur' }
  ]
})

// 加载状态
const loading = ref(false)

// 签名详情数据
const signatureData = ref(null)

// 查询时间
const queryTime = ref('')

// 筛选条件
const filters = reactive({
  mobile: {
    phone: '',
    domain: '',
    status: '',
    dateRange: [],
    actualDateRange: null
  },
  unicom: {
    phone: '',
    domain: '',
    status: '',
    dateRange: [],
    actualDateRange: null
  },
  telecom: {
    phone: '',
    domain: '',
    status: '',
    dateRange: [],
    actualDateRange: null
  }
})

// 筛选展示状态
const filterVisible = reactive({
  mobile: false,
  unicom: false,
  telecom: false
})

// 全局筛选条件
const globalFilters = reactive({
  phone: '',
  domain: '',
  status: '',
  dateRange: [],
  actualDateRange: null
})

// 组件挂载后执行
onMounted(() => {
  // 页面加载时不自动查询
})

// 查询按钮
const handleQuery = () => {
  queryFormRef.value.validate((valid) => {
    if (valid) {
      getSignatureDetail()
    } else {
      ElMessage.warning('请完善必填项')
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  queryFormRef.value.resetFields()
  signatureData.value = null
}

// 获取签名详情数据
const getSignatureDetail = async () => {
  try {
    loading.value = true
    
    // 记录查询时间
    const now = new Date()
    queryTime.value = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    
    // 真实接口调用
    const params = {
      username: queryForm.username,
      signatures: queryForm.signature ? [queryForm.signature] : []
    }
    const res = await instance.post('openApi/querySignatureRealNameStatus', params)
    
    if (res?.code === 200) {
      if (res.data?.length > 0) {
        // 只展示第一条记录作为详情
        signatureData.value = res.data[0]
        
        // 如果接口返回的username为null，使用查询表单中的用户名
        if (!signatureData.value.username) {
          signatureData.value.username = queryForm.username
        }
        
        // 处理引流信息数据mobileAndLinkResult
        if (signatureData.value.mobileAndLinkResult) {
          signatureData.value.drainageInfo = processMobileAndLinkResult(signatureData.value.mobileAndLinkResult)
        }
        
        ElMessage.success('查询成功')
      } else {
        ElMessage.info('未找到相关签名信息')
        signatureData.value = null
      }
    } else {
      ElMessage.error(res?.msg || '获取签名详情失败')
      signatureData.value = null
    }
  } catch (error) {
    console.error('获取签名详情出错:', error)
    ElMessage.error('查询失败，请稍后重试')
    signatureData.value = null
  } finally {
    loading.value = false
  }
}

// 处理mobileAndLinkResult数据
const processMobileAndLinkResult = (mobileAndLinkResult) => {
  const result = {
    mobile: [],
    unicom: [],
    telecom: []
  }
  
  // 运营商映射 1:移动 2:联通 3:电信
  const operatorMap = {
    1: 'mobile',
    2: 'unicom', 
    3: 'telecom'
  }
  
  // 遍历每个运营商的数据
  Object.keys(mobileAndLinkResult).forEach(operatorKey => {
    const operatorName = operatorMap[operatorKey]
    if (operatorName && mobileAndLinkResult[operatorKey]) {
      result[operatorName] = mobileAndLinkResult[operatorKey].map(item => ({
        id: item.id,
        submitTime: item.createTime ? formatDateTime(item.createTime) : '',
        updateTime: item.updateTime && item.updateTime !== item.createTime ? formatDateTime(item.updateTime) : '',
        overallStatus: getOverallStatusFromStatus(item.status),
        // 根据运营商编号显示不同的域名/链接信息
        // operator=1(移动)时显示链接，operator=2,3(联通电信)时显示域名
        domain: {
          name: item.operator === 1 ? (item.link || '') : (item.domain || ''),
          status: getItemStatusFromStatus(item.status),
          reason: ''
        },
        phone: {
          number: item.phone || '',
          status: getItemStatusFromStatus(item.status),
          reason: ''
        },
        // 保存原始数据以备使用
        rawData: item
      }))
    }
  })
  
  return result
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (e) {
    return dateStr
  }
}

// 根据status获取整体状态
const getOverallStatusFromStatus = (status) => {
  const statusMap = {
    0: 'pending',    // 待报备
    1: 'processing', // 报备中 
    2: 'approved',   // 报备成功
    3: 'rejected'    // 报备失败
  }
  return statusMap[status] || 'pending'
}

// 根据status获取单项状态
const getItemStatusFromStatus = (status) => {
  return getOverallStatusFromStatus(status)
}

// 获取审核状态显示文本
const getAuditStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '审核通过',
    3: '审核不通过'
  }
  return statusMap[status] || '未知状态'
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

// // 获取运营商状态显示文本
// const getOperatorStatusText = (status) => {
//   const statusMap = {
//     0: '未实名',
//     1: '已实名'
//   }
//   return statusMap[status] || '未知状态'
// }

// // 获取运营商状态类型
// const getOperatorStatusType = (status) => {
//   const typeMap = {
//     0: 'danger',
//     1: 'success'
//   }
//   return typeMap[status] || 'info'
// }

// 获取签名来源显示文本
const getSignatureTypeText = (type) => {
  const typeMap = {
    1: '企业名称',
    2: '事业单位',
    3: '商标',
    4: 'APP',
    5: '小程序',
    // 6: '公众号',
    7: '网站'
  }
  return typeMap[type] || ''
}

// 获取签名来源标签类型
const getSignatureTypeTag = (type) => {
  const typeMap = {
    1: 'primary',    // 企业名称
    2: 'info',       // 事业单位
    3: 'warning',    // 商标
    4: 'success',    // APP
    5: 'success',    // 小程序
    // 6: 'success',    // 公众号
    7: 'success'     // 网站
  }
  return typeMap[type] || 'info'
}

// 域名状态相关函数
const getDomainStatusText = (status) => {
  const statusMap = {
    'pending': '待报备',
    'processing': '报备中',
    'approved': '报备成功',
    'rejected': '报备失败'
  }
  return statusMap[status] || '未知状态'
}

const getDomainStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getDomainCardClass = (status) => {
  return {
    'status-pending': status === 'pending',
    'status-processing': status === 'processing',
    'status-approved': status === 'approved',
    'status-rejected': status === 'rejected'
  }
}

// 号码状态相关函数
const getPhoneStatusText = (status) => {
  const statusMap = {
    'pending': '待报备',
    'processing': '报备中',
    'approved': '报备成功',
    'rejected': '报备失败'
  }
  return statusMap[status] || '未知状态'
}

const getPhoneStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getPhoneCardClass = (status) => {
  return {
    'status-pending': status === 'pending',
    'status-processing': status === 'processing',
    'status-approved': status === 'approved',
    'status-rejected': status === 'rejected'
  }
}

// 绑定状态相关函数
const getBindingStatusText = (status) => {
  const statusMap = {
    'pending': '待报备',
    'processing': '报备中',
    'approved': '报备成功',
    'rejected': '报备失败'
  }
  return statusMap[status] || '未知状态'
}

const getBindingStatusType = (status) => {
  const typeMap = {
    'pending': 'info',
    'processing': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

const getBindingCardClass = (bindingPair) => {
  return {
    'binding-pending': bindingPair.overallStatus === 'pending',
    'binding-processing': bindingPair.overallStatus === 'processing',
    'binding-approved': bindingPair.overallStatus === 'approved',
    'binding-rejected': bindingPair.overallStatus === 'rejected'
  }
}

// 获取指定运营商的绑定记录
const getOperatorBindings = (operator) => {
  if (!signatureData.value || !signatureData.value.drainageInfo) {
    return []
  }
  return signatureData.value.drainageInfo[operator] || []
}

// 获取指定运营商的绑定记录数量
const getOperatorBindingCount = (operator) => {
  const bindings = getOperatorBindings(operator)
  return bindings.length
}

// 获取运营商徽章类型
const getOperatorBadgeType = (operator) => {
  const bindings = getOperatorBindings(operator)
  if (bindings.length === 0) return 'info'
  
  // 统计各种状态的数量
  const statusCounts = bindings.reduce((acc, binding) => {
    acc[binding.overallStatus] = (acc[binding.overallStatus] || 0) + 1
    return acc
  }, {})
  
  // 如果有任何审核通过的，显示成功
  if (statusCounts.approved > 0) return 'success'
  // 如果有处理中的，显示警告
  if (statusCounts.processing > 0) return 'warning'
  // 如果有驳回的，显示危险
  if (statusCounts.rejected > 0) return 'danger'
  // 其他情况显示信息
  return 'info'
}

// 获取运营商状态概要文本
const getOperatorSummaryText = (operator) => {
  const bindings = getOperatorBindings(operator)
  if (bindings.length === 0) return '暂无报备记录'
  
  return `共${bindings.length}条记录`
}

// 获取显示的更新时间
const getDisplayUpdateTime = (bindingPair) => {
  // 如果status不等于2且有updateStatusTime，优先显示updateStatusTime
  if (bindingPair.rawData && bindingPair.rawData.status !== 2 && bindingPair.rawData.updateStatusTime) {
    return formatDateTime(bindingPair.rawData.updateStatusTime)
  }
  // 否则显示updateTime
  return bindingPair.updateTime || '暂无'
}

// 获取过滤后的运营商绑定记录
const getFilteredOperatorBindings = (operator) => {
  const bindings = getOperatorBindings(operator)
  if (!bindings.length) return []
  
  const operatorFilter = filters[operator]
  const globalFilter = globalFilters
  
  return bindings.filter(binding => {
    // 运营商独立筛选优先级更高
    // 如果运营商有任何筛选条件，则只使用运营商筛选
    const hasOperatorFilter = operatorFilter.phone || operatorFilter.domain || operatorFilter.status || (operatorFilter.actualDateRange && operatorFilter.actualDateRange.length === 2)
    
    if (hasOperatorFilter) {
      // 使用运营商筛选
      // 手机号精确匹配
      if (operatorFilter.phone && binding.phone.number !== operatorFilter.phone) {
        return false
      }
      
      // 域名/链接模糊搜索
      if (operatorFilter.domain) {
        const searchTarget = binding.domain.name || ''
        if (!searchTarget.toLowerCase().includes(operatorFilter.domain.toLowerCase())) {
          return false
        }
      }
      
      // 状态筛选
      if (operatorFilter.status && binding.overallStatus !== operatorFilter.status) {
        return false
      }
      
      // 时间范围筛选
      if (operatorFilter.actualDateRange && operatorFilter.actualDateRange.length === 2) {
        const createTime = new Date(binding.rawData.createTime)
        const startTime = new Date(operatorFilter.actualDateRange[0])
        const endTime = new Date(operatorFilter.actualDateRange[1])
        
        if (createTime < startTime || createTime > endTime) {
          return false
        }
      }
    } else {
      // 使用全局筛选
      // 手机号精确匹配
      if (globalFilter.phone && binding.phone.number !== globalFilter.phone) {
        return false
      }
      
      // 域名/链接模糊搜索
      if (globalFilter.domain) {
        const searchTarget = binding.domain.name || ''
        if (!searchTarget.toLowerCase().includes(globalFilter.domain.toLowerCase())) {
          return false
        }
      }
      
      // 状态筛选
      if (globalFilter.status && binding.overallStatus !== globalFilter.status) {
        return false
      }
      
      // 时间范围筛选
      if (globalFilter.actualDateRange && globalFilter.actualDateRange.length === 2) {
        const createTime = new Date(binding.rawData.createTime)
        const startTime = new Date(globalFilter.actualDateRange[0])
        const endTime = new Date(globalFilter.actualDateRange[1])
        
        if (createTime < startTime || createTime > endTime) {
          return false
        }
      }
    }
    
    return true
  })
}

// 获取过滤后的运营商状态概要文本
const getFilteredOperatorSummaryText = (operator) => {
  const originalBindings = getOperatorBindings(operator)
  const filteredBindings = getFilteredOperatorBindings(operator)
  
  if (originalBindings.length === 0) return '暂无报备记录'
  
  if (filteredBindings.length === originalBindings.length) {
    return `共${originalBindings.length}条记录`
  } else {
    return `显示${filteredBindings.length}条 / 共${originalBindings.length}条`
  }
}

// 处理日期范围变化
const handleDateRangeChange = (operator, dateRange) => {
  if (dateRange && dateRange.length === 2) {
    // 开始时间设为当天0点
    const startDateTime = new Date(dateRange[0])
    startDateTime.setHours(0, 0, 0, 0)
    
    // 结束时间设为当天23:59:59
    const endDateTime = new Date(dateRange[1])
    endDateTime.setHours(23, 59, 59, 999)
    
    // 更新内部的实际时间范围（用于筛选）
    if (operator === 'global') {
      globalFilters.actualDateRange = [
        startDateTime.toISOString(),
        endDateTime.toISOString()
      ]
    } else {
      filters[operator].actualDateRange = [
        startDateTime.toISOString(),
        endDateTime.toISOString()
      ]
    }
  } else {
    if (operator === 'global') {
      globalFilters.actualDateRange = null
    } else {
      filters[operator].actualDateRange = null
    }
  }
}

// 切换筛选显示状态
const toggleFilter = (operator) => {
  filterVisible[operator] = !filterVisible[operator]
}

// 清空筛选条件
const clearFilters = (operator) => {
  if (operator === 'global') {
    globalFilters.phone = ''
    globalFilters.domain = ''
    globalFilters.status = ''
    globalFilters.dateRange = []
    globalFilters.actualDateRange = null
  } else {
    filters[operator].phone = ''
    filters[operator].domain = ''
    filters[operator].status = ''
    filters[operator].dateRange = []
    filters[operator].actualDateRange = null
  }
}

</script>

<style scoped lang="less">
.signature-container {
  padding: 10px;
  max-width: 100%;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
  overflow-y: auto;
  height: 100vh;
}

.query-form {
  margin-bottom: 12px;
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.query-form:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.query-form :deep(.el-form-item) {
  margin-bottom: 12px;
}

.query-form :deep(.el-form-item__label) {
  width: 80px;
  text-align: right;
  font-weight: 500;
  color: #606266;
}

.query-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.query-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.query-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.form-buttons :deep(.el-button) {
  padding: 8px 18px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.form-buttons :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
}

.form-buttons :deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 空数据占位符样式 */
.empty-placeholder {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  padding: 40px;
  min-height: 200px;
  margin-bottom: 12px;
}

.signature-card {
  margin-top: 10px;
}

.signature-card :deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.classification-tag {
  margin-left: 8px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
}

.query-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(144, 147, 153, 0.2);
}

.query-time .el-icon {
  font-size: 12px;
}

/* 签名详情新样式 */
.signature-info-wrapper {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  padding: 15px;
}

.basic-info-card {
  flex: 1;
  min-width: 280px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid #dee2e6;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-section {
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  border: 3px solid #fff;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.signature-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signature-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.signature-content {
  background: #fff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
  border: 2px solid #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.operators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
  flex: 2;
  min-width: 320px;
}

.operator-card {
  background: #fff;
  border-radius: 12px;
  padding: 12px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.operator-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: #ebeef5;
  transition: all 0.3s ease;
}

.operator-card.success::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.operator-card.danger::before {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.operator-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.operator-card.success:hover {
  border-color: #67c23a;
  box-shadow: 0 8px 25px rgba(103, 194, 58, 0.2);
}

.operator-card.danger:hover {
  border-color: #f56c6c;
  box-shadow: 0 8px 25px rgba(245, 108, 108, 0.2);
}

.operator-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.operator-card-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
}

.mobile-card-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.3);
}

.unicom-card-icon {
  background: linear-gradient(135deg, #f56c6c, #e85656);
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.3);
}

.telecom-card-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.3);
}

.operator-name {
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.operator-status-content {
  text-align: center;
}

.available-types {
  text-align: center;
}

.type-header {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.type-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px;
}

.type-tag {
  margin: 0;
  border-radius: 12px;
  font-weight: 500;
  background: #f0f9eb !important;
  border-color: #c2e7b0 !important;
  color: #67c23a !important;
}

.status-reason {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
  margin-top: 8px;
  padding: 6px 10px;
  background: #fef0f0;
  border-radius: 6px;
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.4;
}

:deep(.el-tag) {
  margin: 0 5px;
  padding: 4px 12px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-empty__description) {
  color: #909399;
}

/* 引流信息卡片样式 */
.drainage-card {
  margin-top: 15px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.drainage-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.drainage-card :deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 横向运营商布局样式 */
.operators-horizontal-layout {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.operator-section {
  flex: 1;
  min-width: 400px;
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.operator-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.operator-header-horizontal {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operator-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-toggle-btn {
  font-size: 12px;
  padding: 6px 12px;
  height: 28px;
  border-radius: 14px;
  transition: all 0.3s ease;
  border: 1px solid;
}

.filter-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.operator-content-horizontal {
  padding: 15px;
  background: #fafbfc;
  max-height: 600px;
  overflow-y: auto;
}

.binding-grid-horizontal {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  align-items: start;
}

/* 筛选区域样式 */
.operator-filters {
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  border-top: 1px solid #e4e7ed;
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 8px;
  margin-right: 15px;
}

.filter-form :deep(.el-form-item__label) {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
  width: 50px;
  text-align: right;
}

.filter-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.filter-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.filter-form :deep(.el-date-editor) {
  --el-date-editor-width: 280px;
}

.filter-form :deep(.el-date-editor .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

/* 时间范围组件优化 */
.date-range-item {
  white-space: nowrap;
}

.date-range-item :deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

.filter-form :deep(.el-date-editor--daterange) {
  width: 240px !important;
}

.filter-form :deep(.el-date-editor--daterange .el-input__wrapper) {
  padding: 1px 15px 1px 11px;
}

/* 域名链接项优化 */
.domain-item {
  white-space: nowrap;
}

.domain-item :deep(.el-form-item__label) {
  white-space: nowrap;
  min-width: 60px;
}

/* 筛选条件过渡动画 */
.filter-slide-enter-active,
.filter-slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.filter-slide-enter-from {
  opacity: 0;
  height: 0;
  padding-top: 0;
  padding-bottom: 0;
  border-top-width: 0;
  border-bottom-width: 0;
}

.filter-slide-leave-to {
  opacity: 0;
  height: 0;
  padding-top: 0;
  padding-bottom: 0;
  border-top-width: 0;
  border-bottom-width: 0;
}

.operator-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.operator-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  flex-shrink: 0;
}

.mobile-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  color: #fff;
}

.unicom-icon {
  background: linear-gradient(135deg, #f56c6c, #e85656);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
  color: #fff;
}

.telecom-icon {
  background: linear-gradient(135deg, #409eff, #337ecc);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  color: #fff;
}

.operator-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.operator-badge {
  margin-left: 8px;
}

.operator-summary {
  text-align: right;
}

.summary-text {
  font-size: 14px;
  color: #606266;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.operator-content {
  padding: 20px;
  background: #fafbfc;
}

/* 简化的绑定卡片样式 */
.binding-card-simple {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: auto;
}

.binding-card-simple::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  transition: all 0.3s ease;
}

.binding-card-simple.binding-pending::before {
  background: #909399;
}

.binding-card-simple.binding-processing::before {
  background: #e6a23c;
}

.binding-card-simple.binding-approved::before {
  background: #67c23a;
}

.binding-card-simple.binding-rejected::before {
  background: #f56c6c;
}

.binding-card-simple:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.binding-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f2f5;
}

.binding-time-simple {
  font-size: 11px;
  color: #909399;
}

.binding-content-simple {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.binding-row {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.label-simple {
  color: #606266;
  font-weight: 500;
  min-width: 50px;
  margin-right: 8px;
}

.value-simple {
  color: #303133;
  font-weight: 400;
  word-break: break-all;
  flex: 1;
}

.binding-update-time {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid #f5f7fa;
  text-align: right;
}

.update-time-simple {
  font-size: 11px;
  color: #909399;
}

/* 修改网格布局，让卡片更紧凑 */
.binding-grid-fixed {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
  align-items: start;
}

/* 固定宽度的绑定卡片 */
.binding-card-fixed {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 320px;
  display: flex;
  flex-direction: column;
}

.binding-card-fixed::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  transition: all 0.3s ease;
}

/* 绑定状态样式 */
.binding-card-fixed.binding-pending::before {
  background: linear-gradient(90deg, #909399, #a6a9ad);
}

.binding-card-fixed.binding-processing::before {
  background: linear-gradient(90deg, #e6a23c, #eebe77);
}

.binding-card-fixed.binding-approved::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.binding-card-fixed.binding-rejected::before {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.binding-card-fixed.binding-partial::before {
  background: linear-gradient(90deg, #e6a23c, #f0a932);
}

.binding-card-fixed:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

/* 全局筛选区域样式 */
.global-filter-section {
  border-bottom: 1px solid #e4e7ed;
  background: linear-gradient(135deg, #f0f4f8 0%, #e8f1f5 100%);
}

.global-filter-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid rgba(228, 231, 237, 0.6);
}

.global-filter-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 6px;
}

.global-filter-title::before {
  content: '';
  width: 4px;
  height: 4px;
  background: #409eff;
  border-radius: 50%;
}

.global-filters {
  padding: 15px 20px;
  background: #fafbfc;
  // border-bottom: 1px solid #e4e7ed;
}

/* 全局筛选表单样式 - 与查询表单保持一致 */
.global-filter-form {
  margin: 0;
}

.global-filter-form :deep(.el-form-item) {
  margin-bottom: 12px;
  margin-right: 15px;
}

.global-filter-form :deep(.el-form-item__label) {
  width: 80px;
  text-align: right;
  font-weight: 500;
  color: #606266;
  font-size: 14px;
}

.global-filter-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.global-filter-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.global-filter-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.global-filter-form :deep(.el-button) {
  padding: 8px 18px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.global-filter-form :deep(.el-date-editor) {
  --el-date-editor-width: 240px;
}

.global-filter-form :deep(.el-date-editor .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.global-filter-form :deep(.el-date-editor .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.drainage-content {
  padding: 0 15px;
}

.binding-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

/* 绑定卡片样式 */
.binding-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.binding-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  transition: all 0.3s ease;
}

.el-card {
  :deep(.el-card__body) {
    padding: 0 !important;
  }
}

/* 绑定状态样式 */
.binding-card.binding-pending::before {
  background: linear-gradient(90deg, #909399, #a6a9ad);
}

.binding-card.binding-processing::before {
  background: linear-gradient(90deg, #e6a23c, #eebe77);
}

.binding-card.binding-approved::before {
  background: linear-gradient(90deg, #67c23a, #85ce61);
}

.binding-card.binding-rejected::before {
  background: linear-gradient(90deg, #f56c6c, #f78989);
}

.binding-card.binding-partial::before {
  background: linear-gradient(90deg, #e6a23c, #f0a932);
}

.binding-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.binding-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.binding-time {
  font-size: 12px;
  color: #909399;
}

.pair-item {
  background: #fafbfc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 8px;
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
}

.pair-item:hover {
  background: #f5f7fa;
  border-color: #dcdfe6;
}

.pair-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.pair-label {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.pair-content {
  margin-left: 28px;
}

.pair-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  word-break: break-all;
}

.pair-reason {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f56c6c;
  background: #fef0f0;
  padding: 6px 10px;
  border-radius: 6px;
  line-height: 1.4;
}

.binding-connector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 12px 0;
  position: relative;
}

.connector-line {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #e6a23c, #f0a932);
  border-radius: 1px;
}

.connector-icon {
  position: absolute;
  background: #fff;
  border: 2px solid #e6a23c;
  border-radius: 50%;
  padding: 4px;
  color: #e6a23c;
}

.binding-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin: 16px 0 8px;
  padding: 10px;
  background: #f0f9ff;
  border-radius: 8px;
  color: #409eff;
  font-size: 13px;
  font-weight: 500;
}

.binding-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f2f5;
  text-align: center;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}


/* 时间线图标样式调整 */
:deep(.el-timeline-item__icon) {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-timeline-item__icon .el-icon) {
  font-size: 12px;
}

/* 标签样式优化 */
.step-details .el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 4px 8px;
  font-weight: 500;
}

/* 响应式设计 */
@media screen and (max-width: 767px) {
  .drainage-content {
    padding: 15px;
  }
  
  .section-title {
    font-size: 15px;
  }

  .binding-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .binding-card {
    padding: 16px;
  }

  .pair-item {
    padding: 12px;
  }

  .pair-name {
    font-size: 14px;
  }

  .pair-content {
    margin-left: 24px;
  }

  .binding-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .binding-time {
    order: -1;
  }

  /* 移动端签名详情优化 */
  .signature-info-wrapper {
    flex-direction: column;
    padding: 12px;
    gap: 15px;
  }

  .basic-info-card {
    min-width: unset;
    padding: 12px;
  }

  .user-info {
    gap: 12px;
  }

  .avatar-section {
    width: 50px;
    height: 50px;
  }

  .username {
    font-size: 16px;
  }

  .signature-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .signature-content {
    font-size: 13px;
    padding: 5px 10px;
  }

  .operators-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    min-width: unset;
  }

  .operator-card {
    padding: 10px;
  }

  .operator-name {
    font-size: 14px;
  }

  .status-reason {
    font-size: 11px;
    padding: 5px 8px;
  }

  /* 移动端横向布局优化 */
  .operators-horizontal-layout {
    flex-direction: column;
    gap: 15px;
  }

  .operator-section {
    min-width: unset;
    flex: none;
  }

  .operator-header-horizontal {
    padding: 12px 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .operator-info {
    width: 100%;
  }

  .operator-actions {
    width: 100%;
    justify-content: space-between;
  }

  .filter-toggle-btn {
    font-size: 11px;
    padding: 4px 8px;
    height: 24px;
  }

  .summary-text {
    font-size: 12px;
    padding: 4px 8px;
    display: inline-block;
  }

  .operator-content-horizontal {
    padding: 12px;
    max-height: 400px;
  }

  .binding-grid-horizontal {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  /* 移动端筛选区域优化 */
  .operator-filters {
    padding: 10px 15px;
  }

  .filter-form :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 10px;
    width: 100%;
  }

  .filter-form :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }

  .filter-form :deep(.el-form-item__label) {
    width: 60px;
    text-align: left;
  }

  .filter-form :deep(.el-date-editor) {
    width: 100% !important;
  }

  .filter-form :deep(.el-date-editor--daterange) {
    width: 100% !important;
  }

  .operator-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }

  .operator-name {
    font-size: 16px;
  }

  /* 移动端查询时间优化 */
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .classification-tag {
    margin-left: 0;
    font-size: 10px;
    padding: 1px 4px;
  }

  .query-time {
    font-size: 11px;
    padding: 3px 6px;
  }

  /* 移动端全局筛选优化 */
  .global-filter-header {
    padding: 10px 15px;
  }

  .global-filter-title {
    font-size: 13px;
  }

  .global-filters {
    padding: 10px 15px;
  }

  .domain-item :deep(.el-form-item__label) {
    min-width: 55px;
  }
}

@media screen and (max-width: 991px) and (min-width: 768px) {
  /* 平板端优化 */
  .operators-horizontal-layout {
    flex-direction: column;
    gap: 18px;
  }
  
  .operator-section {
    min-width: unset;
  }
  
  .operator-content-horizontal {
    max-height: 500px;
    padding: 12px;
  }
  
  .binding-grid-horizontal {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
  }
  
  .binding-card-simple {
    padding: 14px;
  }

  /* 平板端筛选区域优化 */
  .operator-filters {
    padding: 12px 18px;
  }

  .filter-form :deep(.el-form-item) {
    margin-bottom: 10px;
    margin-right: 12px;
  }

  .filter-form :deep(.el-date-editor) {
    width: 250px !important;
  }

  .filter-form :deep(.el-date-editor--daterange) {
    width: 220px !important;
  }
}

@media screen and (min-width: 768px) {
  .signature-container {
    padding: 30px;
  }

  .query-form {
    padding: 25px;
  }

  .empty-placeholder {
    padding: 60px;
    min-height: 300px;
  }

  .card-header {
    font-size: 18px;
  }

  .drainage-card {
    margin-top: 25px;
  }
}
</style>
