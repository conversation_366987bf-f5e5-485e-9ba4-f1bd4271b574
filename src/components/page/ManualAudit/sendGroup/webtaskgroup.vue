<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名称" prop="userName">
            <el-input
              v-model="formInline.userName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="活动名称" prop="groupName">
            <el-input
              v-model="formInline.groupName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!--分页-->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">创建活动</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="webtaskgroup"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handelSelection"
          @checkbox-change="handelSelection">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="用户名称" title="用户名称">
            <template v-slot="scope">
              <div>{{ scope.row.userName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="活动名称" title="活动名称">
            <template v-slot="scope">
              <div>{{ scope.row.groupName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>{{
                moment(scope.row.createTime).format('YYYY-MM-DD hh:mm:ss')
              }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="230" fixed="right">
            <template v-slot="scope">
              <el-button
                link
                @click="detailsRow(scope.$index, scope.row)"
                style="color: #409eff;"
                ><el-icon><EditPen /></el-icon>&nbsp;修改</el-button
              >
              <el-button
                link
                style="margin-left: 10px; color: red"
                @click="delState(scope.$index, scope.row)"
                ><el-icon><Delete /></el-icon>&nbsp;删除</el-button
              >
              <el-button
                link
                @click="settingState(scope.$index, scope.row)"
                style="margin-left: 10px; color: #409eff;"
                ><el-icon><Setting /></el-icon>&nbsp;号码池设置</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增栏目 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="用户名称" label-width="80px" prop="userName">
            <el-input
              :disabled="formop.id ? true : false"
              v-model="formop.userName"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="活动名称" label-width="80px" prop="groupName">
            <el-input
              v-model="formop.groupName"
              placeholder=""
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import TableTem from "../../../../publicComponents/TableTem";
// import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import moment from 'moment'
export default {
  name: 'webtaskgroup',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      titleMap: {
        add: '创建任务',
        edit: '修改任务',
      },
      id: '',
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        groupName: '',
        userName: '',
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        //存储查询数据
        groupName: '',
        userName: '',
        currentPage: 1,
        pageSize: 10,
      },
      //添加列表
      formop: {
        groupName: '',
        userName: '',
      },
      rules: {
        groupName: [
          {
            required: true,
            message: '任务名称不能为空',
            trigger: 'change',
          },
        ],
        userName: [
          {
            required: true,
            message: '用户名称不用为空',
            trigger: 'change',
          },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerwebtaskgroup/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    //列表复选框的值
    handelSelection(val) {
      //   console.log(val);
      //   let selectId = [];
      //   // let wordsCount = [];
      //   for (let i = 0; i < val.length; i++) {
      //     selectId.push(val[i].id);
      //     // wordsCount.push(val[i].wordsCount);
      //   }
      //   this.selectId = selectId.join(","); //批量操作选中id
      //   // this.wordsCount = wordsCount; //批量操作选中行的包含敏感词个数
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.omcs + 'consumerwebtaskgroup',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'warning',
                  })
                }
              }
            )
          } else {
            //编辑
            this.formop.id = this.id
            this.$confirms.confirmation(
              'put',
              '确认执行此操作吗？',
              window.path.omcs + 'consumerwebtaskgroup',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'success',
                  // })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  // this.$message({
                  //   message: res.msg,
                  //   type: 'warning',
                  // })
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    settingState(index, row) {
      console.log(row)
      this.$router.push({
        path: '/webtaskpool',
        query: {
          groupId: row.id,
          userName: row.userName,
        },
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.flagPut = false
      this.dialogStatus = 'add'
    },

    detailsRow(index, val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })
    },
    //操作状态功能（删除）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除此任务？',
        window.path.omcs + 'consumerwebtaskgroup/' + val.id,
        {},
        () => {
          this.gettableLIst()
        }
      )
      //   this.$confirms.confirmation(
      //     "delete",
      //     "确定删除此任务？",
      //     window.path.omcs + "consumerwebtaskgroup",
      //     {
      //         id:val.id
      //         // groupName:val.groupName,
      //         // updateName:val.updateName
      //     },
      //     (res) => {
      //       if (res.code == 200) {
      //         this.$message({
      //           message: res.msg,
      //           type: "success",
      //         });
      //         this.gettableLIst();
      //       } else {
      //         this.$message({
      //           message: res.msg,
      //           type: "warning",
      //         });
      //       }

      //     }
      //   );
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (val == false) {
        this.formop.idx = ''
        this.formop.name = ''
        this.formop.id = ''
        this.id = ''
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
