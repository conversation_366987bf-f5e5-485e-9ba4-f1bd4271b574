<template>
  <div class="container_left">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="群发链接" name="0">
        <div style="padding: 10px">
          <el-form
            :model="ruleForm"
            :inline="true"
            ref="ruleForm"
            class="demo-ruleForm"
            label-width="80px"
          >
            <el-form-item label="用户名称" prop="username">
              <el-input class="input-w" v-model="ruleForm.username"></el-input>
            </el-form-item>
            <el-form-item label="链接" prop="link">
              <el-input class="input-w" v-model="ruleForm.link"></el-input>
            </el-form-item>
            <el-form-item label="模板名称" prop="condition">
              <el-input
                class="input-w"
                v-model="ruleForm.condition"
                placeholder="支持模板名称/模板ID/链接ID"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="statusArr">
              <el-select
                class="input-w"
                multiple
                v-model="ruleForm.statusArr"
                placeholder="生成状态"
              >
                <el-option label="生成中" value="0"> </el-option>
                <el-option label="生效中" value="1"> </el-option>
                <el-option label="已失效" value="3"> </el-option>
                <el-option label="生成失败" value="4"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="生成时间" prop="time">
              <el-date-picker
                v-model="ruleForm.time"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <div>
              <el-button plain type="primary" @click="submitForm('ruleForm')"
                >查询</el-button
              >
              <el-button plain type="primary" @click="resetForm('ruleForm')"
                >重置</el-button
              >
            </div>
          </el-form>
        </div>
        <div style="padding: 0 10px"></div>
        <div style="padding: 10px">
          <!-- <el-table
            v-loading="tableData.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            :data="tableData.data"
            border
            stripe
          > -->

          <vxe-toolbar ref="toolbarRef1" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef1"
            id="yuexinLink1"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableData.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableData.data">

            <vxe-column field="用户名称" title="用户名称">
              <template v-slot="scope">
                <div>
                  {{ scope.row.username }}
                </div>
              </template>
            </vxe-column>
            <vxe-column field="链接ID" title="链接ID" width="120">
              <template v-slot="scope">
                <div>
                  {{ scope.row.id }}
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                          class="el-icon-document-copy" @click="handleCopy(scope.row.id, $event)"></i> -->
                  <CopyTemp :content="scope.row.id" />
                </div>
              </template>
            </vxe-column>
            <vxe-column field="模板ID" title="模板ID" width="190">
              <template v-slot="scope">
                <div>
                  <!-- <Tooltip v-if="scope.row.templateId" :content="scope.row.templateId"
                                          className="wrapper-text" effect="light">
                                      </Tooltip> -->
                  {{ scope.row.templateId }}
                  <!-- <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                          class="el-icon-document-copy" @click="handleCopy(scope.row.templateId, $event)"></i> -->
                  <CopyTemp :content="scope.row.templateId" />
                </div>
              </template>
            </vxe-column>
            <vxe-column field="模板名称" title="模板名称">
              <template v-slot="scope">
                <div
                  style="color: #409eff; cursor: pointer"
                  @click="handView(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.templateName"
                    :content="scope.row.templateName"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- {{
                                        scope.row.templateName
                                    }} -->
                </div>
              </template>
            </vxe-column>
            <vxe-column field="签名" title="签名">
              <template v-slot="scope">
                <div>{{ scope.row.smsSigns }}</div>
              </template>
            </vxe-column>
            <vxe-column field="解析次数上限" title="解析次数上限" width="120">
              <template v-slot="scope">
                <div>{{ scope.row.showTimes }}</div>
              </template>
            </vxe-column>
            <vxe-column field="已解析次数" title="已解析次数" width="130">
              <template v-slot="scope">
                <div>{{ scope.row.useTimes }}</div>
              </template>
            </vxe-column>
            <vxe-column field="剩余解析次数" title="剩余解析次数" width="130">
              <template v-slot="scope">
                <div>{{ scope.row.showTimes - scope.row.useTimes }}</div>
              </template>
            </vxe-column>
            <vxe-column field="短链地址" title="短链地址">
              <template v-slot="scope">
                <a
                  style="color: #409eff"
                  v-if="scope.row.resourceLink"
                  :href="'https://' + scope.row.resourceLink"
                  target="_blank"
                  rel="noopener noreferrer"
                  >{{ scope.row.resourceLink }}</a
                >
              </template>
            </vxe-column>
            <vxe-column field="短链生成时间" title="短链生成时间" width="120">
              <template v-slot="scope">
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
                </p>
              </template>
            </vxe-column>
            <vxe-column field="短链到期时间" title="短链到期时间" width="120">
              <template v-slot="scope">
                <p v-if="scope.row.expireTime">
                  {{ $parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.expireTime">
                  {{ $parseTime(scope.row.expireTime, '{h}:{i}:{s}') }}
                </p>
              </template>
            </vxe-column>
            <vxe-column field="短链状态" title="短链状态" width="120">
              <template v-slot="scope">
                <div
                  class="item_status"
                  style="
                    background: #f8eddd;
                    color: #ff8a00;
                    border: 1px solid #ffd8b4;
                  "
                  v-if="scope.row.status == 0"
                >
                  生成中
                </div>
                <div
                  class="item_status"
                  style="
                    background: #f5fff4;
                    color: #00c159;
                    border: 1px solid #a6edb1;
                  "
                  v-if="scope.row.status == 1"
                >
                  已生效
                </div>

                <el-tooltip
                  v-if="scope.row.status == 3"
                  effect="dark"
                  :content="scope.row.opinion"
                  placement="top-start"
                >
                  <div
                    class="item_status"
                    style="
                      background: #ffe7e7;
                      color: #ff6666;
                      border: 1px solid #fac2c2;
                    "
                  >
                    已到期
                  </div>
                </el-tooltip>
                <el-tooltip
                  v-if="scope.row.status == 2"
                  effect="dark"
                  :content="scope.row.opinion"
                  placement="top-start"
                >
                  <div
                    class="item_status"
                    style="
                      background: #ffe7e7;
                      color: #ff6666;
                      border: 1px solid #fac2c2;
                    "
                  >
                    生成失败
                  </div>
                </el-tooltip>
              </template>
            </vxe-column>
            <vxe-column field="备注" title="备注" width="130">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.remark"
                  :content="scope.row.remark"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
                <!-- <span>{{
                                        scope.row.remark
                                    }}</span> -->
              </template>
            </vxe-column>
          </vxe-table>
          <div class="paginationBox">
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="个性化链接" name="1">
        <div style="padding: 10px">
          <el-form
            :model="ruleForm"
            :inline="true"
            ref="ruleForm"
            class="demo-ruleForm"
            label-width="80px"
          >
            <el-form-item label="用户名称" prop="username">
              <el-input class="input-w" v-model="ruleForm.username"></el-input>
            </el-form-item>
            <el-form-item label="模板名称" prop="condition">
              <el-input
                class="input-w"
                v-model="ruleForm.condition"
                placeholder="支持模板名称/模板ID/链接ID"
              ></el-input>
            </el-form-item>
            <el-form-item label="状态" prop="statusArr">
              <el-select
                class="input-w"
                multiple
                v-model="ruleForm.statusArr"
                placeholder="生成状态"
              >
                <el-option label="生成中" value="0"> </el-option>
                <el-option label="生效中" value="1"> </el-option>
                <el-option label="已失效" value="3"> </el-option>
                <el-option label="生成失败" value="4"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="生成时间" prop="time">
              <el-date-picker
                v-model="ruleForm.time"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetimerange"
                range-separator="-"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button plain type="primary" @click="submitForm('ruleForm')"
                >查询</el-button
              >
              <el-button plain type="primary" @click="resetForm('ruleForm')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div style="padding: 0 10px">
          <!-- <el-table
            v-loading="tableData.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            :data="tableData.data"
            border
            stripe
          > -->

          <vxe-toolbar ref="toolbarRef2" custom>
            <template #buttons>
            </template>
          </vxe-toolbar>

          <vxe-table
            ref="tableRef2"
            id="yuexinLink2"
            border
            stripe
            :custom-config="customConfig"
            :column-config="{ resizable: true }"
            :row-config="{ isHover: true }"
            min-height="1"
            v-loading="tableData.loading2"
            element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            style="font-size: 12px;"
            :data="tableData.data">

            <vxe-column field="用户名称" title="用户名称">
              <template v-slot="scope">
                <div>{{ scope.row.username }}</div>
              </template>
            </vxe-column>
            <vxe-column field="链接ID" title="链接ID">
              <template v-slot="scope">
                <div>{{ scope.row.id }}</div>
              </template>
            </vxe-column>
            <vxe-column field="模板ID" title="模板ID">
              <template v-slot="scope">
                <Tooltip
                  v-if="scope.row.templateId"
                  :content="scope.row.templateId"
                  className="wrapper-text"
                  effect="light"
                >
                </Tooltip>
              </template>
            </vxe-column>
            <vxe-column field="模板名称" title="模板名称">
              <template v-slot="scope">
                <div
                  style="color: #409eff; cursor: pointer"
                  @click="handView(scope.row)"
                >
                  <Tooltip
                    v-if="scope.row.templateName"
                    :content="scope.row.templateName"
                    className="wrapper-text"
                    effect="light"
                  >
                  </Tooltip>
                  <!-- {{
                                        scope.row.templateName
                                    }} -->
                </div>
              </template>
            </vxe-column>
            <vxe-column field="签名" title="签名">
              <template v-slot="scope">
                <div>{{ scope.row.smsSigns }}</div>
              </template>
            </vxe-column>
            <vxe-column field="预扣解析数" title="预扣解析数" width="120">
              <template v-slot="scope">
                <div>{{ scope.row.showTimes }}</div>
              </template>
            </vxe-column>
            <vxe-column field="成功链接数" title="成功链接数" width="120">
              <template v-slot="scope">
                <div>{{ scope.row.successCount }}</div>
              </template>
            </vxe-column>
            <vxe-column field="失败链接数" title="失败链接数" width="120">
              <template v-slot="scope">
                <div>{{ scope.row.failCount }}</div>
              </template>
            </vxe-column>
            <vxe-column field="已解析次数" title="已解析次数" width="130">
              <template v-slot="scope">
                <div>{{ scope.row.useTimes }}</div>
              </template>
            </vxe-column>
            <vxe-column field="剩余解析次数" title="剩余解析次数" width="130">
              <template v-slot="scope">
                <div>{{ scope.row.successCount - scope.row.useTimes }}</div>
              </template>
            </vxe-column>
            <!-- <vxe-column field="" title="短链地址">
                                <template #default="scope">
                                    <el-button type="text" v-if="scope.row.status == 1 || scope.row.status == 3" link
                                        @click="downLoad(scope.row.id)">下载链接</el-button>
                                </template>
                            </vxe-column> -->
            <vxe-column field="短链生成时间" title="短链生成时间" width="120">
              <template v-slot="scope">
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}
                </p>
              </template>
            </vxe-column>
            <vxe-column field="短链到期时间" title="短链到期时间" width="120">
              <template v-slot="scope">
                <p v-if="scope.row.expireTime">
                  {{ $parseTime(scope.row.expireTime, '{y}-{m}-{d}') }}
                </p>
                <p v-if="scope.row.expireTime">
                  {{ $parseTime(scope.row.expireTime, '{h}:{i}:{s}') }}
                </p>
              </template>
            </vxe-column>
            <vxe-column field="短链状态" title="短链状态">
              <template v-slot="scope">
                <div
                  class="item_status"
                  style="
                    background: #f8eddd;
                    color: #ff8a00;
                    border: 1px solid #ffd8b4;
                  "
                  v-if="scope.row.status == 0"
                >
                  生成中
                </div>
                <div
                  class="item_status"
                  style="
                    background: #f5fff4;
                    color: #00c159;
                    border: 1px solid #a6edb1;
                  "
                  v-if="scope.row.status == 1"
                >
                  已生效
                </div>

                <el-tooltip
                  v-if="scope.row.status == 3"
                  effect="dark"
                  :content="scope.row.opinion"
                  placement="top-start"
                >
                  <div
                    class="item_status"
                    style="
                      background: #ffe7e7;
                      color: #ff6666;
                      border: 1px solid #fac2c2;
                    "
                  >
                    已到期
                  </div>
                </el-tooltip>
                <el-tooltip
                  v-if="scope.row.status == 2"
                  effect="dark"
                  :content="scope.row.opinion"
                  placement="top-start"
                >
                  <div
                    class="item_status"
                    style="
                      background: #ffe7e7;
                      color: #ff6666;
                      border: 1px solid #fac2c2;
                    "
                  >
                    生成失败
                  </div>
                </el-tooltip>
              </template>
            </vxe-column>
            <vxe-column field="备注" title="备注" width="130">
              <template v-slot="scope">
                <div>{{ scope.row.remark }}</div>
              </template>
            </vxe-column>
          </vxe-table>
          <div style="display: felx">
            <div></div>
            <div style="margin: 10px">
              <el-pagination
                class="page_bottom"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="tabelAlllist.currentPage"
                :page-size="tabelAlllist.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="tableData.total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <el-dialog
      title="模板预览"
      v-model="dialogVisible"
      width="1440px"
      :before-close="handleClose"
    >
      <iframe
        id="myframe"
        ref="newScreen"
        style="width: 1400px; height: 900px"
        :src="flowSrc"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import IMG_TEXT from './components/imageText/imageAndText.vue'
import Tooltip from '@/components/publicComponents/tooltip.vue'
// import clip from '../../../utils/clipboard'
import CopyTemp from '@/components/publicComponents/CopyTemp.vue'
export default {
  components: {
    'img-text': IMG_TEXT,
    Tooltip,
    CopyTemp,
  },
  name: 'yuexinTemplateAudit',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      activeName: '0',
      //查询
      ruleForm: {
        pageSize: 10,
        currentPage: 1,
        statusArr: [],
        condition: '',
        username: '',
        link: '',
        linkType: '0',
        time: [],
        startTime: '',
        endTime: '',
      },
      tabelAlllist: {
        pageSize: 10,
        currentPage: 1,
        statusArr: [],
        condition: '',
        username: '',
        link: '',
        linkType: '0',
        time: [],
        startTime: '',
        endTime: '',
      },
      //列表数据
      tableData: {
        loading2: false,
        data: [],
        total: 0,
      },
      dialogVisible: false,
      detail: {},
      flowSrc: null,
      timer: null,
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTempList()
    })
  },
  mounted() {
    const $table1 = this.$refs.tableRef1
    const $toolbar1 = this.$refs.toolbarRef1
    if ($table1 && $toolbar1) {
      $table1.connect($toolbar1)
    }
    const $table2 = this.$refs.tableRef2
    const $toolbar2 = this.$refs.toolbarRef2
    if ($table2 && $toolbar2) {
      $table2.connect($toolbar2)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTempList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    //模板列表
    getTempList() {
      this.tableData.loading2 = true
      window.api.post(
        window.path.yuexin + '/operator/link/page',
        this.tabelAlllist,
        (res) => {
          if (res.code == 200) {
            this.tableData.loading2 = false
            this.tableData.data = res.data.records
            this.tableData.total = res.data.total
          }
        }
      )
    },
    handleClick(tab, e) {
      this.$refs.ruleForm.resetFields()
      this.ruleForm.linkType = this.activeName
      this.ruleForm.link = ''
      this.ruleForm.startTime = ''
      this.ruleForm.endTime = ''
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getTempList()
    },
    getTimeOperating(val) {
      if (val) {
        this.ruleForm.startTime = val[0]
        this.ruleForm.endTime = val[1]
      } else {
        this.ruleForm.startTime = ''
        this.ruleForm.endTime = ''
      }
    },
    // handleCopy(name, event) {
    //     clip(name, event)
    // },
    //查询
    submitForm() {
      if(this.tableData.loading2) return; //防止重复请求
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getTempList()
    },
    resetForm() {
      this.$refs.ruleForm.resetFields()
      this.ruleForm.link = ''
      this.ruleForm.startTime = ''
      this.ruleForm.endTime = ''
      Object.assign(this.tabelAlllist, this.ruleForm)
      this.getTempList()
    },
    //分页
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size
      this.getTempList()
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage
      this.getTempList()
    },
    //模板审核/查看
    handView(row, tag) {
      this.dialogVisible = true
      let token = window.btoa(window.common.getCookie('ZTADMIN_TOKEN'))
      window.api.get(
        window.path.yuexin + '/operator/template/' + row.templateId,
        {},
        (res) => {
          if (res.code == 200) {
            this.detail = res.data
            this.flowSrc =
              window.path.yuexinip +
              '/#/preview?token=' +
              token +
              '&templateID=' +
              res.data.id +
              '&kindId=' +
              res.data.kindId
            this.$nextTick(() => {
              let mapFrame = document.getElementById('myframe')
              if (mapFrame.attachEvent) {
                //兼容浏览器判断
                this.timer = setTimeout(
                  mapFrame.attachEvent('onload', function () {
                    let iframeWin = mapFrame.contentWindow
                    iframeWin.postMessage(res.data, '*')
                    //res.data传递的参数   *写成子页面的域名或者是ip
                  }),
                  1000
                )
              } else {
                this.timer = setTimeout(
                  (mapFrame.onload = function () {
                    let iframeWin = mapFrame.contentWindow
                    iframeWin.postMessage(res.data, '*')
                    // debugger
                  }),
                  1000
                )
              }
            })
          }
        }
      )
    },
    handleClose() {
      this.dialogVisible = false
    },
    //下载链接
    // downLoad(id) {
    //     function downloadFile(blobUrl) {
    //         var a = document.createElement("a");
    //         a.style.display = "none";
    //         a.download =
    //             id +
    //             ".xlsx";
    //         a.href = blobUrl;
    //         a.click();
    //     }
    //     // downLoad_temp(detail.list.id).then(res => {
    //     //     let blobUrl = new Blob([res], { type: 'application/octet-stream;charset=utf-8' })
    //     //     let href = window.URL.createObjectURL(blobUrl); //下载链接
    //     //     downloadFile(href);
    //     // })
    //     // let path = 'http://192.168.1.5'
    //     axios({
    //         method: "get",
    //         url: window.path.yuexin + "/client/link/download/" + id,
    //         data: {},
    //         headers: {
    //             "Content-Type": "application/json",
    //             Authorization:
    //                 "Bearer " + window.common.getCookie("ZTADMIN_TOKEN"),
    //         },
    //         responseType: 'blob',
    //     })
    //         .then(function (res) {
    //             let blobUrl = new Blob([res.data], { type: 'application/octet-stream;charset=utf-8' })
    //             let href = window.URL.createObjectURL(blobUrl); //下载链接
    //             downloadFile(href);
    //         })
    // }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.flowSrc = ''
        this.detail = {}
        clearTimeout(this.timer)
      }
    },
  },
}
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.mean-item {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-itemj {
  width: 90%;
  height: 30px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.mean-items {
  width: 90%;
  height: 45px;
  padding: 10px 0;
  line-height: 33px;
  display: flex;
  justify-content: space-between;
}
.item_status {
  width: 80px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 8px;
}
.auditinfo {
  width: 600px;
  margin-left: 20px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>