<template>
  <div>
    <div class="OuterFrame fillet">
      <div class="from">
        <el-form
          label-width="82px"
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
              v-model="formInline.userName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              v-model="formInline.mobile"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              class="input-w"
              v-model="formInline.status"
              clearable
              placeholder="请选择"
            >
              <el-option label="待处理" value="0"> </el-option>
              <el-option label="处理中" value="1"> </el-option>
              <el-option label="已回执" value="2"> </el-option>
              <el-option label="已下发" value="3"> </el-option>
              <el-option label="异常" value="-1"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" prop="time">
            <el-date-picker
              :shortcuts="pickerOptions && pickerOptions.shortcuts"
              :disabled-date="pickerOptions && pickerOptions.disabledDate"
              :cell-class-name="pickerOptions && pickerOptions.cellClassName"
              v-model="formInline.time"
              type="datetimerange"
              @change="time"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
            >
            </el-date-picker>
          </el-form-item>
          <div>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>
      <!-- <div class="boderbottom">
            
            </div> -->
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header"  style="height: 35px;line-height: 35px;">彩信路由明细列表</span> -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <el-table-column label="用户名">
            <template v-slot="scope">{{ scope.row.userName }}</template>
          </el-table-column>
          <el-table-column label="手机号">
            <template v-slot="scope">{{ scope.row.mobile }}</template>
          </el-table-column>
          <el-table-column label="通道ID">
            <template v-slot="scope">{{ scope.row.channelId }}</template>
          </el-table-column>
          <el-table-column label="消息id">
            <template v-slot="scope">{{ scope.row.msgId }}</template>
          </el-table-column>
          <el-table-column label="标题">
            <template v-slot="scope">{{ scope.row.title }}</template>
          </el-table-column>
          <!-- <el-table-column label="短息内容" width="400">
                    <template #default="scope">{{ scope.row.content}}</template>
                </el-table-column> -->
          <el-table-column label="状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                待处理
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
              >
                处理中
              </el-tag>
              <el-tag
:disable-transitions="true" v-else-if="scope.row.status == 2" type="" effect="dark">
                已回执
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="success"
                effect="dark"
              >
                已下发
              </el-tag>
              <el-tag
:disable-transitions="true" v-else type="danger" effect="dark"> 异常 </el-tag>
              <!-- <span v-if="scope.row.status ==0"> 待处理</span>
                        <span v-else-if="scope.row.status ==1">处理中</span>
                        <span v-else-if="scope.row.status ==2">已回执</span>
                        <span v-else-if="scope.row.status ==3">已下发</span>
                        <span v-else style="color:red">异常</span> -->
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">{{
              moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
            }}</template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>
        
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import {
  $on,
  $off,
  $once,
  $emit,
} from '../../../../../../utils/gogocodeTransfer'
import moment from 'moment'
export default {
  data() {
    return {
      isFirstEnter: false,
      formInline: {
        userName: '',
        status: '',
        createStartTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        createEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      pickerOptions: {
        shortcuts: [
          {
            text: '最近三天',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              return [start, end]
            },
          },
          {
            text: '最近一周',
            value() {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              return [start, end]
            },
          },
        ],
      },
      tabelAlllist: {
        //存储查询数据
        userName: '',
        status: '',
        createStartTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        createEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        mobile: '',
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          Date.now(),
        ],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
      },
    }
  },
  methods: {
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingdivert/mms/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
        }
      )
    },
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      this.formInline.userName = ''
      this.formInline.mobile = ''
      this.formInline.status = ''
      this.formInline.createStartTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.formInline.createEndTime = moment(Date.now()).format(
        'YYYY-MM-DD HH:mm:ss'
      )
      this.formInline.time = [
        moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        Date.now(),
      ]
      Object.assign(this.tabelAlllist, this.formInline)
      // Object.assign(this.tabelAlllist,this.formInline);
      this.gettableLIst()
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    time(val) {
      // console.log(this.value2,'val');
      if (val) {
        this.formInline.createStartTime = moment(val[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.formInline.createEndTime = moment(val[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        this.formInline.createStartTime = moment()
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss')
        this.formInline.createEndTime = moment(Date.now()).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  emits: ['pick'],
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
</style>
