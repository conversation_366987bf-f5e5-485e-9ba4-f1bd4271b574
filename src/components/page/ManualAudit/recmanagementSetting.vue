<template>
  <div class="container_left">
    <div class="OuterFrame fillet OuterFrameList">
      <div>
        <el-form
          :inline="true"
          :model="failureForm"
          class="demo-form-inline"
          label-width="82px"
          ref="failureForm"
        >
          <el-form-item label="产品名称" label-width="82px" prop="productId">
            <!-- <el-input v-model="failureForm.productId" placeholder="" class="input-w"></el-input> -->
            <el-select
              class="input-w"
              v-model="failureForm.productId"
              placeholder="请选择产品"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="通道号" label-width="82px" prop="channelId">
            <el-input
              v-model="failureForm.channelId"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="失败代码" label-width="82px" prop="failureCode">
            <el-input
              v-model="failureForm.failureCode"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <div>
            <el-button type="primary" plain @click="sensitiveQuery()"
              >查询</el-button
            >
            <el-button
              type="primary"
              plain
              @click="sensitiveReload('failureForm')"
              >重置</el-button
            >
          </div>
        </el-form>
      </div>

      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="handleAdd()">新增</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="recmanagementSetting"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData"
          @checkbox-all="handleSelectionChange"
          @checkbox-change="handleSelectionChange">

          <vxe-column type="checkbox" width="50"></vxe-column>
          <vxe-column field="产品名称" title="产品名称">
            <template v-slot="scope">
              <div v-for="item in productList" :key="item.id">
                <span v-if="item.id == scope.row.productId">{{
                  item.name
                }}</span>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="失败代码" title="失败代码">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.failureCode"
                :content="scope.row.failureCode"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
            </template>
          </vxe-column>
          <vxe-column field="排除通道号" title="排除通道号">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.channelId"
                :content="scope.row.channelId"
                className="wrapper-text"
                effect="light"
              >
              </Tooltip>
              <!-- <span>{{ scope.row.channelId }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" width="170">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
                <!-- <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</p>
                                  <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</p> -->
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人">
            <template v-slot="scope">
              <div>{{ scope.row.createName }}</div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="140">
            <template v-slot="scope">
              <el-button
                style="color: #16a589"
                link
                @click="handleEdit(scope.row)"
                ><el-icon><EditPen /></el-icon>编辑</el-button
              >
              <el-button
                style="color: #f56c6c"
                link
                @click="handleDelete(scope.row.id)"
                ><el-icon><Delete /></el-icon>删除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="failureForm.currentPage"
            :page-size="failureForm.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
      </div>
      <el-dialog
        :title="title"
        v-model="dialogVisible"
        width="500px"
        :before-close="handleClose"
      >
        <el-form
          :model="addForm"
          :rules="addFormRules"
          class="demo-form-inline"
          label-width="82px"
          ref="addForm"
        >
          <el-form-item label="产品名称" label-width="82px" prop="productId">
            <el-select
              :disabled="title == '编辑失败补发'"
              class="input-w—d"
              v-model="addForm.productId"
              placeholder="请选择产品"
            >
              <el-option
                v-for="item in productList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="失败代码" label-width="82px" prop="failureCode">
            <el-input
              :disabled="title == '编辑失败补发'"
              v-model="addForm.failureCode"
              placeholder="失败代码"
              class="input-w—d"
            ></el-input>
          </el-form-item>
          <el-form-item label="通道号" label-width="82px" prop="channelId">
            <el-input
              type="textarea"
              v-model="addForm.channelId"
              placeholder="通道号多个以英文,分割"
              class="input-w—d"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSave('addForm')"
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import Tooltip from '@/components/publicComponents/tooltip.vue'
import moment from 'moment'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      //查询条件的值
      failureForm: {
        channelId: '', //通道号
        productId: '', //产品ID
        failureCode: '', //失败代码
        currentPage: 1,
        pageSize: 10,
      },
      //产品列表
      //产品列表
      productList: JSON.parse(localStorage.getItem('list')) || [],
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      isFirstEnter: false,
      dialogVisible: false,
      addForm: {
        //新增表单
        channelId: '', //通道号
        productId: '', //产品ID
        failureCode: '', //失败代码
      },
      title: '',
      addFormRules: {
        // channelId: [
        //     { required: true, message: "请输入通道号", trigger: "blur" },
        // ],
        productId: [{ required: true, message: '请选择产品', trigger: 'blur' }],
        failureCode: [
          { required: true, message: '请输入失败代码', trigger: 'blur' },
        ],
      },
    }
  },
  components: {
    Tooltip
  },
  name: 'recmanagementSetting',
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingautoreissuesetting/page',
        this.failureForm,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.total = res.data.total
          this.tableDataObj.loading2 = false
        }
      )
    },
    //查询
    sensitiveQuery() {
      this.getTableDtate()
    },
    //重置
    sensitiveReload(formName) {
      this.$refs[formName].resetFields()
      this.getTableDtate()
    },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.failureForm.pageSize = size
      this.getTableDtate()
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.failureForm.currentPage = currentPage
      this.getTableDtate()
    },
    //列表复选框的值
    handleSelectionChange(val) {
      let selectId = []
      let status = []
      for (let i = 0; i < val.records.length; i++) {
        selectId.push(val.records[i].id)
        //批量操作列表选中项的审核状态
        status.push(val.records[i].status)
      }
    },
    handleAdd() {
      this.title = '新增失败补发'
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleSave(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.api.post(
            window.path.omcs + 'operatingautoreissuesetting',
            this.addForm,
            (res) => {
              if (res.code == 200) {
                this.$message.success(res.msg)
                this.dialogVisible = false
                this.getTableDtate()
              } else {
                this.$message.error(res.msg)
              }
            }
          )
        } else {
          return false
        }
      })
    },
    handleDelete(id) {
      this.$confirms.confirmation(
        'delete',
        '确认删除该条记录？',
        window.path.omcs + 'operatingautoreissuesetting/' + id,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    handleEdit(row) {
      this.title = '编辑失败补发'
      this.addForm = {
        channelId: row.channelId,
        productId: row.productId,
        failureCode: row.failureCode,
      }
      this.dialogVisible = true
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
        // this.init()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.$refs.addForm.resetFields()
        this.addForm = {
          channelId: '',
          productId: '',
          failureCode: '',
        }
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.detailsList {
  line-height: 42px;
  padding-left: 30px;
  font-size: 12px;
  position: relative;
}
.bs {
  position: absolute;
  top: 0px;
  left: 100px;
  color: red;
  font-size: 14px;
  z-index: 9999;
}
.detailsList-title {
  display: inline-block;
  width: 80px;
}
.detailsList-content {
  display: inline-block;
  width: 340px;
  color: #848484;
  padding-left: 10px;
  border-bottom: 1px solid #eee;
}
.span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tooltip {
  width: 300px;
}
.action-btn {
  margin: 10px 0;
}
.input-w—d {
  width: 300px;
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}

.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}

.el-tabs__content {
  overflow: visible !important;
}
</style>
