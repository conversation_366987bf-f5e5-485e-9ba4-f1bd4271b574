<template>
    <div style="background: #fff; padding: 15px">
        <!-- 搜索栏 -->
        <div class="search-container">
            <el-form :inline="true" :model="searchForm" class="search-form">
                <!-- <el-form-item label="通道号">
                    <el-input class="input-w" v-model="searchForm.channelId" placeholder="请输入通道号" clearable></el-input>
                </el-form-item> -->
                <el-form-item label="失败代码">
                    <el-input class="input-w" v-model="searchForm.failureCode" placeholder="请输入失败代码"
                        clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 工具栏 -->
        <vxe-toolbar ref="toolbarRef" custom>
            <template #buttons>
                <el-button type="primary" @click="handleAdd">添加</el-button>
            </template>
        </vxe-toolbar>

        <!-- 数据表格 -->
        <vxe-table ref="alertTableRef" id="signatureFailureCodeTable" border stripe show-header-overflow
            :custom-config="customConfig" :column-config="{ resizable: true }" :row-config="{ isHover: true }"
            min-height="1" v-loading="loading" element-loading-text="拼命加载中"
            element-loading-background="rgba(0, 0, 0, 0.6)" style="font-size: 12px;" :data="tableData">
            <!-- <vxe-column field="channelId" title="通道号" min-width="120"></vxe-column> -->
            <vxe-column field="failureCode" title="失败代码" min-width="180"></vxe-column>
            <vxe-column field="createName" title="创建人" min-width="100"></vxe-column>
            <vxe-column field="createTime" title="创建时间" min-width="160">
                <template #default="{ row }">
                    {{ row.createTime ? moment(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '' }}
                </template>
            </vxe-column>
            <vxe-column field="操作" title="操作" fixed="right" width="200">
                <template #default="{ row }">
                    <!-- <el-button type="primary" link @click="handleEdit(row)">编辑</el-button> -->
                    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
                </template>
            </vxe-column>
        </vxe-table>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="pagination.currentPage" :page-size="pagination.pageSize" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" :total="pagination.total"></el-pagination>
        </div>

        <!-- 弹窗表单 -->
        <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑失败代码' : '添加失败代码'" width="550px"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
                <!-- <el-form-item label="通道号" prop="channelId">
                    <el-input v-model="form.channelId" placeholder="请输入通道号" :disabled="isEdit"></el-input>
                </el-form-item> -->
                <el-form-item label="失败代码" prop="failureCode">
                    <el-input type="textarea" v-model="form.failureCode" placeholder="多个以,分割"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import moment from 'moment';

// 表格引用
const alertTableRef = ref(null);
const toolbarRef = ref(null);

// 表格配置
const customConfig = {
    storage: true
};

// 搜索表单
const searchForm = reactive({
    // channelId: '',
    failureCode: '',
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
});

// 弹窗表单
const dialogVisible = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const form = reactive({
    id: '',
    // channelId: '',
    failureCode: '',
});

// 表单验证规则
const rules = {
    // channelId: [
    //     { required: true, message: '请输入通道号', trigger: 'blur' }
    // ],
    failureCode: [
        { required: true, message: '请输入失败代码', trigger: 'blur' }
    ]
};

// 获取列表数据
const getAlertList = async () => {
    loading.value = true;
    try {
        // 使用 axios 发送请求
        const params = {
            current: pagination.currentPage,
            size: pagination.pageSize,
            ...searchForm
        };

        // 调用实际API
        window.api.post(window.path.omcs + 'operatingdivert/failurecode/monitor/page', params, (res) => {
            // 设置表格数据和分页信息
            if (res.code == 200) {
                tableData.value = res.data.records;
                pagination.total = res.data.total;
                loading.value = false;
            } else {
                ElMessage.error(res.msg);
                loading.value = false;
            }
        }, (error) => {
            loading.value = false;
            ElMessage.error('获取列表失败，请稍后重试');
        });
    } catch (error) {
        console.error('获取列表失败:', error);
        ElMessage.error('获取列表失败，请稍后重试');
        loading.value = false;
    }
};

// 查询
const handleSearch = () => {
    pagination.currentPage = 1;
    getAlertList();
};

// 重置查询条件
const resetSearch = () => {
    Object.keys(searchForm).forEach(key => {
        searchForm[key] = '';
    });
    handleSearch();
};

// 处理页码变化
const handleCurrentChange = (page) => {
    pagination.currentPage = page;
    getAlertList();
};

// 处理每页显示数量变化
const handleSizeChange = (size) => {
    pagination.pageSize = size;
    getAlertList();
};

// 添加
const handleAdd = () => {
    isEdit.value = false;
    // 重置表单
    resetForm();

    dialogVisible.value = true;
};

// 编辑
// const handleEdit = (row) => {
//     isEdit.value = true;
//     // 填充表单数据
//     form.id = row.id;
//     // form.channelId = row.channelId;
//     form.failureCode = row.failureCode;

//     dialogVisible.value = true;
// };

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                // 构建提交的数据
                const submitData = {
                    ...form
                };

                // 根据是编辑还是新增，调用不同的接口
                // 新增操作使用POST方法
                window.api.post(window.path.omcs + 'operatingdivert/failurecode/monitor', submitData, (res) => {
                    // 关闭弹窗
                    if (res.code == 200) {
                        // 刷新列表
                        getAlertList();
                        // 提示成功
                        ElMessage.success('添加成功');
                        dialogVisible.value = false;
                    } else {
                        ElMessage.error(res.msg);
                    }
                }, (error) => {
                    console.error('添加失败:', error);
                    ElMessage.error('添加失败，请稍后重试');
                });
                // if (isEdit.value) {
                //     // 编辑操作使用PUT方法
                //     window.api.put(window.path.omcs + 'operatingdivert/failurecode/monitor', submitData, (res) => {
                //         // 关闭弹窗
                //         if (res.code == 200) {
                //             // 刷新列表
                //             getAlertList();
                //             // 提示成功
                //             ElMessage.success('修改成功');
                //             dialogVisible.value = false;
                //         } else {
                //             ElMessage.error(res.msg);
                //         }
                //     }, (error) => {
                //         console.error('修改失败:', error);
                //         ElMessage.error('修改失败，请稍后重试');
                //     });
                // } else {
                //     // 新增操作使用POST方法
                //     window.api.post(window.path.omcs + 'operatingdivert/failurecode/monitor', submitData, (res) => {
                //         // 关闭弹窗
                //         if (res.code == 200) {
                //             // 刷新列表
                //             getAlertList();
                //             // 提示成功
                //             ElMessage.success('添加成功');
                //             dialogVisible.value = false;
                //         } else {
                //             ElMessage.error(res.msg);
                //         }
                //     }, (error) => {
                //         console.error('添加失败:', error);
                //         ElMessage.error('添加失败，请稍后重试');
                //     });
                // }
            } catch (error) {
                console.error(isEdit.value ? '修改失败:' : '添加失败:', error);
                ElMessage.error(isEdit.value ? '修改失败，请稍后重试' : '添加失败，请稍后重试');
            }
        } else {
            return false;
        }
    });
};

// 删除
const handleDelete = async (row) => {
    try {
        // 确认删除
        await ElMessageBox.confirm('确定要删除该记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        // 调用实际API
        window.api.delete(window.path.omcs + 'operatingdivert/failurecode/monitor/' + row.id, {}, () => {
            // 提示成功
            ElMessage.success('删除成功');

            // 刷新列表
            getAlertList();
        }, (error) => {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        });
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除失败:', error);
            ElMessage.error('删除失败，请稍后重试');
        }
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();
    }
    // 重置表单数据到默认值
    form.id = '';
    // form.channelId = '';
    form.failureCode = '';
};

// 监听对话框关闭事件
watch(dialogVisible, (newValue) => {
    if (!newValue) {
        // 当对话框关闭时重置表单
        resetForm();
    }
});

// 页面加载时获取数据
onMounted(() => {
    const $table = alertTableRef.value
    const $toolbar = toolbarRef.value
    if ($table && $toolbar) {
        $table.connect($toolbar)
    }
    getAlertList();
});
</script>

<style scoped>
.alert-config-container {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.search-container {
    margin-bottom: 20px;
    padding: 15px;
    /* background-color: #f5f7fa; */
    border-radius: 4px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>
