<template>
  <div style="background: #fff; padding: 15px">
    <div class="Top_title">
      <el-page-header @back="goBack" content="cmpp连接数"> </el-page-header>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="会话ip" label-width="80px" prop="sessionIp">
            <el-input
              v-model="formInline.sessionIp"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="" label-width="80px">
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
          <!-- <el-form-item label="企业ID" label-width="80px" prop="corpid">
                            <el-input v-model="formInline.corpid" placeholder class="input-w"></el-input>
                        </el-form-item>
                        <el-form-item label="网关端口" label-width="80px" prop="wgPort">
                            <el-input v-model="formInline.wgPort" placeholder class="input-w"></el-input>
                        </el-form-item>
                        <el-form-item label="状态" label-width="80px" prop="status">
                            <el-select v-model="formInline.status" placeholder="请选择" class="input-w">
                                <el-option label="全部" value></el-option>
                                <el-option label="启用" value="1"></el-option>
                                <el-option label="停用" value="2"></el-option>
                            </el-select>
                        </el-form-item> -->
        </el-form>
      </div>
      <div class="boderbottom">
        <!-- <el-button type="primary" plain style @click="Query">查询</el-button> -->
        <!-- <el-button type="primary" plain style @click="Reload('formInline')">重置</el-button> -->
      </div>
      <div class="Signature-search-fun"></div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <!-- <table-tem
                :tableDataObj="tableDataObj"
                @handelOptionButton="handelOptionButton"
              ></table-tem> -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          stripe
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <el-table-column prop="corpId" label="企业ID"> </el-table-column>
          <el-table-column prop="id" label="ID"> </el-table-column>
          <el-table-column prop="port" label="端口"> </el-table-column>
          <el-table-column prop="sessionId" label="会话id"> </el-table-column>
          <el-table-column prop="sessionIp" label="会话ip"> </el-table-column>
          <el-table-column width="120" label="状态">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true"
                v-if="scope.row.type == 'connect'"
                type="success"
                effect="dark"
              >
                连接
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-if="scope.row.type == 'disconnect'"
                type="danger"
                effect="dark"
              >
                断开
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间">
            <template v-slot="scope">
              <span v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="更新时间">
            <template v-slot="scope">
              <span v-if="scope.row.updateTime">{{
                moment(scope.row.updateTime).format('YYYY-MM-DD HH:mm:ss')
              }}</span>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        
          <div
            style="float: right;margin-top: 10px"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.currentPage"
              :page-size="formInline.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </div>
        
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  name: 'cmppConnect',
  data() {
    return {
      isFirstEnter: false,
      formInline: {
        corpId: '',
        port: '',
        sessionIp: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        loading2: false,
        total: 0,
        tableData: [],
      },
    }
  },
  methods: {
    goBack() {
      this.$router.push({
        path: '/CMPPmanagement',
      })
    },
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      if (this.$route.query.corpId || this.$route.query.port) {
        this.formInline.corpId = this.$route.query.corpId
        this.formInline.port = this.$route.query.port
      }
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'cmppserverconnect/page',
        this.formInline,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false
            this.tableDataObj.total = res.data.total
            this.tableDataObj.tableData = res.data.records
          }
        }
      )
    },
    Reload() {
      this.$refs['formInline'].resetFields()
      this.gettableLIst()
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.formInline.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.formInline.currentPage = currentPage
      this.gettableLIst()
    },
    Query() {
      //查询
      this.gettableLIst()
    },
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Mail-table {
  padding-bottom: 40px;
}
</style>
