<template>
  <div>
    <div class="Top_title">
      <span>运营商配置管理</span>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item
            label="运营商名称"
            label-width="82px"
            prop="operatorName"
          >
            <el-input
              v-model="formInline.operatorName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建人" label-width="80px" prop="createName">
            <el-input
              v-model="formInline.createName"
              placeholder
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="创建时间" label-width="80px" prop="dataCreat">
            <el-date-picker
              class="input-w"
              v-model="formInline.dataCreat"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')"
          >重置</el-button
        >
      </div>
      <div class="Signature-search-fun">
        <!-- <span class="Signature-list-header">运营商列表</span> -->
        <el-button type="primary" style="margin-left: 15px" @click="addopt"
          >新增运营商</el-button
        >
        <!-- <el-button type="danger" style="margin-left:15px;" v-if="selectId.length>0" @click="delAll" >批量删除</el-button> -->
      </div>
      <div class="Mail-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
        ></table-tem>
        <!--分页-->
        <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tabelAlllist.currentPage"
              :page-size="tabelAlllist.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
            ></el-pagination>
          </el-col>
        </template>
        <!-- 表格和分页结束 -->
      </div>

      <!-- 新增运营商 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          :model="formop"
          :rules="rules"
          ref="formop"
          label-width="100px"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="运营商名称" prop="operatorName">
            <el-input
              v-model="formop.operatorName"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>

        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 新增运营商结束 -->
    </div>
  </div>
</template>

<script>
import TableTem from '../../../../publicComponents/TableTem'
export default {
  name: 'OperatorConfigurationMag',
  components: {
    TableTem,
  },
  data() {
    return {
      titleMap: {
        add: '增加运营商',
        edit: '编辑运营商',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      operatorId: '', //运营商id

      tableRow: '', //当前行列表数据
      selectId: '',
      formop: {
        //表单数据
        operatorName: '',
      },
      rules: {
        //验证规则
        operatorName: [
          { required: true, message: '请输入运营商名称', trigger: 'blur' },
          {
            min: 1,
            max: 10,
            message: '长度在 1 到 10 个字符',
            trigger: ['blur', 'change'],
          },
          {
            pattern: /^[\u4e00-\u9fa5]+$/,
            message: '含有非法字符（只能输入汉字！）',
          },
        ],
      },
      tabelAlllist: {
        //存储查询数据
        createName: '',
        operatorName: '',
        dataCreat: '',
        begTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      formInline: {
        //查询数据
        createName: '',
        operatorName: '',
        dataCreat: '',
        begTime: '',
        endTime: '',
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //列表数据
        currentPage: '1',
        pageSize: '10',
        total: 0,
        loading2: false,
        tableData: [],
        tableLabel: [
          {
            prop: 'operatorId',
            showName: 'ID',
            width: '70',
            fixed: false,
          },
          {
            prop: 'operatorName',
            showName: '运营商名称',
            fixed: false,
          },
          {
            prop: 'createName',
            showName: '创建人',
            fixed: false,
          },
          {
            prop: 'createTime',
            showName: '创建时间',
            width: '140',
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: '100%',
          },
          optionWidth: '180', //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: '编辑',
            type: '',
            size: 'mini',
            optionMethod: 'details',
            icon: 'el-icon-edit',
          },
          {
            optionName: '删除',
            type: '',
            size: 'mini',
            optionMethod: 'dellog',
            icon: 'el-icon-delete',
            color: '#f56c6c',
          },
        ],
      },
      pldel: false,
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'operatingoperatorinfo/page',
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.loading2 = false
          this.tableDataObj.total = res.total
          this.tableDataObj.tableData = res.records
        }
      )
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.begTime = this.moment(val[0]).format('YYYY-MM-DD ')
        this.formInline.endTime = this.moment(val[1]).format('YYYY-MM-DD ')
      } else {
        this.formInline.begTime = ''
        this.formInline.endTime = ''
      }
    },
    Query() {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = []
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].operatorId)
      }
      this.selectId = selectId.join(',')
    },
    Reload() {
      //重置
      this.formInline.begTime = ''
      this.formInline.endTime = ''
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            //验证唯一性
            window.api.post(
              window.path.omcs + 'operatingoperatorinfo/validate',
              { operatorName: this.formop.operatorName },
              (res) => {
                if (res.code == 400) {
                  this.$message({
                    message: '运营商名称重复，重新输入！',
                    type: 'warning',
                  })
                } else {
                  //添加
                  this.$confirms.confirmation(
                    'post',
                    '确认执行此操作吗？',
                    window.path.omcs + 'operatingoperatorinfo',
                    {
                      operatorName: this.formop.operatorName,
                    },
                    () => {
                      this.dialogFormVisible = false
                      this.gettableLIst()
                    }
                  )
                }
              }
            )
          } else {
            //编辑
            this.formop.operatorId = this.operatorId
            //验证唯一
            if (this.formop.operatorName == this.tableRow.operatorName) {
              this.$confirms.confirmation(
                'put',
                '确认执行此操作吗？',
                window.path.omcs + 'operatingoperatorinfo',
                this.formop,
                () => {
                  this.dialogFormVisible = false
                  this.gettableLIst()
                }
              )
            } else {
              window.api.post(
                window.path.omcs + 'operatingoperatorinfo/validate',
                { operatorName: this.formop.operatorName },
                (res) => {
                  if (res.code == 400) {
                    this.$message({
                      message: '运营商名称重复，重新输入！',
                      type: 'warning',
                    })
                  } else {
                    this.$confirms.confirmation(
                      'put',
                      '确认执行此操作吗？',
                      window.path.omcs + 'operatingoperatorinfo',
                      this.formop,
                      () => {
                        this.dialogFormVisible = false
                        this.gettableLIst()
                      }
                    )
                  }
                }
              )
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },

    addopt() {
      //添加
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },
    delAll() {},
    detailsRow(val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.operatorId = val.row.operatorId
      this.tableRow = val.row
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val.row)
      })
    },
    //删除
    delRow: function (val) {
      //删除验证
      window.api.get(
        window.path.omcs +
          'operatingoperatorinfo/checkChannelIsUsed/' +
          val.row.operatorId,
        {},
        (res) => {
          if (res.code == 400) {
            this.$message({
              message: res.msg,
              type: 'warning',
            })
          } else {
            //删除
            this.$confirms.confirmation(
              'delete',
              '此操作将永久删除该数据, 是否继续？',
              window.path.omcs + 'operatingoperatorinfo/' + val.row.operatorId,
              {},
              () => {
                this.gettableLIst()
              }
            )
          }
        }
      )
    },
    handelOptionButton: function (val) {
      if (val.methods == 'details') {
        this.detailsRow(val)
      }

      if (val.methods == 'dellog') {
        this.delRow(val)
      }
    },
  },
  mounted() {},
  watch: {
    tabelAlllist: {
      handler() {
        this.gettableLIst()
      },
      deep: true, //深度监听
      immediate: true,
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
