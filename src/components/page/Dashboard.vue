<template>
  <div>
    <div class="row-head">
      <div class="user-info">
        <img :src="portraitUrl" class="user-avator" alt="" />
        <div class="user-info-cont">
          <div class="user-info-name">{{ userName }}</div>
          <div>{{ realName }}</div>
        </div>
      </div>
    </div>
    <div class="row-body">
      <div class="content-left">
        <!-- <el-card shadow="hover" style="height: 600px; overflow: auto">
          <template v-slot:header>
            <div class="clearfix">
              <span>登陆日志</span>
            </div>
          </template>
          <el-table
            v-loading="tableDataObj.loading2"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
            style="width: 100%"
          >
            <el-table-column label="用户名">
              <template v-slot="scope">{{
                scope.row.userName || "-"
              }}</template>
            </el-table-column>
            <el-table-column label="登录手机号">
              <template #default="scope">
                <div v-if="scope.row.loginMobile">
                  <span>{{ scope.row.loginMobile }}</span>
                </div>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column label="登录IP">
              <template v-slot="scope">{{ scope.row.ip || "-" }}</template>
            </el-table-column>
            <el-table-column label="IP区域">
              <template v-slot="scope">{{ scope.row.ipArea || "-" }}</template>
            </el-table-column>
            <el-table-column label="登录状态">
              <template v-slot="scope">
                <span v-if="scope.row.isSucced == 1">成功</span>
                <span v-else style="color: red">失败</span>
              </template>
            </el-table-column>
            <el-table-column label="操作时间" width="100">
              <template v-slot="scope">
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
                </p>
                <p v-if="scope.row.createTime">
                  {{ $parseTime(scope.row.createTime, "{h}:{i}:{s}") }}
                </p>
              </template>
            </el-table-column>
            <el-table-column label="详情描述" width="150">
              <template v-slot="scope"
                >{{ scope.row.logName }}:{{ scope.row.remark
                }}{{ scope.row.isSucced == 1 ? "成功" : "失败" }}</template
              >
            </el-table-column>
          </el-table>
          <div class="paginationBox">

          </div>
        </el-card> -->
        <el-card shadow="hover" style="height: 600px; overflow: auto; min-width: 290px">
          <template v-slot:header>
            <div class="clearfix">
              <span>更新日志</span>
            </div>
          </template>
          <el-collapse v-model="activeNames">
            <el-collapse-item
              v-for="item in lists"
              :key="item.id"
              :title="item.time"
              :name="item.id"
            >
              <div v-for="(res, index) in item.content" :key="index">
                {{ res.value }}
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </div>
      <!-- <div class="content-right">
        
      </div> -->
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import moment from "moment";
// import Schart from 'vue-schart'
import bus from "../common/bus";
import axios from "axios";
import { contentList } from "../../utils/version";
export default {
  name: "dashboard",
  data() {
    return {
      name: localStorage.getItem("ms_username"),
      activeNames: contentList[0].id,
      lists: contentList,
      todoList: [
        {
          title: "今天要修复100个bug",
          status: false,
        },
        {
          title: "今天要修复100个bug",
          status: false,
        },
        {
          title: "今天要写100行代码加几个bug吧",
          status: false,
        },
        {
          title: "今天要修复100个bug",
          status: false,
        },
        {
          title: "今天要修复100个bug",
          status: true,
        },
        {
          title: "今天要写100行代码加几个bug吧",
          status: true,
        },
      ],
      data: [
        {
          name: "2018/09/04",
          value: 1083,
        },
        {
          name: "2018/09/05",
          value: 941,
        },
        {
          name: "2018/09/06",
          value: 1139,
        },
        {
          name: "2018/09/07",
          value: 816,
        },
        {
          name: "2018/09/08",
          value: 327,
        },
        {
          name: "2018/09/09",
          value: 228,
        },
        {
          name: "2018/09/10",
          value: 1065,
        },
      ],
      options: {
        title: "最近七天每天的用户访问量",
        showValue: false,
        fillColor: "rgb(45, 140, 240)",
        bottomPadding: 30,
        topPadding: 30,
      },
      options2: {
        title: "最近七天用户访问趋势",
        fillColor: "#FC6FA1",
        axisColor: "#008ACD",
        contentColor: "#EEEEEE",
        bgColor: "#F5F8FD",
        bottomPadding: 30,
        topPadding: 30,
      },
      formInlines: {
        userName: "",
        ip: "",
        isSucced: "",
        begTime: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        endTime: moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        time: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    };
  },
  components: {
    // Schart,
  },
  computed: {
    // role() {
    //     return this.name === 'admin' ? '超级管理员' : '普通用户';
    // }
    ...mapState({
      //比如'movies/hotMovies
      userName: (state) => state.userName,
      compName: (state) => state.compName,
      roleId: (state) => state.roleId,
      realName: (state) => state.realName,
      portraitUrl: (state) => state.portraitUrl,
    }),
  },
  created() {
    this.getList();
    this.InquireList();
    // console.log();
    // console.log(window.path.imgU,'lll');
    // this.handleListener();
    // this.changeDate();
    //   window.api.get( window.path.cpus + 'statistics/sendCharts?productId=2',{},res=>{
    //       console.log(res);
    //   })
  },
  // activated(){
  //     this.handleListener();
  // },
  // deactivated(){
  //     window.removeEventListener('resize', this.renderChart);
  //     bus.$off('collapse', this.handleBus);
  // },
  methods: {
    changeDate() {
      const now = new Date().getTime();
      this.data.forEach((item, index) => {
        const date = new Date(now - (6 - index) * 86400000);
        item.name = `${date.getFullYear()}/${
          date.getMonth() + 1
        }/${date.getDate()}`;
      });
    },
    getList() {
      window.api.get(window.path.recharge + "products", {}, (res) => {
        // console.log(res);
        localStorage.setItem("list", JSON.stringify(res.data));
        // this.options = res.data;
      });
    },
    InquireList() {
      this.tableDataObj.loading2 = true;
      window.api.post(
        window.path.upms + "loginlog/logPage",
        this.formInlines,
        (res) => {
          this.tableDataObj.tableData = res.records;
          this.totalSize = res.total;
          this.tableDataObj.loading2 = false;
          // this.tableDataObj.tableData.forEach((item) => {
          //   item.maskMobile = item.loginMobile;
          // });
        }
      );
    },
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
    // handleListener(){
    //     bus.$on('collapse', this.handleBus);
    //     // 调用renderChart方法对图表进行重新渲染
    //     window.addEventListener('resize', this.renderChart)
    // },
    // handleBus(msg){
    //     setTimeout(() => {
    //         this.renderChart()
    //     }, 300);
    // },
    // renderChart(){
    //     this.$refs.bar.renderChart();
    //     this.$refs.line.renderChart();
    // }
  }
};
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}
.row-head {
  background: #fff;
  padding: 10px;
}
.row-body {
  padding: 10px 0;
  display: flex;
}
.content-left {
  width: 50%;
  /* height: 600px; */
  background: #fff;
}
.content-right {
  flex: 1;
  margin-left: 8px;
}
.grid-content {
  display: flex;
  align-items: center;
  height: 100px;
}
.grid-cont-right {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #999;
}
.grid-num {
  font-size: 30px;
  font-weight: bold;
}
.grid-con-icon {
  font-size: 50px;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
  color: #fff;
}
.grid-con-1 .grid-con-icon {
  background: rgb(45, 140, 240);
}
.grid-con-1 .grid-num {
  color: rgb(45, 140, 240);
}
.grid-con-2 .grid-con-icon {
  background: rgb(100, 213, 114);
}
.grid-con-2 .grid-num {
  color: rgb(45, 140, 240);
}
.grid-con-3 .grid-con-icon {
  background: rgb(242, 94, 67);
}
.grid-con-3 .grid-num {
  color: rgb(242, 94, 67);
}
.user-info {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 2px solid #ccc;
  margin-bottom: 20px;
  background: #fff;
}
.user-avator {
  width: 120px;
  height: 120px;
  border-radius: 50%;
}
.user-info-cont {
  padding-left: 50px;
  flex: 1;
  font-size: 14px;
  color: #999;
}
.user-info-cont div:first-child {
  font-size: 30px;
  color: #222;
}
.user-info-list {
  font-size: 14px;
  color: #999;
  line-height: 25px;
}
.user-info-list span {
  margin-left: 70px;
}
.mgb20 {
  margin-bottom: 20px;
}
.todo-item {
  font-size: 14px;
}
.todo-item-del {
  text-decoration: line-through;
  color: #999;
}
.schart {
  width: 100%;
  height: 300px;
}
.relove {
  height: 500px;
  overflow: auto;
}
.relove::-webkit-scrollbar {
  width: 5px;
  height: 1px;
}
.relove::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ccc;
}
.relove::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  background: #ffffff;
}
</style>
