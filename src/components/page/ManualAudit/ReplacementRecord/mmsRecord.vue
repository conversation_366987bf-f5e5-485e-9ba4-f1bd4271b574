<template>
  <div>
    <div class="passageWay-title" style="margin-top: 30px">
      <!-- 查询框开始 -->
      <el-form
        :inline="true"
        :model="form"
        class="demo-form-inline"
        ref="sendjl"
      >
        <el-form-item label="用户名称" prop="clientName">
          <el-input v-model="form.clientName" class="input-w"></el-input>
        </el-form-item>
        <!-- <el-form-item label="发送标题" prop="title">
                  <el-input v-model="form.title" class="input-w"></el-input>
                </el-form-item> -->
        <el-form-item label="发送时间" prop="time">
          <el-date-picker
            :shortcuts="pickerOptions && pickerOptions.shortcuts"
            :disabled-date="pickerOptions && pickerOptions.disabledDate"
            :cell-class-name="pickerOptions && pickerOptions.cellClassName"
            v-model="form.time"
            type="datetimerange"
            @change="timeD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
          >
          </el-date-picker>
        </el-form-item>
        <div>
          <el-button
            type="primary"
            plain
            @click.prevent="Query()"
            @keyup.enter="Query()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="reset('sendjl')"
            >重置</el-button
          >
        </div>
      </el-form>
      <!-- <div class="boderbottom">
            <el-button
              type="primary"
              plain
              @click.native.prevent="Query()"
              @keyup.enter.native="Query()"
              >查询</el-button
            >
            <el-button type="primary" plain @click="reset('sendjl')"
              >重置</el-button
            >
            <el-button type="primary" plain  @click="export1()">导出</el-button>
          </div> -->
      <div class="sensitive" style="margin: 10px 0">
        <!-- <el-button type="primary" @click="ConditionS('条件补发')"
                  >条件补发</el-button
                > -->
      </div>
      <!-- 查询框结束 -->
      <div class="passage-table" style="margin-top: 10px">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
        <el-table
          :data="tableDataObj1.tableData"
          :border="true"
          :default-expand-all="false"
          style="width: 100%"
        >
          <el-table-column type="expand">
            <template v-slot="props">
              <el-form label-position="left" inline class="demo-table-expand">
                <el-form-item label="用户名:">
                  <span>{{ props.row.clientName }}</span>
                </el-form-item>
                <el-form-item label="消息ID:">
                  <span>{{ props.row.msgid }}</span>
                </el-form-item>
                <el-form-item label="发送内容:">
                  <span>{{ props.row.content }}</span>
                </el-form-item>
                <el-form-item label="手机号码:">
                  <span>{{ props.row.mobile }}</span>
                </el-form-item>
                <el-form-item label="通道ID:">
                  <span>{{ props.row.channelId }}</span>
                </el-form-item>
                <el-form-item label="失败代码:">
                  <span>{{ props.row.originalCode }}</span>
                </el-form-item>
                <el-form-item label="状态:">
                  <span v-if="props.row.smsStatus == 3">待返回</span>
                  <span v-else-if="props.row.smsStatus == 2">发送失败</span>
                  <!-- <span v-if="tableDataObj1.tableData.smsStatus == 0"
                        >待补发</span
                      >
                      <span v-else-if="tableDataObj1.tableData.smsStatus == 1"
                        >补发中</span
                      >
                      <span v-else-if="tableDataObj1.tableData.smsStatus == 2"
                        >补发完成</span
                      >
                      <span v-else>补发失败</span> -->
                </el-form-item>
                <el-form-item label="发送时间:">
                  <span>{{
                    props.row.sendBeginTime + ' - ' + props.row.sendEndTime
                  }}</span>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="用户名" prop="clientName"> </el-table-column>
          <el-table-column label="补发条数" prop="number"> </el-table-column>
          <el-table-column label="应补发数" prop="reissueNumber">
          </el-table-column>
          <!-- <el-table-column label="补发通道号" prop="reissueChannelId">
              </el-table-column> -->
          <!-- <el-table-column label="补发次数" prop="process"> </el-table-column> -->
          <el-table-column label="补发状态" prop="">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark">
                待补发
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
              >
                补发中
              </el-tag>
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 2"
                type="success"
                effect="dark"
              >
                补发完成
              </el-tag>
              <el-tag
:disable-transitions="true" v-else type="danger" effect="dark"> 补发失败 </el-tag>
              <!-- <span v-if="scope.row.status == 0">待补发</span>
                  <span v-else-if="scope.row.status == 1">补发中</span>
                  <span v-else-if="scope.row.status == 2">补发完成</span>
                  <span v-else>补发失败</span> -->
            </template>
          </el-table-column>
          <el-table-column label="补发人" prop="operatorName">
          </el-table-column>
          <el-table-column label="补发时间" width="170" prop="createTime">
          </el-table-column>
          <!-- <el-table-column label="操作" width="120" prop="">
                <template #default="scope">
                  <el-button type="text" @click="DetailsS(scope.row)"
                    >&nbsp;详 情</el-button
                  >
                  <el-button type="text" @click="Retry(scope.row)"
                    >&nbsp;重 试</el-button
                  >
                </template>
              </el-table-column> -->
        </el-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="form.currentPage"
            :page-size="form.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj1.tablecurrent.total"
          >
          </el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  data() {
    return {
      status: '展开',
      form: {
        productId: 2,
        clientName: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formInline1s: {
        productId: 2,
        clientName: '',
        sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
        sendEndTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        time: [
          moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
          moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      tableDataObj1: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      isFirstEnter: false,
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.InquireList()
      //   this.GroupIdChange()
    })
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.InquireList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  methods: {
    InquireList() {
      this.tableDataObj1.loading2 = true
      window.api.post(
        window.path.omcs + 'servicereissuerecord/page',
        this.form,
        (res) => {
          this.tableDataObj1.loading2 = false
          this.tableDataObj1.tableData = res.records
          this.tableDataObj1.tablecurrent.total = res.total
        }
      )
    },
    reset() {
      this.$refs.sendjl.resetFields()
      this.form.pageSize = 10
      this.form.currentPage = 1
      Object.assign(this.formInline1s, this.form)
      this.form.sendBeginTime = moment()
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.form.sendEndTime = moment()
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss')
      this.InquireList()
    },
    Query() {
      Object.assign(this.formInline1s, this.form)
      this.InquireList()
    },
    // renderHeader(h) {
    //   //表头事件
    //   return (
    //     <div style="margin:0;padding:0;line-height: 23px;width:inherit;height: 23px;">
    //       <el-button
    //         nativeOnClick={this.expandOpen}
    //         style="plain:false;background:none;border:none;margin:0;padding:0;list-style:none;"
    //       >
    //         {this.status}
    //       </el-button>
    //     </div>
    //   )
    // },
    expandOpen() {
      var arr = document.querySelectorAll('.el-table__expand-icon')
      for (var i = 0; i < arr.length; i++) {
        if (arr[i].className.length < 25) {
          arr[i].onclick = function () {}
        } else {
          arr[i].onclick = function () {}
        }
      }
      if (this.status == '展开') {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length < 25) {
            arr[i].click()
          }
        }
        this.status = '折叠'
      } else {
        for (var i = 0; i < arr.length; i++) {
          if (arr[i].className.length > 25) {
            arr[i].click()
          }
        }
        this.status = '展开'
      }
    },
    DetailsS() {},
    Retry() {},
    handleSizeChange(size) {
      this.form.pageSize = size
      this.InquireList()
    },
    handleCurrentChange: function (currentPage) {
      this.form.currentPage = currentPage
      this.InquireList()
    },
    timeD(val) {
      if (val) {
        this.form.sendBeginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss')
        this.form.sendEndTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss')
      } else {
        this.form.sendBeginTime = ''
        this.form.sendEndTime = ''
        this.form.time = []
      }
    },
  },
}
</script>

<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}
.demo-table-expand {
  padding: 0 10px;
}
.el-input-group--prepend > .el-input-group__append,
.el-input-group__prepend {
  width: 80px !important;
}
.sensitive {
}
.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}
</style>

<style>
#SMSReplacement .el-input-group--prepend > .el-input-group__append,
.el-input-group__prepend {
  width: 56px !important;
}
</style>
