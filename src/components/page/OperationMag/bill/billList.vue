<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <div>
        <el-form
          :inline="true"
          :model="formInline"
          class="demo-form-inline"
          ref="formInline"
        >
          <el-form-item label="主题" prop="subject">
            <el-input
              class="input-w"
              v-model="formInline.subject"
              placeholder
            ></el-input>
          </el-form-item>
          <el-form-item label="收件箱" prop="email">
            <el-input
              class="input-w"
              v-model="formInline.email"
              placeholder
            ></el-input>
          </el-form-item>
          <el-form-item label="来源" prop="source">
            <el-input
              class="input-w"
              v-model="formInline.source"
              placeholder
            ></el-input>
          </el-form-item>
          <el-form-item label="任务状态" prop="status">
            <el-select
              class="input-w"
              v-model="formInline.status"
              placeholder="请选择"
            >
              <el-option label="全部" value=""></el-option>
              <el-option label="待处理" value="0"></el-option>
              <el-option label="处理中" value="1"></el-option>
              <el-option label="处理成功" value="2"></el-option>
              <el-option label="处理失败" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              class="input-w"
              v-model="formInline.remark"
              placeholder
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain style @click="Query"
              >查询</el-button
            >
            <el-button type="primary" plain style @click="Reload('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="Mail-table">
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="billList"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column field="流水号" title="流水号" width="180">
            <template v-slot="scope">
              <div>{{ scope.row.taskId || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="主题" title="主题" width="180">
            <template v-slot="scope">
              <div>
                <span>{{ scope.row.subject || '-' }}</span>
                <el-tooltip placement="top">
                  <template v-slot:content>
                    <div>
                      <div>{{ scope.row.address }}</div>
                      <div>{{ scope.row.content || '-' }}</div>
                    </div>
                  </template>
                  <el-icon style="color: #409eff; font-size: 16px; margin-left: 4px;"><InfoFilled /></el-icon>
                  <!-- <el-button>Top center</el-button> -->
                </el-tooltip>
              </div>
            </template>
          </vxe-column>
          <vxe-column field="邮件内容" title="邮件内容" width="220">
            <template v-slot="scope">
              <div>{{ scope.row.content || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="收件箱地址" title="收件箱地址" width="164">
            <template v-slot="scope">
              <div>{{ scope.row.address || '-' }}</div>
            </template>
          </vxe-column>
          <!-- <vxe-column field="" title="文件地址" width="140">
                            <template #default="scope">
                                <Tooltip v-if="scope.row.files" :content="scope.row.files" className="wrapper-text" effect="light"></Tooltip>
                            </template>
                        </vxe-column> -->
          <vxe-column field="任务状态" title="任务状态" width="140">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.status == 0" type="info" effect="dark"
                >任务待处理</el-tag
              >
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 1"
                type="warning"
                effect="dark"
                >任务处理中</el-tag
              >
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 2"
                type="success"
                effect="dark"
                >任务处理成功</el-tag
              >
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.status == 3"
                type="danger"
                effect="dark"
                >任务处理失败</el-tag
              >
              <span v-else>-</span>
            </template>
          </vxe-column>
          <vxe-column field="发送状态" title="发送状态" width="140">
            <template v-slot="scope">
              <el-tag
:disable-transitions="true" v-if="scope.row.sendStatus == 0" type="info" effect="dark"
                >邮件待发送</el-tag
              >
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.sendStatus == 1"
                type="warning"
                effect="dark"
                >邮件发送中</el-tag
              >
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.sendStatus == 2"
                type="success"
                effect="dark"
                >邮件发送成功</el-tag
              >
              <el-tag
:disable-transitions="true"
                v-else-if="scope.row.sendStatus == 3"
                type="danger"
                effect="dark"
                >邮件发送失败</el-tag
              >
              <span v-else>-</span>
            </template>
          </vxe-column>
          <vxe-column field="来源" title="来源" width="140">
            <template v-slot="scope">
              <div>{{ scope.row.source || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="备注" title="备注" width="140">
            <template v-slot="scope">
              <Tooltip
                v-if="scope.row.remark"
                :content="scope.row.remark"
                className="wrapper-text"
                effect="light"
              ></Tooltip>
              <!-- <span>{{ scope.row.remark || '-' }}</span> -->
            </template>
          </vxe-column>
          <vxe-column field="创建人" title="创建人" width="120">
            <template v-slot="scope">
              <div>{{ scope.row.createName || '-' }}</div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间" min-width="170">
            <template v-slot="scope">
              <!-- <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</p>
                                <p v-if="scope.row.createTime">{{ $parseTime(scope.row.createTime, '{h}:{i}:{s}') }}</p> -->
              <div v-if="scope.row.createTime">{{
                moment(scope.row.createTime).format('YYYY-MM-DD HH:mm:ss')
              }}</div>
              <div v-else>-</div>
            </template>
          </vxe-column>

          <!-- <vxe-column field="" title="操作" width="150" fixed="right">
                            <template #default="scope">
                                <el-button type="text" style="margin-left: 10px;color:red"
                                    @click="delState(scope.$index, scope.row)">删除</el-button>
                            </template>
                        </vxe-column> -->
        </vxe-table>
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->

        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage"
            :page-size="tabelAlllist.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          ></el-pagination>
        </div>

          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
      <!-- 新增账单 -->
      <el-dialog
        :title="titleMap[dialogStatus]"
        v-model="dialogFormVisible"
        :close-on-click-modal="false"
        width="520px"
      >
        <el-form
          label-width="110px"
          :model="formop"
          :rules="rules"
          ref="formop"
          style="padding: 0 28px 0 20px"
        >
          <el-form-item label="主题" prop="subject">
            <el-input
              class="input-w"
              v-model="formop.subject"
              placeholder="请输入主题"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮件内容" prop="content">
            <el-input
              class="input-w"
              type="textarea"
              v-model="formop.content"
              placeholder="请输入邮件内容"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮件地址" prop="address">
            <el-input
              class="input-w"
              type="textarea"
              v-model="formop.address"
              placeholder="请输入邮件地址"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="文件地址" prop="files">
            <el-input
              class="input-w"
              type="textarea"
              v-model="formop.files"
              placeholder="请输入文件地址"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <template v-slot:footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm('formop')"
              >提 交</el-button
            >
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import Tooltip from '@/components/publicComponents/tooltip.vue'
import common from '../../../../assets/js/common'
export default {
  components: {
    Tooltip
  },
  name: 'billSetting',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      menuName: '明码导出任务',
      titleMap: {
        add: '创建任务',
        edit: '编辑账单',
      },
      dialogStatus: '', //新增编辑标题
      dialogFormVisible: false, //新增弹出框显示隐藏
      //查询表单
      formInline: {
        email: '', //收件箱
        subject: '', // 主题
        source: '', //来源
        status: '', //任务状态
        remark: '', //备注
        currentPage: 1, //当前页
        pageSize: 10, //每页条数
      },
      tabelAlllist: {
        //存储查询数据
        email: '',
        subject: '',
        source: '',
        status: '',
        remark: '',
        currentPage: 1,
        pageSize: 10,
      },
      options: [],
      //添加列表
      formop: {
        subject: '', //主题
        content: '', //邮件内容
        files: '', //文件地址
        address: '', //邮件地址
      },
      id: '', //列表行id
      rules: {
        subject: [
          {
            required: true,
            message: '主题不能为空',
            trigger: 'change',
          },
        ],
        content: [
          {
            required: true,
            message: '邮件内容不能为空',
            trigger: 'change',
          },
        ],
        address: [
          { required: true, message: '邮件地址不能空', trigger: 'change' },
        ],
        files: [
          { required: true, message: '文件地址不能为空', trigger: 'change' },
        ],
      },
      tableDataObj: {
        //列表数据
        //总条数
        total: 0,
        loading2: false,
        tableData: [],
      },
    }
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表

      this.tableDataObj.loading2 = true
      window.api.post(
        window.path.omcs + 'consumerClientBillFiles/page',
        this.tabelAlllist,
        (res) => {
          console.log(res)
          this.tableDataObj.total = res.data.total
          this.tableDataObj.tableData = res.data.records
          this.tableDataObj.loading2 = false
        }
      )
    },
    handleSizeChange(size) {
      //改变每页数量触发事件
      this.tabelAlllist.pageSize = size
      this.gettableLIst()
    },
    handleCurrentChange: function (currentPage) {
      //改变页数触发事件
      this.tabelAlllist.currentPage = currentPage
      this.gettableLIst()
    },
    //----------------------列表操作-------------------
    //查询
    Query() {
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    Reload(val) {
      this.$refs.formInline.resetFields()
      Object.assign(this.tabelAlllist, this.formInline)
      this.gettableLIst()
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.dialogStatus == 'add') {
            window.api.post(
              window.path.omcs + 'consumerClientBillFiles',
              this.formop,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: 'success',
                  })
                  this.dialogFormVisible = false
                  this.gettableLIst()
                } else {
                  this.$message({
                    message: res.msg,
                    type: 'error',
                  })
                }
              }
            )
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addopt() {
      this.dialogFormVisible = true
      this.dialogStatus = 'add'
    },

    detailsRow(index, val) {
      //编辑
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.id = val.id
      this.$nextTick(() => {
        this.$refs.formop.resetFields()
        Object.assign(this.formop, val) //编辑框赋值
      })
    },
    //操作状态功能（启用停用）
    delState: function (index, val) {
      this.$confirms.confirmation(
        'delete',
        '确认执行此操作吗？',
        window.path.omcs + 'consumerClientBillFiles/' + val.id,
        {},
        (res) => {
          this.gettableLIst()
        }
      )
    },
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.gettableLIst()
        this.menuName = common.getMenuList()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.gettableLIst()
      this.menuName = common.getMenuList()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  watch: {
    //监听弹框是否关闭
    dialogFormVisible(val) {
      if (!val) {
        this.$refs.formop.resetFields()
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.boderbottom {
  padding-bottom: 20px;
}
.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Signature-box {
  padding: 20px;
}
.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.Signature-matter > div {
  height: 26px;
  line-height: 26px;
}
.Signature-set {
  color: #0066cc;
}
.Signature-creat {
  margin-top: 20px;
}
.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.Mail-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.sig-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.input-w {
  width: 300px;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>