@charset "UTF-8";
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td { margin: 0; padding: 0; }
ul,ol{list-style:none;}
a{display:block;text-decoration:none;color: #666}
a:hover{color: #16a085}
a:focus{outline: none}
a:focus{border: none}
img{border:0;}
input,button{vertical-align:middle;border:none;}
textarea{border:none;resize:none;}
input, textarea {box-sizing: content-box;background: 0 0;font-family: "Microsoft YaHei"}
input::-webkit-input-placeholder,textarea::-webkit-input-placeholder {
    color: #acacac;
}

input:-moz-placeholder, textarea:-moz-placeholder {
    color:#acacac;
}
input::-moz-placeholder, textarea::-moz-placeholder {
    color:#acacac;
}
input:-ms-input-placeholder, textarea:-ms-input-placeholder {
    color:#acacac;
}
.rt{float:right;}
.lf{float: left;}
body{color: #666;font-family: "Microsoft YaHei"; font-size:14px;min-width:1160px;}

/*清除浮动*/
.clearfix{*zoom: 1;} /* 针对IE7 hack，触发IE7的haslayout，以清除浮动 */
.clearfix:before,.clearfix:after{display:table;content:"";line-height:0;}
.clearfix:after{clear:both;}
table tr td,table{border-collapse:collapse;}

input[type=text]:focus,input[type=text]:active{
    box-shadow: none;
    /* border: none; */
}
/*通用样式*/
/*省略号：添加元素必定包含块级元素上（有float、block、inline-block等）*/
.ellipsis{
    display: block;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
/*1、改变滚动条样式：
*使用方法：
*在需要改变滚动条的标签元素上添加class="bsfw-rdfw"即可
*
*/
/*chrome*/
.bsfw-rdfw::-webkit-scrollbar-track
{
    background-color: #eee;
}
/*定义滚动条高宽及背景*/
.bsfw-rdfw::-webkit-scrollbar
{
    width: 4px;
    /*background: url(../images/py-main-icon16.png);*/
}
/*定义滚动条*/
.bsfw-rdfw::-webkit-scrollbar-thumb
{
    background-color: #d0d0d0;
}
/*IE*/
.bsfw-rdfw{
/*三角箭头的颜色*/
    scrollbar-arrow-color:#eee;
/*滚动条滑块按钮的颜色*/
    scrollbar-face-color:#d0d0d0;
/*滚动条整体颜色*/
    scrollbar-highlight-color:#d0d0d0;
/*滚动条阴影*/
    scrollbar-shadow-color: #eee;
/*滚动条轨道颜色*/
    scrollbar-track-color: #eee;
}
/* 盒子圆角 */
.fillet{
    border-radius: 3px;
    background: #fff;
}
/* 顶层标题 */
.Top_title{
    margin: 10px 0;
    padding-left: 5px;
    color:#16a085;
}
/* 表格上的标题 */
.sensitive-fun{
    padding:30px 0 14px 0;
    font-weight: bold;
}
/* 表格上的标题右边的margin */
.sensitive-list-header{
    display: inline-block;
    padding-right:10px;
}
/* 搜索input框宽度 */
/* .input-w{
    width:248px !important;
} */
.boderbottom{
    border-bottom: 1px solid #f5f5f5;
    padding-bottom: 20px;
}
.el-table{
    width: 99% !important;
}
pre{
    word-wrap: break-word;
    white-space: pre-wrap;  
    font-size: 14px;
}
.buttondiv{
    margin: 10px 0;
}
