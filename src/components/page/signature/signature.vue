<template>
  <div class="signature-container">
    <!-- 查询表单 -->
    <el-form :inline="true" :model="queryForm" :rules="rules" ref="queryFormRef" class="query-form">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="queryForm.username" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="签名" prop="signature">
        <el-input v-model="queryForm.signature" placeholder="请输入签名" clearable></el-input>
      </el-form-item>
      <el-form-item class="form-buttons">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 签名详情展示 -->
    <div class="signature-details" v-loading="loading">
      <!-- 无数据提示 -->
      <el-empty v-if="!signatureData" description="暂无数据，请进行查询"></el-empty>
      
      <!-- 签名详情 -->
      <el-card v-else class="signature-card">
        <template #header>
          <div class="card-header">
            <span>签名详情</span>
          </div>
        </template>
        
        <el-descriptions :column="1" border>
          <el-descriptions-item label="用户名">{{ signatureData.username }}</el-descriptions-item>
          <el-descriptions-item label="签名">{{ signatureData.signature }}</el-descriptions-item>
          <el-descriptions-item label="移动状态">
            <el-tag :type="signatureData.yd==1 ? 'success' : 'danger'">
              {{ signatureData.yd==1 ? '已实名' : '未实名' }}
            </el-tag>
            <span v-if="signatureData.ydReason" style="color: #F56C6C;margin-left: 10px;font-size: 12px;">{{ signatureData.ydReason }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="联通状态">
            <el-tag :type="signatureData.lt==1 ? 'success' : 'danger'">
              {{ signatureData.lt==1 ? '已实名' : '未实名' }}
            </el-tag>
            <span v-if="signatureData.ltReason" style="color: #F56C6C;margin-left: 10px;font-size: 12px;">{{ signatureData.ltReason }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="电信状态">
            <el-tag :type="signatureData.dx==1 ? 'success' : 'danger'">
              {{ signatureData.dx==1 ? '已实名' : '未实名' }}
            </el-tag>
            <span v-if="signatureData.dxReason" style="color: #F56C6C;margin-left: 10px;font-size: 12px;">{{ signatureData.dxReason }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import getNoce from '../../../plugins/getNoce'

// 创建独立的 axios 实例
const instance = axios.create({
  baseURL: window.path.omcs,
  timeout: 10000
})

// 请求拦截器
instance.interceptors.request.use(
  async config => {
    const nonce = await getNoce.useNonce();
    config.headers['Once'] = nonce
    return config
  },
  error => {
    // console.log(error, 'error');
    
    return Promise.reject(error)
  }
)

// 响应拦截器
instance.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response) {
      if (error.response.status == 403) {
        ElMessage.error(error.response.data.msg || error.response.data.message)
        localStorage.removeItem("nonce_list");
      }else{
        ElMessage.error(error.response.data.msg || error.response.data.message)
      }
    }
    // ElMessage.error('请求失败，请稍后重试')
    return Promise.reject(error)
  }
)

// 表单引用
const queryFormRef = ref(null)

// 查询表单
const queryForm = reactive({
  username: '',
  signature: ''
})

// 表单验证规则
const rules = reactive({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  signature: [
    { required: true, message: '请输入签名', trigger: 'blur' }
  ]
})

// 加载状态
const loading = ref(false)

// 签名详情数据
const signatureData = ref(null)

// 组件挂载后执行
onMounted(() => {
  // 页面加载时不自动查询
})

// 查询按钮
const handleQuery = () => {
  queryFormRef.value.validate((valid) => {
    if (valid) {
      getSignatureDetail()
    } else {
      ElMessage.warning('请完善必填项')
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  queryFormRef.value.resetFields()
  signatureData.value = null
}

// 获取签名详情数据
const getSignatureDetail = async () => {
  try {
    loading.value = true
    
    const params = {
      username: queryForm.username,
      signatures: queryForm.signature ? [queryForm.signature] : []
    }
    
    const res = await instance.post('openApi/querySignatureRealNameStatus', params)
    
    if (res?.code === 200) {
      if (res.data?.length > 0) {
        // 只展示第一条记录作为详情
        signatureData.value = res.data[0]
        ElMessage.success('查询成功')
      } else {
        ElMessage.info('未找到相关签名信息')
        signatureData.value = null
      }
    } else {
      ElMessage.error(res?.msg || '获取签名详情失败')
      signatureData.value = null
    }
  } catch (error) {
    console.error('获取签名详情出错:', error)
    // ElMessage.error('系统错误，请稍后重试')
    signatureData.value = null
  } finally {
    loading.value = false
  }
}

// 获取审核状态显示文本
const getAuditStatusText = (status) => {
  const statusMap = {
    1: '待审核',
    2: '审核通过',
    3: '审核不通过'
  }
  return statusMap[status] || '未知状态'
}

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'danger'
  }
  return typeMap[status] || 'info'
}

// // 获取运营商状态显示文本
// const getOperatorStatusText = (status) => {
//   const statusMap = {
//     0: '未实名',
//     1: '已实名'
//   }
//   return statusMap[status] || '未知状态'
// }

// // 获取运营商状态类型
// const getOperatorStatusType = (status) => {
//   const typeMap = {
//     0: 'danger',
//     1: 'success'
//   }
//   return typeMap[status] || 'info'
// }

// 获取签名来源显示文本
const getSignatureTypeText = (type) => {
  const typeMap = {
    1: '企业名称',
    2: '事业单位',
    3: '商标',
    4: 'APP',
    5: '小程序',
    // 6: '公众号',
    7: '网站'
  }
  return typeMap[type] || ''
}

// 获取签名来源标签类型
const getSignatureTypeTag = (type) => {
  const typeMap = {
    1: 'primary',    // 企业名称
    2: 'info',       // 事业单位
    3: 'warning',    // 商标
    4: 'success',    // APP
    5: 'success',    // 小程序
    // 6: 'success',    // 公众号
    7: 'success'     // 网站
  }
  return typeMap[type] || 'info'
}
</script>

<style scoped>
.signature-container {
  padding: 10px;
  max-width: 100%;
  box-sizing: border-box;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.query-form {
  margin-bottom: 15px;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.query-form:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.query-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.query-form :deep(.el-form-item__label) {
  width: 80px;
  text-align: right;
  font-weight: 500;
  color: #606266;
}

.query-form :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.query-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.query-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.form-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
}

.form-buttons :deep(.el-button) {
  padding: 12px 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.form-buttons :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #3a8ee6 100%);
  border: none;
}

.form-buttons :deep(.el-button--primary:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.signature-details {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  min-height: 200px;
  transition: all 0.3s ease;
}

.signature-details:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
}

.signature-card {
  margin-top: 15px;
}

.signature-card :deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-descriptions) {
  padding: 15px;
}

:deep(.el-descriptions__label) {
  width: 100px;
  font-weight: 500;
  color: #606266;
  background-color: #f8f9fa;
}

:deep(.el-descriptions__content) {
  padding: 12px 15px;
  color: #303133;
}

:deep(.el-tag) {
  margin: 0 5px;
  padding: 4px 12px;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-tag--success) {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

:deep(.el-tag--danger) {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-empty__description) {
  color: #909399;
}

@media screen and (min-width: 768px) {
  .signature-container {
    padding: 30px;
  }

  .query-form {
    padding: 25px;
  }

  .signature-details {
    padding: 25px;
    min-height: 300px;
  }

  :deep(.el-descriptions) {
    padding: 20px;
  }

  :deep(.el-descriptions__content) {
    padding: 15px 20px;
  }

  .card-header {
    font-size: 18px;
  }
}
</style>
