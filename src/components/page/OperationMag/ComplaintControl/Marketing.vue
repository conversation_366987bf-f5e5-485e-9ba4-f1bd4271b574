<template>
  <div class="container_left">
    <div class="OuterFrame fillet">
      <el-form
        :inline="true"
        :model="formBlackList"
        class="demo-form-inline"
        ref="formBlackList"
      >
        <el-form-item label="账号" prop="username">
          <el-input
            v-model="formBlackList.username"
            placeholder=""
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-select
            v-model="formBlackList.province"
            clearable
            placeholder="请选择省份"
            class="input-w"
          >
            <el-option
              v-for="item in provinces"
              :key="item.provinceNumber"
              :label="item.provinceName"
              :value="item.provinceName"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="quenryBlack()"
            >查询</el-button
          >
          <el-button type="primary" plain @click="resetForm('formBlackList')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem> -->
        <!-- <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
          style="width: 100%"
          @selection-change="handelSelection"
        > -->

        <vxe-toolbar ref="toolbarRef" custom>
          <template #buttons>
            <el-button type="primary" @click="addopt">新增</el-button>
          </template>
        </vxe-toolbar>

        <vxe-table
          ref="tableRef"
          id="Marketing"
          border
          stripe
          :custom-config="customConfig"
          :column-config="{ resizable: true }"
          :row-config="{ isHover: true }"
          min-height="1"
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          style="font-size: 12px;"
          :data="tableDataObj.tableData">

          <vxe-column
            field="账号"
            title="账号"
            width="150"
          >
            <template v-slot="scope">
              <div>
                {{ scope.row.username }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="省份" title="省份">
            <template v-slot="scope">
              <div>
                {{ scope.row.province }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="创建时间" title="创建时间">
            <template v-slot="scope">
              <div>
                {{ moment(scope.row.createTime).format('YYYY-MM-DD hh:mm:ss') }}
              </div>
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="100">
            <template v-slot="scope">
              <el-button
                link
                style="color: #f56c6c"
                @click="delet(scope.row)"
                ><i class=""></i>&nbsp;删&nbsp;除</el-button
              >
            </template>
          </vxe-column>
        </vxe-table>
        <!--分页-->
        <!-- <template v-slot:pagination>
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            style="background: #fff; padding: 10px 0; text-align: right"
          > -->
        <div class="paginationBox">
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="formBlackListObj.currentPage"
            :page-size="formBlackListObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow"
          >
          </el-pagination>
        </div>
          <!-- </el-col>
        </template> -->
        <!-- 表格和分页结束 -->
      </div>
    </div>
    <el-dialog :title="titleMap" v-model="dialogFormVisible" width="520px">
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="100px"
        style="padding: 0 28px 0 20px"
      >
        <el-form-item label="账号" prop="username">
          <el-input
            v-model="form.username"
            autocomplete="off"
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-select style="width: 200px" v-model="form.province" placeholder="请选择省份">
            <el-option
              v-for="item in provinces"
              :key="item.provinceNumber"
              :label="item.provinceName"
              :value="item.provinceName"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm('form')"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem.vue'
// 引入时间戳转换
import { formatDate } from '@/assets/js/date.js'
import moment from 'moment'
import { mapState, mapMutations, mapActions } from 'vuex'
export default {
  components: {
    TableTem
  },
  name: 'BlackLIst',
  data() {
    return {
      customConfig: {
        storage: true,
        // mode: "popup"
      },
      isFirstEnter: false,
      titleMap: '新增',
      SigDialogVisible: false,
      dialogFormVisible: false, //弹框显示隐藏
      fileList: [],
      header: '',
      form: {
        username: '',
        // time: "",
        province: '',
      },

      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'change' },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur',
          },
        ],
        province: [
          { required: true, message: '请选择省份', trigger: 'change' },
        ],
      },
      provinces: [
        {
          provinceName: '全部',
          provinceNumber: 0,
        },
        {
          provinceName: '湖南',
          provinceNumber: 1368805,
        },
        {
          provinceName: '云南',
          provinceNumber: 1062234,
        },
        {
          provinceName: '江西',
          provinceNumber: 868514,
        },
        {
          provinceName: '黑龙江',
          provinceNumber: 746173,
        },
        {
          provinceName: '广东',
          provinceNumber: 5825500,
        },
        {
          provinceName: '山东',
          provinceNumber: 2615878,
        },
        {
          provinceName: '浙江',
          provinceNumber: 10465138,
        },
        {
          provinceName: '贵州',
          provinceNumber: 726520,
        },
        {
          provinceName: '四川',
          provinceNumber: 2329911,
        },
        {
          provinceName: '福建',
          provinceNumber: 3258609,
        },
        {
          provinceName: '宁夏',
          provinceNumber: 27709,
        },
        {
          provinceName: '新疆',
          provinceNumber: 411956,
        },
        {
          provinceName: '山西',
          provinceNumber: 791569,
        },
        {
          provinceName: '陕西',
          provinceNumber: 1539546,
        },
        {
          provinceName: '上海',
          provinceNumber: 2133964,
        },
        {
          provinceName: '北京',
          provinceNumber: 2081191,
        },
        {
          provinceName: '湖北',
          provinceNumber: 1068225,
        },
        {
          provinceName: '海南',
          provinceNumber: 1291536,
        },
        {
          provinceName: '广西',
          provinceNumber: 1097668,
        },
        {
          provinceName: '重庆',
          provinceNumber: 2090084,
        },
        {
          provinceName: '西藏',
          provinceNumber: 46216,
        },
        {
          provinceName: '辽宁',
          provinceNumber: 1085706,
        },
        {
          provinceName: '青海',
          provinceNumber: 105964,
        },
        {
          provinceName: '江苏',
          provinceNumber: 2574514,
        },
        {
          provinceName: '河北',
          provinceNumber: 2239827,
        },
        {
          provinceName: '安徽',
          provinceNumber: 1441346,
        },
        {
          provinceName: '内蒙古',
          provinceNumber: 410416,
        },
        {
          provinceName: '吉林',
          provinceNumber: 618893,
        },
        {
          provinceName: '甘肃',
          provinceNumber: 461829,
        },
        {
          provinceName: '河南',
          provinceNumber: 4941806,
        },
      ],
      formLabelWidth: '120px',
      formBlackList: {
        username: '',
        province: '',
        currentPage: 1,
        prePage: 10,
      },
      formBlackListObj: {
        currentPage: 1,
        prePage: 10,
      },
      selectId: '', //批量操作选中id
      rowId: '', //操作行的id
      tableDataObj: {
        //列表数据
        loading2: false,
        tableData: [],
        sign: '',
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
      },
    }
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    //-------------   列表信息   --------------
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true //打开loading
      window.api.post(
        window.path.omcs + 'operatingclientmarketingnumber/page',
        this.formBlackListObj,
        (res) => {
          this.tableDataObj.tableData = res.data.records
          // console.log(this.tableDataObj.tableData,'data');
          // this.tableDataObj.tableData.forEach(item=>{
          //     // console.log(item,'ll');
          //     // // this.formatData(item.createTime)
          //     // this.moment(item.createTime).format('YYYY-MM-DD HH:mm:ss')
          //     // this.moment(item.startTime).format()
          //     // this.moment(item.updateTime).format()
          // })

          this.tableDataObj.tablecurrent.totalRow = res.data.total
          this.tableDataObj.loading2 = false //关闭loading
        }
      )
    },
    formatData(val) {
      console.log(val)
      return formatDate(new Date(val * 1), 'YYYY-MM-DD HH:mm:ss')
    },
    //查询
    quenryBlack() {
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    },
    //获取查询时间的开始时间和结束时间
    // hande: function (val) {
    //     if(val){
    //         this.formBlackList.begTime = this.moment(val[0]).format("YYYY-MM-DD");
    //         this.formBlackList.endTime = this.moment(val[1]).format("YYYY-MM-DD");
    //     }else{
    //         this.formBlackList.begTime = '';
    //         this.formBlackList.endTime = '';
    //     }
    // },
    //改变每页数量触发事件
    handleSizeChange(size) {
      this.formBlackListObj.pageSize = size
      this.getTableDtate()
    },
    addopt() {
      this.dialogFormVisible = true
    },
    submitForm(formop) {
      console.log(this.form, 'ss')
      console.log(formop)
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            this.titleMap == '新增' ? 'post' : 'put',
            this.titleMap == '新增' ? '确定新增操作？' : '确定编辑操作？',
            window.path.omcs + 'operatingclientmarketingnumber',
            this.form,
            (res) => {
              this.dialogFormVisible = false
              this.getTableDtate()
              this.$refs[formop].resetFields()
            }
          )
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    delet(val) {
      this.$confirms.confirmation(
        'delete',
        '确定删除操作？',
        window.path.omcs + 'operatingclientmarketingnumber/' + val.id,
        {},
        (res) => {
          this.getTableDtate()
        }
      )
    },
    //改变页数触发事件
    handleCurrentChange: function (currentPage) {
      this.formBlackListObj.currentPage = currentPage
      this.getTableDtate()
    },
    resetForm(formName) {
      //重置
      this.$refs[formName].resetFields()
      this.formBlackList.begTime = ''
      this.formBlackList.endTime = ''
      Object.assign(this.formBlackListObj, this.formBlackList)
      this.getTableDtate()
    }
  },
  mounted() {
    this.header = {
      Authorization: 'Bearer' + window.common.getCookie('ZTADMIN_TOKEN'),
    }
  },
  created() {
    this.isFirstEnter = true
    this.$nextTick(() => {
      this.getTableDtate()
    })
  },
  mounted() {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  activated() {
    if (this.$route.meta.isBack || !this.isFirstEnter) {
      this.$nextTick(() => {
        this.getTableDtate()
      })
    } else {
      this.$route.meta.isBack = false
      this.isFirstEnter = false
    }
  },
  watch: {
    //监听查询框对象的变化
    // formBlackListObj: {
    //   handler() {
    //     this.getTableDtate();
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    blackListDialog(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.$refs.blackList.resetFields() //清空表单
      }
    },
    //监听导入弹框是否关闭
    SigDialogVisible(val) {
      if (val == false) {
        this.$refs.signatureFrom.resetFields() //清空表单
        this.fileList = []
      }
    },
  },
}
</script>

<style scoped>
.OuterFrame {
  padding: 20px;
}
.Signature-search-fun {
  margin: 10px 10px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
</style>

<style>
.el-tabs__content {
  overflow: visible !important;
}
</style>