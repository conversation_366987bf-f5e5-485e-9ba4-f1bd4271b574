<template>
  <div class="showMsgLeft">
    <el-col class="showMsgMenues">
      <el-menu
        class="el-menu-vertical-demo"
        @select="selectMsg"
        :default-active="defaultActive"
      >
        <el-menu-item index="0">
          <el-icon class="circle1"><el-icon-circl /></el-icon>
          <template v-slot:title>
            <span class="navTitle">全部消息({{ showMsgData.count }})</span>
          </template>
          <span class="msgPop">{{ showMsgData.notReadAll }}</span>
        </el-menu-item>
        <el-menu-item index="1">
          <el-icon class="circle2"><el-icon-circl /></el-icon>
          <template v-slot:title>
            <span class="navTitle">预警通知 ({{ showMsgData.warnNum }})</span>
          </template>
          <span class="msgPop">{{ showMsgData.warnNotRead }}</span>
        </el-menu-item>
        <el-menu-item index="2">
          <el-icon class="circle3"><el-icon-circl /></el-icon>
          <template v-slot:title>
            <span class="navTitle">系统消息 ({{ showMsgData.systemNum }})</span>
          </template>
          <span class="msgPop">{{ showMsgData.systemNotRead }}</span>
        </el-menu-item>
        <!-- <el-menu-item index="4">
                        <i class="el-icon-circl circle4"></i>
                        <span slot="title" class="navTitle">活动福利 ({{showMsgData.activity}})</span>
                        <span class="msgPop">{{showMsgData.activityUnread}}</span>
                    </el-menu-item> -->
        <el-menu-item index="3">
          <el-icon class="circle5"><el-icon-circl /></el-icon>
          <template v-slot:title>
            <span class="navTitle">审批提醒 ({{ showMsgData.auditNum }})</span>
          </template>
          <span class="msgPop">{{ showMsgData.auditNotRead }}</span>
        </el-menu-item>
      </el-menu>
    </el-col>
  </div>
</template>

<script>
export default {
  components: {
    ElIconCircl,
  },
  name: 'showMsgLeft',
  props: {
    showMsgData: {
      type: Object,
    },
  },
  computed: {
    defaultActive() {
      return this.$route.query.msg ? this.$route.query.msg : '1'
    },
  },
  methods: {
    selectMsg(a, b) {
      this.$router.push({ path: 'MessageList', query: { msg: a } })
    },
  },
  data() {
    return {
      // defaultActive:'1',
    }
  },
}
</script>

<style scoped>
.showMsgLeft i.el-icon-circl {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 15px;
}
.circle1 {
  background-color: white;
}
.circle2 {
  background-color: #e95d69;
}
.circle3 {
  background-color: #76d1bf;
}
.circle4 {
  background-color: #ffda94;
}
.circle5 {
  background-color: #9dab97;
}
.msgPop {
  padding: 0 8px;
  top: 15px;
  display: block;
  position: absolute;
  right: 40px;
  height: 28px;
  line-height: 28px;
  background: #f2f2f2;
  text-align: center;
  font-size: 12px;
  border-radius: 3px;
  color: #e95d69;
  font-weight: bold;
}
.msgPop:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 5px 0;
  border-color: transparent #f2f2f2 transparent transparent;
  left: -4px;
  top: 4px;
  top: 12px;
}
</style>

<style>
.showMsgLeft .el-menu-item {
  position: relative;
  height: 48px;
  line-height: 48px;
}
.showMsgLeft .el-menu-item:after {
  position: absolute;
  content: '';
  height: 1px;
  background: #e4e4e4;
  width: 90%;
  bottom: 0;
  left: 5%;
}
.showMsgLeft .showMsgMenues .el-menu {
  background: #f7f8fa;
}
.showMsgLeft .el-menu-item.is-active .navTitle {
  color: #333;
  font-weight: bold;
}
.showMsgLeft .el-menu {
  border-right: solid 0px #e6e6e6;
}
.showMsgMenues .el-menu-item:focus,
.showMsgMenues .el-menu-item:hover,
.showMsgMenues .el-submenu__title:hover {
  background: #f7f8fa;
}
</style>
